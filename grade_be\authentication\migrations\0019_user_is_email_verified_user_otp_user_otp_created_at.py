# Generated by Django 5.0.6 on 2025-03-31 16:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentication",
            "0018_remove_user_email_verification_token_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="is_email_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="otp",
            field=models.CharField(blank=True, max_length=6, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="otp_created_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
