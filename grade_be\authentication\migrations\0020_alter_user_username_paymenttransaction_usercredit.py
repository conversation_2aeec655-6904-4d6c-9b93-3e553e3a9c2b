# Generated by Django 5.0.6 on 2025-03-31 18:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentication",
            "0019_user_is_email_verified_user_otp_user_otp_created_at",
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name="user",
            name="username",
            field=models.CharField(max_length=150),
        ),
        migrations.CreateModel(
            name="PaymentTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("currency", models.CharField(default="USD", max_length=3)),
                ("razorpay_payment_id", models.CharField(max_length=100)),
                ("razorpay_order_id", models.Char<PERSON><PERSON>(max_length=100)),
                (
                    "razorpay_signature",
                    models.Char<PERSON>ield(blank=True, max_length=100),
                ),
                ("status", models.Char<PERSON>ield(default="pending", max_length=20)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserCredit",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "free_credit",
                    models.DecimalField(
                        decimal_places=2, default=1.0, max_digits=10
                    ),
                ),
                (
                    "paid_credit",
                    models.DecimalField(
                        decimal_places=2, default=0.0, max_digits=10
                    ),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
