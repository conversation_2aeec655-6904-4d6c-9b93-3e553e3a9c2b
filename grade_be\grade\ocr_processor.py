import os
import json
import logging
from .ocr_processing_core import (
    extract_images_from_pdf,
    group_by_roll_number_and_sort_by_page,
    images_to_pdf_bytes,
    gemini_json_from_pdf,
    convert_answers_format,
    detect_and_crop_diagrams,
)

logger = logging.getLogger(__name__)


def process_answer_ocr(file_path, output_json_dir, output_images_dir, user_id):
    """
    Process uploaded answer file with OCR and return results

    Args:
        file_path: Path to the uploaded PDF file
        output_json_dir: Directory to store JSON results
        output_images_dir: Directory to store diagram images
        user_id: ID of the user who uploaded the file

    Returns:
        dict: Result with success status, paths, and any errors
    """
    try:
        logger.info(f"Starting OCR processing for file: {file_path}")

        # Step 1: Extract images from PDF
        logger.info("Step 1: Extracting images from PDF...")
        all_images = extract_images_from_pdf(file_path)

        if not all_images:
            return {
                "success": False,
                "error": "No images could be extracted from the PDF",
            }

        logger.info(f"Extracted {len(all_images)} images")

        # Step 2: Group by roll number and sort by page
        logger.info("Step 2: Grouping images by roll number...")
        grouped_images = group_by_roll_number_and_sort_by_page(all_images)

        if not grouped_images:
            return {
                "success": False,
                "error": "No valid roll numbers found in the images",
            }

        # Process each roll number group
        results = {}

        for roll_number, pages_data in grouped_images.items():
            logger.info(f"Processing roll number: {roll_number}")

            try:
                # Sort by page number and extract images
                sorted_images = [img for _, img, _ in pages_data]

                # Step 3: Convert images to PDF for Gemini processing
                pdf_bytes = images_to_pdf_bytes(sorted_images)

                if not pdf_bytes:
                    logger.warning(
                        f"Could not create PDF for roll {roll_number}"
                    )
                    continue

                # Step 4: Extract JSON from PDF using Gemini
                logger.info(f"Extracting JSON for roll {roll_number}...")
                raw_json = gemini_json_from_pdf(
                    pdf_bytes, output_images_dir, roll_number
                )

                # Step 5: Convert and validate JSON format
                formatted_json = convert_answers_format(raw_json)

                # Step 6: Detect and crop diagrams
                logger.info(f"Processing diagrams for roll {roll_number}...")
                final_json = detect_and_crop_diagrams(
                    sorted_images,
                    formatted_json,
                    output_images_dir,
                    roll_number,
                )

                # Step 7: Save JSON file
                json_filename = f"{roll_number}_{user_id}_answers.json"
                json_path = os.path.join(output_json_dir, json_filename)

                with open(json_path, "w", encoding="utf-8") as f:
                    if isinstance(final_json, str):
                        f.write(final_json)
                    else:
                        json.dump(final_json, f, indent=2, ensure_ascii=False)

                results[roll_number] = {
                    "json_path": json_path,
                    "images_dir": output_images_dir,
                    "pages_processed": len(sorted_images),
                }

                logger.info(f"Successfully processed roll {roll_number}")

            except Exception as e:
                logger.error(f"Error processing roll {roll_number}: {str(e)}")
                results[roll_number] = {"error": str(e)}

        if not results:
            return {
                "success": False,
                "error": "No roll numbers could be processed successfully",
            }

        # Return results for the first successful roll number
        # (you might want to modify this logic based on your needs)
        for roll_number, result in results.items():
            if "json_path" in result:
                return {
                    "success": True,
                    "roll_number": roll_number,
                    "json_path": result["json_path"],
                    "images_dir": result["images_dir"],
                    "pages_processed": result["pages_processed"],
                    "all_results": results,
                }

        # If no successful results, return the first error
        first_error = next(iter(results.values()))
        return {
            "success": False,
            "error": first_error.get("error", "Unknown processing error"),
            "all_results": results,
        }

    except Exception as e:
        logger.error(f"OCR processing failed: {str(e)}")
        return {"success": False, "error": f"OCR processing failed: {str(e)}"}
