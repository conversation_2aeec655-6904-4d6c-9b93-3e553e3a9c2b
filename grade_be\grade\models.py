from django.db import models
from authentication.models import User as App, Organization
from django.utils import timezone
from datetime import timedelta

# Add these models to your existing models.py file
import json
import os


class GradingResult(models.Model):
    """Stores AI-powered grading results for a student's answer upload.Add commentMore actions

    This model links to an `AnswerUpload` and contains the calculated score,
    paths to result files, and metadata about the grading process.

    Attributes:
        answer_upload: A one-to-one relationship to the `AnswerUpload` model.
        user_id: The ID of the user who submitted the answer.
        total_score: The total score awarded after grading.
        max_possible_score: The maximum possible score for the test.
        percentage: The calculated percentage score.
        result_json_path: The file path to the detailed JSON grading results.
        grading_processed: A boolean indicating if grading is complete.
        grading_error: Stores any error messages encountered during grading.
        created_at: The timestamp when the grading result was created.
        graded_at: The timestamp when the grading was completed.
        questions_count: The total number of questions in the test.
        diagrams_count: The total number of diagrams detected.
    """
    # Link to the answer upload
    answer_upload = models.OneToOneField(
        "AnswerUpload", on_delete=models.CASCADE, related_name="grading_result"
    )

    # Grading metadata
    user_id = models.CharField(max_length=100)  # Match with AnswerUpload
    total_score = models.FloatField(default=0)
    max_possible_score = models.FloatField(default=0)
    percentage = models.FloatField(default=0)

    # File paths for storing results
    result_json_path = models.CharField(max_length=500, null=True, blank=True)

    # Grading status
    grading_processed = models.BooleanField(default=False)
    grading_error = models.TextField(null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    graded_at = models.DateTimeField(null=True, blank=True)

    # Additional metadata
    questions_count = models.IntegerField(default=0)
    diagrams_count = models.IntegerField(default=0)

    class Meta:
        db_table = "grading_results"
        indexes = [
            models.Index(fields=["user_id"]),
            models.Index(fields=["answer_upload"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self) -> str:
        return f"Grading for Answer {self.answer_upload.id} - {self.total_score}/{self.max_possible_score}"

    def get_result_data(self) -> dict:
        """Load and return the grading result JSON data.Add commentMore actions

        Reads the JSON file specified by `result_json_path` and returns its
        contents as a dictionary.

        Returns:
            A dictionary containing the grading data, or None if the path
            does not exist. Returns a dictionary with an 'error' key if
            loading fails.
        """        
        if self.result_json_path and os.path.exists(self.result_json_path):
            try:
                with open(self.result_json_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                return {"error": f"Failed to load result data: {str(e)}"}
        return None


class BaseQuestionPaper(models.Model):
    """An abstract base model for various types of question papers.Add commentMore actions

    This model provides common fields for question papers, such as title,
    subject, and total marks. It is not intended to be used directly but
    inherited by concrete question paper models.

    Attributes:
        organization: The organization to which this paper belongs.
        updated_by: The email of the user who last updated the paper.
        upload_date: The date when the paper was uploaded.
        test_title: The title of the test or question paper.
        board: The educational board (e.g., CBSE, ICSE).
        subject: The subject of the test.
        questions: A JSON field containing a list of questions.
        total_marks: The total marks for the paper.
        total_questions: The total number of questions in the paper.
    """
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="%(class)s_papers",
        null=True,
        blank=True,
    )
    updated_by = models.EmailField(
        null=True, blank=True
    )  # Email of the uploader
    upload_date = models.DateTimeField(auto_now_add=True)
    test_title = models.CharField(max_length=255, null=True, blank=True)
    board = models.CharField(max_length=50, null=True, blank=True)
    subject = models.CharField(max_length=100, null=True, blank=True)
    questions = models.JSONField(default=list)
    total_marks = models.IntegerField(default=0)
    total_questions = models.IntegerField(default=0)

    class Meta:
        abstract = True

    def __str__(self) -> str:
        return f"{self.test_title} - {self.subject} ({self.board})"


class Questions(BaseQuestionPaper):
    """Represents a generic question paper uploaded to the system.Add commentMore actions

    This model inherits from `BaseQuestionPaper` and is used for
    storing general question papers that do not fall into other specific
    categories like 'sample' or 'previous year'.

    Attributes:
        file: The uploaded question paper file.
    """
    file = models.FileField(upload_to="question_papers/qp_uploader")


class SampleQuestionPaper(BaseQuestionPaper):
    """Represents a sample question paper.Add commentMore actions

    This model is used for storing sample question papers for practice.

    Attributes:
        file: The uploaded sample question paper file.
    """
    file = models.FileField(upload_to="question_papers/sample/")


class PreviousYearQuestionPaper(BaseQuestionPaper):
    """Represents a question paper from a previous year.Add commentMore actions

    This is used to store and categorize question papers by their year of
    appearance.

    Attributes:
        file: The uploaded previous year question paper file.
        year: The year the question paper is from.
    """
    file = models.FileField(upload_to="question_papers/previous_year/")
    year = models.PositiveIntegerField(null=True, blank=True)


class GeneratedQuestionPaper(BaseQuestionPaper):
    """Represents a question paper generated by the system.Add commentMore actions

    These papers are typically created on-demand for users.

    Attributes:
        file: The generated question paper file.
        user_id: The ID of the user for whom the paper was generated.
    """
    file = models.FileField(upload_to="question_papers/generated/")
    # Or models.ForeignKey if you have a User model
    user_id = models.CharField(App, max_length=255, null=True, blank=True)


class AnswerUpload(models.Model):
    """Stores answer sheets uploaded by users for grading.Add commentMore actions

    This model links an uploaded answer file to a specific question paper
    and user. It also tracks the status of Optical Character Recognition (OCR)
    processing.

    Attributes:
        file: The uploaded answer sheet file.
        user_id: The ID of the user who uploaded the answer sheet.
        organization: The organization associated with the user.
        question_paper_type: The type of question paper (e.g., 'sample', 'generated').
        question_paper_id: The ID of the associated question paper.
        upload_date: The timestamp of the upload.
        ocr_updated_at: The timestamp when OCR processing was last updated.
        sample_question_paper: FK to `SampleQuestionPaper`.
        previous_year_question_paper: FK to `PreviousYearQuestionPaper`.
        generated_question_paper: FK to `GeneratedQuestionPaper`.
        organization_test: FK to `organization.Test`.
        questions: FK to `Questions`.
        ocr_processed: A boolean indicating if OCR processing is complete.
        ocr_json_path: File path to the OCR results in JSON format.
        ocr_images_dir: Directory path for images extracted during OCR.
        roll_number: The student's roll number extracted from the paper.
        ocr_error: Stores any errors from the OCR process.
        ocr_processed_at: Timestamp when OCR processing was completed.
    """
    # Your existing fields...
    file = models.FileField(upload_to="answer_uploads/")
    # Or models.ForeignKey if you have a User model
    user_id = models.IntegerField(null=True, blank=True)
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name="answer_uploads",
        null=True,
        blank=True,
    )
    question_paper_type = models.CharField(max_length=20)
    question_paper_id = models.IntegerField(null=True, blank=True)
    upload_date = models.DateTimeField(auto_now_add=True)
    ocr_updated_at = models.DateTimeField(null=True, blank=True)

    # Foreign key relationships (your existing code)
    sample_question_paper = models.ForeignKey(
        "SampleQuestionPaper", on_delete=models.CASCADE, null=True, blank=True
    )
    previous_year_question_paper = models.ForeignKey(
        "PreviousYearQuestionPaper",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    generated_question_paper = models.ForeignKey(
        "GeneratedQuestionPaper",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    organization_test = models.ForeignKey(
        "organization.Test", on_delete=models.CASCADE, null=True, blank=True
    )
    questions = models.ForeignKey(
        "Questions", on_delete=models.CASCADE, null=True, blank=True
    )

    # New OCR-related fields
    ocr_processed = models.BooleanField(default=False)
    ocr_json_path = models.CharField(max_length=500, null=True, blank=True)
    ocr_images_dir = models.CharField(max_length=500, null=True, blank=True)
    roll_number = models.CharField(max_length=20, null=True, blank=True)
    ocr_error = models.TextField(null=True, blank=True)
    ocr_processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = (
            "user_id",
            "question_paper_type",
            "question_paper_id",
        )


class QuestionFeedback(models.Model):
    """Stores feedback provided for a specific answer upload.Add commentMore actions

    This model is intended for overall feedback on an entire answer sheet.

    Attributes:
        answer_upload: A foreign key to the `AnswerUpload` model.
        feedback_text: The text of the feedback.
        marks_obtained: The total marks obtained for the answer sheet.
        created_date: The timestamp when the feedback was created.
    """
    answer_upload = models.ForeignKey(
        AnswerUpload, on_delete=models.CASCADE, related_name="feedback"
    )
    feedback_text = models.TextField(null=True, blank=True)
    marks_obtained = models.IntegerField(default=0)
    created_date = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return f"Feedback for {self.answer_upload}"


class AnswerAssignment(models.Model):
    """Tracks the assignment of answer sheets to human evaluators.Add commentMore actions

    This model ensures that each answer sheet is assigned to an evaluator
    and tracks the completion status of the evaluation.

    Attributes:
        answer_upload: A one-to-one link to the `AnswerUpload`.
        evaluator: The `User` assigned to evaluate the answer sheet.
        assigned_date: The date the assignment was made.
        completed: A boolean indicating if the evaluation is complete.
    """
    answer_upload = models.OneToOneField(
        AnswerUpload, on_delete=models.CASCADE
    )
    evaluator = models.ForeignKey(
        App,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="assigned_answers",
    )
    assigned_date = models.DateTimeField(auto_now_add=True)
    completed = models.BooleanField(default=False)

    def is_expired(self) -> bool:
        """Checks if the assignment is older than 3 days and not completed.Add commentMore actions

        Returns:
            True if the assignment is considered expired, False otherwise.
        """
        return not self.completed and (
            timezone.now() - self.assigned_date
        ) > timedelta(days=3)

    def __str__(self) -> str:
        return f"{self.answer_upload} -> {self.evaluator}"


class Feedback(models.Model):
    """Stores detailed, question-specific feedback for an answer upload.Add commentMore actions

    Unlike `QuestionFeedback`, this model holds feedback for each individual
    question in an answer sheet.

    Attributes:
        answer_upload: The associated `AnswerUpload`.
        question_number: The number of the question receiving feedback.
        marks_obtained: The marks awarded for the specific question.
        feedback: The textual feedback for the answer.
        complexity: The difficulty level of the question.
        marks_out_of: The maximum marks for the question.
        created_at: The timestamp when the feedback was created.
        updated_at: The timestamp when the feedback was last updated.
    """

    answer_upload = models.ForeignKey(
        AnswerUpload, related_name="feedbacks", on_delete=models.CASCADE
    )
    question_number = models.IntegerField()
    marks_obtained = models.DecimalField(max_digits=5, decimal_places=2)
    feedback = models.TextField()
    complexity = models.CharField(
        max_length=10,
        choices=[("easy", "Easy"), ("medium", "Medium"), ("hard", "Hard")],
    )

    marks_out_of = models.FloatField()

    complexity = models.CharField(
        max_length=10,
        choices=[("easy", "Easy"), ("medium", "Medium"), ("hard", "Hard")],
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"Feedback for Question {self.question_number}"


class MentorshipRequest(models.Model):
    """Represents a request from a student to be mentored by a mentor.Add commentMore actions

    This model tracks the relationship request between a mentor and a student,
    including its status.

    Attributes:
        mentor: The `User` who is the potential mentor.
        student: The `User` who is the potential student.
        status: The status of the request (Pending, Accepted, Rejected).
        created_at: The timestamp of the request.
    """
    mentor = models.ForeignKey(
        App, related_name="sent_requests", on_delete=models.CASCADE
    )
    student = models.ForeignKey(
        App, related_name="received_requests", on_delete=models.CASCADE
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ("Pending", "Pending"),
            ("Accepted", "Accepted"),
            ("Rejected", "Rejected"),
        ],
        default="Pending",
    )
    created_at = models.DateTimeField(auto_now_add=True)


class Notification(models.Model):
    """Represents a notification sent from one user to another.Add commentMore actions

    This can be used for various purposes, including mentorship requests or
    general communication within the platform.
Add commentMore actions
    Attributes:
        sender: The `User` who sent the notification.
        recipient: The `User` who receives the notification.
        sender_role: The role of the sender.
        recipient_role: The role of the recipient.
        message: The content of the notification.
        created_at: The timestamp when the notification was created.
        is_read: A boolean indicating if the recipient has read it.
        mentor_request: A boolean indicating if this is related to a mentor request.
    """

    ROLE_CHOICES = [
        ("student", "Student"),
        ("admin", "Admin"),
        ("evaluator", "Evaluator"),
        ("mentor", "Mentor"),
    ]

    sender = models.ForeignKey(
        App, on_delete=models.CASCADE, related_name="sent_notifications"
    )
    recipient = models.ForeignKey(
        App, on_delete=models.CASCADE, related_name="received_notifications"
    )
    sender_role = models.CharField(
        max_length=20, choices=ROLE_CHOICES, default="mentor"
    )
    recipient_role = models.CharField(
        max_length=20, choices=ROLE_CHOICES, default="student"
    )
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    mentor_request = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"Notification from {self.sender} ({self.sender_role}) to {self.recipient} ({self.recipient_role}) - {'Read' if self.is_read else 'Unread'}"


class MentorStudent(models.Model):
    """Represents the established relationship between a mentor and a student.Add commentMore actions

    This model is created once a `MentorshipRequest` is accepted, formally
    linking a mentor and a student.

    Attributes:
        mentor: The `User` acting as the mentor.
        student: The `User` being mentored.
        created_at: Timestamp when the relationship was established.
    """
    mentor = models.ForeignKey(
        App, on_delete=models.CASCADE, related_name="mentored_students"
    )
    student = models.ForeignKey(
        App, on_delete=models.CASCADE, related_name="monitored_by_mentors"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        # Prevent duplicate mentor-student pairs
        unique_together = ("mentor", "student")


# class MainRequest(models.Model):
#     user = models.ForeignKey(App, on_delete=models.CASCADE, related_name='main_requests')
#     role = models.CharField(max_length=50)  # Role of the user
#     resume = models.FileField(upload_to='resumes/')  # Path to uploaded resumes
#     board = models.CharField(max_length=50, choices=[
#         ('CBSE', 'CBSE'),
#         ('ICSE', 'ICSE'),
#         ('Stateboard', 'Stateboard'),
#         ('Neet', 'NEET'),
#         ('Jee', 'JEE')
#     ])
#     subject = models.CharField(max_length=100)  # Subject entered by the user
# submitted_at = models.DateTimeField(auto_now_add=True)  # Timestamp for
# submission


#     def __str__(self):
#         return f"MainRequest by {self.user.username} for {self.role}"
class MainRequest(models.Model):
    """Represents a user's request to take on a specific role.Add commentMore actions

    This is used when a user applies to become a mentor or evaluator,
    submitting their resume and specifying their areas of expertise.

    Attributes:
        user: The user making the request.
        role: The role the user is applying for (e.g., 'mentor').
        resume: The user's uploaded resume file.
        board: The educational board the user is associated with.
        subject: The subject of expertise.
        submitted_at: The timestamp of the submission.
    """
    user = models.ForeignKey(
        App, on_delete=models.CASCADE, related_name="main_requests"
    )
    role = models.CharField(max_length=50)  # Role of the user
    resume = models.FileField(upload_to="resumes/")  # Path to uploaded resumes
    board = models.CharField(
        max_length=50,
        choices=[
            ("CBSE", "CBSE"),
            ("ICSE", "ICSE"),
            ("Stateboard", "Stateboard"),
            ("Neet", "NEET"),
            ("Jee", "JEE"),
        ],
        blank=True,  # Allows board field to be empty in forms
        null=True,  # Stores NULL in the database if empty
    )
    subject = models.CharField(
        max_length=100,
        blank=True,  # Allows subject field to be empty in forms
        null=True,  # Stores NULL in the database if empty
    )
    submitted_at = models.DateTimeField(
        auto_now_add=True
    )  # Timestamp for submission

    def __str__(self) -> str:
        return f"MainRequest by {self.user.username} for {self.role}"


# Define constants for exam types, complexity levels, and question types
EXAM_TYPES = [
    ("CBSE", "CBSE"),
    ("ICSE", "ICSE"),
    ("State Board", "State Board"),
    ("JEE", "JEE"),
    ("NEET", "NEET"),
]

COMPLEXITY_LEVELS = [
    ("easy", "Easy"),
    ("medium", "Medium"),
    ("hard", "Hard"),
]

QUESTION_TYPES = [
    ("multiple-choice", "Multiple Choice"),
    ("short-answer", "Short Answer"),
    ("long-answer", "Long Answer"),
]


class Question(models.Model):
    """Stores individual questions for various exams and subjects.Add commentMore actions

    This model defines the structure of a question, including its text, type,
    difficulty, and associated marks. It can also store images, options for
    multiple-choice questions, and detailed explanations.

    Attributes:
        subject: The subject of the question.
        topic: The specific topic within the subject.
        exam_type: The exam this question is relevant for (e.g., CBSE, NEET).
        complexity: The difficulty level (e.g., Easy, Medium, Hard).
        question_type: The format of the question (e.g., Multiple Choice).
        marks: The number of marks the question is worth.
        question_text: The main text of the question.
        question_image: An optional image accompanying the question.
        options: A JSON field for multiple-choice options.
        correct_answer: The correct answer or a detailed explanation.
        explanation: A text explanation for the solution.
        explanation_image: An optional image for the explanation.
        created_at: Timestamp when the question was created.
        updated_at: Timestamp when the question was last updated.
        is_submitted: A flag to track if the question has been submitted.
    """
    # General fields
    subject = models.CharField(max_length=100)
    topic = models.CharField(max_length=200)
    exam_type = models.CharField(max_length=50, choices=EXAM_TYPES)
    complexity = models.CharField(max_length=50, choices=COMPLEXITY_LEVELS)
    question_type = models.CharField(max_length=50, choices=QUESTION_TYPES)
    marks = models.PositiveIntegerField(default=1)

    # Question text and images
    question_text = models.TextField()
    question_image = models.ImageField(
        upload_to="question_images/", blank=True, null=True
    )

    # Options for multiple-choice questions
    # List of options (used for MCQ)
    options = models.JSONField(blank=True, null=True)
    correct_answer = models.TextField(
        blank=True, null=True
    )  # Correct answer or explanation

    # Explanation and solution fields
    explanation = models.TextField(blank=True, null=True)
    explanation_image = models.ImageField(
        upload_to="explanation_images/", blank=True, null=True
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # New field to track submission status
    is_submitted = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.subject} - {self.topic} - {self.question_type}"


class Evaluator(models.Model):
    """Stores individual questions for various exams and subjects.Add commentMore actions

    This model defines the structure of a question, including its text, type,
    difficulty, and associated marks. It can also store images, options for
    multiple-choice questions, and detailed explanations.

    Attributes:
        subject: The subject of the question.
        topic: The specific topic within the subject.
        exam_type: The exam this question is relevant for (e.g., CBSE, NEET).
        complexity: The difficulty level (e.g., Easy, Medium, Hard).
        question_type: The format of the question (e.g., Multiple Choice).
        marks: The number of marks the question is worth.
        question_text: The main text of the question.
        question_image: An optional image accompanying the question.
        options: A JSON field for multiple-choice options.
        correct_answer: The correct answer or a detailed explanation.
        explanation: A text explanation for the solution.
        explanation_image: An optional image for the explanation.
        created_at: Timestamp when the question was created.
        updated_at: Timestamp when the question was last updated.
        is_submitted: A flag to track if the question has been submitted.
    """
    user = models.OneToOneField(
        App, on_delete=models.CASCADE, related_name="evaluator_profile"
    )
    rating = models.FloatField(default=0.0)
    resume = models.FileField(upload_to="resumes/", blank=True, null=True)
    languages = models.ManyToManyField("Language", related_name="evaluators")
    subjects = models.ManyToManyField("Subject", related_name="evaluators")
    boards = models.ManyToManyField("Board", related_name="evaluators")

    def __str__(self) -> str:
        return f"Evaluator: {self.user.username} - Rating: {self.rating}"


class Language(models.Model):
    """Represents a language that can be associated with an evaluator.Add commentMore actions

    Attributes:
        name: The unique name of the language (e.g., 'English', 'Hindi').
    """
    name = models.CharField(max_length=100, unique=True)

    def __str__(self) -> str:
        return self.name


class Subject(models.Model):
    """Represents a subject that can be associated with an evaluator or question.Add commentMore actions

    Attributes:
        name: The unique name of the subject (e.g., 'Physics', 'Mathematics').
    """
    name = models.CharField(max_length=100, unique=True)

    def __str__(self) -> str:
        return self.name


class Board(models.Model):
    """Represents an educational board (e.g., CBSE, ICSE).Add commentMore actions

    Attributes:
        name: The unique name of the board.
    """

    name = models.CharField(max_length=50, unique=True)

    def __str__(self) -> str:
        return self.name
