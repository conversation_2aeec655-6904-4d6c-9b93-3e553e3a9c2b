{"version": 3, "file": "static/js/5275.19c484e3.chunk.js", "mappings": "+RAcA,MAAMA,EAAsBA,CAACC,EAAsBC,EAAmBC,KAA0B,IAADC,EAC7F,MACMC,EAAQ,6BACRC,EAAeC,SAASC,gBAAgBH,EAAO,KAC/CI,EAAUF,SAASC,gBAAgBH,EAAO,QAC1CK,EAAUH,SAASC,gBAAgBH,EAAO,QAChDK,EAAQC,UAAYT,EACpBQ,EAAQE,aAAa,OAAQ,SAC7BN,EAAaO,UAAUC,IAAIb,GAC3BK,EAAaS,YAAYN,GACzBH,EAAaS,YAAYL,GACH,QAAtBN,EAAAD,EAAYa,kBAAU,IAAAZ,GAAtBA,EAAwBa,aAAaX,EAAcH,EAAYe,aAE/D,MAAMC,EAAWT,EAAQU,UAEzBX,EAAQG,aAAa,KAAMO,EAASE,EAdb,GAciCC,YACxDb,EAAQG,aAAa,KAAMO,EAASI,EAfb,GAeiCD,YACxDb,EAAQG,aAAa,SAAUO,EAASK,MAAQ,GAAoBF,YACpEb,EAAQG,aAAa,UAAWO,EAASM,OAAS,GAAoBH,YACtEb,EAAQG,aAAa,OAAQ,QAAQ,EAGjCc,EAA+BC,IAenC,MAAM,QACJC,EAAO,UACPC,EAAS,gBACTC,EAAe,KACfC,EAAI,oBACJC,EAAmB,gBACnBC,EAAe,eACfC,EACAV,MAAOW,EACPV,OAAQW,EAAW,iBACnBC,GACEV,EACEW,GAAWC,EAAAA,EAAAA,QAAuB,MAClCC,GAAgBD,EAAAA,EAAAA,WAGfE,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,KAGrDC,EAAAA,EAAAA,YAAU,KACJH,EACK,OAAPb,QAAO,IAAPA,GAAAA,EAAUa,GAED,OAATZ,QAAS,IAATA,GAAAA,GACF,GACC,CAACY,EAAgBb,EAASC,IAG7B,MAAMgB,GAAgBC,EAAAA,EAAAA,cAAY,KACG,IAA/BN,EAASO,QAAQC,UAA4BR,EAASO,QAAQC,UAC3DR,EAASO,QAAQhB,QACvB,KAEG,kBAAEkB,IAAsBC,EAAAA,EAAAA,MAGxBC,GAAoBL,EAAAA,EAAAA,cACvBM,IACC,IAAKA,EAEH,YADAZ,EAASO,QAAQM,cAInB,MAEMC,EAFkET,IAElCU,QAAOC,IAAA,IAAC,KAAEC,GAAMD,EAAA,OAAKJ,IAAcK,CAAI,IAEzEH,EAAgBI,OAClBlB,EAASO,QAAQY,UAAUL,GAE3Bd,EAASO,QAAQM,aACnB,GAEF,CAACR,KAGHD,EAAAA,EAAAA,YAAU,IAAMK,EAAkBE,IAAoB,CAACF,EAAmBE,KAI1EP,EAAAA,EAAAA,YAAU,KACR,IAAKJ,EAASO,QACZ,OAGF,MAGMO,EAHaT,IAGgBU,QAAQK,GAAW,CAACnB,EAAgBX,GAAiB+B,SAASD,EAAEH,QAG/FH,EAAgBI,OAClBlB,EAASO,QAAQY,UAAUL,GAE3Bd,EAASO,QAAQM,aACnB,GACC,CAACZ,EAAgBX,EAAiBe,IAErC,MAAMiB,GAAkBhB,EAAAA,EAAAA,cACrBiB,IACC,MAAMC,EAA8B,GAC9BC,EAAapB,IACnB,GAA0B,IAAtBoB,EAAWP,OAAc,OAAO,EAEpC,MAAMQ,EAAeC,IACrB,GAA4B,IAAxBD,EAAaR,OAAc,OAAO,EAGtC,MAAMU,EAAoCC,EAASN,EAAeG,EAAa,IAC/E,IAAKE,EAAiB,OAAO,EAC7B,MAAME,EAAiBF,EAQvB,OANAF,EAAaK,SAAQ,CAACX,EAAuBY,KACvCC,EAASb,EAAEU,EAAS,GAAIV,EAAEU,GAASP,EAAe,IACpDC,EAAQU,KAAKT,EAAWO,GAC1B,IAGK,CAACR,EAAQ,GAElB,CAACnB,IAGG8B,GAAuB7B,EAAAA,EAAAA,cAC1BiB,IAEC,MAAMa,EAA6B,GACnC,IAAIC,EAAa,EACjB,MAAMC,EAAiBxC,EAASS,QAEhC,IAAK+B,EACH,OAGFA,EAAeC,iBAAiB,cAAcR,SAAQ,SAAuBS,GAC3E,MAAMC,EAAiBD,EAAQE,aAAa,aAE5C,GAAID,EAAgB,CAClB,MAAME,EAAuBC,WAAWH,EAAeI,MAAM,KAAK,GAAGA,MAAM,KAAK,IAChFT,EAAiBF,KAAKS,GACtB,MAAM,MAAE3D,GAAWwD,EAA+B5D,UAC/B,IAAfyD,IAAkBA,EAAarD,EACrC,CACF,IAEA,MAAM8D,EAAwB,GAC9B,IAAK,IAAId,EAAI,EAAGA,EAAII,EAAiBlB,OAAQc,IAC3Cc,EAAeZ,KAAKE,EAAiBJ,GAAKK,EAAa,GAGzD,IAAIb,EAAU,GAEd,MAAMuB,EAAczB,EAAgBC,GAEpC,IAAIyB,EAAe,GACnB,GAAID,GAAyC,IAA1BA,EAAY,GAAG7B,OAQhC,IAPCM,GAAWuB,EAERvB,EAAQN,OAAS,IACnBM,EAAU,CAACA,EAAQ,KAIjBsB,EAAeG,MAAMpE,GAAMqE,KAAKC,IAAItE,EAAI0C,EAAc6B,SAAW,UAE9D,CAELJ,EADqBxB,EAAQ,GACA,IAC/B,CAEFtB,EAAkB8C,EAAa,GAEjC,CAAClD,EAAUwB,IAGPK,EAAeA,KACnB,MAAM0B,EAAUrD,EAASO,QAAQ+C,SAGjC,OAFoBtD,EAASO,QAAQC,UAAUU,OAASlB,EAASO,QAAQC,UAAYR,EAASO,QAAQhB,QAEnFgE,KAAKnC,GACCpB,EAASO,QAAQiD,uBAAuBpC,GACzCmC,KAAKE,GAAa,CAACA,EAAE,GAAKJ,EAAQK,KAAMD,EAAE,GAAKJ,EAAQM,QAC7E,EAGE9B,EAAWA,CAAC+B,EAA8CC,KAE9D,MAAMhF,EAAI+E,EAAOR,QAGjB,GAAIS,EAAO,GAAG,GAAKhF,EAAG,OAAO,EAC7B,GAAIgF,EAAOA,EAAO3C,OAAS,GAAG,GAAKrC,EAAG,OAAO,EAG7C,IAAK,IAAImD,EAAI,EAAGA,EAAI6B,EAAO3C,OAAQc,IACjC,GAAI6B,EAAO7B,GAAG,GAAKnD,EAAG,OAAOmD,EAE/B,OAAO,CAAK,EAGRC,EAAWA,CACf6B,EACAC,EACAH,EACAI,KAIA,MAAMC,EAAKL,EAAOR,QACZc,EAAKN,EAAOO,SACXC,EAAIC,GAAMP,GACVQ,EAAIC,GAAMR,EACXS,EAAKF,EAAKF,EACVK,EAAKF,EAAKF,EAEhB,OADcnB,KAAKC,IAAIsB,EAAKR,EAAKO,EAAKN,EAAKE,EAAKG,EAAKD,EAAKD,GAAMnB,KAAKwB,KAAKxB,KAAKyB,IAAIH,EAAI,GAAKtB,KAAKyB,IAAIF,EAAI,KAC5FT,CACD,EAgLd,OA7KA5D,EAAAA,EAAAA,YAAU,KACR,GAAiB,OAAbN,EAAmB,CAAC,IAAD8E,EAAAC,EAAAC,EAAAC,EAAAC,EACrB,MAAMC,EAAWvF,EAAewB,OAASzB,EAAgByB,OACnDgE,EAAgE,GAChEC,EAAgE,EAChEC,EAAqBzF,EAAasF,EAAY,GAC9CI,EAAqB1F,EAAasF,EAAY,GAG9CK,EAAoB7F,EAAgBA,EAAgByB,OAAS,GAE7DqE,EAAahG,EAAKgE,KAAKiC,GAAaA,EAAIF,KACxCG,EAAWvC,KAAKwC,OAAOH,EAAWxE,QAAQ4E,IAAeC,MAAMD,MAC/DE,EAAW3C,KAAK4C,OAAOP,EAAWxE,QAAQ4E,IAAeC,MAAMD,MAK/DI,GAAYC,EAAAA,EAAAA,MACfC,OAAO,CAACR,EAAUI,IAClBK,cAAcrH,IACb,MAAMsH,EAAIjD,KAAK4C,IAAI,EAAG5C,KAAKwC,IAAI,EAAG7G,IAClC,MAAO,qBACHqE,KAAK4C,IAAI,EAAG5C,KAAKwC,IAAI,IAAKxC,KAAKkD,MAAM,MAAQD,GAAK,QAAUA,GAAK,SAAWA,GAAK,SAAWA,GAAK,SAAe,SAAJA,0BAC5GjD,KAAK4C,IAAI,EAAG5C,KAAKwC,IAAI,IAAKxC,KAAKkD,MAAM,MAAQD,GAAK,OAASA,GAAK,QAAUA,GAAK,QAAUA,GAAK,QAAc,OAAJA,0BACxGjD,KAAK4C,IAAI,EAAG5C,KAAKwC,IAAI,IAAKxC,KAAKkD,MAAM,KAAOD,GAAK,OAASA,GAAK,SAAWA,GAAK,MAAQA,GAAK,SAAe,QAAJA,uBACzG,IAGA7D,EAAiBxC,EAASS,QAGX,IAAD8F,EAApB,GAAI/D,EAC0C,QAA5C+D,EAAA/D,EAAegE,cAAc,uBAAe,IAAAD,GAA5CA,EAA8CE,SAI5CjE,GACFA,EAAeC,iBAAiB,UAAUR,SAASyE,GAAWA,EAAOD,WAEvE,MAAME,EAAeA,KACnB,MAAMC,EAAOC,OAAOD,KAAKnH,EAAK,IAExBqH,EADgBF,EAAKnD,KAAKsD,GAAQtH,EAAKgE,KAAKnC,GAAWA,EAAEyF,KAAM9F,QAAQ4E,GAAiB,OAANA,MAC5DpC,KAAKoC,GAC3BA,EAAEmB,OAAOjI,IAAY+G,MAAM/G,IAAY,OAANA,IAAoB,SAClD,WAET,OAAO8H,OAAOI,YAAYL,EAAKnD,KAAI,CAACyD,EAAGhF,IAAM,CAAC0E,EAAK1E,GAAI,CAAEiF,KAAML,EAAM5E,OAAO,EA0B9E,GAvBAhC,EAASO,SAAU2G,EAAAA,EAAAA,IAAAA,CAAYpH,EAASS,SACrCvB,MAAMW,GACNV,OAAOW,GACPL,KAAKA,GACL4H,WAAWV,KACXW,MAAM,IACNC,eAAe,IACfC,SAAS,CAAC,SACVC,UAAU,GACVC,OAAOpG,GACFA,GAAKkE,KAAalE,GAAsB,SAAjBA,EAAEkE,GACpBS,EAAU3E,EAAEkE,IAEZ,SAGVmC,aACAC,SACAC,cACAC,UAAU,YAIRtF,EACH,OAIFtC,EAASO,QAAQsH,GAAG,YAAY,KAC9B7H,EAASO,QAAQM,cACjBhB,GAAkB,IAIwB,QAA5C+E,EAAAtC,EAAegE,cAAc,uBAAe,IAAA1B,GAA5CA,EAA8CkD,iBAAiB,aAAa,SAA0BC,GACpG,MAAM,QAAE3E,EAAO,QAAEe,GAAY4D,EAC7B5F,EAAqB,CAAEiB,UAASe,WAClC,IAE4C,QAA5CU,EAAAvC,EAAegE,cAAc,uBAAe,IAAAzB,GAA5CA,EAA8CiD,iBAAiB,YAAY,KACzE5H,EAAkB,GAAG,IAIvBoC,EAAeC,iBAAiB,qBAAqBR,SAASiG,IAC5D,MAAMC,EAAgBD,EAAE7J,UACpB8G,EAAWzF,GACbwI,EAAE5J,aAAa,YAAa,eAE9B4J,EAAE5J,aAAa,IAAK,OACpB4J,EAAE5J,aAAa,IAAK,MACS4J,EAAEE,wBAAwBlJ,MAC5BoG,IACzB4C,EAAE7J,WAAYgK,EAAAA,EAAAA,IAA0BF,EAAe/C,GACnD+C,IAAkBD,EAAE7J,WACtBX,EAAoB,qBAAsByK,EAAeD,GAE7D,IAIF1F,EAAeC,iBAAiB,yBAAyBR,SAASiG,IAChE,MAAMC,EAAgBD,EAAE7J,UACK6J,EAAEE,wBAAwBlJ,MAC5BqG,IACzB2C,EAAE7J,WAAYgK,EAAAA,EAAAA,IAA0BF,EAAe9C,GACnD8C,IAAkBD,EAAE7J,WACtBX,EAAoB,qBAAsByK,EAAeD,GAE7D,IAIF,MAAMI,EAAQC,MAAMC,KAAK,CAAEpH,OAAQ,KAAM,CAAC8F,EAAGhF,IAAMA,EAAI,IACjDuG,EAAKvI,EAASO,QAAQiI,IACzBC,OAAO,QACPA,OAAO,kBACPC,KAAK,KAAM,UACXA,KAAK,KAAM,MACXA,KAAK,KAAM,QACXA,KAAK,KAAM,MAEdN,EAAMrG,SAAS4G,IACbJ,EAAGE,OAAO,QACPC,KAAK,SAAoB,IAAPC,EAAH,KACfC,MAAM,aAAc7C,EAAUN,EAAWkD,GAAQ9C,EAAWJ,IAAW,IAK5E,MAAMoD,EAAsC,QAAnB/D,EAAGhF,EAASS,eAAO,IAAAuE,GAAsB,QAAtBC,EAAhBD,EAAkBwB,cAAc,cAAM,IAAAvB,OAAtB,EAAhBA,EAAwCmD,wBAC9DY,EAA4B,QAAnB9D,EAAGlF,EAASS,eAAO,IAAAyE,OAAA,EAAhBA,EAAkBsB,cAAc,2BAClD,IAAKwC,EAAW,OAChB,MAAMC,EAAyB,OAATD,QAAS,IAATA,OAAS,EAATA,EAAWZ,wBAC3Bc,EAA8B,OAATF,QAAS,IAATA,OAAS,EAATA,EAAWpG,aAAa,aAEnD,IAAKsG,EAAoB,OACzB,MAAMC,EAA2BrG,WAAWoG,EAAmBnG,MAAM,KAAK,GAAGA,MAAM,KAAK,IACxF,GAAIgG,EAAqB,CACV7I,EAASO,QAAQiI,IAAIC,OAAO,QAEtCC,KAAK,IAAKO,EAA2B,IACrCP,KAAK,IAAK,GACVA,KAAK,QAAS,IACdA,KAAK,SAAUK,EAAc9J,OAAS,IACtC2J,MAAM,OAAQ,eACnB,CACF,IACC,CAGDrJ,EACAI,EACAC,EACAF,EACAD,EACAL,EACAI,EACA2C,EACArC,EACAD,KAGKqJ,EAAAA,EAAAA,GAAA,OAAKC,IAAKrJ,EAAUsJ,GAAG,UAAUR,MAAO,CAAE5J,MAAOG,EAAMH,MAAOC,OAAQE,EAAMF,QAAUoK,UAAU,aAAc,EAmDvH,MAhDiClK,IAC/B,MAAMmK,GAAUvJ,EAAAA,EAAAA,QAAuB,OACjC,MAAEwJ,IAAUC,EAAAA,EAAAA,MAEZ,aAAEC,EAAY,YAAEC,EAAW,gBAAEC,IAAoBC,EAAAA,EAAAA,OAEhDC,EAAYC,IAAiB3J,EAAAA,EAAAA,WAAS,GACvC4J,GAAahK,EAAAA,EAAAA,UAgBnB,OAdAK,EAAAA,EAAAA,YAAU,KACRuJ,EAAgBL,EAAQ/I,QAAQ,GAC/B,CAACoJ,KAEJvJ,EAAAA,EAAAA,YAAU,KACR0J,GAAc,GACVC,EAAWxJ,SACbyJ,aAAaD,EAAWxJ,SAE1BwJ,EAAWxJ,QAAU0J,YAAW,KAC9BH,GAAc,EAAM,GACnB,IAAI,GACN,CAACL,EAAcC,KAGhBR,EAAAA,EAAAA,GAAA,OACEC,IAAKG,EACLY,KAAGC,EAAAA,EAAAA,IAAE,CACHC,SAAU,SACVC,KAAM,IACNC,WAAY,OACZC,SAAU,EACV,aAAc,CACZC,gBAAiBjB,EAAMkB,OAAOC,mBAEhC,wBAAyB,CACvBC,KAAMpB,EAAMkB,OAAOG,cAEtB,IAACC,SAEDhB,GACCX,EAAAA,EAAAA,GAAC4B,EAAAA,GAA+B,KAEhC5B,EAAAA,EAAAA,GAAChK,EAA2B,IAAKC,EAAOH,MAAO0K,EAAazK,OAAQwK,KAElE,C", "sources": ["experiment-tracking/components/runs-charts/components/charts/ParallelCoordinatesPlot.tsx"], "sourcesContent": ["import React, { useCallback, useEffect, useRef, useState } from 'react';\nimport { useDesignSystemTheme } from '@databricks/design-system';\nimport Parcoords from 'parcoord-es';\nimport 'parcoord-es/dist/parcoords.css';\nimport { scaleSequential } from 'd3-scale';\nimport { useDynamicPlotSize } from '../RunsCharts.common';\nimport './ParallelCoordinatesPlot.css';\nimport { truncateChartMetricString } from '../../../../utils/MetricsUtils';\nimport { useRunsChartTraceHighlight } from '../../hooks/useRunsChartTraceHighlight';\nimport { RunsChartCardLoadingPlaceholder } from '../cards/ChartCard.common';\n\n/**\n * Attaches custom tooltip to the axis label inside SVG\n */\nconst attachCustomTooltip = (toolTipClass: string, labelText: string, targetLabel: Element) => {\n  const tooltipPadding = 4;\n  const svgNS = 'http://www.w3.org/2000/svg';\n  const tooltipGroup = document.createElementNS(svgNS, 'g');\n  const newRect = document.createElementNS(svgNS, 'rect');\n  const newText = document.createElementNS(svgNS, 'text');\n  newText.innerHTML = labelText;\n  newText.setAttribute('fill', 'black');\n  tooltipGroup.classList.add(toolTipClass);\n  tooltipGroup.appendChild(newRect);\n  tooltipGroup.appendChild(newText);\n  targetLabel.parentNode?.insertBefore(tooltipGroup, targetLabel.nextSibling);\n\n  const textBBox = newText.getBBox();\n\n  newRect.setAttribute('x', (textBBox.x - tooltipPadding).toString());\n  newRect.setAttribute('y', (textBBox.y - tooltipPadding).toString());\n  newRect.setAttribute('width', (textBBox.width + 2 * tooltipPadding).toString());\n  newRect.setAttribute('height', (textBBox.height + 2 * tooltipPadding).toString());\n  newRect.setAttribute('fill', 'white');\n};\n\nconst ParallelCoordinatesPlotImpl = (props: {\n  data: any;\n  metricKey: string;\n  selectedParams: string[];\n  selectedMetrics: string[];\n  onHover: (runUuid?: string) => void;\n  onUnhover: () => void;\n  closeContextMenu: () => void;\n  width: number;\n  height: number;\n  axesRotateThreshold: number;\n  selectedRunUuid: string | null;\n}) => {\n  // De-structure props here so they will be easily used\n  // as hook dependencies later on\n  const {\n    onHover,\n    onUnhover,\n    selectedRunUuid,\n    data,\n    axesRotateThreshold,\n    selectedMetrics,\n    selectedParams,\n    width: chartWidth,\n    height: chartHeight,\n    closeContextMenu,\n  } = props;\n  const chartRef = useRef<HTMLDivElement>(null);\n  const parcoord: any = useRef<null>();\n\n  // Keep the state of the actually hovered run internally\n  const [hoveredRunUuid, setHoveredRunUuid] = useState('');\n\n  // Basing on the stateful hovered run uuid, call tooltip-related callbacks\n  useEffect(() => {\n    if (hoveredRunUuid) {\n      onHover?.(hoveredRunUuid);\n    } else {\n      onUnhover?.();\n    }\n  }, [hoveredRunUuid, onHover, onUnhover]);\n\n  // Memoize this function so it won't cause dependency re-triggers\n  const getActiveData = useCallback(() => {\n    if (parcoord.current.brushed() !== false) return parcoord.current.brushed();\n    return parcoord.current.data();\n  }, []);\n\n  const { onHighlightChange } = useRunsChartTraceHighlight();\n\n  // Listener that will be called when the highlight changes\n  const highlightListener = useCallback(\n    (traceUuid: string | null) => {\n      if (!traceUuid) {\n        parcoord.current.unhighlight();\n        return;\n      }\n      // Get immediate displayed runs data\n      const displayedData: { uuid: string; [k: string]: number | string }[] = getActiveData();\n\n      const runsToHighlight = displayedData.filter(({ uuid }) => traceUuid === uuid);\n\n      if (runsToHighlight.length) {\n        parcoord.current.highlight(runsToHighlight);\n      } else {\n        parcoord.current.unhighlight();\n      }\n    },\n    [getActiveData],\n  );\n\n  useEffect(() => onHighlightChange(highlightListener), [onHighlightChange, highlightListener]);\n\n  // Basing on the stateful hovered run uuid and selected run uuid, determine\n  // which runs should be highlighted\n  useEffect(() => {\n    if (!parcoord.current) {\n      return;\n    }\n    // Get immediate active data\n    const activeData = getActiveData();\n\n    // Get all (at most two) runs that are highlighted and/or selected\n    const runsToHighlight = activeData.filter((d: any) => [hoveredRunUuid, selectedRunUuid].includes(d.uuid));\n\n    // Either select them or unselect all\n    if (runsToHighlight.length) {\n      parcoord.current.highlight(runsToHighlight);\n    } else {\n      parcoord.current.unhighlight();\n    }\n  }, [hoveredRunUuid, selectedRunUuid, getActiveData]);\n\n  const getClickedLines = useCallback(\n    (mouseLocation: { offsetX: number; offsetY: number }) => {\n      const clicked: [number, number][] = [];\n      const activeData = getActiveData();\n      if (activeData.length === 0) return false;\n\n      const graphCentPts = getCentroids();\n      if (graphCentPts.length === 0) return false;\n\n      // find between which axes the point is\n      const potentialAxeNum: number | boolean = findAxes(mouseLocation, graphCentPts[0]);\n      if (!potentialAxeNum) return false;\n      const axeNum: number = potentialAxeNum;\n\n      graphCentPts.forEach((d: [number, number][], i: string | number) => {\n        if (isOnLine(d[axeNum - 1], d[axeNum], mouseLocation, 2)) {\n          clicked.push(activeData[i]);\n        }\n      });\n\n      return [clicked];\n    },\n    [getActiveData],\n  );\n\n  const highlightLineOnHover = useCallback(\n    (mouseLocation: { offsetX: number; offsetY: number }) => {\n      // compute axes locations\n      const axes_left_bounds: number[] = [];\n      let axes_width = 0;\n      const wrapperElement = chartRef.current;\n\n      if (!wrapperElement) {\n        return;\n      }\n\n      wrapperElement.querySelectorAll('.dimension').forEach(function getAxesBounds(element) {\n        const transformValue = element.getAttribute('transform');\n        // transformValue is a string like \"transform(100)\"\n        if (transformValue) {\n          const parsedTransformValue = parseFloat(transformValue.split('(')[1].split(')')[0]);\n          axes_left_bounds.push(parsedTransformValue);\n          const { width } = (element as SVGGraphicsElement).getBBox();\n          if (axes_width === 0) axes_width = width;\n        }\n      });\n\n      const axes_locations: any[] = [];\n      for (let i = 0; i < axes_left_bounds.length; i++) {\n        axes_locations.push(axes_left_bounds[i] + axes_width / 2);\n      }\n\n      let clicked = [];\n\n      const clickedData = getClickedLines(mouseLocation);\n\n      let foundRunUuid = '';\n      if (clickedData && clickedData[0].length !== 0) {\n        [clicked] = clickedData;\n\n        if (clicked.length > 1) {\n          clicked = [clicked[1]];\n        }\n\n        // check if the mouse is over an axis with tolerance of 10px\n        if (axes_locations.some((x) => Math.abs(x - mouseLocation.offsetX) < 10)) {\n          // We are hovering over axes, do nothing\n        } else {\n          const runData: any = clicked[0];\n          foundRunUuid = runData['uuid'];\n        }\n      }\n      setHoveredRunUuid(foundRunUuid);\n    },\n    [chartRef, getClickedLines],\n  );\n\n  const getCentroids = () => {\n    const margins = parcoord.current.margin();\n    const brushedData = parcoord.current.brushed().length ? parcoord.current.brushed() : parcoord.current.data();\n\n    return brushedData.map((d: any) => {\n      const centroidPoints = parcoord.current.compute_real_centroids(d);\n      return centroidPoints.map((p: any[]) => [p[0] + margins.left, p[1] + margins.top]);\n    });\n  };\n\n  const findAxes = (testPt: { offsetX: number; offsetY: number }, cenPts: string | any[]) => {\n    // finds between which two axis the mouse is\n    const x = testPt.offsetX;\n\n    // make sure it is inside the range of x\n    if (cenPts[0][0] > x) return false;\n    if (cenPts[cenPts.length - 1][0] < x) return false;\n\n    // find between which segment the point is\n    for (let i = 0; i < cenPts.length; i++) {\n      if (cenPts[i][0] > x) return i;\n    }\n    return false;\n  };\n\n  const isOnLine = (\n    startPt: [number, number],\n    endPt: [number, number],\n    testPt: { offsetX: number; offsetY: number },\n    tol: number,\n  ) => {\n    // check if test point is close enough to a line\n    // between startPt and endPt. close enough means smaller than tolerance\n    const x0 = testPt.offsetX;\n    const y0 = testPt.offsetY;\n    const [x1, y1] = startPt;\n    const [x2, y2] = endPt;\n    const Dx = x2 - x1;\n    const Dy = y2 - y1;\n    const delta = Math.abs(Dy * x0 - Dx * y0 - x1 * y2 + x2 * y1) / Math.sqrt(Math.pow(Dx, 2) + Math.pow(Dy, 2));\n    if (delta <= tol) return true;\n    return false;\n  };\n\n  useEffect(() => {\n    if (chartRef !== null) {\n      const num_axes = selectedParams.length + selectedMetrics.length;\n      const axesLabelTruncationThreshold = num_axes > axesRotateThreshold ? 15 : 15;\n      const tickLabelTruncationThreshold = num_axes > axesRotateThreshold ? 9 : 9;\n      const maxAxesLabelWidth = (chartWidth / num_axes) * 0.8;\n      const maxTickLabelWidth = (chartWidth / num_axes) * 0.4;\n\n      // last element of selectedMetrics is the primary metric\n      const metricKey: string = selectedMetrics[selectedMetrics.length - 1];\n      // iterate through runs in data to find max and min of metricKey\n      const metricVals = data.map((run: any) => run[metricKey]);\n      const minValue = Math.min(...metricVals.filter((v: number) => !isNaN(v)));\n      const maxValue = Math.max(...metricVals.filter((v: number) => !isNaN(v)));\n\n      // use d3 scale to map metric values to colors\n      // color math is from interpolateTurbo in d3-scale-chromatic https://github.com/d3/d3-scale-chromatic/blob/main/src/sequential-multi/turbo.js\n      // prettier-ignore\n      const color_set = scaleSequential()\n        .domain([minValue, maxValue])\n        .interpolator((x) => {\n          const t = Math.max(0, Math.min(1, x));\n          return `rgb(\n            ${Math.max(0, Math.min(255, Math.round(34.61 + t * (1172.33 - t * (10793.56 - t * (33300.12 - t * (38394.49 - t * 14825.05)))))))},\n            ${Math.max(0, Math.min(255, Math.round(23.31 + t * (557.33 + t * (1225.33 - t * (3574.96 - t * (1073.77 + t * 707.56)))))))},\n            ${Math.max(0, Math.min(255, Math.round(27.2 + t * (3211.1 - t * (15327.97 - t * (27814 - t * (22569.18 - t * 6838.66)))))))}\n          )`;\n        });\n\n      const wrapperElement = chartRef.current;\n\n      // clear the existing chart state\n      if (wrapperElement) {\n        wrapperElement.querySelector('#wrapper svg')?.remove();\n      }\n\n      // clear old canvases if they exist\n      if (wrapperElement) {\n        wrapperElement.querySelectorAll('canvas').forEach((canvas) => canvas.remove());\n      }\n      const getAxesTypes = () => {\n        const keys = Object.keys(data[0]);\n        const nonNullValues = keys.map((key) => data.map((d: any) => d[key]).filter((v: any) => v !== null));\n        const types = nonNullValues.map((v: any) => {\n          if (v.every((x: any) => !isNaN(x) && x !== null)) return 'number';\n          return 'string';\n        });\n        return Object.fromEntries(keys.map((_, i) => [keys[i], { type: types[i] }]));\n      };\n\n      parcoord.current = Parcoords()(chartRef.current)\n        .width(chartWidth)\n        .height(chartHeight)\n        .data(data)\n        .dimensions(getAxesTypes())\n        .alpha(0.8)\n        .alphaOnBrushed(0.1)\n        .hideAxis(['uuid'])\n        .lineWidth(1)\n        .color((d: any) => {\n          if (d && metricKey in d && d[metricKey] !== 'null') {\n            return color_set(d[metricKey]);\n          } else {\n            return '#f33';\n          }\n        })\n        .createAxes()\n        .render()\n        .reorderable()\n        .brushMode('1D-axes');\n\n      // add hover event\n\n      if (!wrapperElement) {\n        return;\n      }\n\n      // if brushing, clear selected lines\n      parcoord.current.on('brushend', () => {\n        parcoord.current.unhighlight();\n        closeContextMenu();\n      });\n\n      // Add event listeners just once\n      wrapperElement.querySelector('#wrapper svg')?.addEventListener('mousemove', function mouseMoveHandler(ev: Event) {\n        const { offsetX, offsetY } = ev as MouseEvent;\n        highlightLineOnHover({ offsetX, offsetY });\n      });\n\n      wrapperElement.querySelector('#wrapper svg')?.addEventListener('mouseout', () => {\n        setHoveredRunUuid('');\n      });\n\n      // rotate and truncate axis labels\n      wrapperElement.querySelectorAll('.parcoords .label').forEach((e) => {\n        const originalLabel = e.innerHTML;\n        if (num_axes > axesRotateThreshold) {\n          e.setAttribute('transform', 'rotate(-30)');\n        }\n        e.setAttribute('y', '-20');\n        e.setAttribute('x', '20');\n        const width_pre_truncation = e.getBoundingClientRect().width;\n        if (width_pre_truncation > maxAxesLabelWidth) {\n          e.innerHTML = truncateChartMetricString(originalLabel, axesLabelTruncationThreshold);\n          if (originalLabel !== e.innerHTML) {\n            attachCustomTooltip('axis-label-tooltip', originalLabel, e);\n          }\n        }\n      });\n\n      // truncate tick labels\n      wrapperElement.querySelectorAll('.parcoords .tick text').forEach((e) => {\n        const originalLabel = e.innerHTML;\n        const width_pre_truncation = e.getBoundingClientRect().width;\n        if (width_pre_truncation > maxTickLabelWidth) {\n          e.innerHTML = truncateChartMetricString(originalLabel, tickLabelTruncationThreshold);\n          if (originalLabel !== e.innerHTML) {\n            attachCustomTooltip('tick-label-tooltip', originalLabel, e);\n          }\n        }\n      });\n\n      // draw color bar\n      const stops = Array.from({ length: 10 }, (_, i) => i / 9);\n      const lg = parcoord.current.svg\n        .append('defs')\n        .append('linearGradient')\n        .attr('id', 'mygrad')\n        .attr('x2', '0%')\n        .attr('y1', '100%')\n        .attr('y2', '0%'); // Vertical linear gradient\n\n      stops.forEach((stop) => {\n        lg.append('stop')\n          .attr('offset', `${stop * 100}%`)\n          .style('stop-color', color_set(minValue + stop * (maxValue - minValue)));\n      });\n\n      // place the color bar right after the last axis\n      // D3's select() has a hard time inside shadow DOM, let's use querySelector instead\n      const parcoord_dimensions = chartRef.current?.querySelector('svg')?.getBoundingClientRect();\n      const last_axes = chartRef.current?.querySelector('.dimension:last-of-type');\n      if (!last_axes) return;\n      const last_axes_box = last_axes?.getBoundingClientRect();\n      const last_axes_location = last_axes?.getAttribute('transform');\n      // last_axes_location is a string like \"transform(100)\"\n      if (!last_axes_location) return;\n      const last_axes_location_value = parseFloat(last_axes_location.split('(')[1].split(')')[0]);\n      if (parcoord_dimensions) {\n        const rect = parcoord.current.svg.append('rect');\n        rect\n          .attr('x', last_axes_location_value + 20)\n          .attr('y', 0)\n          .attr('width', 20)\n          .attr('height', last_axes_box.height - 40)\n          .style('fill', 'url(#mygrad)');\n      }\n    }\n  }, [\n    // Don't retrigger this useEffect on the entire props object update, only\n    // on the fields that are actually relevant\n    data,\n    chartWidth,\n    chartHeight,\n    selectedParams,\n    selectedMetrics,\n    onHover,\n    axesRotateThreshold,\n    highlightLineOnHover,\n    chartRef,\n    closeContextMenu,\n  ]);\n\n  return <div ref={chartRef} id=\"wrapper\" style={{ width: props.width, height: props.height }} className=\"parcoords\" />;\n};\n\nconst ParallelCoordinatesPlot = (props: any) => {\n  const wrapper = useRef<HTMLDivElement>(null);\n  const { theme } = useDesignSystemTheme();\n\n  const { layoutHeight, layoutWidth, setContainerDiv } = useDynamicPlotSize();\n\n  const [isResizing, setIsResizing] = useState(true);\n  const timeoutRef = useRef<ReturnType<typeof setTimeout>>();\n\n  useEffect(() => {\n    setContainerDiv(wrapper.current);\n  }, [setContainerDiv]);\n\n  useEffect(() => {\n    setIsResizing(true);\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n    timeoutRef.current = setTimeout(() => {\n      setIsResizing(false);\n    }, 300); // Unblock after 300 ms\n  }, [layoutHeight, layoutWidth]);\n\n  return (\n    <div\n      ref={wrapper}\n      css={{\n        overflow: 'hidden',\n        flex: '1',\n        paddingTop: '20px',\n        fontSize: 0,\n        '.parcoords': {\n          backgroundColor: theme.colors.backgroundPrimary,\n        },\n        '.parcoords text.label': {\n          fill: theme.colors.textPrimary,\n        },\n      }}\n    >\n      {isResizing ? (\n        <RunsChartCardLoadingPlaceholder />\n      ) : (\n        <ParallelCoordinatesPlotImpl {...props} width={layoutWidth} height={layoutHeight} />\n      )}\n    </div>\n  );\n};\n\nexport default ParallelCoordinatesPlot;\n"], "names": ["attachCustomTooltip", "toolTipClass", "labelText", "targetLabel", "_targetLabel$parentNo", "svgNS", "tooltipGroup", "document", "createElementNS", "newRect", "newText", "innerHTML", "setAttribute", "classList", "add", "append<PERSON><PERSON><PERSON>", "parentNode", "insertBefore", "nextS<PERSON>ling", "textBBox", "getBBox", "x", "toString", "y", "width", "height", "ParallelCoordinatesPlotImpl", "props", "onHover", "onUnhover", "selectedRunUuid", "data", "axesRotateThreshold", "selectedMetrics", "selectedPara<PERSON>", "chartWidth", "chartHeight", "closeContextMenu", "chartRef", "useRef", "parcoord", "hoveredRunUuid", "setHoveredRunUuid", "useState", "useEffect", "getActiveData", "useCallback", "current", "brushed", "onHighlightChange", "useRunsChartTraceHighlight", "highlightListener", "traceUuid", "unhighlight", "runsToHighlight", "filter", "_ref", "uuid", "length", "highlight", "d", "includes", "getClickedLines", "mouseLocation", "clicked", "activeData", "graphCentPts", "getCentroids", "potentialAxeNum", "findAxes", "axe<PERSON>um", "for<PERSON>ach", "i", "isOnLine", "push", "highlightLineOnHover", "axes_left_bounds", "axes_width", "wrapperElement", "querySelectorAll", "element", "transformValue", "getAttribute", "parsedTransformValue", "parseFloat", "split", "axes_locations", "clickedData", "foundRunUuid", "some", "Math", "abs", "offsetX", "margins", "margin", "map", "compute_real_centroids", "p", "left", "top", "testPt", "cenPts", "startPt", "endPt", "tol", "x0", "y0", "offsetY", "x1", "y1", "x2", "y2", "Dx", "<PERSON><PERSON>", "sqrt", "pow", "_wrapperElement$query2", "_wrapperElement$query3", "_chartRef$current", "_chartRef$current$que", "_chartRef$current2", "num_axes", "axesLabelTruncationThreshold", "tickLabelTruncationThreshold", "maxAxesLabel<PERSON>th", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "metricKey", "metricVals", "run", "minValue", "min", "v", "isNaN", "maxValue", "max", "color_set", "scaleSequential", "domain", "interpolator", "t", "round", "_wrapperElement$query", "querySelector", "remove", "canvas", "getAxesTypes", "keys", "Object", "types", "key", "every", "fromEntries", "_", "type", "Parcoords", "dimensions", "alpha", "alphaOnBrushed", "<PERSON><PERSON><PERSON><PERSON>", "lineWidth", "color", "createAxes", "render", "reorderable", "brushMode", "on", "addEventListener", "ev", "e", "originalLabel", "getBoundingClientRect", "truncateChartMetricString", "stops", "Array", "from", "lg", "svg", "append", "attr", "stop", "style", "parcoord_dimensions", "last_axes", "last_axes_box", "last_axes_location", "last_axes_location_value", "_jsx", "ref", "id", "className", "wrapper", "theme", "useDesignSystemTheme", "layoutHeight", "<PERSON><PERSON><PERSON><PERSON>", "setContainerDiv", "useDynamicPlotSize", "isResizing", "setIsResizing", "timeoutRef", "clearTimeout", "setTimeout", "css", "_css", "overflow", "flex", "paddingTop", "fontSize", "backgroundColor", "colors", "backgroundPrimary", "fill", "textPrimary", "children", "RunsChartCardLoadingPlaceholder"], "sourceRoot": ""}