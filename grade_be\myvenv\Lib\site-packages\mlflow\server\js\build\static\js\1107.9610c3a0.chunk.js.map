{"version": 3, "file": "static/js/1107.9610c3a0.chunk.js", "mappings": "kZAkBO,SAASA,EAAaC,GAA2D,IAA1D,QAAEC,EAAO,OAAEC,EAAM,OAAEC,EAAS,CAAC,EAAC,OAAEC,EAAM,OAAEC,GAAeL,EACnF,OACEM,EAAAA,EAAAA,GAACC,EAAAA,IAAW,CACVC,UAAU,kBACV,eAAcJ,EACdK,WAAYP,EACZD,QAASA,EACTI,OAAQA,EACRK,KAAK,SACLC,YAAY,EACZC,MAAOT,GAGb,C,qCCMA,MAAMU,UAA4BC,EAAAA,UAChCC,MAAAA,GACE,MAAM,SAAEC,GAAaC,KAAKC,MAC1B,OACEZ,EAAAA,EAAAA,GAAA,OAAKE,UAAU,kBAAiBW,SAC7BH,EAASI,OAAS,EAAIH,KAAKI,qBAAuBJ,KAAKK,eAAeN,EAAS,KAGtF,CAEAM,cAAAA,CAAeC,GACb,MAAM,WAAEC,EAAU,cAAEC,EAAa,WAAEC,EAAU,WAAEC,EAAU,KAAEC,GAASX,KAAKC,MACnEjB,EAAU,CACd,CACE4B,MAAOD,EAAKE,cAAc,CAAAC,GAAA,SACxBC,eAAe,WAKjBC,UAAW,YACXC,OAAQA,CAACC,EAAQC,IAAYD,EAAEE,UAAYD,EAAEC,WAAa,EAAIF,EAAEE,UAAYD,EAAEC,UAAY,EAAI,EAC9FC,MAtCe,QAwCdrB,KAAKsB,eAEV,OAA6B,IAAtBf,EAAWJ,OAAe,MAC/Bd,EAAAA,EAAAA,GAACP,EAAa,CACZE,QAASA,EACTC,OAAQsC,EAAqBjB,EAASC,EAAYC,EAAeC,EAAYC,EAAYC,GACzFvB,OAAQ,CAAEoC,EAjDK,MAoDrB,CAEApB,kBAAAA,GACE,MAAM,iBAAEqB,EAAgB,SAAE1B,EAAQ,gBAAE2B,EAAe,WAAEnB,EAAU,cAAEC,EAAa,WAAEC,EAAU,WAAEC,EAAU,KAAEC,GACtGX,KAAKC,MACDjB,EAAU,CACd,CACE4B,MAAOD,EAAKE,cAAc,CAAAC,GAAA,SACxBC,eAAe,QAKjBC,UAAW,UACXC,OAAQA,CAACC,EAAQC,IAAYD,EAAES,QAAUR,EAAEQ,SAAW,EAAIT,EAAES,QAAUR,EAAEQ,QAAU,EAAI,EACtFN,MAhEe,QAkEdrB,KAAKsB,eAEV,OAAOf,EAAWqB,KAAKR,IAEnBS,EAAAA,EAAAA,IAACC,EAAAA,SAAQ,CAAA5B,SAAA,EACPb,EAAAA,EAAAA,GAAA,MAAAa,SAAKkB,KACL/B,EAAAA,EAAAA,GAACP,EAAa,CACZE,QAASA,EACTC,OAAQ8C,EACNX,EACAK,EACA1B,EACA2B,EACAlB,EACAC,EACAC,EACAC,GAEFvB,OAAQ,CAAEoC,EAvFC,SAyEAJ,IAmBrB,CAEAE,WAAAA,GACE,MAAO,CACL,CACEV,MAAOZ,KAAKC,MAAMU,KAAKE,cAAc,CAAAC,GAAA,SACnCC,eAAe,WAGjBC,UAAW,kBACXC,OAAQA,CAACC,EAAQC,IAAWD,EAAEc,YAAcb,EAAEa,YAC9CX,MAnGa,IAoGbY,UAAU,GAEZ,CACErB,MAAOZ,KAAKC,MAAMU,KAAKE,cAAc,CAAAC,GAAA,SACnCC,eAAe,QAGjBC,UAAW,eACXC,OAAQA,CAACC,EAAQC,IAAWD,EAAEgB,SAAWf,EAAEe,SAC3Cb,MA7Ga,IA8GbY,UAAU,GAEZ,CACErB,MAAOZ,KAAKC,MAAMU,KAAKE,cAAc,CAAAC,GAAA,SACnCC,eAAe,QAGjBC,UAAW,eACXC,OAAQA,CAACC,EAAQC,IAAWD,EAAEiB,SAAWhB,EAAEgB,SAC3Cd,MAvHa,IAwHbY,UAAU,GAGhB,EAGF,MAAMF,EAAuBA,CAC3BX,EACAK,EACA1B,EACA2B,EACAlB,EACAC,EACAC,EACAC,IAEOZ,EAAS6B,KAAI,CAACtB,EAAc8B,KACjC,MAAMT,EAAUD,EAAgBU,GAChC,MAAO,CACLT,QAASA,EACTU,SAAShD,EAAAA,EAAAA,GAACiD,EAAAA,GAAI,CAACC,GAAIC,EAAAA,EAAOC,gBAAgBhB,EAAiBnB,IAAY,GAAIA,GAASJ,SAAEyB,IACtFe,IAAKpC,KACFqC,EAAQrC,EAASc,EAAWZ,EAAeC,EAAYC,EAAYC,GACvE,IAICY,EAAuBA,CAC3BjB,EACAC,EACAC,EACAC,EACAC,EACAC,IAEOJ,EAAWqB,KAAKR,IACd,CACLA,YACAsB,IAAKtB,KACFuB,EAAQrC,EAASc,EAAWZ,EAAeC,EAAYC,EAAYC,OAG1E,IAAA5B,EAAA,CAAA6D,KAAA,SAAA1D,OAAA,qBAAA2D,EAAA,CAAAD,KAAA,SAAA1D,OAAA,qBAAA4D,EAAA,CAAAF,KAAA,SAAA1D,OAAA,qBAEF,MAAMyD,EAAUA,CAACrC,EAAcc,EAAgBZ,EAAoBC,EAAiBC,EAAiBC,KACnG,MAAMoC,EAAeC,EAAUxC,EAAeF,EAASc,GACjD6B,EAAYD,EAAUvC,EAAYH,EAASc,GAC3C8B,EAAYF,EAAUtC,EAAYJ,EAASc,GAC3CY,EAAcmB,EAASJ,GACvBb,EAAWiB,EAASF,GACpBd,EAAWgB,EAASD,GAC1B,MAAO,CACLE,iBACE/D,EAAAA,EAAAA,GAAA,QAAMuB,MAAOoB,EAAaqB,IAAGtE,EAAsBmB,SAChDoD,EAAaP,EAAcpC,KAGhC4C,cACElE,EAAAA,EAAAA,GAAA,QAAMuB,MAAOsB,EAAUmB,IAAGR,EAAsB3C,SAC7CoD,EAAaL,EAAWtC,KAG7B6C,cACEnE,EAAAA,EAAAA,GAAA,QAAMuB,MAAOuB,EAAUkB,IAAGP,EAAsB5C,SAC7CoD,EAAaJ,EAAWvC,KAG7BqB,cACAE,WACAC,WACD,EAGGa,EAAYA,CAACS,EAAgBnD,EAAcc,IAAmBqC,EAAUnD,IAAYmD,EAAUnD,GAASc,GAEvG+B,EAAYO,GAAgBA,GAAUA,EAAOC,MAE7CL,EAAeA,CAACI,EAAa/C,SACtBiD,IAAXF,EACI,GACA/C,EAAKE,cACH,CAAAC,GAAA,SACEC,eAAe,yBAGjB,CACE4C,MAAOD,EAAOC,MACdE,KAAMH,EAAOG,OAyBjBC,GAA8BC,EAAAA,EAAAA,IAAWnE,GAE/C,OAAeoE,EAAAA,EAAAA,KAvBSC,CAACC,EAAYC,KACnC,MAAM,SAAEpE,GAAaoE,EACf1C,EAAmB,CAAC,EACpBjB,EAAgB,CAAC,EACjBC,EAAa,CAAC,EACdC,EAAa,CAAC,EAYpB,OAXAX,EAASqE,SAAS9D,IAChB,MAAM+D,GAAUC,EAAAA,EAAAA,IAAWhE,EAAS4D,GAEpCzC,EAAiBnB,GAAW+D,GAAWA,EAAQE,aAE/C/D,EAAcF,IAAWkE,EAAAA,EAAAA,IAAiBlE,EAAS4D,GAEnDzD,EAAWH,IAAWmE,EAAAA,EAAAA,IAAcnE,EAAS4D,GAE7CxD,EAAWJ,IAAWoE,EAAAA,EAAAA,IAAcpE,EAAS4D,EAAM,IAE9C,CAAEzC,mBAAkBjB,gBAAeC,aAAYC,aAAY,GAMpE,CAAwCoD,G,0CCrPjC,MAAMa,EAAa5F,IAA+D,IAA9D,KAAE6F,EAAI,UAAErF,EAAS,MAAEI,EAAK,QAAEkF,KAAYC,GAAkB/F,EACjF,OACEM,EAAAA,EAAAA,GAAC0F,EAAAA,EAAM,CACLC,YAAY,6DACZC,KAAK,OACL1F,UAAWA,EACXI,MAAO,CAAEuF,QAAS,KAAMvF,GACxBkF,QAASA,KACLC,EAAS5E,SAEZ0E,GACM,ECHN,MAAMO,UAAwBtF,EAAAA,UAAuBuF,WAAAA,GAAA,SAAAC,WAAA,KAS1DC,cAAgB,KACd,MAAM,aAAEf,EAAY,SAAEgB,GAAavF,KAAKC,MACxC,OACEZ,EAAAA,EAAAA,GAAA,OAAAa,SACGqF,EAAS3D,KAAI,CAAA7C,EAA4ByG,KAAW,IAAtC,KAAE5C,EAAI,MAAE6C,EAAK,MAAEC,EAAK,EAAElE,GAAGzC,EACtC,MAAM2D,EAAM,GAAG+C,KAASD,IAClBjD,EAAKC,EAAAA,EAAOC,gBAAgB8B,EAAckB,GAChD,OACEpG,EAAAA,EAAAA,GAACiD,EAAAA,GAAI,CAAWC,GAAIA,EAAGrC,UACrB2B,EAAAA,EAAAA,IAAA,KAAGlC,MAAO,CAAE+F,SAAQxF,SAAA,EAClBb,EAAAA,EAAAA,GAAA,KAAGE,UAAU,yBAAyBI,MAAO,CAAEgG,YAAa,KAC3D,GAAG/C,MAASgD,EAAAA,EAAMtC,aAAa9B,SAHzBkB,EAKJ,KAGP,EAER,KAEFmD,YAAc,KACZ,MAAM,YAAEC,GAAgB9F,KAAKC,MAC7B,OACE4B,EAAAA,EAAAA,IAAA,OAAA3B,SAAA,EACEb,EAAAA,EAAAA,GAAA,QAAAa,SAAM,6BACNb,EAAAA,EAAAA,GAACsF,EAAU,CACTC,MAAMvF,EAAAA,EAAAA,GAAA,KAAGE,UAAU,iBACnBsF,QAASiB,EACTnG,MAAO,CAAEoG,MAAO,QAASC,WAAY,WAEnC,CAER,CAxCFC,iBAAAA,GACEC,SAASC,iBAAiB,UAAWnG,KAAKC,MAAMmG,cAClD,CAEAC,oBAAAA,GACEH,SAASI,oBAAoB,UAAWtG,KAAKC,MAAMmG,cACrD,CAoCAtG,MAAAA,GACE,MAAM,QAAEyG,EAAO,EAAEC,EAAC,EAAEhF,EAAC,oBAAEiF,GAAwBzG,KAAKC,MACpD,OACEZ,EAAAA,EAAAA,GAACqH,EAAAA,IAAa,CACZC,QAAS3G,KAAKsF,gBACd1E,MAAOZ,KAAK6F,cACZe,UAAU,OACVL,QAASA,EACTM,gBAAiBJ,EAAoBvG,UAErCb,EAAAA,EAAAA,GAAA,OACEM,MAAO,CACLmH,KAAMN,EACNO,IAAKvF,EACLwF,SAAU,eAKpB,E,oCCtDK,MAAMC,EAAkB,OAClBC,EAAiB,MAEjBC,EAAmC,IAI1CC,EAA6B,IAGtBC,EAAuBC,IAClC,MAAMC,EAAS,CAAC,YAAaC,OAAOC,KAAKH,EAAQ,GAAGI,QAAQ,KACtDC,EAAOL,EAAQM,SAAQ7I,IAAA,IAAC,QAAEuB,EAAO,QAAEoH,GAAc3I,EAAA,OACrD2I,EAAQ9F,KAAK8B,GAAgB,CAACpD,KAAYkH,OAAOvI,OAAOyE,KAAS,IAEnE,MAAO,CAAC6D,GACLM,OAAOF,GACP/F,KAAKkG,GAAQA,EAAIC,KAAK,OACtBA,KAAK,KAAK,EAuBR,MAAMC,UAAyBnI,EAAAA,UAkCpCuF,WAAAA,CAAYnF,GACVgI,MAAMhI,GAAO,KAlCfiI,YAAa,EAAM,KAMnBC,oBAAc,OACdC,gBAAU,EAMV,KACAC,mBAAqB,KAErB,KACAC,oBAAuBC,KAAaC,IAGpC,KACAC,yBAA2B,KAG3B,KACAC,6BAA+B,IAK/B,KACAC,4BAA8B3I,KAAK0I,6BAA+B,GAAG,KAqBrEE,QAAU,KACR5I,KAAK6I,SAAS,CAAEC,SAAS,GAAO,EAChC,KAEFC,OAAS,KACP/I,KAAK6I,SAAS,CAAEC,SAAS,GAAQ,EACjC,KAEFE,oBAAsB,KAGpBC,OAAO3C,oBAAoB,QAAStG,KAAK4I,SACzCK,OAAO3C,oBAAoB,OAAQtG,KAAK+I,OAAO,EAC/C,KAEFG,cAAgB,KAGdA,cAAclJ,KAAKoI,YACnBpI,KAAKoI,WAAa,IAAI,EACtB,KAEFe,iBAAmB,IACVnJ,KAAKC,MAAMmJ,kBAAkBjJ,SAAWH,KAAKC,MAAMF,SAASI,OACnE,KAEFkJ,iBAAoBC,IAClB,MAAMhC,EAAUtH,KAAKC,MAAMsJ,uBAAuBD,GAClD,IAAKhC,EACH,OAAO,EAGT,MAAMkC,EAAahC,OAAOvI,OAAOqI,GAAS1F,KAAIiB,IAAA,IAAC,UAAE4G,GAAW5G,EAAA,OAAK4G,CAAS,IAEpEC,EAAkBnB,KAAKoB,OAAOH,GACpC,OAAO,IAAII,MAAOC,UAAYH,EA3HmB,MA2HoC,EACrF,KAEFI,kBAAoB,KAClB,MAAM,kBAAEV,EAAiB,SAAErJ,GAAaC,KAAKC,MAE7C,OADuB8J,IAAAA,WAAahK,EAAUqJ,GACxBY,OAAOD,IAAAA,OAAS/J,KAAKqJ,kBAAkB,EAC7D,KAEFY,WAAa,MACFjK,KAAKmJ,oBAA0D,IAApCnJ,KAAK8J,oBAAoB3J,QAsD/D,KACA+J,eAAkBC,IAChB,MAAM,SAAEpK,EAAQ,UAAEqB,EAAS,SAAEgJ,EAAQ,SAAEC,GAAarK,KAAKC,MAEnDqK,EAAgBC,KAAKC,MAAMC,IAAAA,MAASL,EAASM,QAAqB,aAClEC,EAAW,IACZ3K,KAAK4K,iBACLT,IAEC,cACJU,EAAa,mBACbC,EAAkB,UAClBC,EAAS,cACTC,EAAa,eACbC,EAAc,OACdC,EAAM,iBACNC,EAAgB,qBAChBC,GACET,EACJN,EACE7H,EAAAA,EAAO6I,mBACLtL,EACAqB,EACAkJ,EACAQ,EACAI,EACAL,EACAG,EACAC,EACAF,EACAI,EACAC,GAEF,CACEE,SAAS,GAEZ,EACD,KAEFC,mBAAqB,IACZvL,KAAKC,MAAMuL,6BAA6B5J,KAAIkB,IAAA,IAAC,QAAE4E,GAAS5E,EAAA,OAAK4E,EAAQvH,MAAM,IAAEsL,QAAO,CAACvK,EAAGC,IAAMD,EAAIC,GAAG,GAC5G,KAEFuK,kBAAoB,CAAC3L,EAAeQ,KAClC,GAAIP,KAAKuL,sBAAwBnE,EAI/B,OAHAxB,EAAAA,EAAM+F,sBACJ,uFAEKC,QAAQC,QAAQ,IAEzB7L,KAAK6I,SAAS,CAAEiD,SAAS,IACzB,MAAMC,EAAWhM,EACd6H,SAAS9G,GACRP,EAAWqB,KAAKc,IAAQ,CACtBpC,QAASQ,EACTM,UAAWsB,QAIdsH,QAAOgC,IAAA,IAAC,QAAE1L,EAAO,UAAEc,GAAgB4K,EAAA,OAAKhM,KAAKC,MAAMsJ,uBAAuBjJ,GAAS2L,eAAe7K,EAAU,IAC5GQ,KAAIsK,UAAwC,IAAjC,QAAE5L,EAAO,UAAEc,GAAgB+K,EACrC,MAAMC,EAAa,GACbtL,GAAKuL,EAAAA,EAAAA,MACXD,EAAWE,KAAKxL,GAShB,IAAIyL,SARwBvM,KAAKC,MAAMuM,oBACrClM,EACAc,EA3P6B,UA6P7BwC,EACA9C,IAGgC6C,MAAM8I,gBACxC,KAAOF,GAAe,CACpB,GAAIvM,KAAKuL,sBAAwBnE,EAC/B,MAAO,CAAEgF,aAAYM,SAAS,GAGhC,MAAMC,GAAMN,EAAAA,EAAAA,MACZD,EAAWE,KAAKK,GAShBJ,SAP2BvM,KAAKC,MAAMuM,oBACpClM,EACAc,EA5Q2B,KA8Q3BmL,EACAI,IAE2BhJ,MAAM8I,eACrC,CACA,MAAO,CAAEL,aAAYM,SAAS,EAAM,IAExC,OAAOd,QAAQgB,IAAIb,GAAUc,MAAMC,IAE7B9M,KAAKkI,YACPlI,KAAK6I,SAAS,CAAEiD,SAAS,IAEtBgB,EAAQC,OAAMC,IAAA,IAAC,QAAEN,GAASM,EAAA,OAAKN,CAAO,KACzC9G,EAAAA,EAAM+F,sBACJ,sFAGGmB,EAAQlF,SAAQqF,IAAA,IAAC,WAAEb,GAAYa,EAAA,OAAKb,CAAU,MACrD,EACF,KAEFc,SAAYnN,IACV,MAAMqM,EAAkB,GAMxB,OALArM,EAASqE,SAAS9D,IAChB,MAAMQ,GAAKuL,EAAAA,EAAAA,MACXrM,KAAKC,MAAMkN,UAAU7M,GACrB8L,EAAWE,KAAKxL,EAAG,IAEdsL,CAAU,EACjB,KAEFgB,WAAa,KAEX,MAAMlJ,EAAQlE,KAAK4K,cACbyC,EAAqB,IAAIC,IAAIpJ,EAAM4G,qBACnC,cAAED,GAAkB3G,GACpB,6BAAEsH,GAAiCxL,KAAKC,MAGxCqH,EAAUkE,EAA6BxB,QAAQuD,GAAMF,EAAmBG,IAAID,EAAEnM,aASpF,OANAkG,EAAQlD,SAASV,IACf,MAAM+J,EAAS5C,IAAkB6C,EAAAA,IAAehK,EAAOgE,QAAQ,IAAMqC,IAAAA,SAAWrG,EAAOgE,QAAQ,GAAG7D,MAElGH,EAAOgE,QAAQiG,KAAKF,EAAS7H,EAAAA,EAAMgI,0BAA4BhI,EAAAA,EAAMiI,mBAAmB,IAGnFvG,CAAO,EAGhB,KAKAwG,0BAA6B9C,IAC3B,MAAM9G,EAAQlE,KAAK4K,cACbmD,EAAYhE,IAAAA,UAAY7F,EAAMgH,QAC9B8C,EAAchD,EAAgB,MAAQ,SAM5C,IAAKA,GAAkB9G,EAAckH,sBAAyBlH,EAAckH,qBAAqBjL,OAAS,EAMxG,OALA4N,EAAUE,MAAQ,CAChBhJ,KAAM,SACNiJ,MAAQhK,EAAckH,2BAExBpL,KAAKkK,eAAe,CAAEgB,OAAQ6C,EAAW3C,qBAAsB,KAMjE,IAAKlH,EAAMgH,OAAO+C,QAAU/J,EAAMgH,OAAO+C,MAAMC,MAO7C,OANAH,EAAUE,MAAQ,CAChBhJ,KAAM+I,EACNG,WAAW,KACS,QAAhBH,EAAwB,CAAEI,eAAgB,KAAQ,CAAC,QAEzDpO,KAAKkK,eAAe,CAAEgB,OAAQ6C,EAAW3C,qBAAsB,KASjE,IAAIA,EAAuB,GAI3B,MACMiD,EADYnK,EAAMgH,OACI+C,MAAMC,MAC9BlD,EACEqD,EAAU,IAAM,GAClBjD,EAAuBiD,EAQvBN,EAAUE,MAAQ,CAChBhJ,KAAM,MACNkJ,WAAW,EACXC,eAAgB,MAGlBL,EAAUE,MAAQ,CAChBhJ,KAAM,MACNiJ,MAAO,CAAC3F,KAAK+F,IAAID,EAAU,IAAM9F,KAAK+F,IAAI,IAAK/F,KAAK+F,IAAID,EAAU,IAAM9F,KAAK+F,IAAI,KACjFF,eAAgB,KAKpBL,EAAUE,MAAQ,CAChBhJ,KAAM,SACNiJ,MAAO,CAAC3F,KAAKgG,IAAI,GAAIF,EAAU,IAAK9F,KAAKgG,IAAI,GAAIF,EAAU,MAG/DrO,KAAKkK,eAAe,CAAEgB,OAAQ6C,EAAW3C,wBAAuB,EAGlE,KAKAoD,kBAAqBC,IAEnB,MAAMvK,EAAQlE,KAAK4K,cAOb8D,EANuB,CAC3B,CAACC,EAAAA,IAAc,OACf,CAACC,EAAAA,IAAkB,SACnB,CAAClB,EAAAA,IAAc,UAGqBe,EAAEI,OAAOlL,QAAU,SACnDoK,EAAY,IACb7J,EAAMgH,OACT4D,MAAO,CACLX,WAAW,EACXlJ,KAAMyJ,IAGV1O,KAAKkK,eAAe,CAAEW,cAAe4D,EAAEI,OAAOlL,MAAOuH,OAAQ6C,GAAY,EAQ3E,KAQAgB,mBAAsBhB,IACpB/N,KAAKmI,gBAAiB,EACtB,MAAMjE,EAAQlE,KAAK4K,eAIjB,iBAAkBoE,EAClB,iBAAkBC,EAClB,iBAAkBC,EAClB,iBAAkBC,EAClB,kBAAmBC,EACnB,kBAAmBC,EACnB,mBAAoBC,EACpB,mBAAoBC,KACjBC,GACDzB,EAEJ,IAAI0B,EAAe,IACdvL,EAAMgH,UACNsE,GAEDpE,EAAuB,IAAKlH,EAAckH,sBAG9C,MAAMsE,EAAWD,EAAaX,OAAS,CAAC,OACrBlL,IAAfoL,QAA2CpL,IAAfqL,IAC9BS,EAASxB,MAAQ,CAACc,EAAYC,GAC9BS,EAASvB,WAAY,GAEnBoB,IACFG,EAASC,YAAa,GAEpBP,IACFM,EAASvB,WAAY,GAGvB,MAAMyB,EAAWH,EAAaxB,OAAS,CAAC,EAQxC,QAPmBrK,IAAfsL,QAA2CtL,IAAfuL,IAC9BS,EAAS1B,MAAQ,CAACgB,EAAYC,GAC9BS,EAASzB,WAAY,GAEnBmB,IACFM,EAASD,YAAa,GAEpBN,EAAgB,CAClBjE,EAAuB,GACvB,MAAMsD,EAAWxK,EAAMgH,QAAUhH,EAAMgH,OAAO+C,OAAqC,QAA5B/J,EAAMgH,OAAO+C,MAAMhJ,KAAiB,MAAQ,SACnG2K,EAASzB,WAAY,EACrByB,EAAS3K,KAAOyJ,CAClB,CACsB,QAAlBkB,EAAS3K,OACX2K,EAASxB,eAAiB,KAG5BqB,EAAe,IACVA,EACHX,MAAOY,EACPzB,MAAO2B,GAET5P,KAAKkK,eAAe,CAAEgB,OAAQuE,EAAcrE,wBAAuB,EACnE,KAEFyE,kBAAoB,KAClB,MAAMC,EAAMzI,EAAoBrH,KAAKC,MAAMuL,8BACrCuE,EAAO,IAAIC,KAAK,CAACF,GAAM,CAAE7K,KAAM,mCACrCgL,EAAAA,EAAAA,QAAOF,EAAM,cAAc,EAiB7B,KAIAG,kBAAoBC,IAAiC,IAAhC,YAAEC,EAAW,KAAEC,GAAWF,EAE7C,MAAMjM,EAAQlE,KAAK4K,cACb0F,EAAc1G,KAAK2G,MACzB,GACED,EAActQ,KAAKsI,oBAAsBtI,KAAK0I,8BAC9C0H,IAAgBpQ,KAAKyI,yBAErBzI,KAAKwQ,wBAAwB,CAAEJ,cAAaC,SAC5CrQ,KAAKsI,oBAAuBC,KAAaC,QACpC,CAIL,MAAMiI,EAAWzI,EAAiB0I,YAAYL,EAAKD,IAEnDpQ,KAAKqI,mBAAqBY,OAAO0H,YAAW,KAC1C,MAAMC,EAA2B,IAAItD,IAAKpJ,EAAciH,kBACpDyF,EAAyBpD,IAAIiD,GAC/BG,EAAyBC,OAAOJ,GAEhCG,EAAyBE,IAAIL,GAE/BzQ,KAAKkK,eAAe,CAAEiB,iBAAkB4F,MAAMC,KAAKJ,IAA4B,GAC9E5Q,KAAK2I,6BACR3I,KAAKsI,oBAAsBgI,CAC7B,CAGA,OAFAtQ,KAAKyI,yBAA2B2H,GAEzB,CAAK,EAGd,KAIAI,wBAA0BS,IAAiC,IAAhC,YAAEb,EAAW,KAAEC,GAAWY,EAEnDhI,OAAOiI,aAAalR,KAAKqI,oBAEzB,MAAMoI,EAAWzI,EAAiB0I,YAAYL,EAAKD,IAE7Ce,EADed,EAAKzO,KAAKwP,GAAcpJ,EAAiB0I,YAAYU,KACjCpH,QAAQqH,GAAmBA,IAAcZ,IAElF,OADAzQ,KAAKkK,eAAe,CAAEiB,iBAAkBgG,KACjC,CAAK,EACZ,KAEFG,0BAA6B/Q,IAC3B,MAAMgR,EAAqBvR,KAAK4K,cAAcE,oBAAsB,GAC9D0G,EAAgBjR,EAAWyJ,QAAQyH,IAAYF,EAAmBG,SAASD,KACjFzR,KAAKkK,eAAe,CAAEY,mBAAoBvK,IAC1CP,KAAK0L,kBAAkB1L,KAAKC,MAAMF,SAAUyR,GAAe3E,MAAMT,IAC/DpM,KAAK6I,SAAS,CAAEiD,SAAS,IACzB9L,KAAK6I,UAAU8I,IAAc,CAC3BC,kBAAmB,IAAID,EAAUC,qBAAsBxF,MACtD,GACH,EACF,KAEFyF,sBAAyB9G,GAAmB/K,KAAKkK,eAAe,CAAEa,cAAa,KAE/E+G,uBAA0B7G,GAAwBjL,KAAKkK,eAAe,CAAEe,mBAAkB,KAE1F8G,uBAAyBC,IAA+B,IAA9B,IAAEtP,GAAsBsP,EACpC,WAARtP,GACF1C,KAAK6I,SAAS,CAAEoJ,gBAAgB,GAClC,EACA,KAEFC,cAAiB7B,IACfrQ,KAAKmI,gBAAkBnI,KAAKmI,eAG5BwI,YAAW,KACT,GAAI3Q,KAAKmI,eAAgB,CACvBnI,KAAKmI,gBAAiB,EACtB,MAAM,eAAE8J,EAAc,SAAEE,EAAQ,SAAEC,GAAapS,KAAKkE,OAC9C,OACJmO,EACAC,OAAO,QAAEC,EAAO,QAAEC,IAChBnC,EACEoC,EAAmBN,IAAaI,GAAWH,IAAaI,EACxDjN,EAAW8M,EACd1E,MAAK,CAACzM,EAAQC,IAAWA,EAAEK,EAAIN,EAAEM,IACjCI,KAAK8Q,IAAU,CACdjN,MAAOiN,EAAMrC,KAAK5K,MAClB7C,KAAM8P,EAAMrC,KAAKzN,KACjB8C,MAAOgN,EAAMC,SAASC,OAAOlN,MAC7BlE,EAAGkR,EAAMlR,MAGbxB,KAAK6I,SAAS,CACZoJ,gBAAiBA,IAAmBQ,EACpCN,SAAUI,EACVH,SAAUI,EACVK,gBAAiBtN,GAErB,IACC,IAAI,EAxiBPvF,KAAKkE,MAAQ,CACX0N,kBAAmB,GACnBK,gBAAgB,EAChBE,SAAU,EACVC,SAAU,EACVS,gBAAiB,GACjB/J,SAAS,EACTgD,SAAS,GAEX9L,KAAKmI,gBAAiB,EACtBnI,KAAKoI,WAAa,IACpB,CAEA0K,sBAAAA,GACE,OAAO9S,KAAKC,MAAMqK,eAAiBtK,KAAKC,MAAMqK,cAAcnK,OAAS,CACvE,CAkDA8F,iBAAAA,GACEjG,KAAKkI,YAAa,EAClBlI,KAAK0L,kBAAkB1L,KAAKC,MAAMF,SAAUC,KAAK4K,cAAcE,oBAC3D9K,KAAKiK,eAGPhB,OAAO9C,iBAAiB,OAAQnG,KAAK+I,QACrCE,OAAO9C,iBAAiB,QAASnG,KAAK4I,SACtC5I,KAAKoI,WAAa2K,aAAY,KAG5B,GAAI/S,KAAKkE,MAAM4E,QAAS,CACtB,MAAMkK,EAAiBhT,KAAK8J,oBAC5B9J,KAAK0L,kBAAkBsH,EAAgBhT,KAAK4K,cAAcE,oBAC1D9K,KAAKkN,SAAS8F,GAEThT,KAAKiK,eACRjK,KAAKgJ,sBACLhJ,KAAKkJ,gBAET,IACC/B,GAEP,CAEAd,oBAAAA,GACErG,KAAKkI,YAAa,EAClBlI,KAAKgJ,sBACLhJ,KAAKkJ,eACP,CAEA0B,WAAAA,GACE,OAAOhF,EAAAA,EAAMqN,0BAA0BjT,KAAKC,MAAMmK,SAASM,OAC7D,CAEA,uBAAOwI,CAAiB5L,GAEtB,OAAIA,GAAWA,EAAQnH,QAAU4J,IAAAA,MAAQzC,GAAU5D,GAAWA,EAAOgE,SAAqC,IAA1BhE,EAAOgE,QAAQvH,SACtF+G,EAEFD,CACT,CAEA,kBAAOkM,CAAYzI,GACjB,MAAM0I,EAAS3I,IAAAA,MAASC,GAClB2I,EAAOD,GAAUA,EAAO,SAE9B,QAAOC,GAAO9I,KAAKC,MAAM6I,GAAMlT,OAAS,CAC1C,CAkPAmT,WAAAA,GACE,MAAMpP,EAAQlE,KAAK4K,cACnB,OAAO1G,EAAMgH,QAAUhH,EAAMgH,OAAO+C,OAAqC,QAA5B/J,EAAMgH,OAAO+C,MAAMhJ,KAAiB,MAAQ,QAC3F,CAgFA,kBAAOyL,CAAY6C,GAGjB,MAA0B,QAAtBA,EAAatO,KACRsO,EAAa9N,MAIbG,EAAAA,EAAM8K,YAAY6C,EAAa9N,MAAO8N,EAAaC,WAE9D,CA2GA1T,MAAAA,GACE,MAAM,cAAEwK,EAAa,SAAEvK,EAAQ,gBAAE2B,EAAe,mBAAE+R,EAAkB,SAAErJ,GAAapK,KAAKC,OAClF,eAAEgS,EAAc,SAAEE,EAAQ,SAAEC,EAAQ,gBAAES,EAAe,QAAE/G,GAAY9L,KAAKkE,MACxEA,EAAQlE,KAAK4K,eACb,UAAEG,EAAS,cAAEF,EAAa,mBAAEC,EAAkB,eAAEG,GAAmB/G,EACnE8G,EAAuC,QAAvBhL,KAAKsT,eACrB,kBAAE1B,GAAsB5R,KAAKkE,MAC7BoD,EAAUtH,KAAKoN,aACfsG,EAAY1L,EAAiBkL,iBAAiB5L,GACpD,OACEzF,EAAAA,EAAAA,IAAA,OAAKtC,UAAU,yBAAwBW,SAAA,EACrCb,EAAAA,EAAAA,GAACsU,EAAAA,GACC,CACAC,QAAS5T,KAAKC,MAAMF,SAASI,OAC7B0T,iBAAkB7T,KAAKC,MAAMmJ,kBAAkBjJ,OAC/CsT,mBAAoBA,EACpB5I,cAAeA,EACfC,mBAAoBA,EACpB0D,kBAAmBxO,KAAKwO,kBACxB8C,0BAA2BtR,KAAKsR,0BAChCO,sBAAuB7R,KAAK6R,sBAC5B/D,0BAA2B9N,KAAK8N,0BAChCgE,uBAAwB9R,KAAK8R,uBAC7B4B,UAAWA,EACXzI,eAAgBA,EAChBD,cAAeA,EACfD,UAAWA,EACX8E,kBAAmB7P,KAAK6P,kBACxBiE,yBAA0B9T,KAAKC,MAAM8T,sBAEvC1U,EAAAA,EAAAA,GAAA,OAAKE,UAAU,oBAAmBW,UAChC2B,EAAAA,EAAAA,IAACmS,EAAAA,GAAmB,CAClB5H,WAAYwF,EAIZqC,2BAAyD,IAA7BrC,EAAkBzR,OAAaD,SAAA,CAE1DF,KAAK8S,yBAA2B,MAC/BzT,EAAAA,EAAAA,GAAC8F,EAAe,CACdZ,aAAc+F,EAAc,GAC5B/D,QAAS0L,EACTzL,EAAG2L,EACH3Q,EAAG4Q,EACH7M,SAAUsN,EACVzM,cAAepG,KAAK+R,uBACpBjM,YAAaA,IAAM9F,KAAK6I,SAAS,CAAEoJ,gBAAgB,IACnDxL,oBAAsBF,GAAYvG,KAAK6I,SAAS,CAAEoJ,eAAgB1L,OAGtElH,EAAAA,EAAAA,GAAC6U,EAAAA,EAAO,CAACzU,KAAK,QAAQ4D,KAAG8Q,EAAAA,EAAAA,IAAE,CAAEC,WAAYtI,EAAU,UAAY,UAAU,OACzEzM,EAAAA,EAAAA,GAACgV,EAAAA,GACC,CACAtU,SAAUA,EACV2B,gBAAiBA,EACjB4S,MAAOzJ,EACPvD,QAAStH,KAAKoN,aACd7M,WAAYuK,EACZC,UAAWA,EACX2I,UAAWA,EACXP,YAAanL,EAAiBmL,YAAY/I,EAASM,QACnDO,eAAgBA,EAChBsJ,YAAarQ,EAAMgH,OACnBC,iBAAmBjH,EAAciH,iBACjCqJ,eAAgBxU,KAAK+O,mBACrBlK,QAAS7E,KAAKkS,cACduC,cAAezU,KAAKkQ,kBACpBwE,oBAAqB1U,KAAKwQ,2BAE5BnR,EAAAA,EAAAA,GAACO,EAAmB,CAClBG,SAAUA,EACV2B,gBAAiBA,EACjBnB,WAAYuK,WAMxB,EA7pBW9C,EAGJ2M,aAAe,CACpBZ,oBAAoB,GA4pBxB,MA0CMa,EAAqB,CAAEpI,oBAAmB,KAAEW,UAAU,MAE5D,OAAe0H,EAAAA,EAAAA,IAAe7Q,EAAAA,EAAAA,KA5CNC,CAACC,EAAYC,KACnC,MAAM,SAAEpE,GAAaoE,EACfiF,EAAoBrJ,EAASiK,QAAQ1J,GAAuD,aAAtCgE,EAAAA,EAAAA,IAAWhE,EAAS4D,GAAO4Q,UACjF,uBAAEvL,EAAsB,iBAAEwL,GAAqB7Q,EAAM8Q,SAGrDzU,EAAawJ,IAAAA,QAAUhK,GAAWO,IACtC,MAAME,EAAgB+I,EAAuBjJ,GAC7C,OAAOE,EAAgBgH,OAAOC,KAAKjH,GAAiB,EAAE,IAElDiT,EAAqB,IAAI,IAAInG,IAAI/M,IAAaoN,OAC9CjM,EAAuB,GAE7B,IAAIqS,GAAqB,EAIzB,MAAMvI,EAA+BzB,IAAAA,QAAUhK,GAAWO,IACxD,MAAM2U,EAAiBrP,EAAAA,EAAMsP,mBAAkB5Q,EAAAA,EAAAA,IAAWhE,EAAS4D,GAAQ5D,GAC3EoB,EAAgB4K,KAAK2I,GACrB,MAAME,EAAiBJ,EAAiBzU,GACxC,OAAO6U,EACH3N,OAAOC,KAAK0N,GAAgBvT,KAAKR,IAC/B,MAAMsG,EAAUyN,EAAe/T,GAAWQ,KAAKwT,IAAeC,EAAAA,EAAAA,IAA6BD,KAI3F,OAHI1N,EAAQ4N,MAAKC,IAAA,IAAC,MAAE5R,GAAY4R,EAAA,MAAsB,kBAAV5R,IAAuB6R,MAAM7R,KAAW8R,SAAS9R,EAAM,MACjGoQ,GAAqB,GAEhB,CAAE3S,YAAWsG,UAASpH,UAAS2U,iBAAgB,IAExD,EAAE,IAGR,MAAO,CACLvT,kBACA6H,yBACAkK,qBACAjI,+BACApC,oBACA2K,qBACD,GAKoDa,EAAzB5Q,CAA6CgE,G,wIC3wB3E,MAyBM0N,EAAkBA,CAACC,EAA+BhS,EAAeiS,KACrEnO,EAAAA,EAAAA,MAAKkO,GAAOlK,QACV,CAACoK,EAAMC,IAAUvN,KAAKwN,IAAIC,OAAOF,GAAQnS,GAAS4E,KAAKwN,IAAIF,EAAOlS,GAASqS,OAAOF,GAAQE,OAAOH,IACjGD,GAIEK,EAAeA,CAACN,EAA+BO,EAAsBC,IAC3D,SAAdA,EACI5N,KAAKoB,OACAnC,OAAOC,KAAKkO,GACZ3L,QAAQoM,GAASJ,OAAOI,GAAQF,IAChCtU,IAAIoU,SAETzN,KAAK8N,OACA7O,OAAOC,KAAKkO,GACZ3L,QAAQoM,GAASJ,OAAOI,GAAQF,IAChCtU,IAAIoU,SACP,IAAAhK,EAAA,CAAApJ,KAAA,SAAA1D,OAAA,qBAED,MAAMoX,EAAmBvX,IAWF,IAXG,IAC/B4K,EAAM,EAAC,IACP0M,EAAM,EAAC,KACPxS,EAAI,MACJ8R,EAAK,MACLhS,EAAK,SACL4S,EAAQ,SACRC,EAAQ,cACRC,EAAa,YACbzR,EAAW,UACXzF,GACsBR,EACtB,MAAM,MAAE2X,IAAUC,EAAAA,EAAAA,KACZC,IAAkBC,EAAAA,EAAAA,SAAQlB,GAC1BmB,EAAqBF,GAAkBpP,OAAOC,KAAKkO,GAAOxV,OAlD7B,IAqD5B4W,EAAgBC,IAAqBC,EAAAA,EAAAA,eAA6BrT,GAEzE,OACE/B,EAAAA,EAAAA,IAAA,OACEwB,KAAG8Q,EAAAA,EAAAA,IAAE,CACH+C,QAAS,OACTC,OAAQT,EAAMU,QAAQC,SACtBC,IAAKZ,EAAMa,QAAQC,GACnBC,WAAY,UACb,IAACvX,SAAA,EAEF2B,EAAAA,EAAAA,IAAC6V,EAAAA,IAAOC,KAAI,CACVnB,SAAUA,EACVnT,KAAG8Q,EAAAA,EAAAA,IAAE,CACHyD,KAAM,EACN5Q,SAAU,WACV,kBAAmB,CAAE6Q,OAvEV,IAwEZ,IACDtY,UAAWA,EACX8W,IAAKA,EACL1M,IAAKA,EACLhG,MAAO,CAAM,OAALA,QAAK,IAALA,EAAAA,EAAS,GACjBmU,cAAejV,IAAA,IAAEkV,GAASlV,EAAA,OAAkB,OAAb4T,QAAa,IAAbA,OAAa,EAAbA,EAAgBsB,EAAS,EACxDC,UAAYvJ,IAEV,GAAImI,IACFnI,EAAEwJ,iBAEE,CAAC,YAAa,aAAc,UAAW,aAAavG,SAASjD,EAAE/L,MAAM,CACvE,MAAMwV,EAAYjC,EAChBN,EACK,OAALhS,QAAK,IAALA,EAAAA,EAAS,EACC,cAAV8K,EAAE/L,KAAiC,cAAV+L,EAAE/L,IAAsB,OAAS,MAG/C,OAAb+T,QAAa,IAAbA,GAAAA,EAAgByB,GAChB3B,EAAS2B,EACX,CACF,EAEFC,cAAerV,IAAiB,IAAfiV,GAASjV,EAEtByT,EADEK,EACOlB,EAAgBC,EAAOoC,EAAe,OAALpU,QAAK,IAALA,EAAAA,EAAS,GAG5CoU,EAAS,EAEpBlU,KAAU,OAAJA,QAAI,IAAJA,EAAAA,OAAQD,EAAU1D,SAAA,CAGvB4W,IACCzX,EAAAA,EAAAA,GAAA,OAAKgE,KAAG8Q,EAAAA,EAAAA,IAAE,CAAEnN,SAAU,WAAYoR,MAAO,EAAGzS,YAhHnC,IAgH4D,IAACzF,UACnEuH,EAAAA,EAAAA,MAAKkO,GAAO/T,KAAKyW,IAChBhZ,EAAAA,EAAAA,GAAA,OAEEgE,KAAG8Q,EAAAA,EAAAA,IAAE,CACHnN,SAAU,WACV6Q,OAjHE,EAkHF9Q,IAAK,EACLuR,MAAO,EACPC,OAAQ,EACRvS,YAAY,IACZwS,UAxHI,EAyHJC,cAAe,OACfC,aAAc,OACdC,gBAAiBjC,EAAMkC,OAAOC,+BAC9B1B,OA9HA,EA+HA9V,MA/HA,EAgIAyX,QAAS,IACV,IACDnZ,MAAO,CACLmH,KAAUkP,OAAOqC,IAAiB1O,EAAM0M,GAAQ,IAA1C,MAjBHgC,QAuBbhZ,EAAAA,EAAAA,GAACqY,EAAAA,IAAOqB,MAAK,CAACxZ,UAAU,QAAOW,UAC7Bb,EAAAA,EAAAA,GAACqY,EAAAA,IAAOsB,MAAK,OAEf3Z,EAAAA,EAAAA,GAACqY,EAAAA,IAAOuB,MAAK,CAAC5V,KAAG8Q,EAAAA,EAAAA,IAAE,CAAEnN,SAAU,WAAYmQ,OA7IhC,GA6IoD9V,MA7IpD,IA6IuE,UAEpFhC,EAAAA,EAAAA,GAAC6Z,EAAAA,EAAK,CACJlU,YAAwB,OAAXA,QAAW,IAAXA,EAAAA,EAAe,uDAC5BC,KAAK,SACLuR,SAAUA,EACVH,IAAKA,EACL1M,IAAKA,EACLtG,IAAG2I,EACHnI,KAAMA,EACNF,MAAqB,OAAdoT,QAAc,IAAdA,EAAAA,EAAkBpT,EACzBoF,OAAQA,MAEDoQ,EAAAA,EAAAA,aAAYpC,KACXH,GACW,OAAbH,QAAa,IAAbA,GAAAA,EAAgBf,EAAgBC,EAAOoB,EAAqB,OAALpT,QAAK,IAALA,EAAAA,EAAS,IAChE4S,EAASb,EAAgBC,EAAOoB,EAAqB,OAALpT,QAAK,IAALA,EAAAA,EAAS,MAE5C,OAAb8S,QAAa,IAAbA,GAAAA,GAAgB2C,EAAAA,EAAAA,OAAMrC,EAAgBV,EAAK1M,IAC3C4M,GAAS6C,EAAAA,EAAAA,OAAMrC,EAAgBV,EAAK1M,KAEtCqN,OAAkBpT,GACpB,EAEF2S,SAAUpK,IAA8B,IAA7B,OAAE0C,EAAM,YAAEwK,GAAalN,EAGhC,GAAIkN,aAAuBC,WACzBtC,EAAkBhB,OAAOnH,EAAOlL,aAKlC,GAAIiT,EAAJ,CACE,MAAMsB,EAAYjC,EAAaN,EAAY,OAALhS,QAAK,IAALA,EAAAA,EAAS,EAAGqS,OAAOnH,EAAOlL,OAASqS,OAAOrS,GAAS,OAAS,MAElG4S,EAAS2B,EAEX,MAGA3B,GAAS6C,EAAAA,EAAAA,OAAMpD,OAAOnH,EAAOlL,OAAQ0S,EAAK1M,GAAK,MAG/C,C,wGCpLV,MAAM4P,EAAO1Z,EAAAA,MAAW,IAAM,uCAEjB2Z,EAAWza,IAAA,IAAC,SAAE0a,KAAaxZ,GAAYlB,EAAA,OAClDM,EAAAA,EAAAA,GAACqa,EAAAA,EAAoB,CAAAxZ,UACnBb,EAAAA,EAAAA,GAACQ,EAAAA,SAAc,CAAC4Z,SAAkB,OAARA,QAAQ,IAARA,EAAAA,GAAYpa,EAAAA,EAAAA,GAACsa,EAAAA,IAAc,CAACC,QAAM,IAAI1Z,UAC9Db,EAAAA,EAAAA,GAACka,EAAI,IAAKtZ,OAES,C,+NCDzB,MAGa4Z,EAAMA,CAACC,EAAaC,KAI/B,GAAIA,GAAmB,IAAMD,GAAUA,EAAO3Z,QAN1B,GAMqD2Z,EAAO/M,OAAOiN,GAAWA,IAAMF,EAAO,KAC7G,OAAOA,EAGT,MAAMG,EAAaF,GAAmBG,EAAAA,GAAsB,GACtDC,EAAgB,GACtB,IAAIC,EAAgB,EACpB,IAAK,IAAIC,EAAI,EAAGA,EAAIP,EAAO3Z,OAAQka,IACjC,GAAK7E,MAAMsE,EAAOO,IAShBF,EAAc7N,KAAKwN,EAAOO,QATL,CACrBD,EAAgBA,EAAgBH,GAAc,EAAIA,GAAcH,EAAOO,GAIvE,MACMC,EAAkBF,GADH,EAAM7R,KAAKgG,IAAI0L,EAAYI,EAAI,IAEpDF,EAAc7N,KAAKgO,EACrB,CAIF,OAAOH,CAAa,EAKhBI,EAAY,CAChBlZ,MAAO,MACP8V,OAAQ,IAERqD,KAAM,uZACNC,UAAW,0BACX,IAAAC,GAAAC,EAAAA,EAAAA,GAAA,kBAqBK,MAAMC,UAA4B/a,EAAAA,UAA0CuF,WAAAA,GAAA,SAAAC,WA0BjF,KAIAwV,8BAAgC,KAAO,IAADC,EACpC,MAAM,QAAExT,EAAO,MAAEgN,EAAK,YAAEC,GAAgBvU,KAAKC,MACvC8a,EAA0C,SAAlB,OAAXxG,QAAW,IAAXA,GAAkB,QAAPuG,EAAXvG,EAAatG,aAAK,IAAA6M,OAAP,EAAXA,EAAoB7V,MACjC+V,EAAiB,CAAC,EAExB1T,EAAQlD,SAASV,IACf,MAAM,UAAEtC,EAAS,QAAEsG,GAAYhE,EAE/BsX,EAAe5Z,IAAa6Z,EAAAA,EAAAA,IAA4B,CACtDC,QAAU7G,EAAwB8G,uBAAuBzT,EAAS4M,EAAOtU,KAAKC,MAAMU,MACpFya,QAAS1T,EAAQ9F,KAAKwT,GAAuC,kBAAhBA,EAAMzR,MAAqByR,EAAMzR,MAAQqS,OAAOZ,EAAMzR,SACnG0X,WAAYN,EACZO,gBAAkB3X,GAAU3D,KAAKC,MAAMU,KAAKE,cAAc8C,EAAO,CAAEvC,eACnE,KAGJma,EAAAA,EAAAA,GAAAvb,KAAI0a,GAAAA,GAAmBM,CAAc,EACtCxT,OAAAgU,eAAA,KAAAd,EAAA,CAAAe,UAAA,EAAA9X,MAEiB,CAAC,IAAC,KAEpB+X,yBAA2B,KACzB,MAAM,QAAEpU,EAAO,MAAEgN,EAAK,UAAEvJ,EAAS,eAAEE,EAAc,YAAEkI,EAAW,iBAAEhI,GAAqBnL,KAAKC,MAEpF0b,EAAsB,IAAIrO,IAAInC,GAC9ByQ,EAAc,GACdC,EAAmB,GAuCnB5b,EAAQ,CAAEoQ,KArCH/I,EAAQ1F,KAAK8B,IACxB,MAAM,UAAEtC,EAAS,eAAE6T,EAAc,QAAEvN,EAAO,QAAEpH,GAAYoD,EAClDoY,EAAgBpU,EAAQ9F,KAAKwT,GACV,kBAAhBA,EAAMzR,MAAqByR,EAAMzR,MAAQqS,OAAOZ,EAAMzR,SAOzDoY,EAAkBD,EAAc9R,QAAQrG,IAAgB6R,MAAM7R,KAAQxD,QAAU,EAEhFoG,GAAWoV,EAAoBnO,IAAI5H,EAAAA,EAAM8K,YAAYpQ,EAASc,KAAqB,aASzF,OAPIma,EAAAA,EAAAA,GAAAvb,KAAI0a,GAAAA,IAAoBtZ,KAASma,EAAAA,EAAAA,GAAIvb,KAAI0a,GAAAA,KAAgC,IAAZnU,IAE/DqV,EAAOtP,SAAQiP,EAAAA,EAAAA,GAAAvb,KAAI0a,GAAAA,GAAiBtZ,GAAWwa,QAE/CC,EAAYvP,SAAQiP,EAAAA,EAAAA,GAAAvb,KAAI0a,GAAAA,GAAiBtZ,GAAWya,cAG/C,CACLjZ,KAAOyR,EAAwB2H,cAAc5a,EAAW6T,EAAgB9B,GACxE3M,EAAI6N,EAAwB8G,uBAAuBzT,EAAS4M,GAC5D9S,GAAIua,EAAkBD,EAAgBjC,EAAIiC,EAAe7Q,IAAiBrJ,KAAKwT,GAC5EK,SAASL,GAAeA,EAAN6G,MAErBC,KAAMJ,EAAcla,KAAK+B,GAAgB6R,MAAM7R,GAASA,EAAQA,EAAMwY,QAAQ,KAC9ElX,KAAM,YACNmX,KAAML,EAAkB,UAAY,gBACpCnJ,OAAQ,CAAEkG,QAASiD,GAAmBhR,EAAY,EAAI,GACtDsR,cAAeN,GAAsC,IAAnB9Q,EAAuB,OAAS,mCAClE1E,QAASA,EACTd,MAAOnF,EACPkT,WAAYpS,EACb,KAWH,OAPCnB,EAAciL,OAAS,IAClBjL,EAAciL,UACflL,KAAKC,MAAMsU,YACdqH,SACAC,eAGK5b,CAAK,EACZ,KAEFqc,wBAA0B,KAExB,MAAM,SAAEvc,EAAQ,gBAAE2B,EAAe,iBAAEyJ,GAAqBnL,KAAKC,MAGvDsc,EAAqBvc,KAAKC,MAAMqH,QAAQmE,QAAO,CAAC7J,EAAK8B,KACzD,MAAM,QAAEpD,EAAO,UAAEc,EAAS,QAAEsG,GAAYhE,EAClCC,EAAQ+D,EAAQ,IAAMA,EAAQ,GAAG/D,MAMvC,OALK/B,EAAIR,GAGPQ,EAAIR,GAAWd,GAAWqD,EAF1B/B,EAAIR,GAAa,CAAEA,YAAW,CAACd,GAAUqD,GAIpC/B,CAAG,GACT,CAAC,GAEE4a,EAAkCzS,IAAAA,OAASvC,OAAOvI,OAAOsd,GAAqB,aAE9EE,EAAmBD,EAAgC5a,KAAK8F,GAAaA,EAAgBtG,YACrFua,EAAsB,IAAIrO,IAAInC,GAc9BlL,EAAQ,CAAEoQ,KAbHtQ,EAAS6B,KAAI,CAACtB,EAAS+Z,KAClC,MAAMjG,EAAauH,EAAoBnO,IAAIlN,GAAW,CAAEiG,QAAS,cAAiB,CAAC,EACnF,MAAO,CACL3D,KAAMgD,EAAAA,EAAM8W,eAAehb,EAAgB2Y,GA7Lf,IA8L5B7T,EAAGiW,EACHjb,EAAGgb,EAAgC5a,KAAK8F,GAAaA,EAAgBpH,KACrE2E,KAAM,MACNQ,MAAOnF,KACJ8T,EACJ,IAImBlJ,OADP,CAAEyR,QAAS,UAM1B,OAJA1c,EAAMiL,OAAS,IACVjL,EAAMiL,UACNlL,KAAKC,MAAMsU,aAETtU,CAAK,CACZ,CAzIF,6BAAOkb,CAAuBzT,EAAckV,EAAgBjc,GAC1D,GAAuB,IAAnB+G,EAAQvH,OACV,MAAO,GAET,OAAQyc,GACN,KAAKlP,EAAAA,GACH,OAAOhG,EAAQ9F,KAAI7C,IAAA,IAAC,KAAE8E,GAAW9E,EAAA,OAAK8E,CAAI,IAC5C,KAAK+K,EAAAA,GAAiB,CAEpB,MAAQnF,UAAWoT,GAAiB9S,IAAAA,MAAQrC,EAAS,aACrD,OAAOA,EAAQ9F,KAAIiB,IAAA,IAAC,UAAE4G,GAAgB5G,EAAA,OAAM4G,EAAYoT,GAAgB,GAAI,GAC9E,CACA,QACE,OAAOnV,EAAQ9F,KAAIkB,IAAA,IAAC,UAAE2G,GAAgB3G,EAAA,OAAK2G,CAAS,IAE1D,CA4HAxD,iBAAAA,GACEjG,KAAK6a,+BACP,CAEAiC,kBAAAA,GAKE9c,KAAK6a,+BACP,CAEA/a,MAAAA,GACE,MAAM,eAAE0U,EAAc,QAAE3P,EAAO,cAAE4P,EAAa,oBAAEC,GAAwB1U,KAAKC,MACvE8c,EACJ/c,KAAKC,MAAMyT,YAAcxM,EAAAA,GAAiBlH,KAAKsc,0BAA4Btc,KAAK0b,2BAElF,OACErc,EAAAA,EAAAA,GAAA,OAAKE,UAAU,8BAA6BW,UAC1Cb,EAAAA,EAAAA,GAACma,EAAAA,EAAQ,IACHuD,EACJC,kBAAgB,EAChBC,WAAYzI,EACZ3P,QAASA,EACT4P,cAAeA,EACfC,oBAAqBA,EACrB/U,MAAO,CAAE0B,MAAO,OAAQ8V,OAAQ,QAChCjM,OAAQnB,IAAAA,UAAagT,EAAkB7R,QACvCgS,OAAQ,CACNC,aAAa,EACbC,YAAY,EACZC,uBAAwB,CAAC,mBACzBC,oBAAqB,CACnB,CACE1a,KAAM,4BACNgC,KAAM2V,EACNgD,MAAOA,KACL,MAAMzN,GAAMzI,EAAAA,EAAAA,IAAoBrH,KAAKC,MAAMqH,SACrCyI,EAAO,IAAIC,KAAK,CAACF,GAAM,CAAE7K,KAAM,mCACrCgL,EAAAA,EAAAA,QAAOF,EAAM,cAAc,QAQ3C,EAnMW6K,EACJoB,cAAgB,CAAC5a,EAAgB6T,EAAqB9B,KAC3D,IAAIqK,EAASpc,EAIb,OAHI+R,IACFqK,GAAU,KAAK5X,EAAAA,EAAM8W,eAAezH,EA/DN,OAiEzBuI,CAAM,EAiMV,MAAMnJ,GAAkBtQ,EAAAA,EAAAA,IAAW6W,E,yMCvQnC,MAAM6C,EAAYxd,IAErB4B,EAAAA,EAAAA,IAAA,OAAKwB,IAAKnE,EAAOwe,QAASne,UAAWU,EAAMV,UAAUW,SAAA,EACnDb,EAAAA,EAAAA,GAAA,OAAKgE,IAAKnE,EAAOye,MAAMzd,UACrBb,EAAAA,EAAAA,GAAA,OAAKgE,IAAKnE,EAAO0e,cAAeje,MAAO,CAAE0B,MAAO,GAAGpB,EAAM4d,gBAE1D5d,EAAM6d,OAAO7d,EAAM4d,YAKpB3e,EAAS,CACbwe,QAAUhH,IAAY,CAAQQ,QAAS,OAAQO,WAAY,SAAUH,IAAKZ,EAAMa,QAAQwG,KACxFJ,MAAQjH,IAAY,CAClBiC,gBAAiBjC,EAAMkC,OAAOoF,oBAC9B7G,OAAQT,EAAMa,QAAQwG,GACtBnG,KAAM,EACNc,aAAchC,EAAMa,QAAQwG,KAE9BH,cAAgBlH,IAAY,CAC1BiC,gBAAiBjC,EAAMkC,OAAOqF,QAC9B9G,OAAQT,EAAMa,QAAQwG,GACtBrF,aAAchC,EAAMa,QAAQwG,M,+CClBhC,MAAMG,EAAaC,EAAAA,IAAMC,MACZzP,EAAc,OACdjB,EAAc,OACdkB,EAAkB,WAClBsL,EAAsB,IAAI,IAAAnb,EAAA,CAAA6D,KAAA,UAAA1D,OAAA,2CAwBvC,MAAMmf,UAAgCxe,EAAAA,UAAuBuF,WAAAA,GAAA,SAAAC,WAAA,KAK3DiZ,gCAAkC,CAACpC,EAAWqC,IAC5CA,EAAOte,MAAMW,MAAM4d,cAAc9M,SAASwK,EAAKsC,eAAe,KAEhEC,iBAAmB,KACjB,MAAM,mBAAEhL,GAAuBzT,KAAKC,MACpC,OAAOwT,EAAmB7R,KAAKR,IAAS,CACtCR,MAAOQ,EACPuC,MAAOvC,EACPsB,IAAKtB,KACJ,CACH,CAEFtB,MAAAA,GACE,MAAM,UAAE4T,EAAS,cAAE1I,EAAa,eAAEC,EAAc,UAAEF,EAAS,QAAE6I,EAAO,iBAAEC,EAAgB,yBAAEC,GACtF9T,KAAKC,MAEDye,GACJrf,EAAAA,EAAAA,GAACsf,EAAAA,EACC,CAAA7d,GAAA,SACAC,eAAe,8KAIb6d,GACJvf,EAAAA,EAAAA,GAACsf,EAAAA,EACC,CAAA7d,GAAA,SACAC,eAAe,mIAEf9B,OAAQ,CAAE4f,SAAUtW,KAAKuW,MAAM3X,EAAAA,GAAmC,QAGtE,OACEtF,EAAAA,EAAAA,IAAA,OACEtC,UAAU,gBACV8D,IAAG,CAAGnE,EAAO6f,gBAAiBrL,IAAczM,EAAAA,IAAmB/H,EAAO8f,wBAAuB,IAAE9e,SAAA,CAE9FwT,IAAczM,EAAAA,IACbpF,EAAAA,EAAAA,IAAA,OAAA3B,SAAA,EACEb,EAAAA,EAAAA,GAAA,OAAKE,UAAU,iBAAgBW,UAC7B2B,EAAAA,EAAAA,IAAA,OAAKtC,UAAU,gBAAeW,SAAA,EAC5Bb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,mBAEd,KACH1B,EAAAA,EAAAA,GAAC4f,EAAAA,IAAa,CAACre,MAAOge,EAAyB1e,UAC7Cb,EAAAA,EAAAA,GAAC6f,EAAAA,IAAgB,OAEnB7f,EAAAA,EAAAA,GAACoe,EAAQ,CACPI,QAAStV,KAAKuW,MAAO,IAAMjL,EAAoBD,GAC/CkK,OAAQA,IAAM,GAAGjK,KAAoBD,YAI3C/R,EAAAA,EAAAA,IAAA,OAAKtC,UAAU,iBAAgBW,SAAA,EAC7Bb,EAAAA,EAAAA,GAAA,OAAKE,UAAU,gBAAeW,UAC5Bb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,eAKnB1B,EAAAA,EAAAA,GAAC8f,EAAAA,IAAM,CACLna,YAAY,oFACZ,cAAY,oBACZoa,eAAgBrU,EAChBwL,SAAUvW,KAAKC,MAAM4R,4BAGvBiC,IACAjS,EAAAA,EAAAA,IAAA,OAAKtC,UAAU,gBAAeW,SAAA,EAC5B2B,EAAAA,EAAAA,IAAA,OAAKtC,UAAU,gBAAeW,SAAA,EAC5Bb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,oBAEd,KACH1B,EAAAA,EAAAA,GAAC4f,EAAAA,IAAa,CAACre,MAAO8d,EAA0Bxe,UAC9Cb,EAAAA,EAAAA,GAAC6f,EAAAA,IAAgB,UAGrB7f,EAAAA,EAAAA,GAACiX,EAAAA,EAAgB,CACf,cAAY,oBACZD,IAAK,EACL1M,IAAKuQ,EACL3D,SAAUvW,KAAKC,MAAM6R,uBACrBnO,MAAOsH,QAIbpJ,EAAAA,EAAAA,IAAA,OAAKtC,UAAU,gBAAeW,SAAA,EAC5Bb,EAAAA,EAAAA,GAAA,OAAKE,UAAU,gBAAeW,UAC5Bb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,eAKnBc,EAAAA,EAAAA,IAACqc,EAAU,CACTlZ,YAAY,oFACZpC,KAAK,kCACLS,IAAKnE,EAAOmgB,cACZ9I,SAAUvW,KAAKC,MAAMuO,kBACrB7K,MAAO3D,KAAKC,MAAM4K,cAAc3K,SAAA,EAEhCb,EAAAA,EAAAA,GAAC8e,EAAAA,IAAK,CAACxa,MAAO+J,EAAa,cAAY,eAAcxN,UACnDb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,YAKnB1B,EAAAA,EAAAA,GAAC8e,EAAAA,IAAK,CAACxa,MAAOgL,EAAa,cAAY,eAAczO,UACnDb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,mBAKnB1B,EAAAA,EAAAA,GAAC8e,EAAAA,IAAK,CAACxa,MAAOiL,EAAiB,cAAY,eAAc1O,UACvDb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,+BAQvB,MACJc,EAAAA,EAAAA,IAAA,OAAKtC,UAAU,gBAAeW,SAAA,EAC5Bb,EAAAA,EAAAA,GAAA,OAAKE,UAAU,gBAAeW,UAC5Bb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,eAKnB1B,EAAAA,EAAAA,GAACigB,EAAAA,IAAY,CACXC,YAAavf,KAAKC,MAAMU,KAAKE,cAAc,CAAAC,GAAA,SACzCC,eAAe,yBAKjB4C,MAAO3D,KAAKC,MAAM6K,mBAClByL,SAAUvW,KAAKC,MAAMqR,0BACrB8K,KAAK,WACL/Y,IAAKnE,EAAOsgB,aAAatf,SAExBF,KAAKye,mBAAmB7c,KAAKc,IAC5BrD,EAAAA,EAAAA,GAACigB,EAAAA,IAAaG,OAAM,CAAC9b,MAAOjB,EAAIiB,MAAMzD,SACnCwC,EAAI9B,OADqC8B,EAAIA,aAMtDb,EAAAA,EAAAA,IAAA,OAAKtC,UAAU,iBAAgBW,SAAA,EAC7Bb,EAAAA,EAAAA,GAAA,OAAKE,UAAU,gBAAeW,UAC5Bb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,yBAKnB1B,EAAAA,EAAAA,GAAC8f,EAAAA,IAAM,CACLna,YAAY,oFACZoa,eAAgBpU,EAChBuL,SAAUvW,KAAKC,MAAM6N,gCAGzBzO,EAAAA,EAAAA,GAAA,OAAKE,UAAU,iBAAgBW,UAC7B2B,EAAAA,EAAAA,IAACkD,EAAAA,EAAM,CACLC,YAAY,oFACZ3B,IAAGtE,EAIH8F,QAAS7E,KAAKC,MAAM4P,kBAAkB3P,SAAA,EAEtCb,EAAAA,EAAAA,GAACsf,EAAAA,EAAgB,CAAA7d,GAAA,SACfC,eAAe,mBAIjB1B,EAAAA,EAAAA,GAAA,KAAGE,UAAU,2BAKvB,EAjMI8e,EACG1J,aAAe,CACpBb,0BAA0B,GAkM9B,MAAM5U,EAAS,CACbmgB,cAAgB3I,IAAU,CACxBgJ,MAAO,CAAElH,UAAW9B,EAAMa,QAAQoI,GAAIC,aAAclJ,EAAMa,QAAQoI,MAEpEZ,gBAAiB,CAAEc,SAAU,MAAOC,SAAU,OAC9CN,aAAc,CAAEne,MAAO,QACvB2d,wBAAyB,CAEvBe,eAAgB,WAKPpM,GAAsB5P,EAAAA,EAAAA,IAAWsa,E", "sources": ["experiment-tracking/components/HtmlTableView.tsx", "experiment-tracking/components/MetricsSummaryTable.tsx", "common/components/IconButton.tsx", "experiment-tracking/components/RunLinksPopover.tsx", "experiment-tracking/components/MetricsPlotPanel.tsx", "experiment-tracking/components/LineSmoothSlider.tsx", "experiment-tracking/components/LazyPlot.tsx", "experiment-tracking/components/MetricsPlotView.tsx", "common/components/Progress.tsx", "experiment-tracking/components/MetricsPlotControls.tsx"], "sourcesContent": ["/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport { LegacyTable } from '@databricks/design-system';\nimport './HtmlTableView.css';\n\ntype Props = {\n  columns: any[];\n  values: any[];\n  styles?: any;\n  testId?: string;\n  scroll?: any;\n};\n\nexport function HtmlTableView({ columns, values, styles = {}, testId, scroll }: Props) {\n  return (\n    <LegacyTable\n      className=\"html-table-view\"\n      data-test-id={testId}\n      dataSource={values}\n      columns={columns}\n      scroll={scroll}\n      size=\"middle\"\n      pagination={false}\n      style={styles}\n    />\n  );\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Fragment } from 'react';\nimport { connect } from 'react-redux';\nimport { injectIntl } from 'react-intl';\nimport { HtmlTableView } from './HtmlTableView';\nimport { getRunInfo } from '../reducers/Reducers';\nimport { getLatestMetrics, getMinMetrics, getMaxMetrics } from '../reducers/MetricReducer';\nimport Utils from '../../common/utils/Utils';\nimport { Link } from '../../common/utils/RoutingUtils';\nimport Routes from '../routes';\nimport type { RunInfoEntity } from '../types';\n\nconst maxTableHeight = 300;\n// Because we make the table body scrollable, column widths must be fixed\n// so that the header widths match the table body column widths.\nconst headerColWidth = 350;\nconst dataColWidth = 200;\n\ntype MetricsSummaryTableProps = {\n  runUuids: string[];\n  runExperimentIds: any;\n  runDisplayNames: string[];\n  metricKeys: string[];\n  latestMetrics: any;\n  minMetrics: any;\n  maxMetrics: any;\n  intl: {\n    formatMessage: (...args: any[]) => any;\n  };\n};\n\nclass MetricsSummaryTable extends React.Component<MetricsSummaryTableProps> {\n  render() {\n    const { runUuids } = this.props;\n    return (\n      <div className=\"metrics-summary\">\n        {runUuids.length > 1 ? this.renderMetricTables() : this.renderRunTable(runUuids[0])}\n      </div>\n    );\n  }\n\n  renderRunTable(runUuid: any) {\n    const { metricKeys, latestMetrics, minMetrics, maxMetrics, intl } = this.props;\n    const columns = [\n      {\n        title: intl.formatMessage({\n          defaultMessage: 'Metric',\n          description:\n            // eslint-disable-next-line max-len\n            'Column title for the column displaying the metric names for a run',\n        }),\n        dataIndex: 'metricKey',\n        sorter: (a: any, b: any) => (a.metricKey < b.metricKey ? -1 : a.metricKey > b.metricKey ? 1 : 0),\n        width: headerColWidth,\n      },\n      ...this.dataColumns(),\n    ];\n    return metricKeys.length === 0 ? null : (\n      <HtmlTableView\n        columns={columns}\n        values={getRunValuesByMetric(runUuid, metricKeys, latestMetrics, minMetrics, maxMetrics, intl)}\n        scroll={{ y: maxTableHeight }}\n      />\n    );\n  }\n\n  renderMetricTables() {\n    const { runExperimentIds, runUuids, runDisplayNames, metricKeys, latestMetrics, minMetrics, maxMetrics, intl } =\n      this.props;\n    const columns = [\n      {\n        title: intl.formatMessage({\n          defaultMessage: 'Run',\n          description:\n            // eslint-disable-next-line max-len\n            'Column title for the column displaying the run names for a metric',\n        }),\n        dataIndex: 'runLink',\n        sorter: (a: any, b: any) => (a.runName < b.runName ? -1 : a.runName > b.runName ? 1 : 0),\n        width: headerColWidth,\n      },\n      ...this.dataColumns(),\n    ];\n    return metricKeys.map((metricKey) => {\n      return (\n        <Fragment key={metricKey}>\n          <h1>{metricKey}</h1>\n          <HtmlTableView\n            columns={columns}\n            values={getMetricValuesByRun(\n              metricKey,\n              runExperimentIds,\n              runUuids,\n              runDisplayNames,\n              latestMetrics,\n              minMetrics,\n              maxMetrics,\n              intl,\n            )}\n            scroll={{ y: maxTableHeight }}\n          />\n        </Fragment>\n      );\n    });\n  }\n\n  dataColumns() {\n    return [\n      {\n        title: this.props.intl.formatMessage({\n          defaultMessage: 'Latest',\n          description: 'Column title for the column displaying the latest metric values for a metric',\n        }),\n        dataIndex: 'latestFormatted',\n        sorter: (a: any, b: any) => a.latestValue - b.latestValue,\n        width: dataColWidth,\n        ellipsis: true,\n      },\n      {\n        title: this.props.intl.formatMessage({\n          defaultMessage: 'Min',\n          description: 'Column title for the column displaying the minimum metric values for a metric',\n        }),\n        dataIndex: 'minFormatted',\n        sorter: (a: any, b: any) => a.minValue - b.minValue,\n        width: dataColWidth,\n        ellipsis: true,\n      },\n      {\n        title: this.props.intl.formatMessage({\n          defaultMessage: 'Max',\n          description: 'Column title for the column displaying the maximum metric values for a metric',\n        }),\n        dataIndex: 'maxFormatted',\n        sorter: (a: any, b: any) => a.maxValue - b.maxValue,\n        width: dataColWidth,\n        ellipsis: true,\n      },\n    ];\n  }\n}\n\nconst getMetricValuesByRun = (\n  metricKey: any,\n  runExperimentIds: any,\n  runUuids: any,\n  runDisplayNames: any,\n  latestMetrics: any,\n  minMetrics: any,\n  maxMetrics: any,\n  intl: any,\n) => {\n  return runUuids.map((runUuid: any, runIdx: any) => {\n    const runName = runDisplayNames[runIdx];\n    return {\n      runName: runName,\n      runLink: <Link to={Routes.getRunPageRoute(runExperimentIds[runUuid] || '', runUuid)}>{runName}</Link>,\n      key: runUuid,\n      ...rowData(runUuid, metricKey, latestMetrics, minMetrics, maxMetrics, intl),\n    };\n  });\n};\n\nconst getRunValuesByMetric = (\n  runUuid: any,\n  metricKeys: any,\n  latestMetrics: any,\n  minMetrics: any,\n  maxMetrics: any,\n  intl: any,\n) => {\n  return metricKeys.map((metricKey: any) => {\n    return {\n      metricKey,\n      key: metricKey,\n      ...rowData(runUuid, metricKey, latestMetrics, minMetrics, maxMetrics, intl),\n    };\n  });\n};\n\nconst rowData = (runUuid: any, metricKey: any, latestMetrics: any, minMetrics: any, maxMetrics: any, intl: any) => {\n  const latestMetric = getMetric(latestMetrics, runUuid, metricKey);\n  const minMetric = getMetric(minMetrics, runUuid, metricKey);\n  const maxMetric = getMetric(maxMetrics, runUuid, metricKey);\n  const latestValue = getValue(latestMetric);\n  const minValue = getValue(minMetric);\n  const maxValue = getValue(maxMetric);\n  return {\n    latestFormatted: (\n      <span title={latestValue} css={{ marginRight: 10 }}>\n        {formatMetric(latestMetric, intl)}\n      </span>\n    ),\n    minFormatted: (\n      <span title={minValue} css={{ marginRight: 10 }}>\n        {formatMetric(minMetric, intl)}\n      </span>\n    ),\n    maxFormatted: (\n      <span title={maxValue} css={{ marginRight: 10 }}>\n        {formatMetric(maxMetric, intl)}\n      </span>\n    ),\n    latestValue,\n    minValue,\n    maxValue,\n  };\n};\n\nconst getMetric = (valuesMap: any, runUuid: any, metricKey: any) => valuesMap[runUuid] && valuesMap[runUuid][metricKey];\n\nconst getValue = (metric: any) => metric && metric.value;\n\nconst formatMetric = (metric: any, intl: any) =>\n  metric === undefined\n    ? ''\n    : intl.formatMessage(\n        {\n          defaultMessage: '{value} (step={step})',\n          description: 'Formats a metric value along with the step number it corresponds to',\n        },\n        {\n          value: metric.value,\n          step: metric.step,\n        },\n      );\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const { runUuids } = ownProps;\n  const runExperimentIds = {};\n  const latestMetrics = {};\n  const minMetrics = {};\n  const maxMetrics = {};\n  runUuids.forEach((runUuid: any) => {\n    const runInfo = getRunInfo(runUuid, state) as RunInfoEntity;\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    runExperimentIds[runUuid] = runInfo && runInfo.experimentId;\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    latestMetrics[runUuid] = getLatestMetrics(runUuid, state);\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    minMetrics[runUuid] = getMinMetrics(runUuid, state);\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    maxMetrics[runUuid] = getMaxMetrics(runUuid, state);\n  });\n  return { runExperimentIds, latestMetrics, minMetrics, maxMetrics };\n};\n\n// @ts-expect-error TS(2769): No overload matches this call.\nconst MetricsSummaryTableWithIntl = injectIntl(MetricsSummaryTable);\n\nexport default connect(mapStateToProps)(MetricsSummaryTableWithIntl);\n", "import React from 'react';\nimport { Button } from '@databricks/design-system';\n\ntype Props = {\n  icon: React.ReactNode;\n  style?: React.CSSProperties;\n  className?: string;\n  onClick?: () => void;\n  restProps?: unknown;\n};\n\nexport const IconButton = ({ icon, className, style, onClick, ...restProps }: Props) => {\n  return (\n    <Button\n      componentId=\"codegen_mlflow_app_src_common_components_iconbutton.tsx_20\"\n      type=\"link\"\n      className={className}\n      style={{ padding: 0, ...style }}\n      onClick={onClick}\n      {...restProps}\n    >\n      {icon}\n    </Button>\n  );\n};\n", "import React from 'react';\nimport { Link } from '../../common/utils/RoutingUtils';\nimport Routes from '../routes';\nimport { IconButton } from '../../common/components/IconButton';\nimport Utils from '../../common/utils/Utils';\nimport { LegacyPopover } from '@databricks/design-system';\nimport { type RunItem } from '../types';\n\ntype Props = {\n  experimentId: string;\n  visible: boolean;\n  x: number;\n  y: number;\n  runItems: RunItem[];\n  handleClose: () => void;\n  handleKeyDown: ({ key }: { key: string }) => void;\n  handleVisibleChange: (visible: boolean) => void;\n};\n\nexport class RunLinksPopover extends React.Component<Props> {\n  componentDidMount() {\n    document.addEventListener('keydown', this.props.handleKeyDown);\n  }\n\n  componentWillUnmount() {\n    document.removeEventListener('keydown', this.props.handleKeyDown);\n  }\n\n  renderContent = () => {\n    const { experimentId, runItems } = this.props;\n    return (\n      <div>\n        {runItems.map(({ name, runId, color, y }, index) => {\n          const key = `${runId}-${index}`;\n          const to = Routes.getRunPageRoute(experimentId, runId);\n          return (\n            <Link key={key} to={to}>\n              <p style={{ color }}>\n                <i className=\"fas fa-external-link-o\" style={{ marginRight: 5 }} />\n                {`${name}, ${Utils.formatMetric(y)}`}\n              </p>\n            </Link>\n          );\n        })}\n      </div>\n    );\n  };\n\n  renderTitle = () => {\n    const { handleClose } = this.props;\n    return (\n      <div>\n        <span>Jump to individual runs</span>\n        <IconButton\n          icon={<i className=\"fas fa-times\" />}\n          onClick={handleClose}\n          style={{ float: 'right', marginLeft: '7px' }}\n        />\n      </div>\n    );\n  };\n\n  render() {\n    const { visible, x, y, handleVisibleChange } = this.props;\n    return (\n      <LegacyPopover\n        content={this.renderContent()}\n        title={this.renderTitle()}\n        placement=\"left\"\n        visible={visible}\n        onVisibleChange={handleVisibleChange}\n      >\n        <div\n          style={{\n            left: x,\n            top: y,\n            position: 'absolute',\n          }}\n        />\n      </LegacyPopover>\n    );\n  }\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { connect } from 'react-redux';\nimport Utils from '../../common/utils/Utils';\nimport RequestStateWrapper from '../../common/components/RequestStateWrapper';\nimport { getMetricHistoryApi, getRunApi } from '../actions';\nimport _ from 'lodash';\nimport { MetricsPlotView } from './MetricsPlotView';\nimport { getRunInfo } from '../reducers/Reducers';\nimport { MetricsPlotControls, X_AXIS_WALL, X_AXIS_RELATIVE, X_AXIS_STEP } from './MetricsPlotControls';\nimport MetricsSummaryTable from './MetricsSummaryTable';\nimport qs from 'qs';\nimport { withRouterNext } from '../../common/utils/withRouterNext';\nimport Routes from '../routes';\nimport { RunLinksPopover } from './RunLinksPopover';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport { saveAs } from 'file-saver';\nimport { Spinner } from '@databricks/design-system';\nimport { normalizeMetricsHistoryEntry } from '../utils/MetricsUtils';\nimport type { Location, NavigateFunction } from '../../common/utils/RoutingUtils';\n\nexport const CHART_TYPE_LINE = 'line';\nexport const CHART_TYPE_BAR = 'bar';\n\nexport const METRICS_PLOT_POLLING_INTERVAL_MS = 10 * 1000; // 10 seconds\n// A run is considered as 'hanging' if its status is 'RUNNING' but its latest metric was logged\n// prior to this threshold. The metrics plot doesn't automatically update hanging runs.\nexport const METRICS_PLOT_HANGING_RUN_THRESHOLD_MS = 3600 * 24 * 7 * 1000; // 1 week\nconst MAXIMUM_METRIC_DATA_POINTS = 100_000;\nconst GET_METRIC_HISTORY_MAX_RESULTS = 25000;\n\nexport const convertMetricsToCsv = (metrics: any) => {\n  const header = ['run_id', ...Object.keys(metrics[0].history[0])];\n  const rows = metrics.flatMap(({ runUuid, history }: any) =>\n    history.map((metric: any) => [runUuid, ...Object.values(metric)]),\n  );\n  return [header]\n    .concat(rows)\n    .map((row) => row.join(','))\n    .join('\\n');\n};\n\ntype OwnMetricsPlotPanelProps = {\n  experimentIds: string[];\n  runUuids: string[];\n  completedRunUuids: string[];\n  metricKey: string;\n  latestMetricsByRunUuid: any;\n  distinctMetricKeys: string[];\n  metricsWithRunInfoAndHistory: any[];\n  getMetricHistoryApi: (...args: any[]) => any;\n  getRunApi: (...args: any[]) => any;\n  location: Location;\n  navigate: NavigateFunction;\n  runDisplayNames: string[];\n  containsInfinities: boolean;\n};\n\ntype MetricsPlotPanelState = any;\n\ntype MetricsPlotPanelProps = OwnMetricsPlotPanelProps & typeof MetricsPlotPanel.defaultProps;\n\nexport class MetricsPlotPanel extends React.Component<MetricsPlotPanelProps, MetricsPlotPanelState> {\n  _isMounted = false;\n\n  static defaultProps = {\n    containsInfinities: false,\n  };\n\n  displayPopover: any;\n  intervalId: any;\n\n  // The fields below are exposed as instance attributes rather than component state so that they\n  // can be updated without triggering a rerender.\n  //\n  // ID of Javascript future (created via setTimeout()) used to trigger legend-click events after a\n  // delay, to allow time for double-click events to occur\n  legendClickTimeout = null;\n  // Time (millis after Unix epoch) since last legend click - if two clicks occur in short\n  // succession, we trigger a double-click event & cancel the pending single-click.\n  prevLegendClickTime = (Math as any).inf;\n\n  // Last curve ID clicked in the legend, used to determine if we're double-clicking on a specific\n  // legend curve\n  lastClickedLegendCurveId = null;\n\n  // Max time interval (in milliseconds) between two successive clicks on the metric plot legend\n  // that constitutes a double-click\n  MAX_DOUBLE_CLICK_INTERVAL_MS = 300;\n\n  // Delay (in ms) between when a user clicks on the metric plot legend & when event-handler logic\n  // (to toggle display of the selected curve on or off) actually fires. Set to a larger value than\n  // MAX_DOUBLE_CLICK_INTERVAL_MS to allow time for the double-click handler to fire before firing\n  // a single-click event.\n  SINGLE_CLICK_EVENT_DELAY_MS = this.MAX_DOUBLE_CLICK_INTERVAL_MS + 10;\n\n  constructor(props: MetricsPlotPanelProps) {\n    super(props);\n    this.state = {\n      historyRequestIds: [],\n      popoverVisible: false,\n      popoverX: 0,\n      popoverY: 0,\n      popoverRunItems: [],\n      focused: true,\n      loading: false,\n    };\n    this.displayPopover = false;\n    this.intervalId = null;\n  }\n\n  hasMultipleExperiments() {\n    return this.props.experimentIds && this.props.experimentIds.length > 1;\n  }\n\n  onFocus = () => {\n    this.setState({ focused: true });\n  };\n\n  onBlur = () => {\n    this.setState({ focused: false });\n  };\n\n  clearEventListeners = () => {\n    // `window.removeEventListener` does nothing when called with an unregistered event listener:\n    // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/removeEventListener\n    window.removeEventListener('focus', this.onFocus);\n    window.removeEventListener('blur', this.onBlur);\n  };\n\n  clearInterval = () => {\n    // `clearInterval` does nothing when called with `null` or `undefine`:\n    // https://www.w3.org/TR/2011/WD-html5-20110525/timers.html#dom-windowtimers-cleartimeout\n    clearInterval(this.intervalId);\n    this.intervalId = null;\n  };\n\n  allRunsCompleted = () => {\n    return this.props.completedRunUuids.length === this.props.runUuids.length;\n  };\n\n  isHangingRunUuid = (activeRunUuid: any) => {\n    const metrics = this.props.latestMetricsByRunUuid[activeRunUuid];\n    if (!metrics) {\n      return false;\n    }\n    // @ts-expect-error TS(2345): Argument of type '({ timestamp }: { timestamp: any... Remove this comment to see the full error message\n    const timestamps = Object.values(metrics).map(({ timestamp }) => timestamp);\n    // @ts-expect-error TS(2345): Argument of type 'unknown' is not assignable to pa... Remove this comment to see the full error message\n    const latestTimestamp = Math.max(...timestamps);\n    return new Date().getTime() - latestTimestamp > METRICS_PLOT_HANGING_RUN_THRESHOLD_MS;\n  };\n\n  getActiveRunUuids = () => {\n    const { completedRunUuids, runUuids } = this.props;\n    const activeRunUuids = _.difference(runUuids, completedRunUuids);\n    return activeRunUuids.filter(_.negate(this.isHangingRunUuid)); // Exclude hanging runs\n  };\n\n  shouldPoll = () => {\n    return !(this.allRunsCompleted() || this.getActiveRunUuids().length === 0);\n  };\n\n  componentDidMount() {\n    this._isMounted = true;\n    this.loadMetricHistory(this.props.runUuids, this.getUrlState().selectedMetricKeys);\n    if (this.shouldPoll()) {\n      // Set event listeners to detect when this component gains/loses focus,\n      // e.g., a user switches to a different browser tab or app.\n      window.addEventListener('blur', this.onBlur);\n      window.addEventListener('focus', this.onFocus);\n      this.intervalId = setInterval(() => {\n        // Skip polling if this component is out of focus.\n        // @ts-expect-error TS(4111): Property 'focused' comes from an index signature, ... Remove this comment to see the full error message\n        if (this.state.focused) {\n          const activeRunUuids = this.getActiveRunUuids();\n          this.loadMetricHistory(activeRunUuids, this.getUrlState().selectedMetricKeys);\n          this.loadRuns(activeRunUuids);\n\n          if (!this.shouldPoll()) {\n            this.clearEventListeners();\n            this.clearInterval();\n          }\n        }\n      }, METRICS_PLOT_POLLING_INTERVAL_MS);\n    }\n  }\n\n  componentWillUnmount() {\n    this._isMounted = false;\n    this.clearEventListeners();\n    this.clearInterval();\n  }\n\n  getUrlState() {\n    return Utils.getMetricPlotStateFromUrl(this.props.location.search);\n  }\n\n  static predictChartType(metrics: any) {\n    // Show bar chart when every metric has exactly 1 metric history\n    if (metrics && metrics.length && _.every(metrics, (metric) => metric.history && metric.history.length === 1)) {\n      return CHART_TYPE_BAR;\n    }\n    return CHART_TYPE_LINE;\n  }\n\n  static isComparing(search: any) {\n    const params = qs.parse(search);\n    const runs = params && params['?runs'];\n    // @ts-expect-error TS(2345): Argument of type 'string | string[] | ParsedQs | P... Remove this comment to see the full error message\n    return runs ? JSON.parse(runs).length > 1 : false;\n  }\n\n  // Update page URL from component state. Intended to be called after React applies component\n  // state updates, e.g. in a setState callback\n  updateUrlState = (updatedState: any) => {\n    const { runUuids, metricKey, location, navigate } = this.props;\n    // @ts-expect-error TS(2345): Argument of type 'string | string[] | ParsedQs | P... Remove this comment to see the full error message\n    const experimentIds = JSON.parse(qs.parse(location.search)['experiments']);\n    const newState = {\n      ...this.getUrlState(),\n      ...updatedState,\n    };\n    const {\n      selectedXAxis,\n      selectedMetricKeys,\n      showPoint,\n      yAxisLogScale,\n      lineSmoothness,\n      layout,\n      deselectedCurves,\n      lastLinearYAxisRange,\n    } = newState;\n    navigate(\n      Routes.getMetricPageRoute(\n        runUuids,\n        metricKey,\n        experimentIds,\n        selectedMetricKeys,\n        layout,\n        selectedXAxis,\n        yAxisLogScale,\n        lineSmoothness,\n        showPoint,\n        deselectedCurves,\n        lastLinearYAxisRange,\n      ),\n      {\n        replace: true,\n      },\n    );\n  };\n\n  getNumTotalMetrics = () => {\n    return this.props.metricsWithRunInfoAndHistory.map(({ history }) => history.length).reduce((a, b) => a + b, 0);\n  };\n\n  loadMetricHistory = (runUuids: any, metricKeys: any) => {\n    if (this.getNumTotalMetrics() >= MAXIMUM_METRIC_DATA_POINTS) {\n      Utils.logErrorAndNotifyUser(\n        'The total number of metric data points exceeded 100,000. Cannot fetch more metrics.',\n      );\n      return Promise.resolve([]);\n    }\n    this.setState({ loading: true });\n    const promises = runUuids\n      .flatMap((id: any) =>\n        metricKeys.map((key: any) => ({\n          runUuid: id,\n          metricKey: key,\n        })),\n      )\n      // Avoid fetching non existing metrics\n      .filter(({ runUuid, metricKey }: any) => this.props.latestMetricsByRunUuid[runUuid].hasOwnProperty(metricKey))\n      .map(async ({ runUuid, metricKey }: any) => {\n        const requestIds = [];\n        const id = getUUID();\n        requestIds.push(id);\n        const firstPageResp = await this.props.getMetricHistoryApi(\n          runUuid,\n          metricKey,\n          GET_METRIC_HISTORY_MAX_RESULTS,\n          undefined,\n          id,\n        );\n\n        let nextPageToken = firstPageResp.value.next_page_token;\n        while (nextPageToken) {\n          if (this.getNumTotalMetrics() >= MAXIMUM_METRIC_DATA_POINTS) {\n            return { requestIds, success: false };\n          }\n\n          const uid = getUUID();\n          requestIds.push(uid);\n          /* eslint-disable no-await-in-loop */\n          const nextPageResp = await this.props.getMetricHistoryApi(\n            runUuid,\n            metricKey,\n            GET_METRIC_HISTORY_MAX_RESULTS,\n            nextPageToken,\n            uid,\n          );\n          nextPageToken = nextPageResp.value.next_page_token;\n        }\n        return { requestIds, success: true };\n      });\n    return Promise.all(promises).then((results) => {\n      // Ensure we don't set state if component is unmounted\n      if (this._isMounted) {\n        this.setState({ loading: false });\n      }\n      if (!results.every(({ success }) => success)) {\n        Utils.logErrorAndNotifyUser(\n          'The total number of metric data points exceeded 100,000. Aborted fetching metrics.',\n        );\n      }\n      return results.flatMap(({ requestIds }) => requestIds);\n    });\n  };\n\n  loadRuns = (runUuids: any) => {\n    const requestIds: any = [];\n    runUuids.forEach((runUuid: any) => {\n      const id = getUUID();\n      this.props.getRunApi(runUuid);\n      requestIds.push(id);\n    });\n    return requestIds;\n  };\n\n  getMetrics = () => {\n    /* eslint-disable no-param-reassign */\n    const state = this.getUrlState();\n    const selectedMetricsSet = new Set(state.selectedMetricKeys);\n    const { selectedXAxis } = state;\n    const { metricsWithRunInfoAndHistory } = this.props;\n\n    // Take only selected metrics\n    const metrics = metricsWithRunInfoAndHistory.filter((m) => selectedMetricsSet.has(m.metricKey));\n\n    // Sort metric history based on selected x-axis\n    metrics.forEach((metric) => {\n      const isStep = selectedXAxis === X_AXIS_STEP && metric.history[0] && _.isNumber(metric.history[0].step);\n      // Metric history can be large. Doing an in-place here to save memory\n      metric.history.sort(isStep ? Utils.compareByStepAndTimestamp : Utils.compareByTimestamp);\n    });\n\n    return metrics;\n  };\n\n  /**\n   * Handle changes in the scale type of the y-axis\n   * @param yAxisLogScale: Boolean - if true, y-axis should be converted to log scale, and if false,\n   * y-axis scale should be converted to a linear scale.\n   */\n  handleYAxisLogScaleChange = (yAxisLogScale: any) => {\n    const state = this.getUrlState();\n    const newLayout = _.cloneDeep(state.layout);\n    const newAxisType = yAxisLogScale ? 'log' : 'linear';\n\n    // Handle special case of a linear y-axis scale with negative values converted to log scale &\n    // now being restored to linear scale, by restoring the old linear-axis range from\n    // state.linearYAxisRange. In particular, we assume that if state.linearYAxisRange\n    // is non-empty, it contains a linear y axis range with negative values.\n    if (!yAxisLogScale && (state as any).lastLinearYAxisRange && (state as any).lastLinearYAxisRange.length > 0) {\n      newLayout.yaxis = {\n        type: 'linear',\n        range: (state as any).lastLinearYAxisRange,\n      };\n      this.updateUrlState({ layout: newLayout, lastLinearYAxisRange: [] });\n      return;\n    }\n\n    // Otherwise, if plot previously had no y axis range configured, simply set the axis type to\n    // log or linear scale appropriately\n    if (!state.layout.yaxis || !state.layout.yaxis.range) {\n      newLayout.yaxis = {\n        type: newAxisType,\n        autorange: true,\n        ...(newAxisType === 'log' ? { exponentformat: 'e' } : {}),\n      };\n      this.updateUrlState({ layout: newLayout, lastLinearYAxisRange: [] });\n      return;\n    }\n\n    // lastLinearYAxisRange contains the last range used for a linear-scale y-axis. We set\n    // this state attribute if and only if we're converting from a linear-scale y-axis with\n    // negative bounds to a log scale axis, so that we can restore the negative bounds if we\n    // subsequently convert back to a linear scale axis. Otherwise, we reset this attribute to an\n    // empty array\n    let lastLinearYAxisRange = [];\n\n    // At this point, we know the plot previously had a y axis specified with range bounds\n    // Convert the range to/from log scale as appropriate\n    const oldLayout = state.layout;\n    const oldYRange = oldLayout.yaxis.range;\n    if (yAxisLogScale) {\n      if (oldYRange[0] <= 0) {\n        lastLinearYAxisRange = oldYRange;\n        // When converting to log scale, handle negative values (which have no log-scale\n        // representation as taking the log of a negative number is not possible) as follows:\n        // If bottom of old Y range is negative, then tell plotly to infer the log y-axis scale\n        // (set 'autorange' to true), and preserve the old range in the lastLinearYAxisRange\n        // state attribute so that we can restore it if the user converts back to a linear-scale\n        // y axis. We defer to Plotly's autorange here under the assumption that it will produce\n        // a reasonable y-axis log scale for plots containing negative values.\n        newLayout.yaxis = {\n          type: 'log',\n          autorange: true,\n          exponentformat: 'e',\n        };\n      } else {\n        newLayout.yaxis = {\n          type: 'log',\n          range: [Math.log(oldYRange[0]) / Math.log(10), Math.log(oldYRange[1]) / Math.log(10)],\n          exponentformat: 'e',\n        };\n      }\n    } else {\n      // Otherwise, convert from log to linear scale normally\n      newLayout.yaxis = {\n        type: 'linear',\n        range: [Math.pow(10, oldYRange[0]), Math.pow(10, oldYRange[1])],\n      };\n    }\n    this.updateUrlState({ layout: newLayout, lastLinearYAxisRange });\n  };\n\n  /**\n   * Handle changes in the type of the metric plot's X axis (e.g. changes from wall-clock\n   * scale to relative-time scale to step-based scale).\n   * @param e: Selection event such that e.target.value is a string containing the new X axis type\n   */\n  handleXAxisChange = (e: any) => {\n    // Set axis value type, & reset axis scaling via autorange\n    const state = this.getUrlState();\n    const axisEnumToPlotlyType = {\n      [X_AXIS_WALL]: 'date',\n      [X_AXIS_RELATIVE]: 'linear',\n      [X_AXIS_STEP]: 'linear',\n    };\n    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n    const axisType = axisEnumToPlotlyType[e.target.value] || 'linear';\n    const newLayout = {\n      ...state.layout,\n      xaxis: {\n        autorange: true,\n        type: axisType,\n      },\n    };\n    this.updateUrlState({ selectedXAxis: e.target.value, layout: newLayout });\n  };\n\n  getAxisType() {\n    const state = this.getUrlState();\n    return state.layout && state.layout.yaxis && state.layout.yaxis.type === 'log' ? 'log' : 'linear';\n  }\n\n  /**\n   * Handle changes to metric plot layout (x & y axis ranges), e.g. specifically if the user\n   * zooms in or out on the plot.\n   *\n   * @param newLayout: Object containing the new Plot layout. See\n   * https://plot.ly/javascript/plotlyjs-events/#update-data for details on the object's fields\n   * and schema.\n   */\n  handleLayoutChange = (newLayout: any) => {\n    this.displayPopover = false;\n    const state = this.getUrlState();\n    // Unfortunately, we need to parse out the x & y axis range changes from the onLayout event...\n    // see https://plot.ly/javascript/plotlyjs-events/#update-data\n    const {\n      'xaxis.range[0]': newXRange0,\n      'xaxis.range[1]': newXRange1,\n      'yaxis.range[0]': newYRange0,\n      'yaxis.range[1]': newYRange1,\n      'xaxis.autorange': xAxisAutorange,\n      'yaxis.autorange': yAxisAutorange,\n      'yaxis.showspikes': yAxisShowSpikes,\n      'xaxis.showspikes': xAxisShowSpikes,\n      ...restFields\n    } = newLayout;\n\n    let mergedLayout = {\n      ...state.layout,\n      ...restFields,\n    };\n    let lastLinearYAxisRange = [...(state as any).lastLinearYAxisRange];\n\n    // Set fields for x axis\n    const newXAxis = mergedLayout.xaxis || {};\n    if (newXRange0 !== undefined && newXRange1 !== undefined) {\n      newXAxis.range = [newXRange0, newXRange1];\n      newXAxis.autorange = false;\n    }\n    if (xAxisShowSpikes) {\n      newXAxis.showspikes = true;\n    }\n    if (xAxisAutorange) {\n      newXAxis.autorange = true;\n    }\n    // Set fields for y axis\n    const newYAxis = mergedLayout.yaxis || {};\n    if (newYRange0 !== undefined && newYRange1 !== undefined) {\n      newYAxis.range = [newYRange0, newYRange1];\n      newYAxis.autorange = false;\n    }\n    if (yAxisShowSpikes) {\n      newYAxis.showspikes = true;\n    }\n    if (yAxisAutorange) {\n      lastLinearYAxisRange = [];\n      const axisType = state.layout && state.layout.yaxis && state.layout.yaxis.type === 'log' ? 'log' : 'linear';\n      newYAxis.autorange = true;\n      newYAxis.type = axisType;\n    }\n    if (newYAxis.type === 'log') {\n      newYAxis.exponentformat = 'e';\n    }\n    // Merge new X & Y axis info into layout\n    mergedLayout = {\n      ...mergedLayout,\n      xaxis: newXAxis,\n      yaxis: newYAxis,\n    };\n    this.updateUrlState({ layout: mergedLayout, lastLinearYAxisRange });\n  };\n\n  handleDownloadCsv = () => {\n    const csv = convertMetricsToCsv(this.props.metricsWithRunInfoAndHistory);\n    const blob = new Blob([csv], { type: 'application/csv;charset=utf-8' });\n    saveAs(blob, 'metrics.csv');\n  };\n\n  // Return unique key identifying the curve or bar chart corresponding to the specified\n  // Plotly plot data element\n  static getCurveKey(plotDataElem: any) {\n    // In bar charts, each legend item consists of a single run ID (all bars for that run are\n    // associated with & toggled by that legend item)\n    if (plotDataElem.type === 'bar') {\n      return plotDataElem.runId;\n    } else {\n      // In line charts, each (run, metricKey) tuple has its own legend item, so construct\n      // a unique legend item identifier by concatenating the run id & metric key\n      return Utils.getCurveKey(plotDataElem.runId, plotDataElem.metricName);\n    }\n  }\n\n  /**\n   * Handle clicking on a single curve within the plot legend in order to toggle its display\n   * on/off.\n   */\n  handleLegendClick = ({ curveNumber, data }: any) => {\n    // If two clicks in short succession, trigger double-click event\n    const state = this.getUrlState();\n    const currentTime = Date.now();\n    if (\n      currentTime - this.prevLegendClickTime < this.MAX_DOUBLE_CLICK_INTERVAL_MS &&\n      curveNumber === this.lastClickedLegendCurveId\n    ) {\n      this.handleLegendDoubleClick({ curveNumber, data });\n      this.prevLegendClickTime = (Math as any).inf;\n    } else {\n      // Otherwise, record time of current click & trigger click event\n      // Wait full double-click window to trigger setting state, and only if there was no\n      // double-click do we run the single-click logic (we wait a little extra to be safe)\n      const curveKey = MetricsPlotPanel.getCurveKey(data[curveNumber]);\n      // @ts-expect-error TS(2322): Type 'number' is not assignable to type 'null'.\n      this.legendClickTimeout = window.setTimeout(() => {\n        const existingDeselectedCurves = new Set((state as any).deselectedCurves);\n        if (existingDeselectedCurves.has(curveKey)) {\n          existingDeselectedCurves.delete(curveKey);\n        } else {\n          existingDeselectedCurves.add(curveKey);\n        }\n        this.updateUrlState({ deselectedCurves: Array.from(existingDeselectedCurves) });\n      }, this.SINGLE_CLICK_EVENT_DELAY_MS);\n      this.prevLegendClickTime = currentTime;\n    }\n    this.lastClickedLegendCurveId = curveNumber;\n    // Return false to disable plotly event handler\n    return false;\n  };\n\n  /**\n   * Handle double-clicking on a single curve within the plot legend in order to toggle display\n   * of the selected curve on (and disable display of all other curves).\n   */\n  handleLegendDoubleClick = ({ curveNumber, data }: any) => {\n    // @ts-expect-error TS(2769): No overload matches this call.\n    window.clearTimeout(this.legendClickTimeout);\n    // Exclude everything besides the current curve key\n    const curveKey = MetricsPlotPanel.getCurveKey(data[curveNumber]);\n    const allCurveKeys = data.map((elem: any) => MetricsPlotPanel.getCurveKey(elem));\n    const newDeselectedCurves = allCurveKeys.filter((curvePair: any) => curvePair !== curveKey);\n    this.updateUrlState({ deselectedCurves: newDeselectedCurves });\n    return false;\n  };\n\n  handleMetricsSelectChange = (metricKeys: any) => {\n    const existingMetricKeys = this.getUrlState().selectedMetricKeys || [];\n    const newMetricKeys = metricKeys.filter((k: any) => !existingMetricKeys.includes(k));\n    this.updateUrlState({ selectedMetricKeys: metricKeys });\n    this.loadMetricHistory(this.props.runUuids, newMetricKeys).then((requestIds) => {\n      this.setState({ loading: false });\n      this.setState((prevState: any) => ({\n        historyRequestIds: [...prevState.historyRequestIds, ...requestIds],\n      }));\n    });\n  };\n\n  handleShowPointChange = (showPoint: any) => this.updateUrlState({ showPoint });\n\n  handleLineSmoothChange = (lineSmoothness: any) => this.updateUrlState({ lineSmoothness });\n\n  handleKeyDownOnPopover = ({ key }: { key: string }) => {\n    if (key === 'Escape') {\n      this.setState({ popoverVisible: false });\n    }\n  };\n\n  updatePopover = (data: any) => {\n    this.displayPopover = !this.displayPopover;\n\n    // Ignore double click.\n    setTimeout(() => {\n      if (this.displayPopover) {\n        this.displayPopover = false;\n        const { popoverVisible, popoverX, popoverY } = this.state;\n        const {\n          points,\n          event: { clientX, clientY },\n        } = data;\n        const samePointClicked = popoverX === clientX && popoverY === clientY;\n        const runItems = points\n          .sort((a: any, b: any) => b.y - a.y)\n          .map((point: any) => ({\n            runId: point.data.runId,\n            name: point.data.name,\n            color: point.fullData.marker.color,\n            y: point.y,\n          }));\n\n        this.setState({\n          popoverVisible: !popoverVisible || !samePointClicked,\n          popoverX: clientX,\n          popoverY: clientY,\n          popoverRunItems: runItems,\n        });\n      }\n    }, 300);\n  };\n\n  render() {\n    const { experimentIds, runUuids, runDisplayNames, distinctMetricKeys, location } = this.props;\n    const { popoverVisible, popoverX, popoverY, popoverRunItems, loading } = this.state;\n    const state = this.getUrlState();\n    const { showPoint, selectedXAxis, selectedMetricKeys, lineSmoothness } = state;\n    const yAxisLogScale = this.getAxisType() === 'log';\n    const { historyRequestIds } = this.state;\n    const metrics = this.getMetrics();\n    const chartType = MetricsPlotPanel.predictChartType(metrics);\n    return (\n      <div className=\"metrics-plot-container\">\n        <MetricsPlotControls\n          // @ts-expect-error TS(2322): Type '{ numRuns: number; numCompletedRuns: number;... Remove this comment to see the full error message\n          numRuns={this.props.runUuids.length}\n          numCompletedRuns={this.props.completedRunUuids.length}\n          distinctMetricKeys={distinctMetricKeys}\n          selectedXAxis={selectedXAxis}\n          selectedMetricKeys={selectedMetricKeys}\n          handleXAxisChange={this.handleXAxisChange}\n          handleMetricsSelectChange={this.handleMetricsSelectChange}\n          handleShowPointChange={this.handleShowPointChange}\n          handleYAxisLogScaleChange={this.handleYAxisLogScaleChange}\n          handleLineSmoothChange={this.handleLineSmoothChange}\n          chartType={chartType}\n          lineSmoothness={lineSmoothness}\n          yAxisLogScale={yAxisLogScale}\n          showPoint={showPoint}\n          handleDownloadCsv={this.handleDownloadCsv}\n          disableSmoothnessControl={this.props.containsInfinities}\n        />\n        <div className=\"metrics-plot-data\">\n          <RequestStateWrapper\n            requestIds={historyRequestIds}\n            // In this case where there are no history request IDs (e.g. on the\n            // initial page load / before we try to load additional metrics),\n            // optimistically render the children\n            shouldOptimisticallyRender={historyRequestIds.length === 0}\n          >\n            {this.hasMultipleExperiments() ? null : (\n              <RunLinksPopover\n                experimentId={experimentIds[0]}\n                visible={popoverVisible}\n                x={popoverX}\n                y={popoverY}\n                runItems={popoverRunItems}\n                handleKeyDown={this.handleKeyDownOnPopover}\n                handleClose={() => this.setState({ popoverVisible: false })}\n                handleVisibleChange={(visible) => this.setState({ popoverVisible: visible })}\n              />\n            )}\n            <Spinner size=\"large\" css={{ visibility: loading ? 'visible' : 'hidden' }} />\n            <MetricsPlotView\n              // @ts-expect-error TS(2322): Type '{ runUuids: string[]; runDisplayNames: strin... Remove this comment to see the full error message\n              runUuids={runUuids}\n              runDisplayNames={runDisplayNames}\n              xAxis={selectedXAxis}\n              metrics={this.getMetrics()}\n              metricKeys={selectedMetricKeys}\n              showPoint={showPoint}\n              chartType={chartType}\n              isComparing={MetricsPlotPanel.isComparing(location.search)}\n              lineSmoothness={lineSmoothness}\n              extraLayout={state.layout}\n              deselectedCurves={(state as any).deselectedCurves}\n              onLayoutChange={this.handleLayoutChange}\n              onClick={this.updatePopover}\n              onLegendClick={this.handleLegendClick}\n              onLegendDoubleClick={this.handleLegendDoubleClick}\n            />\n            <MetricsSummaryTable\n              runUuids={runUuids}\n              runDisplayNames={runDisplayNames}\n              metricKeys={selectedMetricKeys}\n            />\n          </RequestStateWrapper>\n        </div>\n      </div>\n    );\n  }\n}\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const { runUuids } = ownProps;\n  const completedRunUuids = runUuids.filter((runUuid: any) => getRunInfo(runUuid, state).status !== 'RUNNING');\n  const { latestMetricsByRunUuid, metricsByRunUuid } = state.entities;\n\n  // All metric keys from all runUuids, non-distinct\n  const metricKeys = _.flatMap(runUuids, (runUuid) => {\n    const latestMetrics = latestMetricsByRunUuid[runUuid];\n    return latestMetrics ? Object.keys(latestMetrics) : [];\n  });\n  const distinctMetricKeys = [...new Set(metricKeys)].sort();\n  const runDisplayNames: any = [];\n\n  let containsInfinities = false;\n\n  // Flat array of all metrics, with history and information of the run it belongs to\n  // This is used for underlying MetricsPlotView & predicting chartType for MetricsPlotControls\n  const metricsWithRunInfoAndHistory = _.flatMap(runUuids, (runUuid) => {\n    const runDisplayName = Utils.getRunDisplayName(getRunInfo(runUuid, state), runUuid);\n    runDisplayNames.push(runDisplayName);\n    const metricsHistory = metricsByRunUuid[runUuid];\n    return metricsHistory\n      ? Object.keys(metricsHistory).map((metricKey) => {\n          const history = metricsHistory[metricKey].map((entry: any) => normalizeMetricsHistoryEntry(entry));\n          if (history.some(({ value }: any) => typeof value === 'number' && !isNaN(value) && !isFinite(value))) {\n            containsInfinities = true;\n          }\n          return { metricKey, history, runUuid, runDisplayName };\n        })\n      : [];\n  });\n\n  return {\n    runDisplayNames,\n    latestMetricsByRunUuid,\n    distinctMetricKeys,\n    metricsWithRunInfoAndHistory,\n    completedRunUuids,\n    containsInfinities,\n  };\n};\n\nconst mapDispatchToProps = { getMetricHistoryApi, getRunApi };\n\nexport default withRouterNext(connect(mapStateToProps, mapDispatchToProps)(MetricsPlotPanel));\n", "import { useDesignSystemTheme } from '@databricks/design-system';\nimport { Input, Slider } from '@databricks/design-system';\nimport { clamp, isEmpty, isUndefined, keys } from 'lodash';\nimport { useState } from 'react';\n\nconst TRACK_SIZE = 20;\nconst THUMB_SIZE = 14;\nconst MARK_SIZE = 8;\nconst MARK_OFFSET_X = (THUMB_SIZE - MARK_SIZE) / 2;\nconst MARK_OFFSET_Y = (TRACK_SIZE - MARK_SIZE) / 2;\n\nconst ZINDEX_MARK = 1;\nconst ZINDEX_THUMB = 2;\n\nconst STEP_MARKS_DISPLAY_THRESHOLD = 10;\n\ninterface LineSmoothSliderProps {\n  max?: number;\n  min?: number;\n  step?: number;\n  marks?: Record<number, any>;\n  value: number | undefined;\n  onChange: (value: number) => void;\n  disabled?: boolean;\n  componentId?: string;\n  onAfterChange?: (value: number) => void;\n  className?: string;\n}\n\n// Internal helper function: finds the closest value to the given value from the marks\nconst getClosestValue = (marks: Record<number, string>, value: number, defaultValue: number) =>\n  keys(marks).reduce(\n    (prev, curr) => (Math.abs(Number(curr) - value) < Math.abs(prev - value) ? Number(curr) : Number(prev)),\n    defaultValue,\n  );\n\n// Internal helper function: finds the next value based on direction (down or up) from the marks\nconst getNextValue = (marks: Record<number, string>, currentValue: number, direction: 'down' | 'up') =>\n  direction === 'down'\n    ? Math.max(\n        ...Object.keys(marks)\n          .filter((mark) => Number(mark) < currentValue)\n          .map(Number),\n      )\n    : Math.min(\n        ...Object.keys(marks)\n          .filter((mark) => Number(mark) > currentValue)\n          .map(Number),\n      );\n\nexport const LineSmoothSlider = ({\n  max = 1,\n  min = 0,\n  step,\n  marks,\n  value,\n  onChange,\n  disabled,\n  onAfterChange,\n  componentId,\n  className,\n}: LineSmoothSliderProps) => {\n  const { theme } = useDesignSystemTheme();\n  const shouldUseMarks = !isEmpty(marks);\n  const shouldDisplayMarks = shouldUseMarks && Object.keys(marks).length < STEP_MARKS_DISPLAY_THRESHOLD;\n\n  // Temporary value is used to store the value of the input field before it is committed\n  const [temporaryValue, setTemporaryValue] = useState<number | undefined>(undefined);\n\n  return (\n    <div\n      css={{\n        display: 'flex',\n        height: theme.general.heightSm,\n        gap: theme.spacing.md,\n        alignItems: 'center',\n      }}\n    >\n      <Slider.Root\n        disabled={disabled}\n        css={{\n          flex: 1,\n          position: 'relative',\n          'span:last-child': { zIndex: ZINDEX_THUMB },\n        }}\n        className={className}\n        min={min}\n        max={max}\n        value={[value ?? 0]}\n        onValueCommit={([newValue]) => onAfterChange?.(newValue)}\n        onKeyDown={(e) => {\n          // If marks are used, we want to find the next value based on direction (arrow left/down or arrow right/up)\n          if (shouldUseMarks) {\n            e.preventDefault();\n\n            if (['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {\n              const nextValue = getNextValue(\n                marks,\n                value ?? 0,\n                e.key === 'ArrowLeft' || e.key === 'ArrowDown' ? 'down' : 'up',\n              );\n\n              onAfterChange?.(nextValue);\n              onChange(nextValue);\n            }\n          }\n        }}\n        onValueChange={([newValue]) => {\n          if (shouldUseMarks) {\n            onChange(getClosestValue(marks, newValue, value ?? 0));\n            return;\n          }\n          onChange(newValue);\n        }}\n        step={step ?? undefined}\n      >\n        {/* Render marks if needed */}\n        {shouldDisplayMarks && (\n          <div css={{ position: 'absolute', inset: 0, marginRight: THUMB_SIZE }}>\n            {keys(marks).map((markPosition) => (\n              <div\n                key={markPosition}\n                css={{\n                  position: 'absolute',\n                  zIndex: ZINDEX_MARK,\n                  top: 0,\n                  right: 0,\n                  bottom: 0,\n                  marginLeft: -MARK_OFFSET_X / 2,\n                  marginTop: MARK_OFFSET_Y,\n                  pointerEvents: 'none',\n                  borderRadius: '100%',\n                  backgroundColor: theme.colors.actionPrimaryBackgroundDefault,\n                  height: MARK_SIZE,\n                  width: MARK_SIZE,\n                  opacity: 0.5,\n                }}\n                style={{\n                  left: `${(Number(markPosition) / (max - min)) * 100}%`,\n                }}\n              />\n            ))}\n          </div>\n        )}\n        <Slider.Track className=\"TRACK\">\n          <Slider.Range />\n        </Slider.Track>\n        <Slider.Thumb css={{ position: 'relative', height: THUMB_SIZE, width: THUMB_SIZE }} />\n      </Slider.Root>\n      <Input\n        componentId={componentId ?? 'mlflow.experiment_tracking.common.line_smooth_slider'}\n        type=\"number\"\n        disabled={disabled}\n        min={min}\n        max={max}\n        css={{ width: 'min-content' }}\n        step={step}\n        value={temporaryValue ?? value}\n        onBlur={() => {\n          // If temporary value is set, we want to commit it to the value\n          if (!isUndefined(temporaryValue)) {\n            if (shouldUseMarks) {\n              onAfterChange?.(getClosestValue(marks, temporaryValue, value ?? 0));\n              onChange(getClosestValue(marks, temporaryValue, value ?? 0));\n            } else {\n              onAfterChange?.(clamp(temporaryValue, min, max));\n              onChange(clamp(temporaryValue, min, max));\n            }\n            setTemporaryValue(undefined);\n          }\n        }}\n        onChange={({ target, nativeEvent }) => {\n          // If the input event is an input event, we want to set the temporary value\n          // to be commited on blur instead of directly setting the value\n          if (nativeEvent instanceof InputEvent) {\n            setTemporaryValue(Number(target.value));\n            return;\n          }\n\n          // If using marks, find the next value based on the direction of the change\n          if (shouldUseMarks) {\n            const nextValue = getNextValue(marks, value ?? 0, Number(target.value) < Number(value) ? 'down' : 'up');\n\n            onChange(nextValue);\n            return;\n          }\n\n          // If not using marks, clamp the value to the min and max\n          onChange(clamp(Number(target.value), min, max));\n        }}\n      />\n    </div>\n  );\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { LegacySkeleton } from '@databricks/design-system';\nimport { SectionErrorBoundary } from '../../common/components/error-boundaries/SectionErrorBoundary';\n\nconst Plot = React.lazy(() => import('react-plotly.js'));\n\nexport const LazyPlot = ({ fallback, ...props }: any) => (\n  <SectionErrorBoundary>\n    <React.Suspense fallback={fallback ?? <LegacySkeleton active />}>\n      <Plot {...props} />\n    </React.Suspense>\n  </SectionErrorBoundary>\n);\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport Utils from '../../common/utils/Utils';\nimport _ from 'lodash';\nimport { saveAs } from 'file-saver';\nimport { X_AXIS_STEP, X_AXIS_RELATIVE, MAX_LINE_SMOOTHNESS } from './MetricsPlotControls';\nimport { CHART_TYPE_BAR, convertMetricsToCsv } from './MetricsPlotPanel';\nimport { LazyPlot } from './LazyPlot';\nimport { generateInfinityAnnotations } from '../utils/MetricsUtils';\nimport { injectIntl, IntlShape } from 'react-intl';\n\nconst MAX_RUN_NAME_DISPLAY_LENGTH = 24;\nconst EMA_THRESHOLD = 1;\n\nexport const EMA = (mArray: any, smoothingWeight: any) => {\n  // If all elements in the set of metric values are constant, or if\n  // the degree of smoothing is set to the minimum value, return the\n  // original set of metric values\n  if (smoothingWeight <= 1 || !mArray || mArray.length <= EMA_THRESHOLD || mArray.every((v: any) => v === mArray[0])) {\n    return mArray;\n  }\n\n  const smoothness = smoothingWeight / (MAX_LINE_SMOOTHNESS + 1);\n  const smoothedArray = [];\n  let biasedElement = 0;\n  for (let i = 0; i < mArray.length; i++) {\n    if (!isNaN(mArray[i])) {\n      biasedElement = biasedElement * smoothness + (1 - smoothness) * mArray[i];\n      // To avoid biasing earlier elements toward smaller-than-accurate values, we divide\n      // all elements by a `debiasedWeight` that asymptotically increases and approaches\n      // 1 as the element index increases\n      const debiasWeight = 1.0 - Math.pow(smoothness, i + 1);\n      const debiasedElement = biasedElement / debiasWeight;\n      smoothedArray.push(debiasedElement);\n    } else {\n      smoothedArray.push(mArray[i]);\n    }\n  }\n  return smoothedArray;\n};\n\n// To avoid pulling in plotly.js (unlazily) and / or using a separate package, just duplicating here\n// Copied from https://github.com/plotly/plotly.js/blob/v2.5.1/src/fonts/ploticon.js#L100\nconst DISK_ICON = {\n  width: 857.1,\n  height: 1000,\n  // eslint-disable-next-line max-len\n  path: 'm214-7h429v214h-429v-214z m500 0h72v500q0 8-6 21t-11 20l-157 156q-5 6-19 12t-22 5v-232q0-22-15-38t-38-16h-322q-22 0-37 16t-16 38v232h-72v-714h72v232q0 22 16 38t37 16h465q22 0 38-16t15-38v-232z m-214 518v178q0 8-5 13t-13 5h-107q-7 0-13-5t-5-13v-178q0-8 5-13t13-5h107q7 0 13 5t5 13z m357-18v-518q0-22-15-38t-38-16h-750q-23 0-38 16t-16 38v750q0 22 16 38t38 16h517q23 0 50-12t42-26l156-157q16-15 27-42t11-49z',\n  transform: 'matrix(1 0 0 -1 0 850)',\n};\n\ntype MetricsPlotViewImplProps = {\n  runUuids: string[];\n  runDisplayNames: string[];\n  metrics: any[];\n  xAxis: string;\n  metricKeys: string[];\n  showPoint: boolean;\n  chartType: string;\n  isComparing: boolean;\n  lineSmoothness?: number;\n  extraLayout?: any;\n  onLayoutChange: (...args: any[]) => any;\n  onClick: (...args: any[]) => any;\n  onLegendClick: (...args: any[]) => any;\n  onLegendDoubleClick: (...args: any[]) => any;\n  deselectedCurves: string[];\n  intl?: any;\n};\n\nexport class MetricsPlotViewImpl extends React.Component<MetricsPlotViewImplProps> {\n  static getLineLegend = (metricKey: any, runDisplayName: any, isComparing: any) => {\n    let legend = metricKey;\n    if (isComparing) {\n      legend += `, ${Utils.truncateString(runDisplayName, MAX_RUN_NAME_DISPLAY_LENGTH)}`;\n    }\n    return legend;\n  };\n\n  static getXValuesForLineChart(history: any, xAxisType: any, intl?: IntlShape) {\n    if (history.length === 0) {\n      return [];\n    }\n    switch (xAxisType) {\n      case X_AXIS_STEP:\n        return history.map(({ step }: any) => step);\n      case X_AXIS_RELATIVE: {\n        // @ts-expect-error TS(2339): Property 'timestamp' does not exist on type '{ toS... Remove this comment to see the full error message\n        const { timestamp: minTimestamp } = _.minBy(history, 'timestamp');\n        return history.map(({ timestamp }: any) => (timestamp - minTimestamp) / 1000);\n      }\n      default: // X_AXIS_WALL\n        return history.map(({ timestamp }: any) => timestamp);\n    }\n  }\n\n  /**\n   * Regenerates annotations and shapes for infinity and NaN values.\n   * Best called infrequently. Ideally should be called only when data input changes.\n   */\n  regenerateInfinityAnnotations = () => {\n    const { metrics, xAxis, extraLayout } = this.props;\n    const isYAxisLog = extraLayout?.yaxis?.type === 'log';\n    const annotationData = {};\n\n    metrics.forEach((metric) => {\n      const { metricKey, history } = metric;\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      annotationData[metricKey] = generateInfinityAnnotations({\n        xValues: (MetricsPlotView as any).getXValuesForLineChart(history, xAxis, this.props.intl),\n        yValues: history.map((entry: any) => (typeof entry.value === 'number' ? entry.value : Number(entry.value))),\n        isLogScale: isYAxisLog,\n        stringFormatter: (value) => this.props.intl.formatMessage(value, { metricKey }),\n      });\n    });\n\n    this.#annotationData = annotationData;\n  };\n\n  #annotationData = {};\n\n  getPlotPropsForLineChart = () => {\n    const { metrics, xAxis, showPoint, lineSmoothness, isComparing, deselectedCurves } = this.props;\n\n    const deselectedCurvesSet = new Set(deselectedCurves);\n    const shapes: any = [];\n    const annotations: any = [];\n\n    const data = metrics.map((metric) => {\n      const { metricKey, runDisplayName, history, runUuid } = metric;\n      const historyValues = history.map((entry: any) =>\n        typeof entry.value === 'number' ? entry.value : Number(entry.value),\n      );\n      // For metrics with exactly one non-NaN item, we set `isSingleHistory` to `true` in order\n      // to display the item as a point. For metrics with zero non-NaN items (i.e., empty metrics),\n      // we also set `isSingleHistory` to `true` in order to populate the plot legend with a\n      // point-style entry for each empty metric, although no data will be plotted for empty\n      // metrics\n      const isSingleHistory = historyValues.filter((value: any) => !isNaN(value)).length <= 1;\n\n      const visible = !deselectedCurvesSet.has(Utils.getCurveKey(runUuid, metricKey)) ? true : 'legendonly';\n\n      if (this.#annotationData && metricKey in this.#annotationData && visible === true) {\n        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n        shapes.push(...this.#annotationData[metricKey].shapes);\n        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n        annotations.push(...this.#annotationData[metricKey].annotations);\n      }\n\n      return {\n        name: (MetricsPlotView as any).getLineLegend(metricKey, runDisplayName, isComparing),\n        x: (MetricsPlotView as any).getXValuesForLineChart(history, xAxis),\n        y: (isSingleHistory ? historyValues : EMA(historyValues, lineSmoothness)).map((entry: any) =>\n          !isFinite(entry) ? NaN : entry,\n        ),\n        text: historyValues.map((value: any) => (isNaN(value) ? value : value.toFixed(5))),\n        type: 'scattergl',\n        mode: isSingleHistory ? 'markers' : 'lines+markers',\n        marker: { opacity: isSingleHistory || showPoint ? 1 : 0 },\n        hovertemplate: isSingleHistory || lineSmoothness === 1 ? '%{y}' : 'Value: %{text}<br>Smoothed: %{y}',\n        visible: visible,\n        runId: runUuid,\n        metricName: metricKey,\n      };\n    });\n    const props = { data };\n\n    (props as any).layout = {\n      ...(props as any).layout,\n      ...this.props.extraLayout,\n      shapes,\n      annotations,\n    };\n\n    return props;\n  };\n\n  getPlotPropsForBarChart = () => {\n    /* eslint-disable no-param-reassign */\n    const { runUuids, runDisplayNames, deselectedCurves } = this.props;\n\n    // A reverse lookup of `metricKey: { runUuid: value, metricKey }`\n    const historyByMetricKey = this.props.metrics.reduce((map, metric) => {\n      const { runUuid, metricKey, history } = metric;\n      const value = history[0] && history[0].value;\n      if (!map[metricKey]) {\n        map[metricKey] = { metricKey, [runUuid]: value };\n      } else {\n        map[metricKey][runUuid] = value;\n      }\n      return map;\n    }, {});\n\n    const arrayOfHistorySortedByMetricKey = _.sortBy(Object.values(historyByMetricKey), 'metricKey');\n\n    const sortedMetricKeys = arrayOfHistorySortedByMetricKey.map((history) => (history as any).metricKey);\n    const deselectedCurvesSet = new Set(deselectedCurves);\n    const data = runUuids.map((runUuid, i) => {\n      const visibility = deselectedCurvesSet.has(runUuid) ? { visible: 'legendonly' } : {};\n      return {\n        name: Utils.truncateString(runDisplayNames[i], MAX_RUN_NAME_DISPLAY_LENGTH),\n        x: sortedMetricKeys,\n        y: arrayOfHistorySortedByMetricKey.map((history) => (history as any)[runUuid]),\n        type: 'bar',\n        runId: runUuid,\n        ...visibility,\n      };\n    });\n\n    const layout = { barmode: 'group' };\n    const props = { data, layout };\n    props.layout = {\n      ...props.layout,\n      ...this.props.extraLayout,\n    };\n    return props;\n  };\n\n  componentDidMount() {\n    this.regenerateInfinityAnnotations();\n  }\n\n  componentDidUpdate() {\n    /**\n     * TODO: make sure that annotations are regenereated only when data changes.\n     * In fact, all internal recalculations should be done only then.\n     */\n    this.regenerateInfinityAnnotations();\n  }\n\n  render() {\n    const { onLayoutChange, onClick, onLegendClick, onLegendDoubleClick } = this.props;\n    const plotProps =\n      this.props.chartType === CHART_TYPE_BAR ? this.getPlotPropsForBarChart() : this.getPlotPropsForLineChart();\n\n    return (\n      <div className=\"metrics-plot-view-container\">\n        <LazyPlot\n          {...plotProps}\n          useResizeHandler\n          onRelayout={onLayoutChange}\n          onClick={onClick}\n          onLegendClick={onLegendClick}\n          onLegendDoubleClick={onLegendDoubleClick}\n          style={{ width: '100%', height: '100%' }}\n          layout={_.cloneDeep((plotProps as any).layout)}\n          config={{\n            displaylogo: false,\n            scrollZoom: true,\n            modeBarButtonsToRemove: ['sendDataToCloud'],\n            modeBarButtonsToAdd: [\n              {\n                name: 'Download plot data as CSV',\n                icon: DISK_ICON,\n                click: () => {\n                  const csv = convertMetricsToCsv(this.props.metrics);\n                  const blob = new Blob([csv], { type: 'application/csv;charset=utf-8' });\n                  saveAs(blob, 'metrics.csv');\n                },\n              },\n            ],\n          }}\n        />\n      </div>\n    );\n  }\n}\n\n// @ts-expect-error TS(2769): No overload matches this call.\nexport const MetricsPlotView = injectIntl(MetricsPlotViewImpl);\n", "import { Theme } from '@emotion/react';\n\nexport interface ProgressProps {\n  percent: number;\n  format: (percent: number) => string;\n  className?: string;\n}\n\n/**\n * Recreates basic features of antd's <Progress /> component.\n * Temporary solution, waiting for this component to be included in DuBois.\n */\nexport const Progress = (props: ProgressProps) => {\n  return (\n    <div css={styles.wrapper} className={props.className}>\n      <div css={styles.track}>\n        <div css={styles.progressTrack} style={{ width: `${props.percent}%` }} />\n      </div>\n      {props.format(props.percent)}\n    </div>\n  );\n};\n\nconst styles = {\n  wrapper: (theme: Theme) => ({ display: 'flex', alignItems: 'center', gap: theme.spacing.sm }),\n  track: (theme: Theme) => ({\n    backgroundColor: theme.colors.backgroundSecondary,\n    height: theme.spacing.sm,\n    flex: 1,\n    borderRadius: theme.spacing.sm,\n  }),\n  progressTrack: (theme: Theme) => ({\n    backgroundColor: theme.colors.primary,\n    height: theme.spacing.sm,\n    borderRadius: theme.spacing.sm,\n  }),\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport _ from 'lodash';\nimport { Button, LegacySelect, Switch, LegacyTooltip, Radio, QuestionMarkIcon } from '@databricks/design-system';\nimport { Progress } from '../../common/components/Progress';\nimport { CHART_TYPE_LINE, METRICS_PLOT_POLLING_INTERVAL_MS } from './MetricsPlotPanel';\n\nimport { FormattedMessage, injectIntl } from 'react-intl';\nimport { LineSmoothSlider } from './LineSmoothSlider';\n\nconst RadioGroup = Radio.Group;\nexport const X_AXIS_WALL = 'wall';\nexport const X_AXIS_STEP = 'step';\nexport const X_AXIS_RELATIVE = 'relative';\nexport const MAX_LINE_SMOOTHNESS = 100;\n\ntype Props = {\n  distinctMetricKeys: string[];\n  selectedMetricKeys: string[];\n  selectedXAxis: string;\n  handleXAxisChange: (...args: any[]) => any;\n  handleShowPointChange: (...args: any[]) => any;\n  handleMetricsSelectChange: (...args: any[]) => any;\n  handleYAxisLogScaleChange: (...args: any[]) => any;\n  handleLineSmoothChange: (value: number) => void;\n  chartType: string;\n  lineSmoothness: number;\n  yAxisLogScale: boolean;\n  showPoint: boolean;\n  intl: {\n    formatMessage: (...args: any[]) => any;\n  };\n  numRuns: number;\n  numCompletedRuns: number;\n  handleDownloadCsv: (...args: any[]) => any;\n  disableSmoothnessControl: boolean;\n};\n\nclass MetricsPlotControlsImpl extends React.Component<Props> {\n  static defaultProps = {\n    disableSmoothnessControl: false,\n  };\n\n  handleMetricsSelectFilterChange = (text: any, option: any) =>\n    option.props.title.toUpperCase().includes(text.toUpperCase());\n\n  getAllMetricKeys = () => {\n    const { distinctMetricKeys } = this.props;\n    return distinctMetricKeys.map((metricKey) => ({\n      title: metricKey,\n      value: metricKey,\n      key: metricKey,\n    }));\n  };\n\n  render() {\n    const { chartType, yAxisLogScale, lineSmoothness, showPoint, numRuns, numCompletedRuns, disableSmoothnessControl } =\n      this.props;\n\n    const lineSmoothnessTooltipText = (\n      <FormattedMessage\n        // eslint-disable-next-line max-len\n        defaultMessage='Make the line between points \"smoother\" based on Exponential Moving Average. Smoothing can be useful for displaying the overall trend when the logging frequency is high.'\n        description=\"Helpful tooltip message to help with line smoothness for the metrics plot\"\n      />\n    );\n    const completedRunsTooltipText = (\n      <FormattedMessage\n        // eslint-disable-next-line max-len\n        defaultMessage=\"MLflow UI automatically fetches metric histories for active runs and updates the metrics plot with a {interval} second interval.\"\n        description=\"Helpful tooltip message to explain the automatic metrics plot update\"\n        values={{ interval: Math.round(METRICS_PLOT_POLLING_INTERVAL_MS / 1000) }}\n      />\n    );\n    return (\n      <div\n        className=\"plot-controls\"\n        css={[styles.controlsWrapper, chartType === CHART_TYPE_LINE && styles.centeredControlsWrapper]}\n      >\n        {chartType === CHART_TYPE_LINE ? (\n          <div>\n            <div className=\"inline-control\">\n              <div className=\"control-label\">\n                <FormattedMessage\n                  defaultMessage=\"Completed Runs\"\n                  description=\"Label for the progress bar to show the number of completed runs\"\n                />{' '}\n                <LegacyTooltip title={completedRunsTooltipText}>\n                  <QuestionMarkIcon />\n                </LegacyTooltip>\n                <Progress\n                  percent={Math.round((100 * numCompletedRuns) / numRuns)}\n                  format={() => `${numCompletedRuns}/${numRuns}`}\n                />\n              </div>\n            </div>\n            <div className=\"inline-control\">\n              <div className=\"control-label\">\n                <FormattedMessage\n                  defaultMessage=\"Points:\"\n                  // eslint-disable-next-line max-len\n                  description=\"Label for the toggle button to toggle to show points or not for the metric experiment run\"\n                />\n              </div>\n              <Switch\n                componentId=\"codegen_mlflow_app_src_experiment-tracking_components_metricsplotcontrols.tsx_120\"\n                data-testid=\"show-point-toggle\"\n                defaultChecked={showPoint}\n                onChange={this.props.handleShowPointChange}\n              />\n            </div>\n            {!disableSmoothnessControl && (\n              <div className=\"block-control\">\n                <div className=\"control-label\">\n                  <FormattedMessage\n                    defaultMessage=\"Line Smoothness\"\n                    description=\"Label for the smoothness slider for the graph plot for metrics\"\n                  />{' '}\n                  <LegacyTooltip title={lineSmoothnessTooltipText}>\n                    <QuestionMarkIcon />\n                  </LegacyTooltip>\n                </div>\n                <LineSmoothSlider\n                  data-testid=\"smoothness-toggle\"\n                  min={1}\n                  max={MAX_LINE_SMOOTHNESS}\n                  onChange={this.props.handleLineSmoothChange}\n                  value={lineSmoothness}\n                />\n              </div>\n            )}\n            <div className=\"block-control\">\n              <div className=\"control-label\">\n                <FormattedMessage\n                  defaultMessage=\"X-axis:\"\n                  // eslint-disable-next-line max-len\n                  description=\"Label for the radio button to toggle the control on the X-axis of the metric graph for the experiment\"\n                />\n              </div>\n              <RadioGroup\n                componentId=\"codegen_mlflow_app_src_experiment-tracking_components_metricsplotcontrols.tsx_154\"\n                name=\"metrics-plot-x-axis-radio-group\"\n                css={styles.xAxisControls}\n                onChange={this.props.handleXAxisChange}\n                value={this.props.selectedXAxis}\n              >\n                <Radio value={X_AXIS_STEP} data-testid=\"x-axis-radio\">\n                  <FormattedMessage\n                    defaultMessage=\"Step\"\n                    // eslint-disable-next-line max-len\n                    description=\"Radio button option to choose the step control option for the X-axis for metric graph on the experiment runs\"\n                  />\n                </Radio>\n                <Radio value={X_AXIS_WALL} data-testid=\"x-axis-radio\">\n                  <FormattedMessage\n                    defaultMessage=\"Time (Wall)\"\n                    // eslint-disable-next-line max-len\n                    description=\"Radio button option to choose the time wall control option for the X-axis for metric graph on the experiment runs\"\n                  />\n                </Radio>\n                <Radio value={X_AXIS_RELATIVE} data-testid=\"x-axis-radio\">\n                  <FormattedMessage\n                    defaultMessage=\"Time (Relative)\"\n                    // eslint-disable-next-line max-len\n                    description=\"Radio button option to choose the time relative control option for the X-axis for metric graph on the experiment runs\"\n                  />\n                </Radio>\n              </RadioGroup>\n            </div>\n          </div>\n        ) : null}\n        <div className=\"block-control\">\n          <div className=\"control-label\">\n            <FormattedMessage\n              defaultMessage=\"Y-axis:\"\n              // eslint-disable-next-line max-len\n              description=\"Label where the users can choose the metric of the experiment run to be plotted on the Y-axis\"\n            />\n          </div>\n          <LegacySelect\n            placeholder={this.props.intl.formatMessage({\n              defaultMessage: 'Please select metric',\n              description:\n                // eslint-disable-next-line max-len\n                'Placeholder text where one can select metrics from the list of available metrics to render on the graph',\n            })}\n            value={this.props.selectedMetricKeys}\n            onChange={this.props.handleMetricsSelectChange}\n            mode=\"multiple\"\n            css={styles.axisSelector}\n          >\n            {this.getAllMetricKeys().map((key) => (\n              <LegacySelect.Option value={key.value} key={key.key}>\n                {key.title}\n              </LegacySelect.Option>\n            ))}\n          </LegacySelect>\n        </div>\n        <div className=\"inline-control\">\n          <div className=\"control-label\">\n            <FormattedMessage\n              defaultMessage=\"Y-axis Log Scale:\"\n              // eslint-disable-next-line max-len\n              description=\"Label for the radio button to toggle the Log scale on the Y-axis of the metric graph for the experiment\"\n            />\n          </div>\n          <Switch\n            componentId=\"codegen_mlflow_app_src_experiment-tracking_components_metricsplotcontrols.tsx_220\"\n            defaultChecked={yAxisLogScale}\n            onChange={this.props.handleYAxisLogScaleChange}\n          />\n        </div>\n        <div className=\"inline-control\">\n          <Button\n            componentId=\"codegen_mlflow_app_src_experiment-tracking_components_metricsplotcontrols.tsx_222\"\n            css={{\n              textAlign: 'justify',\n              textAlignLast: 'left',\n            }}\n            onClick={this.props.handleDownloadCsv}\n          >\n            <FormattedMessage\n              defaultMessage=\"Download data\"\n              // eslint-disable-next-line max-len\n              description=\"String for the download csv button to download metrics from this run offline in a CSV format\"\n            />\n            <i className=\"fas fa-download\" />\n          </Button>\n        </div>\n      </div>\n    );\n  }\n}\n\nconst styles = {\n  xAxisControls: (theme: any) => ({\n    label: { marginTop: theme.spacing.xs, marginBottom: theme.spacing.xs },\n  }),\n  controlsWrapper: { minWidth: '20%', maxWidth: '30%' },\n  axisSelector: { width: '100%' },\n  centeredControlsWrapper: {\n    // Make controls aligned to plotly line chart\n    justifyContent: 'center',\n  },\n};\n\n// @ts-expect-error TS(2769): No overload matches this call.\nexport const MetricsPlotControls = injectIntl(MetricsPlotControlsImpl);\n"], "names": ["HtmlTableView", "_ref", "columns", "values", "styles", "testId", "scroll", "_jsx", "LegacyTable", "className", "dataSource", "size", "pagination", "style", "MetricsSummaryTable", "React", "render", "runUuids", "this", "props", "children", "length", "renderMetricTables", "renderRunTable", "runUuid", "metricKeys", "latestMetrics", "minMetrics", "maxMetrics", "intl", "title", "formatMessage", "id", "defaultMessage", "dataIndex", "sorter", "a", "b", "metricKey", "width", "dataColumns", "getRunValuesByMetric", "y", "runExperimentIds", "runDisplayNames", "runName", "map", "_jsxs", "Fragment", "getMetricValuesByRun", "latestValue", "ellipsis", "minValue", "maxValue", "runIdx", "runLink", "Link", "to", "Routes", "getRunPageRoute", "key", "rowData", "name", "_ref2", "_ref3", "latestMetric", "getMetric", "minMetric", "maxMetric", "getValue", "latestFormatted", "css", "formatMetric", "minFormatted", "maxFormatted", "valuesMap", "metric", "value", "undefined", "step", "MetricsSummaryTableWithIntl", "injectIntl", "connect", "mapStateToProps", "state", "ownProps", "for<PERSON>ach", "runInfo", "getRunInfo", "experimentId", "getLatestMetrics", "getMinMetrics", "getMaxMetrics", "IconButton", "icon", "onClick", "restProps", "<PERSON><PERSON>", "componentId", "type", "padding", "RunLinksPopover", "constructor", "arguments", "renderContent", "runItems", "index", "runId", "color", "marginRight", "Utils", "renderTitle", "handleClose", "float", "marginLeft", "componentDidMount", "document", "addEventListener", "handleKeyDown", "componentWillUnmount", "removeEventListener", "visible", "x", "handleVisibleChange", "LegacyPopover", "content", "placement", "onVisibleChange", "left", "top", "position", "CHART_TYPE_LINE", "CHART_TYPE_BAR", "METRICS_PLOT_POLLING_INTERVAL_MS", "MAXIMUM_METRIC_DATA_POINTS", "convertMetricsToCsv", "metrics", "header", "Object", "keys", "history", "rows", "flatMap", "concat", "row", "join", "MetricsPlotPanel", "super", "_isMounted", "displayPopover", "intervalId", "legendClickTimeout", "prevLegendClickTime", "Math", "inf", "lastClickedLegendCurveId", "MAX_DOUBLE_CLICK_INTERVAL_MS", "SINGLE_CLICK_EVENT_DELAY_MS", "onFocus", "setState", "focused", "onBlur", "clearEventListeners", "window", "clearInterval", "allRunsCompleted", "completedRunUuids", "isHangingRunUuid", "activeRunUuid", "latestMetricsByRunUuid", "timestamps", "timestamp", "latestTimestamp", "max", "Date", "getTime", "getActiveRunUuids", "_", "filter", "shouldPoll", "updateUrlState", "updatedState", "location", "navigate", "experimentIds", "JSON", "parse", "qs", "search", "newState", "getUrlState", "selectedXAxis", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showPoint", "yAxisLogScale", "lineSmoothness", "layout", "deselectedCurves", "lastLinearYAxisRange", "getMetricPageRoute", "replace", "getNumTotalMetrics", "metricsWithRunInfoAndHistory", "reduce", "loadMetricHistory", "logErrorAndNotifyUser", "Promise", "resolve", "loading", "promises", "_ref4", "hasOwnProperty", "async", "_ref5", "requestIds", "getUUID", "push", "nextPageToken", "getMetricHistoryApi", "next_page_token", "success", "uid", "all", "then", "results", "every", "_ref6", "_ref7", "loadRuns", "getRunApi", "getMetrics", "selectedMetricsSet", "Set", "m", "has", "isStep", "X_AXIS_STEP", "sort", "compareByStepAndTimestamp", "compareByTimestamp", "handleYAxisLogScaleChange", "newLayout", "newAxisType", "yaxis", "range", "autorange", "exponentformat", "oldYRange", "log", "pow", "handleXAxisChange", "e", "axisType", "X_AXIS_WALL", "X_AXIS_RELATIVE", "target", "xaxis", "handleLayoutChange", "newXRange0", "newXRange1", "newYRange0", "newYRange1", "xAxisAutorange", "yAxisAutorange", "yAxisShowSpikes", "xAxisShowSpikes", "restFields", "mergedLayout", "newXAxis", "showspikes", "newYAxis", "handleDownloadCsv", "csv", "blob", "Blob", "saveAs", "handleLegendClick", "_ref8", "curveNumber", "data", "currentTime", "now", "handleLegendDoubleClick", "curve<PERSON>ey", "getCurveKey", "setTimeout", "existingDeselectedCurves", "delete", "add", "Array", "from", "_ref9", "clearTimeout", "newDeselectedCurves", "elem", "curvePair", "handleMetricsSelectChange", "existingMetricKeys", "newMetricKeys", "k", "includes", "prevState", "historyRequestIds", "handleShowPointChange", "handleLineSmoothChange", "handleKeyDownOnPopover", "_ref0", "popoverVisible", "updatePopover", "popoverX", "popoverY", "points", "event", "clientX", "clientY", "samePointClicked", "point", "fullData", "marker", "popoverRunItems", "hasMultipleExperiments", "setInterval", "activeRunUuids", "getMetricPlotStateFromUrl", "predictChartType", "isComparing", "params", "runs", "getAxisType", "plotDataElem", "metricName", "distinctMetricKeys", "chartType", "MetricsPlotControls", "numRuns", "numCompletedRuns", "disableSmoothnessControl", "containsInfinities", "RequestStateWrapper", "shouldOptimisticallyRender", "Spinner", "_css", "visibility", "MetricsPlotView", "xAxis", "extraLayout", "onLayoutChange", "onLegendClick", "onLegendDoubleClick", "defaultProps", "mapDispatchToProps", "withRouterNext", "status", "metricsByRunUuid", "entities", "runDisplayName", "getRunDisplayName", "metricsHistory", "entry", "normalizeMetricsHistoryEntry", "some", "_ref1", "isNaN", "isFinite", "getClosestValue", "marks", "defaultValue", "prev", "curr", "abs", "Number", "getNextValue", "currentValue", "direction", "mark", "min", "LineSmoothSlider", "onChange", "disabled", "onAfterChange", "theme", "useDesignSystemTheme", "shouldUseMarks", "isEmpty", "shouldDisplayMarks", "temporaryValue", "setTemporaryValue", "useState", "display", "height", "general", "heightSm", "gap", "spacing", "md", "alignItems", "Slide<PERSON>", "Root", "flex", "zIndex", "onValueCommit", "newValue", "onKeyDown", "preventDefault", "nextValue", "onValueChange", "inset", "markPosition", "right", "bottom", "marginTop", "pointerEvents", "borderRadius", "backgroundColor", "colors", "actionPrimaryBackgroundDefault", "opacity", "Track", "Range", "Thumb", "Input", "isUndefined", "clamp", "nativeEvent", "InputEvent", "Plot", "LazyPlot", "fallback", "SectionErrorBoundary", "LegacySkeleton", "active", "EMA", "m<PERSON>rray", "smoothingWeight", "v", "smoothness", "MAX_LINE_SMOOTHNESS", "smoothedArray", "biasedElement", "i", "debiasedElement", "DISK_ICON", "path", "transform", "_annotationData", "_classPrivateFieldLooseKey", "MetricsPlotViewImpl", "regenerateInfinityAnnotations", "_extraLayout$yaxis", "isYAxisLog", "annotationData", "generateInfinityAnnotations", "xValues", "getXValuesForLineChart", "yV<PERSON><PERSON>", "isLogScale", "string<PERSON><PERSON><PERSON>er", "_classPrivateFieldLooseBase", "defineProperty", "writable", "getPlotPropsForLineChart", "deselectedCurvesSet", "shapes", "annotations", "historyValues", "isSingleHistory", "getLineLegend", "NaN", "text", "toFixed", "mode", "hovertemplate", "getPlotPropsForBarChart", "historyByMetricKey", "arrayOfHistorySortedByMetricKey", "sortedMetricKeys", "truncateString", "barmode", "xAxisType", "minTimestamp", "componentDidUpdate", "plotProps", "useResizeHandler", "onRelayout", "config", "displaylogo", "scrollZoom", "modeBarButtonsToRemove", "modeBarButtonsToAdd", "click", "legend", "Progress", "wrapper", "track", "progressTrack", "percent", "format", "sm", "backgroundSecondary", "primary", "RadioGroup", "Radio", "Group", "MetricsPlotControlsImpl", "handleMetricsSelectFilterChange", "option", "toUpperCase", "getAllMetricKeys", "lineSmoothnessTooltipText", "FormattedMessage", "completedRunsTooltipText", "interval", "round", "controlsWrapper", "centeredControlsWrapper", "LegacyTooltip", "QuestionMarkIcon", "Switch", "defaultChecked", "xAxisControls", "LegacySelect", "placeholder", "axisSelector", "Option", "label", "xs", "marginBottom", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "justifyContent"], "sourceRoot": ""}