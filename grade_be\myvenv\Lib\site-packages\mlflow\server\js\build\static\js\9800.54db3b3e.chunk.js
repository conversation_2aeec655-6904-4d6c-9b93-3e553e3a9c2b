(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[9800],{2340:function(){},27793:function(t,e,n){var r,i;i=window,function(){if(!i.requestAnimationFrame){if(i.webkitRequestAnimationFrame)return i.requestAnimationFrame=i.webkitRequestAnimationFrame,void(i.cancelAnimationFrame=i.webkitCancelAnimationFrame||i.webkitCancelRequestAnimationFrame);var t=0;i.requestAnimationFrame=function(e){var n=(new Date).getTime(),r=Math.max(0,16-(n-t)),a=i.setTimeout((function(){e(n+r)}),r);return t=n+r,a},i.cancelAnimationFrame=function(t){clearTimeout(t)}}}(),void 0===(r=function(){return i.requestAnimationFrame}.call(e,n,e,t))||(t.exports=r)},44520:function(t,e,n){"use strict";n.d(e,{A:function(){return ia}});n(27793);function r(){}function i(t){return null==t?r:function(){return this.querySelector(t)}}function a(){return[]}function s(t){return null==t?a:function(){return this.querySelectorAll(t)}}function o(t){return function(){return this.matches(t)}}function u(t){return new Array(t.length)}function l(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}l.prototype={constructor:l,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};function c(t,e,n,r,i,a){for(var s,o=0,u=e.length,c=a.length;o<c;++o)(s=e[o])?(s.__data__=a[o],r[o]=s):n[o]=new l(t,a[o]);for(;o<u;++o)(s=e[o])&&(i[o]=s)}function h(t,e,n,r,i,a,s){var o,u,c,h={},f=e.length,d=a.length,m=new Array(f);for(o=0;o<f;++o)(u=e[o])&&(m[o]=c="$"+s.call(u,u.__data__,o,e),c in h?i[o]=u:h[c]=u);for(o=0;o<d;++o)(u=h[c="$"+s.call(t,a[o],o,a)])?(r[o]=u,u.__data__=a[o],h[c]=null):n[o]=new l(t,a[o]);for(o=0;o<f;++o)(u=e[o])&&h[m[o]]===u&&(i[o]=u)}function f(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}var d="http://www.w3.org/1999/xhtml",m={svg:"http://www.w3.org/2000/svg",xhtml:d,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function p(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),m.hasOwnProperty(e)?{space:m[e],local:t}:t}function g(t){return function(){this.removeAttribute(t)}}function v(t){return function(){this.removeAttributeNS(t.space,t.local)}}function y(t,e){return function(){this.setAttribute(t,e)}}function b(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function w(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}function x(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}function _(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function M(t){return function(){this.style.removeProperty(t)}}function A(t,e,n){return function(){this.style.setProperty(t,e,n)}}function O(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}function N(t,e){return t.style.getPropertyValue(e)||_(t).getComputedStyle(t,null).getPropertyValue(e)}function E(t){return function(){delete this[t]}}function S(t,e){return function(){this[t]=e}}function P(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}function T(t){return t.trim().split(/^|\s+/)}function D(t){return t.classList||new R(t)}function R(t){this._node=t,this._names=T(t.getAttribute("class")||"")}function j(t,e){for(var n=D(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function C(t,e){for(var n=D(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function q(t){return function(){j(this,t)}}function I(t){return function(){C(this,t)}}function $(t,e){return function(){(e.apply(this,arguments)?j:C)(this,t)}}function z(){this.textContent=""}function X(t){return function(){this.textContent=t}}function B(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}function V(){this.innerHTML=""}function L(t){return function(){this.innerHTML=t}}function F(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}function Y(){this.nextSibling&&this.parentNode.appendChild(this)}function H(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function K(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===d&&e.documentElement.namespaceURI===d?e.createElement(t):e.createElementNS(n,t)}}function U(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function W(t){var e=p(t);return(e.local?U:K)(e)}function G(){return null}function Z(){var t=this.parentNode;t&&t.removeChild(this)}function J(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function Q(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}R.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var tt={},et=null;"undefined"!==typeof document&&("onmouseenter"in document.documentElement||(tt={mouseenter:"mouseover",mouseleave:"mouseout"}));function nt(t,e,n){return t=rt(t,e,n),function(e){var n=e.relatedTarget;n&&(n===this||8&n.compareDocumentPosition(this))||t.call(this,e)}}function rt(t,e,n){return function(r){var i=et;et=r;try{t.call(this,this.__data__,e,n)}finally{et=i}}}function it(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,a=e.length;r<a;++r)n=e[r],t.type&&n.type!==t.type||n.name!==t.name?e[++i]=n:this.removeEventListener(n.type,n.listener,n.capture);++i?e.length=i:delete this.__on}}}function at(t,e,n){var r=tt.hasOwnProperty(t.type)?nt:rt;return function(i,a,s){var o,u=this.__on,l=r(e,a,s);if(u)for(var c=0,h=u.length;c<h;++c)if((o=u[c]).type===t.type&&o.name===t.name)return this.removeEventListener(o.type,o.listener,o.capture),this.addEventListener(o.type,o.listener=l,o.capture=n),void(o.value=e);this.addEventListener(t.type,l,n),o={type:t.type,name:t.name,value:e,listener:l,capture:n},u?u.push(o):this.__on=[o]}}function st(t,e,n,r){var i=et;t.sourceEvent=et,et=t;try{return e.apply(n,r)}finally{et=i}}function ot(t,e,n){var r=_(t),i=r.CustomEvent;"function"===typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}function ut(t,e){return function(){return ot(this,t,e)}}function lt(t,e){return function(){return ot(this,t,e.apply(this,arguments))}}var ct=[null];function ht(t,e){this._groups=t,this._parents=e}function ft(){return new ht([[document.documentElement]],ct)}ht.prototype=ft.prototype={constructor:ht,select:function(t){"function"!==typeof t&&(t=i(t));for(var e=this._groups,n=e.length,r=new Array(n),a=0;a<n;++a)for(var s,o,u=e[a],l=u.length,c=r[a]=new Array(l),h=0;h<l;++h)(s=u[h])&&(o=t.call(s,s.__data__,h,u))&&("__data__"in s&&(o.__data__=s.__data__),c[h]=o);return new ht(r,this._parents)},selectAll:function(t){"function"!==typeof t&&(t=s(t));for(var e=this._groups,n=e.length,r=[],i=[],a=0;a<n;++a)for(var o,u=e[a],l=u.length,c=0;c<l;++c)(o=u[c])&&(r.push(t.call(o,o.__data__,c,u)),i.push(o));return new ht(r,i)},filter:function(t){"function"!==typeof t&&(t=o(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var a,s=e[i],u=s.length,l=r[i]=[],c=0;c<u;++c)(a=s[c])&&t.call(a,a.__data__,c,s)&&l.push(a);return new ht(r,this._parents)},data:function(t,e){if(!t)return g=new Array(this.size()),f=-1,this.each((function(t){g[++f]=t})),g;var n,r=e?h:c,i=this._parents,a=this._groups;"function"!==typeof t&&(n=t,t=function(){return n});for(var s=a.length,o=new Array(s),u=new Array(s),l=new Array(s),f=0;f<s;++f){var d=i[f],m=a[f],p=m.length,g=t.call(d,d&&d.__data__,f,i),v=g.length,y=u[f]=new Array(v),b=o[f]=new Array(v);r(d,m,y,b,l[f]=new Array(p),g,e);for(var w,x,_=0,k=0;_<v;++_)if(w=y[_]){for(_>=k&&(k=_+1);!(x=b[k])&&++k<v;);w._next=x||null}}return(o=new ht(o,i))._enter=u,o._exit=l,o},enter:function(){return new ht(this._enter||this._groups.map(u),this._parents)},exit:function(){return new ht(this._exit||this._groups.map(u),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,a=this.exit();return r="function"===typeof t?t(r):r.append(t+""),null!=e&&(i=e(i)),null==n?a.remove():n(a),r&&i?r.merge(i).order():i},merge:function(t){for(var e=this._groups,n=t._groups,r=e.length,i=n.length,a=Math.min(r,i),s=new Array(r),o=0;o<a;++o)for(var u,l=e[o],c=n[o],h=l.length,f=s[o]=new Array(h),d=0;d<h;++d)(u=l[d]||c[d])&&(f[d]=u);for(;o<r;++o)s[o]=e[o];return new ht(s,this._parents)},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],a=i.length-1,s=i[a];--a>=0;)(r=i[a])&&(s&&4^r.compareDocumentPosition(s)&&s.parentNode.insertBefore(r,s),s=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=f);for(var n=this._groups,r=n.length,i=new Array(r),a=0;a<r;++a){for(var s,o=n[a],u=o.length,l=i[a]=new Array(u),c=0;c<u;++c)(s=o[c])&&(l[c]=s);l.sort(e)}return new ht(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){var t=new Array(this.size()),e=-1;return this.each((function(){t[++e]=this})),t},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,a=r.length;i<a;++i){var s=r[i];if(s)return s}return null},size:function(){var t=0;return this.each((function(){++t})),t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,a=e[n],s=0,o=a.length;s<o;++s)(i=a[s])&&t.call(i,i.__data__,s,a);return this},attr:function(t,e){var n=p(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?v:g:"function"===typeof e?n.local?x:w:n.local?b:y)(n,e))},style:function(t,e,n){return arguments.length>1?this.each((null==e?M:"function"===typeof e?O:A)(t,e,null==n?"":n)):N(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?E:"function"===typeof e?P:S)(t,e)):this.node()[t]},classed:function(t,e){var n=T(t+"");if(arguments.length<2){for(var r=D(this.node()),i=-1,a=n.length;++i<a;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"===typeof e?$:e?q:I)(n,e))},text:function(t){return arguments.length?this.each(null==t?z:("function"===typeof t?B:X)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?V:("function"===typeof t?F:L)(t)):this.node().innerHTML},raise:function(){return this.each(Y)},lower:function(){return this.each(H)},append:function(t){var e="function"===typeof t?t:W(t);return this.select((function(){return this.appendChild(e.apply(this,arguments))}))},insert:function(t,e){var n="function"===typeof t?t:W(t),r=null==e?G:"function"===typeof e?e:i(e);return this.select((function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)}))},remove:function(){return this.each(Z)},clone:function(t){return this.select(t?Q:J)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,a=function(t){return t.trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}))}(t+""),s=a.length;if(!(arguments.length<2)){for(o=e?at:it,null==n&&(n=!1),r=0;r<s;++r)this.each(o(a[r],e,n));return this}var o=this.node().__on;if(o)for(var u,l=0,c=o.length;l<c;++l)for(r=0,u=o[l];r<s;++r)if((i=a[r]).type===u.type&&i.name===u.name)return u.value},dispatch:function(t,e){return this.each(("function"===typeof e?lt:ut)(t,e))}};var dt=ft;function mt(t){return"string"===typeof t?new ht([[document.querySelector(t)]],[document.documentElement]):new ht([[t]],ct)}function pt(){for(var t,e=et;t=e.sourceEvent;)e=t;return e}function gt(t,e){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}var i=t.getBoundingClientRect();return[e.clientX-i.left-t.clientLeft,e.clientY-i.top-t.clientTop]}function vt(t){var e=pt();return e.changedTouches&&(e=e.changedTouches[0]),gt(t,e)}function yt(t){return"string"===typeof t?new ht([document.querySelectorAll(t)],[document.documentElement]):new ht([null==t?[]:t],ct)}var bt=n(67986);function wt(){et.stopImmediatePropagation()}function xt(){et.preventDefault(),et.stopImmediatePropagation()}function _t(t){var e=t.document.documentElement,n=mt(t).on("dragstart.drag",xt,!0);"onselectstart"in e?n.on("selectstart.drag",xt,!0):(e.__noselect=e.style.MozUserSelect,e.style.MozUserSelect="none")}function kt(t,e){var n=t.document.documentElement,r=mt(t).on("dragstart.drag",null);e&&(r.on("click.drag",xt,!0),setTimeout((function(){r.on("click.drag",null)}),0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var Mt=n(93004);function At(t,e,n){arguments.length<3&&(n=e,e=pt().changedTouches);for(var r,i=0,a=e?e.length:0;i<a;++i)if((r=e[i]).identifier===n)return gt(t,r);return null}var Ot={value:()=>{}};function Nt(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new Et(r)}function Et(t){this._=t}function St(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}function Pt(t,e,n){for(var r=0,i=t.length;r<i;++r)if(t[r].name===e){t[r]=Ot,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=n&&t.push({name:e,value:n}),t}Et.prototype=Nt.prototype={constructor:Et,on:function(t,e){var n,r,i=this._,a=(r=i,(t+"").trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))),s=-1,o=a.length;if(!(arguments.length<2)){if(null!=e&&"function"!==typeof e)throw new Error("invalid callback: "+e);for(;++s<o;)if(n=(t=a[s]).type)i[n]=Pt(i[n],t.name,e);else if(null==e)for(n in i)i[n]=Pt(i[n],t.name,null);return this}for(;++s<o;)if((n=(t=a[s]).type)&&(n=St(i[n],t.name)))return n},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new Et(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=new Array(n),a=0;a<n;++a)i[a]=arguments[a+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=0,n=(r=this._[t]).length;a<n;++a)r[a].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,a=r.length;i<a;++i)r[i].value.apply(e,n)}};var Tt,Dt,Rt=Nt,jt=0,Ct=0,qt=0,It=0,$t=0,zt=0,Xt="object"===typeof performance&&performance.now?performance:Date,Bt="object"===typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Vt(){return $t||(Bt(Lt),$t=Xt.now()+zt)}function Lt(){$t=0}function Ft(){this._call=this._time=this._next=null}function Yt(t,e,n){var r=new Ft;return r.restart(t,e,n),r}function Ht(){$t=(It=Xt.now())+zt,jt=Ct=0;try{!function(){Vt(),++jt;for(var t,e=Tt;e;)(t=$t-e._time)>=0&&e._call.call(void 0,t),e=e._next;--jt}()}finally{jt=0,function(){var t,e,n=Tt,r=1/0;for(;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:Tt=e);Dt=t,Ut(r)}(),$t=0}}function Kt(){var t=Xt.now(),e=t-It;e>1e3&&(zt-=e,It=t)}function Ut(t){jt||(Ct&&(Ct=clearTimeout(Ct)),t-$t>24?(t<1/0&&(Ct=setTimeout(Ht,t-Xt.now()-zt)),qt&&(qt=clearInterval(qt))):(qt||(It=Xt.now(),qt=setInterval(Kt,1e3)),jt=1,Bt(Ht)))}function Wt(t,e,n){var r=new Ft;return e=null==e?0:+e,r.restart((n=>{r.stop(),t(n+e)}),e,n),r}Ft.prototype=Yt.prototype={constructor:Ft,restart:function(t,e,n){if("function"!==typeof t)throw new TypeError("callback is not a function");n=(null==n?Vt():+n)+(null==e?0:+e),this._next||Dt===this||(Dt?Dt._next=this:Tt=this,Dt=this),this._call=t,this._time=n,Ut()},stop:function(){this._call&&(this._call=null,this._time=1/0,Ut())}};var Gt=Rt("start","end","cancel","interrupt"),Zt=[];function Jt(t,e,n,r,i,a){var s=t.__transition;if(s){if(n in s)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function a(t){n.state=1,n.timer.restart(s,n.delay,n.time),n.delay<=t&&s(t-n.delay)}function s(a){var l,c,h,f;if(1!==n.state)return u();for(l in i)if((f=i[l]).name===n.name){if(3===f.state)return Wt(s);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",t,t.__data__,f.index,f.group),delete i[l]):+l<e&&(f.state=6,f.timer.stop(),f.on.call("cancel",t,t.__data__,f.index,f.group),delete i[l])}if(Wt((function(){3===n.state&&(n.state=4,n.timer.restart(o,n.delay,n.time),o(a))})),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(n.state=3,r=new Array(h=n.tween.length),l=0,c=-1;l<h;++l)(f=n.tween[l].value.call(t,t.__data__,n.index,n.group))&&(r[++c]=f);r.length=c+1}}function o(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(u),n.state=5,1),a=-1,s=r.length;++a<s;)r[a].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),u())}function u(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=Yt(a,0,n.time)}(t,n,{name:e,index:r,group:i,on:Gt,tween:Zt,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:0})}function Qt(t,e){var n=ee(t,e);if(n.state>0)throw new Error("too late; already scheduled");return n}function te(t,e){var n=ee(t,e);if(n.state>3)throw new Error("too late; already running");return n}function ee(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw new Error("transition not found");return n}function ne(t,e){var n,r,i,a=t.__transition,s=!0;if(a){for(i in e=null==e?null:e+"",a)(n=a[i]).name===e?(r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete a[i]):s=!1;s&&delete t.__transition}}function re(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}var ie,ae=180/Math.PI,se={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function oe(t,e,n,r,i,a){var s,o,u;return(s=Math.sqrt(t*t+e*e))&&(t/=s,e/=s),(u=t*n+e*r)&&(n-=t*u,r-=e*u),(o=Math.sqrt(n*n+r*r))&&(n/=o,r/=o,u/=o),t*r<e*n&&(t=-t,e=-e,u=-u,s=-s),{translateX:i,translateY:a,rotate:Math.atan2(e,t)*ae,skewX:Math.atan(u)*ae,scaleX:s,scaleY:o}}function ue(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(a,s){var o=[],u=[];return a=t(a),s=t(s),function(t,r,i,a,s,o){if(t!==i||r!==a){var u=s.push("translate(",null,e,null,n);o.push({i:u-4,x:re(t,i)},{i:u-2,x:re(r,a)})}else(i||a)&&s.push("translate("+i+e+a+n)}(a.translateX,a.translateY,s.translateX,s.translateY,o,u),function(t,e,n,a){t!==e?(t-e>180?e+=360:e-t>180&&(t+=360),a.push({i:n.push(i(n)+"rotate(",null,r)-2,x:re(t,e)})):e&&n.push(i(n)+"rotate("+e+r)}(a.rotate,s.rotate,o,u),function(t,e,n,a){t!==e?a.push({i:n.push(i(n)+"skewX(",null,r)-2,x:re(t,e)}):e&&n.push(i(n)+"skewX("+e+r)}(a.skewX,s.skewX,o,u),function(t,e,n,r,a,s){if(t!==n||e!==r){var o=a.push(i(a)+"scale(",null,",",null,")");s.push({i:o-4,x:re(t,n)},{i:o-2,x:re(e,r)})}else 1===n&&1===r||a.push(i(a)+"scale("+n+","+r+")")}(a.scaleX,a.scaleY,s.scaleX,s.scaleY,o,u),a=s=null,function(t){for(var e,n=-1,r=u.length;++n<r;)o[(e=u[n]).i]=e.x(t);return o.join("")}}}var le=ue((function(t){const e=new("function"===typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?se:oe(e.a,e.b,e.c,e.d,e.e,e.f)}),"px, ","px)","deg)"),ce=ue((function(t){return null==t?se:(ie||(ie=document.createElementNS("http://www.w3.org/2000/svg","g")),ie.setAttribute("transform",t),(t=ie.transform.baseVal.consolidate())?oe((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):se)}),", ",")",")");function he(t,e){var n,r;return function(){var i=te(this,t),a=i.tween;if(a!==n)for(var s=0,o=(r=n=a).length;s<o;++s)if(r[s].name===e){(r=r.slice()).splice(s,1);break}i.tween=r}}function fe(t,e,n){var r,i;if("function"!==typeof n)throw new Error;return function(){var a=te(this,t),s=a.tween;if(s!==r){i=(r=s).slice();for(var o={name:e,value:n},u=0,l=i.length;u<l;++u)if(i[u].name===e){i[u]=o;break}u===l&&i.push(o)}a.tween=i}}function de(t,e,n){var r=t._id;return t.each((function(){var t=te(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)})),function(t){return ee(t,r).value[e]}}function me(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function pe(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function ge(){}var ve=.7,ye=1/ve,be="\\s*([+-]?\\d+)\\s*",we="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",xe="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",_e=/^#([0-9a-f]{3,8})$/,ke=new RegExp(`^rgb\\(${be},${be},${be}\\)$`),Me=new RegExp(`^rgb\\(${xe},${xe},${xe}\\)$`),Ae=new RegExp(`^rgba\\(${be},${be},${be},${we}\\)$`),Oe=new RegExp(`^rgba\\(${xe},${xe},${xe},${we}\\)$`),Ne=new RegExp(`^hsl\\(${we},${xe},${xe}\\)$`),Ee=new RegExp(`^hsla\\(${we},${xe},${xe},${we}\\)$`),Se={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Pe(){return this.rgb().formatHex()}function Te(){return this.rgb().formatRgb()}function De(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=_e.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?Re(e):3===n?new qe(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?je(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?je(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=ke.exec(t))?new qe(e[1],e[2],e[3],1):(e=Me.exec(t))?new qe(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=Ae.exec(t))?je(e[1],e[2],e[3],e[4]):(e=Oe.exec(t))?je(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=Ne.exec(t))?Ve(e[1],e[2]/100,e[3]/100,1):(e=Ee.exec(t))?Ve(e[1],e[2]/100,e[3]/100,e[4]):Se.hasOwnProperty(t)?Re(Se[t]):"transparent"===t?new qe(NaN,NaN,NaN,0):null}function Re(t){return new qe(t>>16&255,t>>8&255,255&t,1)}function je(t,e,n,r){return r<=0&&(t=e=n=NaN),new qe(t,e,n,r)}function Ce(t,e,n,r){return 1===arguments.length?((i=t)instanceof ge||(i=De(i)),i?new qe((i=i.rgb()).r,i.g,i.b,i.opacity):new qe):new qe(t,e,n,null==r?1:r);var i}function qe(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function Ie(){return`#${Be(this.r)}${Be(this.g)}${Be(this.b)}`}function $e(){const t=ze(this.opacity);return`${1===t?"rgb(":"rgba("}${Xe(this.r)}, ${Xe(this.g)}, ${Xe(this.b)}${1===t?")":`, ${t})`}`}function ze(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Xe(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Be(t){return((t=Xe(t))<16?"0":"")+t.toString(16)}function Ve(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new Fe(t,e,n,r)}function Le(t){if(t instanceof Fe)return new Fe(t.h,t.s,t.l,t.opacity);if(t instanceof ge||(t=De(t)),!t)return new Fe;if(t instanceof Fe)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),a=Math.max(e,n,r),s=NaN,o=a-i,u=(a+i)/2;return o?(s=e===a?(n-r)/o+6*(n<r):n===a?(r-e)/o+2:(e-n)/o+4,o/=u<.5?a+i:2-a-i,s*=60):o=u>0&&u<1?0:s,new Fe(s,o,u,t.opacity)}function Fe(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function Ye(t){return(t=(t||0)%360)<0?t+360:t}function He(t){return Math.max(0,Math.min(1,t||0))}function Ke(t,e,n){return 255*(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)}function Ue(t,e,n,r,i){var a=t*t,s=a*t;return((1-3*t+3*a-s)*e+(4-6*a+3*s)*n+(1+3*t+3*a-3*s)*r+s*i)/6}me(ge,De,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Pe,formatHex:Pe,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Le(this).formatHsl()},formatRgb:Te,toString:Te}),me(qe,Ce,pe(ge,{brighter(t){return t=null==t?ye:Math.pow(ye,t),new qe(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?ve:Math.pow(ve,t),new qe(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new qe(Xe(this.r),Xe(this.g),Xe(this.b),ze(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ie,formatHex:Ie,formatHex8:function(){return`#${Be(this.r)}${Be(this.g)}${Be(this.b)}${Be(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:$e,toString:$e})),me(Fe,(function(t,e,n,r){return 1===arguments.length?Le(t):new Fe(t,e,n,null==r?1:r)}),pe(ge,{brighter(t){return t=null==t?ye:Math.pow(ye,t),new Fe(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?ve:Math.pow(ve,t),new Fe(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new qe(Ke(t>=240?t-240:t+120,i,r),Ke(t,i,r),Ke(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new Fe(Ye(this.h),He(this.s),He(this.l),ze(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=ze(this.opacity);return`${1===t?"hsl(":"hsla("}${Ye(this.h)}, ${100*He(this.s)}%, ${100*He(this.l)}%${1===t?")":`, ${t})`}`}}));var We=t=>()=>t;function Ge(t,e){return function(n){return t+n*e}}function Ze(t){return 1===(t=+t)?Je:function(e,n){return n-e?function(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(r){return Math.pow(t+r*e,n)}}(e,n,t):We(isNaN(e)?n:e)}}function Je(t,e){var n=e-t;return n?Ge(t,n):We(isNaN(t)?e:t)}var Qe=function t(e){var n=Ze(e);function r(t,e){var r=n((t=Ce(t)).r,(e=Ce(e)).r),i=n(t.g,e.g),a=n(t.b,e.b),s=Je(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=a(e),t.opacity=s(e),t+""}}return r.gamma=t,r}(1);function tn(t){return function(e){var n,r,i=e.length,a=new Array(i),s=new Array(i),o=new Array(i);for(n=0;n<i;++n)r=Ce(e[n]),a[n]=r.r||0,s[n]=r.g||0,o[n]=r.b||0;return a=t(a),s=t(s),o=t(o),r.opacity=1,function(t){return r.r=a(t),r.g=s(t),r.b=o(t),r+""}}}tn((function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],a=t[r+1],s=r>0?t[r-1]:2*i-a,o=r<e-1?t[r+2]:2*a-i;return Ue((n-r/e)*e,s,i,a,o)}})),tn((function(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),i=t[(r+e-1)%e],a=t[r%e],s=t[(r+1)%e],o=t[(r+2)%e];return Ue((n-r/e)*e,i,a,s,o)}}));var en=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,nn=new RegExp(en.source,"g");function rn(t,e){var n,r,i,a=en.lastIndex=nn.lastIndex=0,s=-1,o=[],u=[];for(t+="",e+="";(n=en.exec(t))&&(r=nn.exec(e));)(i=r.index)>a&&(i=e.slice(a,i),o[s]?o[s]+=i:o[++s]=i),(n=n[0])===(r=r[0])?o[s]?o[s]+=r:o[++s]=r:(o[++s]=null,u.push({i:s,x:re(n,r)})),a=nn.lastIndex;return a<e.length&&(i=e.slice(a),o[s]?o[s]+=i:o[++s]=i),o.length<2?u[0]?function(t){return function(e){return t(e)+""}}(u[0].x):function(t){return function(){return t}}(e):(e=u.length,function(t){for(var n,r=0;r<e;++r)o[(n=u[r]).i]=n.x(t);return o.join("")})}function an(t,e){var n;return("number"===typeof e?re:e instanceof De?Qe:(n=De(e))?(e=n,Qe):rn)(t,e)}function sn(t){return function(){this.removeAttribute(t)}}function on(t){return function(){this.removeAttributeNS(t.space,t.local)}}function un(t,e,n){var r,i,a=n+"";return function(){var s=this.getAttribute(t);return s===a?null:s===r?i:i=e(r=s,n)}}function ln(t,e,n){var r,i,a=n+"";return function(){var s=this.getAttributeNS(t.space,t.local);return s===a?null:s===r?i:i=e(r=s,n)}}function cn(t,e,n){var r,i,a;return function(){var s,o,u=n(this);if(null!=u)return(s=this.getAttribute(t))===(o=u+"")?null:s===r&&o===i?a:(i=o,a=e(r=s,u));this.removeAttribute(t)}}function hn(t,e,n){var r,i,a;return function(){var s,o,u=n(this);if(null!=u)return(s=this.getAttributeNS(t.space,t.local))===(o=u+"")?null:s===r&&o===i?a:(i=o,a=e(r=s,u));this.removeAttributeNS(t.space,t.local)}}function fn(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(t,e){return function(n){this.setAttributeNS(t.space,t.local,e.call(this,n))}}(t,i)),n}return i._value=e,i}function dn(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(t,e){return function(n){this.setAttribute(t,e.call(this,n))}}(t,i)),n}return i._value=e,i}function mn(t,e){return function(){Qt(this,t).delay=+e.apply(this,arguments)}}function pn(t,e){return e=+e,function(){Qt(this,t).delay=e}}function gn(t,e){return function(){te(this,t).duration=+e.apply(this,arguments)}}function vn(t,e){return e=+e,function(){te(this,t).duration=e}}var yn=dt.prototype.constructor;function bn(t){return function(){this.style.removeProperty(t)}}var wn=0;function xn(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}function _n(){return++wn}var kn=dt.prototype;xn.prototype=function(t){return dt().transition(t)}.prototype={constructor:xn,select:function(t){var e=this._name,n=this._id;"function"!==typeof t&&(t=i(t));for(var r=this._groups,a=r.length,s=new Array(a),o=0;o<a;++o)for(var u,l,c=r[o],h=c.length,f=s[o]=new Array(h),d=0;d<h;++d)(u=c[d])&&(l=t.call(u,u.__data__,d,c))&&("__data__"in u&&(l.__data__=u.__data__),f[d]=l,Jt(f[d],e,n,d,f,ee(u,n)));return new xn(s,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!==typeof t&&(t=s(t));for(var r=this._groups,i=r.length,a=[],o=[],u=0;u<i;++u)for(var l,c=r[u],h=c.length,f=0;f<h;++f)if(l=c[f]){for(var d,m=t.call(l,l.__data__,f,c),p=ee(l,n),g=0,v=m.length;g<v;++g)(d=m[g])&&Jt(d,e,n,g,m,p);a.push(m),o.push(l)}return new xn(a,o,e,n)},selectChild:kn.selectChild,selectChildren:kn.selectChildren,filter:function(t){"function"!==typeof t&&(t=o(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var a,s=e[i],u=s.length,l=r[i]=[],c=0;c<u;++c)(a=s[c])&&t.call(a,a.__data__,c,s)&&l.push(a);return new xn(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var e=this._groups,n=t._groups,r=e.length,i=n.length,a=Math.min(r,i),s=new Array(r),o=0;o<a;++o)for(var u,l=e[o],c=n[o],h=l.length,f=s[o]=new Array(h),d=0;d<h;++d)(u=l[d]||c[d])&&(f[d]=u);for(;o<r;++o)s[o]=e[o];return new xn(s,this._parents,this._name,this._id)},selection:function(){return new yn(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=_n(),r=this._groups,i=r.length,a=0;a<i;++a)for(var s,o=r[a],u=o.length,l=0;l<u;++l)if(s=o[l]){var c=ee(s,e);Jt(s,t,n,l,o,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new xn(r,this._parents,t,n)},call:kn.call,nodes:kn.nodes,node:kn.node,size:kn.size,empty:kn.empty,each:kn.each,on:function(t,e){var n=this._id;return arguments.length<2?ee(this.node(),n).on.on(t):this.each(function(t,e,n){var r,i,a=function(t){return(t+"").trim().split(/^|\s+/).every((function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t}))}(e)?Qt:te;return function(){var s=a(this,t),o=s.on;o!==r&&(i=(r=o).copy()).on(e,n),s.on=i}}(n,t,e))},attr:function(t,e){var n=p(t),r="transform"===n?ce:an;return this.attrTween(t,"function"===typeof e?(n.local?hn:cn)(n,r,de(this,"attr."+t,e)):null==e?(n.local?on:sn)(n):(n.local?ln:un)(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!==typeof e)throw new Error;var r=p(t);return this.tween(n,(r.local?fn:dn)(r,e))},style:function(t,e,n){var r="transform"===(t+="")?le:an;return null==e?this.styleTween(t,function(t,e){var n,r,i;return function(){var a=N(this,t),s=(this.style.removeProperty(t),N(this,t));return a===s?null:a===n&&s===r?i:i=e(n=a,r=s)}}(t,r)).on("end.style."+t,bn(t)):"function"===typeof e?this.styleTween(t,function(t,e,n){var r,i,a;return function(){var s=N(this,t),o=n(this),u=o+"";return null==o&&(this.style.removeProperty(t),u=o=N(this,t)),s===u?null:s===r&&u===i?a:(i=u,a=e(r=s,o))}}(t,r,de(this,"style."+t,e))).each(function(t,e){var n,r,i,a,s="style."+e,o="end."+s;return function(){var u=te(this,t),l=u.on,c=null==u.value[s]?a||(a=bn(e)):void 0;l===n&&i===c||(r=(n=l).copy()).on(o,i=c),u.on=r}}(this._id,t)):this.styleTween(t,function(t,e,n){var r,i,a=n+"";return function(){var s=N(this,t);return s===a?null:s===r?i:i=e(r=s,n)}}(t,r,e),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!==typeof e)throw new Error;return this.tween(r,function(t,e,n){var r,i;function a(){var a=e.apply(this,arguments);return a!==i&&(r=(i=a)&&function(t,e,n){return function(r){this.style.setProperty(t,e.call(this,r),n)}}(t,a,n)),r}return a._value=e,a}(t,e,null==n?"":n))},text:function(t){return this.tween("text","function"===typeof t?function(t){return function(){var e=t(this);this.textContent=null==e?"":e}}(de(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!==typeof t)throw new Error;return this.tween(e,function(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&function(t){return function(e){this.textContent=t.call(this,e)}}(r)),e}return r._value=t,r}(t))},remove:function(){return this.on("end.remove",function(t){return function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}}(this._id))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=ee(this.node(),n).tween,a=0,s=i.length;a<s;++a)if((r=i[a]).name===t)return r.value;return null}return this.each((null==e?he:fe)(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"===typeof t?mn:pn)(e,t)):ee(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"===typeof t?gn:vn)(e,t)):ee(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!==typeof e)throw new Error;return function(){te(this,t).ease=e}}(e,t)):ee(this.node(),e).ease},easeVarying:function(t){if("function"!==typeof t)throw new Error;return this.each(function(t,e){return function(){var n=e.apply(this,arguments);if("function"!==typeof n)throw new Error;te(this,t).ease=n}}(this._id,t))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise((function(a,s){var o={value:s},u={value:function(){0===--i&&a()}};n.each((function(){var n=te(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(o),e._.interrupt.push(o),e._.end.push(u)),n.on=e})),0===i&&a()}))},[Symbol.iterator]:kn[Symbol.iterator]};var Mn={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function An(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw new Error(`transition ${e} not found`);return n}function On(t){return function(){return t}}function Nn(t,e,n){this.target=t,this.type=e,this.selection=n}function En(){et.stopImmediatePropagation()}function Sn(){et.preventDefault(),et.stopImmediatePropagation()}dt.prototype.interrupt=function(t){return this.each((function(){ne(this,t)}))},dt.prototype.transition=function(t){var e,n;t instanceof xn?(e=t._id,t=t._name):(e=_n(),(n=Mn).time=Vt(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,a=0;a<i;++a)for(var s,o=r[a],u=o.length,l=0;l<u;++l)(s=o[l])&&Jt(s,t,e,l,o,n||An(s,e));return new xn(r,this._parents,t,e)};var Pn={name:"drag"},Tn={name:"space"},Dn={name:"handle"},Rn={name:"center"};function jn(t){return[+t[0],+t[1]]}function Cn(t){return[jn(t[0]),jn(t[1])]}var qn={name:"x",handles:["w","e"].map(Ln),input:function(t,e){return null==t?null:[[+t[0],e[0][1]],[+t[1],e[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},In={name:"y",handles:["n","s"].map(Ln),input:function(t,e){return null==t?null:[[e[0][0],+t[0]],[e[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},$n=(["n","w","e","s","nw","ne","sw","se"].map(Ln),{overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"}),zn={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},Xn={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},Bn={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},Vn={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function Ln(t){return{type:t}}function Fn(){return!et.ctrlKey&&!et.button}function Yn(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function Hn(){return navigator.maxTouchPoints||"ontouchstart"in this}function Kn(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function Un(t){var e=t.__brush;return e?e.dim.output(e.selection):null}function Wn(){return Gn(In)}function Gn(t){var e,n=Yn,r=Fn,i=Hn,a=!0,s=(0,bt.A)("start","brush","end"),o=6;function u(e){var n=e.property("__brush",p).selectAll(".overlay").data([Ln("overlay")]);n.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",$n.overlay).merge(n).each((function(){var t=Kn(this).extent;mt(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])})),e.selectAll(".selection").data([Ln("selection")]).enter().append("rect").attr("class","selection").attr("cursor",$n.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var r=e.selectAll(".handle").data(t.handles,(function(t){return t.type}));r.exit().remove(),r.enter().append("rect").attr("class",(function(t){return"handle handle--"+t.type})).attr("cursor",(function(t){return $n[t.type]})),e.each(l).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",f).filter(i).on("touchstart.brush",f).on("touchmove.brush",d).on("touchend.brush touchcancel.brush",m).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function l(){var t=mt(this),e=Kn(this).selection;e?(t.selectAll(".selection").style("display",null).attr("x",e[0][0]).attr("y",e[0][1]).attr("width",e[1][0]-e[0][0]).attr("height",e[1][1]-e[0][1]),t.selectAll(".handle").style("display",null).attr("x",(function(t){return"e"===t.type[t.type.length-1]?e[1][0]-o/2:e[0][0]-o/2})).attr("y",(function(t){return"s"===t.type[0]?e[1][1]-o/2:e[0][1]-o/2})).attr("width",(function(t){return"n"===t.type||"s"===t.type?e[1][0]-e[0][0]+o:o})).attr("height",(function(t){return"e"===t.type||"w"===t.type?e[1][1]-e[0][1]+o:o}))):t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function c(t,e,n){var r=t.__brush.emitter;return!r||n&&r.clean?new h(t,e,n):r}function h(t,e,n){this.that=t,this.args=e,this.state=t.__brush,this.active=0,this.clean=n}function f(){if((!e||et.touches)&&r.apply(this,arguments)){var n,i,s,o,u,h,f,d,m,p,g,v,y=this,b=et.target.__data__.type,w="selection"===(a&&et.metaKey?b="overlay":b)?Pn:a&&et.altKey?Rn:Dn,x=t===In?null:Bn[b],_=t===qn?null:Vn[b],k=Kn(y),M=k.extent,A=k.selection,O=M[0][0],N=M[0][1],E=M[1][0],S=M[1][1],P=0,T=0,D=x&&_&&a&&et.shiftKey,R=et.touches?(v=et.changedTouches[0].identifier,function(t){return At(t,et.touches,v)}):vt,j=R(y),C=j,q=c(y,arguments,!0).beforestart();"overlay"===b?(A&&(m=!0),k.selection=A=[[n=t===In?O:j[0],s=t===qn?N:j[1]],[u=t===In?E:n,f=t===qn?S:s]]):(n=A[0][0],s=A[0][1],u=A[1][0],f=A[1][1]),i=n,o=s,h=u,d=f;var I=mt(y).attr("pointer-events","none"),$=I.selectAll(".overlay").attr("cursor",$n[b]);if(et.touches)q.moved=X,q.ended=V;else{var z=mt(et.view).on("mousemove.brush",X,!0).on("mouseup.brush",V,!0);a&&z.on("keydown.brush",(function(){switch(et.keyCode){case 16:D=x&&_;break;case 18:w===Dn&&(x&&(u=h-P*x,n=i+P*x),_&&(f=d-T*_,s=o+T*_),w=Rn,B());break;case 32:w!==Dn&&w!==Rn||(x<0?u=h-P:x>0&&(n=i-P),_<0?f=d-T:_>0&&(s=o-T),w=Tn,$.attr("cursor",$n.selection),B());break;default:return}Sn()}),!0).on("keyup.brush",(function(){switch(et.keyCode){case 16:D&&(p=g=D=!1,B());break;case 18:w===Rn&&(x<0?u=h:x>0&&(n=i),_<0?f=d:_>0&&(s=o),w=Dn,B());break;case 32:w===Tn&&(et.altKey?(x&&(u=h-P*x,n=i+P*x),_&&(f=d-T*_,s=o+T*_),w=Rn):(x<0?u=h:x>0&&(n=i),_<0?f=d:_>0&&(s=o),w=Dn),$.attr("cursor",$n[b]),B());break;default:return}Sn()}),!0),_t(et.view)}En(),ne(y),l.call(y),q.start()}function X(){var t=R(y);!D||p||g||(Math.abs(t[0]-C[0])>Math.abs(t[1]-C[1])?g=!0:p=!0),C=t,m=!0,Sn(),B()}function B(){var t;switch(P=C[0]-j[0],T=C[1]-j[1],w){case Tn:case Pn:x&&(P=Math.max(O-n,Math.min(E-u,P)),i=n+P,h=u+P),_&&(T=Math.max(N-s,Math.min(S-f,T)),o=s+T,d=f+T);break;case Dn:x<0?(P=Math.max(O-n,Math.min(E-n,P)),i=n+P,h=u):x>0&&(P=Math.max(O-u,Math.min(E-u,P)),i=n,h=u+P),_<0?(T=Math.max(N-s,Math.min(S-s,T)),o=s+T,d=f):_>0&&(T=Math.max(N-f,Math.min(S-f,T)),o=s,d=f+T);break;case Rn:x&&(i=Math.max(O,Math.min(E,n-P*x)),h=Math.max(O,Math.min(E,u+P*x))),_&&(o=Math.max(N,Math.min(S,s-T*_)),d=Math.max(N,Math.min(S,f+T*_)))}h<i&&(x*=-1,t=n,n=u,u=t,t=i,i=h,h=t,b in zn&&$.attr("cursor",$n[b=zn[b]])),d<o&&(_*=-1,t=s,s=f,f=t,t=o,o=d,d=t,b in Xn&&$.attr("cursor",$n[b=Xn[b]])),k.selection&&(A=k.selection),p&&(i=A[0][0],h=A[1][0]),g&&(o=A[0][1],d=A[1][1]),A[0][0]===i&&A[0][1]===o&&A[1][0]===h&&A[1][1]===d||(k.selection=[[i,o],[h,d]],l.call(y),q.brush())}function V(){if(En(),et.touches){if(et.touches.length)return;e&&clearTimeout(e),e=setTimeout((function(){e=null}),500)}else kt(et.view,m),z.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);I.attr("pointer-events","all"),$.attr("cursor",$n.overlay),k.selection&&(A=k.selection),function(t){return t[0][0]===t[1][0]||t[0][1]===t[1][1]}(A)&&(k.selection=null,l.call(y)),q.end()}}function d(){c(this,arguments).moved()}function m(){c(this,arguments).ended()}function p(){var e=this.__brush||{selection:null};return e.extent=Cn(n.apply(this,arguments)),e.dim=t,e}return u.move=function(e,n){e.selection?e.on("start.brush",(function(){c(this,arguments).beforestart().start()})).on("interrupt.brush end.brush",(function(){c(this,arguments).end()})).tween("brush",(function(){var e=this,r=e.__brush,i=c(e,arguments),a=r.selection,s=t.input("function"===typeof n?n.apply(this,arguments):n,r.extent),o=(0,Mt.A)(a,s);function u(t){r.selection=1===t&&null===s?null:o(t),l.call(e),i.brush()}return null!==a&&null!==s?u:u(1)})):e.each((function(){var e=this,r=arguments,i=e.__brush,a=t.input("function"===typeof n?n.apply(e,r):n,i.extent),s=c(e,r).beforestart();ne(e),i.selection=null===a?null:a,l.call(e),s.start().brush().end()}))},u.clear=function(t){u.move(t,null)},h.prototype={beforestart:function(){return 1===++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(){return this.starting?(this.starting=!1,this.emit("start")):this.emit("brush"),this},brush:function(){return this.emit("brush"),this},end:function(){return 0===--this.active&&(delete this.state.emitter,this.emit("end")),this},emit:function(e){st(new Nn(u,e,t.output(this.state.selection)),s.apply,s,[e,this.that,this.args])}},u.extent=function(t){return arguments.length?(n="function"===typeof t?t:On(Cn(t)),u):n},u.filter=function(t){return arguments.length?(r="function"===typeof t?t:On(!!t),u):r},u.touchable=function(t){return arguments.length?(i="function"===typeof t?t:On(!!t),u):i},u.handleSize=function(t){return arguments.length?(o=+t,u):o},u.keyModifiers=function(t){return arguments.length?(a=!!t,u):a},u.on=function(){var t=s.on.apply(s,arguments);return t===s?u:t},u}function Zn(t){return function(){return t}}function Jn(t,e,n,r,i,a,s,o,u,l){this.target=t,this.type=e,this.subject=n,this.identifier=r,this.active=i,this.x=a,this.y=s,this.dx=o,this.dy=u,this._=l}function Qn(){return!et.ctrlKey&&!et.button}function tr(){return this.parentNode}function er(t){return null==t?{x:et.x,y:et.y}:t}function nr(){return navigator.maxTouchPoints||"ontouchstart"in this}function rr(){var t,e,n,r,i=Qn,a=tr,s=er,o=nr,u={},l=(0,bt.A)("start","drag","end"),c=0,h=0;function f(t){t.on("mousedown.drag",d).filter(o).on("touchstart.drag",g).on("touchmove.drag",v).on("touchend.drag touchcancel.drag",y).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function d(){if(!r&&i.apply(this,arguments)){var s=b("mouse",a.apply(this,arguments),vt,this,arguments);s&&(mt(et.view).on("mousemove.drag",m,!0).on("mouseup.drag",p,!0),_t(et.view),wt(),n=!1,t=et.clientX,e=et.clientY,s("start"))}}function m(){if(xt(),!n){var r=et.clientX-t,i=et.clientY-e;n=r*r+i*i>h}u.mouse("drag")}function p(){mt(et.view).on("mousemove.drag mouseup.drag",null),kt(et.view,n),xt(),u.mouse("end")}function g(){if(i.apply(this,arguments)){var t,e,n=et.changedTouches,r=a.apply(this,arguments),s=n.length;for(t=0;t<s;++t)(e=b(n[t].identifier,r,At,this,arguments))&&(wt(),e("start"))}}function v(){var t,e,n=et.changedTouches,r=n.length;for(t=0;t<r;++t)(e=u[n[t].identifier])&&(xt(),e("drag"))}function y(){var t,e,n=et.changedTouches,i=n.length;for(r&&clearTimeout(r),r=setTimeout((function(){r=null}),500),t=0;t<i;++t)(e=u[n[t].identifier])&&(wt(),e("end"))}function b(t,e,n,r,i){var a,o,h,d=n(e,t),m=l.copy();if(st(new Jn(f,"beforestart",a,t,c,d[0],d[1],0,0,m),(function(){return null!=(et.subject=a=s.apply(r,i))&&(o=a.x-d[0]||0,h=a.y-d[1]||0,!0)})))return function s(l){var p,g=d;switch(l){case"start":u[t]=s,p=c++;break;case"end":delete u[t],--c;case"drag":d=n(e,t),p=c}st(new Jn(f,l,a,t,p,d[0]+o,d[1]+h,d[0]-g[0],d[1]-g[1],m),m.apply,m,[l,r,i])}}return f.filter=function(t){return arguments.length?(i="function"===typeof t?t:Zn(!!t),f):i},f.container=function(t){return arguments.length?(a="function"===typeof t?t:Zn(t),f):a},f.subject=function(t){return arguments.length?(s="function"===typeof t?t:Zn(t),f):s},f.touchable=function(t){return arguments.length?(o="function"===typeof t?t:Zn(!!t),f):o},f.on=function(){var t=l.on.apply(l,arguments);return t===l?f:t},f.clickDistance=function(t){return arguments.length?(h=(t=+t)*t,f):Math.sqrt(h)},f}Jn.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var ir=n(18152),ar=n(3983),sr=Math.abs,or=Math.atan2,ur=Math.cos,lr=Math.max,cr=Math.min,hr=Math.sin,fr=Math.sqrt,dr=1e-12,mr=Math.PI,pr=mr/2,gr=2*mr;function vr(t){return t>=1?pr:t<=-1?-pr:Math.asin(t)}function yr(t){return t.innerRadius}function br(t){return t.outerRadius}function wr(t){return t.startAngle}function xr(t){return t.endAngle}function _r(t){return t&&t.padAngle}function kr(t,e,n,r,i,a,s){var o=t-n,u=e-r,l=(s?a:-a)/fr(o*o+u*u),c=l*u,h=-l*o,f=t+c,d=e+h,m=n+c,p=r+h,g=(f+m)/2,v=(d+p)/2,y=m-f,b=p-d,w=y*y+b*b,x=i-a,_=f*p-m*d,k=(b<0?-1:1)*fr(lr(0,x*x*w-_*_)),M=(_*b-y*k)/w,A=(-_*y-b*k)/w,O=(_*b+y*k)/w,N=(-_*y+b*k)/w,E=M-g,S=A-v,P=O-g,T=N-v;return E*E+S*S>P*P+T*T&&(M=O,A=N),{cx:M,cy:A,x01:-c,y01:-h,x11:M*(i/x-1),y11:A*(i/x-1)}}function Mr(){var t=yr,e=br,n=(0,ar.A)(0),r=null,i=wr,a=xr,s=_r,o=null;function u(){var u,l,c,h=+t.apply(this,arguments),f=+e.apply(this,arguments),d=i.apply(this,arguments)-pr,m=a.apply(this,arguments)-pr,p=sr(m-d),g=m>d;if(o||(o=u=(0,ir.A)()),f<h&&(l=f,f=h,h=l),f>dr)if(p>gr-dr)o.moveTo(f*ur(d),f*hr(d)),o.arc(0,0,f,d,m,!g),h>dr&&(o.moveTo(h*ur(m),h*hr(m)),o.arc(0,0,h,m,d,g));else{var v,y,b=d,w=m,x=d,_=m,k=p,M=p,A=s.apply(this,arguments)/2,O=A>dr&&(r?+r.apply(this,arguments):fr(h*h+f*f)),N=cr(sr(f-h)/2,+n.apply(this,arguments)),E=N,S=N;if(O>dr){var P=vr(O/h*hr(A)),T=vr(O/f*hr(A));(k-=2*P)>dr?(x+=P*=g?1:-1,_-=P):(k=0,x=_=(d+m)/2),(M-=2*T)>dr?(b+=T*=g?1:-1,w-=T):(M=0,b=w=(d+m)/2)}var D=f*ur(b),R=f*hr(b),j=h*ur(_),C=h*hr(_);if(N>dr){var q,I=f*ur(w),$=f*hr(w),z=h*ur(x),X=h*hr(x);if(p<mr&&(q=function(t,e,n,r,i,a,s,o){var u=n-t,l=r-e,c=s-i,h=o-a,f=h*u-c*l;if(!(f*f<dr))return[t+(f=(c*(e-a)-h*(t-i))/f)*u,e+f*l]}(D,R,z,X,I,$,j,C))){var B=D-q[0],V=R-q[1],L=I-q[0],F=$-q[1],Y=1/hr(((c=(B*L+V*F)/(fr(B*B+V*V)*fr(L*L+F*F)))>1?0:c<-1?mr:Math.acos(c))/2),H=fr(q[0]*q[0]+q[1]*q[1]);E=cr(N,(h-H)/(Y-1)),S=cr(N,(f-H)/(Y+1))}}M>dr?S>dr?(v=kr(z,X,D,R,f,S,g),y=kr(I,$,j,C,f,S,g),o.moveTo(v.cx+v.x01,v.cy+v.y01),S<N?o.arc(v.cx,v.cy,S,or(v.y01,v.x01),or(y.y01,y.x01),!g):(o.arc(v.cx,v.cy,S,or(v.y01,v.x01),or(v.y11,v.x11),!g),o.arc(0,0,f,or(v.cy+v.y11,v.cx+v.x11),or(y.cy+y.y11,y.cx+y.x11),!g),o.arc(y.cx,y.cy,S,or(y.y11,y.x11),or(y.y01,y.x01),!g))):(o.moveTo(D,R),o.arc(0,0,f,b,w,!g)):o.moveTo(D,R),h>dr&&k>dr?E>dr?(v=kr(j,C,I,$,h,-E,g),y=kr(D,R,z,X,h,-E,g),o.lineTo(v.cx+v.x01,v.cy+v.y01),E<N?o.arc(v.cx,v.cy,E,or(v.y01,v.x01),or(y.y01,y.x01),!g):(o.arc(v.cx,v.cy,E,or(v.y01,v.x01),or(v.y11,v.x11),!g),o.arc(0,0,h,or(v.cy+v.y11,v.cx+v.x11),or(y.cy+y.y11,y.cx+y.x11),g),o.arc(y.cx,y.cy,E,or(y.y11,y.x11),or(y.y01,y.x01),!g))):o.arc(0,0,h,_,x,g):o.lineTo(j,C)}else o.moveTo(0,0);if(o.closePath(),u)return o=null,u+""||null}return u.centroid=function(){var n=(+t.apply(this,arguments)+ +e.apply(this,arguments))/2,r=(+i.apply(this,arguments)+ +a.apply(this,arguments))/2-mr/2;return[ur(r)*n,hr(r)*n]},u.innerRadius=function(e){return arguments.length?(t="function"===typeof e?e:(0,ar.A)(+e),u):t},u.outerRadius=function(t){return arguments.length?(e="function"===typeof t?t:(0,ar.A)(+t),u):e},u.cornerRadius=function(t){return arguments.length?(n="function"===typeof t?t:(0,ar.A)(+t),u):n},u.padRadius=function(t){return arguments.length?(r=null==t?null:"function"===typeof t?t:(0,ar.A)(+t),u):r},u.startAngle=function(t){return arguments.length?(i="function"===typeof t?t:(0,ar.A)(+t),u):i},u.endAngle=function(t){return arguments.length?(a="function"===typeof t?t:(0,ar.A)(+t),u):a},u.padAngle=function(t){return arguments.length?(s="function"===typeof t?t:(0,ar.A)(+t),u):s},u.context=function(t){return arguments.length?(o=null==t?null:t,u):o},u}var Ar=n(70807);function Or(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}var Nr=function(t){var e;return 1===t.length&&(e=t,t=function(t,n){return Or(e(t),n)}),{left:function(e,n,r,i){for(null==r&&(r=0),null==i&&(i=e.length);r<i;){var a=r+i>>>1;t(e[a],n)<0?r=a+1:i=a}return r},right:function(e,n,r,i){for(null==r&&(r=0),null==i&&(i=e.length);r<i;){var a=r+i>>>1;t(e[a],n)>0?i=a:r=a+1}return r}}}(Or);Nr.right,Nr.left;function Er(t,e){var n,r,i,a=t.length,s=-1;if(null==e){for(;++s<a;)if(null!=(n=t[s])&&n>=n)for(r=i=n;++s<a;)null!=(n=t[s])&&(r>n&&(r=n),i<n&&(i=n))}else for(;++s<a;)if(null!=(n=e(t[s],s,t))&&n>=n)for(r=i=n;++s<a;)null!=(n=e(t[s],s,t))&&(r>n&&(r=n),i<n&&(i=n));return[r,i]}var Sr=Array.prototype;Sr.slice,Sr.map,Math.sqrt(50),Math.sqrt(10),Math.sqrt(2);var Pr=n(25503),Tr=Array.prototype.slice;function Dr(t){return t}var Rr=1e-6;function jr(t){return"translate("+(t+.5)+",0)"}function Cr(t){return"translate(0,"+(t+.5)+")"}function qr(t){return function(e){return+t(e)}}function Ir(t){var e=Math.max(0,t.bandwidth()-1)/2;return t.round()&&(e=Math.round(e)),function(n){return+t(n)+e}}function $r(){return!this.__axis}function zr(t,e){var n=[],r=null,i=null,a=6,s=6,o=3,u=1===t||4===t?-1:1,l=4===t||2===t?"x":"y",c=1===t||3===t?jr:Cr;function h(h){var f=null==r?e.ticks?e.ticks.apply(e,n):e.domain():r,d=null==i?e.tickFormat?e.tickFormat.apply(e,n):Dr:i,m=Math.max(a,0)+o,p=e.range(),g=+p[0]+.5,v=+p[p.length-1]+.5,y=(e.bandwidth?Ir:qr)(e.copy()),b=h.selection?h.selection():h,w=b.selectAll(".domain").data([null]),x=b.selectAll(".tick").data(f,e).order(),_=x.exit(),k=x.enter().append("g").attr("class","tick"),M=x.select("line"),A=x.select("text");w=w.merge(w.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),x=x.merge(k),M=M.merge(k.append("line").attr("stroke","currentColor").attr(l+"2",u*a)),A=A.merge(k.append("text").attr("fill","currentColor").attr(l,u*m).attr("dy",1===t?"0em":3===t?"0.71em":"0.32em")),h!==b&&(w=w.transition(h),x=x.transition(h),M=M.transition(h),A=A.transition(h),_=_.transition(h).attr("opacity",Rr).attr("transform",(function(t){return isFinite(t=y(t))?c(t):this.getAttribute("transform")})),k.attr("opacity",Rr).attr("transform",(function(t){var e=this.parentNode.__axis;return c(e&&isFinite(e=e(t))?e:y(t))}))),_.remove(),w.attr("d",4===t||2==t?s?"M"+u*s+","+g+"H0.5V"+v+"H"+u*s:"M0.5,"+g+"V"+v:s?"M"+g+","+u*s+"V0.5H"+v+"V"+u*s:"M"+g+",0.5H"+v),x.attr("opacity",1).attr("transform",(function(t){return c(y(t))})),M.attr(l+"2",u*a),A.attr(l,u*m).text(d),b.filter($r).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",2===t?"start":4===t?"end":"middle"),b.each((function(){this.__axis=y}))}return h.scale=function(t){return arguments.length?(e=t,h):e},h.ticks=function(){return n=Tr.call(arguments),h},h.tickArguments=function(t){return arguments.length?(n=null==t?[]:Tr.call(t),h):n.slice()},h.tickValues=function(t){return arguments.length?(r=null==t?null:Tr.call(t),h):r&&r.slice()},h.tickFormat=function(t){return arguments.length?(i=t,h):i},h.tickSize=function(t){return arguments.length?(a=s=+t,h):a},h.tickSizeInner=function(t){return arguments.length?(a=+t,h):a},h.tickSizeOuter=function(t){return arguments.length?(s=+t,h):s},h.tickPadding=function(t){return arguments.length?(o=+t,h):o},h}function Xr(t){return zr(1,t)}function Br(t){return zr(2,t)}function Vr(t){return zr(3,t)}function Lr(t){return zr(4,t)}var Fr=function(t){var e=[],n=1e3,r=function(){},i=function(){},a=function t(e){e&&t.data(e),r(),i(),t.render()};return a.render=function(){var i=!0;r=a.invalidate=function(){i=!1},function r(){if(!i)return!0;e.splice(0,n).map(t),requestAnimationFrame(r)}()},a.data=function(t){return r(),e=t.slice(0),a},a.add=function(t){e=e.concat(t)},a.rate=function(t){return arguments.length?(n=t,a):n},a.remaining=function(){return e.length},a.clear=function(t){return arguments.length?(i=t,a):(i(),a)},a.invalidate=r,a},Yr=function(t){return t.width-t.margin.right-t.margin.left},Hr=function(t,e){return null===e?[]:"undefined"===typeof e.invert?function(t,e){if(0===t.length)return[];var n=e.domain(),r=e.range(),i=[];return r.forEach((function(e,r){e>=t[0]&&e<=t[1]&&i.push(n[r])})),i}(t,e):t.map((function(t){return e.invert(t)}))},Kr=function(t,e,n){return function(r){var i=t.brushes,a=t.brushNodes;if("undefined"===typeof r)return Object.keys(e.dimensions).reduce((function(t,n){var r=i[n];if(void 0!==r&&null!==Un(a[n])){var s=Un(a[n]),o=e.dimensions[n].yscale,u=Hr(s,o);t[n]={extent:r.extent(),selection:{raw:s,scaled:u}}}return t}),{});var s={};return n.g().selectAll(".brush").each((function(t){s[t]=mt(this)})),Object.keys(e.dimensions).forEach((function(t){if(void 0!==r[t]){var n=i[t];if(void 0!==n){var a=e.dimensions[t],o=r[t].map(a.yscale);s[t].call(n).call(n.move,o.reverse())}}})),n.renderBrushed(),n}},Ur=function(t,e,n){return function(){var r=t.brushNodes,i=Object.keys(e.dimensions).filter((function(t){return r[t]&&null!==Un(r[t])})),a=i.map((function(t){var n=Un(r[t]);return"function"===typeof e.dimensions[t].yscale.invert?[e.dimensions[t].yscale.invert(n[1]),e.dimensions[t].yscale.invert(n[0])]:n}));if(0===i.length)return e.data;var s={date:function(t,n,r){return"function"===typeof e.dimensions[n].yscale.bandwidth?a[r][0]<=e.dimensions[n].yscale(t[n])&&e.dimensions[n].yscale(t[n])<=a[r][1]:a[r][0]<=t[n]&&t[n]<=a[r][1]},number:function(t,n,r){return"function"===typeof e.dimensions[n].yscale.bandwidth?a[r][0]<=e.dimensions[n].yscale(t[n])&&e.dimensions[n].yscale(t[n])<=a[r][1]:a[r][0]<=t[n]&&t[n]<=a[r][1]},string:function(t,n,r){return a[r][0]<=e.dimensions[n].yscale(t[n])&&e.dimensions[n].yscale(t[n])<=a[r][1]}};return e.data.filter((function(t){switch(n.predicate){case"AND":return i.every((function(n,r){return s[e.dimensions[n].type](t,n,r)}));case"OR":return i.some((function(n,r){return s[e.dimensions[n].type](t,n,r)}));default:throw new Error("Unknown brush predicate "+e.brushPredicate)}}))}},Wr=function(t,e,n,r){return function(i){t.brushed=i,n.call("brush",e,t.brushed,r),e.renderBrushed()}},Gr=function(t,e,n,r,i){return function(){n.g()||n.createAxes();var a=n.g().append("svg:g").attr("class","brush").each((function(a){mt(this).call(function(t,e,n,r,i){return function(a,s){if(!e.dimensions.hasOwnProperty(a))return function(){};var o="string"===e.dimensions[a].type?e.dimensions[a].yscale.range()[e.dimensions[a].yscale.range().length-1]:e.dimensions[a].yscale.range()[0],u=Wn().extent([[-15,0],[15,o]]),l=function(t){var n=Array.prototype.slice.call(t),r=n[0],i=Un(n[2][0])||[],a=null;e.dimensions.hasOwnProperty(r)&&(a=e.dimensions[r].yscale);var s=Hr(i,a);return{axis:n[0],node:n[2][0],selection:{raw:i,scaled:s}}};return u.on("start",(function(){null!==et.sourceEvent&&(r.call("brushstart",n,e.brushed,l(arguments)),"function"===typeof et.sourceEvent.stopPropagation&&et.sourceEvent.stopPropagation())})).on("brush",(function(){Wr(e,n,r,l(arguments))(Ur(t,e,i)())})).on("end",(function(){Wr(e,n,r)(Ur(t,e,i)()),r.call("brushend",n,e.brushed,l(arguments))})),t.brushes[a]=u,t.brushNodes[a]=s.node(),u}}(t,e,n,r,i)(a,mt(this)))}));return a.selectAll("rect").style("visibility",null).attr("x",-15).attr("width",30),a.selectAll("rect.background").style("fill","transparent"),a.selectAll("rect.extent").style("fill","rgba(255,255,255,0.25)").style("stroke","rgba(0,0,0,0.6)"),a.selectAll(".resize rect").style("fill","rgba(0,0,0,0.1)"),n.brushExtents=Kr(t,e,n),n.brushReset=function(t,e,n){return function(r){var i=t.brushes;void 0===r?(e.brushed=!1,void 0!==n.g()&&null!==n.g()&&(n.g().selectAll(".brush").each((function(t){void 0!==i[t]&&mt(this).call(i[t].move,null)})),n.renderBrushed())):(e.brushed=!1,void 0!==n.g()&&null!==n.g()&&(n.g().selectAll(".brush").each((function(t){t===r&&(mt(this).call(i[t].move,null),"function"===typeof i[t].type&&i[t].event(mt(this)))})),n.renderBrushed()))}}(t,e,n),n}},Zr=function(t,e){return function(){void 0!==e.g()&&null!==e.g()&&e.g().selectAll(".brush").remove(),t.brushes={},delete e.brushExtents,delete e.brushReset}},Jr=function(t,e,n,r,i){var a=i.selectAll(".brush").data(t,(function(t){return t.id}));a.enter().insert("g",".brush").attr("class","brush").attr("dimension",r).attr("id",(function(t){return"brush-"+Object.keys(e.dimensions).indexOf(r)+"-"+t.id})).each((function(t){t.brush(mt(this))})),a.each((function(e){mt(this).attr("class","brush").selectAll(".overlay").style("pointer-events",(function(){var n=e.brush;return e.id===t.length-1&&void 0!==n?"all":"none"}))})),a.exit().remove()},Qr=function(t,e,n,r,i){var a=t.brushes,s=Object.keys(e.dimensions).filter((function(t,e){for(var n=a[t],r=0;r<n.length;r++){var i=document.getElementById("brush-"+e+"-"+r);if(i&&null!==Un(i))return!0}return!1})),o=s.map((function(t){return a[t].filter((function(t){return!n.hideAxis().includes(t)})).map((function(n,r){return Un(document.getElementById("brush-"+Object.keys(e.dimensions).indexOf(t)+"-"+r))})).map((function(n,r){return null===n||void 0===n?null:"function"===typeof e.dimensions[t].yscale.invert?[e.dimensions[t].yscale.invert(n[1]),e.dimensions[t].yscale.invert(n[0])]:n}))}));if(0===s.length)return e.data;var u={date:function(t,n,r){var i=o[r];if("function"===typeof e.dimensions[n].yscale.bandwidth){var a=!0,s=!1,u=void 0;try{for(var l,c=i[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){var h=l.value;if(null!==h&&void 0!==h&&(h[0]<=e.dimensions[n].yscale(t[n])&&e.dimensions[n].yscale(t[n])<=h[1]))return!0}}catch(y){s=!0,u=y}finally{try{!a&&c.return&&c.return()}finally{if(s)throw u}}return!1}var f=!0,d=!1,m=void 0;try{for(var p,g=i[Symbol.iterator]();!(f=(p=g.next()).done);f=!0){var v=p.value;if(null!==v&&void 0!==v&&(v[0]<=t[n]&&t[n]<=v[1]))return!0}}catch(y){d=!0,m=y}finally{try{!f&&g.return&&g.return()}finally{if(d)throw m}}return!1},number:function(t,n,r){var i=o[r];if("function"===typeof e.dimensions[n].yscale.bandwidth){var a=!0,s=!1,u=void 0;try{for(var l,c=i[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){var h=l.value;if(null!==h&&void 0!==h&&(h[0]<=e.dimensions[n].yscale(t[n])&&e.dimensions[n].yscale(t[n])<=h[1]))return!0}}catch(y){s=!0,u=y}finally{try{!a&&c.return&&c.return()}finally{if(s)throw u}}return!1}var f=!0,d=!1,m=void 0;try{for(var p,g=i[Symbol.iterator]();!(f=(p=g.next()).done);f=!0){var v=p.value;if(null!==v&&void 0!==v&&(v[0]<=t[n]&&t[n]<=v[1]))return!0}}catch(y){d=!0,m=y}finally{try{!f&&g.return&&g.return()}finally{if(d)throw m}}return!1},string:function(t,n,r){var i=o[r],a=!0,s=!1,u=void 0;try{for(var l,c=i[Symbol.iterator]();!(a=(l=c.next()).done);a=!0){var h=l.value;if(null!==h&&void 0!==h&&(h[0]<=e.dimensions[n].yscale(t[n])&&e.dimensions[n].yscale(t[n])<=h[1]))return!0}}catch(f){s=!0,u=f}finally{try{!a&&c.return&&c.return()}finally{if(s)throw u}}return!1}};return e.data.filter((function(t){switch(i.predicate){case"AND":return s.every((function(n,r){return u[e.dimensions[n].type](t,n,r)}));case"OR":return s.some((function(n,r){return u[e.dimensions[n].type](t,n,r)}));default:throw new Error("Unknown brush predicate "+e.brushPredicate)}}))},ti=function(t,e,n){return function(r){t.brushed=r,n.call("brush",e,t.brushed),e.renderBrushed()}},ei=function t(e,n,r,i,a){return function(s,o){var u=e.brushes,l=e.brushNodes,c="string"===n.dimensions[s].type?n.dimensions[s].yscale.range()[n.dimensions[s].yscale.range().length-1]:n.dimensions[s].yscale.range()[0],h=Wn().extent([[-15,0],[15,c]]),f=u[s]?u[s].length:0,d="brush-"+Object.keys(n.dimensions).indexOf(s)+"-"+f;return u[s]?u[s].push({id:f,brush:h,node:d}):u[s]=[{id:f,brush:h,node:d}],l[s]?l[s].push({id:f,node:d}):l[s]=[{id:f,node:d}],h.on("start",(function(){null!==et.sourceEvent&&(i.call("brushstart",r,n.brushed),"function"===typeof et.sourceEvent.stopPropagation&&et.sourceEvent.stopPropagation())})).on("brush",(function(t){ti(n,r,i)(Qr(e,n,r,0,a))})).on("end",(function(){var l=u[s][u[s].length-1].id,c=Un(document.getElementById("brush-"+Object.keys(n.dimensions).indexOf(s)+"-"+l));void 0!==c&&null!==c&&c[0]!==c[1]?(t(e,n,r,i,a)(s,o),Jr(u[s],n,0,s,o),ti(n,r,i)(Qr(e,n,r,0,a))):et.sourceEvent&&"[object MouseEvent]"===et.sourceEvent.toString()&&null===et.selection&&r.brushReset(s),i.call("brushend",r,n.brushed)})),h}},ni=function(t,e,n,r,i){return function(a){var s=t.brushes,o=n.hideAxis();return"undefined"===typeof a?Object.keys(e.dimensions).filter((function(t){return!o.includes(t)})).reduce((function(t,n,r){var i=s[n];return t[n]=void 0===i||null===i?[]:i.reduce((function(t,i,a){var s=Un(document.getElementById("brush-"+r+"-"+a));if(s){var o=e.dimensions[n].yscale,u=Hr(s,o);t.push({extent:i.brush.extent(),selection:{raw:s,scaled:u}})}return t}),[]),t}),{}):(Object.keys(e.dimensions).forEach((function(o,u){if(void 0!==a[o]&&null!==a[o]){var l=e.dimensions[o],c=a[o].map((function(t){return t.map(l.yscale)})).map((function(a,s){var l=ei(t,e,n,r,i)(o,mt("#brush-group-"+u));return l.extent([[-15,a[1]],[15,a[0]]]),{id:s,brush:l,ext:a}}));s[o]=c,Jr(c,e,0,o,mt("#brush-group-"+u)),c.forEach((function(t,e){mt("#brush-"+u+"-"+e).call(t.brush).call(t.brush.move,t.ext.reverse())}))}})),n.renderBrushed(),n)}},ri=function(t,e,n,r,i){return function(){n.g()||n.createAxes();var a=n.hideAxis();return n.g().append("svg:g").attr("id",(function(t,e){return"brush-group-"+e})).attr("class","brush-group").attr("dimension",(function(t){return t})).each((function(s){a.includes(s)||function(t,e,n,r,i){return function(a,s){var o=t.brushes;ei(t,e,n,r,i)(a,s),Jr(o[a],e,0,a,s)}}(t,e,n,r,i)(s,mt(this))})),n.brushExtents=ni(t,e,n,r,i),n.brushReset=function(t,e,n){return function(r){var i=t.brushes;if(void 0===r)void 0!==n.g()&&null!==n.g()&&(Object.keys(e.dimensions).forEach((function(t,e){var r=i[t];r&&r.forEach((function(t,r){var i=document.getElementById("brush-"+e+"-"+r);i&&null!==Un(i)&&n.g().select("#brush-"+e+"-"+r).call(t.brush.move,null)}))})),n.renderBrushed());else if(void 0!==n.g()&&null!==n.g()){var a=i[r],s=Object.keys(e.dimensions).indexOf(r);a&&a.forEach((function(t,e){null!==Un(document.getElementById("brush-"+s+"-"+e))&&(n.g().select("#brush-"+s+"-"+e).call(t.brush.move,null),"function"===typeof t.event&&t.event(mt("#brush-"+s+"-"+e)))})),n.renderBrushed()}}}(t,e,n),n}},ii=function(t,e){return function(){void 0!==e.g()&&null!==e.g()&&e.g().selectAll(".brush-group").remove(),t.brushes={},delete e.brushExtents,delete e.brushReset}},ai=function(t,e){return function(){e.selection.select("svg").select("g#strums").remove(),e.selection.select("svg").select("rect#strum-events").remove(),e.on("axesreorder.strums",void 0),delete e.brushReset,t.strumRect=void 0}},si=function(t,e){return function(n,r){var i=t.strums[r],a=function(t,e){return function(n){var r=[t.p1[0]-t.minX,t.p1[1]-t.minX],i=[t.p2[0]-t.minX,t.p2[1]-t.minX],a=1-e/r[0],s=r[1]*(1-a),o=1-e/i[0],u=i[1]*(1-o),l=n[0],c=n[1],h=a*l+s,f=o*l+u;return c>Math.min(h,f)&&c<Math.max(h,f)}}(i,t.strums.width(r)),s=i.dims.left,o=i.dims.right,u=e.dimensions[s].yscale,l=e.dimensions[o].yscale;return a([u(n[s])-i.minX,l(n[o])-i.minX])}},oi=function(t,e,n){var r=Object.getOwnPropertyNames(e.strums).filter((function(t){return!isNaN(t)})),i=n.data;if(0===r.length)return i;var a=si(e,n);return i.filter((function(e){switch(t.predicate){case"AND":return r.every((function(t){return a(e,t)}));case"OR":return r.some((function(t){return a(e,t)}));default:throw new Error("Unknown brush predicate "+n.brushPredicate)}}))},ui=function(t,e){var n=t.strums[t.strums.active],r=e.selection.select("svg").select("g#strums");delete t.strums[t.strums.active],r.selectAll("line#strum-"+n.dims.i).remove(),r.selectAll("circle#strum-"+n.dims.i).remove()},li=function(t,e,n,r,i){return function(){var a=e.strums[e.strums.active];a&&a.p1[0]===a.p2[0]&&a.p1[1]===a.p2[1]&&ui(e,r);var s=oi(t,e,n);e.strums.active=void 0,n.brushed=s,r.renderBrushed(),i.call("brushend",r,n.brushed)}},ci=function t(e,n,r,i,a,s,o){var u=i.selection.select("svg").select("g#strums"),l=s.dims.i,c=[s.p1,s.p2],h=u.selectAll("line#strum-"+l).data([s]),f=u.selectAll("circle#strum-"+l).data(c),d=rr();h.enter().append("line").attr("id","strum-"+l).attr("class","strum"),h.attr("x1",(function(t){return t.p1[0]})).attr("y1",(function(t){return t.p1[1]})).attr("x2",(function(t){return t.p2[0]})).attr("y2",(function(t){return t.p2[1]})).attr("stroke","black").attr("stroke-width",2),d.on("drag",(function(o,u){var l=et;s["p"+(u+=1)][0]=Math.min(Math.max(s.minX+1,l.x),s.maxX),s["p"+u][1]=Math.min(Math.max(s.minY,l.y),s.maxY),t(e,n,r,i,a,s,u-1)})).on("end",li(e,n,r,i,a)),f.enter().append("circle").attr("id","strum-"+l).attr("class","strum"),f.attr("cx",(function(t){return t[0]})).attr("cy",(function(t){return t[1]})).attr("r",5).style("opacity",(function(t,e){return void 0!==o&&e===o?.8:0})).on("mouseover",(function(){mt(this).style("opacity",.8)})).on("mouseout",(function(){mt(this).style("opacity",0)})).call(d)},hi=function(t,e,n,r,i){return function(){var a=et,s=e.strums[e.strums.active];s.p2[0]=Math.min(Math.max(s.minX+1,a.x-n.margin.left),s.maxX),s.p2[1]=Math.min(Math.max(s.minY,a.y-n.margin.top),s.maxY),ci(t,e,n,r,i,s,1)}},fi=function(t){return t.height-t.margin.top-t.margin.bottom},di=function(t,e,n,r){var i={i:-1,left:void 0,right:void 0};return Object.keys(t.dimensions).some((function(a,s){return!(n(a)<r[0])||(i.i=s,i.left=a,i.right=Object.keys(t.dimensions)[e.getOrderedDimensionKeys().indexOf(a)+1],!1)})),void 0===i.left?(i.i=0,i.left=e.getOrderedDimensionKeys()[0],i.right=e.getOrderedDimensionKeys()[1]):void 0===i.right&&(i.i=Object.keys(t.dimensions).length-1,i.right=i.left,i.left=e.getOrderedDimensionKeys()[Object.keys(t.dimensions).length-2]),i},mi=function(t){return function(e,n){var r=Object.keys(t);return r.some((function(i,a){return i===e&&(a+a<r.length&&t[a+1]===n)}))}},pi=function(t,e,n,r,i,a){return function(){void 0!==r.g()&&null!==r.g()||r.createAxes();var s=rr();e.strums.active=void 0,e.strums.width=function(t){return void 0===e.strums[t]?void 0:e.strums[t].maxX-e.strums[t].minX},r.on("axesreorder.strums",(function(){var a=Object.getOwnPropertyNames(e.strums).filter((function(t){return!isNaN(t)}));a.length>0&&(a.forEach((function(t){var i=e.strums[t].dims;e.strums.active=t,mi(n.dimensions)(i.left,i.right)||ui(e,r)})),li(t,e,n,r,i)())})),r.selection.select("svg").append("g").attr("id","strums").attr("transform","translate("+n.margin.left+","+n.margin.top+")"),r.brushReset=function(t,e,n,r,i){return function(){Object.getOwnPropertyNames(e.strums).filter((function(t){return!isNaN(t)})).forEach((function(t){e.strums.active=t,ui(e,r)})),li(t,e,n,r,i)()}}(t,e,n,r,i),s.on("start",function(t,e,n,r){return function(){var i=vt(t.strumRect.node());i[0]=i[0]-e.margin.left,i[1]=i[1]-e.margin.top;var a=di(e,n,r,i),s={p1:i,dims:a,minX:r(a.left),maxX:r(a.right),minY:0,maxY:fi(e)};s.p1[0]=Math.min(Math.max(s.minX,i[0]),s.maxX),s.p2=s.p1.slice(),t.strums[a.i]=s,t.strums.active=a.i}}(e,n,r,a)).on("drag",hi(t,e,n,r,i)).on("end",li(t,e,n,r,i)),e.strumRect=r.selection.select("svg").insert("rect","g#strums").attr("id","strum-events").attr("x",n.margin.left).attr("y",n.margin.top).attr("width",Yr(n)).attr("height",fi(n)+2).style("opacity",0).call(s)}},gi=function(t,e){return function(){e.selection.select("svg").select("g#arcs").remove(),e.selection.select("svg").select("rect#arc-events").remove(),e.on("axesreorder.arcs",void 0),delete e.brushReset,t.strumRect=void 0}},vi=function(t,e){return Math.sqrt(t*t+e*e)},yi=function(t){return t>Math.PI?1.5*Math.PI-t:.5*Math.PI-t},bi=function(t,e){return function(n,r){var i=t.arcs[r],a=function(t){return function(e){var n=yi(t.startAngle),r=yi(t.endAngle);if(n>r){var i=n;n=r,r=i}return e>=n&&e<=r}}(i),s=i.dims.left,o=i.dims.right,u=e.dimensions[s].yscale,l=e.dimensions[o].yscale,c=t.arcs.width(r),h=u(n[s])-l(n[o]),f=vi(c,h);return a(Math.asin(h/f))}},wi=function(t,e,n){var r=Object.getOwnPropertyNames(e.arcs).filter((function(t){return!isNaN(t)})),i=n.data;if(0===r.length)return i;var a=bi(e,n);return i.filter((function(e){switch(t.predicate){case"AND":return r.every((function(t){return a(e,t)}));case"OR":return r.some((function(t){return a(e,t)}));default:throw new Error("Unknown brush predicate "+n.brushPredicate)}}))},xi=function(t,e){var n=t.arcs[t.arcs.active],r=e.selection.select("svg").select("g#arcs");delete t.arcs[t.arcs.active],t.arcs.active=void 0,r.selectAll("line#arc-"+n.dims.i).remove(),r.selectAll("circle#arc-"+n.dims.i).remove(),r.selectAll("path#arc-"+n.dims.i).remove()},_i=function(t,e,n,r,i){return function(){var a=e.arcs[e.arcs.active];if(a&&a.p1[0]===a.p2[0]&&a.p1[1]===a.p2[1]&&xi(e,r),a){var s=e.arcs.startAngle(e.arcs.active);a.startAngle=s,a.endAngle=s,a.arc.outerRadius(e.arcs.length(e.arcs.active)).startAngle(s).endAngle(s)}e.arcs.active=void 0,n.brushed=wi(t,e,n),r.renderBrushed(),i.call("brushend",r,n.brushed)}},ki=function t(e,n,r,i,a,s,o){var u=i.selection.select("svg").select("g#arcs"),l=s.dims.i,c=[s.p2,s.p3],h=u.selectAll("line#arc-"+l).data([{p1:s.p1,p2:s.p2},{p1:s.p1,p2:s.p3}]),f=u.selectAll("circle#arc-"+l).data(c),d=rr(),m=u.selectAll("path#arc-"+l).data([s]);m.enter().append("path").attr("id","arc-"+l).attr("class","arc").style("fill","orange").style("opacity",.5),m.attr("d",s.arc).attr("transform","translate("+s.p1[0]+","+s.p1[1]+")"),h.enter().append("line").attr("id","arc-"+l).attr("class","arc"),h.attr("x1",(function(t){return t.p1[0]})).attr("y1",(function(t){return t.p1[1]})).attr("x2",(function(t){return t.p2[0]})).attr("y2",(function(t){return t.p2[1]})).attr("stroke","black").attr("stroke-width",2),d.on("drag",(function(o,u){var c=et;s["p"+(u+=2)][0]=Math.min(Math.max(s.minX+1,c.x),s.maxX),s["p"+u][1]=Math.min(Math.max(s.minY,c.y),s.maxY);var h=3===u?n.arcs.startAngle(l):n.arcs.endAngle(l);(s.startAngle<Math.PI&&s.endAngle<Math.PI&&h<Math.PI||s.startAngle>=Math.PI&&s.endAngle>=Math.PI&&h>=Math.PI)&&(2===u?(s.endAngle=h,s.arc.endAngle(h)):3===u&&(s.startAngle=h,s.arc.startAngle(h))),t(e,n,r,i,a,s,u-2)})).on("end",_i(e,n,r,i,a)),f.enter().append("circle").attr("id","arc-"+l).attr("class","arc"),f.attr("cx",(function(t){return t[0]})).attr("cy",(function(t){return t[1]})).attr("r",5).style("opacity",(function(t,e){return void 0!==o&&e===o?.8:0})).on("mouseover",(function(){mt(this).style("opacity",.8)})).on("mouseout",(function(){mt(this).style("opacity",0)})).call(d)},Mi=function(t,e,n,r,i){return function(){var a=et,s=e.arcs[e.arcs.active];s.p2[0]=Math.min(Math.max(s.minX+1,a.x-n.margin.left),s.maxX),s.p2[1]=Math.min(Math.max(s.minY,a.y-n.margin.top),s.maxY),s.p3=s.p2.slice(),ki(t,e,n,r,i,s,1)}},Ai=function(t,e){var n=t[0]-e[0],r=t[1]-e[1],i=vi(n,r);return Math.asin(r/i)},Oi=function(t,e,n,r,i,a){return function(){r.g()||r.createAxes();var s=rr();e.arcs.active=void 0,e.arcs.width=function(t){var n=e.arcs[t];return void 0===n?void 0:n.maxX-n.minX},e.arcs.endAngle=function(t){return function(e){var n=t.arcs[e];if(void 0!==n){var r=-Ai(n.p1,n.p2)+Math.PI/2;return n.p1[0]>n.p2[0]&&(r=2*Math.PI-r),r}}}(e),e.arcs.startAngle=function(t){return function(e){var n=t.arcs[e];if(void 0!==n){var r=-Ai(n.p1,n.p3)+Math.PI/2;return n.p1[0]>n.p3[0]&&(r=2*Math.PI-r),r}}}(e),e.arcs.length=function(t){return function(e){var n=t.arcs[e];if(void 0!==n){var r=n.p1[0]-n.p2[0],i=n.p1[1]-n.p2[1];return vi(r,i)}}}(e),r.on("axesreorder.arcs",(function(){var a=Object.getOwnPropertyNames(arcs).filter((function(t){return!isNaN(t)}));a.length>0&&(a.forEach((function(t){var n=arcs[t].dims;e.arcs.active=t,mi(n)(n.left,n.right)||xi(e,r)})),_i(t,e,n,r,i)())})),r.selection.select("svg").append("g").attr("id","arcs").attr("transform","translate("+n.margin.left+","+n.margin.top+")"),r.brushReset=function(t,e,n,r,i){return function(){Object.getOwnPropertyNames(e.arcs).filter((function(t){return!isNaN(t)})).forEach((function(t){e.arcs.active=t,xi(e,r)})),_i(t,e,n,r,i)()}}(t,e,n,r,i),s.on("start",function(t,e,n,r){return function(){var i=vt(t.strumRect.node());i[0]=i[0]-e.margin.left,i[1]=i[1]-e.margin.top;var a=di(e,n,r,i),s={p1:i,dims:a,minX:r(a.left),maxX:r(a.right),minY:0,maxY:fi(e),startAngle:void 0,endAngle:void 0,arc:Mr().innerRadius(0)};s.p1[0]=Math.min(Math.max(s.minX,i[0]),s.maxX),s.p2=s.p1.slice(),s.p3=s.p1.slice(),t.arcs[a.i]=s,t.arcs.active=a.i}}(e,n,r,a)).on("drag",Mi(t,e,n,r,i)).on("end",_i(t,e,n,r,i)),e.strumRect=r.selection.select("svg").insert("rect","g#arcs").attr("id","arc-events").attr("x",n.margin.left).attr("y",n.margin.top).attr("width",Yr(n)).attr("height",fi(n)+2).style("opacity",0).call(s)}},Ni=function(t,e,n,r){return{x:((t.x*e.y-t.y*e.x)*(n.x-r.x)-(t.x-e.x)*(n.x*r.y-n.y*r.x))/((t.x-e.x)*(n.y-r.y)-(t.y-e.y)*(n.x-r.x)),y:((t.x*e.y-t.y*e.x)*(n.y-r.y)-(t.y-e.y)*(n.x*r.y-n.y*r.x))/((t.x-e.x)*(n.y-r.y)-(t.y-e.y)*(n.x-r.x))}},Ei=function(t,e,n){return function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(null===r)return t.mode;if(-1===n.brushModes().indexOf(r))throw new Error("pc.brushmode: Unsupported brush mode: "+r);return r!==t.mode&&("None"!==t.mode&&n.brushReset(),t.modes[t.mode].uninstall(n),t.mode=r,t.modes[t.mode].install(),"None"===r?delete n.brushPredicate:n.brushPredicate=function(t,e,n){return function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(null===r)return t.predicate;if("AND"!==(r=String(r).toUpperCase())&&"OR"!==r)throw new Error("Invalid predicate "+r);return t.predicate=r,e.brushed=t.currentMode().selected(),n.renderBrushed(),n}}(t,e,n)),n}},Si=function(t){return function(e){return t.dimensions[e].title?t.dimensions[e].title:e}},Pi=function(t,e,n){return function(r){e.flip(r),e.brushReset(r),e.selection.select("svg").selectAll("g.axis").filter((function(t){return t===r})).transition().duration(t.animationTime).call(n.scale(t.dimensions[r].yscale)),e.render()}},Ti=function(t,e){if(t.rotateLabels){var n=et.deltaY;n=(n=n<0?-5:n)>0?5:n,t.dimensionTitleRotation+=n,e.svg.selectAll("text.label").attr("transform","translate(0,-5) rotate("+t.dimensionTitleRotation+")"),et.preventDefault()}},Di=function(t){var e=t.height-t.margin.top-t.margin.bottom;return"bottom"==t.nullValueSeparator?[e+1-t.nullValueSeparatorPadding.bottom-t.nullValueSeparatorPadding.top,1]:"top"==t.nullValueSeparator?[e+1,1+t.nullValueSeparatorPadding.bottom+t.nullValueSeparatorPadding.top]:[e+1,1]},Ri=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},ji=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Ci=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},qi=function(t){return null!==t&&void 0!==t},Ii=function(t,e){var n=void 0;switch(e.orient){case"left":default:n=Lr(e.yscale);break;case"right":n=Br(e.yscale);break;case"top":n=Xr(e.yscale);break;case"bottom":n=Vr(e.yscale)}return n.ticks(e.ticks).tickValues(e.tickValues).tickSizeInner(e.innerTickSize).tickSizeOuter(e.outerTickSize).tickPadding(e.tickPadding).tickFormat(e.tickFormat),n},$i=function(t,e){if(t.brushed&&t.brushed.length!==t.data.length)return!0;var n=e.currentMode().brushState();for(var r in n)if(n.hasOwnProperty(r))return!0;return!1},zi=1e-6,Xi=function(){function t(e){Ri(this,t),this.setElements(e)}return ji(t,[{key:"e",value:function(t,e){return t<1||t>this.elements.length||e<1||e>this.elements[0].length?null:this.elements[t-1][e-1]}},{key:"row",value:function(t){return t>this.elements.length?null:new Bi(this.elements[t-1])}},{key:"col",value:function(t){if(0===this.elements.length)return null;if(t>this.elements[0].length)return null;for(var e=[],n=this.elements.length,r=0;r<n;r++)e.push(this.elements[r][t-1]);return new Bi(e)}},{key:"dimensions",value:function(){var t=0===this.elements.length?0:this.elements[0].length;return{rows:this.elements.length,cols:t}}},{key:"rows",value:function(){return this.elements.length}},{key:"cols",value:function(){return 0===this.elements.length?0:this.elements[0].length}},{key:"eql",value:function(e){var n=e.elements||e;if(n[0]&&"undefined"!==typeof n[0][0]||(n=new t(n).elements),0===this.elements.length||0===n.length)return this.elements.length===n.length;if(this.elements.length!==n.length)return!1;if(this.elements[0].length!==n[0].length)return!1;for(var r,i=this.elements.length,a=this.elements[0].length;i--;)for(r=a;r--;)if(Math.abs(this.elements[i][r]-n[i][r])>zi)return!1;return!0}},{key:"dup",value:function(){return new t(this.elements)}},{key:"map",value:function(e,n){if(0===this.elements.length)return new t([]);for(var r,i=[],a=this.elements.length,s=this.elements[0].length;a--;)for(r=s,i[a]=[];r--;)i[a][r]=e.call(n,this.elements[a][r],a+1,r+1);return new t(i)}},{key:"isSameSizeAs",value:function(e){var n=e.elements||e;return"undefined"===typeof n[0][0]&&(n=new t(n).elements),0===this.elements.length?0===n.length:this.elements.length===n.length&&this.elements[0].length===n[0].length}},{key:"add",value:function(e){if(0===this.elements.length)return this.map((function(t){return t}));var n=e.elements||e;return"undefined"===typeof n[0][0]&&(n=new t(n).elements),this.isSameSizeAs(n)?this.map((function(t,e,r){return t+n[e-1][r-1]})):null}},{key:"subtract",value:function(e){if(0===this.elements.length)return this.map((function(t){return t}));var n=e.elements||e;return"undefined"===typeof n[0][0]&&(n=new t(n).elements),this.isSameSizeAs(n)?this.map((function(t,e,r){return t-n[e-1][r-1]})):null}},{key:"canMultiplyFromLeft",value:function(e){if(0===this.elements.length)return!1;var n=e.elements||e;return"undefined"===typeof n[0][0]&&(n=new t(n).elements),this.elements[0].length===n.length}},{key:"multiply",value:function(e){if(0===this.elements.length)return null;if(!e.elements)return this.map((function(t){return t*e}));var n=!!e.modulus;if("undefined"===typeof(c=e.elements||e)[0][0]&&(c=new t(c).elements),!this.canMultiplyFromLeft(c))return null;for(var r,i,a,s=this.elements.length,o=c[0].length,u=this.elements[0].length,l=[];s--;)for(r=o,l[s]=[];r--;){for(i=u,a=0;i--;)a+=this.elements[s][i]*c[i][r];l[s][r]=a}var c=new t(l);return n?c.col(1):c}},{key:"minor",value:function(e,n,r,i){if(0===this.elements.length)return null;for(var a,s,o,u=[],l=r,c=this.elements.length,h=this.elements[0].length;l--;)for(u[a=r-l-1]=[],s=i;s--;)o=i-s-1,u[a][o]=this.elements[(e+a-1)%c][(n+o-1)%h];return new t(u)}},{key:"transpose",value:function(){if(0===this.elements.length)return new t([]);for(var e,n=this.elements.length,r=[],i=this.elements[0].length;i--;)for(e=n,r[i]=[];e--;)r[i][e]=this.elements[e][i];return new t(r)}},{key:"isSquare",value:function(){var t=0===this.elements.length?0:this.elements[0].length;return this.elements.length===t}},{key:"max",value:function(){if(0===this.elements.length)return null;for(var t,e=0,n=this.elements.length,r=this.elements[0].length;n--;)for(t=r;t--;)Math.abs(this.elements[n][t])>Math.abs(e)&&(e=this.elements[n][t]);return e}},{key:"indexOf",value:function(t){if(0===this.elements.length)return null;var e,n,r=this.elements.length,i=this.elements[0].length;for(e=0;e<r;e++)for(n=0;n<i;n++)if(this.elements[e][n]===t)return{i:e+1,j:n+1};return null}},{key:"diagonal",value:function(){if(!this.isSquare)return null;for(var t=[],e=this.elements.length,n=0;n<e;n++)t.push(this.elements[n][n]);return new Bi(t)}},{key:"toRightTriangular",value:function(){if(0===this.elements.length)return new t([]);var e,n,r,i,a=this.dup(),s=this.elements.length,o=this.elements[0].length;for(n=0;n<s;n++){if(0===a.elements[n][n])for(r=n+1;r<s;r++)if(0!==a.elements[r][n]){for(e=[],i=0;i<o;i++)e.push(a.elements[n][i]+a.elements[r][i]);a.elements[n]=e;break}if(0!==a.elements[n][n])for(r=n+1;r<s;r++){var u=a.elements[r][n]/a.elements[n][n];for(e=[],i=0;i<o;i++)e.push(i<=n?0:a.elements[r][i]-a.elements[n][i]*u);a.elements[r]=e}}return a}},{key:"determinant",value:function(){if(0===this.elements.length)return 1;if(!this.isSquare())return null;for(var t=this.toRightTriangular(),e=t.elements[0][0],n=t.elements.length,r=1;r<n;r++)e*=t.elements[r][r];return e}},{key:"isSingular",value:function(){return this.isSquare()&&0===this.determinant()}},{key:"trace",value:function(){if(0===this.elements.length)return 0;if(!this.isSquare())return null;for(var t=this.elements[0][0],e=this.elements.length,n=1;n<e;n++)t+=this.elements[n][n];return t}},{key:"rank",value:function(){if(0===this.elements.length)return 0;for(var t,e=this.toRightTriangular(),n=0,r=this.elements.length,i=this.elements[0].length;r--;)for(t=i;t--;)if(Math.abs(e.elements[r][t])>zi){n++;break}return n}},{key:"augment",value:function(e){if(0===this.elements.length)return this.dup();var n=e.elements||e;"undefined"===typeof n[0][0]&&(n=new t(n).elements);var r,i=this.dup(),a=i.elements[0].length,s=i.elements.length,o=n[0].length;if(s!==n.length)return null;for(;s--;)for(r=o;r--;)i.elements[s][a+r]=n[s][r];return i}},{key:"inverse",value:function(){if(0===this.elements.length)return null;if(!this.isSquare()||this.isSingular())return null;for(var e,n,r,i,a,s=this.elements.length,o=s,u=this.augment(t.I(s)).toRightTriangular(),l=u.elements[0].length,c=[];o--;){for(r=[],c[o]=[],i=u.elements[o][o],n=0;n<l;n++)a=u.elements[o][n]/i,r.push(a),n>=s&&c[o].push(a);for(u.elements[o]=r,e=o;e--;){for(r=[],n=0;n<l;n++)r.push(u.elements[e][n]-u.elements[o][n]*u.elements[e][o]);u.elements[e]=r}}return new t(c)}},{key:"round",value:function(){return this.map((function(t){return Math.round(t)}))}},{key:"snapTo",value:function(t){return this.map((function(e){return Math.abs(e-t)<=zi?t:e}))}},{key:"inspect",value:function(){var t=[],e=this.elements.length;if(0===e)return"[]";for(var n=0;n<e;n++)t.push(new Bi(this.elements[n]).inspect());return t.join("\n")}},{key:"setElements",value:function(t){var e,n,r=t.elements||t;if(r[0]&&"undefined"!==typeof r[0][0]){for(e=r.length,this.elements=[];e--;)for(n=r[e].length,this.elements[e]=[];n--;)this.elements[e][n]=r[e][n];return this}var i=r.length;for(this.elements=[],e=0;e<i;e++)this.elements.push([r[e]]);return this}},{key:"flatten",value:function(){var t=[];if(0==this.elements.length)return[];for(var e=0;e<this.elements[0].length;e++)for(var n=0;n<this.elements.length;n++)t.push(this.elements[n][e]);return t}},{key:"ensure4x4",value:function(){if(4==this.elements.length&&4==this.elements[0].length)return this;if(this.elements.length>4||this.elements[0].length>4)return null;for(var t=0;t<this.elements.length;t++)for(var e=this.elements[t].length;e<4;e++)t==e?this.elements[t].push(1):this.elements[t].push(0);for(t=this.elements.length;t<4;t++)0==t?this.elements.push([1,0,0,0]):1==t?this.elements.push([0,1,0,0]):2==t?this.elements.push([0,0,1,0]):3==t&&this.elements.push([0,0,0,1]);return this}},{key:"make3x3",value:function(){return 4!=this.elements.length||4!=this.elements[0].length?null:new t([[this.elements[0][0],this.elements[0][1],this.elements[0][2]],[this.elements[1][0],this.elements[1][1],this.elements[1][2]],[this.elements[2][0],this.elements[2][1],this.elements[2][2]]])}}]),t}();Xi.I=function(t){for(var e,n=[],r=t;r--;)for(e=t,n[r]=[];e--;)n[r][e]=r===e?1:0;return new Xi(n)},Xi.Diagonal=function(t){for(var e=t.length,n=Xi.I(e);e--;)n.elements[e][e]=t[e];return n},Xi.Rotation=function(t,e){if(!e)return new Xi([[Math.cos(t),-Math.sin(t)],[Math.sin(t),Math.cos(t)]]);var n=e.dup();if(3!==n.elements.length)return null;var r=n.modulus(),i=n.elements[0]/r,a=n.elements[1]/r,s=n.elements[2]/r,o=Math.sin(t),u=Math.cos(t),l=1-u;return new Xi([[l*i*i+u,l*i*a-o*s,l*i*s+o*a],[l*i*a+o*s,l*a*a+u,l*a*s-o*i],[l*i*s-o*a,l*a*s+o*i,l*s*s+u]])},Xi.RotationX=function(t){var e=Math.cos(t),n=Math.sin(t);return new Xi([[1,0,0],[0,e,-n],[0,n,e]])},Xi.RotationY=function(t){var e=Math.cos(t),n=Math.sin(t);return new Xi([[e,0,n],[0,1,0],[-n,0,e]])},Xi.RotationZ=function(t){var e=Math.cos(t),n=Math.sin(t);return new Xi([[e,-n,0],[n,e,0],[0,0,1]])},Xi.Random=function(t,e){return Xi.Zero(t,e).map((function(){return Math.random()}))},Xi.Translation=function(t){var e;if(2==t.elements.length)return(e=Xi.I(3)).elements[2][0]=t.elements[0],e.elements[2][1]=t.elements[1],e;if(3==t.elements.length)return(e=Xi.I(4)).elements[0][3]=t.elements[0],e.elements[1][3]=t.elements[1],e.elements[2][3]=t.elements[2],e;throw"Invalid length for Translation"},Xi.Zero=function(t,e){for(var n,r=[],i=t;i--;)for(n=e,r[i]=[];n--;)r[i][n]=0;return new Xi(r)},Xi.prototype.toUpperTriangular=Xi.prototype.toRightTriangular,Xi.prototype.det=Xi.prototype.determinant,Xi.prototype.tr=Xi.prototype.trace,Xi.prototype.rk=Xi.prototype.rank,Xi.prototype.inv=Xi.prototype.inverse,Xi.prototype.x=Xi.prototype.multiply;var Bi=function(){function t(e){Ri(this,t),this.setElements(e)}return ji(t,[{key:"e",value:function(t){return t<1||t>this.elements.length?null:this.elements[t-1]}},{key:"dimensions",value:function(){return this.elements.length}},{key:"modulus",value:function(){return Math.sqrt(this.dot(this))}},{key:"eql",value:function(t){var e=this.elements.length,n=t.elements||t;if(e!==n.length)return!1;for(;e--;)if(Math.abs(this.elements[e]-n[e])>zi)return!1;return!0}},{key:"dup",value:function(){return new t(this.elements)}},{key:"map",value:function(e,n){var r=[];return this.each((function(t,i){r.push(e.call(n,t,i))})),new t(r)}},{key:"forEach",value:function(t,e){for(var n=this.elements.length,r=0;r<n;r++)t.call(e,this.elements[r],r+1)}},{key:"toUnitVector",value:function(){var t=this.modulus();return 0===t?this.dup():this.map((function(e){return e/t}))}},{key:"angleFrom",value:function(t){var e=t.elements||t;if(this.elements.length!==e.length)return null;var n=0,r=0,i=0;if(this.each((function(t,a){n+=t*e[a-1],r+=t*t,i+=e[a-1]*e[a-1]})),r=Math.sqrt(r),i=Math.sqrt(i),r*i===0)return null;var a=n/(r*i);return a<-1&&(a=-1),a>1&&(a=1),Math.acos(a)}},{key:"isParallelTo",value:function(t){var e=this.angleFrom(t);return null===e?null:e<=zi}},{key:"isAntiparallelTo",value:function(t){var e=this.angleFrom(t);return null===e?null:Math.abs(e-Math.PI)<=zi}},{key:"isPerpendicularTo",value:function(t){var e=this.dot(t);return null===e?null:Math.abs(e)<=zi}},{key:"add",value:function(t){var e=t.elements||t;return this.elements.length!==e.length?null:this.map((function(t,n){return t+e[n-1]}))}},{key:"subtract",value:function(t){var e=t.elements||t;return this.elements.length!==e.length?null:this.map((function(t,n){return t-e[n-1]}))}},{key:"multiply",value:function(t){return this.map((function(e){return e*t}))}},{key:"dot",value:function(t){var e=t.elements||t,n=0,r=this.elements.length;if(r!==e.length)return null;for(;r--;)n+=this.elements[r]*e[r];return n}},{key:"cross",value:function(e){var n=e.elements||e;if(3!==this.elements.length||3!==n.length)return null;var r=this.elements;return new t([r[1]*n[2]-r[2]*n[1],r[2]*n[0]-r[0]*n[2],r[0]*n[1]-r[1]*n[0]])}},{key:"max",value:function(){for(var t=0,e=this.elements.length;e--;)Math.abs(this.elements[e])>Math.abs(t)&&(t=this.elements[e]);return t}},{key:"indexOf",value:function(t){for(var e=null,n=this.elements.length,r=0;r<n;r++)null===e&&this.elements[r]===t&&(e=r+1);return e}},{key:"toDiagonalMatrix",value:function(){return Xi.Diagonal(this.elements)}},{key:"round",value:function(){return this.map((function(t){return Math.round(t)}))}},{key:"snapTo",value:function(t){return this.map((function(e){return Math.abs(e-t)<=zi?t:e}))}},{key:"distanceFrom",value:function(t){if(t.anchor||t.start&&t.end)return t.distanceFrom(this);var e=t.elements||t;if(e.length!==this.elements.length)return null;var n,r=0;return this.each((function(t,i){n=t-e[i-1],r+=n*n})),Math.sqrt(r)}},{key:"liesOn",value:function(t){return t.contains(this)}},{key:"liesIn",value:function(t){return t.contains(this)}},{key:"rotate",value:function(e,n){var r,i,a,s,o=null;switch(e.determinant&&(o=e.elements),this.elements.length){case 2:return 2!==(r=n.elements||n).length?null:(o||(o=Xi.Rotation(e).elements),i=this.elements[0]-r[0],a=this.elements[1]-r[1],new t([r[0]+o[0][0]*i+o[0][1]*a,r[1]+o[1][0]*i+o[1][1]*a]));case 3:if(!n.direction)return null;var u=n.pointClosestTo(this).elements;return o||(o=Xi.Rotation(e,n.direction).elements),i=this.elements[0]-u[0],a=this.elements[1]-u[1],s=this.elements[2]-u[2],new t([u[0]+o[0][0]*i+o[0][1]*a+o[0][2]*s,u[1]+o[1][0]*i+o[1][1]*a+o[1][2]*s,u[2]+o[2][0]*i+o[2][1]*a+o[2][2]*s]);default:return null}}},{key:"reflectionIn",value:function(e){if(e.anchor){var n=this.elements.slice(),r=e.pointClosestTo(n).elements;return new t([r[0]+(r[0]-n[0]),r[1]+(r[1]-n[1]),r[2]+(r[2]-(n[2]||0))])}var i=e.elements||e;return this.elements.length!==i.length?null:this.map((function(t,e){return i[e-1]+(i[e-1]-t)}))}},{key:"to3D",value:function(){var t=this.dup();switch(t.elements.length){case 3:break;case 2:t.elements.push(0);break;default:return null}return t}},{key:"inspect",value:function(){return"["+this.elements.join(", ")+"]"}},{key:"setElements",value:function(t){return this.elements=(t.elements||t).slice(),this}},{key:"flatten",value:function(){return this.elements}}]),t}();Bi.Random=function(t){for(var e=[];t--;)e.push(Math.random());return new Bi(e)},Bi.Zero=function(t){for(var e=[];t--;)e.push(0);return new Bi(e)},Bi.prototype.x=Bi.prototype.multiply,Bi.prototype.each=Bi.prototype.forEach,Bi.i=new Bi([1,0,0]),Bi.j=new Bi([0,1,0]),Bi.k=new Bi([0,0,1]);var Vi=function(t,e,n,r){var i=function(t,e,n){for(var r=[],i=Object.keys(t.dimensions),a=i.length,s=0;s<a;++s){var o=e(i[s]),u=t.dimensions[i[s]].yscale(n[i[s]]);if(r.push(new Bi([o,u])),s<a-1){var l=o+.5*(e(i[s+1])-o),c=u+.5*(t.dimensions[i[s+1]].yscale(n[i[s+1]])-u);if(null!==t.bundleDimension){var h=.5*(t.clusterCentroids.get(t.dimensions[t.bundleDimension].yscale(n[t.bundleDimension])).get(i[s])+t.clusterCentroids.get(t.dimensions[t.bundleDimension].yscale(n[t.bundleDimension])).get(i[s+1]));c=h+(1-t.bundlingStrength)*(c-h)}r.push(new Bi([l,c]))}}return r}(t,e,n),a=function(t,e){var n=e.length,r=t,i=[];i.push(e[0]),i.push(new Bi([e[0].e(1)+2*r*(e[1].e(1)-e[0].e(1)),e[0].e(2)]));for(var a=1;a<n-1;++a){var s=e[a],o=e[a-1],u=e[a+1],l=o.subtract(u);i.push(s.add(l.x(r))),i.push(s),i.push(s.subtract(l.x(r)))}return i.push(new Bi([e[n-1].e(1)+2*r*(e[n-2].e(1)-e[n-1].e(1)),e[n-1].e(2)])),i.push(e[n-1]),i}(t.smoothness,i);r.moveTo(a[0].e(1),a[0].e(2));for(var s=1;s<a.length;s+=3){if(t.showControlPoints)for(var o=0;o<3;o++)r.fillRect(a[s+o].e(1),a[s+o].e(2),2,2);r.bezierCurveTo(a[s].e(1),a[s].e(2),a[s+1].e(1),a[s+1].e(2),a[s+2].e(1),a[s+2].e(2))}},Li=function(t){return"bottom"===t.nullValueSeparator?fi(t)+1:"top"===t.nullValueSeparator?1:(console.log("A value is NULL, but nullValueSeparator is not set; set it to 'bottom' or 'top'."),fi(t)+1)},Fi=function(t,e,n,r){r.beginPath(),null!==t.bundleDimension&&t.bundlingStrength>0||t.smoothness>0?Vi(t,e,n,r):function(t,e,n,r){Object.keys(t.dimensions).map((function(r){return[e(r),void 0===n[r]?Li(t):t.dimensions[r].yscale(n[r])]})).sort((function(t,e){return t[0]-e[0]})).forEach((function(t,e){0===e?r.moveTo(t[0],t[1]):r.lineTo(t[0],t[1])}))}(t,e,n,r),r.stroke()},Yi=function(t){return"function"===typeof t?t:function(){return t}},Hi=function(t,e,n){return function(r,i){return e.marked.strokeStyle=Yi(t.color)(r,i),Fi(t,n,r,e.marked)}},Ki=function(t,e,n){return function(r,i){return null!==t.brushedColor?e.brushed.strokeStyle=Yi(t.brushedColor)(r,i):e.brushed.strokeStyle=Yi(t.color)(r,i),Fi(t,n,r,e.brushed)}},Ui=function(t){return{}.toString.call(t).match(/\s([a-zA-Z]+)/)[1].toLowerCase()},Wi=function(t){for(var e=[],n=0;n<t.length-1;n++)e.push([t[n],t[n+1]]);return e},Gi=function(t,e,n,r,i,a){return function(){var s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null===s?t.highlighted:(t.highlighted=s,e.clear("highlight"),yt([n.foreground,n.brushed]).classed("faded",!0),s.forEach(function(t,e,n){return function(r,i){return e.highlight.strokeStyle=Yi(t.color)(r,i),Fi(t,n,r,e.highlight)}}(t,i,a)),r.call("highlight",this,s),this)}},Zi=function(t,e,n){return function(r,i){return e.foreground.strokeStyle=Yi(t.color)(r,i),Fi(t,n,r,e.foreground)}},Ji=function(t){return parseFloat(t)==t&&null!==t?"number":Ui(t)},Qi=function(t){return Object.keys(t[0]).reduce((function(e,n){return e[isNaN(Number(n))?n:parseInt(n)]=Ji(t[0][n]),e}),{})},ta={data:[],highlighted:[],marked:[],dimensions:{},dimensionTitleRotation:0,brushes:[],brushed:!1,brushedColor:null,alphaOnBrushed:0,lineWidth:1.4,highlightedLineWidth:3,mode:"default",markedLineWidth:3,markedShadowColor:"#ffffff",markedShadowBlur:10,rate:20,width:600,height:300,margin:{top:24,right:20,bottom:12,left:20},nullValueSeparator:"undefined",nullValueSeparatorPadding:{top:8,right:0,bottom:8,left:0},color:"#069",composite:"source-over",alpha:.7,bundlingStrength:.5,bundleDimension:null,smoothness:0,showControlPoints:!1,hideAxis:[],flipAxes:[],animationTime:1100,rotateLabels:!1},ea=function(t,e,n,r,i,a,s,o,u){return bt.A.apply(undefined,Object.keys(t)).on("composite",(function(t){e.foreground.globalCompositeOperation=t.value,e.brushed.globalCompositeOperation=t.value})).on("alpha",(function(t){e.foreground.globalAlpha=t.value,e.brushed.globalAlpha=t.value})).on("brushedColor",(function(t){e.brushed.strokeStyle=t.value})).on("width",(function(t){return n.resize()})).on("height",(function(t){return n.resize()})).on("margin",(function(t){return n.resize()})).on("rate",(function(t){s.rate(t.value),o.rate(t.value),u.rate(t.value)})).on("dimensions",(function(e){t.dimensions=n.applyDimensionDefaults(Object.keys(e.value)),r.domain(n.getOrderedDimensionKeys()),n.sortDimensions(),a.interactive&&n.render().updateAxes()})).on("bundleDimension",(function(e){Object.keys(t.dimensions).length||n.detectDimensions(),n.autoscale(),"number"===typeof e.value?e.value<Object.keys(t.dimensions).length?t.bundleDimension=t.dimensions[e.value]:e.value<t.hideAxis.length&&(t.bundleDimension=t.hideAxis[e.value]):t.bundleDimension=e.value,t.clusterCentroids=function(t,e){var n=new Map,r=new Map;return t.data.forEach((function(n){var i=t.dimensions[e].yscale(n[e]);r.has(i)||r.set(i,0);var a=r.get(i);r.set(i,a+1)})),t.data.forEach((function(i){Object.keys(t.dimensions).map((function(a){var s=t.dimensions[e].yscale(i[e]);if(!n.has(s)){var o=new Map;n.set(s,o)}n.get(s).has(a)||n.get(s).set(a,0);var u=n.get(s).get(a);u+=t.dimensions[a].yscale(i[a])/r.get(s),n.get(s).set(a,u)}))})),n}(t,t.bundleDimension),a.interactive&&n.render()})).on("hideAxis",(function(e){var r;n.brushReset(),n.dimensions(n.applyDimensionDefaults()),n.dimensions((r=t.dimensions,e.value.forEach((function(t){delete r[t]})),r)),n.render()})).on("flipAxes",(function(e){e.value&&e.value.length&&(e.value.forEach((function(e){Pi(t,n,i)(e)})),n.updateAxes(0))}))},na=function(t,e,n){return t[n]=function(t,e,n){return function(){var r=n.apply(e,arguments);return r===e?t:r}}(t,e,e[n]),t},ra=function(t,e,n,r,i,a,s,o,u,l){!function(t,e,n,r){Object.keys(e).forEach((function(i){t[i]=function(a){if(!arguments.length)return e[i];"dimensions"===i&&"[object Array]"===Object.prototype.toString.call(a)&&(console.warn("pc.dimensions([]) is deprecated, use pc.dimensions({})"),a=t.applyDimensionDefaults(a));var s=e[i];return e[i]=a,r.call(i,t,{value:a,previous:s}),n.call(i,t,{value:a,previous:s}),t}}))}(n,t,u,ea(t,e,n,r,l,i,a,s,o)),na(n,u,"on"),na(n,l,"ticks")},ia=function(t){var e=function(t){var e=Object.assign({},ta,t);t&&t.dimensionTitles&&(console.warn("dimensionTitles passed in userConfig is deprecated. Add title to dimension object."),(0,Pr.jO)(t.dimensionTitles).forEach((function(t){e.dimensions[t.key]?e.dimensions[t.key].title=e.dimensions[t.key].title?e.dimensions[t.key].title:t.value:e.dimensions[t.key]={title:t.value}})));var n=["render","resize","highlight","mark","brush","brushend","brushstart","axesreorder"].concat((0,Pr.HP)(e)),r=bt.A.apply(void 0,n),i=(0,Ar.hq)(),a=Lr().ticks(5);return{config:e,events:r,eventTypes:n,flags:{brushable:!1,reorderable:!1,axes:!1,interactive:!1,debug:!1},xscale:i,dragging:{},axis:a,ctx:{},canvas:{},brush:{modes:{None:{install:function(t){},uninstall:function(t){},selected:function(){return[]},brushState:function(){return{}}}},mode:"None",predicate:"AND",currentMode:function(){return this.modes[this.mode]}}}}(t),n=e.config,r=e.events,i=e.flags,a=e.xscale,s=e.dragging,o=e.axis,u=e.ctx,l=e.canvas,c=e.brush,h=function(t,e,n){return function r(i){return i=r.selection=mt(i),t.width=i.node().clientWidth,t.height=i.node().clientHeight,["dots","foreground","brushed","marked","highlight"].forEach((function(t){e[t]=i.append("canvas").attr("class",t).node(),n[t]=e[t].getContext("2d")})),r.svg=i.append("svg").attr("width",t.width).attr("height",t.height).style("font","14px sans-serif").style("position","absolute").append("svg:g").attr("transform","translate("+t.margin.left+","+t.margin.top+")"),r}}(n,l,u),f=function(t){return 0===a.range().length&&a.range([0,Yr(n)],1),null==s[t]?a(t):s[t]},d=Fr(Ki(n,u,f)).rate(50).clear((function(){return h.clear("brushed")})),m=Fr(Hi(n,u,f)).rate(50).clear((function(){return h.clear("marked")})),p=Fr(Zi(n,u,f)).rate(50).clear((function(){h.clear("foreground"),h.clear("highlight")}));return ra(n,u,h,a,i,d,m,p,r,o),h.state=n,h.flags=i,h.autoscale=function(t,e,n,r){return function(){var i={date:function(e){var n=Er(t.data,(function(t){return t[e]?t[e].getTime():null}));return n[0]===n[1]?(0,Ar.hq)().domain(n).range(Di(t)):(t.flipAxes.includes(e)&&(n=n.map((function(t){return tempDate.unshift(t)}))),(0,Ar.w7)().domain(n).range(Di(t)))},number:function(e){var n=Er(t.data,(function(t){return+t[e]}));return n[0]===n[1]?(0,Ar.hq)().domain(n).range(Di(t)):(t.flipAxes.includes(e)&&(n=n.map((function(t){return tempDate.unshift(t)}))),(0,Ar.m4)().domain(n).range(Di(t)))},string:function(e){var n={},r=[];if(t.data.map((function(r){if(void 0===r[e]&&"undefined"!==t.nullValueSeparator)return null;void 0===n[r[e]]?n[r[e]]=1:n[r[e]]=n[r[e]]+1})),t.flipAxes.includes(e))r=Object.getOwnPropertyNames(n).sort();else for(var i=Object.getOwnPropertyNames(n).sort(),a=0;a<Object.getOwnPropertyNames(n).length;a++)r.push(i.pop());var s=[];1===r.length&&(r=[" ",r[0]," "]);for(var o=Di(t)[0]/(r.length-1),u=0;u<r.length;u++)0!==s.length?s.push(s[u-1]+o):s.push(0);return(0,Ar.UM)().domain(r).range(s)}};Object.keys(t.dimensions).forEach((function(e){void 0!==t.dimensions[e].yscale&&null!==t.dimensions[e].yscale||(t.dimensions[e].yscale=i[t.dimensions[e].type](e))})),n.range([0,Yr(t)]).padding(.2);var a=window.devicePixelRatio||1;return e.selection.selectAll("canvas").style("margin-top",t.margin.top+"px").style("margin-left",t.margin.left+"px").style("width",Yr(t)+2+"px").style("height",fi(t)+2+"px").attr("width",(Yr(t)+2)*a).attr("height",(fi(t)+2)*a),r.foreground.strokeStyle=t.color,r.foreground.lineWidth=t.lineWidth,r.foreground.globalCompositeOperation=t.composite,r.foreground.globalAlpha=t.alpha,r.foreground.scale(a,a),r.brushed.strokeStyle=t.brushedColor,r.brushed.lineWidth=t.lineWidth,r.brushed.globalCompositeOperation=t.composite,r.brushed.globalAlpha=t.alpha,r.brushed.scale(a,a),r.highlight.lineWidth=t.highlightedLineWidth,r.highlight.scale(a,a),r.marked.lineWidth=t.markedLineWidth,r.marked.shadowColor=t.markedShadowColor,r.marked.shadowBlur=t.markedShadowBlur,r.marked.scale(a,a),this}}(n,h,a,u),h.scale=function(t,e){return function(n,r){return t.dimensions[n].yscale.domain(r),e.render.default(),e.updateAxes(),this}}(n,h),h.flip=function(t){return function(e){return t.dimensions[e].yscale.domain(t.dimensions[e].yscale.domain().reverse()),this}}(n),h.commonScale=function(t,e){return function(n,r){var i=r||"number";"undefined"===typeof n&&(n=!0),Object.keys(t.dimensions).length||e.detectDimensions(),e.autoscale();var a=Object.keys(t.dimensions).filter((function(e){return t.dimensions[e].type==i}));if(n){var s=Er(a.map((function(e){return t.dimensions[e].yscale.domain()})).reduce((function(t,e){return t.concat(e)})));a.forEach((function(e){t.dimensions[e].yscale.domain(s)}))}else a.forEach((function(e){t.dimensions[e].yscale.domain(Er(t.data,(function(t){return+t[k]})))}));return null!==t.bundleDimension&&e.bundleDimension(t.bundleDimension),this}}(n,h),h.detectDimensions=function(t){return function(){return t.dimensions(t.applyDimensionDefaults()),this}}(h),h.detectDimensionTypes=Qi,h.applyDimensionDefaults=function(t,e){return function(n){var r=e.detectDimensionTypes(t.data);return(n=n||Object.keys(r)).reduce((function(e,n,i){var a=t.dimensions[n]?t.dimensions[n]:{};return e[n]=Ci({},a,{orient:qi(a.orient)?a.orient:"left",ticks:qi(a.ticks)?a.ticks:5,innerTickSize:qi(a.innerTickSize)?a.innerTickSize:6,outerTickSize:qi(a.outerTickSize)?a.outerTickSize:0,tickPadding:qi(a.tickPadding)?a.tickPadding:3,type:qi(a.type)?a.type:r[n],index:qi(a.index)?a.index:i}),e}),{})}}(n,h),h.getOrderedDimensionKeys=function(t){return function(){return Object.keys(t.dimensions).sort((function(e,n){return Or(t.dimensions[e].index,t.dimensions[n].index)}))}}(n),h.render=function(t,e,n){return function(){return Object.keys(t.dimensions).length||e.detectDimensions(),e.autoscale(),e.render[t.mode](),n.call("render",this),this}}(n,h,r),h.renderBrushed=function(t,e,n){return function(){return Object.keys(t.dimensions).length||e.detectDimensions(),e.renderBrushed[t.mode](),n.call("render",this),this}}(n,h,r),h.renderMarked=function(t,e,n){return function(){return Object.keys(t.dimensions).length||e.detectDimensions(),e.renderMarked[t.mode](),n.call("render",this),this}}(n,h,r),h.render.default=function(t,e,n,r){return function(){e.clear("foreground"),e.clear("highlight"),e.renderBrushed.default(),e.renderMarked.default(),t.data.forEach(Zi(t,n,r))}}(n,h,u,f),h.render.queue=function(t,e,n){return function(){e.renderBrushed.queue(),e.renderMarked.queue(),n(t.data)}}(n,h,p),h.renderBrushed.default=function(t,e,n,r,i){return function(){r.clear("brushed"),$i(t,i)&&!1!==t.brushed&&t.brushed.forEach(Ki(t,e,n))}}(n,u,f,h,c),h.renderBrushed.queue=function(t,e,n){return function(){$i(t,e)?n(t.brushed):n([])}}(n,c,d),h.renderMarked.default=function(t,e,n,r){return function(){e.clear("marked"),t.marked.length&&t.marked.forEach(Hi(t,n,r))}}(n,h,u,f),h.renderMarked.queue=function(t,e){return function(){t.marked?e(t.marked):e([])}}(n,m),h.compute_real_centroids=function(t,e){return function(n){return Object.keys(t.dimensions).map((function(r){return[e(r),t.dimensions[r].yscale(n[r])]}))}}(n,f),h.shadows=function(t,e){return function(){return t.shadows=!0,e.alphaOnBrushed(.1),e.render(),this}}(i,h),h.axisDots=function(t,e,n){return function(r){var i=r||.1,a=e.ctx.dots,s=2*Math.PI;a.globalAlpha=function(t,e){var n,r,i=t.length,a=-1;if(null==e){for(;++a<i;)if(null!=(n=t[a])&&n>=n)for(r=n;++a<i;)null!=(n=t[a])&&r>n&&(r=n)}else for(;++a<i;)if(null!=(n=e(t[a],a,t))&&n>=n)for(r=n;++a<i;)null!=(n=e(t[a],a,t))&&r>n&&(r=n);return r}([1/Math.pow(t.data.length,.5),1]),t.data.forEach((function(e){(0,Pr.jO)(t.dimensions).forEach((function(r,o){a.beginPath(),a.arc(n(r),t.dimensions[r.key].yscale(e[r]),i,0,s),a.stroke(),a.fill()}))}))}}(n,h,f),h.clear=function(t,e,n,r){return function(i){return n[i].clearRect(0,0,Yr(t)+2,fi(t)+2),"brushed"===i&&$i(t,r)&&(n.brushed.fillStyle=e.selection.style("background-color"),n.brushed.globalAlpha=1-t.alphaOnBrushed,n.brushed.fillRect(0,0,Yr(t)+2,fi(t)+2),n.brushed.globalAlpha=t.alpha),this}}(n,h,u,c),h.createAxes=function(t,e,n,r,i){return function(){return void 0!==e.g()&&e.removeAxes(),e._g=e.svg.selectAll(".dimension").data(e.getOrderedDimensionKeys(),(function(t){return t})).enter().append("svg:g").attr("class","dimension").attr("transform",(function(t){return"translate("+n(t)+")"})),e._g.append("svg:g").attr("class","axis").attr("transform","translate(0,0)").each((function(n){var r=mt(this).call(e.applyAxisConfig(i,t.dimensions[n]));r.selectAll("path").style("fill","none").style("stroke","#222").style("shape-rendering","crispEdges"),r.selectAll("line").style("fill","none").style("stroke","#222").style("shape-rendering","crispEdges")})).append("svg:text").attr("text-anchor","middle").attr("y",0).attr("transform","translate(0,-5) rotate("+t.dimensionTitleRotation+")").attr("x",0).attr("class","label").text(Si(t)).on("dblclick",Pi(t,e,i)).on("wheel",Ti(t,e)),"top"===t.nullValueSeparator?e.svg.append("line").attr("x1",0).attr("y1",1+t.nullValueSeparatorPadding.top).attr("x2",Yr(t)).attr("y2",1+t.nullValueSeparatorPadding.top).attr("stroke-width",1).attr("stroke","#777").attr("fill","none").attr("shape-rendering","crispEdges"):"bottom"===t.nullValueSeparator&&e.svg.append("line").attr("x1",0).attr("y1",fi(t)+1-t.nullValueSeparatorPadding.bottom).attr("x2",Yr(t)).attr("y2",fi(t)+1-t.nullValueSeparatorPadding.bottom).attr("stroke-width",1).attr("stroke","#777").attr("fill","none").attr("shape-rendering","crispEdges"),r.axes=!0,this}}(n,h,a,i,o),h.removeAxes=function(t){return function(){return t._g.remove(),delete t._g,this}}(h),h.updateAxes=function(t,e,n,r,i){return function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;null===a&&(a=t.animationTime);var s=e.svg.selectAll(".dimension").data(e.getOrderedDimensionKeys());if(s.enter().append("svg:g").attr("class","dimension").attr("transform",(function(t){return"translate("+n(t)+")"})).style("opacity",0).append("svg:g").attr("class","axis").attr("transform","translate(0,0)").each((function(n){var i=mt(this).call(e.applyAxisConfig(r,t.dimensions[n]));i.selectAll("path").style("fill","none").style("stroke","#222").style("shape-rendering","crispEdges"),i.selectAll("line").style("fill","none").style("stroke","#222").style("shape-rendering","crispEdges")})).append("svg:text").attr("text-anchor","middle").attr("class","label").attr("x",0).attr("y",0).attr("transform","translate(0,-5) rotate("+t.dimensionTitleRotation+")").text(Si(t)).on("dblclick",Pi(t,e,r)).on("wheel",Ti(t,e)),s.attr("opacity",0),s.select(".axis").transition().duration(a).each((function(n){mt(this).call(e.applyAxisConfig(r,t.dimensions[n]))})),s.select(".label").transition().duration(a).text(Si(t)).attr("transform","translate(0,-5) rotate("+t.dimensionTitleRotation+")"),s.exit().remove(),e.svg.selectAll(".dimension").transition().duration(a).attr("transform",(function(t){return"translate("+n(t)+")"})).style("opacity",1),e.svg.selectAll(".axis").transition().duration(a).each((function(n){mt(this).call(e.applyAxisConfig(r,t.dimensions[n]))})),i.brushable&&e.brushable(),i.reorderable&&e.reorderable(),"None"!==e.brushMode()){var o=e.brushMode();e.brushMode("None"),e.brushMode(o)}}}(n,h,f,o,i),h.applyAxisConfig=Ii,h.brushable=function(t,e,n){return function(){return e.g()||e.createAxes(),e.g().append("svg:g").attr("class","brush").each((function(n){void 0!==t.dimensions[n]&&(t.dimensions[n].brush=Wn(mt(this)).extent([[-15,0],[15,t.dimensions[n].yscale.range()[0]]]),mt(this).call(t.dimensions[n].brush.on("start",(function(){null===et.sourceEvent||et.sourceEvent.ctrlKey||e.brushReset()})).on("brush",(function(){et.sourceEvent.ctrlKey||e.brush()})).on("end",(function(){if(et.sourceEvent.ctrlKey){var r=mt(this).select(".selection").nodes()[0].outerHTML;r=r.replace('class="selection"','class="selection dummy selection-'+t.brushes.length+'"');var i=mt(this).nodes()[0].__data__,a={id:t.brushes.length,extent:Un(this),html:r,data:i};t.brushes.push(a),mt(mt(this).nodes()[0].parentNode).select(".axis").nodes()[0].outerHTML+=r,e.brush(),t.dimensions[n].brush.move(mt(this)),mt(this).select(".selection").attr("style","display:none"),e.brushable()}else e.brush()}))),mt(this).on("dblclick",(function(){e.brushReset(n)})))})),n.brushable=!0,this}}(n,h,i),h.brushReset=function(t,e){return function(n){for(var r=[],i=0;i<t.brushes.length;i++)t.brushes[i].data!==n&&r.push(t.brushes[i]);if(t.brushes=r,t.brushed=!1,void 0!==e.g())for(var a=e.g().selectAll(".brush").nodes(),s=0;s<a.length;s++)a[s].__data__===n&&(mt(mt(a[s]).nodes()[0].parentNode).selectAll(".dummy").remove(),t.dimensions[n].brush.move(mt(a[s])));return this}}(n,h),h.selected=function(t,e){return function(){var n=[],r=[],i={};if(0===t.brushes.length){for(var a=e.g().selectAll(".brush").nodes(),s=0;s<a.length;s++)if(null!==Un(a[s])){n.push(a[s].__data__);var o=[],u=Un(a[s]);if("number"===typeof t.dimensions[a[s].__data__].yscale.domain()[0]){for(var l=0;l<u.length;l++)n.includes(a[s].__data__)&&t.flipAxes.includes(a[s].__data__)?o.push(t.dimensions[a[s].__data__].yscale.invert(u[l])):1!==t.dimensions[a[s].__data__].yscale()&&o.unshift(t.dimensions[a[s].__data__].yscale.invert(u[l]));r.push(o);for(var c=0;c<r.length;c++)0===r[c].length&&(r[c]=[1,1])}else{i[a[s].__data__]=Un(a[s]);for(var h=t.dimensions[a[s].__data__].yscale.range(),f=(t.dimensions[a[s].__data__].yscale.domain(),0);f<h.length;f++)h[f]>=u[0]&&h[f]<=u[1]&&n.includes(a[s].__data__)&&t.flipAxes.includes(a[s].__data__)?o.push(h[f]):h[f]>=u[0]&&h[f]<=u[1]&&o.unshift(h[f]);r.push(o);for(var d=0;d<r.length;d++)0===r[d].length&&(r[d]=[1,1])}}var m={date:function(e,n,r){var a=e[n],s=t.dimensions[n].yscale.domain().indexOf(a),o=t.dimensions[n].yscale.range()[s];return o>=i[n][0]&&o<=i[n][1]},number:function(t,e,n){return r[n][0]<=t[e]&&t[e]<=r[n][1]},string:function(e,n,r){var a=e[n],s=t.dimensions[n].yscale.domain().indexOf(a),o=t.dimensions[n].yscale.range()[s];return o>=i[n][0]&&o<=i[n][1]}};return t.data.filter((function(e){return n.every((function(n,r){return m[t.dimensions[n].type](e,n,r)}))}))}for(var p=[],g=function(e){var n=t.brushes[e],a=[],s=n.extent,o=[n.data];if("number"===typeof t.dimensions[n.data].yscale.domain()[0]){for(var u=0;u<s.length;u++)o.includes(n.data)&&t.flipAxes.includes(n.data)?a.push(t.dimensions[n.data].yscale.invert(s[u])):1!==t.dimensions[n.data].yscale()&&a.unshift(t.dimensions[n.data].yscale.invert(s[u]));r.push(a);for(var l=0;l<r.length;l++)0===r[l].length&&(r[l]=[1,1])}else{i[n.data]=n.extent;for(var c=t.dimensions[n.data].yscale.range(),h=(t.dimensions[n.data].yscale.domain(),0);h<c.length;h++)c[h]>=s[0]&&c[h]<=s[1]&&o.includes(n.data)&&t.flipAxes.includes(n.data)?a.push(c[h]):c[h]>=s[0]&&c[h]<=s[1]&&a.unshift(c[h]);r.push(a);for(var f=0;f<r.length;f++)0===r[f].length&&(r[f]=[1,1])}for(var d={date:function(e,n,r){var a=e[n],s=t.dimensions[n].yscale.domain().indexOf(a),o=t.dimensions[n].yscale.range()[s];return o>=i[n][0]&&o<=i[n][1]},number:function(t,n,i){return r[e][0]<=t[n]&&t[n]<=r[e][1]},string:function(e,n,r){var a=e[n],s=t.dimensions[n].yscale.domain().indexOf(a),o=t.dimensions[n].yscale.range()[s];return o>=i[n][0]&&o<=i[n][1]}},m=t.data.filter((function(e){return o.every((function(n,r){return d[t.dimensions[n].type](e,n,r)}))})),g=0;g<m.length;g++)p.push(m[g]);o=[],i={}},v=0;v<t.brushes.length;v++)g(v);return p}}(n,h),h.reorderable=function(t,e,n,r,i,a){return function(){void 0===e.g()&&e.createAxes();var s=e.g();return s.style("cursor","move").call(rr().on("start",(function(t){i[t]=this.__origin__=n(t)})).on("drag",(function(a){i[a]=Math.min(Yr(t),Math.max(0,this.__origin__+=et.dx)),e.sortDimensions(),n.domain(e.getOrderedDimensionKeys()),e.render(),s.attr("transform",(function(t){return"translate("+r(t)+")"}))})).on("end",(function(t){delete this.__origin__,delete i[t],mt(this).transition().attr("transform","translate("+n(t)+")"),e.render(),e.renderMarked()}))),a.reorderable=!0,this}}(n,h,a,f,s,i),h.reorder=function(t,e,n){return function(r){var i=e.getOrderedDimensionKeys()[0];if(e.sortDimensionsByRowData(r),i!==e.getOrderedDimensionKeys()[0]){n.domain(e.getOrderedDimensionKeys());var a=t.highlighted.slice(0);e.unhighlight();var s=t.marked.slice(0);e.unmark(),e.g().transition().duration(1500).attr("transform",(function(t){return"translate("+n(t)+")"})),e.render(),0!==a.length&&e.highlight(a),0!==s.length&&e.mark(s)}}}(n,h,a),h.sortDimensionsByRowData=function(t){return function(e){var n=Object.assign({},t.dimensions),r=Object.keys(t.dimensions).sort((function(n,r){var i=t.dimensions[n].yscale(e[n])-t.dimensions[r].yscale(e[r]);return 0===i?n.localeCompare(r):i}));t.dimensions={},r.forEach((function(e,r){t.dimensions[e]=n[e],t.dimensions[e].index=r}))}}(n),h.sortDimensions=function(t,e){return function(){var n=Object.assign({},t.dimensions),r=Object.keys(t.dimensions).sort((function(t,n){return e(t)-e(n)===0?1:e(t)-e(n)}));t.dimensions={},r.forEach((function(e,r){t.dimensions[e]=n[e],t.dimensions[e].index=r}))}}(n,f),h.adjacent_pairs=Wi,h.interactive=function(t){return function(){return t.interactive=!0,this}}(i),h.xscale=a,h.ctx=u,h.canvas=l,h.g=function(){return h._g},h.resize=function(t,e,n,r){return function(){return e.selection.select("svg").attr("width",t.width).attr("height",t.height),e.svg.attr("transform","translate("+t.margin.left+","+t.margin.top+")"),n.brushable&&e.brushReset(),e.autoscale(),e.g()&&e.createAxes(),n.brushable&&e.brushable(),n.reorderable&&e.reorderable(),r.call("resize",this,{width:t.width,height:t.height,margin:t.margin}),this}}(n,h,i,r),h.highlight=Gi(n,h,l,r,u,f),h.unhighlight=function(t,e,n){return function(){return t.highlighted=[],e.clear("highlight"),yt([n.foreground,n.brushed]).classed("faded",!1),this}}(n,h,l),h.mark=function(t,e,n,r,i,a){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return null===e?t.marked:(t.marked=t.marked.concat(e),yt([n.foreground,n.brushed]).classed("dimmed",!0),e.forEach(Hi(t,i,a)),r.call("mark",this,e),this)}}(n,0,l,r,u,f),h.unmark=function(t,e,n){return function(){return t.marked=[],e.clear("marked"),yt([n.foreground,n.brushed]).classed("dimmed",!1),this}}(n,h,l),h.intersection=Ni,h.mergeParcoords=function(t){return function(e){var n=window.devicePixelRatio||1,r=document.createElement("canvas"),i=t.canvas.foreground,a=Number(i.style.marginLeft.replace("px","")),s=Number(i.style.marginTop.replace("px",""))+15,o=(i.clientWidth+a)*n,u=(i.clientHeight+s)*n;r.width=o+50,r.height=u+30,r.style.width=r.width/n+"px",r.style.height=r.height/n+"px";var l=r.getContext("2d");for(var c in l.fillStyle="#ffffff",l.fillRect(0,0,r.width,r.height),t.canvas)l.drawImage(t.canvas[c],a*n,s*n,o-a*n,u-s*n);window.URL||window.webkitURL||window;var h=new XMLSerializer,f=t.selection.select("svg").node().cloneNode(!0);f.setAttribute("transform","translate(0,15)"),f.setAttribute("height",f.getAttribute("height")+15),mt(f).selectAll("text").attr("fill","black");var d=h.serializeToString(f),m="data:image/svg+xml;base64,"+window.btoa(d),p=new Image;p.onload=function(){l.drawImage(p,0,0,p.width*n,p.height*n),"function"===typeof e&&e(r)},p.src=m}}(h),h.brushModes=function(){return Object.getOwnPropertyNames(c.modes)},h.brushMode=Ei(c,n,h),function(t,e,n,r){var i={brushes:{},brushNodes:{}};t.modes["1D-axes"]={install:Gr(i,e,n,r,t),uninstall:Zr(i,n),selected:Ur(i,e,t),brushState:Kr(i,e,n)}}(c,n,h,r),function(t,e,n,r,i){var a={strums:{},strumRect:{}};t.modes["2D-strums"]={install:pi(t,a,e,n,r,i),uninstall:ai(a,n),selected:oi(t,a,e),brushState:function(){return a.strums}}}(c,n,h,r,a),function(t,e,n,r,i){var a={arcs:{},strumRect:{}};t.modes.angular={install:Oi(t,a,e,n,r,i),uninstall:gi(a,n),selected:wi(t,a,e),brushState:function(){return a.arcs}}}(c,n,h,r,a),function(t,e,n,r){var i={brushes:{},brushNodes:{}};t.modes["1D-axes-multi"]={install:ri(i,e,n,r,t),uninstall:ii(i,n),selected:Qr(i,e,t),brushState:ni(i,e,n)}}(c,n,h,r),h.version="2.2.10",h.toString=function(t){return function(){return"Parallel Coordinates: "+Object.keys(t.dimensions).length+" dimensions ("+Object.keys(t.data[0]).length+" total) , "+t.data.length+" rows"}}(n),h.toType=Ui,h.toTypeCoerceNumbers=Ji,h}},70807:function(t,e,n){"use strict";function r(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}function i(t){var e;return 1===t.length&&(e=t,t=function(t,n){return r(e(t),n)}),{left:function(e,n,r,i){for(null==r&&(r=0),null==i&&(i=e.length);r<i;){var a=r+i>>>1;t(e[a],n)<0?r=a+1:i=a}return r},right:function(e,n,r,i){for(null==r&&(r=0),null==i&&(i=e.length);r<i;){var a=r+i>>>1;t(e[a],n)>0?i=a:r=a+1}return r}}}n.d(e,{m4:function(){return V},UM:function(){return x},hq:function(){return M},ex:function(){return ht},w7:function(){return ut}});var a=i(r),s=a.right,o=(a.left,s),u=Array.prototype,l=(u.slice,u.map,Math.sqrt(50)),c=Math.sqrt(10),h=Math.sqrt(2);function f(t,e,n){var r=(e-t)/Math.max(0,n),i=Math.floor(Math.log(r)/Math.LN10),a=r/Math.pow(10,i);return i>=0?(a>=l?10:a>=c?5:a>=h?2:1)*Math.pow(10,i):-Math.pow(10,-i)/(a>=l?10:a>=c?5:a>=h?2:1)}function d(t,e,n){var r=Math.abs(e-t)/Math.max(0,n),i=Math.pow(10,Math.floor(Math.log(r)/Math.LN10)),a=r/i;return a>=l?i*=10:a>=c?i*=5:a>=h&&(i*=2),e<t?-i:i}function m(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function p(t,e){switch(arguments.length){case 0:break;case 1:this.interpolator(t);break;default:this.interpolator(e).domain(t)}return this}var g=n(25503),v=Array.prototype,y=v.map,b=v.slice,w={name:"implicit"};function x(){var t=(0,g.Tj)(),e=[],n=[],r=w;function i(i){var a=i+"",s=t.get(a);if(!s){if(r!==w)return r;t.set(a,s=e.push(i))}return n[(s-1)%n.length]}return i.domain=function(n){if(!arguments.length)return e.slice();e=[],t=(0,g.Tj)();for(var r,a,s=-1,o=n.length;++s<o;)t.has(a=(r=n[s])+"")||t.set(a,e.push(r));return i},i.range=function(t){return arguments.length?(n=b.call(t),i):n.slice()},i.unknown=function(t){return arguments.length?(r=t,i):r},i.copy=function(){return x(e,n).unknown(r)},m.apply(i,arguments),i}function _(){var t,e,n=x().unknown(void 0),r=n.domain,i=n.range,a=[0,1],s=!1,o=0,u=0,l=.5;function c(){var n=r().length,c=a[1]<a[0],h=a[c-0],f=a[1-c];t=(f-h)/Math.max(1,n-o+2*u),s&&(t=Math.floor(t)),h+=(f-h-t*(n-o))*l,e=t*(1-o),s&&(h=Math.round(h),e=Math.round(e));var d=function(t,e,n){t=+t,e=+e,n=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+n;for(var r=-1,i=0|Math.max(0,Math.ceil((e-t)/n)),a=new Array(i);++r<i;)a[r]=t+r*n;return a}(n).map((function(e){return h+t*e}));return i(c?d.reverse():d)}return delete n.unknown,n.domain=function(t){return arguments.length?(r(t),c()):r()},n.range=function(t){return arguments.length?(a=[+t[0],+t[1]],c()):a.slice()},n.rangeRound=function(t){return a=[+t[0],+t[1]],s=!0,c()},n.bandwidth=function(){return e},n.step=function(){return t},n.round=function(t){return arguments.length?(s=!!t,c()):s},n.padding=function(t){return arguments.length?(o=Math.min(1,u=+t),c()):o},n.paddingInner=function(t){return arguments.length?(o=Math.min(1,t),c()):o},n.paddingOuter=function(t){return arguments.length?(u=+t,c()):u},n.align=function(t){return arguments.length?(l=Math.max(0,Math.min(1,t)),c()):l},n.copy=function(){return _(r(),a).round(s).paddingInner(o).paddingOuter(u).align(l)},m.apply(c(),arguments)}function k(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return k(e())},t}function M(){return k(_.apply(null,arguments).paddingInner(1))}var A=n(93004),O=n(50608);function N(t,e){return t=+t,e=+e,function(n){return Math.round(t*(1-n)+e*n)}}function E(t){return+t}var S=[0,1];function P(t){return t}function T(t,e){return(e-=t=+t)?function(n){return(n-t)/e}:(n=isNaN(e)?NaN:.5,function(){return n});var n}function D(t){var e,n=t[0],r=t[t.length-1];return n>r&&(e=n,n=r,r=e),function(t){return Math.max(n,Math.min(r,t))}}function R(t,e,n){var r=t[0],i=t[1],a=e[0],s=e[1];return i<r?(r=T(i,r),a=n(s,a)):(r=T(r,i),a=n(a,s)),function(t){return a(r(t))}}function j(t,e,n){var r=Math.min(t.length,e.length)-1,i=new Array(r),a=new Array(r),s=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++s<r;)i[s]=T(t[s],t[s+1]),a[s]=n(e[s],e[s+1]);return function(e){var n=o(t,e,1,r)-1;return a[n](i[n](e))}}function C(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function q(t,e){return function(){var t,e,n,r,i,a,s=S,o=S,u=A.A,l=P;function c(){return r=Math.min(s.length,o.length)>2?j:R,i=a=null,h}function h(e){return isNaN(e=+e)?n:(i||(i=r(s.map(t),o,u)))(t(l(e)))}return h.invert=function(n){return l(e((a||(a=r(o,s.map(t),O.A)))(n)))},h.domain=function(t){return arguments.length?(s=y.call(t,E),l===P||(l=D(s)),c()):s.slice()},h.range=function(t){return arguments.length?(o=b.call(t),c()):o.slice()},h.rangeRound=function(t){return o=b.call(t),u=N,c()},h.clamp=function(t){return arguments.length?(l=t?D(s):P,h):l!==P},h.interpolate=function(t){return arguments.length?(u=t,c()):u},h.unknown=function(t){return arguments.length?(n=t,h):n},function(n,r){return t=n,e=r,c()}}()(t,e)}var I=n(29634),$=n(17364);var z=n(12724);function X(t,e,n,r){var i,a=d(t,e,n);switch((r=(0,I.A)(null==r?",f":r)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(e));return null!=r.precision||isNaN(i=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor((0,$.A)(e)/3)))-(0,$.A)(Math.abs(t)))}(a,s))||(r.precision=i),(0,z.s)(r,s);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,(0,$.A)(e)-(0,$.A)(t))+1}(a,Math.max(Math.abs(t),Math.abs(e))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=function(t){return Math.max(0,-(0,$.A)(Math.abs(t)))}(a))||(r.precision=i-2*("%"===r.type))}return(0,z.GP)(r)}function B(t){var e=t.domain;return t.ticks=function(t){var n=e();return function(t,e,n){var r,i,a,s,o=-1;if(n=+n,(t=+t)===(e=+e)&&n>0)return[t];if((r=e<t)&&(i=t,t=e,e=i),0===(s=f(t,e,n))||!isFinite(s))return[];if(s>0)for(t=Math.ceil(t/s),e=Math.floor(e/s),a=new Array(i=Math.ceil(e-t+1));++o<i;)a[o]=(t+o)*s;else for(t=Math.floor(t*s),e=Math.ceil(e*s),a=new Array(i=Math.ceil(t-e+1));++o<i;)a[o]=(t-o)/s;return r&&a.reverse(),a}(n[0],n[n.length-1],null==t?10:t)},t.tickFormat=function(t,n){var r=e();return X(r[0],r[r.length-1],null==t?10:t,n)},t.nice=function(n){null==n&&(n=10);var r,i=e(),a=0,s=i.length-1,o=i[a],u=i[s];return u<o&&(r=o,o=u,u=r,r=a,a=s,s=r),(r=f(o,u,n))>0?r=f(o=Math.floor(o/r)*r,u=Math.ceil(u/r)*r,n):r<0&&(r=f(o=Math.ceil(o*r)/r,u=Math.floor(u*r)/r,n)),r>0?(i[a]=Math.floor(o/r)*r,i[s]=Math.ceil(u/r)*r,e(i)):r<0&&(i[a]=Math.ceil(o*r)/r,i[s]=Math.floor(u*r)/r,e(i)),t},t}function V(){var t=q(P,P);return t.copy=function(){return C(t,V())},m.apply(t,arguments),B(t)}var L=n(18780),F=n(51515),Y=n(82105),H=n(62295),K=n(14503),U=n(83665),W=n(58097),G=n(28164),Z=n(19994);var J=1e3,Q=60*J,tt=60*Q,et=24*tt,nt=7*et,rt=30*et,it=365*et;function at(t){return new Date(t)}function st(t){return t instanceof Date?+t:+new Date(+t)}function ot(t,e,n,r,a,s,o,u,l){var c=q(P,P),h=c.invert,f=c.domain,m=l(".%L"),p=l(":%S"),g=l("%I:%M"),v=l("%I %p"),b=l("%a %d"),w=l("%b %d"),x=l("%B"),_=l("%Y"),k=[[o,1,J],[o,5,5*J],[o,15,15*J],[o,30,30*J],[s,1,Q],[s,5,5*Q],[s,15,15*Q],[s,30,30*Q],[a,1,tt],[a,3,3*tt],[a,6,6*tt],[a,12,12*tt],[r,1,et],[r,2,2*et],[n,1,nt],[e,1,rt],[e,3,3*rt],[t,1,it]];function M(i){return(o(i)<i?m:s(i)<i?p:a(i)<i?g:r(i)<i?v:e(i)<i?n(i)<i?b:w:t(i)<i?x:_)(i)}function A(e,n,r,a){if(null==e&&(e=10),"number"===typeof e){var s=Math.abs(r-n)/e,o=i((function(t){return t[2]})).right(k,s);o===k.length?(a=d(n/it,r/it,e),e=t):o?(a=(o=k[s/k[o-1][2]<k[o][2]/s?o-1:o])[1],e=o[0]):(a=Math.max(d(n,r,e),1),e=u)}return null==a?e:e.every(a)}return c.invert=function(t){return new Date(h(t))},c.domain=function(t){return arguments.length?f(y.call(t,st)):f().map(at)},c.ticks=function(t,e){var n,r=f(),i=r[0],a=r[r.length-1],s=a<i;return s&&(n=i,i=a,a=n),n=(n=A(t,i,a,e))?n.range(i,a+1):[],s?n.reverse():n},c.tickFormat=function(t,e){return null==e?M:l(e)},c.nice=function(t,e){var n=f();return(t=A(t,n[0],n[n.length-1],e))?f(function(t,e){var n,r=0,i=(t=t.slice()).length-1,a=t[r],s=t[i];return s<a&&(n=r,r=i,i=n,n=a,a=s,s=n),t[r]=e.floor(a),t[i]=e.ceil(s),t}(n,t)):c},c.copy=function(){return C(c,ot(t,e,n,r,a,s,o,u,l))},c}function ut(){return m.apply(ot(L.A,F.A,Y.fz,H.A,K.A,U.A,W.A,G.A,Z.DC).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function lt(){var t,e,n,r,i,a=0,s=1,o=P,u=!1;function l(e){return isNaN(e=+e)?i:o(0===n?.5:(e=(r(e)-t)*n,u?Math.max(0,Math.min(1,e)):e))}return l.domain=function(i){return arguments.length?(t=r(a=+i[0]),e=r(s=+i[1]),n=t===e?0:1/(e-t),l):[a,s]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(o=t,l):o},l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return r=i,t=i(a),e=i(s),n=t===e?0:1/(e-t),l}}function ct(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function ht(){var t=B(lt()(P));return t.copy=function(){return ct(t,ht())},p.apply(t,arguments)}}}]);
//# sourceMappingURL=9800.54db3b3e.chunk.js.map