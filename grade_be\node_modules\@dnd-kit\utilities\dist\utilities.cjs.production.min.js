"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react");const t="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function n(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function r(e){return"nodeType"in e}function o(e){var t,o;return e?n(e)?e:r(e)&&null!=(t=null==(o=e.ownerDocument)?void 0:o.defaultView)?t:window:window}function u(e){const{Document:t}=o(e);return e instanceof t}function c(e){return!n(e)&&e instanceof o(e).HTMLElement}function s(e){return e instanceof o(e).SVGElement}const i=t?e.useLayoutEffect:e.useEffect;function a(t){const n=e.useRef(t);return i(()=>{n.current=t}),e.useCallback((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)}),[])}let l={};function f(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce((t,n)=>{const r=Object.entries(n);for(const[n,o]of r){const r=t[n];null!=r&&(t[n]=r+e*o)}return t},{...t})}}const d=f(1),p=f(-1);function x(e){return"clientX"in e&&"clientY"in e}function m(e){if(!e)return!1;const{TouchEvent:t}=o(e.target);return t&&e instanceof t}const h=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[h.Translate.toString(e),h.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),b="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";exports.CSS=h,exports.add=d,exports.canUseDOM=t,exports.findFirstFocusableNode=function(e){return e.matches(b)?e:e.querySelector(b)},exports.getEventCoordinates=function(e){if(m(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return x(e)?{x:e.clientX,y:e.clientY}:null},exports.getOwnerDocument=function(e){return e?n(e)?e.document:r(e)?u(e)?e:c(e)||s(e)?e.ownerDocument:document:document:document},exports.getWindow=o,exports.hasViewportRelativeCoordinates=x,exports.isDocument=u,exports.isHTMLElement=c,exports.isKeyboardEvent=function(e){if(!e)return!1;const{KeyboardEvent:t}=o(e.target);return t&&e instanceof t},exports.isNode=r,exports.isSVGElement=s,exports.isTouchEvent=m,exports.isWindow=n,exports.subtract=p,exports.useCombinedRefs=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.useMemo(()=>e=>{n.forEach(t=>t(e))},n)},exports.useEvent=a,exports.useInterval=function(){const t=e.useRef(null);return[e.useCallback((e,n)=>{t.current=setInterval(e,n)},[]),e.useCallback(()=>{null!==t.current&&(clearInterval(t.current),t.current=null)},[])]},exports.useIsomorphicLayoutEffect=i,exports.useLatestValue=function(t,n){void 0===n&&(n=[t]);const r=e.useRef(t);return i(()=>{r.current!==t&&(r.current=t)},n),r},exports.useLazyMemo=function(t,n){const r=e.useRef();return e.useMemo(()=>{const e=t(r.current);return r.current=e,e},[...n])},exports.useNodeRef=function(t){const n=a(t),r=e.useRef(null),o=e.useCallback(e=>{e!==r.current&&(null==n||n(e,r.current)),r.current=e},[]);return[r,o]},exports.usePrevious=function(t){const n=e.useRef();return e.useEffect(()=>{n.current=t},[t]),n.current},exports.useUniqueId=function(t,n){return e.useMemo(()=>{if(n)return n;const e=null==l[t]?0:l[t]+1;return l[t]=e,t+"-"+e},[t,n])};
//# sourceMappingURL=utilities.cjs.production.min.js.map
