# Generated by Django 5.0.6 on 2025-02-03 15:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0009_customuser"),
    ]

    operations = [
        migrations.CreateModel(
            name="Subject",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        choices=[
                            ("English", "English"),
                            ("Maths", "Maths"),
                            ("Physics", "Physics"),
                            ("Chemistry", "Chemistry"),
                            ("Biology", "Biology"),
                            ("Computer Science", "Computer Science"),
                        ],
                        max_length=50,
                        unique=True,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="user",
            name="subjects",
            field=models.ManyToManyField(
                blank=True, related_name="users", to="authentication.subject"
            ),
        ),
    ]
