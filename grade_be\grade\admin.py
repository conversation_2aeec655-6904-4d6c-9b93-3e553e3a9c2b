from .models import MentorStudent
from .models import Evaluator, Language, Subject, Board
from .models import Question
from .models import MainRequest
from .models import Notification
from .models import AnswerAssignment
from .models import MentorshipRequest
from .models import Feedback, AnswerUpload
from django.contrib import admin
from .models import (
    AnswerUpload,
    QuestionFeedback,
    SampleQuestionPaper,
    PreviousYearQuestionPaper,
    GeneratedQuestionPaper,
)


# Add these to your existing admin.py file

from django.contrib import admin

from .models import GradingResult


@admin.register(GradingResult)
class GradingResultAdmin(admin.ModelAdmin):
    list_display = (
        "answer_upload",
        "user_id",
        "total_score",
        "max_possible_score",
        "percentage",
        "grading_processed",
        "questions_count",
        "diagrams_count",
        "graded_at",
    )
    list_filter = ("grading_processed", "created_at", "graded_at")
    search_fields = ("user_id", "answer_upload__id")
    readonly_fields = ("created_at", "graded_at", "percentage")

    fieldsets = (
        ("Answer Information", {"fields": ("answer_upload", "user_id")}),
        (
            "Grading Results",
            {
                "fields": (
                    "total_score",
                    "max_possible_score",
                    "percentage",
                    "questions_count",
                    "diagrams_count",
                )
            },
        ),
        (
            "Processing Status",
            {
                "fields": (
                    "grading_processed",
                    "grading_error",
                    "result_json_path",
                )
            },
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "graded_at"), "classes": ("collapse",)},
        ),
    )

    def get_readonly_fields(self, request, obj=None) -> list:
        # Make percentage always readonly as it's calculated
        readonly = list(self.readonly_fields)
        if obj:  # If editing existing object
            # Don't allow changing these
            readonly.extend(["answer_upload", "user_id"])
        return readonly


class QuestionFeedbackInline(admin.StackedInline):
    model = QuestionFeedback
    extra = 0
    readonly_fields = ("created_date",)


@admin.register(AnswerUpload)
class AnswerUploadAdmin(admin.ModelAdmin):
    list_display = (
        "user_id",
        "organization",
        "question_paper_type",
        "question_paper_id",
        "upload_date",
        "ocr_processed",
        "roll_number",
    )
    list_filter = (
        "question_paper_type",
        "upload_date",
        "ocr_processed",
        "organization",
    )
    search_fields = (
        "user_id",
        "question_paper_id",
        "roll_number",
        "organization__name",
    )
    readonly_fields = ("upload_date", "ocr_processed_at")

    fieldsets = (
        (
            "User Information",
            {"fields": ("user_id", "organization", "file", "upload_date")},
        ),
        (
            "Question Paper Details",
            {
                "fields": (
                    "question_paper_type",
                    "question_paper_id",
                    "sample_question_paper",
                    "previous_year_question_paper",
                    "generated_question_paper",
                    "questions",
                )
            },
        ),
        (
            "OCR Processing",
            {
                "fields": (
                    "ocr_processed",
                    "ocr_json_path",
                    "ocr_images_dir",
                    "roll_number",
                    "ocr_error",
                    "ocr_processed_at",
                ),
                "classes": ("collapse",),
            },
        ),
    )
    raw_id_fields = ["organization"]


@admin.register(QuestionFeedback)
class QuestionFeedbackAdmin(admin.ModelAdmin):
    list_display = ("answer_upload", "marks_obtained", "created_date")
    list_filter = ("created_date",)
    search_fields = ("answer_upload__user_id",)
    readonly_fields = ("created_date",)


class BaseQuestionPaperAdmin(admin.ModelAdmin):
    list_display = (
        "test_title",
        "subject",
        "board",
        "organization",
        "total_marks",
        "total_questions",
        "upload_date",
    )
    list_filter = ("board", "subject", "upload_date", "organization")
    search_fields = ("test_title", "subject", "board", "organization__name")
    readonly_fields = ("upload_date", "total_questions")
    fieldsets = (
        (
            "Paper Information",
            {
                "fields": (
                    "organization",
                    "test_title",
                    "subject",
                    "board",
                    "updated_by",
                    "upload_date",
                )
            },
        ),
        (
            "Questions and Marks",
            {"fields": ("questions", "total_marks", "total_questions")},
        ),
    )
    raw_id_fields = ["organization"]


@admin.register(SampleQuestionPaper)
class SampleQuestionPaperAdmin(BaseQuestionPaperAdmin):
    list_display = BaseQuestionPaperAdmin.list_display + ("file",)


@admin.register(PreviousYearQuestionPaper)
class PreviousYearQuestionPaperAdmin(BaseQuestionPaperAdmin):
    list_display = BaseQuestionPaperAdmin.list_display + ("year", "file")
    list_filter = BaseQuestionPaperAdmin.list_filter + ("year",)
    search_fields = BaseQuestionPaperAdmin.search_fields + ("year",)
    fieldsets = (
        (
            "Paper Information",
            {
                "fields": (
                    "test_title",
                    "subject",
                    "board",
                    "year",
                    "updated_by",
                    "upload_date",
                )
            },
        ),
        (
            "Questions and Marks",
            {"fields": ("questions", "total_marks", "total_questions")},
        ),
        ("Files", {"fields": ("file",)}),
    )


@admin.register(GeneratedQuestionPaper)
class GeneratedQuestionPaperAdmin(BaseQuestionPaperAdmin):
    list_display = BaseQuestionPaperAdmin.list_display + ("user_id", "file")
    search_fields = BaseQuestionPaperAdmin.search_fields + ("user_id",)
    fieldsets = (
        (
            "Paper Information",
            {
                "fields": (
                    "test_title",
                    "subject",
                    "board",
                    "updated_by",
                    "upload_date",
                )
            },
        ),
        ("User Information", {"fields": ("user_id",)}),
        (
            "Questions and Marks",
            {"fields": ("total_marks", "total_questions")},
        ),
        ("Files", {"fields": ("file",)}),
    )


class FeedbackAdmin(admin.ModelAdmin):
    list_display = (
        "answer_upload",
        "question_number",
        "marks_obtained",
        "complexity",
        "feedback",
        "marks_out_of",
        "created_at",
        "updated_at",
    )
    search_fields = ("answer_upload__answer_id", "question_number")
    list_filter = ("complexity",)
    ordering = ("-updated_at",)


admin.site.register(Feedback, FeedbackAdmin)


# Register the MentorStudentRequest model


@admin.register(MentorshipRequest)
class MentorStudentRequestAdmin(admin.ModelAdmin):
    # Fields to display in the admin panel
    list_display = ("mentor", "student", "status", "created_at")
    list_filter = ("status", "created_at")  # Fields to filter by
    search_fields = ("mentor__email", "student__email")  # Searchable fields


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = (
        "sender",
        "sender_role",
        "recipient",
        "recipient_role",
        "message",
        "created_at",
        "is_read",
        "mentor_request",
    )
    list_filter = ("is_read", "created_at", "sender_role", "recipient_role")
    search_fields = ("sender__username", "recipient__username", "message")
    ordering = ("-created_at",)


@admin.register(MentorStudent)
class MentorStudentAdmin(admin.ModelAdmin):
    # Columns to display in the admin list view
    list_display = ("mentor", "student", "created_at")
    # Search bar for mentors and students
    search_fields = ("mentor__user__username", "student__user__username")
    list_filter = ("mentor", "created_at")  # Filter options


@admin.register(MainRequest)
class MainRequestAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "resume",
        "role",
        "board",
        "subject",
        "submitted_at",
    )


class QuestionAdmin(admin.ModelAdmin):
    # Fields to display in the admin list view
    list_display = (
        "id",
        "subject",
        "topic",
        "exam_type",
        "is_submitted",
        "complexity",
        "question_type",
        "marks",
        "question_text",
        "question_image",
        "options",
        "correct_answer",
        "explanation",
        "explanation_image",
        "created_at",
    )

    # Fields to filter the list view
    list_filter = (
        "subject",
        "exam_type",
        "complexity",
        "question_type",
        "created_at",
        "is_submitted",
    )

    # Fields to search in the list view
    search_fields = ("subject", "topic", "question_text")

    # Fields to display and edit in the form
    fieldsets = (
        (
            "General Information",
            {
                "fields": (
                    "subject",
                    "topic",
                    "exam_type",
                    "complexity",
                    "question_type",
                    "marks",
                    "is_submitted",
                )
            },
        ),
        ("Question Content", {"fields": ("question_text", "question_image")}),
        (
            "Options (For MCQ)",
            {
                "fields": ("options", "correct_answer"),
                "classes": ("collapse",),  # Collapsible section
            },
        ),
        (
            "Explanation & Solution",
            {
                "fields": ("explanation", "explanation_image"),
            },
        ),
        (
            "Timestamps",
            {
                "fields": ("created_at", "updated_at"),
            },
        ),
    )

    # Make timestamps read-only
    readonly_fields = ("created_at", "updated_at")


# Register the model and admin class
admin.site.register(Question, QuestionAdmin)


@admin.register(AnswerAssignment)
class AnswerAssignmentAdmin(admin.ModelAdmin):
    list_display = (
        "answer_upload",
        "evaluator",
        "assigned_date",
        "completed",
        "is_expired_display",
    )
    list_filter = ("completed", "assigned_date")
    search_fields = ("evaluator__email", "answer_upload__id")

    def is_expired_display(self, obj) -> bool:
        return obj.is_expired()

    is_expired_display.boolean = True
    is_expired_display.short_description = "Expired"


@admin.register(Evaluator)
class EvaluatorAdmin(admin.ModelAdmin):
    # Display user and rating in the admin panel
    list_display = ("user", "rating")
    search_fields = ("user__username",)  # Enable searching by username
    # Use a better UI for ManyToMany fields
    filter_horizontal = ("languages", "subjects", "boards")


@admin.register(Language)
class LanguageAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(Subject)
class SubjectAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


@admin.register(Board)
class BoardAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)
