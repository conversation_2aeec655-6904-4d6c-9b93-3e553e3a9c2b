# Generated by Django 5.0.6 on 2025-03-27 19:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentication",
            "0017_alter_user_managers_user_account_locked_until_and_more",
        ),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name="user",
            name="email_verification_token",
        ),
        migrations.RemoveField(
            model_name="user",
            name="is_email_verified",
        ),
        migrations.AlterField(
            model_name="user",
            name="email",
            field=models.EmailField(
                max_length=254, unique=True, verbose_name="email address"
            ),
        ),
        migrations.AlterField(
            model_name="user",
            name="username",
            field=models.Char<PERSON>ield(max_length=150, unique=True),
        ),
    ]
