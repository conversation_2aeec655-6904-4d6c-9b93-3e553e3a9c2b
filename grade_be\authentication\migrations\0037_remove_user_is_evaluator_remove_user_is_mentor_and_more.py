# Generated by Django 5.1.9 on 2025-06-19 05:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentication",
            "0036_remove_user_roles_user_is_evaluator_user_is_mentor_and_more",
        ),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name="user",
            name="is_evaluator",
        ),
        migrations.RemoveField(
            model_name="user",
            name="is_mentor",
        ),
        migrations.RemoveField(
            model_name="user",
            name="is_qp_uploader",
        ),
        migrations.RemoveField(
            model_name="user",
            name="is_student",
        ),
        migrations.AddField(
            model_name="user",
            name="roles",
            field=models.J<PERSON><PERSON>ield(default=list),
        ),
    ]
