import json
import os
from pathlib import Path
from PIL import Image
import google.generativeai as genai
import fitz  # PyMuPDF for PDF handling
from typing import Dict, List, Tuple, Optional, Any
import logging
from dotenv import load_dotenv
import shutil
import re
import hashlib

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StudentGrader:
    def __init__(self) -> None:
        """Initialize the grader with Gemini API key from .env file"""
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_API_KEY not found in .env file")

        genai.configure(api_key=api_key)

        # DETERMINISTIC MODEL CONFIGURATION FOR CONSISTENCY
        self.generation_config = genai.types.GenerationConfig(
            temperature=0.0,
            top_p=None,
            top_k=None,
            candidate_count=1,
            max_output_tokens=4096,
        )

        self.model = genai.GenerativeModel(
            "gemini-1.5-flash", generation_config=self.generation_config
        )

        self.answer_key = {}
        self.answer_key_diagrams = {}

    def extract_answer_key_from_pdf(self, pdf_path: str) -> Tuple[Dict, Dict]:
        """Extract answer key JSON and diagrams from PDF with enhanced extraction"""
        answer_key = {}
        diagram_paths = {}

        try:
            doc = fitz.open(pdf_path)
            all_text = ""

            # Create temporary folder for answer key diagrams
            temp_diagrams_folder = Path("temp_answer_key_diagrams")
            temp_diagrams_folder.mkdir(exist_ok=True)

            # Extract text and images from all pages
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                all_text += text + "\n"

                # Extract images and map to questions intelligently
                image_list = page.get_images(full=True)
                for img_index, img in enumerate(image_list):
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    image_ext = base_image["ext"]

                    image_filename = f"answer_key_page{page_num+1}_img{img_index+1}.{image_ext}"
                    image_path = temp_diagrams_folder / image_filename

                    with open(image_path, "wb") as img_file:
                        img_file.write(image_bytes)

                    # Intelligent question mapping for diagrams
                    page_text = text.upper()
                    question_key = self._map_diagram_to_question(
                        page_text, page_num
                    )

                    if question_key not in diagram_paths:
                        diagram_paths[question_key] = []
                    diagram_paths[question_key].append(str(image_path))

                    logger.info(
                        f"Mapped diagram on page {page_num+1} to {question_key}: {image_filename}"
                    )

            # ENHANCED: Extract answer key with multi-pass approach
            answer_key = self._extract_answer_key_multi_pass(all_text)

            # Add diagram paths to answer key
            for question, paths in diagram_paths.items():
                if question in answer_key:
                    answer_key[question]["reference_diagrams"] = paths

            doc.close()
            return answer_key, diagram_paths

        except Exception as e:
            logger.error(f"Error extracting from PDF: {e}")
            return {}, {}

    def _discover_question_numbers(self, text: str) -> List[str]:
        """Discover all question numbers present in the text - DYNAMIC"""
        question_numbers = set()

        # Multiple patterns to find question numbers (no limit on range)
        patterns = [
            r'"?Q(\d+)"?\s*:',
            r"Question\s+(\d+)",
            r"Q(\d+)[^a-zA-Z]",
            r'"Q(\d+)"',
            r"QUESTION\s+(\d+)",
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                question_numbers.add(f"Q{match}")

        # Sort questions numerically
        sorted_questions = sorted(
            list(question_numbers), key=lambda x: int(x[1:])
        )
        logger.info(
            f"🔍 Discovered questions in answer key: {sorted_questions}"
        )
        return sorted_questions

    def _extract_answer_key_multi_pass(self, text: str) -> Dict:
        """Multi-pass extraction with validation at each step"""
        answer_key = {}
        discovered_questions = self._discover_question_numbers(text)

        logger.info(
            f"🔍 Starting multi-pass extraction for {len(discovered_questions)} questions: {discovered_questions}"
        )

        # Pass 1: Try complete JSON extraction
        try:
            complete_json = self._extract_complete_json(text)
            for q_key in discovered_questions:
                if q_key in complete_json and self._validate_question_data(
                    complete_json[q_key]
                ):
                    answer_key[q_key] = complete_json[q_key]
                    logger.info(f"✅ Pass 1: Found {q_key}")
        except Exception as e:
            logger.warning(f"Pass 1 failed: {e}")

        missing_questions = [
            q for q in discovered_questions if q not in answer_key
        ]
        logger.info(
            f"📋 After Pass 1: Found {len(answer_key)}, Missing: {missing_questions}"
        )

        # Pass 2: Individual question extraction for missing questions
        if missing_questions:
            try:
                individual_results = (
                    self._extract_individual_questions_enhanced(text)
                )
                for q_key in missing_questions:
                    if (
                        q_key in individual_results
                        and self._validate_question_data(
                            individual_results[q_key]
                        )
                    ):
                        answer_key[q_key] = individual_results[q_key]
                        logger.info(f"✅ Pass 2: Found {q_key}")
            except Exception as e:
                logger.warning(f"Pass 2 failed: {e}")

        missing_questions = [
            q for q in discovered_questions if q not in answer_key
        ]
        logger.info(
            f"📋 After Pass 2: Found {len(answer_key)}, Missing: {missing_questions}"
        )

        # Pass 3: Segment-based extraction for remaining missing questions
        if missing_questions:
            try:
                segment_results = self._extract_by_segments(text)
                for q_key in missing_questions:
                    if (
                        q_key in segment_results
                        and self._validate_question_data(
                            segment_results[q_key]
                        )
                    ):
                        answer_key[q_key] = segment_results[q_key]
                        logger.info(f"✅ Pass 3: Found {q_key}")
            except Exception as e:
                logger.warning(f"Pass 3 failed: {e}")

        missing_questions = [
            q for q in discovered_questions if q not in answer_key
        ]
        logger.info(
            f"📋 After Pass 3: Found {len(answer_key)}, Missing: {missing_questions}"
        )

        # Pass 4: Manual text parsing for still missing questions
        if missing_questions:
            for q_key in missing_questions:
                try:
                    manual_result = self._manual_question_extraction(
                        text, q_key
                    )
                    if manual_result and self._validate_question_data(
                        manual_result
                    ):
                        answer_key[q_key] = manual_result
                        logger.info(f"✅ Pass 4: Found {q_key}")
                except Exception as e:
                    logger.warning(f"Pass 4 failed for {q_key}: {e}")

        final_missing = [
            q for q in discovered_questions if q not in answer_key
        ]
        if final_missing:
            logger.error(f"❌ Failed to extract: {final_missing}")

        return answer_key

    def _extract_complete_json(self, text: str) -> Dict:
        """Extract complete JSON block"""
        json_start = text.find("{")
        json_end = text.rfind("}") + 1

        if json_start != -1 and json_end != -1:
            json_text = text[json_start:json_end]

            # Clean and fix JSON
            json_text = self._clean_and_fix_json(json_text)

            # Try to parse
            parsed_json = json.loads(json_text)

            if isinstance(parsed_json, dict):
                return parsed_json

        raise ValueError("No valid complete JSON found")

    def _extract_individual_questions_enhanced(self, text: str) -> Dict:
        """Enhanced method to extract questions with multiple pattern variations"""
        answer_key = {}
        self._discover_question_numbers(text)

        # Multiple comprehensive patterns to handle various JSON structures
        patterns = [
            # Standard JSON format
            r'"?Q(\d+)"?\s*:\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}',
            # With newlines and spacing variations
            r'"?Q(\d+)"?\s*:\s*\{\s*([^{}]*(?:\{[^{}]*\}[^{}]*)*)\s*\}',
            # Multiline with proper nesting
            r'"?Q(\d+)"?\s*:\s*\{((?:[^{}]|\{[^{}]*\})*)\}',
            # Loose matching for malformed JSON
            r"Q(\d+)[^{]*(\{(?:[^{}]|\{[^{}]*\})*\})",
            # With quotes around question numbers
            r'"Q(\d+)"\s*:\s*(\{(?:[^{}]|\{[^{}]*\})*\})',
            # Alternative spacing patterns
            r"Q(\d+)\s*:\s*(\{(?:[^{}]|\{[^{}]*\})*\})",
        ]

        for pattern in patterns:
            matches = re.findall(
                pattern, text, re.DOTALL | re.IGNORECASE | re.MULTILINE
            )
            for question_num, content in matches:
                q_key = f"Q{question_num}"

                if q_key not in answer_key:  # Don't overwrite if already found
                    # Clean the content first
                    content = self._clean_extracted_content(content)
                    question_data = self._parse_question_content_enhanced(
                        content
                    )
                    if question_data:
                        answer_key[q_key] = question_data
                        logger.info(
                            f"✅ Pattern match found {q_key} with {len(question_data.get('evaluation_criteria', []))} criteria"
                        )

        return answer_key

    def _clean_extracted_content(self, content: str) -> str:
        """Clean extracted JSON content to handle malformed structures"""
        # Remove leading/trailing whitespace
        content = content.strip()

        # Ensure proper braces
        if not content.startswith("{"):
            content = "{" + content
        if not content.endswith("}"):
            content = content + "}"

        # Fix common JSON issues
        content = re.sub(r",\s*}", "}", content)  # Remove trailing commas
        # Remove trailing commas in arrays
        content = re.sub(r",\s*]", "]", content)

        return content

    def _extract_by_segments(self, text: str) -> Dict:
        """Extract answer key by identifying text segments for each question"""
        answer_key = {}
        discovered_questions = self._discover_question_numbers(text)

        for i, q_key in enumerate(discovered_questions):
            # If it's not the last question, find the next question boundary
            if i < len(discovered_questions) - 1:
                next_q_key = discovered_questions[i + 1]
                segment_pattern = rf"{q_key}(.*?)(?={next_q_key})"
            else:
                # Last question - take everything until end
                segment_pattern = rf"{q_key}(.*?)$"

            segment_match = re.search(
                segment_pattern, text, re.DOTALL | re.IGNORECASE
            )

            if segment_match:
                segment = (
                    segment_match.group(1)
                    if segment_match.groups()
                    else segment_match.group(0)
                )

                # Extract question data from this segment
                question_data = self._extract_from_segment(segment, q_key)
                if question_data:
                    answer_key[q_key] = question_data
                    logger.info(f"✅ Segment extraction found {q_key}")

        return answer_key

    def _extract_from_segment(
        self, segment: str, q_key: str
    ) -> Optional[Dict]:
        """Extract question data from a text segment"""
        question_data = {}

        # Extract allocated marks
        marks_patterns = [
            r'allocated_marks["\']?\s*:\s*(\d+)',
            r'marks["\']?\s*:\s*(\d+)',
            r"(\d+)\s*marks?",
            r"total[^:]*:\s*(\d+)",
        ]

        for pattern in marks_patterns:
            match = re.search(pattern, segment, re.IGNORECASE)
            if match:
                question_data["allocated_marks"] = int(match.group(1))
                break

        if "allocated_marks" not in question_data:
            return None

        # Extract evaluation criteria from segment
        criteria = self._extract_criteria_from_segment(segment)
        if criteria:
            question_data["evaluation_criteria"] = criteria
        else:
            question_data["evaluation_criteria"] = []
            question_data["criteria_status"] = "not_defined"

        # Extract other fields
        question_data["type"] = self._extract_type_from_segment(segment)
        question_data["expected_answer"] = (
            self._extract_expected_answer_from_segment(segment)
        )

        return question_data

    def _extract_criteria_from_segment(self, segment: str) -> List[str]:
        """Extract evaluation criteria from a text segment using multiple methods"""
        criteria = []

        # Method 1: Look for evaluation_criteria array
        criteria_patterns = [
            r'evaluation_criteria["\']?\s*:\s*\[(.*?)\]',
            r'criteria["\']?\s*:\s*\[(.*?)\]',
            r"grading[^:]*criteria[^:]*:\s*\[(.*?)\]",
        ]

        for pattern in criteria_patterns:
            match = re.search(pattern, segment, re.DOTALL | re.IGNORECASE)
            if match:
                criteria_text = match.group(1)
                # Extract individual criteria from the array
                individual_criteria = re.findall(r'"([^"]+)"', criteria_text)
                if individual_criteria:
                    criteria.extend(individual_criteria)
                    break

        # Method 2: Look for criteria with marks notation
        if not criteria:
            mark_patterns = [
                r"([^.!?\n]+\(\d+\s*marks?\))",
                r"([^.!?\n]+\d+\s*marks?)",
                r"(\d+\s*marks?[^.!?\n]+)",
            ]

            for pattern in mark_patterns:
                matches = re.findall(pattern, segment, re.IGNORECASE)
                for match in matches:
                    clean_match = match.strip()
                    if len(clean_match) > 5 and "mark" in clean_match.lower():
                        criteria.append(clean_match)

        return criteria

    def _extract_type_from_segment(self, segment: str) -> str:
        """Extract question type from segment"""
        type_match = re.search(
            r'type["\']?\s*:\s*["\']([^"\']+)["\']', segment, re.IGNORECASE
        )
        return type_match.group(1) if type_match else "mixed"

    def _extract_expected_answer_from_segment(self, segment: str) -> str:
        """Extract expected answer from segment"""
        patterns = [
            r'expected_answer["\']?\s*:\s*["\']([^"\']+)["\']',
            r"expected[^:]*answer[^:]*:\s*([^.!?\n]+)",
            r"answer[^:]*:\s*([^.!?\n]+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, segment, re.IGNORECASE | re.DOTALL)
            if match:
                answer = match.group(1).strip()
                if len(answer) > 10:
                    return answer

        return "Expected answer not explicitly defined in answer key"

    def _manual_question_extraction(
        self, text: str, q_key: str
    ) -> Optional[Dict]:
        """Manual extraction as last resort for a specific question"""
        q_num = q_key[1:]  # Remove 'Q' prefix

        # Find any mention of this question in various formats
        search_patterns = [
            rf"{q_key}[^Q]*?(\d+)\s*marks?",
            rf"Question\s+{q_num}[^Q]*?(\d+)\s*marks?",
            rf"{q_num}\.[^Q]*?(\d+)\s*marks?",
            rf"Q{q_num}[^Q]*?(\d+)\s*marks?",
        ]

        allocated_marks = None
        for pattern in search_patterns:
            match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if match:
                allocated_marks = int(match.group(1))
                break

        if not allocated_marks:
            return None

        # Try to find the full section for this question
        section = self._extract_question_section(text, q_key)

        # Extract whatever criteria we can find
        criteria = self._find_criteria_in_section(section)
        expected_answer = self._find_expected_answer_in_section(section)

        question_data = {
            "type": "mixed",
            "allocated_marks": allocated_marks,
            "expected_answer": expected_answer
            or "Expected answer not explicitly defined in answer key",
        }

        if criteria:
            question_data["evaluation_criteria"] = criteria
            logger.info(
                f"✅ Manual extraction found {len(criteria)} criteria for {q_key}"
            )
        else:
            question_data["evaluation_criteria"] = []
            question_data["criteria_status"] = "not_defined"
            logger.warning(
                f"⚠️  Manual extraction: No criteria found for {q_key}"
            )

        return question_data

    def _parse_question_content_enhanced(self, content: str) -> Optional[Dict]:
        """Parse individual question content with enhanced extraction"""
        question_data = {}

        # Extract allocated marks
        marks_match = re.search(
            r'(?:allocated_marks|marks)["\']?\s*:\s*(\d+)',
            content,
            re.IGNORECASE,
        )
        if marks_match:
            question_data["allocated_marks"] = int(marks_match.group(1))
        else:
            # Try to find marks in text
            marks_match = re.search(r"(\d+)\s*marks?", content, re.IGNORECASE)
            if marks_match:
                question_data["allocated_marks"] = int(marks_match.group(1))
            else:
                return None  # No marks found

        # Extract question type
        type_match = re.search(
            r'(?:type)["\']?\s*:\s*["\']([^"\']+)["\']', content, re.IGNORECASE
        )
        question_data["type"] = type_match.group(1) if type_match else "mixed"

        # Extract expected answer
        expected_answer = self._find_expected_answer_in_section(content)
        question_data["expected_answer"] = (
            expected_answer
            or "Expected answer not explicitly defined in answer key"
        )

        # CRITICAL: Extract evaluation criteria
        criteria = self._extract_criteria_from_content(content)
        if criteria:
            question_data["evaluation_criteria"] = criteria
        else:
            question_data["evaluation_criteria"] = []
            question_data["criteria_status"] = "not_defined"
            logger.warning(
                f"⚠️  No evaluation criteria found in question content"
            )

        return question_data

    def _extract_criteria_from_content(self, content: str) -> List[str]:
        """Extract evaluation criteria from question content"""
        criteria = []

        # Method 1: Look for evaluation_criteria array
        criteria_pattern = r'evaluation_criteria["\']?\s*:\s*\[(.*?)\]'
        match = re.search(criteria_pattern, content, re.DOTALL | re.IGNORECASE)

        if match:
            criteria_text = match.group(1)
            individual_criteria = re.findall(r'"([^"]+)"', criteria_text)
            criteria = individual_criteria

        # Method 2: Look for criteria with marks
        if not criteria:
            criterion_patterns = [
                r"([^.!?]+\(\d+\s*marks?\))",
                r"([^.!?]+\d+\s*marks?)",
            ]

            for pattern in criterion_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    clean_match = match.strip()
                    if len(clean_match) > 5:
                        criteria.append(clean_match)

        return criteria

    def _validate_question_data(self, question_data: Dict) -> bool:
        """Validate that question data has minimum required fields"""
        if not isinstance(question_data, dict):
            return False

        # Must have allocated marks
        if "allocated_marks" not in question_data:
            return False

        # Must be a positive integer
        try:
            marks = int(question_data["allocated_marks"])
            if marks <= 0:
                return False
        except (ValueError, TypeError):
            return False

        return True

    def _clean_and_fix_json(self, json_text: str) -> str:
        """Clean and fix JSON text"""
        # Remove extra whitespace
        json_text = re.sub(r"\s+", " ", json_text)

        # Fix common issues
        json_text = json_text.replace('""', '"').replace(",,", ",")
        json_text = re.sub(r",(\s*[}\]])", r"\1", json_text)

        # Fix unescaped quotes
        json_text = re.sub(r'(?<!\\)"([^"]*)"(?=\s*:)', r'"\1"', json_text)

        return json_text.strip()

    def _extract_question_section(self, text: str, q_key: str) -> str:
        """Extract text section around a specific question"""
        # Find the question and extract surrounding text
        q_pattern = rf"{q_key}.*?(?=Q\d+|$)"
        match = re.search(q_pattern, text, re.IGNORECASE | re.DOTALL)

        if match:
            return match.group(0)

        # Fallback: find any mention and take 500 characters around it
        q_pos = text.upper().find(q_key)
        if q_pos != -1:
            start = max(0, q_pos - 250)
            end = min(len(text), q_pos + 750)
            return text[start:end]

        return ""

    def _find_criteria_in_section(self, section: str) -> List[str]:
        """Find evaluation criteria in a text section"""
        criteria = []

        # Look for criteria patterns
        patterns = [
            r"([^.!?]+\(\d+\s*marks?\))",
            r"([^.!?]+\d+\s*marks?)",
            r"evaluation_criteria[^[]*\[(.*?)\]",
        ]

        for pattern in patterns:
            matches = re.findall(pattern, section, re.IGNORECASE | re.DOTALL)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0] if match else ""

                clean_match = match.strip()
                if len(clean_match) > 10 and "mark" in clean_match.lower():
                    criteria.append(clean_match)

        return criteria

    def _find_expected_answer_in_section(self, section: str) -> Optional[str]:
        """Find expected answer in a text section"""
        patterns = [
            r'expected_answer["\']?\s*:\s*["\']([^"\']+)["\']',
            r"expected[^:]*answer[^:]*:\s*([^.!?\n]+)",
            r"answer[^:]*:\s*([^.!?\n]+)",
        ]

        for pattern in patterns:
            match = re.search(pattern, section, re.IGNORECASE | re.DOTALL)
            if match:
                answer = match.group(1).strip()
                if len(answer) > 10:  # Only consider substantial answers
                    return answer

        return None

    def _map_diagram_to_question(self, page_text: str, page_num: int) -> str:
        """Intelligently map diagrams to questions based on context"""
        discovered_questions = self._discover_question_numbers(page_text)

        if discovered_questions:
            return discovered_questions[0]  # Return first found question

        return f"Q{page_num + 1}"  # Fallback

    def separate_student_questions(
        self, student_answer: Dict
    ) -> Dict[str, Dict]:
        """Separate student answer into individual questions - DYNAMIC"""
        separated_questions = {}

        for key, value in student_answer.items():
            if key.startswith("Q") and isinstance(value, dict):
                separated_questions[key] = value
                logger.info(f"📝 Found student answer for {key}")
            elif key.startswith("Q"):
                # Handle non-dict answers
                separated_questions[key] = {"answer": value}
                logger.info(f"📝 Found simple student answer for {key}")

        logger.info(
            f"📋 Separated {len(separated_questions)} questions from student answer"
        )
        return separated_questions

    def separate_answer_key_questions(self) -> Dict[str, Dict]:
        """Separate answer key into individual questions - DYNAMIC"""
        separated_questions = {}

        for key, value in self.answer_key.items():
            if key.startswith("Q") and isinstance(value, dict):
                separated_questions[key] = value
                logger.info(f"🔑 Found answer key for {key}")

        logger.info(
            f"📋 Separated {len(separated_questions)} questions from answer key"
        )
        return separated_questions

    def map_questions(
        self, student_questions: Dict, answer_key_questions: Dict
    ) -> Dict[str, Tuple[Dict, Dict]]:
        """Map student questions to answer key questions by question number - DYNAMIC"""
        mapped_questions = {}

        for q_num in answer_key_questions.keys():
            student_answer = student_questions.get(
                q_num, {"answer": "No answer provided"}
            )
            answer_key_data = answer_key_questions[q_num]

            mapped_questions[q_num] = (student_answer, answer_key_data)
            logger.info(f"🔗 Mapped {q_num}")

        # Check for student answers without corresponding answer key
        for q_num in student_questions.keys():
            if q_num not in answer_key_questions:
                logger.warning(
                    f"⚠️  Student provided answer for {q_num} but no answer key exists"
                )

        logger.info(
            f"🗺️  Mapped {len(mapped_questions)} questions for evaluation"
        )
        return mapped_questions

    def load_student_answer(self, json_file_path: str) -> Dict:
        """Load student answer from JSON file with error handling"""
        try:
            with open(json_file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(
                f"Error loading student answer from {json_file_path}: {e}"
            )
            return {}

    def get_student_diagrams_by_question(
        self, student_answer: Dict, output_folder: Path
    ) -> Dict[str, List]:
        """Extract student diagrams organized by question number"""
        diagrams_by_question = {}

        for question_key, question_data in student_answer.items():
            if isinstance(question_data, dict) and "diagram" in question_data:
                diagrams_by_question[question_key] = []

                for diagram_key, diagram_path in question_data[
                    "diagram"
                ].items():
                    possible_paths = [
                        Path(diagram_path),
                        output_folder / diagram_path,
                        Path(
                            diagram_path.replace("output/", "").replace(
                                "output\\", ""
                            )
                        ),
                        output_folder
                        / diagram_path.replace("output/", "").replace(
                            "output\\", ""
                        ),
                        output_folder / Path(diagram_path).name,
                        Path("data") / "output" / Path(diagram_path).name,
                    ]

                    full_path = None
                    for path in possible_paths:
                        if path.exists():
                            full_path = path
                            break

                    if full_path and full_path.exists():
                        try:
                            image = Image.open(full_path)
                            diagrams_by_question[question_key].append(
                                {
                                    "key": diagram_key,
                                    "image": image,
                                    "path": str(full_path),
                                }
                            )
                            logger.info(
                                f"✅ Loaded student diagram for {question_key}: {full_path}"
                            )
                        except Exception as e:
                            logger.error(
                                f"❌ Error loading student diagram {full_path}: {e}"
                            )
                    else:
                        logger.warning(
                            f"⚠️  Student diagram file not found for {question_key}: {diagram_path}"
                        )

        return diagrams_by_question

    def get_answer_key_diagrams_by_question(self) -> Dict[str, List]:
        """Get answer key diagrams organized by question number"""
        diagrams_by_question = {}

        for question_key, diagram_paths in self.answer_key_diagrams.items():
            diagrams_by_question[question_key] = []

            for diagram_path in diagram_paths:
                full_path = Path(diagram_path)
                if full_path.exists():
                    try:
                        image = Image.open(full_path)
                        diagrams_by_question[question_key].append(
                            {"image": image, "path": str(full_path)}
                        )
                        logger.info(
                            f"✅ Loaded reference diagram for {question_key}: {diagram_path}"
                        )
                    except Exception as e:
                        logger.error(
                            f"❌ Error loading answer key diagram {full_path}: {e}"
                        )

        return diagrams_by_question

    def _parse_evaluation_criteria(self, criteria: List[str]) -> List[Dict]:
        """Parse evaluation criteria to extract marks allocation"""
        if not criteria:
            return []

        parsed_criteria = []

        for criterion in criteria:
            marks_match = re.search(r"\((\d+)\s*marks?\)", criterion)
            marks = int(marks_match.group(1)) if marks_match else 1

            clean_criterion = re.sub(
                r"\s*\(\d+\s*marks?\)", "", criterion
            ).strip()

            parsed_criteria.append(
                {"criterion": clean_criterion, "allocated_marks": marks}
            )

        return parsed_criteria

    def _create_deterministic_prompt_hash(self, student_answer: Dict) -> str:
        """Create deterministic hash for consistent grading"""
        answer_str = json.dumps(student_answer, sort_keys=True)
        return hashlib.md5(answer_str.encode()).hexdigest()[:8]

    def prepare_flexible_grading_prompt(
        self,
        question_num: str,
        question_data: Dict,
        student_answer: Dict,
        student_diagrams: Dict,
        answer_key_diagrams: Dict,
    ) -> str:
        """Prepare grading prompt that handles both defined and undefined evaluation criteria"""

        evaluation_criteria = question_data.get("evaluation_criteria", [])
        criteria_status = question_data.get("criteria_status", "defined")
        parsed_criteria = (
            self._parse_evaluation_criteria(evaluation_criteria)
            if evaluation_criteria
            else []
        )

        answer_hash = self._create_deterministic_prompt_hash(
            {question_num: student_answer}
        )

        if criteria_status == "not_defined" or not evaluation_criteria:
            # Handle case where evaluation criteria is not defined
            prompt = f"""You are Professor Sarah Mitchell, an expert academic grader with 25+ years of experience. You are known for ABSOLUTE CONSISTENCY and STRICT ADHERENCE to available guidelines.

CRITICAL IDENTITY: Grading session #{answer_hash} for {question_num}.

⚠️  IMPORTANT NOTE: The evaluation criteria for this question is NOT DEFINED in the answer key.

QUESTION: {question_num}
QUESTION TYPE: {question_data.get('type', 'mixed')}
TOTAL ALLOCATED MARKS: {question_data.get('allocated_marks', 0)}

EVALUATION CRITERIA STATUS: NOT DEFINED

STUDENT ANSWER FOR THIS QUESTION:
{json.dumps(student_answer, indent=2)}

EXPECTED ANSWER (REFERENCE):
{question_data.get('expected_answer', 'No expected answer provided')}

GRADING INSTRUCTIONS FOR UNDEFINED CRITERIA:
Since evaluation criteria is not defined in the answer key, you should:
1. Compare student answer with expected answer/reference
2. Award marks based on correctness, completeness, and understanding
3. Identify specific mistakes in the student's answer
4. Provide constructive feedback explaining why marks were awarded/deducted
5. Be consistent in your evaluation approach

OUTPUT FORMAT (JSON only):
{{
  "question_number": "{question_num}",
  "question_type": "{question_data.get('type', 'mixed')}",
  "allocated_marks": {question_data.get('allocated_marks', 0)},
  "obtained_marks": <marks awarded based on your evaluation>,
  "student_answer": {json.dumps(student_answer)},
  "expected_answer": "{question_data.get('expected_answer', 'No expected answer provided')}",
  "diagram_comparison": "<comparison if diagrams exist, otherwise null>",
  "evaluation_criteria_status": "not_defined",
  "general_feedback": "<detailed feedback explaining the evaluation and marks awarded>",
  "mistakes_identified": ["<list of specific mistakes or areas for improvement>"],
  "summary": "<concise summary of performance and key points for improvement>"
}}"""
        else:
            # Handle case where evaluation criteria is defined
            prompt = f"""You are Professor Sarah Mitchell, an expert academic grader with 25+ years of experience. You are known for ABSOLUTE CONSISTENCY and STRICT ADHERENCE to answer key criteria.

CRITICAL IDENTITY: Grading session #{answer_hash} for {question_num}. You MUST follow the answer key criteria EXACTLY.

🔒 FUNDAMENTAL GRADING PRINCIPLE:
THE ANSWER KEY EVALUATION CRITERIA ARE THE ONLY ALLOWED EVALUATION STANDARDS. You CANNOT use any other criteria.

QUESTION: {question_num}
QUESTION TYPE: {question_data.get('type', 'mixed')}
TOTAL ALLOCATED MARKS: {question_data.get('allocated_marks', 0)}

MANDATORY ANSWER KEY EVALUATION CRITERIA:
{json.dumps(parsed_criteria, indent=2)}

STUDENT ANSWER FOR THIS QUESTION:
{json.dumps(student_answer, indent=2)}

EXPECTED ANSWER (REFERENCE):
{question_data.get('expected_answer', 'No expected answer provided')}

ABSOLUTE GRADING REQUIREMENTS:
1. Evaluate ONLY against the criteria listed above from the answer key
2. Each criterion has specific allocated marks - respect these EXACTLY
3. Award marks per criterion based on student performance against that criterion
4. Be DETERMINISTIC - identical performance = identical marks
5. Provide specific feedback for each criterion
6. Identify specific mistakes in student's answer
7. Provide rubric-style feedback

OUTPUT FORMAT (JSON only):
{{
  "question_number": "{question_num}",
  "question_type": "{question_data.get('type', 'mixed')}",
  "allocated_marks": {question_data.get('allocated_marks', 0)},
  "obtained_marks": <sum of all criterion marks>,
  "student_answer": {json.dumps(student_answer)},
  "expected_answer": "{question_data.get('expected_answer', 'No expected answer provided')}",
  "diagram_comparison": "<comparison if diagrams exist, otherwise null>",
  "criteria_breakdown": [
    {{
      "criterion": "<EXACT criterion text from answer key>",
      "allocated_marks": <marks for this criterion from answer key>,
      "obtained_marks": <marks awarded based on student performance>,
      "feedback": "<specific academic feedback explaining grade for this criterion>",
      "mistakes_found": ["<specific mistakes for this criterion>"]
    }}
  ],
  "mistakes_identified": ["<comprehensive list of all mistakes found across all criteria>"],
  "summary": "<formal academic summary including all mistakes that should be corrected and overall feedback>"
}}"""

        return prompt

    def grade_student_question(
        self,
        question_num: str,
        question_data: Dict,
        student_answer: Dict,
        student_diagrams: Dict,
        answer_key_diagrams: Dict,
    ) -> Dict:
        """Grade individual question using flexible approach"""
        try:
            prompt = self.prepare_flexible_grading_prompt(
                question_num,
                question_data,
                student_answer,
                student_diagrams,
                answer_key_diagrams,
            )

            content = [prompt]

            if question_num in student_diagrams:
                content.append(f"\n\nSTUDENT DIAGRAMS FOR {question_num}:")
                for diagram in student_diagrams[question_num]:
                    content.append(f"  - Diagram {diagram['key']}:")
                    content.append(diagram["image"])

            if question_num in answer_key_diagrams:
                content.append(f"\n\nREFERENCE DIAGRAMS FOR {question_num}:")
                for diagram in answer_key_diagrams[question_num]:
                    content.append(diagram["image"])

            response = self._call_gemini_deterministic(content)
            response_text = response.text.strip()

            response_text = self._clean_gemini_response(response_text)
            result = json.loads(response_text)

            result = self._validate_question_result(result, question_data)

            return result

        except json.JSONDecodeError as e:
            logger.error(
                f"Error parsing JSON response for {question_num}: {e}"
            )
            return self._create_error_question_result(
                question_num, question_data, "JSON parsing error"
            )
        except Exception as e:
            logger.error(f"Error during grading for {question_num}: {e}")
            return self._create_error_question_result(
                question_num, question_data, str(e)
            )

    def grade_student(
        self, student_answer: Dict, output_folder: Path, student_id: str
    ) -> Dict:
        """Grade student answer using the robust question mapping approach"""
        try:
            # Step 1: Separate student questions
            student_questions = self.separate_student_questions(student_answer)

            # Step 2: Separate answer key questions
            answer_key_questions = self.separate_answer_key_questions()

            # Step 3: Map questions using question numbers
            mapped_questions = self.map_questions(
                student_questions, answer_key_questions
            )

            # Get diagrams
            student_diagrams = self.get_student_diagrams_by_question(
                student_answer, output_folder
            )
            answer_key_diagrams = self.get_answer_key_diagrams_by_question()

            # Step 4: Grade each mapped question
            results = []
            total_score = 0
            max_possible_score = 0

            for question_num, (
                student_q_answer,
                answer_key_data,
            ) in mapped_questions.items():
                # Grade this question using its specific criteria or flexible
                # approach
                question_result = self.grade_student_question(
                    question_num,
                    answer_key_data,
                    student_q_answer,
                    student_diagrams,
                    answer_key_diagrams,
                )

                results.append(question_result)
                total_score += question_result.get("obtained_marks", 0)
                max_possible_score += question_result.get("allocated_marks", 0)

                logger.info(
                    f"✅ Graded {question_num}: {question_result.get('obtained_marks', 0)}/{question_result.get('allocated_marks', 0)}"
                )

            # Compile final result
            final_result = {
                "total_score": total_score,
                "max_possible_score": max_possible_score,
                "results": results,
                "student_id": student_id,
                "grading_metadata": {
                    "grading_method": "multi_pass_enhanced_extraction",
                    "consistency_level": "deterministic",
                    "total_questions": len(mapped_questions),
                    "student_diagrams_count": sum(
                        len(diagrams) for diagrams in student_diagrams.values()
                    ),
                    "reference_diagrams_count": sum(
                        len(diagrams)
                        for diagrams in answer_key_diagrams.values()
                    ),
                    "questions_with_diagrams": list(student_diagrams.keys()),
                    "reference_questions_with_diagrams": list(
                        answer_key_diagrams.keys()
                    ),
                },
            }

            return final_result

        except Exception as e:
            logger.error(f"Error during overall grading for {student_id}: {e}")
            return self._create_error_result(student_id, str(e))

    def _validate_question_result(
        self, result: Dict, question_data: Dict
    ) -> Dict:
        """Validate question result against answer key criteria"""
        allocated = question_data.get("allocated_marks", 0)
        obtained = result.get("obtained_marks", 0)

        if obtained > allocated:
            logger.warning(
                f"Obtained marks ({obtained}) > Allocated marks ({allocated}). Capping to {allocated}."
            )
            result["obtained_marks"] = allocated
            if "summary" in result:
                result[
                    "summary"
                ] += f" [Note: Marks capped at maximum {allocated}]"

        if obtained < 0:
            result["obtained_marks"] = 0

        if "criteria_breakdown" in result:
            criteria_total = sum(
                c.get("obtained_marks", 0)
                for c in result["criteria_breakdown"]
            )
            if abs(criteria_total - result["obtained_marks"]) > 0.1:
                logger.warning(
                    f"Criteria breakdown total ({criteria_total}) doesn't match obtained marks ({result['obtained_marks']})"
                )

        result["allocated_marks"] = allocated
        result["question_type"] = question_data.get("type", "mixed")
        result["expected_answer"] = question_data.get(
            "expected_answer", "No expected answer provided"
        )

        return result

    def _create_error_question_result(
        self, question_num: str, question_data: Dict, error_msg: str
    ) -> Dict:
        """Create error result for a single question"""
        return {
            "question_number": question_num,
            "question_type": question_data.get("type", "mixed"),
            "allocated_marks": question_data.get("allocated_marks", 0),
            "obtained_marks": 0,
            "student_answer": "Error loading answer",
            "expected_answer": question_data.get(
                "expected_answer", "No expected answer provided"
            ),
            "diagram_comparison": None,
            "evaluation_criteria_status": "error",
            "general_feedback": f"Grading failed: {error_msg} - Manual review required",
            "mistakes_identified": ["Grading system error"],
            "summary": f"Grading failed: {error_msg} - Manual review required",
        }

    def _call_gemini_deterministic(self, content: str, max_retries: int = 3) -> Any:
        """Call Gemini API with deterministic settings"""
        for attempt in range(max_retries):
            try:
                return self.model.generate_content(
                    content, generation_config=self.generation_config
                )
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                logger.warning(
                    f"Gemini API call failed (attempt {attempt + 1}): {e}"
                )
                import time

                time.sleep(2**attempt)

    def _clean_gemini_response(self, response_text: str) -> str:
        """Clean Gemini response to extract valid JSON"""
        if response_text.startswith("```"):
            response_text = response_text[7:-3].strip()
        elif response_text.startswith("```"):
            response_text = response_text[3:-3].strip()

        return response_text.strip()

    def _create_error_result(self, student_id: str, error_msg: str) -> Dict:
        """Create error result when grading fails"""
        results = []
        total_allocated = 0

        for question_num, answer_data in self.answer_key.items():
            allocated = answer_data["allocated_marks"]
            total_allocated += allocated

            results.append(
                self._create_error_question_result(
                    question_num, answer_data, error_msg
                )
            )

        return {
            "total_score": 0,
            "max_possible_score": total_allocated,
            "results": results,
            "student_id": student_id,
            "error": error_msg,
            "grading_metadata": {
                "grading_method": "multi_pass_enhanced_extraction",
                "student_diagrams_count": 0,
                "reference_diagrams_count": 0,
                "questions_with_diagrams": [],
                "reference_questions_with_diagrams": [],
            },
        }

    def save_individual_result(
        self, result: Dict, results_folder: Path, student_id: str
    ) -> None:
        """Save individual student result"""
        results_folder.mkdir(exist_ok=True)
        result_file = results_folder / f"{student_id}_result.json"

        try:
            with open(result_file, "w", encoding="utf-8") as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"✅ Result saved for {student_id}: {result_file}")
        except Exception as e:
            logger.error(f"❌ Error saving result for {student_id}: {e}")

    def grade_all_students(
        self, output_folder: str, results_folder: str, answer_key_pdf_path: str
    ) -> Dict:
        """Main function to grade all students using enhanced multi-pass extraction"""

        logger.info(f"🔍 Extracting answer key from: {answer_key_pdf_path}")
        self.answer_key, self.answer_key_diagrams = (
            self.extract_answer_key_from_pdf(answer_key_pdf_path)
        )

        if not self.answer_key:
            raise ValueError("❌ Failed to extract answer key from PDF")

        # DEBUG: Show what was extracted
        logger.info(f"📋 EXTRACTED QUESTIONS: {list(self.answer_key.keys())}")
        total_marks = 0
        for q_num, q_data in self.answer_key.items():
            marks = q_data["allocated_marks"]
            criteria_count = len(q_data.get("evaluation_criteria", []))
            criteria_status = q_data.get("criteria_status", "defined")
            total_marks += marks
            logger.info(
                f"  {q_num}: {marks} marks, {criteria_count} criteria, status: {criteria_status}"
            )
            if criteria_status == "defined":
                logger.info(
                    f"    Criteria: {q_data.get('evaluation_criteria', [])}"
                )
            else:
                logger.info(f"    Criteria: NOT DEFINED")

        logger.info(f"📊 Total marks in answer key: {total_marks}")
        logger.info(
            f"📋 Total questions found: {len(self.answer_key)} (DYNAMIC)"
        )

        # Setup paths
        output_path = Path(output_folder)
        results_path = Path(results_folder)

        # Find all JSON files
        json_files = list(output_path.glob("*.json"))

        if not json_files:
            logger.warning("⚠️  No JSON files found in output folder")
            return {"total_students": 0, "processed": 0, "errors": 0}

        logger.info(f"📚 Found {len(json_files)} student files to grade")

        # Grade each student using enhanced approach
        processed = 0
        errors = 0

        for json_file in json_files:
            try:
                student_id = json_file.stem
                logger.info(f"📝 Grading student: {student_id}")

                student_answer = self.load_student_answer(json_file)

                if not student_answer:
                    logger.error(f"❌ Failed to load answer for {student_id}")
                    errors += 1
                    continue

                result = self.grade_student(
                    student_answer, output_path, student_id
                )

                self.save_individual_result(result, results_path, student_id)

                if "error" not in result:
                    score = result["total_score"]
                    max_score = result["max_possible_score"]
                    percentage = (
                        (score / max_score * 100) if max_score > 0 else 0
                    )
                    logger.info(
                        f"✅ Completed grading for {student_id}: {score}/{max_score} ({percentage:.1f}%)"
                    )
                    processed += 1
                else:
                    logger.error(
                        f"❌ Error grading {student_id}: {result['error']}"
                    )
                    errors += 1

            except Exception as e:
                logger.error(f"❌ Error processing {json_file}: {e}")
                errors += 1

        # Cleanup
        temp_folder = Path("temp_answer_key_diagrams")
        if temp_folder.exists():
            shutil.rmtree(temp_folder)
            logger.info("🧹 Cleaned up temporary diagram files")

        summary = {
            "total_students": len(json_files),
            "processed": processed,
            "errors": errors,
            "results_folder": str(results_path),
            "answer_key_questions": len(self.answer_key),
            "total_possible_marks": sum(
                q["allocated_marks"] for q in self.answer_key.values()
            ),
            "grading_method": "multi_pass_enhanced_extraction",
        }

        logger.info(f"🎯 Enhanced multi-pass grading completed!")
        logger.info(f"   📊 Processed: {processed}/{len(json_files)} students")
        logger.info(f"   ❌ Errors: {errors}")
        logger.info(f"   📁 Results saved in: {results_path}")
        logger.info(
            f"   📋 Questions handled: {len(self.answer_key)} (DYNAMIC)"
        )
        logger.info(
            f"   🔧 Method: Multi-pass enhanced extraction with 4 fallback methods"
        )

        return summary
