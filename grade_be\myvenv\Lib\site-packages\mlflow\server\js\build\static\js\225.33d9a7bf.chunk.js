"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[225],{11473:function(e,r,t){t.d(r,{NW:function(){return l},OT:function(){return c},Yc:function(){return d}});t(31014);var n=t(37504),s=t.n(n),o=t(75826);let i=null;const c=()=>i||(i=new o.Converter,i.setFlavor("github"),i),u={allowedTags:["h1","h2","h3","h4","h5","h6","h7","h8","blockquote","p","a","ul","ol","nl","li","ins","b","i","strong","em","strike","code","hr","br","div","table","thead","tbody","tr","th","td","pre","del","sup","sub","dl","dt","dd","kbd","q","samp","samp","var","hr","rt","rp","summary","iframe","img","caption","figure"],allowedAttributes:{a:["href","name","target"],img:["src","longdesc"],div:["itemscope","itemtype"]}},l=e=>s()(e,u),d=e=>e.replace(new RegExp("<a","g"),'<a target="_blank"')},25869:function(e,r,t){t.d(r,{h:function(){return o}});t(31014);var n=t(93215),s=t(50111);const o=e=>r=>{const t=(0,n.zy)(),o=(0,n.Zp)(),i=(0,n.g)();return(0,s.Y)(e,{params:i,location:t,navigate:o,...r})}},27705:function(e,r,t){t.d(r,{g:function(){return i}});var n=t(31014),s=t(76010),o=t(50111);class i extends n.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e,r){this.setState({error:e}),console.error(e,r)}renderErrorMessage(e){return this.props.showServerError?(0,o.FD)("div",{children:["Error message: ",e.message]}):""}render(){const{children:e}=this.props,{error:r}=this.state;return r?(0,o.Y)("div",{children:(0,o.FD)("p",{children:[(0,o.Y)("i",{"data-testid":"icon-fail",className:"fa fa-exclamation-triangle icon-fail",css:c.wrapper}),(0,o.Y)("span",{children:" Something went wrong with this section. "}),(0,o.Y)("span",{children:"If this error persists, please report an issue "}),(0,o.Y)("a",{href:s.A.getSupportPageUrl(),target:"_blank",children:"here"}),".",this.renderErrorMessage(r)]})}):e}}const c={wrapper:{marginLeft:-2}}},30214:function(e,r,t){t.d(r,{W:function(){return u}});var n=t(89555),s=(t(31014),t(88443)),o=t(32599),i=t(48012),c=t(50111);const u=e=>{let{className:r}=e;const{theme:t}=(0,o.u)();return(0,c.Y)(i.vwO,{componentId:"codegen_mlflow_app_src_shared_building_blocks_previewbadge.tsx_14",className:r,css:(0,n.AH)({marginLeft:t.spacing.xs},""),color:"turquoise",children:(0,c.Y)(s.A,{id:"8qJt7/",defaultMessage:"Experimental"})})}},34860:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHcAAAB3CAYAAAA5Od+KAAAAAXNSR0IArs4c6QAABBdpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IlhNUCBDb3JlIDUuNC4wIj4KICAgPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4KICAgICAgPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIKICAgICAgICAgICAgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iCiAgICAgICAgICAgIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIgogICAgICAgICAgICB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iCiAgICAgICAgICAgIHhtbG5zOnRpZmY9Imh0dHA6Ly9ucy5hZG9iZS5jb20vdGlmZi8xLjAvIj4KICAgICAgICAgPHhtcE1NOkRlcml2ZWRGcm9tIHJkZjpwYXJzZVR5cGU9IlJlc291cmNlIj4KICAgICAgICAgICAgPHN0UmVmOmluc3RhbmNlSUQ+eG1wLmlpZDpERTk4ODkyMDVDMEQxMUU4QjcwMkI4NjVBQkFBMDNBNzwvc3RSZWY6aW5zdGFuY2VJRD4KICAgICAgICAgICAgPHN0UmVmOmRvY3VtZW50SUQ+eG1wLmRpZDpERTk4ODkyMTVDMEQxMUU4QjcwMkI4NjVBQkFBMDNBNzwvc3RSZWY6ZG9jdW1lbnRJRD4KICAgICAgICAgPC94bXBNTTpEZXJpdmVkRnJvbT4KICAgICAgICAgPHhtcE1NOkRvY3VtZW50SUQ+eG1wLmRpZDpERTk4ODkyMzVDMEQxMUU4QjcwMkI4NjVBQkFBMDNBNzwveG1wTU06RG9jdW1lbnRJRD4KICAgICAgICAgPHhtcE1NOkluc3RhbmNlSUQ+eG1wLmlpZDpERTk4ODkyMjVDMEQxMUU4QjcwMkI4NjVBQkFBMDNBNzwveG1wTU06SW5zdGFuY2VJRD4KICAgICAgICAgPHhtcDpDcmVhdG9yVG9vbD5BZG9iZSBQaG90b3Nob3AgQ0MgMjAxOCAoTWFjaW50b3NoKTwveG1wOkNyZWF0b3JUb29sPgogICAgICAgICA8dGlmZjpPcmllbnRhdGlvbj4xPC90aWZmOk9yaWVudGF0aW9uPgogICAgICA8L3JkZjpEZXNjcmlwdGlvbj4KICAgPC9yZGY6UkRGPgo8L3g6eG1wbWV0YT4KUEkHLAAAE61JREFUeAHtXQt4VNWdP+fMJBG0IjMDVAUSqe1WbVVqSh4gJARB+ulHqwiCiu5aF6kk8KGt1u6u0e3qVlR0gmxja9XqiqBr0T7sQhJSUJII1vdaX5BMqAp5EAkEksycs78zyR1mJjOTOzPnzCPJ/ZLv3jn3nN///z+/e94vQkauIRsDdMhaJg0Tgha91TGGdZExnBGfrRZm6Tze293z2oxxnUPZfp/B6WpkUX3bRCvlFwhCzwOXZ1MicmBLNv4d+LfhP9LlxssW/P+dCPIFYeIjKsiHlJMPuzj5a7qTn1bkXrVZWFonteVSKi6mgs5CWiwAMfZI7MXxTggCogl5HQ/brRZavXWavTkOvIQHTXlyL9ojMsb0tl5KCb0SZF6GGNJFppnI/4gQ+hIi7cWqPFsDwVdmJlCy/KQsucX1LRdZBP0hCF2MGBybrAiKIFem4mc8nP+mtnD8JxH8Je1VSpErU+lpva2LkCJWI0ZykxYrUQoWlPyFceKckW/fUk5RYqfIlRLkFuxqHjWKjV6OytCtiJeJKRI30atByaeomK2zfcX+q+fPoz3RA6gNkVRyi7YLq/Wk1n8UlN4Ds76q1rSkojWj5n4PP257sraYyhp5Uq6kkVtS33YJGqIPw+pzk2J5YoR+TDgtqy60/zkx4gKlJJzc2btaz6SMSFIXBqoydH8hkl92C8uq2oKxjYm0MqHkzq5rvYFSL7FjEmlkisg6Cj1ur86zb0hUEyoh5JY0HLYT0fM4jFuQIhGdNDUQ4c9X5TsWJUIBq24hl9S1T+eiZzPknKFbVqrjCyJeszAmWwQJuZhOKXPqWks55dshY7gTy1Ec/Yfd5ZiVyC5MLdmybOJYRrWvR214uc6PJ02wP0cnx7U1eY6aROtrUS3w8j2fjfZYen4P3ISUK6r1V4snXsnscc/bNn3C+2pxzaEpTblFew47LO6eP0L0NHPih6yvXpSvdyK1PpiomnGomFRG7vwdn4/rzsioQdnyrVCCho8b3Ytx4aurpzl2J9tmJRWqebu+tI0QK6kUmzIJmZoKxEpt4m4KyTK2y937CrKA4Zxiu9CXvKqmwPFrGampcsVFrpwZ0e5uexHGDNsyFrM03qOULq4psP9fqpBq6BFXtnxoUhuaO2SeATb87rTymDg2rTo/9YiVXMScctFBsQLtt5tTnNA26NeE/xYUG4cxo8OB52IFOndgus1NIPUFBVjhISoab4SciaQ0++7wnsK/gc3RXyUNbQWYNvoXhMyIPrS2EAcFFbVUsF2Yb/UGPtt3q3JtX/pLm1PXtgh+Nvm7xfBc7xGWJVpHeJwfn8poRiVmdF4t9cP9B6R08pZodY065c7Y2TGWCLeMoOQTK4gLw4cbuYe8OLPAvmewKS6ciNExfc19sYo5BeT+Qxb7v76RS3ujjWjT/itcuZiRsgm5zBQjDH4/JTY0TSU/yt5ruJm5R01uVob7VwCeZAZckx+UBuT3nNBHZ+bbqgxCzfTtMSIykApiUesAwi3DcN3WWAKbCoPqNqloWgMi74P/4IRzKmZmPS02i5lkEfWYwoOnqMgtqWu5BmGuNAuu2B+sp8+hOPj3qgLHBxLbDKH+OnDKTkbk+TuZed7mYXxZ7bTxX5jxHJOfys8cdH3zb9GbNT9seEEKyUHXT/Bekm/qMl1bLqk/MAHCnaZQlXuiOxnj36nKty81iI1NBI/mY3Yjjf/04jz7pVqJfaSxmPb0voOSNTyx/cZilnQ5ebTxHLO2mzZWCMtDKHMGW55hVq5Zf1+iHbm6Jt/2FD6sqJNcsBAmyCkyTzdxNUHekqo8e12VCc8xeUEfATvYXC6EuBPhzSayTMrZBoSZbSY+TIGW1LcUg9ilMRkRe6Bawcl56PV50owhpsQwauZj/p/uXutUlK91pjBj8eTcP5F+4aoFSf+C4KY4OCFGFJFHXdee+B3+aVBgfFrSj5zQlsjrPpvLPqem0PF3lUKRC5wSAe84mkkrqvMdC1+9+LRDEfzF92q9awGlnndQr5sRKxDysPvIQ82jBgs/6Je8o6H9elRkzh8MSNH7XkTw9TV54zYqwguAQaRghUqAk/HjA2R3i6sLbe8aDsrvzo+zGM1ci9RaqgD7TGL1yOk6P4+EFTHlXvW+yETt8u5IAArfHWOMzNdFrNQThfZXBugrxOOWUe7vVukkdv3+b1CaWQ/5Koj1moD+7NvIun2nDbDHzyFiym070vpPSLWJaNN2McHmbptme81PN+WPqDegTuW7OkH3P1cXjHvO56Ljwdm0jArPBkCfrBh+DLOy1ZyQ8nC4YVOuLGvRlffjcAEVuvciu1ywrUAvsVJfEHtqv957hMcztTpfL7HM2fSf+KCegkzVxHrNgD2ryS/+NjA36jcyLLk7G9qvQHRM6fen7QYFb0TbVVuLw19xFLfy70Es1JpeM33Cp/7vdDxz4pF1BzkZXdc1howedUM48LDkIoCy8iGccGT5D9TkO54O9161OyprP8ZIzm0JW4FXNuVtdEjLXj1tF+pEK4nsugxxhXTEIi0szhKaZ+zRnZ5jttnJXAUXIj60OLGKpvuQQ92hBRygoHYuWZm9LRg/dMqlHOOIWq9Oj2DLhgOxMhZ525G7UBy8rStGQeL1obAHkCunzuBT0NobhezxJ1rHQ0NZmky38vN60Nsme5V6dKiBzpkrCMaAg7EHkNuR01YETzoXQtfPnOZ4LFiRIf971eT3UAbq6ulDb1XG5cFxOIBczvEV6LwoXVOeQvtG6DQ1GJtnuu9FCSn3vVJ+UcoG8BZIbl+tS+cyy5e0dsgrjzLFgMu/9qUgrFwxaj+cmEee2HeSP3YAuXPqDsm5x2f6e1D5TDm/WyVeWmKJ7seh9wENup9MjrLp/rgB5KJbfY7/S8XPNVWF499UjJl+cGVf78aUnQodiqNvtcQfN4BcLF4q9n+p9BkzhJTipTNYL/0vqH9ctQn4aAL4CyKX5KsW2I93sMNil6v/Ri4ZA2smtaM//WX1kSG+Qyr3+CbX+cids/vAFAgcp14gmu+CbNY6HVSH0poxsdRex5h1JnGPv9BQ3Ucu55YLDEfVd+x1/DvVmGmPJ7pfgQ2HldvhEQPJZVzoIZeSbt7VuUu5EekOiIoVxiC3qzYDqdW3aZsv5RLKvqFakMRDh3l9bfFZyisPOnRNNCZmp9WqlolBBN/UVx+5mNtztmpBEo/2rSnSAZ3+mNy73kqxHeIsA/AEuYzkGI4q75jvXK8Sb0hhZbW+B3vcam2ikw08L7lyayFdNWWWQTSPCxumpOF9eW4vCq69ijU/iazfb5eYXnIzMlrHKxZgwPVUXTS22fgxcg8RA5R+GMI1PiePsEkAL7nuTKZpm3m6X9lqgfjMTdnQOIijSblytO/YAC+5Fh5iPq8SiaJVCcxQBhGiXbl51D1aYnrJJYIHDBUpFHZIIdaQhOKMBqz+V2KkYN7prn3kKkEMCTLSvg0ZLX6Ogqsnl1KLlKCbXD8rRh5DxoBgnpDuChx1kztg0pYCnYcYhMhSbpAQRySml1wumJZZecAPu9RBuUHpCshIxMVcMZlFubdjxEsuZdTLdExAkQONpNzI8SNXpnnbpIN4i+41Ix0ygJdcwXlbdKFN+87pX7xtOsDw8yh83YXKbGdW7wzLvmy5u1MXuZk7dnUkYgmosnhJNBCmxkxRLrP3uLd/wUtu/5Cc+sY0tGbM83Xlyg8lQHFiiE6NWZgXLceKcXnJ7QOlLjXgQSg0fQ5cDNJc/8+Kfd/EolK1lU5BGw3FT5ArxD7DUeUd61gC5tKqxE5/LEuRahuwo9MnBqaPXOyhqGe/YEoKRypVRnQH3rF1wYxAl/h/cXGCRx+5GFfUQy7OjX+14VBh/GoPMQQs3UGuVqzcKsbeNTB95DIP/avhqPouqOcHqjHTHq+iMQ82nKHcDk/vmwamj9zCAvtHcNTTmSHolSNZsxHlfXdG2OJAFyW/PiervuYykHzk9i+r1LVVUPbOuva5htBhfy9/PxOzQq9VHQ+UkFf9MX3kSkeUATv9X6p8pkysUImX1li2k69CE8ih2gYuxA5/zAByGaXV/i9VPuNLvaxo10Et02dV6pkILOz+tlqPHCZXMfiuAHJn5NlexxtdXZHMYmHlPsnD9eHR5ktheq4G8/eSssmf+uMGkCvLXeTbf/L3oPRZkKV92yApRU0fsHLsysf5vToURrtqSzBuALnyJfLt54M9KfyNb0c8rBAvvaDszddD4ak6lMbhHAN4G0Bu1qGOrRCufvXZCYsumdPQsvTEz2HyhIniSF33a7GWkkZSmtMQjD2A3Fe+J0cUtKwd9clG58w6eRyrz2EYPDDieURHDVlGHSqrT4SaHz6AXOkZWfMT8q7xGm9xd/8WbS9k08PgqnAtRjPzGk2WCuJxPxkKOyS52wscSOL0rVAB1LnR+SUNrWvU4ZlEqvxsNCkXVpO+4/cmN9Im4tfxA4VBoOQP/r1S/r5Ckis9IKk7/T1qeH6fM/ZnDbjhISuav41jXnaTu4i26aQBwrGTOSWel+EW6WyFgCDR/hAesS5cmLDkZrW3P4tAX4QLGJ87fayLH/vu9mn2xK0ArHDdTAmX7fhzQ5VP8dkTIjTONKBWugWp5B9CvFXl9CZZlbM9HFjY7ElWrGbXtzyIPZHXhgscg/thLHy6qarAvjmGsLEFkanHypAtiiv7AXS2BPpEoO+YssznQeys2JQ2Fwrzr+6J5DNsypWB+DHxS9wORgIw+w41pwYLtV6YUGLXNxbQDCaHwAxipboocTRekljHKS9AyuUapUjot8jKSS9FkhGR3Nri8RgCpAp6VOjaQ1b7xVvzTtsXSRll72QtvKLpDuQSOxDJOUG4+lKuzCXsp2xLALHYNVn8bLDiZdCmiDx+pr2z/QN88FOCImnQn4jiFkbodTg27X8H9azKw8N7J1CL5WnAXRIGskmUZueEeRe7Mya7YedU3WWsod9W2DDP+BHuHjHlykDe8wCouDUcQAT3Kk75+Qkl1umaC2Lfhk7hiJXqdkbQObZXzsZrcKrNbqRYnZUnQzeP4NQUH4OSKxGr8xxbsC2N2WaLBz0xP8PplfO0nl5pmCrvaLfKcwQw80/qOMH/1cBn8+fPDgwb5OL8fByraNyIIbxn8EZbc8dfKvYueYBgY25/t3DPYWvLwQE83LrCQj1yEt2o4He+3ziJGsXdkppC+y5tA8M+Yf0P6/blUGvzRtSS8oNfhf7dtwIu9DuTrhjdIQ7XD6nouRc1VrvJUCq87eNZGfeYBTKVciVY35kE9KdhgQX5XbfbeqEkNqwf1S+cTQvRzEFtWJgkViog3DGr4a2oub5P7a63kYIqgZNIYjmOsbmBLD+jy6z+g1aoAoBgXElDm0yUxT53bP+HsmYNTq/c4HPT/YAdwdkRtg6p9eYYRP0RlZHLogonT7y0isXI9mVZ962owiryjI/pF7ws+45o4Exny15QWOepb1uG40tkv7Mdkfs3HAm3uDrf9k40QuPy62w6FwtON0F2bJEsTG7qJU9pOdg8HefcLxWCL4LOY+PSO57Aguzm7Uf+LVqI6MgFem2+ff/suvbrMKXgKpxeWbr1gq8ejVZozP4rGm9EVuNE+NGxYqDycxQfxsBL7lPsmXAO8YhCRvgsccA1F55sIf0ODK3TpU0I90KC42uiFRJdthwtuir/ODOH0YxKVF6ujhsSqQDt7xcwGRALsMQ4DMWdDsyz+/8z48ZXC+CBfpeSsuyqWGBTn9wKVy6GzDbBuCmxGJjOYfARLscxbo/FaoPp2nKsAmIOJ2umzsZbQaysfQ87YpHq7o+HWBnvqZly0TlAae9TyDbnx/xxpHFAnLlbyW+ZvGKwvuPBTIy6QjUYYNzvH2ksprTnv4Ejy8Jhd2GI9Vk+ftIt8RIrIy51yEXTgx1sxnoxcSf0St3iQuPnhiLoCT5h8k1kkZou0tTIlp37J1LqeQ7xNmxX4SPFrucrJ5WpSLHG95f8FLLetQDEyk6Q4UosMitxGy+dXKqSWElw8lIu5hgxmvUATiFbaXxpw/B+FMQuI2U5L+qwPTnkyumewiPbrhfqMCo9MMVHGAi4As0dbZMEE58tO5uWgVi5RcOwJRYVp+dE1/FcncTKDzxxKRdjoMzmehJdE9elR8rSoKUgnRgJvgWkymlA2q/EpdxyLF4k7HFY9Il2q1JTwB8EsZybKGJlFCQu5RoRLk9nPsJuh2A5NnmS4Tx079SFSuNtpDR7wBJL3TYnnlzDoorGs7Cjy89h+BI4JU8PQx/1dzm0uJZkZqyNZvaESjWSH6nOxqlowN8FeheoNCyJWMcRqZXc2nsvWXG2kgn9sdqSfHINzZ17L2DMshrjl3LMNh2z63ZMVPkl51kPk7LTWwyzknlPHXKNWJBHlQnPTUjNKzAqpH6jaUOOqjslbyD7fYxkZDyTrOw3nCmpR66hqZw+apNzmPgSNJ8WolgeZ7xK/p26sAXCRm5lz5IfTUrc/LEoDU9dcv0NkZPVDjTNYpR9DxWwIsy2lJuGJK4Z5z0pU7yGaT5/Ihz/JieF+5uQjOf0IDc4Zh5qtpEMMZMJkY8uvPORfX8bXiYGe4vjdxMqeLtR/r8h7+TosdfJ7d/sjAMvKUHTk9xQUbWhaSzpFecQC5Ukn4FTP04HMWcK3r/Mg9IxWJTaby/tRBfgESyVO4KRmA7sAdKMeyPCNhL38UZsI384lIgRt5EYSJkY+H8lx8IKTKY5+wAAAABJRU5ErkJggg=="},53140:function(e,r,t){t.d(r,{Ay:function(){return a},dD:function(){return d}});var n=t(31014),s=t(10811),o=t(72877),i=t(96277),c=t(47664),u=t(50111);class l extends n.Component{constructor(){super(...arguments),this.state={shouldRender:!1,shouldRenderError:!1}}static getErrorRequests(e,r){return e.filter((e=>void 0!==e.error&&!(r&&r.includes(e.id)&&e.error.getErrorCode()===c.tG.RESOURCE_DOES_NOT_EXIST)))}static getDerivedStateFromProps(e){const r=!!e.requests.length&&e.requests.every((e=>e&&!1===e.active)),t=l.getErrorRequests(e.requests,e.requestIdsWith404sToIgnore);return{shouldRender:r,shouldRenderError:t.length>0,requestErrors:t}}getRenderedContent(){const{children:e,requests:r,customSpinner:t,permissionDeniedView:n,suppressErrorThrow:s,customRequestErrorHandlerFn:o}=this.props,{shouldRender:l,shouldRenderError:a,requestErrors:g}=this.state,m=g.filter((e=>e.error.getErrorCode()===c.tG.PERMISSION_DENIED));return"function"===typeof e?e(!l,a,r,g):l||a||this.props.shouldOptimisticallyRender?m.length>0&&n?n:(a&&!s&&(o?o(g):d(g)),e):t||(0,u.Y)(i.y,{})}render(){return this.getRenderedContent()}}l.defaultProps={requests:[],requestIdsWith404sToIgnore:[],shouldOptimisticallyRender:!1};const d=e=>{throw console.error("ERROR",e),Error(`A request error occurred.: ${e.error}`)};var a=(0,s.Ng)(((e,r)=>({requests:(0,o.EW)(r.requestIds,e)})))(l)},79085:function(e,r,t){t.d(r,{o:function(){return d},z:function(){return g}});var n=t(89555),s=(t(31014),t(48012)),o=t(32599),i=t(15579),c=t(88464),u=t(30214),l=t(50111);function d(e){let{menu:r}=e;const t=(0,l.Y)(s.W1t,{children:r.map((e=>{let{id:r,itemName:t,onClick:n,href:o,...i}=e;return(0,l.Y)(s.W1t.Item,{onClick:n,href:o,"data-test-id":r,...i,children:t},r)}))});return r.length>0?(0,l.Y)(s.msM,{overlay:t,trigger:["click"],placement:"bottomLeft",arrow:!0,children:(0,l.Y)(o.B,{componentId:"codegen_mlflow_app_src_shared_building_blocks_pageheader.tsx_54",icon:(0,l.Y)(s.ssM,{}),"data-test-id":"overflow-menu-trigger","aria-label":"Open header dropdown menu"})}):null}var a={name:"1gz4j9a",styles:"margin-left:0"};function g(e){const{title:r,breadcrumbs:t=[],titleAddOns:d=[],preview:g,children:m,spacerSize:A,hideSpacer:h=!1,dangerouslyAppendEmotionCSS:p}=e,{theme:I}=(0,o.u)();(0,c.A)();return(0,l.FD)(l.FK,{children:[(0,l.Y)(s.Y9Y,{breadcrumbs:t.length>0&&(0,l.Y)(s.QpV,{includeTrailingCaret:!0,children:t.map(((e,r)=>(0,l.Y)(s.QpV.Item,{children:e},r)))}),buttons:m,title:r,titleAddOns:(0,l.FD)(l.FK,{children:[g&&(0,l.Y)(u.W,{css:a}),d]}),dangerouslyAppendEmotionCSS:p}),(0,l.Y)(i.S,{css:(0,n.AH)({flexShrink:0,...h?{display:"none"}:{}},""),size:A})]})}},81866:function(e,r,t){t.d(r,{$0:function(){return d},$p:function(){return a},BE:function(){return f},CG:function(){return I},Gs:function(){return p},IP:function(){return g},QQ:function(){return l},Qs:function(){return R},SF:function(){return C},Tm:function(){return k},UA:function(){return h},e3:function(){return c},gL:function(){return N},jI:function(){return u},uB:function(){return E},zA:function(){return m},zr:function(){return A}});var n=t(48012),s=t(88443),o=t(24478),i=t(50111);const c={NONE:"None",STAGING:"Staging",PRODUCTION:"Production",ARCHIVED:"Archived"},u=[c.STAGING,c.PRODUCTION],l={[c.NONE]:"None",[c.STAGING]:"Staging",[c.PRODUCTION]:"Production",[c.ARCHIVED]:"Archived"},d={[c.NONE]:(0,i.Y)(n.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_37",children:l[c.NONE]}),[c.STAGING]:(0,i.Y)(n.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_38",color:"lemon",children:l[c.STAGING]}),[c.PRODUCTION]:(0,i.Y)(n.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_39",color:"lime",children:l[c.PRODUCTION]}),[c.ARCHIVED]:(0,i.Y)(n.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_40",color:"charcoal",children:l[c.ARCHIVED]})};let a=function(e){return e.APPLIED_TRANSITION="APPLIED_TRANSITION",e.REQUESTED_TRANSITION="REQUESTED_TRANSITION",e.SYSTEM_TRANSITION="SYSTEM_TRANSITION",e.CANCELLED_REQUEST="CANCELLED_REQUEST",e.APPROVED_REQUEST="APPROVED_REQUEST",e.REJECTED_REQUEST="REJECTED_REQUEST",e.NEW_COMMENT="NEW_COMMENT",e}({});(0,i.Y)("div",{style:{marginTop:-12},children:"_"});const g={READY:"READY"},m={[g.READY]:(0,i.Y)(s.A,{id:"f/An1W",defaultMessage:"Ready."})},A={[g.READY]:(0,i.Y)(s.A,{id:"zAvilr",defaultMessage:"Ready"})},h={[g.READY]:(0,i.Y)(o.vV,{})},p=1e4,I=25,C=75,R="name",f="timestamp",E={ASC:"ascend",DESC:"descend"},N=e=>(0,i.Y)(s.A,{id:"Ll6vbT",defaultMessage:"Model versions in the `{currentStage}` stage will be moved to the `Archived` stage.",values:{currentStage:e}}),k="https://mlflow.org/docs/latest/model-registry.html#using-registered-model-aliases"},96277:function(e,r,t){t.d(r,{y:function(){return i}});var n=t(34860),s=t(89555),o=t(50111);function i(e){let{showImmediately:r}=e;return(0,o.Y)("div",{css:e=>c.spinner(e,r),children:(0,o.Y)("img",{alt:"Page loading...",src:n})})}const c={spinner:(e,r)=>({width:100,marginTop:100,marginLeft:"auto",marginRight:"auto",img:{position:"absolute",opacity:0,top:"50%",left:"50%",width:2*e.general.heightBase,height:2*e.general.heightBase,marginTop:-e.general.heightBase,marginLeft:-e.general.heightBase,animation:`${s.i7`
          0% {
            opacity: 1;
          }
          100% {
            opacity: 1;
            -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
          `} 3s linear infinite`,animationDelay:r?"0s":"0.5s"}})}}}]);
//# sourceMappingURL=225.33d9a7bf.chunk.js.map