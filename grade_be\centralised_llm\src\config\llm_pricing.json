{"models": {"openai": ["gpt-4.1-nano"], "mistral": ["mistralai/Mistral-Small-24B-Instruct-2501", "mistralai/Mistral-7B-Instruct-v0.1", "mistralai/Mixtral-8x7B-Instruct-v0.1", "mistralai/Mistral-7B-Instruct-v0.3"], "meta_llama": ["meta-llama/Llama-3.3-70B-Instruct-Turbo", "meta-llama/Llama-Vision-Free", "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo"], "deepseek": ["deepseek-ai/DeepSeek-V3", "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free", "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B"], "qwen": ["Qwen/Qwen2.5-72B-Instruct-Turbo", "Qwen/QwQ-32B"], "Gemini": ["gemini-1.5-flash"]}, "pricing": {"Mistral AI": {"mistralai/Mistral-Small-24B-Instruct-2501": {"input": 0.002, "output": 0.006}, "mistralai/Mistral-7B-Instruct-v0.1": {"input": 0.0002, "output": 0.0002}, "mistralai/Mixtral-8x7B-Instruct-v0.1": {"input": 0.0009, "output": 0.0009}, "mistralai/Mistral-7B-Instruct-v0.3": {"input": 3e-05, "output": 5.5e-05}}, "Meta Llama": {"meta-llama/Llama-3.3-70B-Instruct-Turbo": {"input": 0.00088, "output": 0.00088}, "meta-llama/Llama-Vision-Free": {"input": 0.0, "output": 0.0}, "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo": {"input": 0.0035, "output": 0.0035}}, "DeepSeek AI": {"deepseek-ai/DeepSeek-V3": {"input": 0.00125, "output": 0.00125}, "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free": {"input": 0.0008, "output": 0.0008}, "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": {"input": 0.00015, "output": 0.00015}}, "Qwen": {"Qwen/Qwen2.5-72B-Instruct-Turbo": {"input": 0.0012, "output": 0.0012}, "Qwen/QwQ-32B": {"input": 0.0012, "output": 0.0012}}, "OpenAI": {"gpt-4o-mini": {"input": 0.00015, "output": 0.0006}, "gpt-4.1-nano": {"input": 0.0001, "output": 0.0004}}, "Gemini": {"gemini-1.5-flash": {"input": 0.00013, "output": 0.0005}}}}