{"version": 3, "file": "static/css/4461.d4d5a39c.chunk.css", "mappings": "AAAA,2BAGE,gBAAiB,CACjB,iBAAkB,CAFlB,eAAgB,CADhB,UAIF,CCLA,wBAGE,sBAAuB,CAFvB,YAAa,CACb,UAEF,CAEA,uCACE,YAAa,CACb,qBAAsB,CACtB,gBACF,CAEA,uDAGE,kBAAmB,CADnB,YAAa,CADb,eAGF,CAEA,sEACE,iBACF,CAEA,sDACE,eACF,CAEA,2CAEE,YAAa,CADb,QAAO,CAEP,qBACF,CAEA,qDAEE,QAAO,CADP,gBAEF,CAEA,yCACE,0BACF,CAEA,0DAGE,oBAAqB,CAFrB,kBAGF,CAIA,6DACE,cACF,CChDA,uBAEE,eAAgB,CADhB,UAEF,CAEA,oBAKE,UAAW,CADX,cAAe,CADf,iBAAkB,CADlB,eAAgB,CADhB,UAKF", "sources": ["common/components/RequestStateWrapper.css", "experiment-tracking/components/MetricView.css", "experiment-tracking/components/HtmlTableView.css"], "sourcesContent": [".RequestStateWrapper-error {\n  width: auto;\n  margin-top: 50px;\n  margin-left: auto;\n  margin-right: auto;\n}\n", ".metrics-plot-container {\n  display: flex;\n  width: 100%;\n  align-items: flex-start;\n}\n\n.metrics-plot-container .plot-controls {\n  display: flex;\n  flex-direction: column;\n  min-height: 500px;\n}\n\n.metrics-plot-container .plot-controls .inline-control {\n  margin-top: 25px;\n  display: flex;\n  align-items: center;\n}\n\n.metrics-plot-container .plot-controls .inline-control .control-label {\n  margin-right: 10px;\n}\n\n.metrics-plot-container .plot-controls .block-control {\n  margin-top: 25px;\n}\n\n.metrics-plot-container .metrics-plot-data {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.metrics-plot-container .metrics-plot-view-container {\n  min-height: 500px;\n  flex: 1;\n}\n\n.metrics-plot-container .metrics-summary {\n  margin: 20px 20px 20px 60px;\n}\n\n.metrics-plot-container .metrics-summary .html-table-view {\n  margin-bottom: 25px;\n  /* Shrink to fit, so that scroll bars are aligned with the edge of the table */\n  display: inline-block;\n}\n\n/* Reset min-width which is overridden in HtmlTableView as this breaks the\n   table layout when scrolling is enabled and widths are specified */\n.metrics-plot-container .metrics-summary .html-table-view th {\n  min-width: auto;\n}\n", "/* Overriding the table styles since antd tables take the full screen by default.\nWe would like to change it to auto to automatically grow based on the columns */\n\n.html-table-view table {\n  width: auto;\n  min-width: 400px;\n}\n\n.html-table-view th {\n  width: auto;\n  min-width: 200px;\n  margin-right: 80px;\n  font-size: 13px;\n  color: #888;\n}\n"], "names": [], "sourceRoot": ""}