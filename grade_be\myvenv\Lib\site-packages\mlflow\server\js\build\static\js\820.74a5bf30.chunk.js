"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[820],{28486:function(e,t,o){o.d(t,{tH:function(){return s}});var l=o(31014);function i(e,t,o,l){Object.defineProperty(e,t,{get:o,set:l,enumerable:!0,configurable:!0})}i({},"ErrorBoundary",(()=>s));i({},"ErrorBoundaryContext",(()=>n));const n=(0,l.createContext)(null),r={didCatch:!1,error:null};class s extends l.Component{state=(()=>r)();static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary=(()=>{var e=this;return function(){const{error:t}=e.state;if(null!==t){for(var o=arguments.length,l=new Array(o),i=0;i<o;i++)l[i]=arguments[i];e.props.onReset?.({args:l,reason:"imperative-api"}),e.setState(r)}}})();componentDidCatch(e,t){this.props.onError?.(e,t)}componentDidUpdate(e,t){const{didCatch:o}=this.state,{resetKeys:l}=this.props;o&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some(((e,o)=>!Object.is(e,t[o])))}(e.resetKeys,l)&&(this.props.onReset?.({next:l,prev:e.resetKeys,reason:"keys"}),this.setState(r))}render(){const{children:e,fallbackRender:t,FallbackComponent:o,fallback:i}=this.props,{didCatch:r,error:s}=this.state;let d=e;if(r){const e={error:s,resetErrorBoundary:this.resetErrorBoundary};if((0,l.isValidElement)(i))d=i;else if("function"===typeof t)d=t(e);else{if(!o)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");d=(0,l.createElement)(o,e)}}return(0,l.createElement)(n.Provider,{value:{didCatch:r,error:s,resetErrorBoundary:this.resetErrorBoundary}},d)}}function d(e){if(null==e||"boolean"!==typeof e.didCatch||"function"!==typeof e.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function a(){const e=(0,l.useContext)(n);d(e);const[t,o]=(0,l.useState)({error:null,hasError:!1}),i=(0,l.useMemo)((()=>({resetBoundary:()=>{e?.resetErrorBoundary(),o({error:null,hasError:!1})},showBoundary:e=>o({error:e,hasError:!0})})),[e?.resetErrorBoundary]);if(t.hasError)throw t.error;return i}i({},"useErrorBoundary",(()=>a));function u(e,t){const o=o=>(0,l.createElement)(s,t,(0,l.createElement)(e,o)),i=e.displayName||e.name||"Unknown";return o.displayName=`withErrorBoundary(${i})`,o}i({},"withErrorBoundary",(()=>u))},72314:function(e,t,o){o.d(t,{L:function(){return r}});var l=o(56675),i=o(95947);const n=l.J1`
  query MlflowGetExperimentQuery($input: MlflowGetExperimentInput!) @component(name: "MLflow.ExperimentRunTracking") {
    mlflowGetExperiment(input: $input) {
      apiError {
        code
        message
      }
      experiment {
        artifactLocation
        creationTime
        experimentId
        lastUpdateTime
        lifecycleStage
        name
        tags {
          key
          value
        }
      }
    }
  }
`,r=e=>{var t;let{experimentId:o,options:l={}}=e;const{data:r,loading:s,error:d,refetch:a}=(0,i.I)(n,{variables:{input:{experimentId:o}},skip:!o,...l});return{loading:s,data:null===r||void 0===r||null===(t=r.mlflowGetExperiment)||void 0===t?void 0:t.experiment,refetch:a,apolloError:d,apiError:(()=>{var e;return null===r||void 0===r||null===(e=r.mlflowGetExperiment)||void 0===e?void 0:e.apiError})()}}},77020:function(e,t,o){o.d(t,{n:function(){return g}});var l=o(31014),i=o(61226),n=o(28999),r=o(84865),s=o(95904),d=o(21363);class a extends d.Q{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;const o=this.options;this.options=this.client.defaultMutationOptions(e),(0,n.f8)(o,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){var e;this.hasListeners()||(null==(e=this.currentMutation)||e.removeObserver(this))}onMutationUpdate(e){this.updateResult();const t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:"undefined"!==typeof e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){const e=this.currentMutation?this.currentMutation.state:(0,r.$)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){s.j.batch((()=>{var t,o,l,i;if(this.mutateOptions&&this.hasListeners())if(e.onSuccess)null==(t=(o=this.mutateOptions).onSuccess)||t.call(o,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(l=(i=this.mutateOptions).onSettled)||l.call(i,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context);else if(e.onError){var n,r,s,d;null==(n=(r=this.mutateOptions).onError)||n.call(r,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(s=(d=this.mutateOptions).onSettled)||s.call(d,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context)}e.listeners&&this.listeners.forEach((e=>{let{listener:t}=e;t(this.currentResult)}))}))}}var u=o(27288),c=o(71233);function g(e,t,o){const r=(0,n.GR)(e,t,o),d=(0,u.jE)({context:r.context}),[g]=l.useState((()=>new a(d,r)));l.useEffect((()=>{g.setOptions(r)}),[g,r]);const v=(0,i.r)(l.useCallback((e=>g.subscribe(s.j.batchCalls(e))),[g]),(()=>g.getCurrentResult()),(()=>g.getCurrentResult())),h=l.useCallback(((e,t)=>{g.mutate(e,t).catch(m)}),[g]);if(v.error&&(0,c.G)(g.options.useErrorBoundary,[v.error]))throw v.error;return{...v,mutate:h,mutateAsync:v.mutate}}function m(){}},88457:function(e,t,o){o.d(t,{s:function(){return u}});var l=o(31014),i=o(9133),n=o(77735),r=o(63528),s=o(84174);const d=e=>["USE_RELATED_RUNS_DATA_FOR_LOGGED_MODELS",{runUuid:e}],a=async e=>{let{queryKey:[,{runUuid:t}]}=e;try{const e=await r.x.getRun({run_id:t});return null===e||void 0===e?void 0:e.run}catch(o){return null}},u=e=>{var t;let{loggedModels:o=[]}=e;const r=(0,l.useMemo)((()=>{const e=(0,i.compact)(null===o||void 0===o?void 0:o.flatMap((e=>{var t,o;return null===e||void 0===e||null===(t=e.data)||void 0===t||null===(o=t.metrics)||void 0===o?void 0:o.map((e=>e.run_id))}))),t=(0,i.compact)(null===o||void 0===o?void 0:o.map((e=>{var t;return null===e||void 0===e||null===(t=e.info)||void 0===t?void 0:t.source_run_id})));return(0,i.sortBy)((0,i.uniq)([...e,...t]))}),[o]),u=(0,n.E)({queries:r.map((e=>({queryKey:d(e),queryFn:a,cacheTime:1/0,staleTime:1/0,refetchOnWindowFocus:!1,retry:!1})))}),c=u.some((e=>{let{isLoading:t}=e;return t})),g=null===(t=u.find((e=>{let{error:t}=e;return t})))||void 0===t?void 0:t.error,m=(0,s.z)(u.map((e=>{let{data:t}=e;return t})));return{data:(0,l.useMemo)((()=>m.map((e=>e)).filter(Boolean)),[m]),loading:c,error:g}}},92246:function(e,t,o){o.d(t,{X:function(){return c}});var l=o(37368),i=o(28486),n=o(48012),r=o(32599),s=o(88443),d=o(50111);var a={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"};const u=e=>{var t;let{error:o}=e;return(0,d.Y)(n.ffj,{css:a,children:(0,d.Y)(n.SvL,{"data-testid":"fallback",title:(0,d.Y)(s.A,{id:"AOoWxS",defaultMessage:"Error"}),description:null!==(t=null===o||void 0===o?void 0:o.message)&&void 0!==t?t:(0,d.Y)(s.A,{id:"zmMR5p",defaultMessage:"An error occurred while rendering this component."}),image:(0,d.Y)(r.j,{})})})},c=e=>{let{children:t,resetKey:o}=e;return(0,d.Y)(i.tH,{FallbackComponent:u,resetKeys:[o],children:(0,d.Y)(l.Au,{children:t})})}},95885:function(e,t,o){o.r(t),o.d(t,{default:function(){return Ce}});var l=o(89555),i=o(32599),n=o(48012),r=o(4877),s=o.n(r),d=o(93215),a=o(88464),u=o(88443),c=o(58481),g=o(79085),m=o(85466),v=o(31014),h=o(52350),f=o(37616),p=o(56412),M=o(77484),Y=o(25866);const y=e=>(0,v.useCallback)((async()=>{var t,l,i,n;if(null===e||void 0===e||null===(t=e.info)||void 0===t||!t.model_id||null===e||void 0===e||null===(l=e.info)||void 0===l||!l.artifact_uri)return!0;const r=(0,M.qk)(Y.eh,e.info.model_id),s=await(0,M.UQ)(r),d=(await Promise.resolve().then(o.t.bind(o,77948,23))).load(await s.text());return void 0!==(null===d||void 0===d||null===(i=d.signature)||void 0===i?void 0:i.inputs)&&void 0!==(null===d||void 0===d||null===(n=d.signature)||void 0===n?void 0:n.outputs)}),[e]);var _=o(76010),x=o(50111);const w=e=>{var t;let{loggedModel:o,onSuccess:l}=e;const i=(0,a.A)(),n=(0,v.useCallback)((e=>{var t,o;null===l||void 0===l||l();const n=i.formatMessage({id:"UwpML4",defaultMessage:"Model registered successfully"});_.A.displayGlobalInfoNotification(`${n} ${null!==(t=null===e||void 0===e||null===(o=e.value)||void 0===o?void 0:o.status)&&void 0!==t?t:""}`)}),[i,l]),r=(0,v.useCallback)((e=>{var t;const o=i.formatMessage({id:"0Rao9q",defaultMessage:"Error registering model"}),l=null!==(t=e instanceof h.s?e.getMessageField():null===e||void 0===e?void 0:e.message)&&void 0!==t?t:String(e);_.A.displayGlobalErrorNotification(`${o} ${l}`)}),[i]);y(o);return null!==o&&void 0!==o&&null!==(t=o.info)&&void 0!==t&&t.artifact_uri&&o.info.model_id?(0,x.Y)(m.vR,{modelPath:o.info.artifact_uri,modelRelativePath:"",disabled:!1,loggedModelId:o.info.model_id,buttonType:"primary",showButton:!0,onRegisterSuccess:n,onRegisterFailure:r}):null};var b=o(77020),R=o(15579),A=o(8986);var D={name:"ozd7xs",styles:"flex-shrink:0"};const E=e=>{var t;let{experimentId:o,experiment:l,loading:r=!1,loggedModel:s,onSuccess:m}=e;const{theme:h}=(0,i.u)(),f=null===s||void 0===s||null===(t=s.info)||void 0===t?void 0:t.name,p=(0,d.Zp)(),M=(0,a.A)(),{modalElement:y,openModal:_}=(e=>{var t;let{loggedModel:o,onSuccess:l}=e;const[i,r]=(0,v.useState)(!1),s=(0,b.n)({mutationFn:async e=>{let{loggedModelId:t}=e;await(0,A.G)(`ajax-api/2.0/mlflow/logged-models/${t}`,"DELETE")}}),{mutate:d,isLoading:a,reset:c}=s;return{modalElement:(0,x.FD)(R.d,{componentId:"mlflow.logged_model.details.delete_modal",visible:i,onCancel:()=>r(!1),title:(0,x.Y)(u.A,{id:"0IVF6w",defaultMessage:"Delete logged model"}),okText:(0,x.Y)(u.A,{id:"Wm6dYs",defaultMessage:"Delete"}),okButtonProps:{danger:!0,loading:a},onOk:async()=>{var e;null!==o&&void 0!==o&&null!==(e=o.info)&&void 0!==e&&e.model_id?d({loggedModelId:o.info.model_id},{onSuccess:()=>{null===l||void 0===l||l(),r(!1)}}):r(!1)},cancelText:(0,x.Y)(u.A,{id:"6vII7y",defaultMessage:"Cancel"}),children:[(null===(t=s.error)||void 0===t?void 0:t.message)&&(0,x.FD)(x.FK,{children:[(0,x.Y)(n.FcD,{componentId:"mlflow.logged_model.details.delete_modal.error",closable:!1,message:s.error.message,type:"error"}),(0,x.Y)(R.S,{})]}),(0,x.Y)(u.A,{id:"AzdZPn",defaultMessage:"Are you sure you want to delete this logged model?"})]}),openModal:(0,v.useCallback)((()=>{c(),r(!0)}),[c])}})({loggedModel:s,onSuccess:()=>{p(c.h.getExperimentPageTabRoute(o,Y.fM.Models))}}),E=[(0,x.Y)(d.N_,{to:c.h.getExperimentPageTabRoute(o,Y.fM.Models),children:l&&"name"in l?null===l||void 0===l?void 0:l.name:o}),(0,x.Y)(d.N_,{to:c.h.getExperimentPageTabRoute(o,Y.fM.Models),children:(0,x.Y)(u.A,{id:"Uue10g",defaultMessage:"Models"})})];return(0,x.FD)("div",{css:D,children:[r?(0,x.Y)(C,{}):(0,x.FD)(g.z,{title:(0,x.FD)(x.FK,{children:[(0,x.Y)(I,{}),(0,x.Y)(x.FK,{children:f})]}),dangerouslyAppendEmotionCSS:{h2:{display:"flex",gap:h.spacing.sm},wordBreak:"break-word"},breadcrumbs:E,children:[(0,x.FD)(n.rId.Root,{children:[(0,x.Y)(n.rId.Trigger,{asChild:!0,children:(0,x.Y)(i.B,{componentId:"mlflow.logged_model.details.more_actions",icon:(0,x.Y)(n.ssM,{}),"aria-label":M.formatMessage({id:"uB7rmB",defaultMessage:"More actions"})})}),(0,x.Y)(n.rId.Content,{align:"end",children:(0,x.Y)(n.rId.Item,{componentId:"mlflow.logged_model.details.delete_button",onClick:_,children:(0,x.Y)(u.A,{id:"U+2XCK",defaultMessage:"Delete"})})})]}),(0,x.Y)(w,{loggedModel:s,onSuccess:m})]}),y]})},I=()=>{const{theme:e}=(0,i.u)();return(0,x.Y)("div",{css:(0,l.AH)({display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:e.colors.tagDefault,width:e.general.heightSm,height:e.general.heightSm,borderRadius:e.legacyBorders.borderRadiusMd},""),children:(0,x.Y)(n.oiI,{css:(0,l.AH)({color:e.colors.textSecondary},"")})})};var S={name:"1eoy87d",styles:"display:flex;justify-content:space-between"};const C=()=>{const{theme:e}=(0,i.u)();return(0,x.FD)("div",{css:(0,l.AH)({height:2*e.general.heightSm,marginBottom:e.spacing.sm},""),children:[(0,x.Y)("div",{css:(0,l.AH)({height:e.spacing.lg},""),children:(0,x.Y)(n.xUE,{css:(0,l.AH)({width:100,height:e.spacing.md},""),loading:!0})}),(0,x.FD)("div",{css:S,children:[(0,x.FD)("div",{css:(0,l.AH)({display:"flex",gap:e.spacing.sm,marginTop:.5*e.spacing.xs},""),children:[(0,x.Y)(n.xUE,{css:(0,l.AH)({width:e.general.heightSm,height:e.general.heightSm},""),loading:!0}),(0,x.Y)(n.xUE,{css:(0,l.AH)({width:160,height:e.general.heightSm},""),loading:!0})]}),(0,x.FD)("div",{css:(0,l.AH)({display:"flex",gap:e.spacing.sm},""),children:[(0,x.Y)(n.xUE,{css:(0,l.AH)({width:100,height:e.general.heightSm},""),loading:!0}),(0,x.Y)(n.xUE,{css:(0,l.AH)({width:60,height:e.general.heightSm},""),loading:!0})]})]})]})};var F=o(92246);const k=e=>{let{experimentId:t,modelId:o,activeTabName:l}=e;return(0,x.Y)(n.KSe.Root,{children:(0,x.FD)(n.KSe.List,{children:[(0,x.Y)(n.KSe.Item,{active:!l,children:(0,x.Y)(d.N_,{to:c.h.getExperimentLoggedModelDetailsPageRoute(t,o),children:(0,x.Y)(u.A,{id:"FBR3Q/",defaultMessage:"Overview"})})},"overview"),(0,x.Y)(n.KSe.Item,{active:"traces"===l,children:(0,x.Y)(d.N_,{to:c.h.getExperimentLoggedModelDetailsPageRoute(t,o,"traces"),children:(0,x.Y)(u.A,{id:"TeN9hs",defaultMessage:"Traces"})})},"traces"),(0,x.Y)(n.KSe.Item,{active:"artifacts"===l,children:(0,x.Y)(d.N_,{to:c.h.getExperimentLoggedModelDetailsPageRoute(t,o,"artifacts"),children:(0,x.Y)(u.A,{id:"Kwz1fc",defaultMessage:"Artifacts"})})},"artifacts")]})})};var L=o(14830),N=o(4874),T=o(58710),B=o(83858),z=o(67212),H=o(96034),K=o(56928),U=o(9133);var O=o(37368);const P=e=>{var t,o,r,s,d;let{loggedModel:c,onDescriptionChanged:g}=e;const m=null!==(t=null===c||void 0===c||null===(o=c.info)||void 0===o||null===(r=o.tags)||void 0===r||null===(s=r.find((e=>e.key===K.e)))||void 0===s?void 0:s.value)&&void 0!==t?t:void 0,[h,f]=(0,v.useState)(!1),p=(0,a.A)(),{theme:M}=(0,i.u)(),{patch:Y}=(e=>{let{loggedModelId:t}=e;const{isLoading:o,error:l,mutateAsync:i}=(0,b.n)({mutationFn:async e=>{const o={tags:(0,U.entries)(e).map((e=>{let[t,o]=e;return{key:t,value:o}}))};return(0,A.G)(`ajax-api/2.0/mlflow/logged-models/${t}/tags`,"PATCH",o)}});return{isLoading:o,error:l,patch:i}})({loggedModelId:null===c||void 0===c||null===(d=c.info)||void 0===d?void 0:d.model_id}),{handleError:y}=(0,O.tF)(),_=!m;return(0,x.FD)("div",{css:(0,l.AH)({marginBottom:M.spacing.md},""),children:[(0,x.FD)(i.T.Title,{level:4,css:(0,l.AH)({display:"flex",alignItems:"center",gap:M.spacing.xs},""),children:[(0,x.Y)(u.A,{id:"IB0MR8",defaultMessage:"Description"}),(0,x.Y)(i.B,{componentId:"mlflow.logged_models.details.description.edit",size:"small",type:"tertiary","aria-label":p.formatMessage({id:"7pRcdg",defaultMessage:"Edit description"}),onClick:()=>f(!0),icon:(0,x.Y)(n.R2l,{})})]}),_&&!h&&(0,x.Y)(i.T.Hint,{children:(0,x.Y)(u.A,{id:"npKSv/",defaultMessage:"No description"})}),(!_||h)&&(0,x.Y)(H.V,{defaultMarkdown:m,onSubmit:async e=>{try{await Y({[K.e]:e}),await g(),f(!1)}catch(t){y(t)}},onCancel:()=>f(!1),showEditor:h})]})};var j=o(68109),V=o(41028),G=o(70618),$=o(9856);const X=e=>{var t;let{getValue:o}=e;const{runName:l,runId:i}=null!==(t=o())&&void 0!==t?t:{};return(0,x.Y)(d.N_,{to:c.h.getDirectRunPageRoute(null!==i&&void 0!==i?i:""),children:l||i})};var q=o(28684),Z=o(3288);const W=e=>{let{getValue:t}=e;const{datasetDigest:o,datasetName:l,runId:i}=t();return l?(0,x.Y)(q.f,{datasetName:l,datasetDigest:o,runId:i}):"-"};var Q={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"};const J=e=>{let{loggedModel:t,relatedRunsData:o,relatedRunsLoading:r}=e;const{theme:s}=(0,i.u)(),{usingUnifiedDetailsLayout:d,detailsPageTableStyles:c,detailsPageNoEntriesStyles:g,detailsPageNoResultsWrapperStyles:m}=(0,Z.z)(),h=(0,a.A)(),[f,p]=(0,v.useState)(""),M=(0,v.useMemo)((()=>{var e,l,i;return r?[]:null!==(e=null===t||void 0===t||null===(l=t.data)||void 0===l||null===(i=l.metrics)||void 0===i?void 0:i.map((e=>{var l,i,n;const r=null===o||void 0===o||null===(l=o.find((t=>{var o;return(null===(o=t.info)||void 0===o?void 0:o.runUuid)===e.run_id})))||void 0===l||null===(i=l.info)||void 0===i?void 0:i.runName;return{...e,experimentId:null===(n=t.info)||void 0===n?void 0:n.experiment_id,runName:r}})))&&void 0!==e?e:[]}),[t,r,o]),Y=(0,v.useMemo)((()=>M.filter((e=>{let{key:t,dataset_name:o,dataset_digest:l,runName:i}=e;const n=f.toLowerCase();return(null===t||void 0===t?void 0:t.toLowerCase().includes(n))||(null===o||void 0===o?void 0:o.toLowerCase().includes(n))||(null===l||void 0===l?void 0:l.toLowerCase().includes(n))||(null===i||void 0===i?void 0:i.toLowerCase().includes(n))}))),[f,M]),y=(0,v.useMemo)((()=>[{id:"metric",accessorKey:"key",header:h.formatMessage({id:"R/4/8G",defaultMessage:"Metric"}),enableResizing:!0,size:240},{id:"dataset",header:h.formatMessage({id:"lCfyQO",defaultMessage:"Dataset"}),accessorFn:e=>{let{dataset_name:t,dataset_digest:o,run_id:l}=e;return{datasetName:t,datasetDigest:o,runId:l}},enableResizing:!0,cell:W},{id:"sourceRun",header:h.formatMessage({id:"z42Rsj",defaultMessage:"Source run"}),accessorFn:e=>{let{run_id:t,runName:o,experimentId:l}=e;return{runId:t,runName:o,experimentId:l}},enableResizing:!0,cell:X},{id:"value",header:h.formatMessage({id:"NU5iHM",defaultMessage:"Value"}),accessorKey:"value",enableResizing:!d,meta:d?{styles:{minWidth:120}}:{}}]),[h,d]),_=(0,G.N4)({data:Y,getCoreRowModel:(0,$.HT)(),getExpandedRowModel:(0,$.D0)(),getRowId:e=>{var t;return null!==(t=[e.key,e.dataset_digest,e.run_id].join("."))&&void 0!==t?t:""},enableColumnResizing:!0,columnResizeMode:"onChange",columns:y});return(0,x.FD)("div",{css:Q,children:[(0,x.Y)(i.T.Title,{level:4,children:(0,x.Y)(u.A,{id:"lPNTq7",defaultMessage:"Metrics ({length})",values:{length:M.length}})}),(0,x.Y)("div",{css:(0,l.AH)({padding:s.spacing.sm,border:`1px solid ${s.colors.borderDecorative}`,borderRadius:s.general.borderRadiusBase,flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},""),children:(()=>{if(r)return(0,x.Y)(n.QvX,{lines:3});if(!M.length)return(0,x.Y)("div",{css:g,children:(0,x.Y)(n.SvL,{description:(0,x.Y)(u.A,{id:"XwkX9B",defaultMessage:"No metrics recorded"})})});const e=Y.length<1;return(0,x.FD)(x.FK,{children:[(0,x.Y)("div",{css:(0,l.AH)({marginBottom:s.spacing.sm},""),children:(0,x.Y)(V.I,{componentId:"mlflow.logged_model.details.metrics.table.search",prefix:(0,x.Y)(V.S,{}),placeholder:h.formatMessage({id:"+Cr7Gu",defaultMessage:"Search metrics"}),value:f,onChange:e=>p(e.target.value),allowClear:!0})}),(0,x.FD)(n.XIK,{ref:e=>null===e||void 0===e?void 0:e.setAttribute("data-testid","logged-model-details-metrics-table"),scrollable:!0,empty:e?(0,x.Y)("div",{css:m,children:(0,x.Y)(n.SvL,{description:(0,x.Y)(u.A,{id:"hyHL1n",defaultMessage:"No metrics match the search filter"})})}):null,css:c,children:[(0,x.Y)(n.Hjg,{isHeader:!0,children:_.getLeafHeaders().map(((e,t)=>{var o;return(0,x.Y)(n.A0N,{componentId:"mlflow.logged_model.details.metrics.table.header",header:e,column:e.column,setColumnSizing:_.setColumnSizing,isResizing:e.column.getIsResizing(),css:(0,l.AH)({flexGrow:e.column.getCanResize()?0:1,...null===(o=e.column.columnDef.meta)||void 0===o?void 0:o.styles},""),style:{flexBasis:e.column.getCanResize()?e.column.getSize():void 0},children:(0,G.Kv)(e.column.columnDef.header,e.getContext())},e.id)}))}),_.getRowModel().rows.map((e=>(0,x.Y)(n.Hjg,{children:e.getAllCells().map((e=>{var t;return(0,x.Y)(n.nA6,{style:{flexGrow:e.column.getCanResize()?0:1,flexBasis:e.column.getCanResize()?e.column.getSize():void 0},css:(0,l.AH)({...null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.styles},""),children:(0,G.Kv)(e.column.columnDef.cell,e.getContext())},e.id)}))},e.id)))]})]})})()})]})};const ee=e=>{var t;let{getValue:o}=e;const l=null!==(t=o())&&void 0!==t?t:[];return(0,U.isEmpty)(l)?(0,x.Y)(x.FK,{children:"-"}):(0,x.Y)(n.nEg,{children:l.map((e=>{let{datasetDigest:t,datasetName:o,runId:l}=e;return(0,x.Y)(q.f,{datasetName:o,datasetDigest:t,runId:l},[o,t].join("."))}))})};var te={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"},oe={name:"11g4mt0",styles:"font-size:16px"};const le=e=>{let{loggedModel:t,relatedRunsData:o,relatedRunsLoading:r}=e;const{theme:s}=(0,i.u)(),{detailsPageTableStyles:d,detailsPageNoEntriesStyles:c,detailsPageNoResultsWrapperStyles:g}=(0,Z.z)(),m=(0,a.A)(),[h,f]=(0,v.useState)(""),p=(0,v.useMemo)((()=>{var e,l,i;if(r)return[];const n=null!==(e=null===t||void 0===t||null===(l=t.data)||void 0===l?void 0:l.metrics)&&void 0!==e?e:[],s=(0,U.groupBy)(n,"run_id");return null!==t&&void 0!==t&&null!==(i=t.info)&&void 0!==i&&i.source_run_id&&!s[t.info.source_run_id]&&(s[t.info.source_run_id]=[]),(0,U.entries)(s).map((e=>{var l,i,n;let[r,s]=e;const d=(0,U.uniqBy)(s,"dataset_name").map((e=>{let{dataset_digest:t,dataset_name:o}=e;return{datasetDigest:t,datasetName:o,runId:r}})).filter((e=>Boolean(e.datasetName)||Boolean(e.datasetDigest))),a=null===o||void 0===o||null===(l=o.find((e=>{var t;return(null===(t=e.info)||void 0===t?void 0:t.runUuid)===r})))||void 0===l||null===(i=l.info)||void 0===i?void 0:i.runName;return{runId:r,runName:a,datasets:d,experimentId:null===t||void 0===t||null===(n=t.info)||void 0===n?void 0:n.experiment_id}}))}),[t,r,o]),M=(0,v.useMemo)((()=>p.filter((e=>{let{runName:t,datasets:o}=e;const l=h.toLowerCase();return(null===t||void 0===t?void 0:t.toLowerCase().includes(l))||o.find((e=>{var t;return null===(t=e.datasetName)||void 0===t?void 0:t.toLowerCase().includes(l)}))}))),[h,p]),Y=(0,v.useMemo)((()=>[{id:"run",header:m.formatMessage({id:"/4Aok8",defaultMessage:"Run"}),enableResizing:!0,size:240,accessorFn:e=>{let{runId:t,runName:o,experimentId:l}=e;return{runId:t,runName:o,experimentId:l}},cell:X},{id:"input",header:m.formatMessage({id:"ubeHow",defaultMessage:"Input"}),accessorKey:"datasets",enableResizing:!1,cell:ee}]),[m]),y=(0,G.N4)({data:M,getCoreRowModel:(0,$.HT)(),getExpandedRowModel:(0,$.D0)(),getRowId:e=>e.key,enableColumnResizing:!0,columnResizeMode:"onChange",columns:Y});return(0,x.FD)("div",{css:te,children:[(0,x.Y)(i.T.Title,{css:oe,children:"Runs"}),(0,x.Y)("div",{css:(0,l.AH)({padding:s.spacing.sm,border:`1px solid ${s.colors.borderDecorative}`,borderRadius:s.general.borderRadiusBase,flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},""),children:(()=>{if(r)return(0,x.Y)(n.QvX,{lines:3});if(!p.length)return(0,x.Y)("div",{css:c,children:(0,x.Y)(n.SvL,{description:(0,x.Y)(u.A,{id:"TC9IhO",defaultMessage:"No runs"})})});const e=M.length<1;return(0,x.FD)(x.FK,{children:[(0,x.Y)("div",{css:(0,l.AH)({marginBottom:s.spacing.sm},""),children:(0,x.Y)(V.I,{componentId:"mlflow.logged_model.details.runs.table.search",prefix:(0,x.Y)(V.S,{}),placeholder:m.formatMessage({id:"eQV8Hs",defaultMessage:"Search runs"}),value:h,onChange:e=>f(e.target.value),allowClear:!0})}),(0,x.FD)(n.XIK,{scrollable:!0,ref:e=>null===e||void 0===e?void 0:e.setAttribute("data-testid","logged-model-details-runs-table"),empty:e?(0,x.Y)("div",{css:g,children:(0,x.Y)(n.SvL,{description:(0,x.Y)(u.A,{id:"5bBqLk",defaultMessage:"No runs match the search filter"})})}):null,css:d,children:[(0,x.Y)(n.Hjg,{isHeader:!0,children:y.getLeafHeaders().map(((e,t)=>(0,x.Y)(n.A0N,{componentId:"mlflow.logged_model.details.runs.table.header",header:e,column:e.column,setColumnSizing:y.setColumnSizing,isResizing:e.column.getIsResizing(),css:(0,l.AH)({flexGrow:e.column.getCanResize()?0:1},""),style:{flexBasis:e.column.getCanResize()?e.column.getSize():void 0},children:(0,G.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),y.getRowModel().rows.map((e=>(0,x.Y)(n.Hjg,{children:e.getAllCells().map((e=>(0,x.Y)(n.nA6,{style:{flexGrow:e.column.getCanResize()?0:1,flexBasis:e.column.getCanResize()?e.column.getSize():void 0},multiline:!0,children:(0,G.Kv)(e.column.columnDef.cell,e.getContext())},e.id)))},e.id)))]})]})})()})]})};var ie=o(88457);const ne=e=>{let{loggedModel:t,empty:o}=e;const l=(0,v.useMemo)((()=>{var e,o;return(null!==(e=null===(o=t.data)||void 0===o?void 0:o.metrics)&&void 0!==e?e:[]).reduce(((e,t)=>{let{dataset_digest:o,dataset_name:l,run_id:i}=t;return l&&o&&!e.find((e=>e.dataset_name===l&&e.dataset_digest===o))&&e.push({dataset_name:l,dataset_digest:o,run_id:i}),e}),[])}),[t]);return l.length?(0,x.Y)(n.nEg,{children:l.map((e=>{let{dataset_digest:t,dataset_name:o,run_id:l}=e;return(0,x.Y)(q.f,{datasetName:o,datasetDigest:t,runId:null!==l&&void 0!==l?l:null},[o,t].join("."))}))}):null!==o&&void 0!==o?o:(0,x.Y)(x.FK,{children:"-"})};var re=o(36506),se=o(31179),de=o(21317);var ae={name:"ozd7xs",styles:"flex-shrink:0"};const ue=e=>{let{loggedModel:t,empty:o}=e;const r=(0,v.useMemo)((()=>[t]),[t]),{theme:s}=(0,i.u)(),a=(0,se.b)({loggedModels:r});return(0,U.isEmpty)(a)?null!==o&&void 0!==o?o:(0,x.Y)(x.FK,{children:"-"}):(0,x.Y)(n.nEg,{children:null===a||void 0===a?void 0:a.map((e=>{let{displayedName:t,version:o,link:i}=e;return(0,x.FD)(d.N_,{to:i,css:(0,l.AH)({display:"flex",alignItems:"center",gap:s.spacing.sm},""),children:[(0,x.FD)("span",{css:(0,l.AH)({display:"flex",alignItems:"center",gap:s.spacing.sm,wordBreak:"break-all"},""),children:[(0,x.Y)(de.h,{css:ae})," ",t," "]}),(0,x.FD)(n.vwO,{componentId:"mlflow.logged_model.details.registered_model_version_tag",children:["v",o]})]},`${t}-${o}`)}))})};var ce=o(43102),ge=o(65287),me=o(81313);var ve=function(e){return e.DETAILS="DETAILS",e.DATASETS="DATASETS",e.MODEL_VERSIONS="MODEL_VERSIONS",e}(ve||{}),he={name:"1bmnxg7",styles:"white-space:nowrap"};var fe={name:"82a6rk",styles:"flex:1"};const pe=e=>{var t,o;let{onDataUpdated:r,loggedModel:s}=e;const{theme:g}=(0,i.u)(),{usingUnifiedDetailsLayout:m}=(0,Z.z)(),{data:h,loading:f,error:p}=(0,ie.s)({loggedModels:s?[s]:[]}),M=(0,v.useMemo)((()=>null===h||void 0===h?void 0:h.find((e=>{var t,o;return(null===(t=e.info)||void 0===t?void 0:t.runUuid)===(null===s||void 0===s||null===(o=s.info)||void 0===o?void 0:o.source_run_id)}))),[null===s||void 0===s||null===(t=s.info)||void 0===t?void 0:t.source_run_id,h]),Y=(0,v.useMemo)((()=>{var e,t;return(0,U.keyBy)((null!==(e=null===s||void 0===s||null===(t=s.data)||void 0===t?void 0:t.params)&&void 0!==e?e:[]).filter((e=>{let{key:t,value:o}=e;return!(0,U.isEmpty)(t)&&!(0,U.isEmpty)(o)})),"key")}),[null===s||void 0===s||null===(o=s.data)||void 0===o?void 0:o.params]),y=(e=>{var t,o,r,s,u,g,m,v,h,f,p,M,Y,y,_;let{loggedModel:w,relatedRunsLoading:b,relatedSourceRun:R}=e;const A=(0,a.A)(),{theme:D}=(0,i.u)(),E=w&&(0,x.FD)(x.FK,{children:[(0,x.Y)(me.wB,{keyValue:A.formatMessage({id:"MUUYWZ",defaultMessage:"Created at"}),value:(0,x.Y)(T.P,{value:null===w||void 0===w||null===(t=w.info)||void 0===t?void 0:t.creation_timestamp_ms})}),(0,x.Y)(me.wB,{keyValue:A.formatMessage({id:"UgF7ax",defaultMessage:"Status"}),value:(0,x.Y)(B.a,{data:w})}),(0,x.Y)(me.wB,{keyValue:A.formatMessage({id:"A+5KMV",defaultMessage:"Model ID"}),value:(0,x.Y)(z.t,{value:null!==(o=null===(r=w.info)||void 0===r?void 0:r.model_id)&&void 0!==o?o:"",css:he})}),(null===(s=w.info)||void 0===s?void 0:s.source_run_id)&&(null===(u=w.info)||void 0===u?void 0:u.experiment_id)&&(b||R)&&(0,x.Y)(me.wB,{keyValue:A.formatMessage({id:"d8cdYc",defaultMessage:"Source run"}),value:b?(0,x.Y)(n.xUE,{css:(0,l.AH)({width:200,height:D.spacing.md},"")}):(0,x.Y)(d.N_,{to:c.h.getRunPageRoute(null===(g=w.info)||void 0===g?void 0:g.experiment_id,null===(m=w.info)||void 0===m?void 0:m.source_run_id),children:null===R||void 0===R||null===(v=R.info)||void 0===v?void 0:v.runName})}),(null===(h=w.info)||void 0===h?void 0:h.source_run_id)&&(0,x.Y)(me.wB,{keyValue:A.formatMessage({id:"wFaOf0",defaultMessage:"Source run ID"}),value:(0,x.Y)(z.t,{value:null!==(f=null===(p=w.info)||void 0===p?void 0:p.source_run_id)&&void 0!==f?f:"",element:null!==(M=w.info)&&void 0!==M&&M.experiment_id?(0,x.Y)(d.N_,{to:c.h.getRunPageRoute(null===(Y=w.info)||void 0===Y?void 0:Y.experiment_id,null===(y=w.info)||void 0===y?void 0:y.source_run_id),children:null===(_=w.info)||void 0===_?void 0:_.source_run_id}):void 0})}),(0,x.Y)(me.wB,{keyValue:A.formatMessage({id:"oShuJS",defaultMessage:"Logged from"}),value:(0,x.Y)(ce.J,{loggedModel:w,displayDetails:!0,css:(0,l.AH)({paddingTop:D.spacing.xs,paddingBottom:D.spacing.xs,wordBreak:"break-all"},"")})})]});return[{id:ve.DETAILS,title:A.formatMessage({id:"fWPlO9",defaultMessage:"About this logged model"}),content:E},{id:ve.DATASETS,title:A.formatMessage({id:"msllnR",defaultMessage:"Datasets used"}),content:w&&(0,x.Y)(ne,{loggedModel:w,empty:(0,x.Y)(me.bw,{})})},{id:ve.MODEL_VERSIONS,title:A.formatMessage({id:"LpdcPw",defaultMessage:"Model versions"}),content:w&&(0,x.Y)(ue,{empty:(0,x.Y)(me.bw,{}),loggedModel:w})}]})({loggedModel:s,relatedRunsLoading:f,relatedSourceRun:M});return(0,x.Y)(re.Xs,{children:(0,x.FD)(ge.a,{css:fe,usingSidebarLayout:m,secondarySections:y,children:[(0,x.Y)(P,{loggedModel:s,onDescriptionChanged:r}),!m&&(0,x.FD)(x.FK,{children:[(0,x.Y)(i.T.Title,{level:4,children:(0,x.Y)(u.A,{id:"XcKgD5",defaultMessage:"Details"})}),(()=>{var e,t,o,i,r,a,m,v,h,p,Y;return s?(0,x.FD)(L.N,{children:[(0,x.Y)(N.Z,{title:(0,x.Y)(u.A,{id:"MUUYWZ",defaultMessage:"Created at"}),value:(0,x.Y)(T.P,{value:null===(e=s.info)||void 0===e?void 0:e.creation_timestamp_ms})}),(0,x.Y)(N.Z,{title:(0,x.Y)(u.A,{id:"UgF7ax",defaultMessage:"Status"}),value:(0,x.Y)(B.a,{data:s})}),(0,x.Y)(N.Z,{title:(0,x.Y)(u.A,{id:"A+5KMV",defaultMessage:"Model ID"}),value:(0,x.Y)(z.t,{value:null!==(t=null===(o=s.info)||void 0===o?void 0:o.model_id)&&void 0!==t?t:""})}),(null===(i=s.info)||void 0===i?void 0:i.source_run_id)&&(null===(r=s.info)||void 0===r?void 0:r.experiment_id)&&(f||M)&&(0,x.Y)(N.Z,{title:(0,x.Y)(u.A,{id:"d8cdYc",defaultMessage:"Source run"}),value:f?(0,x.Y)(n.xUE,{css:(0,l.AH)({width:200,height:g.spacing.md},"")}):(0,x.Y)(d.N_,{to:c.h.getRunPageRoute(null===(a=s.info)||void 0===a?void 0:a.experiment_id,null===(m=s.info)||void 0===m?void 0:m.source_run_id),children:null===M||void 0===M||null===(v=M.info)||void 0===v?void 0:v.runName})}),(null===(h=s.info)||void 0===h?void 0:h.source_run_id)&&(0,x.Y)(N.Z,{title:(0,x.Y)(u.A,{id:"wFaOf0",defaultMessage:"Source run ID"}),value:(0,x.Y)(z.t,{value:null!==(p=null===(Y=s.info)||void 0===Y?void 0:Y.source_run_id)&&void 0!==p?p:""})}),(0,x.Y)(N.Z,{title:(0,x.Y)(u.A,{id:"oShuJS",defaultMessage:"Logged from"}),value:(0,x.Y)(ce.J,{loggedModel:s,displayDetails:!0})}),(0,x.Y)(N.Z,{title:(0,x.Y)(u.A,{id:"msllnR",defaultMessage:"Datasets used"}),value:(0,x.Y)(ne,{loggedModel:s})}),(0,x.Y)(N.Z,{title:(0,x.Y)(u.A,{id:"LpdcPw",defaultMessage:"Model versions"}),value:(0,x.Y)(ue,{loggedModel:s})})]}):null})()]}),(null===p||void 0===p?void 0:p.message)&&(0,x.FD)(x.FK,{children:[(0,x.Y)(n.FcD,{closable:!1,message:(0,x.Y)(u.A,{id:"nNIors",defaultMessage:"Error when fetching related runs data: {error}",values:{error:p.message}}),type:"error",componentId:"mlflow.logged_model.details.related_runs.error"}),(0,x.Y)(R.S,{size:"md"})]}),(0,x.FD)("div",{css:[m?{display:"flex",flexDirection:"column"}:{display:"grid",gridTemplateColumns:"1fr 1fr",gridTemplateRows:"400px 400px",marginBottom:g.spacing.md},{gap:g.spacing.lg,overflow:"hidden"},""],children:[(0,x.Y)(J,{loggedModel:s,relatedRunsLoading:f,relatedRunsData:null!==h&&void 0!==h?h:void 0}),(0,x.Y)(j.y,{params:Y}),(0,x.Y)(le,{loggedModel:s,relatedRunsLoading:f,relatedRunsData:null!==h&&void 0!==h?h:void 0})]})]})})};var Me=o(93358),Ye=o(72314),ye=o(5643);var _e={name:"1jv19ey",styles:"height:100%;overflow:hidden;display:flex"};const xe=e=>{var t,o,l,i,n,r;let{loggedModel:s}=e;return(0,x.Y)("div",{css:_e,children:(0,x.Y)(ye.Ay,{isLoggedModelsMode:!0,loggedModelId:null!==(t=null===(o=s.info)||void 0===o?void 0:o.model_id)&&void 0!==t?t:"",artifactRootUri:null!==(l=null===s||void 0===s||null===(i=s.info)||void 0===i?void 0:i.artifact_uri)&&void 0!==l?l:"",useAutoHeight:!0,experimentId:null!==(n=null===s||void 0===s||null===(r=s.info)||void 0===r?void 0:r.experiment_id)&&void 0!==n?n:""})})};var we=o(91089);var be={name:"1hyob9y",styles:"position:relative;width:min-content"};const Re=e=>{let{modelId:t}=e;const{theme:o}=(0,i.u)(),r=`import mlflow\n          \nmlflow.set_active_model(model_id="${t}")`;return(0,x.FD)(x.FK,{children:[(0,x.Y)(i.T.Paragraph,{children:(0,x.Y)(u.A,{id:"3X52Jw",defaultMessage:"This tab displays all the traces logged to this logged model. MLflow supports automatic tracing for many popular generative AI frameworks. Follow the steps below to log your first trace. For more information about MLflow Tracing, visit the <a>MLflow documentation</a>.",values:{a:e=>(0,x.Y)(i.T.Link,{componentId:"mlflow.logged_model.traces.traces_table.quickstart_docs_link",href:"https://mlflow.org/docs/latest/llms/tracing/index.html",openInNewTab:!0,children:e})}})}),(0,x.Y)(i.T.Paragraph,{children:(0,x.Y)(u.A,{id:"PBeZnP",defaultMessage:"You can start logging traces to this logged model by calling {code} first:",values:{code:(0,x.Y)("code",{children:"mlflow.set_active_model"})}})}),(0,x.Y)(i.T.Paragraph,{children:(0,x.FD)("div",{css:be,children:[(0,x.Y)(p.i,{componentId:"mlflow.logged_model.traces.traces_table.set_active_model_quickstart_snippet_copy",css:(0,l.AH)({zIndex:1,position:"absolute",top:o.spacing.xs,right:o.spacing.xs},""),showLabel:!1,copyText:r,icon:(0,x.Y)(n.TdU,{})}),(0,x.Y)(f.z7,{showLineNumbers:!0,theme:o.isDarkMode?"duotoneDark":"light",style:{padding:`${o.spacing.sm}px ${o.spacing.md}px`},language:"python",children:r})]})}),(0,x.Y)(i.T.Paragraph,{children:(0,x.Y)(u.A,{id:"uXtIPX",defaultMessage:"Next, you can log traces to this logged model depending on your framework:"})})]})};var Ae=o(51455);var De={name:"3ytxc3",styles:"height:100%;overflow:hidden"};const Ee=e=>{var t,o,l,i;let{loggedModel:n}=e;const r=(0,v.useMemo)((()=>{var e,t;return[null!==(e=null===(t=n.info)||void 0===t?void 0:t.experiment_id)&&void 0!==e?e:""]}),[null===(t=n.info)||void 0===t?void 0:t.experiment_id]);return null!==(o=n.info)&&void 0!==o&&o.experiment_id?(0,x.Y)("div",{css:De,children:(0,x.Y)(Ae.A,{introductionText:(null===(l=n.info)||void 0===l?void 0:l.model_id)&&(0,x.Y)(Re,{modelId:n.info.model_id}),displayVersionWarnings:!1,children:(0,x.Y)(we.O,{experimentIds:r,loggedModelId:null===(i=n.info)||void 0===i?void 0:i.model_id,baseComponentId:"mlflow.logged_model.traces"})})}):null};var Ie={name:"1pbmicl",styles:"overflow:auto;flex:1"};const Se=()=>{var e;const{experimentId:t,loggedModelId:o,tabName:r}=(0,d.g)(),{clearUserActionError:a,currentUserActionError:c}=(0,O.tF)();s()(t,"Experiment ID must be defined"),s()(o,"Logged model ID must be defined");const{theme:g}=(0,i.u)(),{data:m,isLoading:v,error:h,refetch:f}=(0,Me.b)({loggedModelId:o}),{data:p,loading:M,apiError:Y,apolloError:y}=(0,Ye.L)({experimentId:t});if(h)throw h;const _=null!==Y&&void 0!==Y?Y:y;return(0,x.FD)(x.FK,{children:[(0,x.Y)(E,{experimentId:t,experiment:p,loggedModel:m,loading:v||M,onSuccess:f}),c&&(0,x.Y)(n.FcD,{componentId:"mlflow.logged_model.details.user-action-error",css:(0,l.AH)({marginBottom:g.spacing.sm},""),type:"error",message:null!==(e=c.displayMessage)&&void 0!==e?e:c.message,onClose:a}),(null===_||void 0===_?void 0:_.message)&&(0,x.Y)(n.FcD,{componentId:"mlflow.logged_model.details.experiment-error",css:(0,l.AH)({marginBottom:g.spacing.sm},""),type:"error",message:(0,x.Y)(u.A,{id:"E4Te7L",defaultMessage:"Experiment load error: {errorMessage}",values:{errorMessage:_.message}}),closable:!1}),(0,x.Y)(k,{experimentId:t,modelId:o,activeTabName:r}),(0,x.Y)("div",{css:Ie,children:v?(0,x.Y)(n.QvX,{lines:12}):m?"traces"===r?(0,x.Y)(Ee,{loggedModel:m}):"artifacts"===r?(0,x.Y)(xe,{loggedModel:m}):(0,x.Y)(pe,{onDataUpdated:f,loggedModel:m}):null})]})};var Ce=()=>{const{theme:e}=(0,i.u)();return(0,x.Y)(F.X,{children:(0,x.Y)(n.ffj,{css:(0,l.AH)({paddingTop:e.spacing.md,display:"flex",paddingBottom:e.spacing.md,overflow:"hidden",height:"100%",flexDirection:"column"},""),children:(0,x.Y)(Se,{})})})}}}]);
//# sourceMappingURL=820.74a5bf30.chunk.js.map