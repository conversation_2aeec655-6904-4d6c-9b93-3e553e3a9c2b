# Generated by Django 5.1.9 on 2025-07-01 09:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="SiteSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("log_retention_days", models.PositiveIntegerField(default=7)),
            ],
            options={
                "verbose_name": "Site Setting",
                "verbose_name_plural": "Site Settings",
            },
        ),
        migrations.CreateModel(
            name="Topic",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Question",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("description", models.TextField()),
                ("sample_input", models.TextField()),
                ("sample_output", models.TextField()),
                ("explanation", models.TextField(blank=True, null=True)),
                ("constraints", models.TextField(blank=True, null=True)),
                ("testcase_description", models.TextField(blank=True, null=True)),
                (
                    "difficulty",
                    models.CharField(
                        choices=[
                            ("easy", "Easy"),
                            ("medium", "Medium"),
                            ("hard", "Hard"),
                        ],
                        default="easy",
                        max_length=10,
                    ),
                ),
                ("year_asked", models.PositiveIntegerField(blank=True, null=True)),
                (
                    "custom_id",
                    models.PositiveIntegerField(blank=True, null=True, unique=True),
                ),
                (
                    "companies",
                    models.ManyToManyField(blank=True, to="executor.company"),
                ),
                ("topics", models.ManyToManyField(blank=True, to="executor.topic")),
            ],
        ),
        migrations.CreateModel(
            name="TestCase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("input_data", models.JSONField()),
                ("expected_output", models.JSONField()),
                ("is_sample", models.BooleanField(default=False)),
                (
                    "test_type",
                    models.CharField(
                        choices=[
                            ("normal", "Normal"),
                            ("edge", "Edge Case"),
                            ("boundary", "Boundary Case"),
                        ],
                        default="normal",
                        max_length=10,
                    ),
                ),
                (
                    "question",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="test_cases",
                        to="executor.question",
                    ),
                ),
            ],
        ),
    ]
