{"version": 3, "file": "static/js/7566.50d7f70e.chunk.js", "mappings": "wOAuBA,MAAMA,EAAqBC,IAGgD,IAH/C,MAC1BC,EAAK,qBACLC,GACmEF,EACnE,MAAMG,EAAYD,EAAqB,YAEjCE,EAAY,IAAID,SAChBE,EAAc,IAAIF,WAClBG,EAAkB,IAAIH,gBAE5B,MAAO,CACLI,SAAU,GACV,CAAC,OAAOH,OAAeC,KAAgB,CACrCG,YAAa,EACbC,WAAY,GACZC,cAAe,GACfC,QAAS,OACTC,WAAY,SACZL,SAAU,GACVM,WAAY,SACZC,WAAYb,EAAMc,WAAWC,cAE/B,CAACV,GAAkB,CACjBW,QAAS,GAAGhB,EAAMiB,QAAQC,UAAUlB,EAAMiB,QAAQE,UAErD,EAGI,SAASC,EAAmBC,GACjC,MAAM,MACJC,EAAK,UACLC,EAAS,gBACTC,EAAe,iBACfC,EAAgB,SAChBC,EAAQ,UACRC,EAAS,YACTC,EAAc,6CACZP,EAIEQ,EAAgBN,GAAa,CAAEO,UAAW,CAAC,MAC3CC,EAAmBN,EAAmB,KAAO,CAAC,MAE9C,MAAEzB,EAAK,qBAAEC,IAAyB+B,EAAAA,EAAAA,MAClC,cAAEC,IAAkBC,EAAAA,EAAAA,KAEpBC,GAAgBC,EAAAA,EAAAA,cACpBC,IAAA,IAAC,SAAEC,GAAkCD,EAAA,OACnCE,EAAAA,EAAAA,GAAA,OACEC,KAAKC,EAAAA,EAAAA,GAAa,CAAEC,MAAO1C,EAAM2C,QAAQC,WAAa,EAAGC,UAAWP,EAAW,qBAAkBQ,IAAaC,UAE9GR,EAAAA,EAAAA,GAACS,EAAAA,EAAgB,CACfR,KAAGS,EAAAA,EAAAA,IAAE,CACHC,IAAK,CAAER,MAAO1C,EAAM2C,QAAQC,WAAa,EAAGO,OAAQnD,EAAM2C,QAAQC,WAAa,IAChF,IACD,aAEMX,EADJK,EAEM,CAAAc,GAAA,SACEC,eAAe,oBAMjB,CAAAD,GAAA,SACEC,eAAe,kBAJjB,CAAE/B,aAWR,GAER,CAACtB,EAAOsB,EAAOW,IAGjB,OACEM,EAAAA,EAAAA,GAACe,EAAAA,IAAS,CACR1B,YAAaA,KACTC,EACJ0B,4BAA6BzD,EAAmB,CAAEE,QAAOC,yBACzDuD,wBAAyB,CACvB7B,YACA8B,mBAAoB,OACpBC,WAAYvB,GAEdJ,iBAAkC,OAAhBA,QAAgB,IAAhBA,EAAAA,OAAoBe,EACtCpB,SAAUA,EAASqB,UAEnBR,EAAAA,EAAAA,GAACe,EAAAA,IAAUK,MAAK,CAACC,OAAQtC,EAAMyB,UAC7BR,EAAAA,EAAAA,GAACsB,EAAAA,EAAoB,CAACrC,gBAAiBA,EAAgBuB,SAAE1B,EAAM0B,YAD7B,MAK1C,C,yKCjHA,IAAAV,EAAA,CAAAyB,KAAA,UAAAC,OAAA,cAAAC,EAAA,CAAAF,KAAA,SAAAC,OAAA,kBAAAE,EAAA,CAAAH,KAAA,SAAAC,OAAA,UAAAG,EAAA,CAAAJ,KAAA,SAAAC,OAAA,UAGO,MAAMI,EAA0BpE,IAgBhC,IAhBiC,UACtCqE,EAAS,gBACTC,EAAe,gBACfC,EAAe,aACfC,EAAY,QACZC,EAAO,kBACPC,EAAiB,SACjBC,GASD3E,EACC,MAAM4E,GAAOzC,EAAAA,EAAAA,MACN0C,EAAiBC,IAAsBC,EAAAA,EAAAA,WAAS,IAEjD,MAAE9E,IAAUgC,EAAAA,EAAAA,KAEZ+C,GAA0B3C,EAAAA,EAAAA,cAC7B4C,IACCX,GAAiBY,GAAYA,EAAQC,QAAQC,GAAkBA,IAAkBH,KAAO,GAE1F,CAACX,IAGGe,GAAsBhD,EAAAA,EAAAA,cACzB6C,IACC,MAAMI,EAAmBJ,EAEtBK,KAAKN,GACJA,EACGO,QAAQ,UAAW,IACnBC,cACAC,UAAU,EAAG,OAIjBP,QAAQF,GAAUA,EAAMU,OAAS,IAG9BC,EAAgBC,MAAMC,KAAK,IAAIC,IAAIT,IACzChB,EAAgBsB,GAChBd,GAAmB,EAAM,GAE3B,CAACR,IAGH,OAGE0B,EAAAA,EAAAA,IAACC,EAAAA,IAAY,CACXtB,SAAUA,EACVuB,aAAcA,CAACC,EAAKC,IAAW,OAAHA,QAAG,IAAHA,OAAG,EAAHA,EAAKC,MAAMZ,cAAca,WAAWH,EAAIV,eACpEc,YAAa3B,EAAK1C,cAAc,CAAAmB,GAAA,SAC9BC,eAAe,8CAGjBkD,YAAU,EACV/D,IAAGH,EACHmE,KAAK,OAML9E,SAAU0D,EACV5B,wBAAyB,CACvBiD,0BAA0B,EAC1BC,UAAWC,IAAA,IAAC,MAAEP,GAAOO,EAAA,OACnBpE,EAAAA,EAAAA,GAACqE,EAAAA,EAAoB,CACnBC,SAAO,EACPrE,IAAGwB,EACH8C,UAAQ,EACRC,QAASA,IAAMhC,EAAwBqB,EAAMY,YAC7CZ,MAAOA,EAAMY,YACb,GAGNC,wBAAyBpC,EACzBqC,KAAMtC,EACNwB,MAAO7B,GAAgB,GAAGxB,SAAA,CAEzBuB,EAAgBgB,KAAKN,IACpBzC,EAAAA,EAAAA,GAACyD,EAAAA,IAAamB,OAAM,CAAaf,MAAOpB,EAAO,cAAY,qBAAoBjC,UAC7EgD,EAAAA,EAAAA,IAAA,OAAiBvD,KAAGS,EAAAA,EAAAA,IAAE,CAAEvC,QAAS,OAAQ0G,YAAapH,EAAMiB,QAAQC,IAAI,IAAC6B,SAAA,EACvER,EAAAA,EAAAA,GAAA,OAAKC,IAAGyB,EAAclB,SAAEiC,KACxBzC,EAAAA,EAAAA,GAAA,OAAAQ,UACER,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,qBAJX2B,IADcA,KAY3BsC,OAAOC,QAAQ9C,GACbS,QAAOsC,IAAA,IAAE,CAAEC,GAAaD,EAAA,OAAKC,IAAiBjD,CAAO,IACrDc,KAAIoC,IAAA,IAAE1C,EAAO2C,GAAeD,EAAA,OAC3BnF,EAAAA,EAAAA,GAACyD,EAAAA,IAAamB,OAAM,CAAaf,MAAOpB,EAAO,cAAY,qBAAoBjC,UAC7EgD,EAAAA,EAAAA,IAAA,OAAiBvD,KAAGS,EAAAA,EAAAA,IAAE,CAAEvC,QAAS,OAAQ0G,YAAapH,EAAMiB,QAAQC,IAAI,IAAC6B,SAAA,EACvER,EAAAA,EAAAA,GAAA,OAAKC,IAAG0B,EAAcnB,SAAEiC,KACxBzC,EAAAA,EAAAA,GAAA,OAAAQ,UACER,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,oBAEfuE,OAAQ,CAAEpD,QAASmD,SANf3C,IADcA,EAWJ,MA7CrB6C,KAAKC,UAAU1D,GA+CP,E,qCC/GnB,MAKa2D,EAAqChI,IAU3C,IAV4C,MACjDiI,EAAK,UACLC,EAAS,WACTC,EAAU,iBACVC,GAMDpI,EACC,MAAOqI,EAAWC,IAAgBvD,EAAAA,EAAAA,WAAS,IACpCwD,GAAQC,EAAAA,IAAWC,WAEnBC,EAAcC,IAAmB5D,EAAAA,EAAAA,UAAiB,KACnD,MAAE9E,IAAUgC,EAAAA,EAAAA,MAGXsC,EAAiBqE,IAAsB7D,EAAAA,EAAAA,UAAmB,KAE1DP,EAAcF,IAAmBS,EAAAA,EAAAA,UAAmB,KAEpD8D,EAAwBC,IAA6B/D,EAAAA,EAAAA,UAAiB,KAEvEgE,GAAWC,EAAAA,EAAAA,MAKXC,GAAuB5G,EAAAA,EAAAA,cAC1B6G,IAA2B,IAADC,EACzB,IAAKlB,EACH,OAGF,MAAMmB,GACS,QAAbD,EAAAlB,EAAM/C,eAAO,IAAAiE,OAAA,EAAbA,EAAehE,QAAO7C,IAAA,IAAC,QAAEmC,GAASnC,EAAA,OAAKmC,IAAYyE,CAAa,IAAE3D,KAAIqB,IAAA,IAAC,MAAE3B,GAAO2B,EAAA,OAAK3B,CAAK,MAAK,GAE7FiE,IACFN,EAAmBQ,GACnB9E,EAAgB8E,GAChBN,EAA0BI,GAC1BZ,GAAa,GACf,GAEF,CAACL,IAIGoB,GAAoBC,EAAAA,EAAAA,UAAQ,KAChC,GAAU,OAALrB,QAAK,IAALA,IAAAA,EAAO/C,QACV,MAAO,GAET,MAAMqE,EAAsBtB,EAAM/C,QAAQsE,QACxC,CAACC,EAAUC,KAAgB,IAADC,EACxB,OAAKF,EAASG,MAAK3F,IAAA,IAAC,QAAEQ,GAASR,EAAA,OAAKQ,IAAYiF,EAAWjF,OAAO,KAGJ,QAA9DkF,EAAAF,EAASI,MAAK3F,IAAA,IAAC,QAAEO,GAASP,EAAA,OAAKO,IAAYiF,EAAWjF,OAAO,WAAC,IAAAkF,GAA9DA,EAAgEzE,QAAQ4E,KAAKJ,EAAWzE,OACjFwE,GAHE,IAAIA,EAAU,CAAEhF,QAASiF,EAAWjF,QAASS,QAAS,CAACwE,EAAWzE,QAG5D,GAEjB,IAEI8E,EAAuBR,EAAoBpE,QAC/CsC,IAAA,IAAGhD,QAASiD,GAAcD,EAAA,OAAKC,IAAiBmB,CAAsB,IAExE,OAAOrE,EACJe,KAAKN,IAAK,CACTA,QACAyC,aAAcqC,EAAqBF,MAAMpF,IAAO,IAAAuF,EAAA,OAC/B,QAD+BA,EAC9CvF,EAAQS,eAAO,IAAA8E,OAAA,EAAfA,EAAiBH,MAAMI,GAAeA,IAAehF,GAAM,QAG9DE,QAAOwC,IAAA,IAAC,aAAED,GAAcC,EAAA,OAAKD,CAAY,GAAC,GAC5C,CAAM,OAALO,QAAK,IAALA,OAAK,EAALA,EAAO/C,QAASV,EAAcqE,IAG5BnE,GAAoB4E,EAAAA,EAAAA,UACxB,SAAAY,EAAA,OACO,OAALjC,QAAK,IAALA,GAAc,QAATiC,EAALjC,EAAO/C,eAAO,IAAAgF,OAAT,EAALA,EAAgBV,QAA+B,CAACW,EAAMhG,KAA0B,IAAxB,MAAEc,EAAK,QAAER,GAASN,EACxE,MAAO,IAAKgG,EAAQ,CAAClF,GAAQR,EAAS,GACrC,CAAC,KAAM,CAAC,CAAC,GACd,CAACwD,IAoBGmC,GAAaC,EAAAA,EAAAA,SAAQ9F,EAAgB+F,QAAQC,OAAQ/F,EAAa8F,QAAQC,QAC1EC,EAAmBhG,EAAamB,OA5GF,GA8G9B8E,EAAYL,GAAcI,EAuHhC,MAAO,CAAEE,kBApHP1E,EAAAA,EAAAA,IAAC2E,EAAAA,EAAK,CACJ9I,YAAY,yFACZ+I,QAASvC,EACTwC,QACE7E,EAAAA,EAAAA,IAAA,OAAAhD,SAAA,EACER,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,yFACZkJ,QAASA,IAAMzC,GAAa,GAAOtF,UAEnCR,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,cAInBd,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,yFACZmJ,SAAS,EACTC,KAAK,UACLtG,SAAU8F,EACVM,QA1CGG,KACNjD,IAGLU,EAAgB,IAChBI,GAASoC,EAAAA,EAAAA,IAA0BlD,EAAMlE,KAAM8E,EAAwBtE,EAAiBC,IACrF4G,MAAK,KACJ9C,GAAa,GACJ,OAATJ,QAAS,IAATA,GAAAA,GAAa,IAEdmD,OAAOC,IACN,MAAMC,EAAwBD,EAAEE,mBAAqBF,EAAEG,sBAAsBxE,YAAcqE,EAAEI,KAC7F/C,EAAgB4C,EAAsB,IACtC,EA6BkBvI,UAEdR,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,sBAMvBqI,gBAAc,EACdpK,MACE4G,EACEA,EAAWU,IAEXrG,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,6CAEfuE,OAAQ,CAAEpD,QAASoE,KAIzB+C,SAAUA,IAAMtD,GAAa,GAC7BuD,gBAAgB,EAAM7I,SAAA,EAEtBR,EAAAA,EAAAA,GAACsJ,EAAAA,EAAWC,UAAS,CAAA/I,SACF,OAAhBoF,QAAgB,IAAhBA,EAAAA,GACC5F,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,gHAEfuE,OAAQ,CACNmE,KAAOC,IACLzJ,EAAAA,EAAAA,GAAA,KAAG0J,KAAMC,EAAAA,GAA4BC,IAAI,aAAaC,OAAO,SAAQrJ,SAClEiJ,UAObjG,EAAAA,EAAAA,IAACwC,EAAAA,IAAU,CAACD,KAAMA,EAAM+D,OAAO,WAAUtJ,SAAA,EACvCR,EAAAA,EAAAA,GAACgG,EAAAA,IAAW+D,KAAI,CAAAvJ,UACdR,EAAAA,EAAAA,GAAC4B,EAAuB,CACtBO,UAAU,EACVN,UAAWgF,EACX3E,kBAAmBA,EACnBD,QAASoE,EACTrE,aAAcA,EACdD,gBAAiBA,EACjBD,gBAAiBA,OAGrB0B,EAAAA,EAAAA,IAAA,OAAKvD,KAAGS,EAAAA,EAAAA,IAAE,CAAEvC,QAAS,OAAQ6L,cAAe,SAAUC,IAAKxM,EAAMiB,QAAQC,IAAI,IAAC6B,SAAA,CAC3EwH,IACChI,EAAAA,EAAAA,GAACkK,EAAAA,IAAK,CACJ7K,YAAY,yFACZ8K,KAAK,QACLC,SACEpK,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,oFAEfuE,OAAQ,CAAEgF,MAhMU,MAmMxB5B,KAAK,QACLlE,UAAU,IAGbsC,EAAkB9D,KAAIuH,IAAA,IAAC,MAAE7H,EAAK,aAAEyC,GAAcoF,EAAA,OAC7CtK,EAAAA,EAAAA,GAACkK,EAAAA,IAAK,CACJ7K,YAAY,yFACZ8K,KAAK,QAELC,SACEpK,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,0IAEfuE,OAAQ,CAAEH,aAA0B,OAAZA,QAAY,IAAZA,OAAY,EAAZA,EAAcjD,QAASQ,WAGnDgG,KAAK,OACLlE,UAAU,GATL9B,EAUL,IAEHyD,IACClG,EAAAA,EAAAA,GAACkK,EAAAA,IAAK,CACJ7K,YAAY,yFACZ8K,KAAK,QACLC,QAASlE,EACTuC,KAAK,QACLlE,UAAU,aAQKkC,uBAAsB,C,oJC9NnD,MAAM8D,EAAkBC,EAAAA,gBAaxB,MAAMC,UAAqBD,EAAAA,UAAmCE,WAAAA,GAAA,SAAAC,WAAA,KAC5DC,eAAkBC,IAChB,MAAM,KAAEnC,EAAI,UAAEoC,EAAS,OAAEC,GAAWC,KAAKlM,MACvB,UAAd+L,EAAMI,IAERvC,EAAKoC,GACkB,WAAdD,EAAMI,KAEfF,GACF,CACA,CAEFG,MAAAA,GACE,MAAM,QAAEC,EAAO,UAAEC,EAAS,OAAEC,EAAM,SAAE7K,GAAawK,KAAKlM,MACtD,OACEkB,EAAAA,EAAAA,GAACuK,EAAgBe,SAAQ,CAAA9K,SAEtBhD,IAAA,IAAC,QAAE+N,GAAS/N,EAAA,OACXwC,EAAAA,EAAAA,GAAA,OAAKZ,UAAW+L,EAAU,eAAiB,GAAG3K,SAC3C2K,GAECnL,EAAAA,EAAAA,GAACgG,EAAAA,IAAU,CAACwF,IAAKD,EAAQ/K,UAEvBR,EAAAA,EAAAA,GAACgG,EAAAA,IAAW+D,KAAI,CAAC0B,MAAO,CAAEC,OAAQ,GAAKnK,KAAM6J,EAAWO,aAAcN,EAAOD,GAAW5K,UACtFR,EAAAA,EAAAA,GAAC4L,EAAAA,EAAK,CACJvM,YAAY,2EACZwM,UAAWb,KAAKJ,eAChB,cAAY,oCAKlBpK,GAEE,GAId,EAaK,MAAMsL,UAAsBtB,EAAAA,UAIjCE,WAAAA,CAAY5L,GACViN,MAAMjN,GAAO,KAJfkN,aAAO,OACPjG,UAAI,OASJkG,YAAc,IAAM,IACfjB,KAAKlM,MAAMkN,QAAQjJ,KAAKmJ,GACzBA,EAAIC,SACA,IACKD,EACHhB,OAAQA,CAAChC,EAAWmC,KAClBrL,EAAAA,EAAAA,GAACyK,EAAY,CACXY,OAAQA,EACRD,UAAWc,EAAId,UACfrM,MAAOmN,EAAInN,MACXoM,QAASH,KAAKoB,UAAUf,GACxB3C,KAAMsC,KAAKtC,KACXqC,OAAQC,KAAKD,OACbD,UAAWO,EAAOJ,IAClBzK,SAAU0I,KAIhBgD,IAEN,CACEnN,OACEiB,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,YAInBsK,UAAW,YACXF,OAAQA,CAAChC,EAAWmC,KAClB,MAAM,WAAEgB,EAAU,iBAAEC,GAAqBtB,KAAKuB,MACxCpB,EAAUH,KAAKoB,UAAUf,GAC/B,OAAIF,GAAWmB,GACNtM,EAAAA,EAAAA,GAACwM,EAAAA,EAAO,CAACC,KAAK,UAEhBtB,GACL3H,EAAAA,EAAAA,IAAA,QAAAhD,SAAA,EACER,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,4EACZoJ,KAAK,OACLF,QAASA,IAAMyC,KAAKtC,KAAK2C,EAAOJ,KAChCQ,MAAO,CAAE5G,YAAa,IACtB,cAAY,6BAA4BrE,UAExCR,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,YAInBd,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,4EACZoJ,KAAK,OAELF,QAASA,IAAMyC,KAAKD,OAAOM,EAAOJ,KAClC,cAAY,+BAA8BzK,UAE1CR,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,iBAMrB0C,EAAAA,EAAAA,IAAA,QAAAhD,SAAA,EACER,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,4EACZqN,MAAM1M,EAAAA,EAAAA,GAAC2M,EAAAA,IAAU,IACjBxK,SAAyB,KAAfkK,EACV9D,QAASA,IAAMyC,KAAK4B,KAAKvB,EAAOJ,KAChC,cAAY,gCAEdjL,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,4EACZqN,MAAM1M,EAAAA,EAAAA,GAAC6M,EAAAA,IAAS,IAChB1K,SAAyB,KAAfkK,EACV9D,QAASA,IAAMyC,KAAK8B,SAAS,CAAEC,YAAa1B,EAAOJ,MACnD,cAAY,mCAGjB,IAKP,KACAmB,UAAaf,GAAgBA,EAAOJ,MAAQD,KAAKuB,MAAMF,WAAW,KAElEtB,OAAS,KACPC,KAAK8B,SAAS,CAAET,WAAY,IAAK,EACjC,KAEF3D,KAAQuC,IACND,KAAKjF,KAAKiH,QAAQC,iBAAiBrE,MAAMvD,IACvC,MAAMgG,EAASL,KAAKlM,MAAMoO,KAAK7F,MAAM8F,GAAMA,EAAElC,MAAQA,IACjDI,IACFL,KAAK8B,SAAS,CAAER,kBAAkB,IAClCtB,KAAKlM,MAAMsO,WAAW,IAAK/B,KAAWhG,IAAUuD,MAAK,KACnDoC,KAAK8B,SAAS,CAAET,WAAY,GAAIC,kBAAkB,GAAQ,IAE9D,GACA,EACF,KAEFe,OAASC,UACP,IACE,MAAMjC,EAASL,KAAKlM,MAAMoO,KAAK7F,MAAM8F,GAAMA,EAAElC,MAAQA,IACjDI,IACFL,KAAK8B,SAAS,CAAER,kBAAkB,UAC5BtB,KAAKlM,MAAMyO,SAAS,IAAKlC,IAEnC,CAAC,QACCL,KAAK8B,SAAS,CAAEC,YAAa,GAAIT,kBAAkB,GACrD,GACA,KAEFM,KAAQ3B,IACND,KAAK8B,SAAS,CAAET,WAAYpB,GAAM,EAxHlCD,KAAKuB,MAAQ,CAAEF,WAAY,GAAIC,kBAAkB,EAAOS,YAAa,IACrE/B,KAAKgB,QAAUhB,KAAKiB,cACpBjB,KAAKjF,KAAOyE,EAAAA,WACd,CAwHAU,MAAAA,GACE,MAAM,KAAEgC,GAASlC,KAAKlM,MACtB,OACE0E,EAAAA,EAAAA,IAAC+G,EAAgBiD,SAAQ,CAAC3J,MAAO,CAAE0H,QAASP,KAAKjF,MAAOvF,SAAA,EACtDR,EAAAA,EAAAA,GAACyN,EAAAA,IAAW,CACVrO,UAAU,iBACV,cAAY,iBACZsO,WAAYR,EACZlB,QAAShB,KAAKgB,QACdS,KAAK,SACLkB,YAAY,QACZC,YAAY,EACZC,OAAQ,CACNC,WACE9N,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,oBAKrBiN,OAAQ,CAAEC,EAAG,QAEfhO,EAAAA,EAAAA,GAACmI,EAAAA,EAAK,CACJ9I,YAAY,4EACZ,cAAY,mCACZN,OACEiB,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,mDAMnBsH,QAAS4C,KAAKuB,MAAMQ,YACpBkB,QACEjO,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,YAKnBoN,YACElO,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,WAMnBuI,eAAgB2B,KAAKuB,MAAMD,iBAE3B6B,KAAMA,IAAMnD,KAAKqC,OAAOrC,KAAKuB,MAAMQ,aACnC3D,SAAUA,IAAM4B,KAAK8B,SAAS,CAAEC,YAAa,SAIrD,EAGK,MAAMqB,EAAoBtC,E,kCCvPjC,MAAMuC,UAAkCC,EAAAA,UAAiB5D,WAAAA,GAAA,SAAAC,WAAA,KACvD4D,aAAe,CACb,CACExP,MAAOiM,KAAKlM,MAAMsD,KAAK1C,cAAc,CAAAmB,GAAA,SACnCC,eAAe,SAGjBsK,UAAW,OACXjL,MAAO,KAET,CACEpB,MAAOiM,KAAKlM,MAAMsD,KAAK1C,cAAc,CAAAmB,GAAA,SACnCC,eAAe,UAGjBsK,UAAW,QACXjL,MAAO,IACPgM,UAAU,IAEZ,KAEFqC,QAAU,IACRC,IAAAA,OACEC,EAAAA,EAAMC,oBAAoB3D,KAAKlM,MAAM8P,MAAM7L,KAAKsC,IAAM,CACpD4F,IAAK5F,EAAO,GACZ9D,KAAM8D,EAAO,GACbxB,MAAOwB,EAAO,OAEhB,QACA,KAEJwJ,iBAAmB,IAAM,IAAItL,IAAImL,EAAAA,EAAMC,oBAAoB3D,KAAKlM,MAAM8P,MAAM7L,KAAKsC,GAAWA,EAAO,MAAK,KAExGyJ,iBAAmB,CAACC,EAAWlL,EAAYmL,KAEzCA,EADoBhE,KAAK6D,mBAEXI,IAAIpL,GACZmH,KAAKlM,MAAMsD,KAAK1C,cACd,CAAAmB,GAAA,SACEC,eAAe,iCAGjB,CACE+C,MAAOA,SAGXtD,EACL,CACD,CAEF2K,MAAAA,GACE,MAAM,iBAAEoB,EAAgB,eAAE4C,EAAc,gBAAEC,EAAe,aAAEC,EAAY,SAAEC,GAAarE,KAAKlM,MAE3F,OACE0E,EAAAA,EAAAA,IAAA8L,EAAAA,GAAA,CAAA9O,SAAA,EACER,EAAAA,EAAAA,GAACoO,EAAiB,CAChBpC,QAAShB,KAAKuD,aACdrB,KAAMlC,KAAKwD,UACXpB,WAAY8B,EACZ3B,SAAU4B,KAEZnP,EAAAA,EAAAA,GAACuP,EAAAA,EAAM,CAAC9C,KAAK,QACbzM,EAAAA,EAAAA,GAAA,OAAAQ,UAEEgD,EAAAA,EAAAA,IAACwC,EAAAA,IAAU,CAACwF,IAAK6D,EAAUvF,OAAO,SAAS0F,SAAUJ,EAAcnP,IAAKuB,EAAOuE,KAAKvF,SAAA,EAClFR,EAAAA,EAAAA,GAACgG,EAAAA,IAAW+D,KAAI,CACdxI,KAAK,OACLkO,MAAO,CACL,CACEC,UAAU,EACVtF,QAASY,KAAKlM,MAAMsD,KAAK1C,cAAc,CAAAmB,GAAA,SACrCC,eAAe,uBAInB,CACE6O,UAAW3E,KAAK8D,mBAElBtO,UAEFR,EAAAA,EAAAA,GAAC4L,EAAAA,EAAK,CACJvM,YAAY,yEACZ,aAAW,WACX,cAAY,uBACZ0E,YAAaiH,KAAKlM,MAAMsD,KAAK1C,cAAc,CAAAmB,GAAA,SACzCC,eAAe,cAKrBd,EAAAA,EAAAA,GAACgG,EAAAA,IAAW+D,KAAI,CAACxI,KAAK,QAAQkO,MAAO,GAAGjP,UACtCR,EAAAA,EAAAA,GAAC4L,EAAAA,EAAK,CACJvM,YAAY,yEACZ,aAAW,YACX,cAAY,wBACZ0E,YAAaiH,KAAKlM,MAAMsD,KAAK1C,cAAc,CAAAmB,GAAA,SACzCC,eAAe,eAKrBd,EAAAA,EAAAA,GAACgG,EAAAA,IAAW+D,KAAI,CAAAvJ,UACdR,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,yEACZmJ,QAAS8D,EACTsD,SAAS,SACT,cAAY,iBAAgBpP,UAE5BR,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,mBAS/B,EAGF,MAAMU,EAAS,CACbuE,KAAOtI,IAAU,CACf,UAAW,CAAEoH,YAAapH,EAAMiB,QAAQmR,OAK/BC,GAAwBC,EAAAA,EAAAA,IAAW1B,E,6FCxHzC,MAAM2B,EAAexS,IAAwE,IAAvE,SAAEgD,EAAQ,QAAEwL,GAAqDxO,EAC5F,MAAMyS,EAAiBjE,EAAUxK,EAAO0O,iBAAiBlE,GAAWxK,EAAO2O,YAE3E,OAAOnQ,EAAAA,EAAAA,GAAA,OAAKC,IAAKgQ,EAAezP,SAAEA,GAAe,EACjD,IAAA4D,EAAA,CAAA7C,KAAA,UAAAC,OAAA,sBAEFwO,EAAajG,KAAOjK,IAAkG,IAAjG,MAAEsQ,EAAK,UAAEC,EAAY,KAAI,SAAE7P,EAAQ,KAAE8P,GAAsDxQ,EAC9G,OACE0D,EAAAA,EAAAA,IAAA,OAAK,eAAa,oBAAoBvD,IAAKuB,EAAO+O,gBAAgBD,GAAQ,GAAG9P,SAAA,EAC3ER,EAAAA,EAAAA,GAAA,OAAK,eAAa,0BAA0BC,IAAGmE,EAA2B5D,UACxER,EAAAA,EAAAA,GAACsJ,EAAAA,EAAWkH,KAAI,CAAC/D,KAAM4D,EAAWI,MAAM,YAAWjQ,SAChD4P,OAGLpQ,EAAAA,EAAAA,GAAA,OAAK,eAAa,0BAA0BC,IAAKuB,EAAOkP,MAAMlQ,UAC5DR,EAAAA,EAAAA,GAACsJ,EAAAA,EAAWkH,KAAI,CAAC/D,KAAM4D,EAAWI,MAAM,YAAWjQ,SAAC,SAItDR,EAAAA,EAAAA,GAAA,OAAK,eAAa,4BAA2BQ,SAAEA,MAC3C,EAIV,MAAMgB,EAAS,CACb0O,iBAAmBS,GAAyBlT,IAAY,CACtDU,QAAS,OACTyS,oBAAqB,UAAUD,yBAC/BE,UAAWpT,EAAMiB,QAAQmR,GACzBiB,OAAQrT,EAAMiB,QAAQE,GACtBmS,aAActT,EAAMiB,QAAQsS,KAE9Bb,YAAc1S,IAAY,CACxBU,QAAS,OACTyS,oBAAqB,wCACrBK,QAASxT,EAAMiB,QAAQE,KAEzB2R,gBAAkBD,IAAY,CAC5BnS,QAAS,OACT+S,WAAY,QAAQZ,MAEtBI,MAAO,CACLhF,OAAQ,a,mHCzEZ,MAaayF,EAAS3T,IAAqC,IAApC,KAAE4T,EAAI,QAAEhH,GAAsB5M,EACnD,MAAM6T,EAbE7G,EAAAA,WAAiB8G,EAAAA,IAAkCC,UAAUF,MA2BrE,OAZA7G,EAAAA,WAAgB,KACd,IAAK4G,EAAM,OAQX,OANqB,OAALC,QAAK,IAALA,OAAK,EAALA,GAAQ,IAEfG,OAAOC,QAAQrH,IAIV,GACb,CAACA,EAASiH,EAAOD,IAEb,IAAI,E,2DCAb,MAAMM,EAAmBnQ,IAAiBvB,EAAAA,EAAAA,GAAC2R,EAAW,CAACpQ,KAAMA,IAEtD,MAAMqQ,UAAyBtD,EAAAA,UAAwD5D,WAAAA,GAAA,SAAAC,WAAA,KAmB5F4B,MAAQ,CACNsF,SAAU7G,KAAKlM,MAAMgT,gBACrBC,YAAa/G,KAAKlM,MAAMkT,mBACxBC,MAAO,MACP,KAEFC,WAAYC,EAAAA,EAAAA,MAAuB,KAEnCC,qBAAwBP,IACtB7G,KAAK8B,SAAS,CAAE+E,YAAW,EAC3B,KAEFQ,gBAAmBN,IACjB/G,KAAK8B,SAAS,CAAEiF,eAAc,EAC9B,KAEFO,kBAAoB,KAClB,MAAM,SAAEC,GAAavH,KAAKlM,OACpB,SAAE+S,GAAa7G,KAAKuB,MAE1B,OADAvB,KAAK8B,SAAS,CAAEzD,gBAAgB,IAC5BkJ,EACKC,QAAQC,QAAQF,EAASV,IAC7BjJ,MAAK,KACJoC,KAAK8B,SAAS,CAAEzD,gBAAgB,EAAO4I,MAAO,MAAO,IAEtDpJ,OAAOC,IACNkC,KAAK8B,SAAS,CACZzD,gBAAgB,EAChB4I,MACEnJ,GAAKA,EAAEE,gBACHF,EAAEE,kBACFgC,KAAKlM,MAAMsD,KAAK1C,cAAc,CAAAmB,GAAA,SAC5BC,eAAe,sBAGvB,IAGD,IAAI,EACX,KAEF4R,kBAAoB,KAElB1H,KAAK8B,SAAS,CACZ+E,SAAU7G,KAAKlM,MAAMgT,gBACrBC,YAAa/G,KAAKlM,MAAMkT,qBAE1B,MAAM,SAAE5I,GAAa4B,KAAKlM,MACtBsK,GACFA,GACF,CACA,CAEFuJ,iBAAAA,GACE,OAAO3H,KAAKuB,MAAMsF,WAAa7G,KAAKlM,MAAMgT,eAC5C,CAEAc,aAAAA,GAEE,MAAM,eAAEvJ,GAAmB2B,KAAKuB,MAChC,OACEvM,EAAAA,EAAAA,GAAA,OAAKZ,UAAU,wBAAwB,cAAY,wBAAuBoB,UACxEgD,EAAAA,EAAAA,IAAA,OAAAhD,SAAA,EACER,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,gEACZoJ,KAAK,UACLrJ,UAAU,4BACVmJ,QAASyC,KAAKsH,kBACdnQ,UAAW6I,KAAK2H,qBAAuBtJ,EACvCb,QAASa,EACT,cAAY,4BAA2B7I,SAEtCwK,KAAKlM,MAAM+T,YAEd7S,EAAAA,EAAAA,GAACsI,EAAAA,EAAM,CACLjJ,YAAY,gEACZuQ,SAAS,SACTxQ,UAAU,8BACVmJ,QAASyC,KAAK0H,kBACdvQ,SAAUkH,EAAe7I,UAEzBR,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SACfC,eAAe,iBAO3B,CAEAgS,uBAAAA,GACE,MAAM,SAAEjB,GAAa7G,KAAKuB,MAC1B,GAAIsF,EAAU,CACZ,MAAMkB,GAAYC,EAAAA,EAAAA,IAAsBhI,KAAKkH,UAAUe,SAASpB,IAChE,OAAOqB,EAAAA,EAAAA,IAAqBH,EAC9B,CACA,OAAO,IACT,CAEA7H,MAAAA,GACE,MAAM,WAAEiI,GAAenI,KAAKlM,OACtB,SAAE+S,EAAQ,YAAEE,EAAW,MAAEE,GAAUjH,KAAKuB,MACxC6G,EAAcpI,KAAK8H,0BACzB,OACE9S,EAAAA,EAAAA,GAAA,OAAKZ,UAAU,4BAA4B,cAAY,4BAA2BoB,SAC/E2S,GACC3P,EAAAA,EAAAA,IAACgH,EAAAA,SAAc,CAAAhK,SAAA,EACbR,EAAAA,EAAAA,GAAA,OAAKZ,UAAU,sBAAqBoB,UAClCR,EAAAA,EAAAA,GAACqT,EAAAA,QAAQ,CACPxP,MAAOgO,EACPyB,gBAAiBtI,KAAKlM,MAAMwU,gBAC5BC,gBAAiBvI,KAAKlM,MAAMyU,gBAC5BC,iBAAkB,GAClBC,WAAYzI,KAAKlM,MAAM2U,WACvBC,gBAAiB1I,KAAKlM,MAAM4U,gBAC5BvU,SAAU6L,KAAKoH,qBAEfL,YAAaA,EACb4B,YAAa3I,KAAKqH,gBAElBuB,wBAA0BhV,GAAO4T,QAAQC,QAAQzH,KAAK8H,wBAAwBlU,IAC9EiV,QAASnC,MAGZO,IACCjS,EAAAA,EAAAA,GAACkK,EAAAA,IAAK,CACJ7K,YAAY,gEACZoJ,KAAK,QACL2B,QAASY,KAAKlM,MAAMsD,KAAK1C,cAAc,CAAAmB,GAAA,SACrCC,eAAe,6CAGjBgT,YAAa7B,EACb1N,UAAQ,IAGXyG,KAAK4H,iBACN5S,EAAAA,EAAAA,GAACmR,EAAM,CACLC,KAAMpG,KAAK2H,oBACXvI,QAASY,KAAKlM,MAAMsD,KAAK1C,cAAc,CAAAmB,GAAA,SACrCC,eAAe,0FAMrBd,EAAAA,EAAAA,GAAC+T,EAAe,CAACC,QAASZ,KAIlC,EAOF,SAASzB,EAAY7S,GACnB,MAAM,MAAErB,IAAUgC,EAAAA,EAAAA,MACZ,KAAE8B,GAASzC,EACjB,OAEEkB,EAAAA,EAAAA,GAACiU,EAAAA,IAAa,CAACC,SAAS,MAAMnV,MAAOwC,EAAKf,UACxCR,EAAAA,EAAAA,GAAA,QAAMC,KAAGS,EAAAA,EAAAA,IAAE,CAAE+P,MAAOhT,EAAM0W,OAAOC,aAAa,IAAC5T,UAE7CR,EAAAA,EAAAA,GAACqU,EAAAA,QAAO,CAAC3H,KAAMnL,OAIvB,CAMA,SAASwS,EAAgBjV,GACvB,MAAM,QAAEkV,GAAYlV,EACpB,OAAOkV,GACLhU,EAAAA,EAAAA,GAAA,OAAKZ,UAAU,4BAA4B,cAAY,4BAA2BoB,UAChFR,EAAAA,EAAAA,GAAA,OAAKZ,UAAU,sBAAqBoB,UAClCR,EAAAA,EAAAA,GAAA,OAAKZ,UAAU,wCAAuCoB,UACpDR,EAAAA,EAAAA,GAAA,OACEZ,UAAU,8BACV,cAAY,8BAGZkV,wBAAyB,CAAEC,OAAQzV,EAAMkV,kBAMjDhU,EAAAA,EAAAA,GAAA,OAAAQ,UACER,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SAACC,eAAe,UAGvC,CAxNa8Q,EACJ4C,aAAe,CACpB1C,gBAAiB,GACjBE,mBAAoB,QACpBmB,YAAY,EACZN,UACE7S,EAAAA,EAAAA,GAAC8E,EAAAA,EAAgB,CAAAjE,GAAA,SAACC,eAAe,SAEnCuI,gBAAgB,EAChBqK,gBAAiB,CACf,CAAC,SAAU,OAAQ,SAAU,iBAC7B,CAAC,OAAQ,QAAS,OAAQ,SAC1B,CAAC,iBAAkB,eAAgB,iBAErCH,gBAAiB,IACjBD,gBAAiB,IACjBG,WAAY,CAAC,GA0MV,MAAMgB,GAAe1E,EAAAA,EAAAA,IAAW6B,E", "sources": ["common/components/CollapsibleSection.tsx", "model-registry/components/aliases/ModelVersionAliasSelect.tsx", "model-registry/hooks/useEditRegisteredModelAliasesModal.tsx", "common/components/tables/EditableFormTable.tsx", "common/components/EditableTagsTableView.tsx", "common/components/Descriptions.tsx", "common/components/Prompt.tsx", "common/components/EditableNote.tsx"], "sourcesContent": ["import React, { useCallback } from 'react';\nimport { SectionErrorBoundary } from './error-boundaries/SectionErrorBoundary';\nimport {\n  ChevronRightIcon,\n  useDesignSystemTheme,\n  Accordion,\n  DesignSystemThemeInterface,\n  importantify,\n} from '@databricks/design-system';\nimport { useIntl } from 'react-intl';\n\ninterface CollapsibleSectionProps {\n  title: string | any;\n  forceOpen?: boolean;\n  children: React.ReactNode;\n  showServerError?: boolean;\n  defaultCollapsed?: boolean;\n  onChange?: (key: string | string[]) => void;\n  className?: string;\n  componentId?: string;\n}\n\n// Custom styles to make <Accordion> look like previously used <Collapse> from antd\nconst getAccordionStyles = ({\n  theme,\n  getPrefixedClassName,\n}: Pick<DesignSystemThemeInterface, 'theme' | 'getPrefixedClassName'>) => {\n  const clsPrefix = getPrefixedClassName('collapse');\n\n  const classItem = `.${clsPrefix}-item`;\n  const classHeader = `.${clsPrefix}-header`;\n  const classContentBox = `.${clsPrefix}-content-box`;\n\n  return {\n    fontSize: 14,\n    [`& > ${classItem} > ${classHeader}`]: {\n      paddingLeft: 0,\n      paddingTop: 12,\n      paddingBottom: 12,\n      display: 'flex',\n      alignItems: 'center',\n      fontSize: 16,\n      fontWeight: 'normal',\n      lineHeight: theme.typography.lineHeightLg,\n    },\n    [classContentBox]: {\n      padding: `${theme.spacing.xs}px 0 ${theme.spacing.md}px 0`,\n    },\n  };\n};\n\nexport function CollapsibleSection(props: CollapsibleSectionProps) {\n  const {\n    title,\n    forceOpen,\n    showServerError,\n    defaultCollapsed,\n    onChange,\n    className,\n    componentId = 'mlflow.common.generic_collapsible_section',\n  } = props;\n\n  // We need to spread `activeKey` into <Collapse/> as an optional prop because its enumerability\n  // affects rendering, i.e. passing `activeKey={undefined}` is different from not passing activeKey\n  const activeKeyProp = forceOpen && { activeKey: ['1'] };\n  const defaultActiveKey = defaultCollapsed ? null : ['1'];\n\n  const { theme, getPrefixedClassName } = useDesignSystemTheme();\n  const { formatMessage } = useIntl();\n\n  const getExpandIcon = useCallback(\n    ({ isActive }: { isActive?: boolean }) => (\n      <div\n        css={importantify({ width: theme.general.heightBase / 2, transform: isActive ? 'rotate(90deg)' : undefined })}\n      >\n        <ChevronRightIcon\n          css={{\n            svg: { width: theme.general.heightBase / 2, height: theme.general.heightBase / 2 },\n          }}\n          aria-label={\n            isActive\n              ? formatMessage(\n                  {\n                    defaultMessage: 'collapse {title}',\n                    description: 'Common component > collapsible section > alternative label when expand',\n                  },\n                  { title },\n                )\n              : formatMessage(\n                  {\n                    defaultMessage: 'expand {title}',\n                    description: 'Common component > collapsible section > alternative label when collapsed',\n                  },\n                  { title },\n                )\n          }\n        />\n      </div>\n    ),\n    [theme, title, formatMessage],\n  );\n\n  return (\n    <Accordion\n      componentId={componentId}\n      {...activeKeyProp}\n      dangerouslyAppendEmotionCSS={getAccordionStyles({ theme, getPrefixedClassName })}\n      dangerouslySetAntdProps={{\n        className,\n        expandIconPosition: 'left',\n        expandIcon: getExpandIcon,\n      }}\n      defaultActiveKey={defaultActiveKey ?? undefined}\n      onChange={onChange}\n    >\n      <Accordion.Panel header={title} key=\"1\">\n        <SectionErrorBoundary showServerError={showServerError}>{props.children}</SectionErrorBoundary>\n      </Accordion.Panel>\n    </Accordion>\n  );\n}\n", "import { Dispatch, useCallback, useState } from 'react';\n\nimport { LegacySelect, useDesignSystemTheme } from '@databricks/design-system';\n\nimport { ModelVersionAliasTag } from './ModelVersionAliasTag';\nimport { FormattedMessage, useIntl } from 'react-intl';\n\n/**\n * A specialized <LegacySelect> component used for adding and removing aliases from model versions\n */\nexport const ModelVersionAliasSelect = ({\n  renderKey,\n  setDraftAliases,\n  existingAliases,\n  draftAliases,\n  version,\n  aliasToVersionMap,\n  disabled,\n}: {\n  renderKey: any;\n  disabled: boolean;\n  setDraftAliases: Dispatch<React.SetStateAction<string[]>>;\n  existingAliases: string[];\n  draftAliases: string[];\n  version: string;\n  aliasToVersionMap: Record<string, string>;\n}) => {\n  const intl = useIntl();\n  const [dropdownVisible, setDropdownVisible] = useState(false);\n\n  const { theme } = useDesignSystemTheme();\n\n  const removeFromEditedAliases = useCallback(\n    (alias: string) => {\n      setDraftAliases((aliases) => aliases.filter((existingAlias) => existingAlias !== alias));\n    },\n    [setDraftAliases],\n  );\n\n  const updateEditedAliases = useCallback(\n    (aliases: string[]) => {\n      const sanitizedAliases = aliases\n        // Remove all characters that are not alphanumeric, underscores or hyphens\n        .map((alias) =>\n          alias\n            .replace(/[^\\w-]/g, '')\n            .toLowerCase()\n            .substring(0, 255),\n        )\n        // After sanitization, filter out invalid aliases\n        // so we won't get empty values\n        .filter((alias) => alias.length > 0);\n\n      // Remove duplicates that might result from varying letter case\n      const uniqueAliases = Array.from(new Set(sanitizedAliases));\n      setDraftAliases(uniqueAliases);\n      setDropdownVisible(false);\n    },\n    [setDraftAliases],\n  );\n\n  return (\n    // For the time being, we will use <LegacySelect /> under the hood,\n    // while <TypeaheadCombobox /> is still in the design phase.\n    <LegacySelect\n      disabled={disabled}\n      filterOption={(val, opt) => opt?.value.toLowerCase().startsWith(val.toLowerCase())}\n      placeholder={intl.formatMessage({\n        defaultMessage: 'Enter aliases (champion, challenger, etc)',\n        description: 'Model registry > model version alias select > Alias input placeholder',\n      })}\n      allowClear\n      css={{ width: '100%' }}\n      mode=\"tags\"\n      // There's a bug with current <LegacySelect /> implementation that causes the dropdown\n      // to detach from input vertically when its position on screen changes (in this case, it's\n      // caused by the conflict alerts). A small key={} hack ensures that the component is recreated\n      // and the dropdown is repositioned each time the alerts below are changed.\n      key={JSON.stringify(renderKey)}\n      onChange={updateEditedAliases}\n      dangerouslySetAntdProps={{\n        dropdownMatchSelectWidth: true,\n        tagRender: ({ value }) => (\n          <ModelVersionAliasTag\n            compact\n            css={{ marginTop: 2 }}\n            closable\n            onClose={() => removeFromEditedAliases(value.toString())}\n            value={value.toString()}\n          />\n        ),\n      }}\n      onDropdownVisibleChange={setDropdownVisible}\n      open={dropdownVisible}\n      value={draftAliases || []}\n    >\n      {existingAliases.map((alias) => (\n        <LegacySelect.Option key={alias} value={alias} data-testid=\"model-alias-option\">\n          <div key={alias} css={{ display: 'flex', marginRight: theme.spacing.xs }}>\n            <div css={{ flex: 1 }}>{alias}</div>\n            <div>\n              <FormattedMessage\n                defaultMessage=\"This version\"\n                description=\"Model registry > model version alias select > Indicator for alias of selected version\"\n              />\n            </div>\n          </div>\n        </LegacySelect.Option>\n      ))}\n      {Object.entries(aliasToVersionMap)\n        .filter(([, otherVersion]) => otherVersion !== version)\n        .map(([alias, aliasedVersion]) => (\n          <LegacySelect.Option key={alias} value={alias} data-testid=\"model-alias-option\">\n            <div key={alias} css={{ display: 'flex', marginRight: theme.spacing.xs }}>\n              <div css={{ flex: 1 }}>{alias}</div>\n              <div>\n                <FormattedMessage\n                  defaultMessage=\"Version {version}\"\n                  description=\"Model registry > model version alias select > Indicator for alias of a particular version\"\n                  values={{ version: aliasedVersion }}\n                />\n              </div>\n            </div>\n          </LegacySelect.Option>\n        ))}\n    </LegacySelect>\n  );\n};\n", "import { isEqual } from 'lodash';\nimport { use<PERSON><PERSON>back, useMemo, useState } from 'react';\n\nimport { <PERSON><PERSON>, Button, LegacyForm, Modal, useDesignSystemTheme } from '@databricks/design-system';\nimport { Typography } from '@databricks/design-system';\nimport { ModelEntity } from '../../experiment-tracking/types';\nimport { ModelVersionAliasSelect } from '../components/aliases/ModelVersionAliasSelect';\nimport { FormattedMessage } from 'react-intl';\nimport { useDispatch } from 'react-redux';\nimport { ThunkDispatch } from '../../redux-types';\nimport { setModelVersionAliasesApi } from '../actions';\nimport { mlflowAliasesLearnMoreLink } from '../constants';\nimport { ErrorWrapper } from '../../common/utils/ErrorWrapper';\n\nconst MAX_ALIASES_PER_MODEL_VERSION = 10;\n\n/**\n * Provides methods to initialize and display modal used to add and remove aliases from the model version\n */\nexport const useEditRegisteredModelAliasesModal = ({\n  model,\n  onSuccess,\n  modalTitle,\n  modalDescription,\n}: {\n  model: null | ModelEntity;\n  onSuccess?: () => void;\n  modalTitle?: (version: string) => React.ReactNode;\n  modalDescription?: React.ReactNode;\n}) => {\n  const [showModal, setShowModal] = useState(false);\n  const [form] = LegacyForm.useForm();\n\n  const [errorMessage, setErrorMessage] = useState<string>('');\n  const { theme } = useDesignSystemTheme();\n\n  // We will keep version's existing aliases in `existingAliases` state array\n  const [existingAliases, setExistingAliases] = useState<string[]>([]);\n  // Currently edited aliases will be kept in `draftAliases` state array\n  const [draftAliases, setDraftAliases] = useState<string[]>([]);\n  // Currently edited version\n  const [currentlyEditedVersion, setCurrentlyEditedVersion] = useState<string>('0');\n\n  const dispatch = useDispatch<ThunkDispatch>();\n\n  /**\n   * Function used to invoke the modal and start editing aliases of the particular model version\n   */\n  const showEditAliasesModal = useCallback(\n    (versionNumber: string) => {\n      if (!model) {\n        return;\n      }\n\n      const modelVersionAliases =\n        model.aliases?.filter(({ version }) => version === versionNumber).map(({ alias }) => alias) || [];\n\n      if (versionNumber) {\n        setExistingAliases(modelVersionAliases);\n        setDraftAliases(modelVersionAliases);\n        setCurrentlyEditedVersion(versionNumber);\n        setShowModal(true);\n      }\n    },\n    [model],\n  );\n\n  // // Finds and stores alias values found in other model versions\n  const conflictedAliases = useMemo(() => {\n    if (!model?.aliases) {\n      return [];\n    }\n    const versionsWithAliases = model.aliases.reduce<{ version: string; aliases: string[] }[]>(\n      (aliasMap, aliasEntry) => {\n        if (!aliasMap.some(({ version }) => version === aliasEntry.version)) {\n          return [...aliasMap, { version: aliasEntry.version, aliases: [aliasEntry.alias] }];\n        }\n        aliasMap.find(({ version }) => version === aliasEntry.version)?.aliases.push(aliasEntry.alias);\n        return aliasMap;\n      },\n      [],\n    );\n    const otherVersionMappings = versionsWithAliases.filter(\n      ({ version: otherVersion }) => otherVersion !== currentlyEditedVersion,\n    );\n    return draftAliases\n      .map((alias) => ({\n        alias,\n        otherVersion: otherVersionMappings.find((version) =>\n          version.aliases?.find((alias_name) => alias_name === alias),\n        ),\n      }))\n      .filter(({ otherVersion }) => otherVersion);\n  }, [model?.aliases, draftAliases, currentlyEditedVersion]);\n\n  // Maps particular aliases to versions\n  const aliasToVersionMap = useMemo(\n    () =>\n      model?.aliases?.reduce<Record<string, string>>((result, { alias, version }) => {\n        return { ...result, [alias]: version };\n      }, {}) || {},\n    [model],\n  );\n\n  const save = () => {\n    if (!model) {\n      return;\n    }\n    setErrorMessage('');\n    dispatch(setModelVersionAliasesApi(model.name, currentlyEditedVersion, existingAliases, draftAliases))\n      .then(() => {\n        setShowModal(false);\n        onSuccess?.();\n      })\n      .catch((e: ErrorWrapper) => {\n        const extractedErrorMessage = e.getMessageField() || e.getUserVisibleError().toString() || e.text;\n        setErrorMessage(extractedErrorMessage);\n      });\n  };\n\n  // Indicates if there is any pending change to the alias set\n  const isPristine = isEqual(existingAliases.slice().sort(), draftAliases.slice().sort());\n  const isExceedingLimit = draftAliases.length > MAX_ALIASES_PER_MODEL_VERSION;\n\n  const isInvalid = isPristine || isExceedingLimit;\n\n  const EditAliasesModal = (\n    <Modal\n      componentId=\"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_127\"\n      visible={showModal}\n      footer={\n        <div>\n          <Button\n            componentId=\"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_131\"\n            onClick={() => setShowModal(false)}\n          >\n            <FormattedMessage\n              defaultMessage=\"Cancel\"\n              description=\"Model registry > model version alias editor > Cancel editing aliases\"\n            />\n          </Button>\n          <Button\n            componentId=\"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_137\"\n            loading={false}\n            type=\"primary\"\n            disabled={isInvalid}\n            onClick={save}\n          >\n            <FormattedMessage\n              defaultMessage=\"Save aliases\"\n              description=\"Model registry > model version alias editor > Confirm change of aliases\"\n            />\n          </Button>\n        </div>\n      }\n      destroyOnClose\n      title={\n        modalTitle ? (\n          modalTitle(currentlyEditedVersion)\n        ) : (\n          <FormattedMessage\n            defaultMessage=\"Add/Edit alias for model version {version}\"\n            description=\"Model registry > model version alias editor > Title of the update alias modal\"\n            values={{ version: currentlyEditedVersion }}\n          />\n        )\n      }\n      onCancel={() => setShowModal(false)}\n      confirmLoading={false}\n    >\n      <Typography.Paragraph>\n        {modalDescription ?? (\n          <FormattedMessage\n            defaultMessage=\"Aliases allow you to assign a mutable, named reference to a particular model version. <link>Learn more</link>\"\n            description=\"Explanation of registered model aliases\"\n            values={{\n              link: (chunks) => (\n                <a href={mlflowAliasesLearnMoreLink} rel=\"noreferrer\" target=\"_blank\">\n                  {chunks}\n                </a>\n              ),\n            }}\n          />\n        )}\n      </Typography.Paragraph>\n      <LegacyForm form={form} layout=\"vertical\">\n        <LegacyForm.Item>\n          <ModelVersionAliasSelect\n            disabled={false}\n            renderKey={conflictedAliases} // todo\n            aliasToVersionMap={aliasToVersionMap}\n            version={currentlyEditedVersion}\n            draftAliases={draftAliases}\n            existingAliases={existingAliases}\n            setDraftAliases={setDraftAliases}\n          />\n        </LegacyForm.Item>\n        <div css={{ display: 'flex', flexDirection: 'column', gap: theme.spacing.xs }}>\n          {isExceedingLimit && (\n            <Alert\n              componentId=\"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_192\"\n              role=\"alert\"\n              message={\n                <FormattedMessage\n                  defaultMessage=\"You are exceeding a limit of {limit} aliases assigned to the single model version\"\n                  description=\"Model registry > model version alias editor > Warning about exceeding aliases limit\"\n                  values={{ limit: MAX_ALIASES_PER_MODEL_VERSION }}\n                />\n              }\n              type=\"error\"\n              closable={false}\n            />\n          )}\n          {conflictedAliases.map(({ alias, otherVersion }) => (\n            <Alert\n              componentId=\"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_206\"\n              role=\"alert\"\n              key={alias}\n              message={\n                <FormattedMessage\n                  defaultMessage='The \"{alias}\" alias is also being used on version {otherVersion}. Adding it to this version will remove it from version {otherVersion}.'\n                  description=\"Model registry > model version alias editor > Warning about reusing alias from the other version\"\n                  values={{ otherVersion: otherVersion?.version, alias }}\n                />\n              }\n              type=\"info\"\n              closable={false}\n            />\n          ))}\n          {errorMessage && (\n            <Alert\n              componentId=\"codegen_mlflow_app_src_model-registry_hooks_useeditregisteredmodelaliasesmodal.tsx_220\"\n              role=\"alert\"\n              message={errorMessage}\n              type=\"error\"\n              closable={false}\n            />\n          )}\n        </div>\n      </LegacyForm>\n    </Modal>\n  );\n\n  return { EditAliasesModal, showEditAliasesModal };\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport {\n  Input,\n  Button,\n  LegacyForm,\n  Modal,\n  LegacyTable,\n  PencilIcon,\n  Spinner,\n  TrashIcon,\n} from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\n// @ts-expect-error TS(2554): Expected 1 arguments, but got 0.\nconst EditableContext = React.createContext();\n\ntype EditableCellProps = {\n  editing?: boolean;\n  dataIndex?: string;\n  title?: string;\n  record?: any;\n  index?: number;\n  save?: (...args: any[]) => any;\n  cancel?: (...args: any[]) => any;\n  recordKey?: string;\n};\n\nclass EditableCell extends React.Component<EditableCellProps> {\n  handleKeyPress = (event: any) => {\n    const { save, recordKey, cancel } = this.props;\n    if (event.key === 'Enter') {\n      // @ts-expect-error TS(2722): Cannot invoke an object which is possibly 'undefin... Remove this comment to see the full error message\n      save(recordKey);\n    } else if (event.key === 'Escape') {\n      // @ts-expect-error TS(2722): Cannot invoke an object which is possibly 'undefin... Remove this comment to see the full error message\n      cancel();\n    }\n  };\n\n  render() {\n    const { editing, dataIndex, record, children } = this.props;\n    return (\n      <EditableContext.Consumer>\n        {/* @ts-expect-error TS(2322): Type '({ formRef }: { formRef: any; }) => Element'... Remove this comment to see the full error message */}\n        {({ formRef }) => (\n          <div className={editing ? 'editing-cell' : ''}>\n            {editing ? (\n              // @ts-expect-error TS(2322): Type '{ children: Element; ref: any; }' is not ass... Remove this comment to see the full error message\n              <LegacyForm ref={formRef}>\n                {/* @ts-expect-error TS(2322): Type '{ children: Element; style: { margin: number... Remove this comment to see the full error message */}\n                <LegacyForm.Item style={{ margin: 0 }} name={dataIndex} initialValue={record[dataIndex]}>\n                  <Input\n                    componentId=\"codegen_mlflow_app_src_common_components_tables_editableformtable.tsx_50\"\n                    onKeyDown={this.handleKeyPress}\n                    data-testid=\"editable-table-edited-input\"\n                  />\n                </LegacyForm.Item>\n              </LegacyForm>\n            ) : (\n              children\n            )}\n          </div>\n        )}\n      </EditableContext.Consumer>\n    );\n  }\n}\n\ntype EditableTableProps = {\n  columns: any[];\n  data: any[];\n  onSaveEdit: (...args: any[]) => any;\n  onDelete: (...args: any[]) => any;\n  intl?: any;\n};\n\ntype EditableTableState = any;\n\nexport class EditableTable extends React.Component<EditableTableProps, EditableTableState> {\n  columns: any;\n  form: any;\n\n  constructor(props: EditableTableProps) {\n    super(props);\n    this.state = { editingKey: '', isRequestPending: false, deletingKey: '' };\n    this.columns = this.initColumns();\n    this.form = React.createRef();\n  }\n\n  initColumns = () => [\n    ...this.props.columns.map((col) =>\n      col.editable\n        ? {\n            ...col,\n            render: (text: any, record: any) => (\n              <EditableCell\n                record={record}\n                dataIndex={col.dataIndex}\n                title={col.title}\n                editing={this.isEditing(record)}\n                save={this.save}\n                cancel={this.cancel}\n                recordKey={record.key}\n                children={text}\n              />\n            ),\n          }\n        : col,\n    ),\n    {\n      title: (\n        <FormattedMessage\n          defaultMessage=\"Actions\"\n          description=\"Column title for actions column in editable form table in MLflow\"\n        />\n      ),\n      dataIndex: 'operation',\n      render: (text: any, record: any) => {\n        const { editingKey, isRequestPending } = this.state;\n        const editing = this.isEditing(record);\n        if (editing && isRequestPending) {\n          return <Spinner size=\"small\" />;\n        }\n        return editing ? (\n          <span>\n            <Button\n              componentId=\"codegen_mlflow_app_src_common_components_tables_editableformtable.tsx_120\"\n              type=\"link\"\n              onClick={() => this.save(record.key)}\n              style={{ marginRight: 10 }}\n              data-testid=\"editable-table-button-save\"\n            >\n              <FormattedMessage\n                defaultMessage=\"Save\"\n                description=\"Text for saving changes on rows in editable form table in MLflow\"\n              />\n            </Button>\n            <Button\n              componentId=\"codegen_mlflow_app_src_common_components_tables_editableformtable.tsx_131\"\n              type=\"link\"\n              // @ts-expect-error TS(2554): Expected 0 arguments, but got 1.\n              onClick={() => this.cancel(record.key)}\n              data-testid=\"editable-table-button-cancel\"\n            >\n              <FormattedMessage\n                defaultMessage=\"Cancel\"\n                description=\"Text for canceling changes on rows in editable form table in MLflow\"\n              />\n            </Button>\n          </span>\n        ) : (\n          <span>\n            <Button\n              componentId=\"codegen_mlflow_app_src_common_components_tables_editableformtable.tsx_145\"\n              icon={<PencilIcon />}\n              disabled={editingKey !== ''}\n              onClick={() => this.edit(record.key)}\n              data-testid=\"editable-table-button-edit\"\n            />\n            <Button\n              componentId=\"codegen_mlflow_app_src_common_components_tables_editableformtable.tsx_151\"\n              icon={<TrashIcon />}\n              disabled={editingKey !== ''}\n              onClick={() => this.setState({ deletingKey: record.key })}\n              data-testid=\"editable-table-button-delete\"\n            />\n          </span>\n        );\n      },\n    },\n  ];\n\n  // @ts-expect-error TS(4111): Property 'editingKey' comes from an index signatur... Remove this comment to see the full error message\n  isEditing = (record: any) => record.key === this.state.editingKey;\n\n  cancel = () => {\n    this.setState({ editingKey: '' });\n  };\n\n  save = (key: any) => {\n    this.form.current.validateFields().then((values: any) => {\n      const record = this.props.data.find((r) => r.key === key);\n      if (record) {\n        this.setState({ isRequestPending: true });\n        this.props.onSaveEdit({ ...record, ...values }).then(() => {\n          this.setState({ editingKey: '', isRequestPending: false });\n        });\n      }\n    });\n  };\n\n  delete = async (key: any) => {\n    try {\n      const record = this.props.data.find((r) => r.key === key);\n      if (record) {\n        this.setState({ isRequestPending: true });\n        await this.props.onDelete({ ...record });\n      }\n    } finally {\n      this.setState({ deletingKey: '', isRequestPending: false });\n    }\n  };\n\n  edit = (key: any) => {\n    this.setState({ editingKey: key });\n  };\n\n  render() {\n    const { data } = this.props;\n    return (\n      <EditableContext.Provider value={{ formRef: this.form }}>\n        <LegacyTable\n          className=\"editable-table\"\n          data-testid=\"editable-table\"\n          dataSource={data}\n          columns={this.columns}\n          size=\"middle\"\n          tableLayout=\"fixed\"\n          pagination={false}\n          locale={{\n            emptyText: (\n              <FormattedMessage\n                defaultMessage=\"No tags found.\"\n                description=\"Text for no tags found in editable form table in MLflow\"\n              />\n            ),\n          }}\n          scroll={{ y: 280 }}\n        />\n        <Modal\n          componentId=\"codegen_mlflow_app_src_common_components_tables_editableformtable.tsx_228\"\n          data-testid=\"editable-form-table-remove-modal\"\n          title={\n            <FormattedMessage\n              defaultMessage=\"Are you sure you want to delete this tag？\"\n              description=\"Title text for confirmation pop-up to delete a tag from table\n                     in MLflow\"\n            />\n          }\n          // @ts-expect-error TS(4111): Property 'deletingKey' comes from an index signatu... Remove this comment to see the full error message\n          visible={this.state.deletingKey}\n          okText={\n            <FormattedMessage\n              defaultMessage=\"Confirm\"\n              description=\"OK button text for confirmation pop-up to delete a tag from table\n                     in MLflow\"\n            />\n          }\n          cancelText={\n            <FormattedMessage\n              defaultMessage=\"Cancel\"\n              description=\"Cancel button text for confirmation pop-up to delete a tag from\n                     table in MLflow\"\n            />\n          }\n          // @ts-expect-error TS(4111): Property 'isRequestPending' comes from an index si... Remove this comment to see the full error message\n          confirmLoading={this.state.isRequestPending}\n          // @ts-expect-error TS(4111): Property 'deletingKey' comes from an index signatu... Remove this comment to see the full error message\n          onOk={() => this.delete(this.state.deletingKey)}\n          onCancel={() => this.setState({ deletingKey: '' })}\n        />\n      </EditableContext.Provider>\n    );\n  }\n}\n\nexport const EditableFormTable = EditableTable;\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport Utils from '../utils/Utils';\nimport { LegacyForm, Input, Button, Spacer } from '@databricks/design-system';\nimport { EditableFormTable } from './tables/EditableFormTable';\nimport _ from 'lodash';\nimport { FormattedMessage, injectIntl } from 'react-intl';\n\ntype Props = {\n  tags: any;\n  handleAddTag: (...args: any[]) => any;\n  handleSaveEdit: (...args: any[]) => any;\n  handleDeleteTag: (...args: any[]) => any;\n  isRequestPending: boolean;\n  intl: {\n    formatMessage: (...args: any[]) => any;\n  };\n  innerRef?: any;\n};\n\nclass EditableTagsTableViewImpl extends Component<Props> {\n  tableColumns = [\n    {\n      title: this.props.intl.formatMessage({\n        defaultMessage: 'Name',\n        description: 'Column title for name column in editable tags table view in MLflow',\n      }),\n      dataIndex: 'name',\n      width: 200,\n    },\n    {\n      title: this.props.intl.formatMessage({\n        defaultMessage: 'Value',\n        description: 'Column title for value column in editable tags table view in MLflow',\n      }),\n      dataIndex: 'value',\n      width: 200,\n      editable: true,\n    },\n  ];\n\n  getData = () =>\n    _.sortBy(\n      Utils.getVisibleTagValues(this.props.tags).map((values) => ({\n        key: values[0],\n        name: values[0],\n        value: values[1],\n      })),\n      'name',\n    );\n\n  getTagNamesAsSet = () => new Set(Utils.getVisibleTagValues(this.props.tags).map((values) => values[0]));\n\n  tagNameValidator = (rule: any, value: any, callback: any) => {\n    const tagNamesSet = this.getTagNamesAsSet();\n    callback(\n      tagNamesSet.has(value)\n        ? this.props.intl.formatMessage(\n            {\n              defaultMessage: 'Tag \"{value}\" already exists.',\n              description: 'Validation message for tags that already exist in tags table in MLflow',\n            },\n            {\n              value: value,\n            },\n          )\n        : undefined,\n    );\n  };\n\n  render() {\n    const { isRequestPending, handleSaveEdit, handleDeleteTag, handleAddTag, innerRef } = this.props;\n\n    return (\n      <>\n        <EditableFormTable\n          columns={this.tableColumns}\n          data={this.getData()}\n          onSaveEdit={handleSaveEdit}\n          onDelete={handleDeleteTag}\n        />\n        <Spacer size=\"sm\" />\n        <div>\n          {/* @ts-expect-error TS(2322): Type '{ children: Element[]; ref: any; layout: \"in... Remove this comment to see the full error message */}\n          <LegacyForm ref={innerRef} layout=\"inline\" onFinish={handleAddTag} css={styles.form}>\n            <LegacyForm.Item\n              name=\"name\"\n              rules={[\n                {\n                  required: true,\n                  message: this.props.intl.formatMessage({\n                    defaultMessage: 'Name is required.',\n                    description: 'Error message for name requirement in editable tags table view in MLflow',\n                  }),\n                },\n                {\n                  validator: this.tagNameValidator,\n                },\n              ]}\n            >\n              <Input\n                componentId=\"codegen_mlflow_app_src_common_components_editabletagstableview.tsx_107\"\n                aria-label=\"tag name\"\n                data-testid=\"tags-form-input-name\"\n                placeholder={this.props.intl.formatMessage({\n                  defaultMessage: 'Name',\n                  description: 'Default text for name placeholder in editable tags table form in MLflow',\n                })}\n              />\n            </LegacyForm.Item>\n            <LegacyForm.Item name=\"value\" rules={[]}>\n              <Input\n                componentId=\"codegen_mlflow_app_src_common_components_editabletagstableview.tsx_117\"\n                aria-label=\"tag value\"\n                data-testid=\"tags-form-input-value\"\n                placeholder={this.props.intl.formatMessage({\n                  defaultMessage: 'Value',\n                  description: 'Default text for value placeholder in editable tags table form in MLflow',\n                })}\n              />\n            </LegacyForm.Item>\n            <LegacyForm.Item>\n              <Button\n                componentId=\"codegen_mlflow_app_src_common_components_editabletagstableview.tsx_127\"\n                loading={isRequestPending}\n                htmlType=\"submit\"\n                data-testid=\"add-tag-button\"\n              >\n                <FormattedMessage\n                  defaultMessage=\"Add\"\n                  description=\"Add button text in editable tags table view in MLflow\"\n                />\n              </Button>\n            </LegacyForm.Item>\n          </LegacyForm>\n        </div>\n      </>\n    );\n  }\n}\n\nconst styles = {\n  form: (theme: any) => ({\n    '& > div': { marginRight: theme.spacing.sm },\n  }),\n};\n\n// @ts-expect-error TS(2769): No overload matches this call.\nexport const EditableTagsTableView = injectIntl(EditableTagsTableViewImpl);\n", "import { Typography } from '@databricks/design-system';\nimport { Theme } from '@emotion/react';\nimport React from 'react';\n\nexport interface DescriptionsProps {\n  columns?: number;\n}\n\nexport interface DescriptionsItemProps {\n  label: string | React.ReactNode;\n  labelSize?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl';\n  span?: number;\n}\n\n/**\n * A component that displays the informative data in a key-value\n * fashion. Behaves similarly to antd's <Descriptions /> component.\n * If the number of columns is specified, then the key-values will\n * be displayed as such and will always be that number of columns\n * regardless of the width of the window.\n * If the number of columns is not specified, then the number of\n * columns will vary based on the size of the window.\n *\n * The following example will display four key-value descriptions\n * using two columns, which will result in data displayed in two rows:\n *\n * @example\n * <Descriptions columns={2}>\n *   <Descriptions.Item label=\"The label\">The value</Descriptions.Item>\n *   <Descriptions.Item label=\"Another label\">Another value</Descriptions.Item>\n *   <Descriptions.Item label=\"A label\">A value</Descriptions.Item>\n *   <Descriptions.Item label=\"Extra label\">Extra value</Descriptions.Item>\n * </Descriptions>\n */\nexport const Descriptions = ({ children, columns }: React.PropsWithChildren<DescriptionsProps>) => {\n  const instanceStyles = columns ? styles.descriptionsArea(columns) : styles.autoFitArea;\n\n  return <div css={instanceStyles}>{children}</div>;\n};\n\nDescriptions.Item = ({ label, labelSize = 'sm', children, span }: React.PropsWithChildren<DescriptionsItemProps>) => {\n  return (\n    <div data-test-id=\"descriptions-item\" css={styles.descriptionItem(span || 1)}>\n      <div data-test-id=\"descriptions-item-label\" css={{ whiteSpace: 'nowrap' }}>\n        <Typography.Text size={labelSize} color=\"secondary\">\n          {label}\n        </Typography.Text>\n      </div>\n      <div data-test-id=\"descriptions-item-colon\" css={styles.colon}>\n        <Typography.Text size={labelSize} color=\"secondary\">\n          :\n        </Typography.Text>\n      </div>\n      <div data-test-id=\"descriptions-item-content\">{children}</div>\n    </div>\n  );\n};\n\nconst styles = {\n  descriptionsArea: (columnCount: number) => (theme: Theme) => ({\n    display: 'grid',\n    gridTemplateColumns: `repeat(${columnCount}, minmax(100px, 1fr))`,\n    columnGap: theme.spacing.sm,\n    rowGap: theme.spacing.md,\n    marginBottom: theme.spacing.lg,\n  }),\n  autoFitArea: (theme: Theme) => ({\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',\n    gridGap: theme.spacing.md,\n  }),\n  descriptionItem: (span: number) => ({\n    display: 'flex',\n    gridColumn: `span ${span}`,\n  }),\n  colon: {\n    margin: '0 8px 0 0',\n  },\n};\n", "import React from 'react';\nimport { UNSAFE_NavigationContext } from '../utils/RoutingUtils';\n\nconst useNavigationBlock = () => {\n  return (React.useContext(UNSAFE_NavigationContext) as any).navigator.block;\n};\n\nexport interface PromptProps {\n  when: boolean;\n  message: string;\n}\n\n/**\n * Component confirms navigating away by displaying prompt if given condition is met.\n * Uses react-router v6 API.\n */\nexport const Prompt = ({ when, message }: PromptProps) => {\n  const block = useNavigationBlock();\n\n  React.useEffect(() => {\n    if (!when) return;\n\n    const unblock = block?.(() => {\n      // eslint-disable-next-line no-alert\n      return window.confirm(message);\n    });\n\n    // eslint-disable-next-line consistent-return\n    return unblock;\n  }, [message, block, when]);\n\n  return null;\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport { <PERSON><PERSON>, Button, LegacyTooltip, useDesignSystemTheme } from '@databricks/design-system';\nimport { Prompt } from './Prompt';\nimport ReactMde, { SvgIcon } from 'react-mde';\nimport { forceAnchorTagNewTab, getMarkdownConverter, sanitizeConvertedHtml } from '../utils/MarkdownUtils';\nimport './EditableNote.css';\nimport { FormattedMessage, IntlShape, injectIntl } from 'react-intl';\n\ntype EditableNoteImplProps = {\n  defaultMarkdown?: string;\n  defaultSelectedTab?: string;\n  onSubmit?: (...args: any[]) => any;\n  onCancel?: (...args: any[]) => any;\n  showEditor?: boolean;\n  saveText?: any;\n  toolbarCommands?: any[];\n  maxEditorHeight?: number;\n  minEditorHeight?: number;\n  childProps?: any;\n  intl: IntlShape;\n};\n\ntype EditableNoteImplState = any;\n\nconst getReactMdeIcon = (name: string) => <TooltipIcon name={name} />;\n\nexport class EditableNoteImpl extends Component<EditableNoteImplProps, EditableNoteImplState> {\n  static defaultProps = {\n    defaultMarkdown: '',\n    defaultSelectedTab: 'write',\n    showEditor: false,\n    saveText: (\n      <FormattedMessage defaultMessage=\"Save\" description=\"Default text for save button on editable notes in MLflow\" />\n    ),\n    confirmLoading: false,\n    toolbarCommands: [\n      ['header', 'bold', 'italic', 'strikethrough'],\n      ['link', 'quote', 'code', 'image'],\n      ['unordered-list', 'ordered-list', 'checked-list'],\n    ],\n    maxEditorHeight: 500,\n    minEditorHeight: 200,\n    childProps: {},\n  };\n\n  state = {\n    markdown: this.props.defaultMarkdown,\n    selectedTab: this.props.defaultSelectedTab,\n    error: null,\n  };\n\n  converter = getMarkdownConverter();\n\n  handleMdeValueChange = (markdown: any) => {\n    this.setState({ markdown });\n  };\n\n  handleTabChange = (selectedTab: any) => {\n    this.setState({ selectedTab });\n  };\n\n  handleSubmitClick = () => {\n    const { onSubmit } = this.props;\n    const { markdown } = this.state;\n    this.setState({ confirmLoading: true });\n    if (onSubmit) {\n      return Promise.resolve(onSubmit(markdown))\n        .then(() => {\n          this.setState({ confirmLoading: false, error: null });\n        })\n        .catch((e) => {\n          this.setState({\n            confirmLoading: false,\n            error:\n              e && e.getMessageField\n                ? e.getMessageField()\n                : this.props.intl.formatMessage({\n                    defaultMessage: 'Failed to submit',\n                    description: 'Message text for failing to save changes in editable note in MLflow',\n                  }),\n          });\n        });\n    }\n    return null;\n  };\n\n  handleCancelClick = () => {\n    // Reset to the last defaultMarkdown passed in as props.\n    this.setState({\n      markdown: this.props.defaultMarkdown,\n      selectedTab: this.props.defaultSelectedTab,\n    });\n    const { onCancel } = this.props;\n    if (onCancel) {\n      onCancel();\n    }\n  };\n\n  contentHasChanged() {\n    return this.state.markdown !== this.props.defaultMarkdown;\n  }\n\n  renderActions() {\n    // @ts-expect-error TS(2339): Property 'confirmLoading' does not exist on type '... Remove this comment to see the full error message\n    const { confirmLoading } = this.state;\n    return (\n      <div className=\"editable-note-actions\" data-testid=\"editable-note-actions\">\n        <div>\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_components_editablenote.tsx_114\"\n            type=\"primary\"\n            className=\"editable-note-save-button\"\n            onClick={this.handleSubmitClick}\n            disabled={!this.contentHasChanged() || confirmLoading}\n            loading={confirmLoading}\n            data-testid=\"editable-note-save-button\"\n          >\n            {this.props.saveText}\n          </Button>\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_components_editablenote.tsx_124\"\n            htmlType=\"button\"\n            className=\"editable-note-cancel-button\"\n            onClick={this.handleCancelClick}\n            disabled={confirmLoading}\n          >\n            <FormattedMessage\n              defaultMessage=\"Cancel\"\n              description=\"Text for the cancel button in an editable note in MLflow\"\n            />\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  getSanitizedHtmlContent() {\n    const { markdown } = this.state;\n    if (markdown) {\n      const sanitized = sanitizeConvertedHtml(this.converter.makeHtml(markdown));\n      return forceAnchorTagNewTab(sanitized);\n    }\n    return null;\n  }\n\n  render() {\n    const { showEditor } = this.props;\n    const { markdown, selectedTab, error } = this.state;\n    const htmlContent = this.getSanitizedHtmlContent();\n    return (\n      <div className=\"note-view-outer-container\" data-testid=\"note-view-outer-container\">\n        {showEditor ? (\n          <React.Fragment>\n            <div className=\"note-view-text-area\">\n              <ReactMde\n                value={markdown}\n                minEditorHeight={this.props.minEditorHeight}\n                maxEditorHeight={this.props.maxEditorHeight}\n                minPreviewHeight={50}\n                childProps={this.props.childProps}\n                toolbarCommands={this.props.toolbarCommands}\n                onChange={this.handleMdeValueChange}\n                // @ts-expect-error TS(2322): Type 'string' is not assignable to type '\"write\" |... Remove this comment to see the full error message\n                selectedTab={selectedTab}\n                onTabChange={this.handleTabChange}\n                // @ts-expect-error TS(2554): Expected 0 arguments, but got 1.\n                generateMarkdownPreview={(md) => Promise.resolve(this.getSanitizedHtmlContent(md))}\n                getIcon={getReactMdeIcon}\n              />\n            </div>\n            {error && (\n              <Alert\n                componentId=\"codegen_mlflow_app_src_common_components_editablenote.tsx_178\"\n                type=\"error\"\n                message={this.props.intl.formatMessage({\n                  defaultMessage: 'There was an error submitting your note.',\n                  description: 'Error message text when saving an editable note in MLflow',\n                })}\n                description={error}\n                closable\n              />\n            )}\n            {this.renderActions()}\n            <Prompt\n              when={this.contentHasChanged()}\n              message={this.props.intl.formatMessage({\n                defaultMessage: 'Are you sure you want to navigate away? Your pending text changes will be lost.',\n                description: 'Prompt text for navigating away before saving changes in editable note in MLflow',\n              })}\n            />\n          </React.Fragment>\n        ) : (\n          <HTMLNoteContent content={htmlContent} />\n        )}\n      </div>\n    );\n  }\n}\n\ntype TooltipIconProps = {\n  name?: string;\n};\n\nfunction TooltipIcon(props: TooltipIconProps) {\n  const { theme } = useDesignSystemTheme();\n  const { name } = props;\n  return (\n    // @ts-expect-error TS(2322): Type '{ children: Element; position: string; title... Remove this comment to see the full error message\n    <LegacyTooltip position=\"top\" title={name}>\n      <span css={{ color: theme.colors.textPrimary }}>\n        {/* @ts-expect-error TS(2322): Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message */}\n        <SvgIcon icon={name} />\n      </span>\n    </LegacyTooltip>\n  );\n}\n\ntype HTMLNoteContentProps = {\n  content?: string;\n};\n\nfunction HTMLNoteContent(props: HTMLNoteContentProps) {\n  const { content } = props;\n  return content ? (\n    <div className=\"note-view-outer-container\" data-testid=\"note-view-outer-container\">\n      <div className=\"note-view-text-area\">\n        <div className=\"note-view-preview note-editor-preview\">\n          <div\n            className=\"note-editor-preview-content\"\n            data-testid=\"note-editor-preview-content\"\n            // @ts-expect-error TS(2322): Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message\n            // eslint-disable-next-line react/no-danger\n            dangerouslySetInnerHTML={{ __html: props.content }}\n          ></div>\n        </div>\n      </div>\n    </div>\n  ) : (\n    <div>\n      <FormattedMessage defaultMessage=\"None\" description=\"Default text for no content in an editable note in MLflow\" />\n    </div>\n  );\n}\n\nexport const EditableNote = injectIntl(EditableNoteImpl);\n"], "names": ["getAccordionStyles", "_ref", "theme", "getPrefixedClassName", "clsPrefix", "classItem", "classHeader", "classContentBox", "fontSize", "paddingLeft", "paddingTop", "paddingBottom", "display", "alignItems", "fontWeight", "lineHeight", "typography", "lineHeightLg", "padding", "spacing", "xs", "md", "CollapsibleSection", "props", "title", "forceOpen", "showServerError", "defaultCollapsed", "onChange", "className", "componentId", "activeKeyProp", "active<PERSON><PERSON>", "defaultActiveKey", "useDesignSystemTheme", "formatMessage", "useIntl", "getExpandIcon", "useCallback", "_ref2", "isActive", "_jsx", "css", "importantify", "width", "general", "heightBase", "transform", "undefined", "children", "ChevronRightIcon", "_css", "svg", "height", "id", "defaultMessage", "Accordion", "dangerouslyAppendEmotionCSS", "dangerouslySetAntdProps", "expandIconPosition", "expandIcon", "Panel", "header", "SectionErrorBoundary", "name", "styles", "_ref4", "_ref5", "_ref8", "ModelVersionAliasSelect", "<PERSON><PERSON><PERSON>", "setDraftAliases", "existingAliases", "draftAliases", "version", "aliasToVersionMap", "disabled", "intl", "dropdownVisible", "setDropdownVisible", "useState", "removeFromEditedAliases", "alias", "aliases", "filter", "existingAlias", "updateEditedAliases", "sanitizedAliases", "map", "replace", "toLowerCase", "substring", "length", "uniqueAliases", "Array", "from", "Set", "_jsxs", "LegacySelect", "filterOption", "val", "opt", "value", "startsWith", "placeholder", "allowClear", "mode", "dropdownMatchSelectWidth", "tagRender", "_ref3", "ModelVersionAliasTag", "compact", "closable", "onClose", "toString", "onDropdownVisibleChange", "open", "Option", "marginRight", "FormattedMessage", "Object", "entries", "_ref6", "otherVersion", "_ref7", "alias<PERSON><PERSON><PERSON><PERSON>", "values", "JSON", "stringify", "useEditRegisteredModelAliasesModal", "model", "onSuccess", "modalTitle", "modalDescription", "showModal", "setShowModal", "form", "LegacyForm", "useForm", "errorMessage", "setErrorMessage", "setExistingAliases", "currentlyEditedVersion", "setCurrentlyEditedVersion", "dispatch", "useDispatch", "showEditAliasesModal", "versionNumber", "_model$aliases", "modelVersionAliases", "conflictedAliases", "useMemo", "versionsWithAliases", "reduce", "aliasMap", "alias<PERSON><PERSON><PERSON>", "_aliasMap$find", "some", "find", "push", "otherVersionMappings", "_version$aliases", "alias_name", "_model$aliases2", "result", "isPristine", "isEqual", "slice", "sort", "isExceedingLimit", "isInvalid", "EditAliasesModal", "Modal", "visible", "footer", "<PERSON><PERSON>", "onClick", "loading", "type", "save", "setModelVersionAliasesApi", "then", "catch", "e", "extractedErrorMessage", "getMessageField", "getUserVisibleError", "text", "destroyOnClose", "onCancel", "confirmLoading", "Typography", "Paragraph", "link", "chunks", "href", "mlflowAliasesLearnMoreLink", "rel", "target", "layout", "<PERSON><PERSON>", "flexDirection", "gap", "<PERSON><PERSON>", "role", "message", "limit", "_ref9", "EditableContext", "React", "EditableCell", "constructor", "arguments", "handleKeyPress", "event", "<PERSON><PERSON>ey", "cancel", "this", "key", "render", "editing", "dataIndex", "record", "Consumer", "formRef", "ref", "style", "margin", "initialValue", "Input", "onKeyDown", "EditableTable", "super", "columns", "initColumns", "col", "editable", "isEditing", "<PERSON><PERSON><PERSON>", "isRequestPending", "state", "Spinner", "size", "icon", "PencilIcon", "edit", "TrashIcon", "setState", "deletingKey", "current", "validateFields", "data", "r", "onSaveEdit", "delete", "async", "onDelete", "Provider", "LegacyTable", "dataSource", "tableLayout", "pagination", "locale", "emptyText", "scroll", "y", "okText", "cancelText", "onOk", "EditableFormTable", "EditableTagsTableViewImpl", "Component", "tableColumns", "getData", "_", "Utils", "getVisibleTagValues", "tags", "getTagNamesAsSet", "tagNameValidator", "rule", "callback", "has", "handleSaveEdit", "handleDeleteTag", "handleAddTag", "innerRef", "_Fragment", "Spacer", "onFinish", "rules", "required", "validator", "htmlType", "sm", "EditableTagsTableView", "injectIntl", "Descriptions", "instanceStyles", "descriptionsArea", "autoFitArea", "label", "labelSize", "span", "descriptionItem", "Text", "color", "colon", "columnCount", "gridTemplateColumns", "columnGap", "rowGap", "marginBottom", "lg", "gridGap", "gridColumn", "Prompt", "when", "block", "UNSAFE_NavigationContext", "navigator", "window", "confirm", "getReactMdeIcon", "TooltipIcon", "EditableNoteImpl", "markdown", "defaultMarkdown", "selectedTab", "defaultSelectedTab", "error", "converter", "getMarkdownConverter", "handleMdeValueChange", "handleTabChange", "handleSubmitClick", "onSubmit", "Promise", "resolve", "handleCancelClick", "contentHasChanged", "renderActions", "saveText", "getSanitizedHtmlContent", "sanitized", "sanitizeConvertedHtml", "makeHtml", "forceAnchorTagNewTab", "showEditor", "htmlContent", "ReactMde", "minEditorHeight", "maxEditorHeight", "minPreviewHeight", "childProps", "toolbarCommands", "onTabChange", "generateMarkdownPreview", "getIcon", "description", "HTMLNoteContent", "content", "LegacyTooltip", "position", "colors", "textPrimary", "SvgIcon", "dangerouslySetInnerHTML", "__html", "defaultProps", "EditableNote"], "sourceRoot": ""}