# Generated by Django 5.0.6 on 2025-04-01 05:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentication",
            "0020_alter_user_username_paymenttransaction_usercredit",
        ),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="paymenttransaction",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "Payment Transaction",
                "verbose_name_plural": "Payment Transactions",
            },
        ),
        migrations.AlterModelOptions(
            name="usercredit",
            options={
                "verbose_name": "User Credit",
                "verbose_name_plural": "User Credits",
            },
        ),
        migrations.RemoveField(
            model_name="usercredit",
            name="id",
        ),
        migrations.AddField(
            model_name="paymenttransaction",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="paymenttransaction",
            name="razorpay_payment_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="paymenttransaction",
            name="razorpay_signature",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="paymenttransaction",
            name="status",
            field=models.CharField(
                choices=[
                    ("created", "Created"),
                    ("pending", "Pending"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                    ("refunded", "Refunded"),
                ],
                default="created",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="paymenttransaction",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="transactions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="usercredit",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                primary_key=True,
                related_name="credit",
                serialize=False,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
