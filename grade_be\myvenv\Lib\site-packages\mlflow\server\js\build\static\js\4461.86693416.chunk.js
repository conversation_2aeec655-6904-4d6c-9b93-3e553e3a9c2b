"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[4461],{20109:function(e,r,t){t.d(r,{X:function(){return l}});t(31014);var n=t(28486),s=t(48012),o=t(32599),i=t(88443),a=t(50111);function u(){return(0,a.Y)(s.SvL,{"data-testid":"fallback",title:(0,a.Y)(i.A,{id:"qAdWdK",defaultMessage:"Error"}),description:(0,a.Y)(i.A,{id:"RzZVxC",defaultMessage:"An error occurred while rendering this component."}),image:(0,a.Y)(o.j,{})})}function c(e){let{children:r,customFallbackComponent:t}=e;function s(e,r){console.error("Caught Unexpected Error: ",e,r.componentStack)}return t?(0,a.Y)(n.tH,{onError:s,FallbackComponent:t,children:r}):(0,a.Y)(n.tH,{onError:s,fallback:(0,a.Y)(u,{}),children:r})}function l(e,r,t,n){return function(e){return(0,a.Y)(c,{customFallbackComponent:n,children:(0,a.Y)(r,{...e})})}}},25869:function(e,r,t){t.d(r,{h:function(){return o}});t(31014);var n=t(93215),s=t(50111);const o=e=>r=>{const t=(0,n.zy)(),o=(0,n.Zp)(),i=(0,n.g)();return(0,s.Y)(e,{params:i,location:t,navigate:o,...r})}},27705:function(e,r,t){t.d(r,{g:function(){return i}});var n=t(31014),s=t(76010),o=t(50111);class i extends n.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e,r){this.setState({error:e}),console.error(e,r)}renderErrorMessage(e){return this.props.showServerError?(0,o.FD)("div",{children:["Error message: ",e.message]}):""}render(){const{children:e}=this.props,{error:r}=this.state;return r?(0,o.Y)("div",{children:(0,o.FD)("p",{children:[(0,o.Y)("i",{"data-testid":"icon-fail",className:"fa fa-exclamation-triangle icon-fail",css:a.wrapper}),(0,o.Y)("span",{children:" Something went wrong with this section. "}),(0,o.Y)("span",{children:"If this error persists, please report an issue "}),(0,o.Y)("a",{href:s.A.getSupportPageUrl(),target:"_blank",children:"here"}),".",this.renderErrorMessage(r)]})}):e}}const a={wrapper:{marginLeft:-2}}},28486:function(e,r,t){t.d(r,{tH:function(){return a}});var n=t(31014);function s(e,r,t,n){Object.defineProperty(e,r,{get:t,set:n,enumerable:!0,configurable:!0})}s({},"ErrorBoundary",(()=>a));s({},"ErrorBoundaryContext",(()=>o));const o=(0,n.createContext)(null),i={didCatch:!1,error:null};class a extends n.Component{state=(()=>i)();static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary=(()=>{var e=this;return function(){const{error:r}=e.state;if(null!==r){for(var t=arguments.length,n=new Array(t),s=0;s<t;s++)n[s]=arguments[s];e.props.onReset?.({args:n,reason:"imperative-api"}),e.setState(i)}}})();componentDidCatch(e,r){this.props.onError?.(e,r)}componentDidUpdate(e,r){const{didCatch:t}=this.state,{resetKeys:n}=this.props;t&&null!==r.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==r.length||e.some(((e,t)=>!Object.is(e,r[t])))}(e.resetKeys,n)&&(this.props.onReset?.({next:n,prev:e.resetKeys,reason:"keys"}),this.setState(i))}render(){const{children:e,fallbackRender:r,FallbackComponent:t,fallback:s}=this.props,{didCatch:i,error:a}=this.state;let u=e;if(i){const e={error:a,resetErrorBoundary:this.resetErrorBoundary};if((0,n.isValidElement)(s))u=s;else if("function"===typeof r)u=r(e);else{if(!t)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");u=(0,n.createElement)(t,e)}}return(0,n.createElement)(o.Provider,{value:{didCatch:i,error:a,resetErrorBoundary:this.resetErrorBoundary}},u)}}function u(e){if(null==e||"boolean"!==typeof e.didCatch||"function"!==typeof e.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function c(){const e=(0,n.useContext)(o);u(e);const[r,t]=(0,n.useState)({error:null,hasError:!1}),s=(0,n.useMemo)((()=>({resetBoundary:()=>{e?.resetErrorBoundary(),t({error:null,hasError:!1})},showBoundary:e=>t({error:e,hasError:!0})})),[e?.resetErrorBoundary]);if(r.hasError)throw r.error;return s}s({},"useErrorBoundary",(()=>c));function l(e,r){const t=t=>(0,n.createElement)(a,r,(0,n.createElement)(e,t)),s=e.displayName||e.name||"Unknown";return t.displayName=`withErrorBoundary(${s})`,t}s({},"withErrorBoundary",(()=>l))},30214:function(e,r,t){t.d(r,{W:function(){return u}});var n=t(89555),s=(t(31014),t(88443)),o=t(32599),i=t(48012),a=t(50111);const u=e=>{let{className:r}=e;const{theme:t}=(0,o.u)();return(0,a.Y)(i.vwO,{componentId:"codegen_mlflow_app_src_shared_building_blocks_previewbadge.tsx_14",className:r,css:(0,n.AH)({marginLeft:t.spacing.xs},""),color:"turquoise",children:(0,a.Y)(s.A,{id:"8qJt7/",defaultMessage:"Experimental"})})}},34860:function(e){e.exports="data:image/png;base64,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"},48588:function(e,r,t){t.d(r,{L:function(){return i}});t(31014);var n=t(48012),s=t(15579),o=t(50111);function i(e){const{usesFullHeight:r,...t}=e;return(0,o.FD)(n.ffj,{css:r?a.useFullHeightLayout:a.wrapper,children:[(0,o.Y)(s.S,{css:a.fixedSpacer}),r?e.children:(0,o.Y)("div",{...t,css:a.container})]})}i.defaultProps={usesFullHeight:!1};const a={useFullHeightLayout:{height:"calc(100% - 60px)",display:"flex",flexDirection:"column","&:last-child":{flexGrow:1}},wrapper:{flex:1},fixedSpacer:{flexShrink:0},container:{width:"100%",flexGrow:1,paddingBottom:24}}},53140:function(e,r,t){t.d(r,{Ay:function(){return d},dD:function(){return l}});var n=t(31014),s=t(10811),o=t(72877),i=t(96277),a=t(47664),u=t(50111);class c extends n.Component{constructor(){super(...arguments),this.state={shouldRender:!1,shouldRenderError:!1}}static getErrorRequests(e,r){return e.filter((e=>void 0!==e.error&&!(r&&r.includes(e.id)&&e.error.getErrorCode()===a.tG.RESOURCE_DOES_NOT_EXIST)))}static getDerivedStateFromProps(e){const r=!!e.requests.length&&e.requests.every((e=>e&&!1===e.active)),t=c.getErrorRequests(e.requests,e.requestIdsWith404sToIgnore);return{shouldRender:r,shouldRenderError:t.length>0,requestErrors:t}}getRenderedContent(){const{children:e,requests:r,customSpinner:t,permissionDeniedView:n,suppressErrorThrow:s,customRequestErrorHandlerFn:o}=this.props,{shouldRender:c,shouldRenderError:d,requestErrors:p}=this.state,m=p.filter((e=>e.error.getErrorCode()===a.tG.PERMISSION_DENIED));return"function"===typeof e?e(!c,d,r,p):c||d||this.props.shouldOptimisticallyRender?m.length>0&&n?n:(d&&!s&&(o?o(p):l(p)),e):t||(0,u.Y)(i.y,{})}render(){return this.getRenderedContent()}}c.defaultProps={requests:[],requestIdsWith404sToIgnore:[],shouldOptimisticallyRender:!1};const l=e=>{throw console.error("ERROR",e),Error(`A request error occurred.: ${e.error}`)};var d=(0,s.Ng)(((e,r)=>({requests:(0,o.EW)(r.requestIds,e)})))(c)},62448:function(e,r,t){t.d(r,{h:function(){return a}});var n=t(39416),s=t(52350),o=t(47664);class i{}i.mlflowServices={MODEL_REGISTRY:"Model Registry",EXPERIMENTS:"Experiments",MODEL_SERVING:"Model Serving",RUN_TRACKING:"Run Tracking"};const a=(e,r)=>{if(!(e instanceof s.s))return;const{status:t}=e;let i;const a={status:t};e.getErrorCode()===o.tG.RESOURCE_DOES_NOT_EXIST&&(i=new n.m_(a)),e.getErrorCode()===o.tG.PERMISSION_DENIED&&(i=new n.i_(a)),e.getErrorCode()===o.tG.INTERNAL_ERROR&&(i=new n.PO(a)),e.getErrorCode()===o.tG.INVALID_PARAMETER_VALUE&&(i=new n.v7(a));const u=e.getMessageField();return i&&u&&(i.message=u),i};r.A=i},64558:function(e,r,t){var n=t(31014),s=t(50111);class o extends n.Component{render(){return(0,s.Y)("div",{children:"Resource not found."})}}r.A=o},79085:function(e,r,t){t.d(r,{o:function(){return l},z:function(){return p}});var n=t(89555),s=(t(31014),t(48012)),o=t(32599),i=t(15579),a=t(88464),u=t(30214),c=t(50111);function l(e){let{menu:r}=e;const t=(0,c.Y)(s.W1t,{children:r.map((e=>{let{id:r,itemName:t,onClick:n,href:o,...i}=e;return(0,c.Y)(s.W1t.Item,{onClick:n,href:o,"data-test-id":r,...i,children:t},r)}))});return r.length>0?(0,c.Y)(s.msM,{overlay:t,trigger:["click"],placement:"bottomLeft",arrow:!0,children:(0,c.Y)(o.B,{componentId:"codegen_mlflow_app_src_shared_building_blocks_pageheader.tsx_54",icon:(0,c.Y)(s.ssM,{}),"data-test-id":"overflow-menu-trigger","aria-label":"Open header dropdown menu"})}):null}var d={name:"1gz4j9a",styles:"margin-left:0"};function p(e){const{title:r,breadcrumbs:t=[],titleAddOns:l=[],preview:p,children:m,spacerSize:h,hideSpacer:g=!1,dangerouslyAppendEmotionCSS:f}=e,{theme:A}=(0,o.u)();(0,a.A)();return(0,c.FD)(c.FK,{children:[(0,c.Y)(s.Y9Y,{breadcrumbs:t.length>0&&(0,c.Y)(s.QpV,{includeTrailingCaret:!0,children:t.map(((e,r)=>(0,c.Y)(s.QpV.Item,{children:e},r)))}),buttons:m,title:r,titleAddOns:(0,c.FD)(c.FK,{children:[p&&(0,c.Y)(u.W,{css:d}),l]}),dangerouslyAppendEmotionCSS:f}),(0,c.Y)(i.S,{css:(0,n.AH)({flexShrink:0,...g?{display:"none"}:{}},""),size:h})]})}},83649:function(e,r,t){t.r(r),t.d(r,{MetricPage:function(){return L},MetricPageImpl:function(){return S},default:function(){return M}});var n=t(31014),s=t(10811),o=t(51079),i=t.n(o),a=t(26809),u=t(53140),c=t(64558),l=t(88443),d=t(76010),p=t(72877),m=t(3159),h=t(93215),g=t(79085),f=t(58481),A=t(25869),E=t(50111);class y extends n.Component{getCompareRunsPageText(e,r){return r>1?(0,E.Y)(l.A,{id:"IEORCP",defaultMessage:"Comparing {numRuns} Runs from {numExperiments} Experiments",values:{numRuns:e,numExperiments:r}}):(0,E.Y)(l.A,{id:"NN0ScV",defaultMessage:"Comparing {numRuns} Runs from 1 Experiment",values:{numRuns:e}})}hasMultipleExperiments(){return this.props.experimentIds.length>1}getRunPageLink(){const{experimentIds:e,runUuids:r,runNames:t}=this.props;if(!r||0===r.length)return null;if(1===r.length)return(0,E.Y)(h.N_,{to:f.h.getRunPageRoute(e[0],r[0]),children:t[0]});const n=this.getCompareRunsPageText(r.length,e.length);return(0,E.Y)(h.N_,{to:f.h.getCompareRunPageRoute(r,e),children:n})}getCompareExperimentsPageLinkText(e){return(0,E.Y)(l.A,{id:"LLm5Bo",defaultMessage:"Displaying Runs from {numExperiments} Experiments",values:{numExperiments:e}})}getExperimentPageLink(){const{comparedExperimentIds:e,hasComparedExperimentsBefore:r,experimentIds:t,experiments:n}=this.props;if(r&&e){const r=this.getCompareExperimentsPageLinkText(e.length);return(0,E.Y)(h.N_,{to:f.h.getCompareExperimentsPageRoute(e),children:r})}if(this.hasMultipleExperiments()){const e=this.getCompareExperimentsPageLinkText(t.length);return(0,E.Y)(h.N_,{to:f.h.getCompareExperimentsPageRoute(t),children:e})}return(0,E.Y)(h.N_,{to:f.h.getExperimentPageRoute(t[0]),children:n[0].name})}render(){const{experimentIds:e,runUuids:r,metricKey:t,location:n}=this.props,{selectedMetricKeys:s}=d.A.getMetricPlotStateFromUrl(n.search),o=s.length>1?(0,E.Y)(l.A,{id:"wbRVln",defaultMessage:"Metrics"}):s[0],i=[this.getExperimentPageLink(),this.getRunPageLink()];return(0,E.FD)("div",{children:[(0,E.Y)(g.z,{title:o,breadcrumbs:i,hideSpacer:!0}),(0,E.Y)(m.Ay,{experimentIds:e,runUuids:r,metricKey:t})]})}}const x=(0,A.h)((0,s.Ng)(((e,r)=>{const{comparedExperimentIds:t,hasComparedExperimentsBefore:n}=e.compareExperiments,{experimentIds:s,runUuids:o}=r;return{experiments:null!==s?s.map((r=>(0,p._m)(r,e))):null,runNames:o.map((r=>{const t=(0,p.K4)(r,e);return d.A.getRunDisplayName(t,r)})),comparedExperimentIds:t,hasComparedExperimentsBefore:n}}))(y));var I=t(7204),C=t(48588),R=t(20109),k=t(62448),v=t(64912),b=t(52350),Y=t(48012),N=t(32599);class S extends n.Component{constructor(e){super(e),this.requestIds=void 0,this.requestIds=[]}fetchExperiments(){return this.props.experimentIds.map((e=>{const r=(0,I.yk)();return this.props.dispatch((0,a.yc)(e,r)).catch((e=>{if(!(e instanceof b.s))throw e})),r}))}componentDidMount(){if(this.props.loadError instanceof Error){const e=this.props.intl.formatMessage({id:"eUUNCv",defaultMessage:"Error during metric page load: invalid URL"});throw new Error(e)}if(null!==this.props.experimentIds){const e=this.fetchExperiments();this.requestIds.push(...e)}this.props.runUuids.forEach((e=>{const r=(0,I.yk)();this.requestIds.push(r),this.props.dispatch((0,a.aO)(e,r)).catch((e=>{if(!(e instanceof b.s))throw e}))}))}renderPageContent(){const{runUuids:e}=this.props;return e.length>=1?(0,E.Y)(x,{runUuids:this.props.runUuids,metricKey:this.props.metricKey,experimentIds:this.props.experimentIds}):(0,E.Y)(c.A,{})}render(){return(0,E.Y)(C.L,{children:(0,E.Y)(u.Ay,{requestIds:this.requestIds,customRequestErrorHandlerFn:e=>{var r;const t=null===(r=e.find((e=>e.error)))||void 0===r?void 0:r.error;if(t instanceof b.s)throw t.translateToErrorInstance();if(t)throw t},children:this.renderPageContent()})})}}const w=(0,A.h)((0,s.Ng)(((e,r)=>{const{location:t}=r,n=i().parse(t.search);try{const e=JSON.parse(n["?runs"]),r=JSON.parse(n.metric);let t=null;return n.hasOwnProperty("experiments")&&(t=JSON.parse(n.experiments)),{runUuids:e,metricKey:r,experimentIds:t}}catch(s){return{runUuids:[],metricKey:"",experimentIds:[],loadError:s}}}))((0,v.Ay)(S)));var P={name:"64bku7",styles:"height:100%;align-items:center;justify-content:center;display:flex"};const L=(0,R.X)(k.A.mlflowServices.EXPERIMENTS,w,void 0,(e=>{let{error:r}=e;return(0,E.Y)("div",{css:P,children:(0,E.Y)(Y.SvL,{title:(0,E.Y)(l.A,{id:"N8YuIr",defaultMessage:"Error while loading metric page"}),description:r.message,image:(0,E.Y)(N.j,{})})})}));var M=L},96277:function(e,r,t){t.d(r,{y:function(){return i}});var n=t(34860),s=t(89555),o=t(50111);function i(e){let{showImmediately:r}=e;return(0,o.Y)("div",{css:e=>a.spinner(e,r),children:(0,o.Y)("img",{alt:"Page loading...",src:n})})}const a={spinner:(e,r)=>({width:100,marginTop:100,marginLeft:"auto",marginRight:"auto",img:{position:"absolute",opacity:0,top:"50%",left:"50%",width:2*e.general.heightBase,height:2*e.general.heightBase,marginTop:-e.general.heightBase,marginLeft:-e.general.heightBase,animation:`${s.i7`
          0% {
            opacity: 1;
          }
          100% {
            opacity: 1;
            -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
          `} 3s linear infinite`,animationDelay:r?"0s":"0.5s"}})}}}]);
//# sourceMappingURL=4461.86693416.chunk.js.map