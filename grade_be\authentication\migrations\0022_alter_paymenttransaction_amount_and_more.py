# Generated by Django 5.0.6 on 2025-04-01 07:26

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0021_alter_paymenttransaction_options_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="paymenttransaction",
            name="amount",
            field=models.DecimalField(decimal_places=7, max_digits=10),
        ),
        migrations.AlterField(
            model_name="usercredit",
            name="free_credit",
            field=models.DecimalField(
                decimal_places=7, default=Decimal("1.00"), max_digits=10
            ),
        ),
        migrations.AlterField(
            model_name="usercredit",
            name="paid_credit",
            field=models.DecimalField(
                decimal_places=7, default=Decimal("0.00"), max_digits=10
            ),
        ),
    ]
