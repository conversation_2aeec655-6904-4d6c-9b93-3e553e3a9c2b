{"version": 3, "file": "static/js/4035.932817ba.chunk.js", "mappings": "saAaO,MAAMA,EAAyBC,IAA4D,IAA3D,KAAEC,EAAO,GAAE,UAAEC,GAAwCF,EAC1F,MAAM,MAAEG,IAAUC,EAAAA,EAAAA,KAElB,OACEC,EAAAA,EAAAA,GAAA,OACEC,KAAGC,EAAAA,EAAAA,IAAE,CACHC,QAAS,OACTC,SAAU,OACV,MAAO,CACLC,YAAa,gBAEfC,IAAKR,EAAMS,QAAQC,IACpB,IAACC,SAEDb,EAAKc,OAAS,GACbV,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,yEACZC,KAAK,QACLC,KAAK,OACLC,QAASlB,EAAUY,UAEnBT,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,WAGnCC,EAAAA,EAAAA,IAAAC,EAAAA,GAAA,CAAAX,SAAA,CACGb,EAAKyB,KAAKC,IACTtB,EAAAA,EAAAA,GAACuB,EAAAA,EAAW,CAACD,IAAKA,GAAU,GAAGA,EAAIE,OAAOF,EAAIG,YAEhDzB,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,yEACZC,KAAK,QACLa,MAAM1B,EAAAA,EAAAA,GAAC2B,EAAAA,IAAU,IACjBZ,QAASlB,QAIX,E,gDCFwE,IAkB7E+B,EAAU,SAAVA,GAAU,OAAVA,EAAU,gBAAVA,EAAU,kBAAVA,EAAU,wCAAVA,EAAU,kBAAVA,EAAU,YAAVA,EAAU,cAAVA,EAAU,0BAAVA,EAAU,kBAAVA,CAAU,EAAVA,GAAU,IAAAC,EAAA,CAAAC,KAAA,UAAAC,OAAA,mDAWR,MAAMC,EAAoBrC,IASF,IATG,UAChCsC,EAAS,cACTC,EAAa,gBACbC,EAAe,SACfC,EAAQ,YACRC,EAAW,kBACXC,EAAiB,kBACjBC,EAAiB,QACjBC,GACuB7C,EACvB,MAAM8C,GAAmBC,EAAAA,EAAAA,UAAQ,KAC/B,MAAMC,EAAmC,CAAC,EAO1C,OANO,OAAPH,QAAO,IAAPA,GAAAA,EAASI,SAAQC,IAAyB,IAAxB,MAAEC,EAAK,QAAEC,GAASF,EAC7BF,EAAOI,KACVJ,EAAOI,GAAW,IAEpBJ,EAAOI,GAASC,KAAKF,EAAM,IAEtBH,CAAM,GACZ,CAACH,IACES,GAAWP,EAAAA,EAAAA,UACf,IACEP,GACKD,GAAiB,IAAIgB,QAAOC,IAAA,IAAC,cAAEC,GAAeD,EAAA,OAAKE,EAAAA,GAAcC,SAASF,EAAc,IACzFlB,GACN,CAACC,EAAiBD,KAGd,MAAEpC,IAAUC,EAAAA,EAAAA,KACZwD,GAAOC,EAAAA,EAAAA,KAEPC,GAAcf,EAAAA,EAAAA,UAAQ,KAC1B,MAAMgB,GAAwC,OAART,QAAQ,IAARA,OAAQ,EAARA,EAAU5B,KAAKsC,IAA6B,OAAZA,QAAY,IAAZA,OAAY,EAAZA,EAAc/D,OAAQ,KAAIgE,SAAU,GAG1G,OAAOC,MAAMC,KAAK,IAAIC,IAAIL,EAAYrC,KAAI2C,IAAA,IAAC,IAAExC,GAAKwC,EAAA,OAAKxC,CAAG,MAAIyC,MAAM,GACnE,CAAChB,IAEEiB,GAAWC,EAAAA,EAAAA,OAEX,cAAEC,EAAa,kBAAEC,IAAsBC,EAAAA,EAAAA,GAAiD,CAC5FC,iBAAkBd,EAClBe,gBAAiBC,MAAOd,EAAce,EAAcC,IAClDT,GAASU,EAAAA,EAAAA,IAA0BjB,EAAce,EAAcC,IACjEE,UAAWvC,KAGP,iBAAEwC,EAAgB,qBAAEC,IAAyBC,EAAAA,EAAAA,GAAmC,CACpFC,MAAO5C,GAAe,KACtBwC,UAAWvC,KAGN4C,EAAcC,IAAmBC,EAAAA,EAAAA,UAA4B,CAAC,IAE9DC,EAAYC,IAAiBF,EAAAA,EAAAA,UAA0B,CAC5DG,SAAU,GACVC,UAAW,KAGbC,EAAAA,EAAAA,YAAU,KACR,MAAMC,GAAoBzC,GAAY,IAAIC,QAAOyC,IAAA,IAAC,QAAE5C,GAAS4C,EAAA,OAAKT,EAAanC,EAAQ,IACjF6C,EAAyBF,EAAiBrE,KAAIwE,IAAA,IAAC,QAAE9C,GAAS8C,EAAA,OAAK9C,CAAO,IAC5EX,EAASwD,EAAwBF,EAAiB,GACjD,CAACR,EAAc9C,EAAUa,IAE5B,MAAM6C,GAAepD,EAAAA,EAAAA,UAAQ,KAC3B,MAAMqD,EAAmC,CACvC,CACE9E,GAAIW,EAAWoE,OACfC,eAAe,EACfC,OAAQ,GACRC,KAAM,CAAEpE,OAAQ,CAAEqE,UAAWtG,EAAMuG,QAAQC,SAAUC,SAAU,IAC/DC,KAAMC,IAA4B,IAAzBC,KAAK,SAAEC,IAAYF,EAC1B,MAAM,OAAEG,EAAM,eAAEC,GAAmBF,GAAY,CAAC,EAChD,OACE3G,EAAAA,EAAAA,GAAC8G,EAAAA,IAAa,CAACC,MAAOF,GAAkBG,EAAAA,GAA+BJ,GAAQnG,UAC7ET,EAAAA,EAAAA,GAACiH,EAAAA,EAAWC,KAAI,CAAAzG,SAAE0G,EAAAA,GAAwBP,MAC5B,IA8HxB,OAzHAb,EAAQ/C,KACN,CACE/B,GAAIW,EAAWwF,QACfnB,eAAe,EACfC,OAAQ3C,EAAK8D,cAAc,CAAApG,GAAA,SACzBC,eAAe,YAGjBiF,KAAM,CAAEmB,UAAW,iBACnBC,YAAa,UACbf,KAAMgB,IAAA,IAAC,SAAEC,GAAUD,EAAA,OACjBxH,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uCAEfwG,OAAQ,CACNC,KAAOC,IACL5H,EAAAA,EAAAA,GAAC6H,EAAAA,GAAI,CAACC,GAAIC,EAAAA,GAAoBC,yBAAyB/F,EAAWgG,OAAOR,MAAahH,SAAEmH,IAE1FM,cAAeT,MAEjB,GAGN,CACExG,GAAIW,EAAWuG,mBACflC,eAAe,EACfE,KAAM,CAAEpE,OAAQ,CAAEqG,SAAU,MAC5BlC,OAAQ3C,EAAK8D,cAAc,CAAApG,GAAA,SACzBC,eAAe,kBAGjBqG,YAAa,qBACbf,KAAM6B,IAAA,IAAC,SAAEZ,GAAUY,EAAA,OAAKC,EAAAA,EAAMC,gBAAgBd,IAAYlE,EAAK,GAGjE,CACEtC,GAAIW,EAAW4G,QACfvC,eAAe,EACfE,KAAM,CAAEpE,OAAQ,CAAEqG,SAAU,MAC5BlC,OAAQ3C,EAAK8D,cAAc,CAAApG,GAAA,SACzBC,eAAe,eAGjBqG,YAAa,UACbf,KAAMiC,IAAA,IAAC,SAAEhB,GAAUgB,EAAA,OAAKzI,EAAAA,EAAAA,GAAA,QAAAS,SAAOgH,KAAkB,IAIjDlF,EAEFwD,EAAQ/C,KACN,CACE/B,GAAIW,EAAW8G,KACfzC,eAAe,EACfC,OAAQ3C,EAAK8D,cAAc,CAAApG,GAAA,SACzBC,eAAe,SAGjBiF,KAAM,CAAEpE,OAAQ,CAAE4G,KAAM,IACxBpB,YAAa,OACbf,KAAMoC,IAAsC,IAArC,SAAEnB,EAAUf,KAAK,SAAEC,IAAYiC,EACpC,OACE5I,EAAAA,EAAAA,GAACN,EAAsB,CACrBE,KAAM6H,IACN5H,UAAWA,KACQ,OAAjBwE,QAAiB,IAAjBA,GAAAA,EAAoBsC,EAAS,GAE/B,GAIR,CACE1F,GAAIW,EAAWiH,QACftB,YAAa,UACbtB,eAAe,EACfC,OAAQ3C,EAAK8D,cAAc,CAAApG,GAAA,SACzBC,eAAe,YAGjBiF,KAAM,CAAEpE,OAAQ,CAAE4G,KAAM,GAAKG,WAAW,GACxCtC,KAAMuC,IAAsC,IAArC,SAAEtB,EAAUf,KAAK,SAAEC,IAAYoC,EACpC,MAAMC,EAAYvG,EAAiBkE,EAAS5D,UAAY,GACxD,OACE/C,EAAAA,EAAAA,GAACiJ,EAAAA,EAA4B,CAC3BhH,UAAWA,EACXc,QAAS4D,EAAS5D,QAClBP,QAASwG,EACTnJ,UAAWA,KACW,OAApBkF,QAAoB,IAApBA,GAAAA,EAAuB4B,EAAS5D,QAAQ,GAE1C,IAOVgD,EAAQ/C,KAAK,CACX/B,GAAIW,EAAWsH,MACfjD,eAAe,EACfC,OAAQ3C,EAAK8D,cAAc,CAAApG,GAAA,SACzBC,eAAe,UAGjBqG,YAAa,gBACbf,KAAM2C,IAAmB,IAAlB,SAAE1B,GAAU0B,EACjB,OAAOC,EAAAA,GAAmB3B,IAAqB,IAIrD1B,EAAQ/C,KAAK,CACX/B,GAAIW,EAAWyH,YACfpD,eAAe,EACfC,OAAQ3C,EAAK8D,cAAc,CAAApG,GAAA,SACzBC,eAAe,gBAGjBiF,KAAM,CAAEpE,OAAQ,CAAE4G,KAAM,IACxBpB,YAAa,cACbf,KAAM8C,IAAA,IAAC,SAAE7B,GAAU6B,EAAA,OAAKC,EAAAA,EAAAA,IAAiC9B,IAAsB,GAAG,IAE7E1B,CAAO,GACb,CAACjG,EAAOyD,EAAMtB,EAAWoC,EAAmBU,EAAsBxC,EAAmBE,KAEjF+G,EAASC,IAAcrE,EAAAA,EAAAA,UAAuB,CAAC,CAAEnE,GAAIW,EAAWuG,mBAAoBuB,MAAM,KAE3FC,GAAQC,EAAAA,EAAAA,IAAsC,CAClDC,KAAM5G,GAAY,GAClB8C,QAASD,EACTgE,MAAO,CACLzE,aACAH,eACAsE,WAEFO,iBAAiBA,EAAAA,EAAAA,MACjBC,mBAAmBA,EAAAA,EAAAA,MACnBC,uBAAuBA,EAAAA,EAAAA,MACvBC,SAAUC,IAAA,IAAC,QAAEpH,GAASoH,EAAA,OAAKpH,CAAO,EAClCqH,qBAAsBjF,EACtBkF,gBAAiBZ,IASba,GACJtK,EAAAA,EAAAA,GAACuK,EAAAA,IAAU,CACT3J,YAAY,6EACZ4J,iBAAkBnF,EAAWG,UAAY,EACzCiF,UAAWxH,GAAY,IAAIvC,OAC3B0B,SAAUA,CAACsI,EAAMnF,KACfD,EAAc,CACZC,SAAUA,GAAYF,EAAWE,SACjCC,UAAWkF,EAAO,GAClB,EAEJnF,SAAUF,EAAWE,WAInBoF,GACJ3K,EAAAA,EAAAA,GAAC4K,EAAAA,IAAK,CACJC,aACE7K,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,wGAGfwG,OAAQ,CACNC,KAAOC,IACL5H,EAAAA,EAAAA,GAACiH,EAAAA,EAAWY,KAAI,CACdjH,YAAY,6EACZkK,OAAO,SACPC,KA9BLC,EAAAA,GA8BiCvK,SAE3BmH,OAMXqD,OAAOjL,EAAAA,EAAAA,GAACkL,EAAAA,IAAQ,MAIpB,OACE/J,EAAAA,EAAAA,IAAAC,EAAAA,GAAA,CAAAX,SAAA,EACEU,EAAAA,EAAAA,IAACgK,EAAAA,IAAK,CACJ,cAAY,mBACZ9F,WAAYiF,EACZc,YAAU,EACVC,MAnDoD,IAApC1B,EAAM2B,cAAcC,KAAK7K,OAmDtBiK,OAAiBa,EACpCC,iBAAkB9B,EAAM+B,yBAA2B/B,EAAMgC,uBAAuBlL,SAAA,EAEhFU,EAAAA,EAAAA,IAACyK,EAAAA,IAAQ,CAACC,UAAQ,EAAApL,SAAA,EAChBT,EAAAA,EAAAA,GAAC8L,EAAAA,IAAkB,CACjBlL,YAAY,6EACZmL,QAASpC,EAAMgC,uBACfK,cAAerC,EAAM+B,wBACrBtJ,SAAUuH,EAAMsC,oCAEjBtC,EAAMuC,iBAAiB7K,KAAK6E,IAAM,IAAAiG,EAAA,OACjCnM,EAAAA,EAAAA,GAACoM,EAAAA,IAAW,CACVxL,YAAY,6EACZkI,WAAW,EAEXuD,SAAUnG,EAAOoG,OAAOC,aACxBC,cAAetG,EAAOoG,OAAOG,eAAiB,OAC9CC,aAAcxG,EAAOoG,OAAOK,0BAC5B1M,IAA4D,QAAzDkM,EAAGjG,EAAOoG,OAAOM,UAAoCzG,YAAI,IAAAgG,OAAA,EAAvDA,EAAyDpK,OAAOtB,UAEpEoM,EAAAA,EAAAA,IAAW3G,EAAOoG,OAAOM,UAAU1G,OAAQA,EAAO4G,eAN9C5G,EAAOjF,GAOA,OAGjB0I,EAAM2B,cAAcC,KAAKlK,KAAKqF,IAC7BvF,EAAAA,EAAAA,IAACyK,EAAAA,IAAQ,CAEP3L,IAAG4B,EAIDpB,SAAA,EAEFT,EAAAA,EAAAA,GAAC8L,EAAAA,IAAkB,CACjBlL,YAAY,6EACZmL,QAASrF,EAAIqG,gBACb3K,SAAUsE,EAAIsG,6BAEftG,EAAIuG,cAAc5L,KAAKmF,IAAI,IAAA0G,EAAAC,EAAAC,EAAA,OAC1BpN,EAAAA,EAAAA,GAACqN,EAAAA,IAAS,CACR/F,UAAgE,QAAvD4F,EAAG1G,EAAK8F,OAAOM,UAAoCzG,YAAI,IAAA+G,OAAA,EAArDA,EAAuD5F,UAClEwB,UAAgE,QAAvDqE,EAAG3G,EAAK8F,OAAOM,UAAoCzG,YAAI,IAAAgH,OAAA,EAArDA,EAAuDrE,UAElE7I,IAA0D,QAAvDmN,EAAG5G,EAAK8F,OAAOM,UAAoCzG,YAAI,IAAAiH,OAAA,EAArDA,EAAuDrL,OAAOtB,UAElEoM,EAAAA,EAAAA,IAAWrG,EAAK8F,OAAOM,UAAUpG,KAAMA,EAAKsG,eAHxCtG,EAAKvF,GAIA,MApBTyF,EAAIzF,SAyBdmD,EACAU,IACA,E,wICvXA,MAAMwI,EACN,MADMA,EAEH,SACR,IAAAnK,EAAA,CAAArB,KAAA,SAAAC,OAAA,sBAAAiC,EAAA,CAAAlC,KAAA,UAAAC,OAAA,2DAuBK,MAAMwL,UAAsBC,EAAAA,UACjCC,WAAAA,CAAYC,GACVC,MAAMD,GAAO,KAIf5D,MAAQ,CACN8D,YAAaN,EACbO,uBAAuB,EACvBC,sBAAsB,EACtBC,6BAA6B,EAC7BC,aAAc,CAAC,EACfC,sBAAsB,EACtBC,0BAA0B,GAC1B,KAEFC,QAAUX,EAAAA,YAAkB,KAQ5BY,wBAA2BC,IACzBC,KAAKC,SAAS,CAAEX,YAAaS,EAAEvD,OAAOrJ,OAAQ,EAC9C,KAOF+M,4BAA8B,KAC5BF,KAAKC,SAAS,CAAEV,uBAAuB,GAAQ,EAC/C,KAEFY,4BAA+B5D,GACtByD,KAAKZ,MAAMgB,sBAAsB7D,GAAa8D,MAAK,KACxDL,KAAKC,SAAS,CAAEV,uBAAuB,GAAQ,IAEjD,KAEFe,wBAA2BP,IACzBA,EAAEQ,kBACFP,KAAKC,SAAS,CAAEV,uBAAuB,GAAO,EAC9C,KAqBFiB,gBAAkB,KAChBR,KAAKC,SAAS,CAAET,sBAAsB,GAAO,EAC7C,KAEFiB,gBAAkB,KAChBT,KAAKC,SAAS,CAAET,sBAAsB,GAAQ,EAC9C,KAEFkB,mBAAqB,KACnBV,KAAKC,SAAS,CAAER,6BAA6B,GAAO,EACpD,KAEFkB,mBAAqB,KACnBX,KAAKC,SAAS,CAAER,6BAA6B,GAAQ,EACrD,KAEFmB,oBAAsB,KACpB,MAAM,SAAEC,GAAab,KAAKZ,MAC1BY,KAAKU,qBACLV,KAAKZ,MACF0B,eACAT,MAAK,KACJQ,EAASpH,EAAAA,GAAoBsH,mBAAmB,IAEjDC,OAAOjB,IACNC,KAAKW,qBACL3G,EAAAA,EAAMiH,sBAAsBlB,EAAE,GAC9B,EACJ,KAEFmB,aAAgB9H,IACd,MAAM+H,EAAOnB,KAAKH,QAAQuB,SACpB,MAAEzK,GAAUqJ,KAAKZ,MAEjBzL,EAAYgD,EAAMnD,KACxBwM,KAAKC,SAAS,CAAEN,sBAAsB,IACtCK,KAAKZ,MACFiC,yBAAyB1N,EAAWyF,EAAO5F,KAAM4F,EAAOjG,OACxDkN,MAAK,KACJL,KAAKC,SAAS,CAAEN,sBAAsB,IACrCwB,EAAaG,aAAa,IAE5BN,OAAOO,IACNvB,KAAKC,SAAS,CAAEN,sBAAsB,IAEtC6B,QAAQC,MAAMF,GACd,MAAMG,EAAUH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGG,QACvE1H,EAAAA,EAAM6H,+BAA+B,6BAA+BH,EAAQ,GAC5E,EACJ,KAEFI,eAAiBzQ,IAA2B,IAA1B,KAAEmC,EAAI,MAAEL,GAAY9B,EACpC,MAAM,MAAEsF,GAAUqJ,KAAKZ,MAEjBzL,EAAYgD,EAAMnD,KACxB,OAAOwM,KAAKZ,MAAMiC,yBAAyB1N,EAAWH,EAAML,GAAO6N,OAAOO,IAExEC,QAAQC,MAAMF,GACd,MAAMG,EAAUH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGG,QACvE1H,EAAAA,EAAM6H,+BAA+B,6BAA+BH,EAAQ,GAC5E,EACF,KAEFK,gBAAkBxN,IAAoB,IAAnB,KAAEf,GAAWe,EAC9B,MAAM,MAAEoC,GAAUqJ,KAAKZ,MAEjBzL,EAAYgD,EAAMnD,KACxB,OAAOwM,KAAKZ,MAAM4C,4BAA4BrO,EAAWH,GAAMwN,OAAOO,IAEpEC,QAAQC,MAAMF,GACd,MAAMG,EAAUH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGG,QACvE1H,EAAAA,EAAM6H,+BAA+B,gCAAkCH,EAAQ,GAC/E,EACF,KAEF5N,SAAW,CAACmO,EAAsBC,KAChC,MAAMC,EAAWC,OAAOC,OAAO,CAAC,EAAGrC,KAAKxE,OACxC2G,EAASzC,aAAe,CAAC,EACzBwC,EAAa5N,SAAS8D,IACpB+J,EAASzC,aAAe,IACnByC,EAASzC,aACZ,CAACtH,EAAI3D,SAAU2D,EAAIkK,OACpB,IAEHtC,KAAKC,SAASkC,EAAS,EACvB,KA6BFI,cAAgB,KACd,MAAM,MAAE5L,EAAK,cAAE/C,EAAa,KAAEtC,GAAS0O,KAAKZ,OACtC,YACJE,EAAW,sBACXC,EAAqB,qBACrBC,EAAoB,4BACpBC,EAA2B,qBAC3BE,GACEK,KAAKxE,MAEH7H,EAAYgD,EAAMnD,KAClBgP,EAAkBJ,OAAOK,KAAKzC,KAAKxE,MAAMkE,cAActN,OAAS,EACtE,OACES,EAAAA,EAAAA,IAAA,OAAKlB,IAAK8B,EAAOiP,QAAQvQ,SAAA,EAEvBU,EAAAA,EAAAA,IAAC8P,EAAAA,EAAY,CAAClL,QAAS,EAAG,cAAY,sBAAqBtF,SAAA,EACzDT,EAAAA,EAAAA,GAACiR,EAAAA,EAAaC,KAAI,CAChB,cAAY,2BACZC,MAAO7C,KAAKZ,MAAMnK,KAAK8D,cAAc,CAAApG,GAAA,SACnCC,eAAe,iBAEdT,SAGF6H,EAAAA,EAAMC,gBAAgBtD,EAAMmM,mBAAoB9C,KAAKZ,MAAMnK,SAE9DvD,EAAAA,EAAAA,GAACiR,EAAAA,EAAaC,KAAI,CAChB,cAAY,2BACZC,MAAO7C,KAAKZ,MAAMnK,KAAK8D,cAAc,CAAApG,GAAA,SACnCC,eAAe,kBAEdT,SAGF6H,EAAAA,EAAMC,gBAAgBtD,EAAMoM,uBAAwB/C,KAAKZ,MAAMnK,QAIhE0B,EAAcqM,UACdtR,EAAAA,EAAAA,GAACiR,EAAAA,EAAaC,KAAI,CAChB,cAAY,2BACZC,MAAO7C,KAAKZ,MAAMnK,KAAK8D,cAAc,CAAApG,GAAA,SACnCC,eAAe,YAEdT,UAGHT,EAAAA,EAAAA,GAAA,OAAAS,SAAOwE,EAAcqM,gBAM3BtR,EAAAA,EAAAA,GAACuR,EAAAA,EAAkB,CACjBtR,IAAK8B,EAAOyP,iBACZzK,OACE5F,EAAAA,EAAAA,IAAA,QAAAV,SAAA,EACET,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gBAGd,IACD2M,EAA2D,KAAnCS,KAAKmD,+BAGnCC,UAAW7D,EAGX8D,kBAAoB1M,EAAc4F,YAClC,eAAa,4BAA2BpK,UAExCT,EAAAA,EAAAA,GAAC4R,EAAAA,EAAY,CACXC,gBAAkB5M,EAAc4F,YAChCiH,SAAUxD,KAAKG,4BACfsD,SAAUzD,KAAKE,4BACfwD,WAAYnE,OAGhB7N,EAAAA,EAAAA,GAAA,OAAK,eAAa,eAAcS,UAC9BT,EAAAA,EAAAA,GAACuR,EAAAA,EAAkB,CACjBtR,IAAK8B,EAAOyP,iBACZzK,OACE/G,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,SAKnByQ,iBAA6D,IAA3CrJ,EAAAA,EAAM2J,oBAAoBrS,GAAMc,OAClD,eAAa,qBAAoBD,UAEjCT,EAAAA,EAAAA,GAACkS,EAAAA,EACC,CACAC,SAAU7D,KAAKH,QACfqB,aAAclB,KAAKkB,aACnBa,gBAAiB/B,KAAK+B,gBACtBD,eAAgB9B,KAAK8B,eACrBxQ,KAAMA,EACNwS,iBAAkBnE,SAIxB9M,EAAAA,EAAAA,IAACoQ,EAAAA,EAAkB,CACjBtR,IAAK8B,EAAOyP,iBACZzK,OACE/G,EAAAA,EAAAA,GAAAoB,EAAAA,GAAA,CAAAX,UACEU,EAAAA,EAAAA,IAAA,OAAKlB,IAAK8B,EAAOsQ,mBAAmB5R,SAAA,EAClCT,EAAAA,EAAAA,GAAA,QAAAS,UACET,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gBAKjBoN,KAAKZ,MAAMnL,oBACXpB,EAAAA,EAAAA,IAACmR,EAAAA,IAAqB,CACpB1R,YAAY,qEACZkB,KAAK,eACLL,MAAO6M,KAAKxE,MAAM8D,YAClBxL,SAAWiM,GAAMC,KAAKF,wBAAwBC,GAC9CpO,IAAGkD,EAA2B1C,SAAA,EAE9BT,EAAAA,EAAAA,GAACuS,EAAAA,IAAsB,CAAC9Q,MAAO6L,EAAiB7M,UAC9CT,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAInBC,EAAAA,EAAAA,IAACoR,EAAAA,IAAsB,CAAC9Q,MAAO6L,EAAoB7M,SAAA,EACjDT,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAGd,IACFoN,KAAKkE,gCAIZxS,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,qEACZ,eAAa,gBACb6R,SAAU3B,EACV/P,QAASuN,KAAKoE,UAAUjS,UAExBT,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,mBAQzB,eAAa,yBAAwBT,SAAA,EAEpCkS,EAAAA,EAAAA,QACC3S,EAAAA,EAAAA,GAAA,OACEC,IAAG+D,EAIDvD,UAEFT,EAAAA,EAAAA,GAAC4S,EAAAA,EAAwB,OAG7B5S,EAAAA,EAAAA,GAACgC,EAAiB,CAChBG,gBAAiByL,IAAgBN,IAAwBgB,KAAKZ,MAAMnL,kBACpEN,UAAWA,EACXC,cAAeA,EACfG,YAAa4C,EACb7C,SAAUkM,KAAKlM,SACfE,kBAAmBgM,KAAKZ,MAAMpL,kBAC9BC,kBAAmB+L,KAAKZ,MAAMnL,kBAC9BC,QAAc,OAALyC,QAAK,IAALA,OAAK,EAALA,EAAOzC,cAKpBxC,EAAAA,EAAAA,GAAC6S,EAAAA,EAAW,CACVjS,YAAY,qEACZ,cAAY,qBACZmG,MAAOuH,KAAKZ,MAAMnK,KAAK8D,cAAc,CAAApG,GAAA,SACnCC,eAAe,iBAGjB4R,QAAShF,EACTiF,eAAgBhF,EAChBiF,KAAM1E,KAAKY,oBACX+D,OAAQ3E,KAAKZ,MAAMnK,KAAK8D,cAAc,CAAApG,GAAA,SACpCC,eAAe,WAGjBgS,WAAY5E,KAAKZ,MAAMnK,KAAK8D,cAAc,CAAApG,GAAA,SACxCC,eAAe,WAGjB6Q,SAAUzD,KAAKS,gBAAgBtO,UAE/BT,EAAAA,EAAAA,GAAA,QAAAS,UACET,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sEAEfwG,OAAQ,CAAEzF,UAAWA,WAIvB,EAjYRqM,KAAKoE,UAAYpE,KAAKoE,UAAUS,KAAK7E,KACvC,CAcA8E,iBAAAA,GAEE,MAAMC,EAAY,GAAG/E,KAAKZ,MAAMzI,MAAMnD,sBACtCwG,EAAAA,EAAMgL,gBAAgBD,EACxB,CAMAb,sBAAAA,GACE,MAAM,cAAEtQ,GAAkBoM,KAAKZ,MAC/B,OAAOxL,EAAgBA,EAAcgB,QAAQqQ,GAAMlQ,EAAAA,GAAcC,SAASiQ,EAAEnQ,iBAAgB1C,OAAS,CACvG,CAiBA8S,oBAAAA,GAgBE,MAfkB,CAChB,CACEvS,GAAI,SACJwS,UACEzT,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAKnBH,QAASuN,KAAKQ,gBACd2D,SAAUnE,KAAKkE,yBAA2B,GAKhD,CAyFAE,SAAAA,GACOpE,KAAKZ,MAAMzI,OAGhBqJ,KAAKZ,MAAMyB,SACTpH,EAAAA,GAAoB2L,iCAAiCpF,KAAKZ,MAAMzI,MAAMnD,KAAMwM,KAAKxE,MAAMkE,cAE3F,CAEAyD,yBAAAA,GACE,OACEzR,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,qEACZ,eAAa,wBACbE,KAAK,OACLb,IAAK8B,EAAO4R,WACZ5S,QAASuN,KAAKM,wBAAwBnO,UAEtCT,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,UAMvB,CAqNA0S,eAAAA,GACE,OAAOtF,KAAKuC,eACd,CAEAgD,MAAAA,GACE,MAAM,MAAE5O,GAAUqJ,KAAKZ,MAEjBzL,EAAYgD,EAAMnD,KAElBgS,EAAc,EAClB9T,EAAAA,EAAAA,GAAC6H,EAAAA,GAAI,CAACC,GAAIC,EAAAA,GAAoBsH,mBAAmB5O,UAC/CT,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yBAKrB,OACEC,EAAAA,EAAAA,IAAA,OAAAV,SAAA,EACET,EAAAA,EAAAA,GAAC+T,EAAAA,EAAU,CAAChN,MAAO9E,EAAW6R,YAAaA,EAAYrT,UACrDT,EAAAA,EAAAA,GAACgU,EAAAA,EAAY,CAACC,KAAM3F,KAAKkF,2BAE1BlF,KAAKsF,oBAGZ,EAGF,MAKMM,EAAqB,CAAEvE,yBAAwB,KAAEW,4BAA4B,MAE7EvO,EAAS,CACboS,oCAAsCrU,IAAU,CAC9CsU,MAAO,IACPC,aAAcvU,EAAMS,QAAQ+T,KAE9BC,+BAAiCzU,IAAU,CACzC0U,YAAa1U,EAAMS,QAAQkU,GAC3BC,aAAc5U,EAAMS,QAAQkU,KAE9BjD,iBAAmB1R,IAAU,CAC3BuU,aAAcvU,EAAMS,QAAQ+T,KAE9BtD,QAAUlR,IAAU,CAMlB,oCAAqC,CACnC6U,OAAQ7U,EAAMuG,QAAQuO,gBAG1BjB,WAAa7T,IAAU,CACrB+U,WAAY/U,EAAMS,QAAQ+T,KAE5BjC,mBAAqBvS,IAAU,CAC7BK,QAAS,OACTG,IAAKR,EAAMS,QAAQ+T,GACnBQ,WAAY,YAIHC,GAAYC,EAAAA,EAAAA,KAvCDC,CAACnL,EAAYoL,KACnC,MAAMjT,EAAYiT,EAASjQ,MAAMnD,KAEjC,MAAO,CAAElC,MADIuV,EAAAA,EAAAA,IAAuBlT,EAAW6H,GAChC,GAsCfoK,EAFuBc,EAGvBI,EAAAA,EAAAA,IAAwBC,EAAAA,EAAAA,IAAW9H,K,6FCnd9B,MAAM+H,WAAsB9H,EAAAA,UAAoCC,WAAAA,GAAA,SAAA8H,WAAA,KACrEC,yBAAmB,OACnBC,oBAAc,OAEdC,qCAAsCC,EAAAA,EAAAA,MAAU,KAChDC,oCAAqCD,EAAAA,EAAAA,MAAU,KAC/CE,4BAA6BF,EAAAA,EAAAA,MAAU,KACvCG,4BAA6BH,EAAAA,EAAAA,MAAU,KAEvCI,0BAA4B,CAACzH,KAAKoH,oCAAqCpH,KAAKsH,oCAAoC,KAEhHlH,sBAAyB7D,IACvB,MAAM,MAAE5F,GAAUqJ,KAAKZ,MACvB,OAAOY,KAAKZ,MACTsI,yBAAyB/Q,EAAMnD,KAAM+I,EAAayD,KAAKuH,4BACvDlH,KAAKL,KAAK2H,SAAS,EACtB,KAEF7G,aAAe,KACb,MAAM,MAAEnK,GAAUqJ,KAAKZ,MACvB,OAAOY,KAAKZ,MAAMwI,yBAAyBjR,EAAMnD,KAAMwM,KAAKwH,2BAA2B,EACvF,KAEFG,SAAYE,IACV,MAAM,UAAElU,GAAcqM,KAAKZ,MAC3BY,KAAKkH,qBAAsB,EAC3B,MAAMY,EAAgB,CACpB9H,KAAKZ,MAAM2I,sBACTpU,GACqB,IAArBkU,EAA4B7H,KAAKsH,mCAAqC,MAExEtH,KAAKZ,MAAM4I,uBACT,CAAExU,KAAMG,IACa,IAArBkU,EAA4B7H,KAAKoH,oCAAsC,OAG3E,OAAOa,QAAQC,IAAIJ,GAAezH,MAAK,KACrCL,KAAKkH,qBAAsB,CAAK,GAChC,EACF,KAEFiB,SAAW,KACT,MAAM,UAAExU,EAAS,SAAEkN,GAAab,KAAKZ,MACrC,OAAKY,KAAKkH,qBAAuBlN,EAAAA,EAAMoO,sBAE9BpI,KAAK2H,WAAW3G,OAAOjB,IACxBA,aAAa4B,EAAAA,GAAqC,4BAArB5B,EAAEsI,gBACjCrO,EAAAA,EAAMiH,sBAAsBlB,GAC5BC,KAAKZ,MAAMwI,yBAAyBjU,OAAWuJ,GAAW,GAC1D2D,EAASpH,EAAAA,GAAoBsH,qBAG7BS,QAAQC,MAAM1B,GAEhBC,KAAKkH,qBAAsB,CAAK,IAG7Be,QAAQK,SAAS,CACxB,CAEFxD,iBAAAA,GAEE9E,KAAK2H,UAAS,GAAM3G,MAAMQ,QAAQC,OAClCzB,KAAKkH,qBAAsB,EAC3BlH,KAAKmH,eAAiBoB,YAAYvI,KAAKmI,SAAUK,EAAAA,GACnD,CAEAC,oBAAAA,GACEC,cAAc1I,KAAKmH,eACrB,CAEA5B,MAAAA,GACE,MAAM,MAAE5O,EAAK,cAAE/C,EAAa,SAAEiN,EAAQ,UAAElN,GAAcqM,KAAKZ,MAC3D,OACE1N,EAAAA,EAAAA,GAACiX,EAAAA,EAAa,CAAAxW,UACZT,EAAAA,EAAAA,GAACkX,EAAAA,GAAmB,CAClBC,WAAY7I,KAAKyH,0BACjBtV,SAECA,CAAC2W,EAAcC,EAAeC,KAC7B,GAAID,EAAU,CAEZ,GADAL,cAAc1I,KAAKmH,gBACfnN,EAAAA,EAAMiP,gBAAgBD,EAAU,CAAChJ,KAAKsH,qCACxC,OACE5V,EAAAA,EAAAA,GAACwX,EAAAA,EAAS,CACRC,WAAY,IACZC,WAAYpJ,KAAKZ,MAAMnK,KAAK8D,cAC1B,CAAApG,GAAA,SACEC,eAAe,oCAGjB,CACEe,UAAWA,IAGf0V,2BAA4B5P,EAAAA,GAAoBsH,qBAItD,MAAMuI,EAAyBN,EAASpU,QAAQ2U,IAAkB,IAADC,EAC/D,OACExJ,KAAKyH,0BAA0BzS,SAASuU,EAAQ5W,MACnC,QAAb6W,EAAAD,EAAQ9H,aAAK,IAAA+H,OAAA,EAAbA,EAAenB,kBAAmBoB,EAAAA,GAAWC,iBAAiB,IAGR,IAADC,EAAzD,GAAIL,GAA0BA,EAAuB,GACnD,OACE5X,EAAAA,EAAAA,GAACwX,EAAAA,EAAS,CACRC,WAAY,IACZC,WAAYpJ,KAAKZ,MAAMnK,KAAK8D,cAC1B,CAAApG,GAAA,SACEC,eAAe,0DAGjB,CACEe,UAAWA,EACXiW,SAAyC,QAAjCD,EAAEL,EAAuB,GAAG7H,aAAK,IAAAkI,OAAA,EAA/BA,EAAiC/H,oBAG/CyH,2BAA4B5P,EAAAA,GAAoBsH,sBAKtD8I,EAAAA,EAAAA,IAAab,EACf,KAAO,IAAIF,EACT,OAAOpX,EAAAA,EAAAA,GAACoY,EAAAA,EAAO,IACV,GAAInT,EAET,OACEjF,EAAAA,EAAAA,GAAC+U,EAAS,CACR9P,MAAOA,EACP/C,cAAeA,EACfwM,sBAAuBJ,KAAKI,sBAC5BU,aAAcd,KAAKc,aACnBD,SAAUA,EACV7M,kBAAmBgM,KAAK2H,UAG9B,CACA,OAAO,IAAI,KAKrB,EAGF,MAWM/B,GAAqB,CACzBoC,uBAAsB,KACtBD,sBAAqB,KACrBL,yBAAwB,KACxBE,yBACF,MAEMmC,IAAsBC,EAAAA,EAAAA,IAE1BtD,EAAAA,EAAAA,KApBsBC,CAACnL,EAAYoL,KACnC,MAAMjT,EAAYsW,mBAAmBrD,EAASsD,OAAOvW,WAGrD,MAAO,CACLA,YACAgD,MAJY6E,EAAM2O,SAASC,YAAYzW,GAKvCC,eAJoByW,EAAAA,EAAAA,IAAiB7O,EAAO7H,GAK7C,GAYwBiS,GAAzBc,EAA6CK,EAAAA,EAAAA,IAAWC,MAG7CsD,IAAYC,EAAAA,GAAAA,GAAkBC,GAAAA,EAAWC,eAAeC,eAAgBX,IAErF,S,yKC1NwG,IAAA1Y,EAAA,CAAAmC,KAAA,SAAAC,OAAA,iBAyExG,SAASkX,EAAgB1U,GACvB,OAAQ0P,GA/DV,SAAsBA,EAA0B1P,GAC9C,MAAMhB,GAAOC,EAAAA,EAAAA,MACP,MAAE1D,IAAUC,EAAAA,EAAAA,KACZmZ,EAAcjF,EAAKvG,MAAMwL,YAAYC,cAwD3C,OAtDqBzW,EAAAA,EAAAA,UAAQ,KAC3B,IAAKwW,EAAa,OAAOjF,EAGzB,IADsBmF,EAAAA,EAAAA,eAAc7U,EAAkB2U,IAAgB,EACnD,OAAOjF,EAE1B,MAAMoF,EAAgB,kBAAkBC,KAAKJ,GAG7C,OAAO1L,EAAAA,aAAmByG,EAAM,CAC9BsF,eAAgB,CACd,CACE1P,KAAM,CACJpI,MAAOyX,EACPzG,UAAW4G,EACXG,MAAO,CACLC,MAAOJ,EAAgBvZ,EAAM4Z,OAAOC,0BAA4B7Z,EAAM4Z,OAAOE,oBAE/EnZ,UACET,EAAAA,EAAAA,GAAC8G,EAAAA,IAAa,CACZC,MACEsS,OACI7N,EACAjI,EAAK8D,cAAc,CAAApG,GAAA,SACjBC,eAAe,iDAKvB2Y,UAAU,QAAOpZ,UAEjBU,EAAAA,EAAAA,IAAA,QAAMlB,IAAGN,EAAuBc,SAAA,EAC9BT,EAAAA,EAAAA,GAACkL,EAAAA,IAAQ,CAACjL,KAAGC,EAAAA,EAAAA,IAAE,CAAEG,YAAaP,EAAMS,QAAQkU,IAAI,MAC/ClR,EAAK8D,cACJ,CAAApG,GAAA,SACEC,eAAe,sBAGjB,CACE4Y,OAAQZ,UAOpB1X,IAAK0X,EACLa,aAAa,MAEZ9F,EAAKvG,MAAM6L,iBAEhB,GACD,CAAChV,EAAkB0P,EAAMiF,EAAa3V,EAAMzD,GAGjD,CAGuCka,CAAa/F,EAAM1P,EAC1D,CAEA,IAAApB,EAAA,CAAArB,KAAA,UAAAC,OAAA,cAGO,SAASkY,EAAoBpX,GAQhC,IARiC,iBACnC0B,EAAgB,QAChB2V,EAAO,oBACPC,GAKDtX,EACC,MAAMU,GAAOC,EAAAA,EAAAA,MACN4W,EAAQC,IAAajV,EAAAA,EAAAA,WAAS,GAC/BkV,GAAYC,EAAAA,EAAAA,QAAgD,OAE5D,MAAEC,EAAK,WAAEC,IAAeC,EAAAA,EAAAA,IAAc,CAC1CR,QAASA,EACTpY,KAAM,MACN6Y,MAAO,CACLC,SAAU,CACR5K,QAASzM,EAAK8D,cAAc,CAAApG,GAAA,SAC1BC,eAAe,0BAGjBO,OAAO,MAmBb,OACEzB,EAAAA,EAAAA,GAAC6a,EAAAA,IAAY,CACXC,YAAU,EACVC,IAAKT,EACLU,wBAAyB,CACvBC,YAAY,EACZC,eAAgBjC,EAAgB1U,IAElCtE,IAAGkD,EACHgY,YAAa5X,EAAK8D,cAAc,CAAApG,GAAA,SAC9BC,eAAe,eAGjBO,MAAO+Y,EAAM/Y,MACb2Z,aAAcZ,EAAM/Y,MACpB4Z,KAAMjB,EACNkB,wBA9BiCxI,IACnCuH,EAAUvH,EAAQ,EA8BhByI,aAAcA,CAACC,EAAOC,IAAiB,OAANA,QAAM,IAANA,OAAM,EAANA,EAAQha,MAAM0X,cAAc7V,SAASkY,EAAMrC,eAC5EuC,SAvBkBla,IACpBgZ,EAAMpY,SAASZ,GACI,OAAnB2Y,QAAmB,IAAnBA,GAAAA,EAAsB3Y,EAAI,EAsBxBma,QA7BgBC,KAClBpB,EAAMpY,cAASoJ,GACI,OAAnB2O,QAAmB,IAAnBA,GAAAA,OAAsB3O,EAAU,EA4B9BqQ,gBAAiBpB,EAAW1K,MAAQ,aAAUvE,EAAU/K,SAEvD8D,EAAiBlD,KAAKC,IACrBtB,EAAAA,EAAAA,GAAC6a,EAAAA,IAAaiB,OAAM,CAACra,MAAOH,EAAIb,SAC7Ba,GADmCA,MAM9C,C,0BCpIA,SAASya,EAAWnc,GAClB,OAAO,IAAIoc,IAAIpc,EAAKyB,KAAKC,GAAQ,CAACA,EAAIE,IAAKF,KAC7C,CAEA,IAAA6B,EAAA,CAAArB,KAAA,SAAAC,OAAA,UAAAiC,EAAA,CAAAlC,KAAA,SAAAC,OAAA,UAGO,MAAMuC,EAA2B3E,IAYjC,IAZyE,UAC9EkF,EAAS,gBACTL,EAAe,iBACfD,EAAgB,cAChB0X,GAAgB,EAAK,MACrBlV,GAODpH,EACC,MAAMuc,GAAkB3B,EAAAA,EAAAA,WACjB4B,EAAcC,IAAmBhX,EAAAA,EAAAA,UAAiB,KACnD,MAAEtF,IAAUC,EAAAA,EAAAA,MAEXsc,EAAaC,IAAkBlX,EAAAA,EAAAA,UAAsC,IAAI4W,MACzEO,EAAWC,IAAgBpX,EAAAA,EAAAA,UAAsC,IAAI4W,MAErES,EAAWC,IAAgBtX,EAAAA,EAAAA,WAAS,GAErCqK,GAAOkN,EAAAA,EAAAA,IAAwB,CACnCC,cAAe,CACbpb,SAAKgK,EACL/J,MAAO,MAILob,EAAYA,IAAMH,GAAa,GAK/BrY,GAAoByY,EAAAA,EAAAA,cACvBC,IACCb,EAAgBxM,QAAUqN,EAC1BT,EAAeP,EAAWgB,EAAand,MAAQ,KAC/C4c,EAAaT,EAAWgB,EAAand,MAAQ,KAC7C6P,EAAKuN,QAELN,GAAa,EAAK,GAEpB,CAACjN,IAGGwN,EAAWxY,UACVyX,EAAgBxM,UAGrB0M,EAAgB,IAChBc,GAAa,GACb1Y,EAAgB0X,EAAgBxM,QAAS7L,MAAMC,KAAKuY,EAAY3U,UAAW7D,MAAMC,KAAKyY,EAAU7U,WAC7FiH,MAAK,KACJkO,IACS,OAAThY,QAAS,IAATA,GAAAA,IACAqY,GAAa,EAAM,IAEpB5N,OAAOjB,IAA6B,IAAD8O,EAClCD,GAAa,GACbd,EAAgB/N,aAAa4B,EAAAA,EAAsC,QAA1BkN,EAAG9O,EAAE+O,6BAAqB,IAAAD,OAAA,EAAvBA,EAAyBnN,QAAU3B,EAAE2B,QAAQ,IACzF,EAGAzM,GAAOC,EAAAA,EAAAA,KACP6Z,EAAa5N,EAAK6N,SAEjBC,EAAWL,IAAgB9X,EAAAA,EAAAA,WAAS,GAErCoY,GAAe9a,EAAAA,EAAAA,UACnB,MAAO+a,EAAAA,EAAAA,UAAQC,EAAAA,EAAAA,QAAO7Z,MAAMC,KAAKuY,EAAY3U,UAAW,QAAQgW,EAAAA,EAAAA,QAAO7Z,MAAMC,KAAKyY,EAAU7U,UAAW,SACvG,CAAC2U,EAAaE,IAEVoB,EAAUN,EAAW7b,KAAO6b,EAAW5b,MACvCmc,EAAqBJ,GAAgBG,EAmL3C,MAAO,CAAEvZ,eAnJPjD,EAAAA,EAAAA,IAAC0c,EAAAA,EAAK,CACJjd,YAAY,uEACZkd,gBAAc,EACdhL,QAAS2J,EACT1V,MACO,OAALA,QAAK,IAALA,EAAAA,GACE/G,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAKrB6Q,SAAU8K,EACVkB,QACE5c,EAAAA,EAAAA,IAAC6c,EAAAA,EAA2B,CAAAvd,SAAA,EAC1BT,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,uEACZqd,gCAA8B,EAC9Bld,QAAS8b,EAKT5c,KAAGC,EAAAA,EAAAA,IAAE,CAAEG,YAAcmd,EAAkC,EAAnB1d,EAAMS,QAAQkU,IAAQ,IAAChU,SAE1D8C,EAAK8D,cAAc,CAAApG,GAAA,SAClBC,eAAe,aAIlB0c,GACC5d,EAAAA,EAAAA,GAACke,EAAwB,CAACb,WAAYA,EAAYE,UAAWA,EAAWY,WAAYlB,KAEpFjd,EAAAA,EAAAA,GAAC8G,EAAAA,IAAa,CACZC,MACGyW,OAKGhS,EAJAjI,EAAK8D,cAAc,CAAApG,GAAA,SACjBC,eAAe,wDAItBT,UAEDT,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,uEACZqd,gCAA8B,EAC9BxL,UAAW+K,EACXpG,QAASmG,EACTzc,KAAK,UACLC,QAASkc,EAASxc,SAEjB8C,EAAK8D,cAAc,CAAApG,GAAA,SAClBC,eAAe,qBAO1BT,SAAA,EAEDU,EAAAA,EAAAA,IAAA,QACE2Q,SAAUrC,EAAK2O,cA7EJtM,KAEf,GAAImK,IAAkBoB,EAAW5b,MAAM4c,OACrC,OAIF,MAAMC,EAAa,IAAItC,IAAIO,GAC3B+B,EAAWC,IAAIlB,EAAW7b,IAAK6b,GAE/Bb,EAAa8B,GACb7O,EAAKuN,OAAO,IAmER/c,KAAGC,EAAAA,EAAAA,IAAE,CAAEC,QAAS,OAAQ2U,WAAY,WAAYxU,IAAKR,EAAMS,QAAQ+T,IAAI,IAAC7T,SAAA,EAExEU,EAAAA,EAAAA,IAAA,OAAKlB,KAAGC,EAAAA,EAAAA,IAAE,CAAEkI,SAAU,EAAGjI,QAAS,OAAQG,IAAKR,EAAMS,QAAQ+T,GAAI3L,KAAM,GAAG,IAAClI,SAAA,EACzEU,EAAAA,EAAAA,IAAA,OAAKlB,IAAGkD,EAAc1C,SAAA,EACpBT,EAAAA,EAAAA,GAACwe,EAAAA,IAAOC,MAAK,CAACC,QAAQ,MAAKje,SACxB8C,EAAK8D,cAAc,CAAApG,GAAA,SAClBC,eAAe,WAInBlB,EAAAA,EAAAA,GAACia,EAAoB,CACnB1V,iBAAkBA,GAAoB,GACtC2V,QAASzK,EAAKyK,QACdC,oBA1GiB3Y,IAA6B,IAADmd,EACvD,MAAMrd,EAAME,EAAM+a,EAAUqC,IAAIpd,QAAOgK,EAIvCiE,EAAKoP,SAAS,QAAmB,QAAZF,EAAK,OAAHrd,QAAG,IAAHA,OAAG,EAAHA,EAAKG,aAAK,IAAAkd,EAAAA,EAAI,GAAG,QAwGlCxd,EAAAA,EAAAA,IAAA,OAAKlB,IAAG+D,EAAcvD,SAAA,EACpBT,EAAAA,EAAAA,GAACwe,EAAAA,IAAOC,MAAK,CAACC,QAAQ,QAAOje,SAC1Bwb,EACG1Y,EAAK8D,cAAc,CAAApG,GAAA,SACjBC,eAAe,UAGjBqC,EAAK8D,cAAc,CAAApG,GAAA,SACjBC,eAAe,wBAIvBlB,EAAAA,EAAAA,GAAC8e,EAAAA,IAAwBC,MAAK,CAC5Bne,YAAY,uEACZkB,KAAK,QACLoY,QAASzK,EAAKyK,QACd,aACE+B,EACI1Y,EAAK8D,cAAc,CAAApG,GAAA,SACjBC,eAAe,UAGjBqC,EAAK8D,cAAc,CAAApG,GAAA,SACjBC,eAAe,qBAIvBia,YAAa5X,EAAK8D,cAAc,CAAApG,GAAA,SAC9BC,eAAe,0BAMvBlB,EAAAA,EAAAA,GAAC8G,EAAAA,IAAa,CACZC,MAAOxD,EAAK8D,cAAc,CAAApG,GAAA,SACxBC,eAAe,YAEdT,UAEHT,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,uEACZoe,SAAS,SACT,aAAYzb,EAAK8D,cAAc,CAAApG,GAAA,SAC7BC,eAAe,YAEdT,UAEHT,EAAAA,EAAAA,GAACkL,EAAAA,IAAQ,WAIdiR,IAAgBnc,EAAAA,EAAAA,GAACwe,EAAAA,IAAOS,QAAO,CAACne,KAAK,QAAQkP,QAASmM,KACvDnc,EAAAA,EAAAA,GAAA,OACEC,KAAGC,EAAAA,EAAAA,IAAE,CACHC,QAAS,OACT+e,OAAQpf,EAAMS,QAAQC,GACtBJ,SAAU,OACV+e,UAAWrf,EAAMS,QAAQkU,IAC1B,IAAChU,SAEDoD,MAAMC,KAAKyY,EAAU7U,UAAUrG,KAAKC,IACnCtB,EAAAA,EAAAA,GAACuB,EAAAA,EAAW,CAAC6d,YAAU,EAAC9d,IAAKA,EAAK+d,QAASA,IAnK3Bxc,KAA8B,IAA7B,IAAErB,GAAqBqB,EAC9C2Z,GAAc8C,IACZA,EAAiBC,OAAO/d,GACjB,IAAIwa,IAAIsD,KACf,EA+JqDE,CAAgBle,IAAWA,EAAIE,YAMhE6C,oBAAmBkZ,YAAW,EACtD,IAAA1X,EAAA,CAAA/D,KAAA,SAAAC,OAAA,mBAEF,SAASmc,EAAwBvY,GAQ7B,IAR8B,UAChC4X,EAAS,WACTF,EAAU,WACVc,GAKDxY,EACC,MAAMpC,GAAOC,EAAAA,EAAAA,MACP,MAAE1D,IAAUC,EAAAA,EAAAA,KAIZ0f,EAAiB,GAFD,IAAGC,EAAAA,EAAAA,UAASrC,EAAW7b,IAAK,CAAEd,OAAQ,MAAS,QAC7C2c,EAAW5b,MAAQ,KAAIie,EAAAA,EAAAA,UAASrC,EAAW5b,MAAO,CAAEf,OAAQ,OAAU,KAGxFif,EAAYpc,EAAK8D,cACrB,CAAApG,GAAA,SACEC,eAAe,kEAGjB,CACEI,IAAKme,IAGT,OACEte,EAAAA,EAAAA,IAACye,EAAAA,GAAQC,KAAI,CAACjf,YAAY,uEAAsEH,SAAA,EAC9FT,EAAAA,EAAAA,GAAC4f,EAAAA,GAAQE,QAAO,CAACC,SAAO,EAAAtf,UACtBT,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,uEACZqd,gCAA8B,EAC9B7G,QAASmG,EACTzc,KAAK,UAASL,SAEb8C,EAAK8D,cAAc,CAAApG,GAAA,SAClBC,eAAe,mBAKrBC,EAAAA,EAAAA,IAACye,EAAAA,GAAQI,QAAO,CAACC,MAAM,MAAM,aAAYN,EAAUlf,SAAA,EACjDT,EAAAA,EAAAA,GAACiH,EAAAA,EAAWiZ,UAAS,CAACjgB,IAAG4F,EAAoBpF,SAAEkf,KAC/C3f,EAAAA,EAAAA,GAAC4f,EAAAA,GAAQO,MAAK,CAACJ,SAAO,EAAAtf,UACpBT,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,uEACZG,QAASod,EAAW1d,SAEnB8C,EAAK8D,cAAc,CAAApG,GAAA,SAClBC,eAAe,6BAKrBlB,EAAAA,EAAAA,GAAC4f,EAAAA,GAAQO,MAAK,CAACJ,SAAO,EAAAtf,UACpBT,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,uEACZE,KAAK,UACLb,KAAGC,EAAAA,EAAAA,IAAE,CAAE2U,WAAY/U,EAAMS,QAAQkU,IAAI,IAAChU,SAErC8C,EAAK8D,cAAc,CAAApG,GAAA,SAClBC,eAAe,gBAKrBlB,EAAAA,EAAAA,GAAC4f,EAAAA,GAAQQ,MAAK,SAItB,C,wPCjWA,MAAMC,EAAWA,IAAM,qCAEV9W,EAAmCA,CAAC+W,EAAaC,KAC5D,MAAMC,EAAYC,IAAAA,SAAWH,EAAK,CAChC5f,OAAQ6f,IAEV,OAAOE,IAAAA,UAAYD,GAAYE,GAAkB,OAATA,IAAeC,KAAK,GAAG,EAUpDC,EAAoBA,CAACN,EAAaO,KAC7C,GAAIP,EAAI5f,OAASmgB,EAAQ,CACvB,MAAMC,EAAeC,KAAKC,OAAOH,EAAS,GAAK,GACzCI,EAAcJ,EAAS,EAAIC,EACjC,OAAOR,EAAIY,UAAU,EAAGJ,GAAgB,MAAQR,EAAIY,UAAUZ,EAAI5f,OAASugB,EAAaX,EAAI5f,OAC9F,CACE,OAAO4f,CACT,EAOIa,EAAU,oEASHC,EAAY5F,IACvB,IAAI6F,EAAS,GACTC,EAAI,EAER,MAAM3e,EAAS4e,EAAa/F,GAE5B,KAAO8F,EAAI3e,EAAOjC,QAAQ,CACxB,MAAM8gB,EAAO7e,EAAO8e,WAAWH,KACzBI,EAAO/e,EAAO8e,WAAWH,KACzBK,EAAOhf,EAAO8e,WAAWH,KAEzBM,EAAOJ,GAAQ,EACfK,GAAgB,EAAPL,IAAa,EAAME,GAAQ,EAC1C,IAAII,GAAgB,GAAPJ,IAAc,EAAMC,GAAQ,EACrCI,EAAc,GAAPJ,EAEPK,MAAMN,IACRK,EAAO,GACPD,EAAOC,GACEC,MAAML,KACfI,EAAO,IAGTV,EAASA,EAASF,EAAQc,OAAOL,GAAQT,EAAQc,OAAOJ,GAAQV,EAAQc,OAAOH,GAAQX,EAAQc,OAAOF,EACxG,CAEA,OAAOV,CAAM,EASFa,EAAY1G,IACvB,IAAI6F,EAAS,GACTC,EAAI,EAER,MAAM3e,GAAc,OAAL6Y,QAAK,IAALA,OAAK,EAALA,EAAO2G,QAAQ,mBAAoB,MAAO,GAEzD,KAAOb,EAAI3e,EAAOjC,QAAQ,CACxB,MAAMkhB,EAAOT,EAAQiB,QAAQzf,EAAOsf,OAAOX,MACrCO,EAAOV,EAAQiB,QAAQzf,EAAOsf,OAAOX,MACrCQ,EAAOX,EAAQiB,QAAQzf,EAAOsf,OAAOX,MACrCS,EAAOZ,EAAQiB,QAAQzf,EAAOsf,OAAOX,MAErCE,EAAQI,GAAQ,EAAMC,GAAQ,EAC9BH,GAAgB,GAAPG,IAAc,EAAMC,GAAQ,EACrCH,GAAgB,EAAPG,IAAa,EAAKC,EAEjCV,GAAUpZ,OAAOoa,aAAab,GAEjB,KAATM,IACFT,GAAUpZ,OAAOoa,aAAaX,IAGnB,KAATK,IACFV,GAAUpZ,OAAOoa,aAAaV,GAElC,CAEA,OAAOW,EAAajB,EAAO,EASvBE,EAAe,WACnB,MAAM5e,GADoB4S,UAAA7U,OAAA,QAAA8K,IAAA+J,UAAA,GAAAA,UAAA,GAAG,IACP4M,QAAQ,QAAS,MACvC,IAAII,EAAU,GAEd,IAAK,IAAIC,EAAI,EAAGA,EAAI7f,EAAOjC,OAAQ8hB,IAAK,CACtC,MAAMC,EAAI9f,EAAO8e,WAAWe,GAG1BD,GADEE,EAAI,IACKxa,OAAOoa,aAAaI,GACtBA,EAAI,KAAOA,EAAI,KACbxa,OAAOoa,aAAcI,GAAK,EAAK,KAAOxa,OAAOoa,aAAkB,GAAJI,EAAU,KAG9Exa,OAAOoa,aAAcI,GAAK,GAAM,KAChCxa,OAAOoa,aAAeI,GAAK,EAAK,GAAM,KACtCxa,OAAOoa,aAAkB,GAAJI,EAAU,IAErC,CAEA,OAAOF,CACT,EAQMD,EAAe,WAAmB,IAAlBC,EAAOhN,UAAA7U,OAAA,QAAA8K,IAAA+J,UAAA,GAAAA,UAAA,GAAG,GAC1BmN,EAAS,GACTpB,EAAI,EAER,KAAOA,EAAIiB,EAAQ7hB,QAAQ,CACzB,MAAM+hB,EAAIF,EAAQd,WAAWH,GAE7B,GAAImB,EAAI,IACNC,GAAUza,OAAOoa,aAAaI,GAC9BnB,SACK,GAAImB,EAAI,KAAOA,EAAI,IAAK,CAC7B,MAAME,EAAKJ,EAAQd,WAAWH,EAAI,GAClCoB,GAAUza,OAAOoa,cAAmB,GAAJI,IAAW,EAAW,GAALE,GACjDrB,GAAK,CACP,KAAO,CACL,MAAMqB,EAAKJ,EAAQd,WAAWH,EAAI,GAC5BsB,EAAKL,EAAQd,WAAWH,EAAI,GAClCoB,GAAUza,OAAOoa,cAAmB,GAAJI,IAAW,IAAa,GAALE,IAAY,EAAW,GAALC,GACrEtB,GAAK,CACP,CACF,CACA,OAAOoB,CACT,EAMaG,EAAmBrH,GACvBsH,OAAOC,OAAOC,OAAO,WAAW,IAAIC,aAAcC,OAAO1H,IAAQ7M,MAAMwU,GACrEtf,MAAMuf,UAAU/hB,IAAIgiB,KAAK,IAAIC,WAAWH,IAAeI,IAAO,KAAOA,EAAEC,SAAS,KAAKC,OAAO,KAAI9C,KAAK,MAI1G+C,EAAiC,WAE1BC,EAAsBlf,UACjC,MACMmf,SADavD,KACKwD,QAAQC,GAGhC,GAAsB,qBAAXC,OAAwB,CACjC,MAAMC,EAAaD,OAAOjgB,KAAK8f,GAAYJ,SAAS,UACpD,MAAO,GAAGE,IAAiCM,GAC7C,CAGA,MAAMC,EAAepgB,MAAMC,KAAK8f,GAAaM,GAASjc,OAAOkc,cAAcD,KAAOvD,KAAK,IACvF,MAAO,GAAG+C,IAAiCU,KAAKH,IAAe,EAGpDI,EAAwB5f,UACnC,MAAM6f,QAAajE,IACnB,IAAKkE,EAAeC,WAAWd,GAC7B,MAAM,IAAIe,MAAM,mDAElB,MAAMC,EAA8BH,EAAed,MAAMC,GAGzD,GAAsB,qBAAXK,OAAwB,CACjC,MAAME,EAAeF,OAAOjgB,KAAK4gB,EAA6B,UAC9D,OAAOJ,EAAKK,QAIVV,EACA,CAAEnc,GAAI,UAEV,CAGA,MAAMmc,EAAeW,KAAKF,GAC1B,OAAOJ,EAAKK,QACVrB,WAAWxf,KAAKmgB,GAAeY,IAAC,IAAAC,EAAA,OAAqB,QAArBA,EAAKD,EAAEE,YAAY,UAAE,IAAAD,EAAAA,EAAI,CAAC,IAC1D,CAAEhd,GAAI,UACP,EAGUkd,EAA2BlB,GAAiBA,EAAKU,WAAWd,E,mHCxNW,IAAA7gB,EAAA,CAAAf,KAAA,UAAAC,OAAA,aAQ7E,MAAMkjB,EAAatlB,IAAmF,IAAlF,SAAEulB,EAAQ,UAAEC,GAAY,EAAI,YAAEvkB,KAAgBwkB,GAA8BzlB,EACrG,MAAO0lB,EAAaC,IAAkBlgB,EAAAA,EAAAA,WAAS,GAc/C,OACEpF,EAAAA,EAAAA,GAAC8G,EAAAA,IAAa,CACZC,OACE/G,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,WAEnC8Z,wBAAyB,CACvBlI,QAASuS,GACT5kB,UAEFT,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAwB,OAAXA,QAAW,IAAXA,EAAAA,EAAe,4BAC5BE,KAAK,UACLC,QAxBcwkB,KAClBC,UAAUC,UAAUC,UAAUR,GAC9BI,GAAe,GACfK,YAAW,KACTL,GAAe,EAAM,GACpB,IAAK,EAoBJM,aAjBmBC,KACvBP,GAAe,EAAM,EAiBjBrlB,IAAG4C,EAEHpC,SACE0kB,GAAYnlB,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,cAAsDsK,KAEjG4Z,KAEQ,C,8HClCb,MAAMnc,EAA+BtJ,IAIF,IAJG,QAC3C6C,EAAU,GAAE,UACZ3C,EAAS,UACTyH,GACkC3H,EAClC,MAAM,MAAEG,IAAUC,EAAAA,EAAAA,KAElB,OACEC,EAAAA,EAAAA,GAAA,OACEC,KAAGC,EAAAA,EAAAA,IAAE,CACH4lB,SAAU,IACV3lB,QAAS,OACTC,SAAU,OACV0U,WAAY,aACZ,MAAO,CACLzU,YAAa,gBAEf6e,OAAQpf,EAAMS,QAAQC,GAAK,EAC3BulB,UAAWjmB,EAAMS,QAAQC,IAC1B,IACD8G,UAAWA,EAAU7G,SAEpB+B,EAAQ9B,OAAS,GAChBV,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,+FACZC,KAAK,QACLC,KAAK,OACLC,QAASlB,EAAUY,UAEnBT,EAAAA,EAAAA,GAACgB,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAKnBC,EAAAA,EAAAA,IAAAC,EAAAA,GAAA,CAAAX,SAAA,CACG+B,EAAQnB,KAAKyB,IACZ9C,EAAAA,EAAAA,GAACgmB,EAAAA,EAAoB,CAACvkB,MAAOqB,EAAmB7C,KAAGC,EAAAA,EAAAA,IAAE,CAAEif,UAAWrf,EAAMS,QAAQC,GAAK,GAAG,KAA/CsC,MAE3C9C,EAAAA,EAAAA,GAACW,EAAAA,EAAM,CACLC,YAAY,+FACZC,KAAK,QACLa,MAAM1B,EAAAA,EAAAA,GAAC2B,EAAAA,IAAU,IACjBZ,QAASlB,QAIX,C,oJCzDV,MAAM,UAAEqgB,GAAcjZ,EAAAA,EAC4D,IAAAtH,EAAA,CAAAmC,KAAA,QAAAC,OAAA,gBAAAc,EAAA,CAAAf,KAAA,UAAAC,OAAA,eAS3E,MAAMkkB,EAA2BzY,EAAAA,MAAYE,IAClD,MAAM,MAAE5N,IAAUC,EAAAA,EAAAA,KAElB,OACEC,EAAAA,EAAAA,GAAC6d,EAAAA,EAAK,CACJjd,YAAY,2EACZmG,MAAO,QAAU2G,EAAMoM,OACvBhH,QAASpF,EAAMwY,kCACfnU,SAAUA,IAAMrE,EAAMyY,sCAAqC,GAAO1lB,UAElEU,EAAAA,EAAAA,IAAA,OAAKlB,IAAGN,EAAsBc,SAAA,EAC5BT,EAAAA,EAAAA,GAACkgB,EAAS,CAACjgB,IAAG4C,EAAkBpC,UAC9BT,EAAAA,EAAAA,GAAA,OACEC,KAAGC,EAAAA,EAAAA,IAAE,CACHkmB,gBAAiBtmB,EAAM4Z,OAAO2M,kBAC9BlH,UAAWrf,EAAMS,QAAQkU,GACzB6R,WAAY,WACZC,UAAW,aACZ,IAAC9lB,SAEDiN,EAAM8Y,cAGXxmB,EAAAA,EAAAA,GAAA,OACEC,KAAGC,EAAAA,EAAAA,IAAE,CACHif,UAAWrf,EAAMS,QAAQkU,IAC1B,IAAChU,UAEFT,EAAAA,EAAAA,GAACilB,EAAAA,EAAU,CAACC,SAAUxX,EAAM8Y,SAAUrB,WAAW,EAAOzjB,MAAM1B,EAAAA,EAAAA,GAACymB,EAAAA,IAAQ,IAAK,aAAW,eAGrF,IC/BNC,EAA2B,GAEjC,SAASC,IACP,QADwCpR,UAAA7U,OAAA,QAAA8K,IAAA+J,UAAA,KAAAA,UAAA,GAEpC,CACEqR,SAAU,SACVC,aAAc,WACdC,SAAU,SACVR,WAAY,UAEd,CAAEA,WAAY,SACpB,CAKO,MAAM/kB,EAAc5B,IAgBpB,IAhBqB,WAC1Byf,GAAa,EAAK,QAClBC,EAAO,IACP/d,EAAG,oBACHylB,GAAsB,EAAK,UAC3BC,EAAYN,EAAwB,SACpCZ,EAAW,IAAG,UACdxe,GASD3H,EACC,MAAM4D,GAAOC,EAAAA,EAAAA,MAEN0iB,EAAmCC,IAAwC/gB,EAAAA,EAAAA,WAAS,IAErF,kBAAE6hB,EAAiB,oBAAEC,GA+CtB,SACL5lB,GAE+D,IAD/D0lB,EAASzR,UAAA7U,OAAA,QAAA8K,IAAA+J,UAAA,GAAAA,UAAA,GAAGmR,EAEZ,MAAM,IAAEllB,EAAG,MAAEC,GAAUH,EACjB6lB,EAAa3lB,EAAId,OAASe,EAAMf,OAChC0mB,EAAc5lB,EAAId,OAASe,EAAMf,OACjC2mB,EAAgBD,EAAc3lB,EAAMf,OAASc,EAAId,OAGvD,OAAIymB,GAAcH,EAAkB,CAAEC,mBAAmB,EAAOC,qBAAqB,GAEjFG,EAAgBL,EAAY,EAAU,CAAEC,mBAAmB,EAAMC,qBAAqB,GAGnF,CACLD,kBAAmBG,EACnBF,qBAAsBE,EAE1B,CAlEqDE,CAAgChmB,EAAK0lB,GAClFO,EAAqBR,IAAwBE,GAAqBC,GAElEM,EAAqBjkB,EAAK8D,cAAc,CAAApG,GAAA,SAC5CC,eAAe,sBAIjB,OACEC,EAAAA,EAAAA,IAAA,OAAAV,SAAA,EACET,EAAAA,EAAAA,GAACynB,EAAAA,IAAG,CACF7mB,YAAY,8DACZ8mB,SAAUtI,EACVC,QAASA,EACTtY,MAAOzF,EAAIE,IACX8F,UAAWA,EAAU7G,UAErBT,EAAAA,EAAAA,GAAC8G,EAAAA,IAAa,CAACC,MAAOwgB,EAAqBC,EAAqB,GAAG/mB,UACjEU,EAAAA,EAAAA,IAAA,QACElB,KAAGC,EAAAA,EAAAA,IAAE,CAAE4lB,WAAU3lB,QAAS,eAAe,IACzCY,QAASA,IAAOwmB,EAAqBpB,GAAqC,QAAQ3a,EAAW/K,SAAA,EAE7FT,EAAAA,EAAAA,GAACiH,EAAAA,EAAWC,KAAI,CAACygB,MAAI,EAAC5gB,MAAOzF,EAAIE,IAAKvB,IAAK0mB,EAAmBM,GAAmBxmB,SAC9Ea,EAAIE,MAENF,EAAIG,QACHN,EAAAA,EAAAA,IAAC8F,EAAAA,EAAWC,KAAI,CAACH,MAAOzF,EAAIG,MAAOxB,IAAK0mB,EAAmBO,GAAqBzmB,SAAA,CAAC,KAC5Ea,EAAIG,iBAMjBzB,EAAAA,EAAAA,GAAA,OAAAS,SACGylB,IACClmB,EAAAA,EAAAA,GAACimB,EAAwB,CACvBnM,OAAQxY,EAAIE,IACZglB,SAAUllB,EAAIG,MACdykB,kCAAmCA,EACnCC,qCAAsCA,QAIxC,C", "sources": ["common/components/KeyValueTagsEditorCell.tsx", "model-registry/components/ModelVersionTable.tsx", "model-registry/components/ModelView.tsx", "model-registry/components/ModelPage.tsx", "common/components/TagSelectDropdown.tsx", "common/hooks/useEditKeyValueTagsModal.tsx", "common/utils/StringUtils.ts", "shared/building_blocks/CopyButton.tsx", "model-registry/components/aliases/ModelVersionTableAliasesCell.tsx", "common/components/KeyValueTagFullViewModal.tsx", "common/components/KeyValueTag.tsx"], "sourcesContent": ["import { Button, PencilIcon, useDesignSystemTheme } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport { KeyValueEntity } from '../../experiment-tracking/types';\nimport { KeyValueTag } from './KeyValueTag';\n\ninterface KeyValueTagsEditorCellProps {\n  tags?: KeyValueEntity[];\n  onAddEdit: () => void;\n}\n\n/**\n * A cell renderer used in tables, displaying a list of key-value tags with button for editing those\n */\nexport const KeyValueTagsEditorCell = ({ tags = [], onAddEdit }: KeyValueTagsEditorCellProps) => {\n  const { theme } = useDesignSystemTheme();\n\n  return (\n    <div\n      css={{\n        display: 'flex',\n        flexWrap: 'wrap',\n        '> *': {\n          marginRight: '0 !important',\n        },\n        gap: theme.spacing.xs,\n      }}\n    >\n      {tags.length < 1 ? (\n        <Button\n          componentId=\"codegen_mlflow_app_src_common_components_keyvaluetagseditorcell.tsx_29\"\n          size=\"small\"\n          type=\"link\"\n          onClick={onAddEdit}\n        >\n          <FormattedMessage defaultMessage=\"Add\" description=\"Key-value tag table cell > 'add' button label\" />\n        </Button>\n      ) : (\n        <>\n          {tags.map((tag) => (\n            <KeyValueTag tag={tag} key={`${tag.key}-${tag.value}`} />\n          ))}\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_components_keyvaluetagseditorcell.tsx_37\"\n            size=\"small\"\n            icon={<PencilIcon />}\n            onClick={onAddEdit}\n          />\n        </>\n      )}\n    </div>\n  );\n};\n", "import {\n  Empty,\n  NotificationIcon,\n  Pagination,\n  PlusIcon,\n  Table,\n  TableCell,\n  TableHeader,\n  TableRow,\n  TableRowSelectCell,\n  LegacyTooltip,\n  Typography,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport {\n  ColumnDef,\n  PaginationState,\n  RowSelectionState,\n  SortingState,\n  flexRender,\n  getCoreRowModel,\n  getPaginationRowModel,\n  getSortedRowModel,\n  useReactTable,\n} from '@tanstack/react-table';\nimport { KeyValueEntity, ModelEntity, ModelVersionInfoEntity, ModelAliasMap } from '../../experiment-tracking/types';\nimport { useEffect, useMemo, useState } from 'react';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { RegisteringModelDocUrl } from '../../common/constants';\nimport {\n  ACTIVE_STAGES,\n  EMPTY_CELL_PLACEHOLDER,\n  ModelVersionStatusIcons,\n  StageTagComponents,\n  modelVersionStatusIconTooltips,\n} from '../constants';\nimport { Link } from '../../common/utils/RoutingUtils';\nimport { ModelRegistryRoutes } from '../routes';\nimport Utils from '../../common/utils/Utils';\nimport { KeyValueTagsEditorCell } from '../../common/components/KeyValueTagsEditorCell';\nimport { useDispatch } from 'react-redux';\nimport { ThunkDispatch } from '../../redux-types';\nimport { useEditKeyValueTagsModal } from '../../common/hooks/useEditKeyValueTagsModal';\nimport { useEditRegisteredModelAliasesModal } from '../hooks/useEditRegisteredModelAliasesModal';\nimport { updateModelVersionTagsApi } from '../actions';\nimport { ModelVersionTableAliasesCell } from './aliases/ModelVersionTableAliasesCell';\nimport { Interpolation, Theme } from '@emotion/react';\nimport { truncateToFirstLineWithMaxLength } from '../../common/utils/StringUtils';\nimport ExpandableList from '../../common/components/ExpandableList';\n\ntype ModelVersionTableProps = {\n  modelName: string;\n  modelVersions?: ModelVersionInfoEntity[];\n  activeStageOnly?: boolean;\n  onChange: (selectedRowKeys: string[], selectedRows: ModelVersionInfoEntity[]) => void;\n  modelEntity?: ModelEntity;\n  onMetadataUpdated: () => void;\n  usingNextModelsUI: boolean;\n  aliases?: ModelAliasMap;\n};\n\ntype ModelVersionColumnDef = ColumnDef<ModelVersionInfoEntity> & {\n  meta?: { styles?: Interpolation<Theme>; multiline?: boolean; className?: string };\n};\n\nenum COLUMN_IDS {\n  STATUS = 'STATUS',\n  VERSION = 'VERSION',\n  CREATION_TIMESTAMP = 'CREATION_TIMESTAMP',\n  USER_ID = 'USER_ID',\n  TAGS = 'TAGS',\n  STAGE = 'STAGE',\n  DESCRIPTION = 'DESCRIPTION',\n  ALIASES = 'ALIASES',\n}\n\nexport const ModelVersionTable = ({\n  modelName,\n  modelVersions,\n  activeStageOnly,\n  onChange,\n  modelEntity,\n  onMetadataUpdated,\n  usingNextModelsUI,\n  aliases,\n}: ModelVersionTableProps) => {\n  const aliasesByVersion = useMemo(() => {\n    const result: Record<string, string[]> = {};\n    aliases?.forEach(({ alias, version }) => {\n      if (!result[version]) {\n        result[version] = [];\n      }\n      result[version].push(alias);\n    });\n    return result;\n  }, [aliases]);\n  const versions = useMemo(\n    () =>\n      activeStageOnly\n        ? (modelVersions || []).filter(({ current_stage }) => ACTIVE_STAGES.includes(current_stage))\n        : modelVersions,\n    [activeStageOnly, modelVersions],\n  );\n\n  const { theme } = useDesignSystemTheme();\n  const intl = useIntl();\n\n  const allTagsKeys = useMemo(() => {\n    const allTagsList: KeyValueEntity[] = versions?.map((modelVersion) => modelVersion?.tags || []).flat() || [];\n\n    // Extract keys, remove duplicates and sort the\n    return Array.from(new Set(allTagsList.map(({ key }) => key))).sort();\n  }, [versions]);\n\n  const dispatch = useDispatch<ThunkDispatch>();\n\n  const { EditTagsModal, showEditTagsModal } = useEditKeyValueTagsModal<ModelVersionInfoEntity>({\n    allAvailableTags: allTagsKeys,\n    saveTagsHandler: async (modelVersion, existingTags, newTags) =>\n      dispatch(updateModelVersionTagsApi(modelVersion, existingTags, newTags)),\n    onSuccess: onMetadataUpdated,\n  });\n\n  const { EditAliasesModal, showEditAliasesModal } = useEditRegisteredModelAliasesModal({\n    model: modelEntity || null,\n    onSuccess: onMetadataUpdated,\n  });\n\n  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});\n\n  const [pagination, setPagination] = useState<PaginationState>({\n    pageSize: 10,\n    pageIndex: 0,\n  });\n\n  useEffect(() => {\n    const selectedVersions = (versions || []).filter(({ version }) => rowSelection[version]);\n    const selectedVersionNumbers = selectedVersions.map(({ version }) => version);\n    onChange(selectedVersionNumbers, selectedVersions);\n  }, [rowSelection, onChange, versions]);\n\n  const tableColumns = useMemo(() => {\n    const columns: ModelVersionColumnDef[] = [\n      {\n        id: COLUMN_IDS.STATUS,\n        enableSorting: false,\n        header: '', // Status column does not have title\n        meta: { styles: { flexBasis: theme.general.heightSm, flexGrow: 0 } },\n        cell: ({ row: { original } }) => {\n          const { status, status_message } = original || {};\n          return (\n            <LegacyTooltip title={status_message || modelVersionStatusIconTooltips[status]}>\n              <Typography.Text>{ModelVersionStatusIcons[status]}</Typography.Text>\n            </LegacyTooltip>\n          );\n        },\n      },\n    ];\n    columns.push(\n      {\n        id: COLUMN_IDS.VERSION,\n        enableSorting: false,\n        header: intl.formatMessage({\n          defaultMessage: 'Version',\n          description: 'Column title text for model version in model version table',\n        }),\n        meta: { className: 'model-version' },\n        accessorKey: 'version',\n        cell: ({ getValue }) => (\n          <FormattedMessage\n            defaultMessage=\"<link>Version {versionNumber}</link>\"\n            description=\"Link to model version in the model version table\"\n            values={{\n              link: (chunks) => (\n                <Link to={ModelRegistryRoutes.getModelVersionPageRoute(modelName, String(getValue()))}>{chunks}</Link>\n              ),\n              versionNumber: getValue(),\n            }}\n          />\n        ),\n      },\n      {\n        id: COLUMN_IDS.CREATION_TIMESTAMP,\n        enableSorting: true,\n        meta: { styles: { minWidth: 200 } },\n        header: intl.formatMessage({\n          defaultMessage: 'Registered at',\n          description: 'Column title text for created at timestamp in model version table',\n        }),\n        accessorKey: 'creation_timestamp',\n        cell: ({ getValue }) => Utils.formatTimestamp(getValue(), intl),\n      },\n\n      {\n        id: COLUMN_IDS.USER_ID,\n        enableSorting: false,\n        meta: { styles: { minWidth: 100 } },\n        header: intl.formatMessage({\n          defaultMessage: 'Created by',\n          description: 'Column title text for creator username in model version table',\n        }),\n        accessorKey: 'user_id',\n        cell: ({ getValue }) => <span>{getValue()}</span>,\n      },\n    );\n\n    if (usingNextModelsUI) {\n      // Display tags and aliases columns only when \"new models UI\" is flipped\n      columns.push(\n        {\n          id: COLUMN_IDS.TAGS,\n          enableSorting: false,\n          header: intl.formatMessage({\n            defaultMessage: 'Tags',\n            description: 'Column title text for model version tags in model version table',\n          }),\n          meta: { styles: { flex: 2 } },\n          accessorKey: 'tags',\n          cell: ({ getValue, row: { original } }) => {\n            return (\n              <KeyValueTagsEditorCell\n                tags={getValue() as KeyValueEntity[]}\n                onAddEdit={() => {\n                  showEditTagsModal?.(original);\n                }}\n              />\n            );\n          },\n        },\n        {\n          id: COLUMN_IDS.ALIASES,\n          accessorKey: 'aliases',\n          enableSorting: false,\n          header: intl.formatMessage({\n            defaultMessage: 'Aliases',\n            description: 'Column title text for model version aliases in model version table',\n          }),\n          meta: { styles: { flex: 2 }, multiline: true },\n          cell: ({ getValue, row: { original } }) => {\n            const mvAliases = aliasesByVersion[original.version] || [];\n            return (\n              <ModelVersionTableAliasesCell\n                modelName={modelName}\n                version={original.version}\n                aliases={mvAliases}\n                onAddEdit={() => {\n                  showEditAliasesModal?.(original.version);\n                }}\n              />\n            );\n          },\n        },\n      );\n    } else {\n      // If not, display legacy \"Stage\" columns\n      columns.push({\n        id: COLUMN_IDS.STAGE,\n        enableSorting: false,\n        header: intl.formatMessage({\n          defaultMessage: 'Stage',\n          description: 'Column title text for model version stage in model version table',\n        }),\n        accessorKey: 'current_stage',\n        cell: ({ getValue }) => {\n          return StageTagComponents[getValue() as string];\n        },\n      });\n    }\n    columns.push({\n      id: COLUMN_IDS.DESCRIPTION,\n      enableSorting: false,\n      header: intl.formatMessage({\n        defaultMessage: 'Description',\n        description: 'Column title text for description in model version table',\n      }),\n      meta: { styles: { flex: 2 } },\n      accessorKey: 'description',\n      cell: ({ getValue }) => truncateToFirstLineWithMaxLength(getValue() as string, 32),\n    });\n    return columns;\n  }, [theme, intl, modelName, showEditTagsModal, showEditAliasesModal, usingNextModelsUI, aliasesByVersion]);\n\n  const [sorting, setSorting] = useState<SortingState>([{ id: COLUMN_IDS.CREATION_TIMESTAMP, desc: true }]);\n\n  const table = useReactTable<ModelVersionInfoEntity>({\n    data: versions || [],\n    columns: tableColumns,\n    state: {\n      pagination,\n      rowSelection,\n      sorting,\n    },\n    getCoreRowModel: getCoreRowModel(),\n    getSortedRowModel: getSortedRowModel(),\n    getPaginationRowModel: getPaginationRowModel(),\n    getRowId: ({ version }) => version,\n    onRowSelectionChange: setRowSelection,\n    onSortingChange: setSorting,\n  });\n\n  const isEmpty = () => table.getRowModel().rows.length === 0;\n\n  const getLearnMoreLinkUrl = () => {\n    return RegisteringModelDocUrl;\n  };\n\n  const paginationComponent = (\n    <Pagination\n      componentId=\"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_403\"\n      currentPageIndex={pagination.pageIndex + 1}\n      numTotal={(versions || []).length}\n      onChange={(page, pageSize) => {\n        setPagination({\n          pageSize: pageSize || pagination.pageSize,\n          pageIndex: page - 1,\n        });\n      }}\n      pageSize={pagination.pageSize}\n    />\n  );\n\n  const emptyComponent = (\n    <Empty\n      description={\n        <FormattedMessage\n          defaultMessage=\"No models versions are registered yet. <link>Learn more</link> about how to\n          register a model version.\"\n          description=\"Message text when no model versions are registered\"\n          values={{\n            link: (chunks) => (\n              <Typography.Link\n                componentId=\"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_425\"\n                target=\"_blank\"\n                href={getLearnMoreLinkUrl()}\n              >\n                {chunks}\n              </Typography.Link>\n            ),\n          }}\n        />\n      }\n      image={<PlusIcon />}\n    />\n  );\n\n  return (\n    <>\n      <Table\n        data-testid=\"model-list-table\"\n        pagination={paginationComponent}\n        scrollable\n        empty={isEmpty() ? emptyComponent : undefined}\n        someRowsSelected={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}\n      >\n        <TableRow isHeader>\n          <TableRowSelectCell\n            componentId=\"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_450\"\n            checked={table.getIsAllRowsSelected()}\n            indeterminate={table.getIsSomeRowsSelected()}\n            onChange={table.getToggleAllRowsSelectedHandler()}\n          />\n          {table.getLeafHeaders().map((header) => (\n            <TableHeader\n              componentId=\"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_458\"\n              multiline={false}\n              key={header.id}\n              sortable={header.column.getCanSort()}\n              sortDirection={header.column.getIsSorted() || 'none'}\n              onToggleSort={header.column.getToggleSortingHandler()}\n              css={(header.column.columnDef as ModelVersionColumnDef).meta?.styles}\n            >\n              {flexRender(header.column.columnDef.header, header.getContext())}\n            </TableHeader>\n          ))}\n        </TableRow>\n        {table.getRowModel().rows.map((row) => (\n          <TableRow\n            key={row.id}\n            css={{\n              '.table-row-select-cell': {\n                alignItems: 'flex-start',\n              },\n            }}\n          >\n            <TableRowSelectCell\n              componentId=\"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_477\"\n              checked={row.getIsSelected()}\n              onChange={row.getToggleSelectedHandler()}\n            />\n            {row.getAllCells().map((cell) => (\n              <TableCell\n                className={(cell.column.columnDef as ModelVersionColumnDef).meta?.className}\n                multiline={(cell.column.columnDef as ModelVersionColumnDef).meta?.multiline}\n                key={cell.id}\n                css={(cell.column.columnDef as ModelVersionColumnDef).meta?.styles}\n              >\n                {flexRender(cell.column.columnDef.cell, cell.getContext())}\n              </TableCell>\n            ))}\n          </TableRow>\n        ))}\n      </Table>\n      {EditTagsModal}\n      {EditAliasesModal}\n    </>\n  );\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { ModelVersionTable } from './ModelVersionTable';\nimport Utils from '../../common/utils/Utils';\nimport { Link, NavigateFunction } from '../../common/utils/RoutingUtils';\nimport { ModelRegistryRoutes } from '../routes';\nimport { ACTIVE_STAGES } from '../constants';\nimport { CollapsibleSection } from '../../common/components/CollapsibleSection';\nimport { EditableNote } from '../../common/components/EditableNote';\nimport { EditableTagsTableView } from '../../common/components/EditableTagsTableView';\nimport { getRegisteredModelTags } from '../reducers';\nimport { setRegisteredModelTagApi, deleteRegisteredModelTagApi } from '../actions';\nimport { connect } from 'react-redux';\nimport { OverflowMenu, PageHeader } from '../../shared/building_blocks/PageHeader';\nimport { FormattedMessage, type IntlShape, injectIntl } from 'react-intl';\nimport { Button, SegmentedControlGroup, SegmentedControlButton, DangerModal } from '@databricks/design-system';\nimport { Descriptions } from '../../common/components/Descriptions';\nimport { ModelVersionInfoEntity, type ModelEntity } from '../../experiment-tracking/types';\nimport { shouldShowModelsNextUI } from '../../common/utils/FeatureUtils';\nimport { ModelsNextUIToggleSwitch } from './ModelsNextUIToggleSwitch';\nimport { withNextModelsUIContext } from '../hooks/useNextModelsUI';\nimport { ErrorWrapper } from '../../common/utils/ErrorWrapper';\n\nexport const StageFilters = {\n  ALL: 'ALL',\n  ACTIVE: 'ACTIVE',\n};\n\ntype ModelViewImplProps = {\n  model?: ModelEntity;\n  modelVersions?: ModelVersionInfoEntity[];\n  handleEditDescription: (...args: any[]) => any;\n  handleDelete: (...args: any[]) => any;\n  navigate: NavigateFunction;\n  showEditPermissionModal: (...args: any[]) => any;\n  activePane?: any; // TODO: PropTypes.oneOf(Object.values(PANES))\n  emailSubscriptionStatus?: string;\n  userLevelEmailSubscriptionStatus?: string;\n  handleEmailNotificationPreferenceChange?: (...args: any[]) => any;\n  tags: any;\n  setRegisteredModelTagApi: (...args: any[]) => any;\n  deleteRegisteredModelTagApi: (...args: any[]) => any;\n  intl: IntlShape;\n  onMetadataUpdated: () => void;\n  usingNextModelsUI: boolean;\n};\n\ntype ModelViewImplState = any;\n\nexport class ModelViewImpl extends React.Component<ModelViewImplProps, ModelViewImplState> {\n  constructor(props: ModelViewImplProps) {\n    super(props);\n    this.onCompare = this.onCompare.bind(this);\n  }\n\n  state = {\n    stageFilter: StageFilters.ALL,\n    showDescriptionEditor: false,\n    isDeleteModalVisible: false,\n    isDeleteModalConfirmLoading: false,\n    runsSelected: {},\n    isTagsRequestPending: false,\n    updatingEmailPreferences: false,\n  };\n\n  formRef = React.createRef();\n\n  componentDidMount() {\n    // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n    const pageTitle = `${this.props.model.name} - MLflow Model`;\n    Utils.updatePageTitle(pageTitle);\n  }\n\n  handleStageFilterChange = (e: any) => {\n    this.setState({ stageFilter: e.target.value });\n  };\n\n  getActiveVersionsCount() {\n    const { modelVersions } = this.props;\n    return modelVersions ? modelVersions.filter((v) => ACTIVE_STAGES.includes(v.current_stage)).length : 0;\n  }\n\n  handleCancelEditDescription = () => {\n    this.setState({ showDescriptionEditor: false });\n  };\n\n  handleSubmitEditDescription = (description: any) => {\n    return this.props.handleEditDescription(description).then(() => {\n      this.setState({ showDescriptionEditor: false });\n    });\n  };\n\n  startEditingDescription = (e: any) => {\n    e.stopPropagation();\n    this.setState({ showDescriptionEditor: true });\n  };\n\n  getOverflowMenuItems() {\n    const menuItems = [\n      {\n        id: 'delete',\n        itemName: (\n          <FormattedMessage\n            defaultMessage=\"Delete\"\n            // eslint-disable-next-line max-len\n            description=\"Text for disabled delete button due to active versions on model view page header\"\n          />\n        ),\n        onClick: this.showDeleteModal,\n        disabled: this.getActiveVersionsCount() > 0,\n      },\n    ];\n\n    return menuItems;\n  }\n\n  showDeleteModal = () => {\n    this.setState({ isDeleteModalVisible: true });\n  };\n\n  hideDeleteModal = () => {\n    this.setState({ isDeleteModalVisible: false });\n  };\n\n  showConfirmLoading = () => {\n    this.setState({ isDeleteModalConfirmLoading: true });\n  };\n\n  hideConfirmLoading = () => {\n    this.setState({ isDeleteModalConfirmLoading: false });\n  };\n\n  handleDeleteConfirm = () => {\n    const { navigate } = this.props;\n    this.showConfirmLoading();\n    this.props\n      .handleDelete()\n      .then(() => {\n        navigate(ModelRegistryRoutes.modelListPageRoute);\n      })\n      .catch((e: any) => {\n        this.hideConfirmLoading();\n        Utils.logErrorAndNotifyUser(e);\n      });\n  };\n\n  handleAddTag = (values: any) => {\n    const form = this.formRef.current;\n    const { model } = this.props;\n    // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n    const modelName = model.name;\n    this.setState({ isTagsRequestPending: true });\n    this.props\n      .setRegisteredModelTagApi(modelName, values.name, values.value)\n      .then(() => {\n        this.setState({ isTagsRequestPending: false });\n        (form as any).resetFields();\n      })\n      .catch((ex: ErrorWrapper | Error) => {\n        this.setState({ isTagsRequestPending: false });\n        // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n        console.error(ex);\n        const message = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n        Utils.displayGlobalErrorNotification('Failed to add tag. Error: ' + message);\n      });\n  };\n\n  handleSaveEdit = ({ name, value }: any) => {\n    const { model } = this.props;\n    // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n    const modelName = model.name;\n    return this.props.setRegisteredModelTagApi(modelName, name, value).catch((ex: ErrorWrapper | Error) => {\n      // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n      console.error(ex);\n      const message = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n      Utils.displayGlobalErrorNotification('Failed to set tag. Error: ' + message);\n    });\n  };\n\n  handleDeleteTag = ({ name }: any) => {\n    const { model } = this.props;\n    // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n    const modelName = model.name;\n    return this.props.deleteRegisteredModelTagApi(modelName, name).catch((ex: ErrorWrapper | Error) => {\n      // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n      console.error(ex);\n      const message = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n      Utils.displayGlobalErrorNotification('Failed to delete tag. Error: ' + message);\n    });\n  };\n\n  onChange = (selectedRowKeys: any, selectedRows: any) => {\n    const newState = Object.assign({}, this.state);\n    newState.runsSelected = {};\n    selectedRows.forEach((row: any) => {\n      newState.runsSelected = {\n        ...newState.runsSelected,\n        [row.version]: row.run_id,\n      };\n    });\n    this.setState(newState);\n  };\n\n  onCompare() {\n    if (!this.props.model) {\n      return;\n    }\n    this.props.navigate(\n      ModelRegistryRoutes.getCompareModelVersionsPageRoute(this.props.model.name, this.state.runsSelected),\n    );\n  }\n\n  renderDescriptionEditIcon() {\n    return (\n      <Button\n        componentId=\"codegen_mlflow_app_src_model-registry_components_modelview.tsx_467\"\n        data-test-id=\"descriptionEditButton\"\n        type=\"link\"\n        css={styles.editButton}\n        onClick={this.startEditingDescription}\n      >\n        <FormattedMessage\n          defaultMessage=\"Edit\"\n          description=\"Text for the edit button next to the description section title on\n             the model view page\"\n        />\n      </Button>\n    );\n  }\n\n  renderDetails = () => {\n    const { model, modelVersions, tags } = this.props;\n    const {\n      stageFilter,\n      showDescriptionEditor,\n      isDeleteModalVisible,\n      isDeleteModalConfirmLoading,\n      isTagsRequestPending,\n    } = this.state;\n    // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n    const modelName = model.name;\n    const compareDisabled = Object.keys(this.state.runsSelected).length < 2;\n    return (\n      <div css={styles.wrapper}>\n        {/* Metadata List */}\n        <Descriptions columns={3} data-testid=\"model-view-metadata\">\n          <Descriptions.Item\n            data-testid=\"model-view-metadata-item\"\n            label={this.props.intl.formatMessage({\n              defaultMessage: 'Created Time',\n              description: 'Label name for the created time under details tab on the model view page',\n            })}\n          >\n            {/* @ts-expect-error TS(2532): Object is possibly 'undefined'. */}\n            {Utils.formatTimestamp(model.creation_timestamp, this.props.intl)}\n          </Descriptions.Item>\n          <Descriptions.Item\n            data-testid=\"model-view-metadata-item\"\n            label={this.props.intl.formatMessage({\n              defaultMessage: 'Last Modified',\n              description: 'Label name for the last modified time under details tab on the model view page',\n            })}\n          >\n            {/* @ts-expect-error TS(2532): Object is possibly 'undefined'. */}\n            {Utils.formatTimestamp(model.last_updated_timestamp, this.props.intl)}\n          </Descriptions.Item>\n          {/* Reported during ESLint upgrade */}\n          {/* eslint-disable-next-line react/prop-types */}\n          {(model as any).user_id && (\n            <Descriptions.Item\n              data-testid=\"model-view-metadata-item\"\n              label={this.props.intl.formatMessage({\n                defaultMessage: 'Creator',\n                description: 'Lable name for the creator under details tab on the model view page',\n              })}\n            >\n              {/* eslint-disable-next-line react/prop-types */}\n              <div>{(model as any).user_id}</div>\n            </Descriptions.Item>\n          )}\n        </Descriptions>\n\n        {/* Page Sections */}\n        <CollapsibleSection\n          css={styles.collapsiblePanel}\n          title={\n            <span>\n              <FormattedMessage\n                defaultMessage=\"Description\"\n                description=\"Title text for the description section under details tab on the model\n                   view page\"\n              />{' '}\n              {!showDescriptionEditor ? this.renderDescriptionEditIcon() : null}\n            </span>\n          }\n          forceOpen={showDescriptionEditor}\n          // Reported during ESLint upgrade\n          // eslint-disable-next-line react/prop-types\n          defaultCollapsed={!(model as any).description}\n          data-test-id=\"model-description-section\"\n        >\n          <EditableNote\n            defaultMarkdown={(model as any).description}\n            onSubmit={this.handleSubmitEditDescription}\n            onCancel={this.handleCancelEditDescription}\n            showEditor={showDescriptionEditor}\n          />\n        </CollapsibleSection>\n        <div data-test-id=\"tags-section\">\n          <CollapsibleSection\n            css={styles.collapsiblePanel}\n            title={\n              <FormattedMessage\n                defaultMessage=\"Tags\"\n                description=\"Title text for the tags section under details tab on the model view\n                   page\"\n              />\n            }\n            defaultCollapsed={Utils.getVisibleTagValues(tags).length === 0}\n            data-test-id=\"model-tags-section\"\n          >\n            <EditableTagsTableView\n              // @ts-expect-error TS(2322): Type '{ innerRef: RefObject<unknown>; handleAddTag... Remove this comment to see the full error message\n              innerRef={this.formRef}\n              handleAddTag={this.handleAddTag}\n              handleDeleteTag={this.handleDeleteTag}\n              handleSaveEdit={this.handleSaveEdit}\n              tags={tags}\n              isRequestPending={isTagsRequestPending}\n            />\n          </CollapsibleSection>\n        </div>\n        <CollapsibleSection\n          css={styles.collapsiblePanel}\n          title={\n            <>\n              <div css={styles.versionsTabButtons}>\n                <span>\n                  <FormattedMessage\n                    defaultMessage=\"Versions\"\n                    description=\"Title text for the versions section under details tab on the\n                       model view page\"\n                  />\n                </span>\n                {!this.props.usingNextModelsUI && (\n                  <SegmentedControlGroup\n                    componentId=\"codegen_mlflow_app_src_model-registry_components_modelview.tsx_600\"\n                    name=\"stage-filter\"\n                    value={this.state.stageFilter}\n                    onChange={(e) => this.handleStageFilterChange(e)}\n                    css={{ fontWeight: 'normal' }}\n                  >\n                    <SegmentedControlButton value={StageFilters.ALL}>\n                      <FormattedMessage\n                        defaultMessage=\"All\"\n                        description=\"Tab text to view all versions under details tab on the model view page\"\n                      />\n                    </SegmentedControlButton>\n                    <SegmentedControlButton value={StageFilters.ACTIVE}>\n                      <FormattedMessage\n                        defaultMessage=\"Active\"\n                        description=\"Tab text to view active versions under details tab\n                                on the model view page\"\n                      />{' '}\n                      {this.getActiveVersionsCount()}\n                    </SegmentedControlButton>\n                  </SegmentedControlGroup>\n                )}\n                <Button\n                  componentId=\"codegen_mlflow_app_src_model-registry_components_modelview.tsx_619\"\n                  data-test-id=\"compareButton\"\n                  disabled={compareDisabled}\n                  onClick={this.onCompare}\n                >\n                  <FormattedMessage\n                    defaultMessage=\"Compare\"\n                    description=\"Text for compare button to compare versions under details tab\n                       on the model view page\"\n                  />\n                </Button>\n              </div>\n            </>\n          }\n          data-test-id=\"model-versions-section\"\n        >\n          {shouldShowModelsNextUI() && (\n            <div\n              css={{\n                marginBottom: 8,\n                display: 'flex',\n                justifyContent: 'flex-end',\n              }}\n            >\n              <ModelsNextUIToggleSwitch />\n            </div>\n          )}\n          <ModelVersionTable\n            activeStageOnly={stageFilter === StageFilters.ACTIVE && !this.props.usingNextModelsUI}\n            modelName={modelName}\n            modelVersions={modelVersions}\n            modelEntity={model}\n            onChange={this.onChange}\n            onMetadataUpdated={this.props.onMetadataUpdated}\n            usingNextModelsUI={this.props.usingNextModelsUI}\n            aliases={model?.aliases}\n          />\n        </CollapsibleSection>\n\n        {/* Delete Model Dialog */}\n        <DangerModal\n          componentId=\"codegen_mlflow_app_src_model-registry_components_modelview.tsx_662\"\n          data-testid=\"mlflow-input-modal\"\n          title={this.props.intl.formatMessage({\n            defaultMessage: 'Delete Model',\n            description: 'Title text for delete model modal on model view page',\n          })}\n          visible={isDeleteModalVisible}\n          confirmLoading={isDeleteModalConfirmLoading}\n          onOk={this.handleDeleteConfirm}\n          okText={this.props.intl.formatMessage({\n            defaultMessage: 'Delete',\n            description: 'OK text for delete model modal on model view page',\n          })}\n          cancelText={this.props.intl.formatMessage({\n            defaultMessage: 'Cancel',\n            description: 'Cancel text for delete model modal on model view page',\n          })}\n          onCancel={this.hideDeleteModal}\n        >\n          <span>\n            <FormattedMessage\n              defaultMessage=\"Are you sure you want to delete {modelName}? This cannot be undone.\"\n              description=\"Confirmation message for delete model modal on model view page\"\n              values={{ modelName: modelName }}\n            />\n          </span>\n        </DangerModal>\n      </div>\n    );\n  };\n\n  renderMainPanel() {\n    return this.renderDetails();\n  }\n\n  render() {\n    const { model } = this.props;\n    // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n    const modelName = model.name;\n\n    const breadcrumbs = [\n      <Link to={ModelRegistryRoutes.modelListPageRoute}>\n        <FormattedMessage\n          defaultMessage=\"Registered Models\"\n          description=\"Text for link back to model page under the header on the model view page\"\n        />\n      </Link>,\n    ];\n    return (\n      <div>\n        <PageHeader title={modelName} breadcrumbs={breadcrumbs}>\n          <OverflowMenu menu={this.getOverflowMenuItems()} />\n        </PageHeader>\n        {this.renderMainPanel()}\n      </div>\n    );\n  }\n}\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const modelName = ownProps.model.name;\n  const tags = getRegisteredModelTags(modelName, state);\n  return { tags };\n};\nconst mapDispatchToProps = { setRegisteredModelTagApi, deleteRegisteredModelTagApi };\n\nconst styles = {\n  emailNotificationPreferenceDropdown: (theme: any) => ({\n    width: 300,\n    marginBottom: theme.spacing.md,\n  }),\n  emailNotificationPreferenceTip: (theme: any) => ({\n    paddingLeft: theme.spacing.sm,\n    paddingRight: theme.spacing.sm,\n  }),\n  collapsiblePanel: (theme: any) => ({\n    marginBottom: theme.spacing.md,\n  }),\n  wrapper: (theme: any) => ({\n    /**\n     * This seems to be a best and most stable method to catch\n     * antd's collapsible section buttons without hacks\n     * and using class names.\n     */\n    'div[role=\"button\"][aria-expanded]': {\n      height: theme.general.buttonHeight,\n    },\n  }),\n  editButton: (theme: any) => ({\n    marginLeft: theme.spacing.md,\n  }),\n  versionsTabButtons: (theme: any) => ({\n    display: 'flex',\n    gap: theme.spacing.md,\n    alignItems: 'center',\n  }),\n};\n\nexport const ModelView = connect(\n  mapStateToProps,\n  mapDispatchToProps,\n)(withNextModelsUIContext(injectIntl(ModelViewImpl)));\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { connect } from 'react-redux';\nimport {\n  searchModelVersionsApi,\n  getRegisteredModelApi,\n  updateRegisteredModelApi,\n  deleteRegisteredModelApi,\n} from '../actions';\nimport { ModelView } from './ModelView';\nimport { getModelVersions } from '../reducers';\nimport { MODEL_VERSION_STATUS_POLL_INTERVAL as POLL_INTERVAL } from '../constants';\nimport { PageContainer } from '../../common/components/PageContainer';\nimport RequestStateWrapper, { triggerError } from '../../common/components/RequestStateWrapper';\nimport { Spinner } from '../../common/components/Spinner';\nimport { ErrorView } from '../../common/components/ErrorView';\nimport { ModelRegistryRoutes } from '../routes';\nimport Utils from '../../common/utils/Utils';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport { injectIntl } from 'react-intl';\nimport { ErrorWrapper } from '../../common/utils/ErrorWrapper';\nimport { withRouterNext } from '../../common/utils/withRouterNext';\nimport type { WithRouterNextProps } from '../../common/utils/withRouterNext';\nimport { withErrorBoundary } from '../../common/utils/withErrorBoundary';\nimport ErrorUtils from '../../common/utils/ErrorUtils';\nimport { ErrorCodes } from '../../common/constants';\n\ntype ModelPageImplProps = WithRouterNextProps<{ subpage: string }> & {\n  modelName: string;\n  model?: any;\n  modelVersions?: any[];\n  emailSubscriptionStatus?: string;\n  userLevelEmailSubscriptionStatus?: string;\n  searchModelVersionsApi: (...args: any[]) => any;\n  getRegisteredModelApi: (...args: any[]) => any;\n  updateRegisteredModelApi: (...args: any[]) => any;\n  deleteRegisteredModelApi: (...args: any[]) => any;\n  setEmailSubscriptionStatusApi: (...args: any[]) => any;\n  getEmailSubscriptionStatusApi: (...args: any[]) => any;\n  getUserLevelEmailSubscriptionStatusApi: (...args: any[]) => any;\n  searchEndpointsByModelNameApi: (...args: any[]) => any;\n  intl?: any;\n};\n\nexport class ModelPageImpl extends React.Component<ModelPageImplProps> {\n  hasUnfilledRequests: any;\n  pollIntervalId: any;\n\n  initSearchModelVersionsApiRequestId = getUUID();\n  initgetRegisteredModelApiRequestId = getUUID();\n  updateRegisteredModelApiId = getUUID();\n  deleteRegisteredModelApiId = getUUID();\n\n  criticalInitialRequestIds = [this.initSearchModelVersionsApiRequestId, this.initgetRegisteredModelApiRequestId];\n\n  handleEditDescription = (description: any) => {\n    const { model } = this.props;\n    return this.props\n      .updateRegisteredModelApi(model.name, description, this.updateRegisteredModelApiId)\n      .then(this.loadData);\n  };\n\n  handleDelete = () => {\n    const { model } = this.props;\n    return this.props.deleteRegisteredModelApi(model.name, this.deleteRegisteredModelApiId);\n  };\n\n  loadData = (isInitialLoading: any) => {\n    const { modelName } = this.props;\n    this.hasUnfilledRequests = true;\n    const promiseValues = [\n      this.props.getRegisteredModelApi(\n        modelName,\n        isInitialLoading === true ? this.initgetRegisteredModelApiRequestId : null,\n      ),\n      this.props.searchModelVersionsApi(\n        { name: modelName },\n        isInitialLoading === true ? this.initSearchModelVersionsApiRequestId : null,\n      ),\n    ];\n    return Promise.all(promiseValues).then(() => {\n      this.hasUnfilledRequests = false;\n    });\n  };\n\n  pollData = () => {\n    const { modelName, navigate } = this.props;\n    if (!this.hasUnfilledRequests && Utils.isBrowserTabVisible()) {\n      // @ts-expect-error TS(2554): Expected 1 arguments, but got 0.\n      return this.loadData().catch((e) => {\n        if (e instanceof ErrorWrapper && e.getErrorCode() === 'RESOURCE_DOES_NOT_EXIST') {\n          Utils.logErrorAndNotifyUser(e);\n          this.props.deleteRegisteredModelApi(modelName, undefined, true);\n          navigate(ModelRegistryRoutes.modelListPageRoute);\n        } else {\n          // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n          console.error(e);\n        }\n        this.hasUnfilledRequests = false;\n      });\n    }\n    return Promise.resolve();\n  };\n\n  componentDidMount() {\n    // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n    this.loadData(true).catch(console.error);\n    this.hasUnfilledRequests = false;\n    this.pollIntervalId = setInterval(this.pollData, POLL_INTERVAL);\n  }\n\n  componentWillUnmount() {\n    clearInterval(this.pollIntervalId);\n  }\n\n  render() {\n    const { model, modelVersions, navigate, modelName } = this.props;\n    return (\n      <PageContainer>\n        <RequestStateWrapper\n          requestIds={this.criticalInitialRequestIds}\n          // eslint-disable-next-line no-trailing-spaces\n        >\n          {(loading: any, hasError: any, requests: any) => {\n            if (hasError) {\n              clearInterval(this.pollIntervalId);\n              if (Utils.shouldRender404(requests, [this.initgetRegisteredModelApiRequestId])) {\n                return (\n                  <ErrorView\n                    statusCode={404}\n                    subMessage={this.props.intl.formatMessage(\n                      {\n                        defaultMessage: 'Model {modelName} does not exist',\n                        description: 'Sub-message text for error message on overall model page',\n                      },\n                      {\n                        modelName: modelName,\n                      },\n                    )}\n                    fallbackHomePageReactRoute={ModelRegistryRoutes.modelListPageRoute}\n                  />\n                );\n              }\n              const permissionDeniedErrors = requests.filter((request: any) => {\n                return (\n                  this.criticalInitialRequestIds.includes(request.id) &&\n                  request.error?.getErrorCode() === ErrorCodes.PERMISSION_DENIED\n                );\n              });\n              if (permissionDeniedErrors && permissionDeniedErrors[0]) {\n                return (\n                  <ErrorView\n                    statusCode={403}\n                    subMessage={this.props.intl.formatMessage(\n                      {\n                        defaultMessage: 'Permission denied for {modelName}. Error: \"{errorMsg}\"',\n                        description: 'Permission denied error message on registered model detail page',\n                      },\n                      {\n                        modelName: modelName,\n                        errorMsg: permissionDeniedErrors[0].error?.getMessageField(),\n                      },\n                    )}\n                    fallbackHomePageReactRoute={ModelRegistryRoutes.modelListPageRoute}\n                  />\n                );\n              }\n              // TODO(Zangr) Have a more generic boundary to handle all errors, not just 404.\n              triggerError(requests);\n            } else if (loading) {\n              return <Spinner />;\n            } else if (model) {\n              // Null check to prevent NPE after delete operation\n              return (\n                <ModelView\n                  model={model}\n                  modelVersions={modelVersions}\n                  handleEditDescription={this.handleEditDescription}\n                  handleDelete={this.handleDelete}\n                  navigate={navigate}\n                  onMetadataUpdated={this.loadData}\n                />\n              );\n            }\n            return null;\n          }}\n        </RequestStateWrapper>\n      </PageContainer>\n    );\n  }\n}\n\nconst mapStateToProps = (state: any, ownProps: WithRouterNextProps<{ modelName: string }>) => {\n  const modelName = decodeURIComponent(ownProps.params.modelName);\n  const model = state.entities.modelByName[modelName];\n  const modelVersions = getModelVersions(state, modelName);\n  return {\n    modelName,\n    model,\n    modelVersions,\n  };\n};\n\nconst mapDispatchToProps = {\n  searchModelVersionsApi,\n  getRegisteredModelApi,\n  updateRegisteredModelApi,\n  deleteRegisteredModelApi,\n};\n\nconst ModelPageWithRouter = withRouterNext(\n  // @ts-expect-error TS(2769): No overload matches this call.\n  connect(mapStateToProps, mapDispatchToProps)(injectIntl(ModelPageImpl)),\n);\n\nexport const ModelPage = withErrorBoundary(ErrorUtils.mlflowServices.MODEL_REGISTRY, ModelPageWithRouter);\n\nexport default ModelPage;\n", "import { sortedIndexOf } from 'lodash';\nimport React, { useMemo, useRef, useState } from 'react';\nimport { Control, useController } from 'react-hook-form';\nimport { useIntl } from 'react-intl';\n\nimport { PlusIcon, LegacySelect, LegacyTooltip, useDesignSystemTheme } from '@databricks/design-system';\nimport { KeyValueEntity } from '../../experiment-tracking/types';\n\n/**\n * Will show an extra row at the bottom of the dropdown menu to create a new tag when\n * The user has typed something in the search input\n * and either\n * 1. The search input is not an exact match for an existing tag name\n * 2. There are no tags available based on search input\n */\n\nfunction DropdownMenu(menu: React.ReactElement, allAvailableTags: string[]) {\n  const intl = useIntl();\n  const { theme } = useDesignSystemTheme();\n  const searchValue = menu.props.searchValue.toLowerCase();\n\n  const resolvedMenu = useMemo(() => {\n    if (!searchValue) return menu;\n\n    const doesTagExists = sortedIndexOf(allAvailableTags, searchValue) >= 0;\n    if (doesTagExists) return menu;\n\n    const isValidTagKey = /^[^,.:/=\\-\\s]+$/.test(searchValue);\n\n    // Overriding the menu to add a new option at the top\n    return React.cloneElement(menu, {\n      flattenOptions: [\n        {\n          data: {\n            value: searchValue,\n            disabled: !isValidTagKey,\n            style: {\n              color: isValidTagKey ? theme.colors.actionTertiaryTextDefault : theme.colors.actionDisabledText,\n            },\n            children: (\n              <LegacyTooltip\n                title={\n                  isValidTagKey\n                    ? undefined\n                    : intl.formatMessage({\n                        defaultMessage: ', . : / - = and blank spaces are not allowed',\n                        description:\n                          'Key-value tag editor modal > Tag dropdown Manage Modal > Invalid characters error',\n                      })\n                }\n                placement=\"right\"\n              >\n                <span css={{ display: 'block' }}>\n                  <PlusIcon css={{ marginRight: theme.spacing.sm }} />\n                  {intl.formatMessage(\n                    {\n                      defaultMessage: 'Add tag \"{tagKey}\"',\n                      description: 'Key-value tag editor modal > Tag dropdown Manage Modal > Add new tag button',\n                    },\n                    {\n                      tagKey: searchValue,\n                    },\n                  )}\n                </span>\n              </LegacyTooltip>\n            ),\n          },\n          key: searchValue,\n          groupOption: false,\n        },\n        ...menu.props.flattenOptions,\n      ],\n    });\n  }, [allAvailableTags, menu, searchValue, intl, theme]);\n\n  return resolvedMenu;\n}\n\nfunction getDropdownMenu(allAvailableTags: string[]) {\n  return (menu: React.ReactElement) => DropdownMenu(menu, allAvailableTags);\n}\n\n/**\n * Used in tag edit feature, allows selecting existing / adding new tag value\n */\nexport function TagKeySelectDropdown({\n  allAvailableTags,\n  control,\n  onKeyChangeCallback,\n}: {\n  allAvailableTags: string[];\n  control: Control<KeyValueEntity>;\n  onKeyChangeCallback?: (key?: string) => void;\n}) {\n  const intl = useIntl();\n  const [isOpen, setIsOpen] = useState(false);\n  const selectRef = useRef<{ blur: () => void; focus: () => void }>(null);\n\n  const { field, fieldState } = useController({\n    control: control,\n    name: 'key',\n    rules: {\n      required: {\n        message: intl.formatMessage({\n          defaultMessage: 'A tag key is required',\n          description: 'Key-value tag editor modal > Tag dropdown > Tag key required error message',\n        }),\n        value: true,\n      },\n    },\n  });\n\n  const handleDropdownVisibleChange = (visible: boolean) => {\n    setIsOpen(visible);\n  };\n\n  const handleClear = () => {\n    field.onChange(undefined);\n    onKeyChangeCallback?.(undefined);\n  };\n\n  const handleSelect = (key: string) => {\n    field.onChange(key);\n    onKeyChangeCallback?.(key);\n  };\n\n  return (\n    <LegacySelect\n      allowClear\n      ref={selectRef}\n      dangerouslySetAntdProps={{\n        showSearch: true,\n        dropdownRender: getDropdownMenu(allAvailableTags),\n      }}\n      css={{ width: '100%' }}\n      placeholder={intl.formatMessage({\n        defaultMessage: 'Type a key',\n        description: 'Key-value tag editor modal > Tag dropdown > Tag input placeholder',\n      })}\n      value={field.value}\n      defaultValue={field.value}\n      open={isOpen}\n      onDropdownVisibleChange={handleDropdownVisibleChange}\n      filterOption={(input, option) => option?.value.toLowerCase().includes(input.toLowerCase())}\n      onSelect={handleSelect}\n      onClear={handleClear}\n      validationState={fieldState.error ? 'error' : undefined}\n    >\n      {allAvailableTags.map((tag) => (\n        <LegacySelect.Option value={tag} key={tag}>\n          {tag}\n        </LegacySelect.Option>\n      ))}\n    </LegacySelect>\n  );\n}\n", "import { isEqual, sortBy } from 'lodash';\nimport { useCallback, useMemo, useRef, useState } from 'react';\nimport { truncate } from 'lodash';\n\nimport {\n  Button,\n  FormUI,\n  Modal,\n  PlusIcon,\n  Popover,\n  RHFControlledComponents,\n  RestoreAntDDefaultClsPrefix,\n  LegacyTooltip,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport { Typography } from '@databricks/design-system';\nimport { KeyValueEntity } from '../../experiment-tracking/types';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { useForm } from 'react-hook-form';\nimport { TagKeySelectDropdown } from '../components/TagSelectDropdown';\nimport { KeyValueTag } from '../components/KeyValueTag';\nimport { ErrorWrapper } from '../utils/ErrorWrapper';\n\nfunction getTagsMap(tags: KeyValueEntity[]) {\n  return new Map(tags.map((tag) => [tag.key, tag]));\n}\n\n/**\n * Provides methods to initialize and display modal used to add and remove tags from any compatible entity\n */\nexport const useEditKeyValueTagsModal = <T extends { tags?: KeyValueEntity[] }>({\n  onSuccess,\n  saveTagsHandler,\n  allAvailableTags,\n  valueRequired = false,\n  title,\n}: {\n  onSuccess?: () => void;\n  saveTagsHandler: (editedEntity: T, existingTags: KeyValueEntity[], newTags: KeyValueEntity[]) => Promise<any>;\n  allAvailableTags?: string[];\n  valueRequired?: boolean;\n  title?: React.ReactNode;\n}) => {\n  const editedEntityRef = useRef<T>();\n  const [errorMessage, setErrorMessage] = useState<string>('');\n  const { theme } = useDesignSystemTheme();\n\n  const [initialTags, setInitialTags] = useState<Map<string, KeyValueEntity>>(new Map());\n  const [finalTags, setFinalTags] = useState<Map<string, KeyValueEntity>>(new Map());\n\n  const [showModal, setShowModal] = useState(false);\n\n  const form = useForm<KeyValueEntity>({\n    defaultValues: {\n      key: undefined,\n      value: '',\n    },\n  });\n\n  const hideModal = () => setShowModal(false);\n\n  /**\n   * Function used to invoke the modal and start editing tags of the particular model version\n   */\n  const showEditTagsModal = useCallback(\n    (editedEntity: T) => {\n      editedEntityRef.current = editedEntity;\n      setInitialTags(getTagsMap(editedEntity.tags || []));\n      setFinalTags(getTagsMap(editedEntity.tags || []));\n      form.reset();\n\n      setShowModal(true);\n    },\n    [form],\n  );\n\n  const saveTags = async () => {\n    if (!editedEntityRef.current) {\n      return;\n    }\n    setErrorMessage('');\n    setIsLoading(true);\n    saveTagsHandler(editedEntityRef.current, Array.from(initialTags.values()), Array.from(finalTags.values()))\n      .then(() => {\n        hideModal();\n        onSuccess?.();\n        setIsLoading(false);\n      })\n      .catch((e: ErrorWrapper | Error) => {\n        setIsLoading(false);\n        setErrorMessage(e instanceof ErrorWrapper ? e.getUserVisibleError()?.message : e.message);\n      });\n  };\n\n  const intl = useIntl();\n  const formValues = form.watch();\n\n  const [isLoading, setIsLoading] = useState(false);\n\n  const hasNewValues = useMemo(\n    () => !isEqual(sortBy(Array.from(initialTags.values()), 'key'), sortBy(Array.from(finalTags.values()), 'key')),\n    [initialTags, finalTags],\n  );\n  const isDirty = formValues.key || formValues.value;\n  const showPopoverMessage = hasNewValues && isDirty;\n\n  const onKeyChangeCallback = (key: string | undefined) => {\n    const tag = key ? finalTags.get(key) : undefined;\n    /**\n     * If a tag value exists for provided key, set the value to the existing tag value\n     */\n    form.setValue('value', tag?.value ?? '');\n  };\n\n  const handleTagDelete = ({ key }: KeyValueEntity) => {\n    setFinalTags((currentFinalTags) => {\n      currentFinalTags.delete(key);\n      return new Map(currentFinalTags);\n    });\n  };\n\n  const onSubmit = () => {\n    // Do not accept form if no value provided while it's required\n    if (valueRequired && !formValues.value.trim()) {\n      return;\n    }\n\n    // Add new tag to existing tags leaving only one tag per key value\n    const newEntries = new Map(finalTags);\n    newEntries.set(formValues.key, formValues);\n\n    setFinalTags(newEntries);\n    form.reset();\n  };\n\n  const EditTagsModal = (\n    <Modal\n      componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_135\"\n      destroyOnClose\n      visible={showModal}\n      title={\n        title ?? (\n          <FormattedMessage\n            defaultMessage=\"Add/Edit tags\"\n            description=\"Key-value tag editor modal > Title of the update tags modal\"\n          />\n        )\n      }\n      onCancel={hideModal}\n      footer={\n        <RestoreAntDDefaultClsPrefix>\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_147\"\n            dangerouslyUseFocusPseudoClass\n            onClick={hideModal}\n            /**\n             * Hack: The footer will remove the margin to the save tags button\n             * if the button if wrapped on another component.\n             */\n            css={{ marginRight: !hasNewValues ? theme.spacing.sm : 0 }}\n          >\n            {intl.formatMessage({\n              defaultMessage: 'Cancel',\n              description: 'Key-value tag editor modal > Manage Tag cancel button',\n            })}\n          </Button>\n          {showPopoverMessage ? (\n            <UnsavedTagPopoverTrigger formValues={formValues} isLoading={isLoading} onSaveTask={saveTags} />\n          ) : (\n            <LegacyTooltip\n              title={\n                !hasNewValues\n                  ? intl.formatMessage({\n                      defaultMessage: 'Please add or remove one or more tags before saving',\n                      description: 'Key-value tag editor modal > Tag disabled message',\n                    })\n                  : undefined\n              }\n            >\n              <Button\n                componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_174\"\n                dangerouslyUseFocusPseudoClass\n                disabled={!hasNewValues}\n                loading={isLoading}\n                type=\"primary\"\n                onClick={saveTags}\n              >\n                {intl.formatMessage({\n                  defaultMessage: 'Save tags',\n                  description: 'Key-value tag editor modal > Manage Tag save button',\n                })}\n              </Button>\n            </LegacyTooltip>\n          )}\n        </RestoreAntDDefaultClsPrefix>\n      }\n    >\n      <form\n        onSubmit={form.handleSubmit(onSubmit)}\n        css={{ display: 'flex', alignItems: 'flex-end', gap: theme.spacing.md }}\n      >\n        <div css={{ minWidth: 0, display: 'flex', gap: theme.spacing.md, flex: 1 }}>\n          <div css={{ flex: 1 }}>\n            <FormUI.Label htmlFor=\"key\">\n              {intl.formatMessage({\n                defaultMessage: 'Key',\n                description: 'Key-value tag editor modal > Key input label',\n              })}\n            </FormUI.Label>\n            <TagKeySelectDropdown\n              allAvailableTags={allAvailableTags || []}\n              control={form.control}\n              onKeyChangeCallback={onKeyChangeCallback}\n            />\n          </div>\n          <div css={{ flex: 1 }}>\n            <FormUI.Label htmlFor=\"value\">\n              {valueRequired\n                ? intl.formatMessage({\n                    defaultMessage: 'Value',\n                    description: 'Key-value tag editor modal > Value input label (required)',\n                  })\n                : intl.formatMessage({\n                    defaultMessage: 'Value (optional)',\n                    description: 'Key-value tag editor modal > Value input label',\n                  })}\n            </FormUI.Label>\n            <RHFControlledComponents.Input\n              componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_223\"\n              name=\"value\"\n              control={form.control}\n              aria-label={\n                valueRequired\n                  ? intl.formatMessage({\n                      defaultMessage: 'Value',\n                      description: 'Key-value tag editor modal > Value input label (required)',\n                    })\n                  : intl.formatMessage({\n                      defaultMessage: 'Value (optional)',\n                      description: 'Key-value tag editor modal > Value input label',\n                    })\n              }\n              placeholder={intl.formatMessage({\n                defaultMessage: 'Type a value',\n                description: 'Key-value tag editor modal > Value input placeholder',\n              })}\n            />\n          </div>\n        </div>\n        <LegacyTooltip\n          title={intl.formatMessage({\n            defaultMessage: 'Add tag',\n            description: 'Key-value tag editor modal > Add tag button',\n          })}\n        >\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_248\"\n            htmlType=\"submit\"\n            aria-label={intl.formatMessage({\n              defaultMessage: 'Add tag',\n              description: 'Key-value tag editor modal > Add tag button',\n            })}\n          >\n            <PlusIcon />\n          </Button>\n        </LegacyTooltip>\n      </form>\n      {errorMessage && <FormUI.Message type=\"error\" message={errorMessage} />}\n      <div\n        css={{\n          display: 'flex',\n          rowGap: theme.spacing.xs,\n          flexWrap: 'wrap',\n          marginTop: theme.spacing.sm,\n        }}\n      >\n        {Array.from(finalTags.values()).map((tag) => (\n          <KeyValueTag isClosable tag={tag} onClose={() => handleTagDelete(tag)} key={tag.key} />\n        ))}\n      </div>\n    </Modal>\n  );\n\n  return { EditTagsModal, showEditTagsModal, isLoading };\n};\n\nfunction UnsavedTagPopoverTrigger({\n  isLoading,\n  formValues,\n  onSaveTask,\n}: {\n  isLoading: boolean;\n  formValues: any;\n  onSaveTask: () => void;\n}) {\n  const intl = useIntl();\n  const { theme } = useDesignSystemTheme();\n\n  const tagKeyDisplay = `${truncate(formValues.key, { length: 20 }) || '_'}`;\n  const tagValueDisplay = formValues.value ? `:${truncate(formValues.value, { length: 20 })}` : '';\n  const fullTagDisplay = `${tagKeyDisplay}${tagValueDisplay}`;\n\n  const shownText = intl.formatMessage(\n    {\n      defaultMessage: 'Are you sure you want to save and close without adding \"{tag}\"',\n      description: 'Key-value tag editor modal > Unsaved tag message',\n    },\n    {\n      tag: fullTagDisplay,\n    },\n  );\n  return (\n    <Popover.Root componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_309\">\n      <Popover.Trigger asChild>\n        <Button\n          componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_306\"\n          dangerouslyUseFocusPseudoClass\n          loading={isLoading}\n          type=\"primary\"\n        >\n          {intl.formatMessage({\n            defaultMessage: 'Save tags',\n            description: 'Key-value tag editor modal > Manage Tag save button',\n          })}\n        </Button>\n      </Popover.Trigger>\n      <Popover.Content align=\"end\" aria-label={shownText}>\n        <Typography.Paragraph css={{ maxWidth: 400 }}>{shownText}</Typography.Paragraph>\n        <Popover.Close asChild>\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_316\"\n            onClick={onSaveTask}\n          >\n            {intl.formatMessage({\n              defaultMessage: 'Yes, save and close',\n              description: 'Key-value tag editor modal > Unsaved tag message > Yes, save and close button',\n            })}\n          </Button>\n        </Popover.Close>\n        <Popover.Close asChild>\n          <Button\n            componentId=\"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_324\"\n            type=\"primary\"\n            css={{ marginLeft: theme.spacing.sm }}\n          >\n            {intl.formatMessage({\n              defaultMessage: 'Cancel',\n              description: 'Key-value tag editor modal > Unsaved tag message > cancel button',\n            })}\n          </Button>\n        </Popover.Close>\n        <Popover.Arrow />\n      </Popover.Content>\n    </Popover.Root>\n  );\n}\n", "import _ from 'lodash';\n// Import pako lazily to reduce bundle size\nconst lazyPako = () => import('pako');\n\nexport const truncateToFirstLineWithMaxLength = (str: string, maxLength: number): string => {\n  const truncated = _.truncate(str, {\n    length: maxLength,\n  });\n  return _.takeWhile(truncated, (char) => char !== '\\n').join('');\n};\n\nexport const capitalizeFirstChar = (str: unknown) => {\n  if (!str || typeof str !== 'string' || str.length < 1) {\n    return str;\n  }\n  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n};\n\nexport const middleTruncateStr = (str: string, maxLen: number) => {\n  if (str.length > maxLen) {\n    const firstPartLen = Math.floor((maxLen - 3) / 2);\n    const lastPartLen = maxLen - 3 - firstPartLen;\n    return str.substring(0, firstPartLen) + '...' + str.substring(str.length - lastPartLen, str.length);\n  } else {\n    return str;\n  }\n};\n\nconst capitalizeFirstLetter = (string: string) => {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n};\n\nconst _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n\n/* eslint-disable no-bitwise */\n/**\n * UTF-8 safe version of base64 encoder\n * Source: http://www.webtoolkit.info/javascript_base64.html\n *\n * @param {string} input - Text to encode\n */\nexport const btoaUtf8 = (input: string) => {\n  let output = '';\n  let i = 0;\n\n  const result = _utf8_encode(input);\n\n  while (i < result.length) {\n    const chr1 = result.charCodeAt(i++);\n    const chr2 = result.charCodeAt(i++);\n    const chr3 = result.charCodeAt(i++);\n\n    const enc1 = chr1 >> 2;\n    const enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n    let enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n    let enc4 = chr3 & 63;\n\n    if (isNaN(chr2)) {\n      enc4 = 64;\n      enc3 = enc4;\n    } else if (isNaN(chr3)) {\n      enc4 = 64;\n    }\n\n    output = output + _keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4);\n  }\n\n  return output;\n};\n\n/**\n * UTF-8 safe version of base64 decoder\n * Source: http://www.webtoolkit.info/javascript_base64.html\n *\n * @param {string} input - Text to decode\n */\nexport const atobUtf8 = (input: string) => {\n  let output = '';\n  let i = 0;\n\n  const result = input?.replace(/[^A-Za-z0-9+/=]/g, '') || '';\n\n  while (i < result.length) {\n    const enc1 = _keyStr.indexOf(result.charAt(i++));\n    const enc2 = _keyStr.indexOf(result.charAt(i++));\n    const enc3 = _keyStr.indexOf(result.charAt(i++));\n    const enc4 = _keyStr.indexOf(result.charAt(i++));\n\n    const chr1 = (enc1 << 2) | (enc2 >> 4);\n    const chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n    const chr3 = ((enc3 & 3) << 6) | enc4;\n\n    output += String.fromCharCode(chr1);\n\n    if (enc3 !== 64) {\n      output += String.fromCharCode(chr2);\n    }\n\n    if (enc4 !== 64) {\n      output += String.fromCharCode(chr3);\n    }\n  }\n\n  return _utf8_decode(output);\n};\n\n/**\n * (private method) does a UTF-8 encoding\n *\n * @private\n * @param {string} string - Text to encode\n */\nconst _utf8_encode = (string = '') => {\n  const result = string.replace(/\\r\\n/g, '\\n');\n  let utftext = '';\n\n  for (let n = 0; n < result.length; n++) {\n    const c = result.charCodeAt(n);\n\n    if (c < 128) {\n      utftext += String.fromCharCode(c);\n    } else if (c > 127 && c < 2048) {\n      utftext += String.fromCharCode((c >> 6) | 192) + String.fromCharCode((c & 63) | 128);\n    } else {\n      utftext +=\n        String.fromCharCode((c >> 12) | 224) +\n        String.fromCharCode(((c >> 6) & 63) | 128) +\n        String.fromCharCode((c & 63) | 128);\n    }\n  }\n\n  return utftext;\n};\n\n/**\n * (private method) does a UTF-8 decoding\n *\n * @private\n * @param {string} utftext - UTF-8 text to dencode\n */\nconst _utf8_decode = (utftext = '') => {\n  let string = '';\n  let i = 0;\n\n  while (i < utftext.length) {\n    const c = utftext.charCodeAt(i);\n\n    if (c < 128) {\n      string += String.fromCharCode(c);\n      i++;\n    } else if (c > 191 && c < 224) {\n      const c2 = utftext.charCodeAt(i + 1);\n      string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));\n      i += 2;\n    } else {\n      const c2 = utftext.charCodeAt(i + 1);\n      const c3 = utftext.charCodeAt(i + 2);\n      string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));\n      i += 3;\n    }\n  }\n  return string;\n};\n/* eslint-enable no-bitwise */\n\n/**\n * Returns a SHA256 hash of the input string\n */\nexport const getStringSHA256 = (input: string) => {\n  return crypto.subtle.digest('SHA-256', new TextEncoder().encode(input)).then((arrayBuffer) => {\n    return Array.prototype.map.call(new Uint8Array(arrayBuffer), (x) => ('00' + x.toString(16)).slice(-2)).join('');\n  });\n};\n\nconst COMPRESSED_TEXT_DEFLATE_PREFIX = 'deflate;';\n\nexport const textCompressDeflate = async (text: string) => {\n  const pako = await lazyPako();\n  const binaryData = pako.deflate(text);\n\n  // Buffer-based implementation\n  if (typeof Buffer !== 'undefined') {\n    const b64encoded = Buffer.from(binaryData).toString('base64');\n    return `${COMPRESSED_TEXT_DEFLATE_PREFIX}${b64encoded}`;\n  }\n\n  // btoa-based implementation\n  const binaryString = Array.from(binaryData, (byte) => String.fromCodePoint(byte)).join('');\n  return `${COMPRESSED_TEXT_DEFLATE_PREFIX}${btoa(binaryString)}`;\n};\n\nexport const textDecompressDeflate = async (compressedText: string) => {\n  const pako = await lazyPako();\n  if (!compressedText.startsWith(COMPRESSED_TEXT_DEFLATE_PREFIX)) {\n    throw new Error('Invalid compressed text, payload header invalid');\n  }\n  const compressedTextWithoutPrefix = compressedText.slice(COMPRESSED_TEXT_DEFLATE_PREFIX.length);\n\n  // Buffer-based implementation\n  if (typeof Buffer !== 'undefined') {\n    const binaryString = Buffer.from(compressedTextWithoutPrefix, 'base64');\n    return pako.inflate(\n      // This doesn't fail in Mlflow-Copybara-Tester-Pr. TODO: check why.\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore [FEINF-4084] No overload matches this call.\n      binaryString,\n      { to: 'string' },\n    );\n  }\n\n  // atob-based implementation\n  const binaryString = atob(compressedTextWithoutPrefix);\n  return pako.inflate(\n    Uint8Array.from(binaryString, (m) => m.codePointAt(0) ?? 0),\n    { to: 'string' },\n  );\n};\n\nexport const isTextCompressedDeflate = (text: string) => text.startsWith(COMPRESSED_TEXT_DEFLATE_PREFIX);\n", "import React, { useState } from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { Button, type ButtonProps, LegacyTooltip } from '@databricks/design-system';\n\ninterface CopyButtonProps extends Partial<ButtonProps> {\n  copyText: string;\n  showLabel?: React.ReactNode;\n  componentId?: string;\n}\n\nexport const CopyButton = ({ copyText, showLabel = true, componentId, ...buttonProps }: CopyButtonProps) => {\n  const [showTooltip, setShowTooltip] = useState(false);\n\n  const handleClick = () => {\n    navigator.clipboard.writeText(copyText);\n    setShowTooltip(true);\n    setTimeout(() => {\n      setShowTooltip(false);\n    }, 3000);\n  };\n\n  const handleMouseLeave = () => {\n    setShowTooltip(false);\n  };\n\n  return (\n    <LegacyTooltip\n      title={\n        <FormattedMessage defaultMessage=\"Copied\" description=\"Tooltip text shown when copy operation completes\" />\n      }\n      dangerouslySetAntdProps={{\n        visible: showTooltip,\n      }}\n    >\n      <Button\n        componentId={componentId ?? 'mlflow.shared.copy_button'}\n        type=\"primary\"\n        onClick={handleClick}\n        onMouseLeave={handleMouseLeave}\n        css={{ 'z-index': 1 }}\n        // Define children as a explicit prop so it can be easily overrideable\n        children={\n          showLabel ? <FormattedMessage defaultMessage=\"Copy\" description=\"Button text for copy button\" /> : undefined\n        }\n        {...buttonProps}\n      />\n    </LegacyTooltip>\n  );\n};\n", "import { Button, PencilIcon, useDesignSystemTheme } from '@databricks/design-system';\nimport { ModelVersionAliasTag } from './ModelVersionAliasTag';\nimport { FormattedMessage } from 'react-intl';\n\ninterface ModelVersionTableAliasesCellProps {\n  aliases?: string[];\n  modelName: string;\n  version: string;\n  onAddEdit: () => void;\n  className?: string;\n}\n\nexport const ModelVersionTableAliasesCell = ({\n  aliases = [],\n  onAddEdit,\n  className,\n}: ModelVersionTableAliasesCellProps) => {\n  const { theme } = useDesignSystemTheme();\n\n  return (\n    <div\n      css={{\n        maxWidth: 300,\n        display: 'flex',\n        flexWrap: 'wrap',\n        alignItems: 'flex-start',\n        '> *': {\n          marginRight: '0 !important',\n        },\n        rowGap: theme.spacing.xs / 2,\n        columnGap: theme.spacing.xs,\n      }}\n      className={className}\n    >\n      {aliases.length < 1 ? (\n        <Button\n          componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelversiontablealiasescell.tsx_30\"\n          size=\"small\"\n          type=\"link\"\n          onClick={onAddEdit}\n        >\n          <FormattedMessage\n            defaultMessage=\"Add\"\n            description=\"Model registry > model version table > aliases column > 'add' button label\"\n          />\n        </Button>\n      ) : (\n        <>\n          {aliases.map((alias) => (\n            <ModelVersionAliasTag value={alias} key={alias} css={{ marginTop: theme.spacing.xs / 2 }} />\n          ))}\n          <Button\n            componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelversiontablealiasescell.tsx_41\"\n            size=\"small\"\n            icon={<PencilIcon />}\n            onClick={onAddEdit}\n          />\n        </>\n      )}\n    </div>\n  );\n};\n", "import React from 'react';\nimport { Modal, Typography, CopyIcon, useDesignSystemTheme } from '@databricks/design-system';\nconst { Paragraph } = Typography;\nimport { CopyButton } from '@mlflow/mlflow/src/shared/building_blocks/CopyButton';\n\nexport interface KeyValueTagFullViewModalProps {\n  tagKey: string;\n  tagValue: string;\n  setIsKeyValueTagFullViewModalVisible: React.Dispatch<React.SetStateAction<boolean>>;\n  isKeyValueTagFullViewModalVisible: boolean;\n}\n\nexport const KeyValueTagFullViewModal = React.memo((props: KeyValueTagFullViewModalProps) => {\n  const { theme } = useDesignSystemTheme();\n\n  return (\n    <Modal\n      componentId=\"codegen_mlflow_app_src_common_components_keyvaluetagfullviewmodal.tsx_17\"\n      title={'Tag: ' + props.tagKey}\n      visible={props.isKeyValueTagFullViewModalVisible}\n      onCancel={() => props.setIsKeyValueTagFullViewModalVisible(false)}\n    >\n      <div css={{ display: 'flex' }}>\n        <Paragraph css={{ flexGrow: 1 }}>\n          <pre\n            css={{\n              backgroundColor: theme.colors.backgroundPrimary,\n              marginTop: theme.spacing.sm,\n              whiteSpace: 'pre-wrap',\n              wordBreak: 'break-all',\n            }}\n          >\n            {props.tagValue}\n          </pre>\n        </Paragraph>\n        <div\n          css={{\n            marginTop: theme.spacing.sm,\n          }}\n        >\n          <CopyButton copyText={props.tagValue} showLabel={false} icon={<CopyIcon />} aria-label=\"Copy\" />\n        </div>\n      </div>\n    </Modal>\n  );\n});\n", "import { Tag, LegacyTooltip, Typography } from '@databricks/design-system';\nimport { KeyValueEntity } from '../../experiment-tracking/types';\nimport React, { useState } from 'react';\nimport { useIntl } from 'react-intl';\nimport { KeyValueTagFullViewModal } from './KeyValueTagFullViewModal';\nimport { Interpolation, Theme } from '@emotion/react';\n\n/**\n * An arbitrary number that is used to determine if a tag is too\n * long and should be truncated. We want to avoid short keys or values\n * in a long tag to be truncated\n * */\nconst TRUNCATE_ON_CHARS_LENGTH = 30;\n\nfunction getTruncatedStyles(shouldTruncate = true): Interpolation<Theme> {\n  return shouldTruncate\n    ? {\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        textWrap: 'nowrap',\n        whiteSpace: 'nowrap' as const,\n      }\n    : { whiteSpace: 'nowrap' as const };\n}\n\n/**\n * A <Tag /> wrapper used for displaying key-value entity\n */\nexport const KeyValueTag = ({\n  isClosable = false,\n  onClose,\n  tag,\n  enableFullViewModal = false,\n  charLimit = TRUNCATE_ON_CHARS_LENGTH,\n  maxWidth = 300,\n  className,\n}: {\n  isClosable?: boolean;\n  onClose?: () => void;\n  tag: KeyValueEntity;\n  enableFullViewModal?: boolean;\n  charLimit?: number;\n  maxWidth?: number;\n  className?: string;\n}) => {\n  const intl = useIntl();\n\n  const [isKeyValueTagFullViewModalVisible, setIsKeyValueTagFullViewModalVisible] = useState(false);\n\n  const { shouldTruncateKey, shouldTruncateValue } = getKeyAndValueComplexTruncation(tag, charLimit);\n  const allowFullViewModal = enableFullViewModal && (shouldTruncateKey || shouldTruncateValue);\n\n  const fullViewModalLabel = intl.formatMessage({\n    defaultMessage: 'Click to see more',\n    description: 'Run page > Overview > Tags cell > Tag',\n  });\n\n  return (\n    <div>\n      <Tag\n        componentId=\"codegen_mlflow_app_src_common_components_keyvaluetag.tsx_60\"\n        closable={isClosable}\n        onClose={onClose}\n        title={tag.key}\n        className={className}\n      >\n        <LegacyTooltip title={allowFullViewModal ? fullViewModalLabel : ''}>\n          <span\n            css={{ maxWidth, display: 'inline-flex' }}\n            onClick={() => (allowFullViewModal ? setIsKeyValueTagFullViewModalVisible(true) : undefined)}\n          >\n            <Typography.Text bold title={tag.key} css={getTruncatedStyles(shouldTruncateKey)}>\n              {tag.key}\n            </Typography.Text>\n            {tag.value && (\n              <Typography.Text title={tag.value} css={getTruncatedStyles(shouldTruncateValue)}>\n                : {tag.value}\n              </Typography.Text>\n            )}\n          </span>\n        </LegacyTooltip>\n      </Tag>\n      <div>\n        {isKeyValueTagFullViewModalVisible && (\n          <KeyValueTagFullViewModal\n            tagKey={tag.key}\n            tagValue={tag.value}\n            isKeyValueTagFullViewModalVisible={isKeyValueTagFullViewModalVisible}\n            setIsKeyValueTagFullViewModalVisible={setIsKeyValueTagFullViewModalVisible}\n          />\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport function getKeyAndValueComplexTruncation(\n  tag: KeyValueEntity,\n  charLimit = TRUNCATE_ON_CHARS_LENGTH,\n): { shouldTruncateKey: boolean; shouldTruncateValue: boolean } {\n  const { key, value } = tag;\n  const fullLength = key.length + value.length;\n  const isKeyLonger = key.length > value.length;\n  const shorterLength = isKeyLonger ? value.length : key.length;\n\n  // No need to truncate if tag is short enough\n  if (fullLength <= charLimit) return { shouldTruncateKey: false, shouldTruncateValue: false };\n  // If the shorter string is too long, truncate both key and value.\n  if (shorterLength > charLimit / 2) return { shouldTruncateKey: true, shouldTruncateValue: true };\n\n  // Otherwise truncate the longer string\n  return {\n    shouldTruncateKey: isKeyLonger,\n    shouldTruncateValue: !isKeyLonger,\n  };\n}\n"], "names": ["KeyValueTagsEditorCell", "_ref", "tags", "onAddEdit", "theme", "useDesignSystemTheme", "_jsx", "css", "_css", "display", "flexWrap", "marginRight", "gap", "spacing", "xs", "children", "length", "<PERSON><PERSON>", "componentId", "size", "type", "onClick", "FormattedMessage", "id", "defaultMessage", "_jsxs", "_Fragment", "map", "tag", "KeyValueTag", "key", "value", "icon", "PencilIcon", "COLUMN_IDS", "_ref14", "name", "styles", "ModelVersionTable", "modelName", "modelVersions", "activeStageOnly", "onChange", "modelEntity", "onMetadataUpdated", "usingNextModelsUI", "aliases", "aliasesByV<PERSON><PERSON>", "useMemo", "result", "for<PERSON>ach", "_ref2", "alias", "version", "push", "versions", "filter", "_ref3", "current_stage", "ACTIVE_STAGES", "includes", "intl", "useIntl", "allTagsKeys", "allTagsList", "modelVersion", "flat", "Array", "from", "Set", "_ref4", "sort", "dispatch", "useDispatch", "EditTagsModal", "showEditTagsModal", "useEditKeyValueTagsModal", "allAvailableTags", "saveTagsHandler", "async", "existingTags", "newTags", "updateModelVersionTagsApi", "onSuccess", "EditAliasesModal", "showEditAliasesModal", "useEditRegisteredModelAliasesModal", "model", "rowSelection", "setRowSelection", "useState", "pagination", "setPagination", "pageSize", "pageIndex", "useEffect", "selectedVersions", "_ref5", "selectedVersionNumbers", "_ref6", "tableColumns", "columns", "STATUS", "enableSorting", "header", "meta", "flexBasis", "general", "heightSm", "flexGrow", "cell", "_ref7", "row", "original", "status", "status_message", "LegacyTooltip", "title", "modelVersionStatusIconTooltips", "Typography", "Text", "ModelVersionStatusIcons", "VERSION", "formatMessage", "className", "accessorKey", "_ref8", "getValue", "values", "link", "chunks", "Link", "to", "ModelRegistryRoutes", "getModelVersionPageRoute", "String", "versionNumber", "CREATION_TIMESTAMP", "min<PERSON><PERSON><PERSON>", "_ref9", "Utils", "formatTimestamp", "USER_ID", "_ref0", "TAGS", "flex", "_ref1", "ALIASES", "multiline", "_ref10", "mvAliases", "ModelVersionTableAliasesCell", "STAGE", "_ref11", "StageTagComponents", "DESCRIPTION", "_ref12", "truncateToFirstLineWithMaxLength", "sorting", "setSorting", "desc", "table", "useReactTable", "data", "state", "getCoreRowModel", "getSortedRowModel", "getPaginationRowModel", "getRowId", "_ref13", "onRowSelectionChange", "onSortingChange", "paginationComponent", "Pagination", "currentPageIndex", "numTotal", "page", "emptyComponent", "Empty", "description", "target", "href", "RegisteringModelDocUrl", "image", "PlusIcon", "Table", "scrollable", "empty", "getRowModel", "rows", "undefined", "someRowsSelected", "getIsSomeRowsSelected", "getIsAllRowsSelected", "TableRow", "<PERSON><PERSON><PERSON><PERSON>", "TableRowSelectCell", "checked", "indeterminate", "getToggleAllRowsSelectedHandler", "getLeafHeaders", "_meta", "TableHeader", "sortable", "column", "getCanSort", "sortDirection", "getIsSorted", "onToggleSort", "getToggleSortingHandler", "columnDef", "flexRender", "getContext", "getIsSelected", "getToggleSelectedHandler", "getAllCells", "_meta2", "_meta3", "_meta4", "TableCell", "StageFilters", "ModelViewImpl", "React", "constructor", "props", "super", "stageFilter", "showDescriptionEditor", "isDeleteModalVisible", "isDeleteModalConfirmLoading", "runsSelected", "isTagsRequestPending", "updatingEmailPreferences", "formRef", "handleStageFilterChange", "e", "this", "setState", "handleCancelEditDescription", "handleSubmitEditDescription", "handleEditDescription", "then", "startEditingDescription", "stopPropagation", "showDeleteModal", "hideDeleteModal", "showConfirmLoading", "hideConfirmLoading", "handleDeleteConfirm", "navigate", "handleDelete", "modelListPageRoute", "catch", "logErrorAndNotifyUser", "handleAddTag", "form", "current", "setRegisteredModelTagApi", "resetFields", "ex", "console", "error", "message", "ErrorWrapper", "getMessageField", "displayGlobalErrorNotification", "handleSaveEdit", "handleDeleteTag", "deleteRegisteredModelTagApi", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedRows", "newState", "Object", "assign", "run_id", "renderDetails", "compareDisabled", "keys", "wrapper", "Descriptions", "<PERSON><PERSON>", "label", "creation_timestamp", "last_updated_timestamp", "user_id", "CollapsibleSection", "collapsiblePanel", "renderDescriptionEditIcon", "forceOpen", "defaultCollapsed", "EditableNote", "defaultMarkdown", "onSubmit", "onCancel", "showEditor", "getVisibleTagValues", "EditableTagsTableView", "innerRef", "isRequestPending", "versionsTabButtons", "SegmentedControlGroup", "SegmentedControlButton", "getActiveVersionsCount", "disabled", "onCompare", "shouldShowModelsNextUI", "ModelsNextUIToggleSwitch", "DangerModal", "visible", "confirmLoading", "onOk", "okText", "cancelText", "bind", "componentDidMount", "pageTitle", "updatePageTitle", "v", "getOverflowMenuItems", "itemName", "getCompareModelVersionsPageRoute", "edit<PERSON><PERSON><PERSON>", "renderMainPanel", "render", "breadcrumbs", "<PERSON><PERSON><PERSON><PERSON>", "OverflowMenu", "menu", "mapDispatchToProps", "emailNotificationPreferenceDropdown", "width", "marginBottom", "md", "emailNotificationPreferenceTip", "paddingLeft", "sm", "paddingRight", "height", "buttonHeight", "marginLeft", "alignItems", "<PERSON><PERSON><PERSON><PERSON>", "connect", "mapStateToProps", "ownProps", "getRegisteredModelTags", "withNextModelsUIContext", "injectIntl", "ModelPageImpl", "arguments", "hasUnfilledRequests", "pollIntervalId", "initSearchModelVersionsApiRequestId", "getUUID", "initgetRegisteredModelApiRequestId", "updateRegisteredModelApiId", "deleteRegisteredModelApiId", "criticalInitialRequestIds", "updateRegisteredModelApi", "loadData", "deleteRegisteredModelApi", "isInitialLoading", "promise<PERSON><PERSON><PERSON>", "getRegisteredModelApi", "searchModelVersionsApi", "Promise", "all", "pollData", "isBrowserTabVisible", "getErrorCode", "resolve", "setInterval", "POLL_INTERVAL", "componentWillUnmount", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>", "RequestStateWrapper", "requestIds", "loading", "<PERSON><PERSON><PERSON><PERSON>", "requests", "shouldRender404", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusCode", "subMessage", "fallbackHomePageReactRoute", "permissionDeniedErrors", "request", "_request$error", "ErrorCodes", "PERMISSION_DENIED", "_permissionDeniedErro", "errorMsg", "triggerError", "Spinner", "ModelPageWithRouter", "withRouterNext", "decodeURIComponent", "params", "entities", "modelByName", "getModelVersions", "ModelPage", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "MODEL_REGISTRY", "getDropdownMenu", "searchValue", "toLowerCase", "sortedIndexOf", "isValidTag<PERSON>ey", "test", "flattenOptions", "style", "color", "colors", "actionTertiaryTextDefault", "actionDisabledText", "placement", "<PERSON><PERSON><PERSON>", "groupOption", "DropdownMenu", "TagKeySelectDropdown", "control", "onKeyChangeCallback", "isOpen", "setIsOpen", "selectRef", "useRef", "field", "fieldState", "useController", "rules", "required", "LegacySelect", "allowClear", "ref", "dangerouslySetAntdProps", "showSearch", "dropdownRender", "placeholder", "defaultValue", "open", "onDropdownVisibleChange", "filterOption", "input", "option", "onSelect", "onClear", "handleClear", "validationState", "Option", "getTagsMap", "Map", "valueRequired", "editedEntityRef", "errorMessage", "setErrorMessage", "initialTags", "setInitialTags", "finalTags", "setFinalTags", "showModal", "setShowModal", "useForm", "defaultValues", "hideModal", "useCallback", "editedEntity", "reset", "saveTags", "setIsLoading", "_e$getUserVisibleErro", "getUserVisibleError", "formValues", "watch", "isLoading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isEqual", "sortBy", "isDirty", "showPopoverMessage", "Modal", "destroyOnClose", "footer", "RestoreAntDDefaultClsPrefix", "dangerouslyUseFocusPseudoClass", "UnsavedTagPopoverTrigger", "onSaveTask", "handleSubmit", "trim", "newEntries", "set", "FormUI", "Label", "htmlFor", "_tag$value", "get", "setValue", "RHFControlledComponents", "Input", "htmlType", "Message", "rowGap", "marginTop", "isClosable", "onClose", "currentFinalTags", "delete", "handleTagDelete", "fullTagDisplay", "truncate", "shownText", "Popover", "Root", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Content", "align", "Paragraph", "Close", "Arrow", "lazyPako", "str", "max<PERSON><PERSON><PERSON>", "truncated", "_", "char", "join", "middleTruncateStr", "maxLen", "firstPartLen", "Math", "floor", "lastPartLen", "substring", "_keyStr", "btoaUtf8", "output", "i", "_utf8_encode", "chr1", "charCodeAt", "chr2", "chr3", "enc1", "enc2", "enc3", "enc4", "isNaN", "char<PERSON>t", "atobUtf8", "replace", "indexOf", "fromCharCode", "_utf8_decode", "utftext", "n", "c", "string", "c2", "c3", "getStringSHA256", "crypto", "subtle", "digest", "TextEncoder", "encode", "arrayBuffer", "prototype", "call", "Uint8Array", "x", "toString", "slice", "COMPRESSED_TEXT_DEFLATE_PREFIX", "textCompressDeflate", "binaryData", "deflate", "text", "<PERSON><PERSON><PERSON>", "b64encoded", "binaryString", "byte", "fromCodePoint", "btoa", "textDecompressDeflate", "pako", "compressedText", "startsWith", "Error", "compressedTextWithoutPrefix", "inflate", "atob", "m", "_m$codePointAt", "codePointAt", "isTextCompressedDeflate", "Copy<PERSON><PERSON><PERSON>", "copyText", "showLabel", "buttonProps", "showTooltip", "setShowTooltip", "handleClick", "navigator", "clipboard", "writeText", "setTimeout", "onMouseLeave", "handleMouseLeave", "max<PERSON><PERSON><PERSON>", "columnGap", "ModelVersionAliasTag", "KeyValueTagFullViewModal", "isKeyValueTagFullViewModalVisible", "setIsKeyValueTagFullViewModalVisible", "backgroundColor", "backgroundPrimary", "whiteSpace", "wordBreak", "tagValue", "CopyIcon", "TRUNCATE_ON_CHARS_LENGTH", "getTruncatedStyles", "overflow", "textOverflow", "textWrap", "enableFullViewModal", "charLimit", "shouldTruncateKey", "shouldTruncateValue", "full<PERSON>ength", "is<PERSON>ey<PERSON>onger", "<PERSON><PERSON><PERSON><PERSON>", "getKeyAndValueComplexTruncation", "allowFullViewModal", "fullViewModalLabel", "Tag", "closable", "bold"], "sourceRoot": ""}