{"version": 3, "file": "static/js/8245.96b6bfe5.chunk.js", "mappings": "4WA0BAA,EAAAA,GAAMC,oBAAoBC,UAAY,+BAUtC,MAAMC,UAA4BC,EAAAA,UAAwBC,WAAAA,GAAA,SAAAC,WAAA,KACxDC,MAAQ,CACNC,SAAS,EACTC,WAAOC,EACPC,aAASD,EACTE,YAAa,EACbC,SAAU,GACV,KA8BFC,sBAAwBC,IAAwB,IAAvB,SAAEF,GAAeE,EACxCC,KAAKC,SAAS,CAAEJ,YAAW,EAC3B,KAEFK,oBAAuBT,IACrBU,EAAAA,EAAMC,sBAAsB,IAAIC,EAAAA,EAAaZ,GAAO,EACpD,KAEFa,aAAe,CAACC,EAAoBC,KAClCR,KAAKC,SAAS,CAAEL,YAAaW,GAAgB,EAC7C,KAEFE,UAAY,KAERC,EAAAA,EAAAA,GAACC,EAAAA,SAAc,CAAAC,UACbC,EAAAA,EAAAA,IAAA,OAAKC,UAAU,aAAYF,SAAA,EACzBF,EAAAA,EAAAA,GAAA,OAAKI,UAAU,YAAWF,UACxBF,EAAAA,EAAAA,GAACK,EAAAA,IACC,CACAC,QAAM,EACNC,iBAAkBjB,KAAKT,MAAMK,YAC7BsB,SAAUlB,KAAKT,MAAMM,SACrBsB,SAAU,EACVC,SAAUpB,KAAKM,aAKfe,wBAAyB,CAAEL,QAAQ,QAGvCN,EAAAA,EAAAA,GAAA,OAAKI,UAAU,WAAUF,UACvBF,EAAAA,EAAAA,GAACY,EAAAA,GAAQ,CACPC,KAAMvB,KAAKT,MAAMI,QACjB6B,cAAexB,KAAKF,sBACpB2B,YAAazB,KAAKE,oBAClBV,SAASkB,EAAAA,EAAAA,GAACgB,EAAAA,EAAO,IAAId,UAErBF,EAAAA,EAAAA,GAACiB,EAAAA,GAAI,CAACC,WAAY5B,KAAKT,MAAMK,YAAaJ,SAASkB,EAAAA,EAAAA,GAACgB,EAAAA,EAAO,cAMrE,CAnEFG,QAAAA,GAAY,IAADC,EAAAC,EACT,MAAM,KAAEC,EAAI,QAAEC,EAAO,mBAAEC,EAAkB,cAAEC,EAAa,aAAEC,GAAiBpC,KAAKqC,MAGlE,QADdP,GAAAC,EAAA/B,KAAKqC,OACFC,mBAAW,IAAAR,GADdA,EAAAS,KAAAR,EACiB,CAAEC,OAAMC,UAASC,qBAAoBC,gBAAeC,gBAAgBI,EAAAA,IAClFC,MAAMC,IACL1C,KAAKC,SAAS,CAAEN,QAAS,CAAEgD,KAAMD,GAAmBlD,SAAS,GAAQ,IAEtEoD,OAAOnD,IACNO,KAAKC,SAAS,CAAER,MAAOA,EAAOD,SAAS,GAAQ,GAErD,CAEAqD,iBAAAA,GACE7C,KAAK6B,UACP,CAEAiB,kBAAAA,CAAmBC,GACb/C,KAAKqC,MAAML,OAASe,EAAUf,MAAQhC,KAAKqC,MAAMJ,UAAYc,EAAUd,SACzEjC,KAAK6B,UAET,CAgDAmB,MAAAA,GACE,OAAIhD,KAAKT,MAAMC,SACNkB,EAAAA,EAAAA,GAACuC,EAAAA,EAAoB,CAACnC,UAAU,8BAErCd,KAAKT,MAAME,OACNiB,EAAAA,EAAAA,GAACwC,EAAAA,EAAsB,CAACpC,UAAU,6BAElCJ,EAAAA,EAAAA,GAAA,OAAKI,UAAU,sBAAqBF,SAAEZ,KAAKS,aAEtD,EA5FItB,EASGgE,aAAe,CACpBb,YAAac,EAAAA,GAqFjB,O", "sources": ["experiment-tracking/components/artifact-view-components/ShowArtifactPdfView.tsx"], "sourcesContent": ["/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\n// @ts-expect-error TS(7016): Could not find a declaration file for module 'reac... Remove this comment to see the full error message\nimport { Document, Page, pdfjs } from 'react-pdf';\nimport { Pagination, Spinner } from '@databricks/design-system';\nimport {\n  getArtifactBytesContent,\n  getArtifactLocationUrl,\n  getLoggedModelArtifactLocationUrl,\n} from '../../../common/utils/ArtifactUtils';\nimport './ShowArtifactPdfView.css';\nimport Utils from '../../../common/utils/Utils';\nimport { ErrorWrapper } from '../../../common/utils/ErrorWrapper';\nimport { ArtifactViewSkeleton } from './ArtifactViewSkeleton';\nimport { ArtifactViewErrorState } from './ArtifactViewErrorState';\nimport type { LoggedModelArtifactViewerProps } from './ArtifactViewComponents.types';\nimport { fetchArtifactUnified, type FetchArtifactUnifiedFn } from './utils/fetchArtifactUnified';\n\n// See: https://github.com/wojtekmaj/react-pdf/blob/master/README.md#enable-pdfjs-worker for how\n// workerSrc is supposed to be specified.\npdfjs.GlobalWorkerOptions.workerSrc = `./static-files/pdf.worker.js`;\n\ntype Props = {\n  runUuid: string;\n  path: string;\n  getArtifact: FetchArtifactUnifiedFn;\n} & LoggedModelArtifactViewerProps;\n\ntype State = any;\n\nclass ShowArtifactPdfView extends Component<Props, State> {\n  state = {\n    loading: true,\n    error: undefined,\n    pdfData: undefined,\n    currentPage: 1,\n    numPages: 1,\n  };\n\n  static defaultProps = {\n    getArtifact: fetchArtifactUnified,\n  };\n\n  /** Fetches artifacts and updates component state with the result */\n  fetchPdf() {\n    const { path, runUuid, isLoggedModelsMode, loggedModelId, experimentId } = this.props;\n\n    this.props\n      .getArtifact?.({ path, runUuid, isLoggedModelsMode, loggedModelId, experimentId }, getArtifactBytesContent)\n      .then((artifactPdfData: any) => {\n        this.setState({ pdfData: { data: artifactPdfData }, loading: false });\n      })\n      .catch((error: any) => {\n        this.setState({ error: error, loading: false });\n      });\n  }\n\n  componentDidMount() {\n    this.fetchPdf();\n  }\n\n  componentDidUpdate(prevProps: Props) {\n    if (this.props.path !== prevProps.path || this.props.runUuid !== prevProps.runUuid) {\n      this.fetchPdf();\n    }\n  }\n\n  onDocumentLoadSuccess = ({ numPages }: any) => {\n    this.setState({ numPages });\n  };\n\n  onDocumentLoadError = (error: any) => {\n    Utils.logErrorAndNotifyUser(new ErrorWrapper(error));\n  };\n\n  onPageChange = (newPageNumber: any, itemsPerPage: any) => {\n    this.setState({ currentPage: newPageNumber });\n  };\n\n  renderPdf = () => {\n    return (\n      <React.Fragment>\n        <div className=\"pdf-viewer\">\n          <div className=\"paginator\">\n            <Pagination\n              // @ts-expect-error TS(2322): Type '{ simple: true; currentPageIndex: number; nu... Remove this comment to see the full error message\n              simple\n              currentPageIndex={this.state.currentPage}\n              numTotal={this.state.numPages}\n              pageSize={1}\n              onChange={this.onPageChange}\n              /*\n               * Currently DuBois pagination does not natively support\n               * \"simple\" mode which is required here, hence `dangerouslySetAntdProps`\n               */\n              dangerouslySetAntdProps={{ simple: true }}\n            />\n          </div>\n          <div className=\"document\">\n            <Document\n              file={this.state.pdfData}\n              onLoadSuccess={this.onDocumentLoadSuccess}\n              onLoadError={this.onDocumentLoadError}\n              loading={<Spinner />}\n            >\n              <Page pageNumber={this.state.currentPage} loading={<Spinner />} />\n            </Document>\n          </div>\n        </div>\n      </React.Fragment>\n    );\n  };\n\n  render() {\n    if (this.state.loading) {\n      return <ArtifactViewSkeleton className=\"artifact-pdf-view-loading\" />;\n    }\n    if (this.state.error) {\n      return <ArtifactViewErrorState className=\"artifact-pdf-view-error\" />;\n    } else {\n      return <div className=\"pdf-outer-container\">{this.renderPdf()}</div>;\n    }\n  }\n}\n\nexport default ShowArtifactPdfView;\n"], "names": ["pdfjs", "GlobalWorkerOptions", "workerSrc", "ShowArtifactPdfView", "Component", "constructor", "arguments", "state", "loading", "error", "undefined", "pdfData", "currentPage", "numPages", "onDocumentLoadSuccess", "_ref", "this", "setState", "onDocumentLoadError", "Utils", "logErrorAndNotifyUser", "ErrorWrapper", "onPageChange", "newPageNumber", "itemsPerPage", "renderPdf", "_jsx", "React", "children", "_jsxs", "className", "Pagination", "simple", "currentPageIndex", "numTotal", "pageSize", "onChange", "dangerouslySetAntdProps", "Document", "file", "onLoadSuccess", "onLoadError", "Spinner", "Page", "pageNumber", "fetchPdf", "_this$props$getArtifa", "_this$props", "path", "runUuid", "isLoggedModelsMode", "loggedModelId", "experimentId", "props", "getArtifact", "call", "getArtifactBytesContent", "then", "artifactPdfData", "data", "catch", "componentDidMount", "componentDidUpdate", "prevProps", "render", "ArtifactViewSkeleton", "ArtifactViewErrorState", "defaultProps", "fetchArtifactUnified"], "sourceRoot": ""}