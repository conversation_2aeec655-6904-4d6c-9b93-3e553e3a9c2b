# Generated by Django 5.1.9 on 2025-06-09 14:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("grade", "0004_answerupload_organization_and_more"),
        ("organization", "0007_studentinvitation"),
    ]

    operations = [
        migrations.AddField(
            model_name="answerupload",
            name="organization_test",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="organization.test",
            ),
        ),
        migrations.AlterField(
            model_name="answerupload",
            name="user_id",
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
