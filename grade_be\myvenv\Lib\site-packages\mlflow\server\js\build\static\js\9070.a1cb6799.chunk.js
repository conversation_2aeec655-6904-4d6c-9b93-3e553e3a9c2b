"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[9070],{8220:function(e,t,i){i.d(t,{$:function(){return h},d:function(){return p}});var n=i(89555),r=i(48012),o=i(32599),s=i(88443),a=i(50111);const l=e=>{let{count:t}=e;return(0,a.Y)(a.FK,{children:new Array(t).fill("").map(((e,t)=>(0,a.Y)(r.I_K,{seed:t.toString(),label:0===t?(0,a.Y)(s.A,{id:"v+0HXI",defaultMessage:"Artifact loading"}):void 0},t)))})};var d={name:"36bnqj",styles:"display:flex;flex:1"},c={name:"82a6rk",styles:"flex:1"};const p=()=>{const{theme:e}=(0,o.u)();return(0,a.FD)("div",{css:d,children:[(0,a.Y)("div",{css:c,children:(0,a.Y)("div",{css:(0,n.AH)({margin:e.spacing.sm},""),children:(0,a.Y)(l,{count:9})})}),(0,a.Y)("div",{css:(0,n.AH)({flex:3,borderLeft:`1px solid ${e.colors.border}`},""),children:(0,a.FD)("div",{css:(0,n.AH)({margin:e.spacing.sm},""),children:[(0,a.Y)(r.oud,{css:(0,n.AH)({marginBottom:e.spacing.md},"")}),(0,a.Y)(l,{count:3}),(0,a.Y)("div",{css:(0,n.AH)({width:"75%",marginTop:e.spacing.md},""),children:(0,a.Y)(l,{count:3})})]})})]})},h=e=>{const{theme:t}=(0,o.u)();return(0,a.Y)("div",{"data-testid":"artifact-view-skeleton",css:(0,n.AH)({margin:t.spacing.md},""),...e,children:(0,a.Y)(l,{count:9})})}},16407:function(e,t,i){i.d(t,{i:function(){return C}});var n=i(89555),r=i(31014),o=i(32599),s=i(48012),a=i(15579),l=i(41028),d=i(47664),c=i(88464),p=i(88443),h=i(9133),u=i(82638),g=i(50111);const{Text:m}=o.T;function f(e,t){const{type:i}=e,n=" ".repeat(2*t);if("object"===i){return`${n}{\n${Object.keys(e.properties).map((i=>{const n=e.properties[i],r=n.required?"":" (optional)",o=f(n,t+1),s=2*(t+1);return`${" ".repeat(s)}${i}: ${o.slice(s)+r}`})).join(",\n")}\n${n}}`}if("array"===i){const i=2*t;return`${n}Array(${f(e.items,t).slice(i)})`}return`${n}${i}`}var v={name:"hhyoc3",styles:"margin-left:32px"};function Y(e){let{spec:t}=e,i=!0;void 0!==t.required?({required:i}=t):void 0!==t.optional&&t.optional&&(i=!1);const n=i?(0,g.Y)(m,{bold:!0,children:"(required)"}):(0,g.Y)(m,{color:"secondary",children:"(optional)"}),r="name"in t?t.name:"-";return(0,g.FD)(m,{css:v,children:[r," ",n]})}function x(e){let{spec:t}=e;const{theme:i}=(0,o.u)(),r="tensor"===t.type?`Tensor (dtype: ${(s=t)["tensor-spec"].dtype}, shape: [${s["tensor-spec"].shape}])`:f(t,0);var s;return(0,g.Y)("pre",{css:(0,n.AH)({whiteSpace:"pre-wrap",padding:i.spacing.sm,marginTop:i.spacing.sm,marginBottom:i.spacing.sm},""),children:r})}var w={name:"1q1lx84",styles:"flex:2;align-items:center"},y={name:"qejc9x",styles:"flex:3;align-items:center"};const A=e=>{let{schemaData:t}=e;const i=(0,h.isEmpty)(t),n=(0,c.A)(),m=Boolean(t&&t.length>100),[f,v]=(0,r.useState)(""),A=(0,u.t)(f),M=(0,r.useMemo)((()=>{if(!m)return t;const e=A.toLowerCase();return null===t||void 0===t?void 0:t.filter((t=>"name"in t&&t.name.toLowerCase().includes(e))).slice(0,100)}),[t,A,m]);return i?(0,g.Y)(s.Hjg,{children:(0,g.Y)(s.nA6,{children:(0,g.Y)(p.A,{id:"Wd8Tga",defaultMessage:"No schema. See <link>MLflow docs</link> for how to include input and output schema with your model.",values:{link:e=>(0,g.Y)("a",{href:d.zR,target:"_blank",rel:"noreferrer",children:e})}})})}):(0,g.FD)(g.FK,{children:[m&&(0,g.FD)(g.FK,{children:[(0,g.Y)(a.S,{}),(0,g.Y)(o.T.Hint,{children:(0,g.Y)(p.A,{id:"UYSMKb",defaultMessage:"Schema is too large to display all rows. Please search for a column name to filter the results. Currently showing {currentResults} results from {allResults} total rows.",values:{currentResults:null===M||void 0===M?void 0:M.length,allResults:null===t||void 0===t?void 0:t.length}})}),(0,g.Y)(a.S,{}),(0,g.Y)(l.I,{placeholder:n.formatMessage({id:"6BqLF2",defaultMessage:"Search for a field"}),componentId:"mlflow.schema_table.search_input",value:f,onChange:e=>v(e.target.value)}),(0,g.Y)(a.S,{})]}),null===M||void 0===M?void 0:M.map(((e,t)=>(0,g.FD)(s.Hjg,{children:[(0,g.Y)(s.nA6,{css:w,children:(0,g.Y)(Y,{spec:e})}),(0,g.Y)(s.nA6,{css:y,children:(0,g.Y)(x,{spec:e})})]},t)))]})};var M={name:"ddxhyk",styles:"max-width:800px"},b={name:"1189xqs",styles:"flex:2"},S={name:"15hibo9",styles:"flex:3"},k={name:"e0dnmk",styles:"cursor:pointer"},F={name:"e0dnmk",styles:"cursor:pointer"};const C=e=>{let{schema:t,defaultExpandAllRows:i}=e;const{theme:a}=(0,o.u)(),[l,d]=(0,r.useState)(i),[c,h]=(0,r.useState)(i);return(0,g.FD)(s.XIK,{css:M,children:[(0,g.FD)(s.Hjg,{isHeader:!0,children:[(0,g.Y)(s.A0N,{componentId:"mlflow.schema_table.header.name",css:b,children:(0,g.Y)(m,{bold:!0,css:(0,n.AH)({paddingLeft:a.spacing.lg+a.spacing.xs},""),children:(0,g.Y)(p.A,{id:"Nj6Ez5",defaultMessage:"Name"})})}),(0,g.Y)(s.A0N,{componentId:"mlflow.schema_table.header.type",css:S,children:(0,g.Y)(m,{bold:!0,children:(0,g.Y)(p.A,{id:"2f2qeb",defaultMessage:"Type"})})})]}),(0,g.FD)(g.FK,{children:[(0,g.Y)(s.Hjg,{onClick:()=>d(!l),css:k,children:(0,g.Y)(s.nA6,{children:(0,g.FD)("div",{css:(0,n.AH)({display:"flex",alignItems:"center",gap:a.spacing.xs},""),children:[(0,g.Y)("div",{css:(0,n.AH)({width:a.spacing.lg,height:a.spacing.lg,display:"flex",alignItems:"center",justifyContent:"center",svg:{color:a.colors.textSecondary}},""),children:l?(0,g.Y)(s.NEo,{}):(0,g.Y)(s.Xeq,{})}),(0,g.Y)(p.A,{id:"sxlGRc",defaultMessage:"Inputs ({numInputs})",values:{numInputs:t.inputs.length}})]})})}),l&&(0,g.Y)(A,{schemaData:t.inputs}),(0,g.Y)(s.Hjg,{onClick:()=>h(!c),css:F,children:(0,g.Y)(s.nA6,{children:(0,g.FD)("div",{css:(0,n.AH)({display:"flex",alignItems:"center",gap:a.spacing.xs},""),children:[(0,g.Y)("div",{css:(0,n.AH)({width:a.spacing.lg,height:a.spacing.lg,display:"flex",alignItems:"center",justifyContent:"center",svg:{color:a.colors.textSecondary}},""),children:c?(0,g.Y)(s.NEo,{}):(0,g.Y)(s.Xeq,{})}),(0,g.Y)(p.A,{id:"llVQ2r",defaultMessage:"Outputs ({numOutputs})",values:{numOutputs:t.outputs.length}})]})})}),c&&(0,g.Y)(A,{schemaData:t.outputs})]})]})}},17275:function(e,t,i){i.d(t,{K:function(){return c}});var n=i(90725),r=i(64408),o=i(34860),s=i(32599),a=i(31014),l=i(88443),d=i(50111);const c=e=>{let{data:t,onToggleTreebeard:i}=e;const{theme:r}=(0,s.u)(),o=(0,a.useMemo)((()=>p(r)),[r]);return(0,d.Y)(n.Treebeard,{data:t,onToggle:i,style:o,decorators:n.decorators})};n.decorators.Header=e=>{let t,{style:i,node:n}=e;if(n.children)t="folder";else{const e=(0,r.uQ)(n.name);t=r.f9.has(e)?"file-image-o":r.GE.has(e)?"file-excel-o":r.lg.has(e)?"file-code-o":"file-text-o"}const o=`fa fa-${t}`,s=n.children?{marginRight:"5px"}:{marginRight:"5px",marginLeft:"19px"};return(0,d.Y)("div",{style:i.base,"data-test-id":"artifact-tree-node","artifact-name":n.name,"aria-label":n.name,children:(0,d.FD)("div",{style:i.title,children:[(0,d.Y)("i",{className:o,style:s}),n.name]})})},n.decorators.Loading=e=>{let{style:t}=e;return(0,d.FD)("div",{style:t,children:[(0,d.Y)("img",{alt:"",className:"loading-spinner",src:o}),(0,d.Y)(l.A,{id:"RlRsah",defaultMessage:"loading..."})]})};const p=e=>({tree:{base:{listStyle:"none",margin:0,padding:0,backgroundColor:e.colors.backgroundPrimary,color:e.colors.textPrimary,fontSize:e.typography.fontSizeMd,maxWidth:"500px",height:"100%",overflow:"scroll"},node:{base:{position:"relative"},link:{cursor:"pointer",position:"relative",padding:"0px 5px",display:"block"},activeLink:{background:e.isDarkMode?e.colors.grey700:e.colors.grey300},toggle:{base:{position:"relative",display:"inline-block",verticalAlign:"top",marginLeft:"-5px",height:"24px",width:"24px"},wrapper:{position:"absolute",top:"50%",left:"50%",margin:"-12px 0 0 -4px",height:"14px"},height:7,width:7,arrow:{fill:"#7a7a7a",strokeWidth:0}},header:{base:{display:"inline-block",verticalAlign:"top",color:e.colors.textPrimary},connector:{width:"2px",height:"12px",borderLeft:"solid 2px black",borderBottom:"solid 2px black",position:"absolute",top:"0px",left:"-21px"},title:{lineHeight:"24px",verticalAlign:"middle"}},subtree:{listStyle:"none",paddingLeft:"19px"}}}})},38235:function(e,t,i){i.d(t,{i:function(){return c}});var n=i(89555),r=(i(31014),i(32599)),o=i(48012),s=i(37616),a=i(56412),l=i(50111);var d={name:"bjn8wh",styles:"position:relative"};const c=e=>{let{code:t}=e;const{theme:i}=(0,r.u)();return(0,l.FD)("div",{css:d,children:[(0,l.Y)(a.i,{css:(0,n.AH)({zIndex:1,position:"absolute",top:i.spacing.xs,right:i.spacing.xs},""),showLabel:!1,copyText:t,icon:(0,l.Y)(o.TdU,{})}),(0,l.Y)(s.z7,{language:"python",showLineNumbers:!1,style:{padding:i.spacing.sm,color:i.colors.textPrimary,backgroundColor:i.colors.backgroundSecondary,whiteSpace:"pre-wrap"},wrapLongLines:!0,children:t})]})}},57596:function(e,t,i){i.d(t,{F:function(){return l}});var n=i(48012),r=i(32599),o=(i(31014),i(88443)),s=i(50111);var a={name:"r3950p",styles:"flex:1;display:flex;justify-content:center;align-items:center"};const l=e=>{let{description:t,title:i,...l}=e;return(0,s.Y)("div",{css:a,...l,children:(0,s.Y)(n.SvL,{image:(0,s.Y)(r.j,{}),title:null!==i&&void 0!==i?i:(0,s.Y)(o.A,{id:"Gm77Ws",defaultMessage:"Loading artifact failed"}),description:t})})}},62680:function(e,t,i){i.d(t,{A:function(){return Se}});var n=i(31014),r=i(64408),o=i(98590),s=i(25866),a=i(48012),l=i(77484),d=i(46536),c=i(69650),p=i(50111);var h={name:"82a6rk",styles:"flex:1"};const u={imageOuterContainer:{padding:"10px",overflow:"scroll",background:"white",minHeight:"100%"},imageWrapper:{display:"inline-block"},image:{cursor:"pointer","&:hover":{boxShadow:"0 0 4px gray"}},hidden:{display:"none"}};var g=e=>{let{experimentId:t,runUuid:i,path:r,getArtifact:o=l.xC,isLoggedModelsMode:s,loggedModelId:g}=e;const[m,f]=(0,n.useState)(!0),[v,Y]=(0,n.useState)(!1),[x,w]=(0,n.useState)(null);return(0,n.useEffect)((()=>{f(!0),(0,c.F)({runUuid:i,path:r,isLoggedModelsMode:s,loggedModelId:g,experimentId:t},o).then((e=>{const t=r.toLowerCase().endsWith(".svg")?{type:"image/svg+xml"}:void 0;w(URL.createObjectURL(new Blob([new Uint8Array(e)],t))),f(!1)}))}),[i,r,o,s,g,t]),x&&(0,p.Y)("div",{css:h,children:(0,p.FD)("div",{css:u.imageOuterContainer,children:[m&&(0,p.Y)(a.PLz,{active:!0}),(0,p.Y)("div",{css:m?u.hidden:u.imageWrapper,children:(0,p.Y)("img",{alt:r,css:u.image,src:x,onLoad:()=>f(!1),onClick:()=>Y(!0)})}),(0,p.Y)("div",{css:[u.hidden,""],children:(0,p.Y)(d.b,{visible:v,onVisibleChange:Y,children:(0,p.Y)(d._,{src:x})})})]})})},m=i(7315),f=i(26217),v=i(32599),Y=i(8220),x=i(57596);class w extends n.Component{constructor(e){super(e),this.state={loading:!0,error:void 0,text:void 0,path:void 0},this.fetchArtifacts=this.fetchArtifacts.bind(this)}componentDidMount(){this.fetchArtifacts()}componentDidUpdate(e){this.props.path===e.path&&this.props.runUuid===e.runUuid||this.fetchArtifacts()}render(){if(this.state.loading||this.state.path!==this.props.path)return(0,p.Y)(Y.$,{className:"artifact-text-view-loading"});if(this.state.error)return(0,p.Y)(x.F,{className:"artifact-text-view-error"});{const e=(this.props.size||0)>102400?"text":(0,r.Z0)(this.props.path),{theme:t}=this.props.designSystemThemeApi,i={fontFamily:"Source Code Pro,Menlo,monospace",fontSize:t.typography.fontSizeMd,overflow:"auto",marginTop:"0",width:"100%",height:"100%",padding:t.spacing.xs,borderColor:t.colors.borderDecorative,border:"none"},n=this.state.text?function(e,t){if("json"===e){try{const e=JSON.parse(t);return JSON.stringify(e,null,2)}catch(i){}return t}return t}(e,this.state.text):this.state.text,o=t.isDarkMode?f.iU:f.ge;return(0,p.Y)("div",{className:"ShowArtifactPage",children:(0,p.Y)("div",{className:"text-area-border-box",children:(0,p.Y)(m.A,{language:e,style:o,customStyle:i,children:null!==n&&void 0!==n?n:""})})})}}fetchArtifacts(){var e,t;this.setState({loading:!0});const{isLoggedModelsMode:i,loggedModelId:n,path:r,runUuid:o,experimentId:s}=this.props;null===(e=(t=this.props).getArtifact)||void 0===e||e.call(t,{isLoggedModelsMode:i,loggedModelId:n,path:r,runUuid:o,experimentId:s},l.Y0).then((e=>{this.setState({text:e,loading:!1})})).catch((e=>{this.setState({error:e,loading:!1})})),this.setState({path:this.props.path})}}w.defaultProps={getArtifact:c.F};var y=n.memo((0,v.as)(w)),A=i(27705);const M=n.lazy((()=>Promise.all([i.e(1841),i.e(9801)]).then(i.bind(i,79716)))),b=e=>(0,p.Y)(A.g,{children:(0,p.Y)(n.Suspense,{fallback:(0,p.Y)(a.PLz,{active:!0}),children:(0,p.Y)(M,{...e})})});var S=i(28715);class k extends n.Component{constructor(e){super(e),this.state={loading:!0,error:void 0,html:"",path:""},this.getBlobURL=(e,t)=>{const i=new Blob([e],{type:t});return URL.createObjectURL(i)},this.fetchArtifacts=this.fetchArtifacts.bind(this)}componentDidMount(){this.fetchArtifacts()}componentDidUpdate(e){this.props.path===e.path&&this.props.runUuid===e.runUuid||this.fetchArtifacts()}render(){return this.state.loading||this.state.path!==this.props.path?(0,p.Y)(Y.$,{className:"artifact-html-view-loading"}):this.state.error?(console.error("Unable to load HTML artifact, got error "+this.state.error),(0,p.Y)("div",{className:"artifact-html-view-error",children:"Oops we couldn't load your file because of an error."})):(0,p.Y)("div",{className:"artifact-html-view",children:(0,p.Y)(S.A,{url:"",src:this.getBlobURL(this.state.html,"text/html"),width:"100%",height:"100%",id:"html",className:"html-iframe",display:"block",position:"relative",sandbox:"allow-scripts"})})}fetchArtifacts(){var e,t;const{path:i,runUuid:n,isLoggedModelsMode:r,loggedModelId:o,experimentId:s}=this.props;null===(e=(t=this.props).getArtifact)||void 0===e||e.call(t,{path:i,runUuid:n,isLoggedModelsMode:r,loggedModelId:o,experimentId:s},l.Y0).then((e=>{this.setState({html:e,loading:!1,path:this.props.path})})).catch((e=>{this.setState({error:e,loading:!1,path:this.props.path})}))}}k.defaultProps={getArtifact:c.F};var F=k;const C=n.lazy((()=>Promise.all([i.e(3314),i.e(8245)]).then(i.bind(i,97540)))),L=e=>(0,p.Y)(A.g,{children:(0,p.Y)(n.Suspense,{fallback:(0,p.Y)(a.PLz,{active:!0}),children:(0,p.Y)(C,{...e})})}),I=n.lazy((()=>i.e(3334).then(i.bind(i,73334)))),D=e=>(0,p.Y)(A.g,{children:(0,p.Y)(n.Suspense,{fallback:(0,p.Y)(a.PLz,{active:!0}),children:(0,p.Y)(I,{...e})})});var T=i(77948),N=i.n(T),z=i(16407),P=i(47664),R=i(88443),_=i(64912),K=i(38235);const{Paragraph:U,Text:j,Title:H}=v.T;var E={name:"18uqayh",styles:"margin-bottom:16px"},q={name:"18uqayh",styles:"margin-bottom:16px"};class J extends n.Component{constructor(e){super(e),this.state={loading:!0,error:void 0,inputs:void 0,outputs:void 0,flavor:void 0,loader_module:void 0,hasInputExample:!1},this.fetchLoggedModelMetadata=this.fetchLoggedModelMetadata.bind(this)}componentDidMount(){this.fetchLoggedModelMetadata()}componentDidUpdate(e){this.props.path===e.path&&this.props.runUuid===e.runUuid||this.fetchLoggedModelMetadata()}renderModelRegistryText(){return this.props.registeredModelLink?(0,p.Y)(p.FK,{children:(0,p.Y)(R.A,{id:"BQt0TQ",defaultMessage:"This model is also registered to the <link>model registry</link>.",values:{link:e=>(0,p.Y)("a",{href:J.getLearnModelRegistryLinkUrl(),target:"_blank",children:e})}})}):(0,p.Y)(p.FK,{children:(0,p.Y)(R.A,{id:"js4/Y8",defaultMessage:"You can also <link>register it to the model registry</link> to version control",values:{link:e=>(0,p.Y)("a",{href:J.getLearnModelRegistryLinkUrl(),target:"_blank",children:e})}})})}sparkDataFrameCodeText(e){return`import mlflow\nfrom pyspark.sql.functions import struct, col\nlogged_model = '${e}'\n\n# ${this.props.intl.formatMessage({id:"O1rYVN",defaultMessage:"Load model as a Spark UDF. Override result_type if the model does not return double values."})}\nloaded_model = mlflow.pyfunc.spark_udf(spark, model_uri=logged_model)\n\n# ${this.props.intl.formatMessage({id:"OEGyWZ",defaultMessage:"Predict on a Spark DataFrame."})}\ndf.withColumn('predictions', loaded_model(struct(*map(col, df.columns))))`}loadModelCodeText(e,t){return`import mlflow\nlogged_model = '${e}'\n\n# ${this.props.intl.formatMessage({id:"6bmLqb",defaultMessage:"Load model"})}\nloaded_model = mlflow.${t}.load_model(logged_model)\n`}pandasDataFrameCodeText(e){return`import mlflow\nlogged_model = '${e}'\n\n# ${this.props.intl.formatMessage({id:"kV2Dw/",defaultMessage:"Load model as a PyFuncModel."})}\nloaded_model = mlflow.pyfunc.load_model(logged_model)\n\n# ${this.props.intl.formatMessage({id:"IKx3DN",defaultMessage:"Predict on a Pandas DataFrame."})}\nimport pandas as pd\nloaded_model.predict(pd.DataFrame(data))`}mlflowSparkCodeText(e){return`import mlflow\nlogged_model = '${e}'\n\n# ${this.props.intl.formatMessage({id:"NFUJyk",defaultMessage:"Load model"})}\nloaded_model = mlflow.spark.load_model(logged_model)\n\n# ${this.props.intl.formatMessage({id:"ZGrkWk",defaultMessage:"Perform inference via model.transform()"})}\nloaded_model.transform(data)`}validateModelPredict(e){return this.state.hasInputExample?`import mlflow\nfrom mlflow.models import Model\n\nmodel_uri = '${e}'\n# The model is logged with an input example\npyfunc_model = mlflow.pyfunc.load_model(model_uri)\ninput_data = pyfunc_model.input_example\n\n# Verify the model with the provided input data using the logged dependencies.\n# For more details, refer to:\n# https://mlflow.org/docs/latest/models.html#validate-models-before-deployment\nmlflow.models.predict(\n    model_uri=model_uri,\n    input_data=input_data,\n    env_manager="uv",\n)`:`import mlflow\n\nmodel_uri = '${e}'\n\n# Replace INPUT_EXAMPLE with your own input example to the model\n# A valid input example is a data instance suitable for pyfunc prediction\ninput_data = INPUT_EXAMPLE\n\n# Verify the model with the provided input data using the logged dependencies.\n# For more details, refer to:\n# https://mlflow.org/docs/latest/models.html#validate-models-before-deployment\nmlflow.models.predict(\n    model_uri=model_uri,\n    input_data=input_data,\n    env_manager="uv",\n)`}renderNonPyfuncCodeSnippet(){const{flavor:e}=this.state,{runUuid:t,path:i}=this.props,n=`runs:/${t}/${i}`;return"mleap"===e?(0,p.Y)(p.FK,{}):(0,p.FD)(p.FK,{children:[(0,p.Y)(H,{level:3,children:(0,p.Y)(R.A,{id:"2NfDVN",defaultMessage:"Load the model"})}),(0,p.Y)("div",{className:"artifact-logged-model-view-code-content",children:(0,p.FD)("div",{children:[(0,p.Y)(K.i,{code:this.loadModelCodeText(n,e)}),(0,p.Y)(R.A,{id:"bHuphj",defaultMessage:"See the documents below to learn how to customize this model and deploy it for batch or real-time scoring using the pyfunc model flavor."}),(0,p.FD)("ul",{children:[(0,p.Y)("li",{children:(0,p.Y)("a",{href:P.HC,children:"API reference for the mlflow.pyfunc module"})}),(0,p.Y)("li",{children:(0,p.Y)("a",{href:P.A6,children:"Creating custom Pyfunc models"})})]})]})})]})}renderPandasDataFramePrediction(e){return(0,p.FD)("div",{css:E,children:[(0,p.Y)(j,{children:(0,p.Y)(R.A,{id:"GXayIF",defaultMessage:"Predict on a Pandas DataFrame:"})}),(0,p.Y)(K.i,{code:this.pandasDataFrameCodeText(e)})]})}renderPyfuncCodeSnippet(){if("mlflow.spark"===this.state.loader_module)return this.renderMlflowSparkCodeSnippet();const{runUuid:e,path:t}=this.props,i=`runs:/${e}/${t}`;return(0,p.FD)(p.FK,{children:[(0,p.Y)(H,{level:3,children:(0,p.Y)(R.A,{id:"r3/K3V",defaultMessage:"Make Predictions"})}),(0,p.FD)("div",{className:"artifact-logged-model-view-code-content",children:[this.renderPandasDataFramePrediction(i),(0,p.Y)(j,{children:(0,p.Y)(R.A,{id:"6xPc4W",defaultMessage:"Predict on a Spark DataFrame:"})}),(0,p.Y)(K.i,{code:this.sparkDataFrameCodeText(i)})]})]})}renderMlflowSparkCodeSnippet(){const{runUuid:e,path:t}=this.props,i=`runs:/${e}/${t}`;return(0,p.FD)(p.FK,{children:[(0,p.Y)(H,{level:3,children:(0,p.Y)(R.A,{id:"r3/K3V",defaultMessage:"Make Predictions"})}),(0,p.FD)("div",{className:"artifact-logged-model-view-code-content",children:[this.renderPandasDataFramePrediction(i),(0,p.Y)(K.i,{code:this.mlflowSparkCodeText(i)})]})]})}renderModelPredict(e){return(0,p.FD)("div",{css:q,children:[(0,p.Y)(j,{children:(0,p.Y)(R.A,{id:"7Y0hIS",defaultMessage:"Run the following code to validate model inference works on the example input data and logged model dependencies, prior to deploying it to a serving endpoint"})}),(0,p.Y)(K.i,{code:this.validateModelPredict(e)})]})}renderModelPredictCodeSnippet(){const{runUuid:e,path:t}=this.props,i=`runs:/${e}/${t}`;return(0,p.FD)(p.FK,{children:[(0,p.Y)(H,{level:3,children:(0,p.Y)(R.A,{id:"5B5dhT",defaultMessage:"Validate the model before deployment"})}),(0,p.Y)("div",{className:"artifact-logged-model-view-code-content",children:this.renderModelPredict(i)})]})}render(){return this.state.loading?(0,p.Y)(Y.$,{className:"artifact-logged-model-view-loading"}):this.state.error?(0,p.Y)(x.F,{className:"artifact-logged-model-view-error",description:(0,p.Y)(R.A,{id:"81+fJx",defaultMessage:"Couldn't load model information due to an error."})}):(0,p.Y)("div",{className:"ShowArtifactPage",children:(0,p.FD)("div",{className:"show-artifact-logged-model-view",children:[(0,p.FD)("div",{className:"artifact-logged-model-view-header",style:{marginTop:16,marginBottom:16,marginLeft:16},children:[(0,p.Y)(H,{level:2,children:(0,p.Y)(R.A,{id:"z2Gyuw",defaultMessage:"MLflow Model"})}),"pyfunc"===this.state.flavor?(0,p.Y)(R.A,{id:"ySno61",defaultMessage:"The code snippets below demonstrate how to make predictions using the logged model."}):(0,p.Y)(R.A,{id:"25EUlg",defaultMessage:"The code snippets below demonstrate how to load the logged model."})," ",this.renderModelRegistryText()]}),(0,p.Y)("hr",{}),(0,p.FD)("div",{className:"artifact-logged-model-view-schema-table",style:{width:"45%",marginLeft:16,float:"left"},children:[(0,p.Y)(H,{level:3,children:(0,p.Y)(R.A,{id:"27oNFE",defaultMessage:"Model schema"})}),(0,p.Y)("div",{className:"content",children:(0,p.Y)(j,{children:(0,p.Y)(R.A,{id:"GAJL1v",defaultMessage:"Input and output schema for your model. <link>Learn more</link>",values:{link:e=>(0,p.Y)("a",{href:P.$L,target:"_blank",children:e})}})})}),(0,p.Y)("div",{style:{marginTop:12},children:(0,p.Y)(z.i,{schema:{inputs:this.state.inputs,outputs:this.state.outputs},defaultExpandAllRows:!0})})]}),(0,p.FD)("div",{className:"artifact-logged-model-view-code-group",style:{width:"50%",marginRight:16,float:"right"},children:[this.renderModelPredictCodeSnippet(),"pyfunc"===this.state.flavor?this.renderPyfuncCodeSnippet():this.renderNonPyfuncCodeSnippet()]})]})})}fetchLoggedModelMetadata(){const e=`${this.props.path}/${s.eh}`,{getArtifact:t,path:i,runUuid:n,experimentId:r}=this.props;(0,c.F)({path:e,runUuid:n,experimentId:r},t).then((e=>{const t=N().load(e);if(t.signature){const e=Array.isArray(t.signature.inputs)?t.signature.inputs:JSON.parse(t.signature.inputs||"[]"),i=Array.isArray(t.signature.outputs)?t.signature.outputs:JSON.parse(t.signature.outputs||"[]");this.setState({inputs:e,outputs:i})}else this.setState({inputs:"",outputs:""});t.flavors.mleap?this.setState({flavor:"mleap"}):t.flavors.python_function?this.setState({flavor:"pyfunc",loader_module:t.flavors.python_function.loader_module}):this.setState({flavor:Object.keys(t.flavors)[0]}),this.setState({loading:!1}),t.saved_input_example_info&&t.saved_input_example_info.artifact_path&&this.setState({hasInputExample:!0})})).catch((e=>{this.setState({error:e,loading:!1})}))}}J.defaultProps={getArtifact:l.Y0},J.getLearnModelRegistryLinkUrl=()=>P.gw;var G=(0,_.Ay)(J);var B=i.p+"static/media/warning.290a3b14118933547965e91ea61c5a61.svg",Q=i(69869),X=i(89555),V=i(9133),O=i(88464),W=i(70618),Z=i(9856),$=i(54421),ee=i(14343),te=i(40555),ie=i(15230);var ne={name:"1h0bf8r",styles:"body, :host{user-select:none;}"},re={name:"8k1832",styles:"position:relative;display:flex"},oe={name:"111ddn1",styles:"display:flex;justify-content:space-between;flex-shrink:0"},se={name:"1tc6xju",styles:"flex:1;overflow:auto"};const ae=e=>{let{data:t,onClose:i}=e;const{theme:r}=(0,v.u)(),[o,s]=(0,n.useState)(!1);return(0,V.isUndefined)(t)?null:(0,p.FD)("div",{css:(0,X.AH)({display:"flex",height:"100%",flexDirection:"row-reverse",position:"relative",borderLeft:`1px solid ${r.colors.border}`},""),children:[o&&(0,p.Y)(X.mL,{styles:ne}),(0,p.Y)(ie.ResizableBox,{width:200,height:void 0,axis:"x",resizeHandles:["w"],minConstraints:[200,150],maxConstraints:[500,150],onResizeStart:()=>s(!0),onResizeStop:()=>s(!1),handle:(0,p.Y)("div",{css:(0,X.AH)({width:r.spacing.xs,left:-r.spacing.xs/2,height:"100%",position:"absolute",top:0,cursor:"ew-resize","&:hover":{backgroundColor:r.colors.border,opacity:.5}},"")}),css:re,children:(0,p.FD)("div",{css:(0,X.AH)({padding:r.spacing.sm,overflow:"hidden",display:"flex",flexDirection:"column"},""),children:[(0,p.FD)("div",{css:oe,children:[(0,p.Y)(v.T.Title,{level:5,children:(0,p.Y)(R.A,{id:"+dGAnC",defaultMessage:"Preview"})}),(0,p.Y)(v.B,{componentId:"mlflow.run.artifact_view.preview_close",onClick:()=>i(),icon:(0,p.Y)(v.C,{})})]}),!t&&(0,p.Y)(v.T.Text,{color:"secondary",children:(0,p.Y)(R.A,{id:"kjltRf",defaultMessage:"Click a cell to preview data"})}),(0,p.Y)("div",{css:se,children:(0,p.Y)(te.f,{json:t})})]})})]})};var le=i(76010);const de=(e,t)=>""===e?`column-${t+1}`:String(e);var ce={name:"7zbxzd",styles:"flex:1;overflow:hidden;display:flex;flex-direction:column"},pe={name:"1pbmicl",styles:"overflow:auto;flex:1"},he={name:"fmgtfw",styles:".table-header-icon-container{line-height:0;}"};const ue=e=>{let{data:t,runUuid:i}=e;const[r,o]=(0,n.useState)([]),[d,c]=(0,n.useState)(!1),h=(0,O.A)(),{theme:u}=(0,v.u)(),g=160-2*u.spacing.sm,m=(0,n.useRef)(null),[f,Y]=(0,n.useState)({width:0,height:0});(0,n.useEffect)((()=>{if(m.current){const{width:e,height:t}=m.current.getBoundingClientRect();Y({width:e,height:t})}}),[]);const x=(0,n.useMemo)((()=>{var e,i;return null!==(e=null===(i=t.columns)||void 0===i?void 0:i.map(de))&&void 0!==e?e:[]}),[t]),[w,y]=(0,n.useState)([]),[A,M]=(0,n.useState)(void 0),b=(0,n.useMemo)((()=>t.data),[t]),S=(0,n.useMemo)((()=>b.length>0?x.filter(((e,t)=>{if(null!==b[0][t]&&"object"===typeof b[0][t]){const{type:e}=b[0][t];return e===s.Oe}return!1})):[]),[x,b]),k=(0,n.useMemo)((()=>{const e=1+(d?24:32);return S.length>0?Math.floor((f.height-e-48)/160):Math.floor((f.height-e-48)/e)}),[f,S,d]),[F,C]=(0,n.useState)({pageSize:1,pageIndex:0});(0,n.useEffect)((()=>{C((e=>({...e,pageSize:k})))}),[k]);const L=(0,n.useMemo)((()=>x.filter((e=>!w.includes(e))).map((e=>{const t=String(e);return S.includes(e)?{id:t,header:t,accessorKey:t,minSize:100,cell:e=>{try{const t=JSON.parse(e.getValue()),{filepath:n,compressed_filepath:r}=t,o=(0,l.To)(n,i),s=(0,l.To)(r,i);return(0,p.Y)($.TV,{imageUrl:o,compressedImageUrl:s,maxImageSize:g})}catch{return le.A.logErrorAndNotifyUser("Error parsing image data in logged table's image column"),e.getValue()}}}:{id:t,header:t,accessorKey:t,minSize:100}}))),[x,g,S,i,w]),I=(0,n.useMemo)((()=>b.map((e=>{const t={};for(let i=0;i<x.length;i++){const n=e[i];t[x[i]]="string"===typeof n?n:JSON.stringify(n)}return t}))),[b,x]),D=(0,W.N4)({columns:L,data:I,state:{pagination:F,sorting:r},onSortingChange:o,getCoreRowModel:(0,Z.HT)(),getSortedRowModel:(0,Z.h5)(),getPaginationRowModel:(0,Z.kW)(),enableColumnResizing:!0,columnResizeMode:"onChange"}),T=(0,p.Y)(a.dKS,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_artifact-view-components_showartifactloggedtableview.tsx_181",currentPageIndex:F.pageIndex+1,numTotal:b.length,onChange:(e,t)=>{C({pageSize:t||F.pageSize,pageIndex:e-1})},pageSize:F.pageSize});return(0,p.FD)("div",{ref:m,css:(0,X.AH)({paddingLeft:u.spacing.md,height:"100%",display:"flex",gap:u.spacing.xs,overflow:"hidden"},""),children:[(0,p.FD)("div",{css:ce,children:[(0,p.Y)("div",{css:pe,children:(0,p.FD)(a.XIK,{scrollable:!0,size:d?"small":"default",css:he,style:{width:D.getTotalSize()},children:[D.getHeaderGroups().map((e=>(0,p.Y)(a.Hjg,{isHeader:!0,children:e.headers.map(((e,t)=>(0,p.Y)(a.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_artifact-view-components_showartifactloggedtableview.tsx_223",sortable:!0,sortDirection:e.column.getIsSorted()||"none",onToggleSort:e.column.getToggleSortingHandler(),header:e,column:e.column,setColumnSizing:D.setColumnSizing,isResizing:e.column.getIsResizing(),style:{maxWidth:e.column.getSize()},children:(0,W.Kv)(e.column.columnDef.header,e.getContext())},e.id)))},e.id))),D.getRowModel().rows.map((e=>(0,p.Y)(a.Hjg,{children:e.getAllCells().map((e=>(0,p.Y)(a.nA6,{css:(0,X.AH)({maxHeight:160,"&:hover":{backgroundColor:u.colors.tableBackgroundSelectedHover,cursor:"pointer"}},""),onClick:()=>{M(String(e.getValue()))},tabIndex:0,onKeyDown:t=>{let{key:i}=t;"Enter"===i&&M(String(e.getValue()))},style:{maxWidth:e.column.getSize()},children:(0,W.Kv)(e.column.columnDef.cell,e.getContext())},e.id)))},e.id)))]})}),(0,p.Y)("div",{css:(0,X.AH)({display:"flex",justifyContent:"flex-end",paddingBottom:u.spacing.sm,paddingTop:u.spacing.sm},""),children:T})]}),!(0,V.isUndefined)(A)&&(0,p.Y)(ae,{data:A,onClose:()=>M(void 0)}),(0,p.FD)("div",{css:(0,X.AH)({paddingTop:u.spacing.sm,paddingRight:u.spacing.sm,display:"flex",flexDirection:"column",gap:u.spacing.xs},""),children:[(0,p.FD)(a.rId.Root,{modal:!1,children:[(0,p.Y)(a.paO,{title:h.formatMessage({id:"NzRvyj",defaultMessage:"Table settings"}),useAsLabel:!0,children:(0,p.Y)(a.rId.Trigger,{asChild:!0,"aria-label":h.formatMessage({id:"NzRvyj",defaultMessage:"Table settings"}),children:(0,p.Y)(v.B,{componentId:"mlflow.run.artifact_view.table_settings",icon:(0,p.Y)(a.L64,{})})})}),(0,p.FD)(a.rId.Content,{css:(0,X.AH)({maxHeight:10*u.general.heightSm,overflowY:"auto"},""),side:"left",children:[(0,p.Y)(a.rId.Arrow,{}),(0,p.FD)(a.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_artifact-view-components_showartifactloggedtableview.tsx_315",checked:d,onCheckedChange:c,children:[(0,p.Y)(a.rId.ItemIndicator,{}),(0,p.Y)(R.A,{id:"odkg7W",defaultMessage:"Compact view"})]}),(0,p.Y)(a.rId.Separator,{}),(0,p.FD)(a.rId.Group,{children:[(0,p.Y)(a.rId.Label,{children:(0,p.Y)(R.A,{id:"fMf88R",defaultMessage:"Columns"})}),x.map((e=>(0,p.FD)(a.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_artifact-view-components_showartifactloggedtableview.tsx_331",onSelect:e=>e.preventDefault(),checked:!w.includes(e),onCheckedChange:()=>{y((t=>t.includes(e)?t.filter((t=>t!==e)):[...t,e]))},children:[(0,p.Y)(a.rId.ItemIndicator,{}),e]},e)))]})]})]}),(0,p.Y)(ee.k,{onClick:()=>{M((()=>(0,V.isUndefined)(A)?"":void 0))},pressed:!(0,V.isUndefined)(A),componentId:"mlflow.run.artifact_view.preview_sidebar_toggle",icon:(0,p.Y)(a.CPw,{})})]})]})},ge=n.memo((e=>{let{runUuid:t,path:i,isLoggedModelsMode:r,loggedModelId:s,experimentId:d}=e;const[h,u]=(0,n.useState)(!0),[g,m]=(0,n.useState)(),[f,Y]=(0,n.useState)(void 0),[x,w]=(0,n.useState)("");(0,n.useEffect)((()=>{u(!0),(0,c.F)({runUuid:t,path:i,isLoggedModelsMode:r,loggedModelId:s,experimentId:d},l.Y0).then((e=>{u(!1),e&&"string"===typeof e?(w(e),m(void 0)):m(Error("Artifact is not a JSON file"))})).catch((e=>{m(e),u(!1)})),Y(i)}),[i,t,r,s,d]);const y=(0,n.useMemo)((()=>{const e=(0,o.pt)(x);if(e&&(0,V.isArray)(null===e||void 0===e?void 0:e.columns)&&(0,V.isArray)(null===e||void 0===e?void 0:e.data))return e}),[x]),{theme:A}=(0,v.u)(),M=e=>(0,p.Y)("div",{css:(0,X.AH)({padding:A.spacing.md},""),children:(0,p.Y)(a.SvL,{image:(0,p.Y)(v.j,{}),title:(0,p.Y)(R.A,{id:"hnhCDT",defaultMessage:"Error occurred"}),description:e})});return h||i!==f?(0,p.Y)("div",{css:(0,X.AH)({padding:A.spacing.md},""),children:(0,p.Y)(a.QvX,{lines:5})}):g?M(g.message):x?y?(0,p.Y)(ue,{data:y,runUuid:t}):M((0,p.Y)(R.A,{id:"mqDCNl",defaultMessage:"Unable to parse JSON file. The file should contain an object with 'columns' and 'data' keys."})):M(null)}));var me=i(15579);const fe=n.lazy((()=>i.e(847).then(i.bind(i,847)))),ve=e=>(0,p.Y)(A.g,{children:(0,p.Y)(n.Suspense,{fallback:(0,p.Y)(a.PLz,{active:!0}),children:(0,p.Y)(fe,{...e})})});class Ye extends n.Component{render(){if(this.props.path){const{loggedModelId:e,isLoggedModelsMode:t,path:i,runUuid:n,experimentId:a}=this.props,l={loggedModelId:e,isLoggedModelsMode:t,path:i,runUuid:n,experimentId:a},d=(0,r.uQ)(this.props.path);let c;const{modelVersions:h}=this.props;if(h){const[e]=h.filter((e=>e.source.endsWith(`artifacts/${d}`)));if(e){const{name:t,version:i}=e;c=Q.fM.getModelVersionPageRoute(t,i)}}if(this.props.size>50*s.PE)return be();if(this.props.isDirectory){if(this.props.runTags&&(0,o.x1)(this.props.runTags).includes(this.props.path))return(0,p.Y)(G,{runUuid:this.props.runUuid,path:this.props.path,artifactRootUri:this.props.artifactRootUri,registeredModelLink:c,experimentId:this.props.experimentId})}else{if(this.props.showArtifactLoggedTableView)return(0,p.Y)(ge,{...l});if(d){if(r.f9.has(d.toLowerCase()))return(0,p.Y)(g,{...l});if(r.GE.has(d.toLowerCase()))return(0,p.Y)(D,{...l});if(r.lg.has(d.toLowerCase()))return(0,p.Y)(y,{...l,size:this.props.size});if(r.Yj.has(d.toLowerCase()))return(0,p.Y)(b,{...l});if(r.jO.has(d.toLowerCase()))return(0,p.Y)(F,{...l});if(r.Ap.has(d.toLowerCase()))return(0,p.Y)(L,{...l});if(r.In.has(d.toLowerCase()))return(0,p.Y)(ve,{...l})}}}return ye()}}var xe={name:"r3950p",styles:"flex:1;display:flex;justify-content:center;align-items:center"},we={name:"173u6wr",styles:"width:64px;height:64px"};const ye=()=>(0,p.Y)("div",{css:xe,children:(0,p.Y)(a.SvL,{image:(0,p.FD)(p.FK,{children:[(0,p.Y)("img",{alt:"Preview icon.",src:"data:image/png;base64,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",css:we}),(0,p.Y)(me.S,{size:"sm"})]}),title:(0,p.Y)(R.A,{id:"6b6fTN",defaultMessage:"Select a file to preview"}),description:(0,p.Y)(R.A,{id:"VzuUre",defaultMessage:"Supported formats: image, text, html, pdf, audio, geojson files"})})});var Ae={name:"r3950p",styles:"flex:1;display:flex;justify-content:center;align-items:center"},Me={name:"173u6wr",styles:"width:64px;height:64px"};const be=()=>(0,p.Y)("div",{css:Ae,children:(0,p.Y)(a.SvL,{image:(0,p.FD)(p.FK,{children:[(0,p.Y)("img",{alt:"Preview icon.",src:B,css:Me}),(0,p.Y)(me.S,{size:"sm"})]}),title:(0,p.Y)(R.A,{id:"9y+yUQ",defaultMessage:"File is too large to preview"}),description:(0,p.Y)(R.A,{id:"8T27QL",defaultMessage:"Maximum file size for preview: 50MiB"})})});var Se=Ye},64408:function(e,t,i){i.d(t,{Ap:function(){return u},GE:function(){return g},In:function(){return m},Yj:function(){return h},Z0:function(){return o},f9:function(){return d},hA:function(){return n},jO:function(){return p},lg:function(){return c},uQ:function(){return r}});const n=e=>{const t=e.split("/");return t[t.length-1]},r=e=>{const t=e.split(/[./]/);return t[t.length-1]},o=e=>{const t=r(e).toLowerCase();return t in l?l[t]:t},s="mlproject",a="mlmodel",l={[s.toLowerCase()]:"yaml",[a.toLowerCase()]:"yaml"},d=new Set(["jpg","bmp","jpeg","png","gif","svg"]),c=new Set(["txt","log","err","cfg","conf","cnf","cf","ini","properties","prop","hocon","toml","yaml","yml","xml","json","js","py","py3","md","rst",s.toLowerCase(),a.toLowerCase(),"jsonnet"]),p=new Set(["html"]),h=new Set(["geojson"]),u=new Set(["pdf"]),g=new Set(["csv","tsv"]),m=new Set(["m4a","mp3","mp4","wav","aac","wma","flac","opus","ogg"])},69650:function(e,t,i){i.d(t,{F:function(){return r}});var n=i(77484);const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n.Y0;const i=(e=>{const{runUuid:t,path:i,isLoggedModelsMode:r,loggedModelId:o}=e;return r&&o?(0,n.qk)(i,o):(0,n.To)(i,t)})(e);return t(i)}},82638:function(e,t,i){i.d(t,{t:function(){return o}});var n=i(9133),r=i(31014);const o=(0,n.isFunction)(r.useDeferredValue)?r.useDeferredValue:n.identity}}]);
//# sourceMappingURL=9070.a1cb6799.chunk.js.map