# ocr_processor.py
import os
import io
import re
import base64
import json
from collections import defaultdict
from PIL import Image
import fitz  # PyMuPDF
import cv2
from ultralytics import YOLO
from dotenv import load_dotenv
import google.generativeai as genai
import time
from pydantic import BaseModel, ValidationError, RootModel
from typing import List, Dict, Optional, Any, Tuple

# --- Gemini Setup ---
load_dotenv()
genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
GEMINI_MODEL = genai.GenerativeModel("gemini-1.5-flash")
ROLL_REGEX = re.compile(r"\d{2}[nN]\d{3}")


def get_json_extraction_prompt(output_path: str, roll_number: str) -> List[Dict[str, str]]:
    return [
        {
            "text": f"""You are an expert assistant for analyzing student answer scripts. Extract and structure the answers into a valid JSON format. Follow these rules:

1. **Question Identification**
   - Use "Q1", "Q2", etc. If missing, assign "Q_missing_1", etc.
   - Use horizontal/double lines only as separators if no question number is available.

2. **Content Types**
   - {{ "text": "..." }} for regular paragraphs.
   - {{ "equations": [ {{"step": 1, "equation": "..."}} ] }} for math steps.
     - Convert exponents like x² into x^2.
   - {{ "tables": [ {{...}}, {{...}} ] }}
     - First row = header. Use "heading" if any column has no name.
     - Each row is a dictionary with column-value pairs.
   - {{ "bullets": [ "...", "..." ] }} for bullet/numbered lists.
     - If any line starts with •, -, or numbers like 1., 2., treat it as a bullet point.
   - {{ "types": {{ "main_type": "...", "subtypes": [ {{"type": "...", "description": "..."}}, ... ] }} }}

3. **Diagram Detection**
   - If you see a visual diagram, flowchart, figure, or illustration that CANNOT be properly extracted as text/JSON, add:
     "diagram": {{"1": "{output_path}/Q<question_number>_{roll_number}_1.png"}}
   - Do NOT add diagram key for tables, text, or content that can be extracted as JSON
   - Only add diagram key for actual visual diagrams that need image preservation

4. **Rules**
   - Extract content EXACTLY as written by the student
   - Do not correct spelling, grammar, or factual errors
   - Do not add or modify any content
   - Do not mix bullets and text
   - Skip crossed-out or invalid content
   - Maintain content order per question
   - Output only valid JSON, no extra text
   - Start your response with {{ and end with }}
   - Use double quotes for all strings
   - Ensure proper JSON formatting

IMPORTANT: Return ONLY valid JSON. No explanations, no markdown, no extra text. Just the JSON object starting with {{ and ending with }}.
"""
        }
    ]


# Pydantic Models for validation


class EquationStep(BaseModel):
    step: int
    equation: str


class TypesSubtype(BaseModel):
    type: str
    description: str


class TypesModel(BaseModel):
    main_type: str
    subtypes: List[TypesSubtype]


class QuestionContent(BaseModel):
    text: Optional[str] = None
    equations: Optional[List[EquationStep]] = None
    tables: Optional[List[Dict[str, Any]]] = None
    bullets: Optional[List[str]] = None
    types: Optional[TypesModel] = None
    diagram: Optional[Dict[str, str]] = None


class OutputModel(RootModel[Dict[str, QuestionContent]]):
    pass


def crop_top_right(img: Image.Image) -> Image.Image:
    """Extract top-right corner for roll number/page number detection"""
    width, height = img.size
    return img.crop((width // 2, 0, width, height // 5))


def extract_roll_number(img: Image.Image) -> Optional[str]:
    """Extract roll number from image"""
    crop = crop_top_right(img)
    buf = io.BytesIO()
    crop.save(buf, format="PNG")
    img_b64 = base64.b64encode(buf.getvalue()).decode()
    try:
        response = GEMINI_MODEL.generate_content(
            [
                {"inline_data": {"data": img_b64, "mime_type": "image/png"}},
                {
                    "text": "Extract the roll number from the top-right corner. Only return the roll number in format 22N234, 22N235, etc. If not found, return UNKNOWN."
                },
            ]
        )
        match = ROLL_REGEX.search(response.text)
        return match.group(0).upper() if match else None
    except Exception:
        return None


def extract_page_number(img: Image.Image) -> int:
    """Extract page number from image"""
    crop = crop_top_right(img)
    buf = io.BytesIO()
    crop.save(buf, format="PNG")
    img_b64 = base64.b64encode(buf.getvalue()).decode()
    try:
        response = GEMINI_MODEL.generate_content(
            [
                {"inline_data": {"data": img_b64, "mime_type": "image/png"}},
                {
                    "text": "Extract the page number from the top-right corner. Return digits only."
                },
            ]
        )
        return int(re.sub(r"\D", "", response.text))
    except Exception:
        return 9999


def extract_images_from_pdf(pdf_path: str) -> List[Tuple[str, Image.Image]]:
    """Extract images from PDF pages with unique naming"""
    images = []
    doc = fitz.open(pdf_path)
    pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]

    for i, page in enumerate(doc):
        pix = page.get_pixmap()
        img = Image.open(io.BytesIO(pix.tobytes("png")))
        # Create unique filename for each PDF page
        page_name = f"{pdf_name}_page{i+1:03d}.png"
        images.append((page_name, img))

    doc.close()
    return images


def images_to_pdf_bytes(images: List[Image.Image]) -> bytes:
    """Convert PIL images to PDF bytes"""
    if not images:
        return b""
    pil_images = [img.convert("RGB") for img in images]
    buf = io.BytesIO()
    pil_images[0].save(
        buf, format="PDF", save_all=True, append_images=pil_images[1:]
    )
    return buf.getvalue()


def clean_json_response(response_text: str) -> Optional[str]:
    """Improved JSON cleaning with better error handling"""
    if not response_text or response_text.strip() == "":
        return None

    # Remove markdown code blocks
    response_text = re.sub(
        r"```json\s*", "", response_text, flags=re.IGNORECASE
    )
    response_text = re.sub(r"```\s*", "", response_text)
    response_text = response_text.strip()

    # Try to find JSON content between outermost curly braces
    # Handle nested braces properly
    brace_count = 0
    start_idx = -1
    end_idx = -1

    for i, char in enumerate(response_text):
        if char == "{":
            if brace_count == 0:
                start_idx = i
            brace_count += 1
        elif char == "}":
            brace_count -= 1
            if brace_count == 0 and start_idx != -1:
                end_idx = i
                break

    if start_idx != -1 and end_idx != -1:
        json_str = response_text[start_idx : end_idx + 1]
        return json_str

    # Fallback: try to find any JSON-like structure
    json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
    if json_match:
        return json_match.group(0)

    return response_text.strip()


def fix_json_format(json_str: str) -> str:
    """Fix common JSON formatting issues"""
    if not json_str:
        return json_str

    # Fix single quotes to double quotes (but be careful with apostrophes in
    # content)
    json_str = re.sub(r"'([^']*)':", r'"\1":', json_str)  # Fix keys
    json_str = re.sub(
        r":\s*'([^']*)'", r': "\1"', json_str
    )  # Fix simple string values

    # Fix trailing commas
    json_str = re.sub(r",\s*}", "}", json_str)
    json_str = re.sub(r",\s*]", "]", json_str)

    return json_str


def convert_answers_format(raw_json: str) -> str:
    """Convert and validate JSON response with enhanced error handling"""
    print(f"Raw JSON length: {len(raw_json) if raw_json else 0}")

    if not raw_json or raw_json.strip() == "":
        return json.dumps({"error": "Empty response from Gemini"}, indent=2)

    try:
        # Step 1: Clean the response
        cleaned_json = clean_json_response(raw_json)
        if not cleaned_json:
            print("Could not extract JSON from response")
            return json.dumps(
                {
                    "error": "Could not extract JSON from response",
                    "raw_response_preview": raw_json[:500],
                },
                indent=2,
            )

        print(f"Cleaned JSON length: {len(cleaned_json)}")

        # Step 2: Fix common formatting issues
        fixed_json = fix_json_format(cleaned_json)

        # Step 3: Parse JSON
        try:
            data = json.loads(fixed_json)
        except json.JSONDecodeError as first_error:
            print(f"First JSON parse attempt failed: {first_error}")

            # Try with the original cleaned version
            try:
                data = json.loads(cleaned_json)
            except json.JSONDecodeError as second_error:
                print(f"Second JSON parse attempt failed: {second_error}")
                return json.dumps(
                    {
                        "error": "Failed to parse JSON response",
                        "first_error": str(first_error),
                        "second_error": str(second_error),
                        "cleaned_json_preview": cleaned_json[:500],
                    },
                    indent=2,
                )

        # Step 4: Validate with Pydantic (optional, can be disabled if causing
        # issues)
        try:
            OutputModel.parse_obj(data)
            print("Pydantic validation passed")
        except ValidationError as ve:
            print(f"Pydantic validation warning: {ve}")
            # Don't fail on Pydantic errors, just log them

        return json.dumps(data, indent=2, ensure_ascii=False)

    except Exception as e:
        print(f"Unexpected error in JSON conversion: {e}")
        return json.dumps(
            {
                "error": "Unexpected error in JSON processing",
                "exception": str(e),
                "raw_response_preview": raw_json[:500] if raw_json else "None",
            },
            indent=2,
        )


def gemini_json_from_pdf(pdf_bytes: bytes, output_path: str, roll_number: str) -> str:
    """Extract JSON from PDF with enhanced retry mechanism"""
    pdf_b64 = base64.b64encode(pdf_bytes).decode()
    prompt = [
        {"inline_data": {"data": pdf_b64, "mime_type": "application/pdf"}},
        *get_json_extraction_prompt(output_path, roll_number),
    ]

    max_retries = 3
    for attempt in range(max_retries):
        try:
            print(
                f"Attempt {attempt + 1}/{max_retries} to get JSON from Gemini..."
            )

            generation_config = {
                "temperature": 0,
                "top_p": 0.8,
                "top_k": 40,
                "max_output_tokens": 8192,
            }

            response = GEMINI_MODEL.generate_content(
                prompt, generation_config=generation_config
            )

            if response and response.text:
                response_text = response.text.strip()
                print(f"Received response of length: {len(response_text)}")

                cleaned = clean_json_response(response_text)
                if (
                    cleaned
                    and cleaned.startswith("{")
                    and cleaned.endswith("}")
                ):
                    return response_text
                else:
                    print(
                        f"Response doesn't look like valid JSON on attempt {attempt + 1}"
                    )

        except Exception as e:
            print(f"Error on attempt {attempt + 1}: {e}")

        if attempt < max_retries - 1:
            print("Retrying...")
            time.sleep(5 + attempt * 2)

    return json.dumps(
        {
            "error": "Failed to get valid JSON response from Gemini after multiple attempts"
        }
    )


def scan_page_for_diagrams(img: Image.Image, page_number: int) -> bool:
    """Pre-scan page to check if it contains diagrams"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            buf = io.BytesIO()
            img.save(buf, format="PNG")
            img_b64 = base64.b64encode(buf.getvalue()).decode()

            response = GEMINI_MODEL.generate_content(
                [
                    {
                        "inline_data": {
                            "data": img_b64,
                            "mime_type": "image/png",
                        }
                    },
                    {
                        "text": """Analyze this page and determine if it contains VISUAL DIAGRAMS.

Look for: Flowcharts, Block diagrams, Circuit diagrams, Network diagrams, Mathematical graphs/plots, Technical illustrations, Hand-drawn figures

DO NOT consider as diagrams: Tables, Text content, Mathematical equations as text, Lists

Answer with only "YES" if there are actual visual diagrams, or "NO" if none."""
                    },
                ],
                generation_config={"temperature": 0},
            )

            result = response.text.strip().upper()
            has_diagrams = result == "YES"
            print(
                f"Page {page_number}: {'HAS DIAGRAMS' if has_diagrams else 'NO DIAGRAMS'}"
            )
            return has_diagrams

        except Exception as e:
            if "429" in str(e) or "quota" in str(e).lower():
                print(
                    f"Rate limit hit while scanning page {page_number}, attempt {attempt + 1}/{max_retries}"
                )
                if attempt < max_retries - 1:
                    wait_time = 10 + (attempt * 5)
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                    continue
            print(f"Error scanning page {page_number}: {e}")
            return True


def get_questions_with_diagrams_on_page(data: Dict[str, Any], page_idx: int, total_pages: int) -> List[Tuple[str, Dict[str, Any]]]:
    """Determine which questions with diagrams are on this page"""
    questions_on_page = []

    for question_key, question_data in data.items():
        if (
            question_key.startswith("Q")
            and isinstance(question_data, dict)
            and "diagram" in question_data
        ):

            q_match = re.search(r"Q(\d+)", question_key)
            if q_match:
                q_num = int(q_match.group(1))
                estimated_page = (q_num - 1) % total_pages
                if estimated_page == page_idx:
                    questions_on_page.append((question_key, question_data))

    return questions_on_page


def select_diagram_boxes_for_page(img_path: str, boxes: List[Tuple[int, int, int, int]], questions_on_page: List[Tuple[str, Dict[str, Any]]], output_folder: str) -> Dict[str, List[Tuple[int, int, int, int]]]:
    """Select bounding boxes for diagrams using Gemini"""
    if not boxes:
        return {}

    max_retries = 3
    for attempt in range(max_retries):
        try:
            # Create image with numbered boxes
            img_cv = cv2.imread(img_path)
            combined_img = img_cv.copy()

            for i, box in enumerate(boxes):
                x1, y1, x2, y2 = box
                cv2.rectangle(combined_img, (x1, y1), (x2, y2), (0, 255, 0), 3)
                cv2.putText(
                    combined_img,
                    str(i + 1),
                    (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    1.0,
                    (0, 255, 0),
                    2,
                )

            combined_path = os.path.join(output_folder, "temp_selection.png")
            cv2.imwrite(combined_path, combined_img)

            with open(combined_path, "rb") as f:
                img_b64 = base64.b64encode(f.read()).decode()

            question_context = ""
            for question_key, question_data in questions_on_page:
                diagram_count = len(question_data.get("diagram", {}))
                question_context += (
                    f"\n{question_key}: Expected diagrams: {diagram_count}\n"
                )

            response = GEMINI_MODEL.generate_content(
                [
                    {
                        "inline_data": {
                            "data": img_b64,
                            "mime_type": "image/png",
                        }
                    },
                    {
                        "text": f"""Green boxes are numbered 1, 2, 3, etc.

Questions with diagrams on this page:
{question_context}

ONLY select boxes containing VISUAL DIAGRAMS: Flowcharts, Block diagrams, Circuit diagrams, Technical illustrations
IGNORE: Tables, text content, mathematical equations as text

Respond in format:
Q3: [1, 4]
Q4: [2, 5]

Numbers = green box numbers with diagrams for each question."""
                    },
                ],
                generation_config={"temperature": 0},
            )

            os.remove(combined_path)

            # Parse response
            selected_boxes = {}
            for line in response.text.strip().split("\n"):
                if ":" in line:
                    parts = line.split(":", 1)
                    if len(parts) == 2:
                        question_key = parts[0].strip()
                        numbers = re.findall(r"\d+", parts[1])
                        box_indices = []

                        for num_str in numbers:
                            try:
                                box_idx = int(num_str) - 1
                                if 0 <= box_idx < len(boxes):
                                    box_indices.append(boxes[box_idx])
                            except ValueError:
                                continue

                        if box_indices:
                            selected_boxes[question_key] = box_indices

            return selected_boxes

        except Exception as e:
            if "429" in str(e) or "quota" in str(e).lower():
                print(
                    f"Rate limit hit during box selection, attempt {attempt + 1}/{max_retries}"
                )
                if attempt < max_retries - 1:
                    wait_time = 15 + (attempt * 10)
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                    continue
            print(f"Error in diagram box selection: {e}")
            return {}


def detect_and_crop_diagrams(imgs_sorted: List[Image.Image], json_data: str, output_folder: str, roll_number: str) -> str:
    """Process diagrams with YOLO detection and cropping"""
    # Check if YOLO model exists
    yolo_model_path = "grade/yolo/best2.pt"
    if not os.path.exists(yolo_model_path):
        print(
            f"YOLO model not found at {yolo_model_path}. Skipping diagram detection."
        )
        return json_data

    model = YOLO(yolo_model_path)
    try:
        data = json.loads(json_data)
        if "error" in data:
            print("Skipping diagram detection due to JSON errors")
            return json_data
    except Exception as e:
        print(f"Could not parse JSON, skipping diagram cropping: {e}")
        return json_data

    print("Processing diagrams...")

    for page_idx, img in enumerate(imgs_sorted):
        print(f"Processing page {page_idx+1}")

        # Check if page has diagrams
        if not scan_page_for_diagrams(img, page_idx + 1):
            continue

        # Get questions with diagrams on this page
        questions_on_page = get_questions_with_diagrams_on_page(
            data, page_idx, len(imgs_sorted)
        )
        if not questions_on_page:
            continue

        # Save temp image
        temp_path = os.path.join(output_folder, f"temp_page_{page_idx+1}.png")
        img.save(temp_path)

        try:
            # YOLO detection
            results = model.predict(
                source=temp_path, imgsz=640, conf=0.3, task="detect"
            )

            if results and len(results) > 0 and results[0].boxes is not None:
                all_boxes = [
                    tuple(map(int, box.xyxy[0].tolist()))
                    for box in results[0].boxes
                ]

                if all_boxes:
                    # Select diagram boxes
                    selected_boxes_dict = select_diagram_boxes_for_page(
                        temp_path, all_boxes, questions_on_page, output_folder
                    )

                    # Crop and save diagrams
                    for (
                        question_key,
                        selected_boxes,
                    ) in selected_boxes_dict.items():
                        q_match = re.search(r"Q(\d+)", question_key)
                        if (
                            q_match
                            and question_key in data
                            and "diagram" in data[question_key]
                        ):
                            q_num = q_match.group(1)
                            diagram_paths = data[question_key]["diagram"]

                            for idx, box in enumerate(selected_boxes):
                                diagram_num = str(idx + 1)
                                diagram_filename = (
                                    f"Q{q_num}_{roll_number}_{diagram_num}.png"
                                )
                                path = os.path.join(
                                    output_folder, diagram_filename
                                )

                                # Update JSON path
                                diagram_paths[diagram_num] = (
                                    f"{output_folder}/{diagram_filename}"
                                )

                                # Crop and save
                                img_cv = cv2.imread(temp_path)
                                x1, y1, x2, y2 = box
                                crop = img_cv[y1:y2, x1:x2]
                                cv2.imwrite(path, crop)
                                print(f"Created diagram: {path}")

        except Exception as e:
            print(f"Error processing page {page_idx+1}: {e}")

        # Clean up
        try:
            os.remove(temp_path)
        except BaseException:
            pass

    return json.dumps(data, indent=2, ensure_ascii=False)


def group_by_roll_number_and_sort_by_page(all_images: List[Tuple[str, Image.Image]]) -> Dict[str, List[Tuple[int, Image.Image, str]]]:
    """Group images by roll number and sort by page number within each group"""
    print("Step 3: Grouping by roll number...")
    groups = defaultdict(list)

    for fname, img in all_images:
        roll = extract_roll_number(img)
        if roll:
            page = extract_page_number(img)
            groups[roll].append((page, img, fname))
            print(f"  {fname} -> Roll: {roll}, Page: {page}")
        else:
            print(f"  Skipping {fname} (no roll number found)")

    print(f"Found {len(groups)} unique roll numbers")

    # Sort pages within each roll number group
    print("Step 4: Sorting pages within each roll number group...")
    for roll in groups:
        groups[roll].sort(key=lambda x: x[0])
        print(f"  Roll {roll}: {len(groups[roll])} pages")

    return groups
