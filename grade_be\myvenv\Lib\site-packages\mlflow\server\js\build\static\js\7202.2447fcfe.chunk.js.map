{"version": 3, "file": "static/js/7202.2447fcfe.chunk.js", "mappings": "0LAsBO,MAAMA,EAOTC,GAGAC,IASA,MAAMC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,MACXC,GAASC,EAAAA,EAAAA,KAEf,OACEC,EAAAA,EAAAA,GAACR,EACC,CACAM,OAAQA,EACRJ,SAAUA,EACVE,SAAUA,KACLH,GACL,C,8HChDD,MAAMQ,EAAeC,IAA4C,IAA3C,UAAEC,GAAmCD,EAChE,MAAM,MAAEE,IAAUC,EAAAA,EAAAA,KAClB,OACEL,EAAAA,EAAAA,GAACM,EAAAA,IAAG,CACFC,YAAY,oEACZJ,UAAWA,EACXK,KAAGC,EAAAA,EAAAA,IAAE,CAAEC,WAAYN,EAAMO,QAAQC,IAAI,IACrCC,MAAM,YAAWC,UAEjBd,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAGb,C,wDCPK,MAAMC,EAanB,2BAAOC,CAAqBC,EAAoBJ,GAC9C,OAAO,IAAIK,EAAkB,CAACD,EAAeJ,GAAIM,KAAK,KAAM,eAC9D,CAEA,wCAAOC,CAAkCH,EAAoBJ,GAC3D,OAAO,IAAIK,EAAkB,CAACD,EAAeJ,GAAIM,KAAK,KAAM,iBAC9D,EAnBmBJ,EAMZM,QAAU,MAoBnB,MAAMH,EACJI,WAAAA,CAAYC,EAAYC,GAAY,KAUpCD,WAAK,OACLE,gBAAU,EAVRC,KAAKH,MAAQA,EAEXG,KAAKD,WADM,iBAATD,EACgBG,OAAOC,aAEPD,OAAOE,cAE7B,CASAC,kBAAAA,GACE,MAAMC,EAAYL,KAAKM,QAAQd,EAAkBe,wBACjD,OAAIF,EACKG,KAAKC,MAAMJ,GAEb,CAAC,CACV,CAMAK,kBAAAA,CAAmBC,GACjB,MAAMC,EAA4C,oBAAvBD,EAAYE,OAAwBF,EAAYE,SAAWF,EACtFX,KAAKc,QAAQtB,EAAkBe,uBAAwBC,KAAKO,UAAUH,GACxE,CAMAI,eAAAA,CAAgBC,GACd,MAAO,CAAC,qBAAsB5B,EAAkBM,QAASK,KAAKH,MAAOoB,GAAKxB,KAAK,IACjF,CAGAqB,OAAAA,CAAQG,EAAUC,GAChBlB,KAAKD,WAAWe,QAAQd,KAAKgB,gBAAgBC,GAAMC,EACrD,CAGAZ,OAAAA,CAAQW,GACN,OAAOjB,KAAKD,WAAWO,QAAQN,KAAKgB,gBAAgBC,GACtD,EAlDIzB,EASGe,uBAAyB,qB,mIC1C3B,SAASY,EAAmBC,GACjC,OAAIA,EACK,GAAGC,EAAAA,aAA6CC,EAAAA,EAAAA,IAAmBF,GAAO,KAE1E,EAEX,CAEO,SAASG,IAIP,IAJ+B,MACtCH,EAAQ,IAGTI,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACH,MAAMG,EAAU,GACVC,EAAgBR,EAAMS,SAAS,SAAWT,EAAQD,EAAmBC,GAE3E,OADIQ,GAAeD,EAAQG,KAAKF,GACzBD,EAAQlC,KAAK,QACtB,CAEO,SAASsC,EAAiCC,GAC/C,MAAI,gBAAiBA,EACZA,EAAsB,YAE3B,oBAAqBA,GAAY,mBAAoBA,EAChDb,EAAmBa,EAA0B,iBAAK,QAAUA,EAAyB,eAE1F,mBAAoBA,EACfA,EAAyB,eAE9B,oBAAqBA,EAChBA,EAA0B,gBAE5B,EACT,C,wGCJO,MAAMC,UAA0BtE,EAAAA,UAAwBiC,WAAAA,GAAA,SAAA4B,WAAA,KAC7DU,MAAQ,CACNC,cAAc,GACd,KAEFC,QAAUC,EAAAA,YAAkB,KAE5BC,SAAWC,UACTvC,KAAKwC,SAAS,CAAEL,cAAc,IAC9B,IACE,MAAMM,QAAgBzC,KAAaoC,QAAQM,QAAQC,uBAC7C3C,KAAKpC,MAAMgF,aAAaH,GAC9BzC,KAAK6C,yBACL7C,KAAK8C,uBACP,CAAE,MAAOC,GACP/C,KAAKgD,oBAAoBD,EAC3B,GACA,KAEFF,uBAAyB,KACvB7C,KAAKwC,SAAS,CAAEL,cAAc,IAC7BnC,KAAaoC,QAAQM,QAAQO,aAAa,EAC3C,KAEFD,oBAAuBD,IACrB/C,KAAKwC,SAAS,CAAEL,cAAc,IAC9Be,EAAAA,EAAMC,sBAAsBJ,EAAE,EAC9B,KAEFD,sBAAwB,KACtB9C,KAAK6C,yBACL7C,KAAKpC,MAAMwF,SAAS,EACpB,KAEFC,aAAe,KAAO,IAADC,EAAAC,EACnBvD,KAAK8C,wBACc,QAAnBQ,GAAAC,EAAAvD,KAAKpC,OAAM4F,gBAAQ,IAAAF,GAAnBA,EAAAG,KAAAF,EAAuB,CACvB,CAEFG,MAAAA,GACE,MAAM,aAAEvB,GAAiBnC,KAAKkC,OACxB,OAAEyB,EAAM,WAAEC,EAAU,OAAEC,EAAM,OAAEC,EAAM,SAAE7E,GAAae,KAAKpC,MAGxDmG,EAAc1B,EAAAA,SAAe2B,IAAI/E,GAAWgF,GAG5C5B,EAAAA,eAAqB4B,GAEhB5B,EAAAA,aAAmB4B,EAAO,CAAEC,SAAUlE,KAAKoC,UAE7C6B,IAGT,OACE9F,EAAAA,EAAAA,GAACgG,EAAAA,EAAK,CACJ,cAAY,qBACZ7F,UAAW0B,KAAKpC,MAAMU,UACtB8F,MAAOpE,KAAKpC,MAAMwG,MAElBC,MAAO,IACPC,QAAST,EACTU,KAAMvE,KAAKsC,SACXqB,OAAQA,EACRC,WAAYA,EACZY,eAAgBrC,EAChBqB,SAAUxD,KAAKqD,aACfS,OAAQA,EACRW,UAAQ,EAAAxF,SAEP8E,GAGP,E,oQC5FK,MAAMW,EAAmB,YAYhC,MAAMC,UAA4BhH,EAAAA,UAGhC+F,MAAAA,GACE,MAAMkB,EAAmBD,EAAoBE,sBAC7C,OAEEC,EAAAA,EAAAA,IAACC,EAAAA,IAAU,CAACC,IAAKhF,KAAKpC,MAAMsG,SAAUe,OAAO,WAAW,cAAY,0BAAyBhG,SAAA,EAC3Fd,EAAAA,EAAAA,GAAC4G,EAAAA,IAAWG,KAAI,CACdC,KAAMT,EACNU,MAAOpF,KAAKpC,MAAMyH,KAAKC,cAAc,CAAAnG,GAAA,SACnCC,eAAe,eAGjBmG,MAAO,CACL,CACEC,UAAU,EACVC,QAASzF,KAAKpC,MAAMyH,KAAKC,cAAc,CAAAnG,GAAA,SACrCC,eAAe,4CAInB,CAAEsG,UAAW1F,KAAKpC,MAAM8H,YACxBzG,UAEFd,EAAAA,EAAAA,GAACwH,EAAAA,EAAK,CAACjH,YAAY,0EAA0EkH,WAAS,OAExGd,EAAAA,EAAAA,IAAA,KAAGxG,UAAU,gCAA+BW,SAAA,EAC1Cd,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yEAGjBjB,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,0BAEfqD,OAAQ,CACNoD,KACEC,IAGA3H,EAAAA,EAAAA,GAAA,KAAG4H,KAAMnB,EAAkBoB,OAAO,SAAQ/G,SACvC6G,OAIP,SAKV,EAlDInB,EACGE,oBAAsB,IAAMoB,EAAAA,GAqD9B,MAAMC,GAAkBC,EAAAA,EAAAA,IAAWxB,G,+ECjD1C,MAAMyB,UAA6B/D,EAAAA,UAAuBzC,WAAAA,GAAA,SAAA4B,WAAA,KACxD6E,gCAAiCC,EAAAA,EAAAA,MAAU,KAE3CC,4BAA8BhE,UAC5B,MAAMiE,QAAexG,KAAKpC,MAAM6I,yBAC9BhE,EAAOiC,GACP1E,KAAKqG,gCAEDK,EAAWF,EAAOtF,OAASsF,EAAOtF,MAAMyF,iBAC1CD,GAGFE,YAAW,IAAM5G,KAAKpC,MAAMG,SAAS8I,EAAAA,GAAoBC,kBAAkBJ,EAASvB,QACtF,EACA,KAEF4B,6BAA8BC,EAAAA,EAAAA,UAASC,EAAAA,EAAoB,KAAK,KAEhEC,eAAiB,KACXlH,KAAKpC,MAAMuJ,sBACbnH,KAAKpC,MAAMG,SAAS8I,EAAAA,GAAoBO,mBAC1C,CACA,CAEF1D,MAAAA,GACE,MAAM,aAAE2D,EAAY,UAAEC,GAActH,KAAKpC,MACzC,OACEO,EAAAA,EAAAA,GAAC8D,EAAAA,EAAiB,CAChBmC,MAAOpE,KAAKpC,MAAMyH,KAAKC,cAAc,CAAAnG,GAAA,SACnCC,eAAe,iBAGjBuE,OAAQ3D,KAAKpC,MAAMyH,KAAKC,cAAc,CAAAnG,GAAA,SACpCC,eAAe,WAGjBwE,WAAY5D,KAAKpC,MAAMyH,KAAKC,cAAc,CAAAnG,GAAA,SACxCC,eAAe,WAGjByE,OAAQwD,EACRzE,aAAc5C,KAAKuG,4BACnBnD,QAASkE,EACT9D,SAAUxD,KAAKkH,eAAejI,UAG9Bd,EAAAA,EAAAA,GAAC+H,EAAe,CAAC5B,QAAS+C,EAAc3B,UAAW1F,KAAK+G,+BAG9D,EAGF,MAAMQ,EAAqB,CACzBd,yBACF,MAEMe,GAA6B9J,EAAAA,EAAAA,IACjC+J,EAAAA,EAAAA,SAAQ/F,EAAW6F,EAAnBE,EAAuCtB,EAAAA,EAAAA,IAA0BC,KAGtDsB,GAAmBC,EAAAA,EAAAA,GAAkBC,EAAAA,EAAWC,eAAeC,eAAgBN,GChFrF,SAASO,EAAiB1J,GAGtB,IAHuB,WAChC2J,EAAa,UAAS,WACtBC,GAAa9J,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,kBACxCf,EACN,MAAOgJ,EAAca,IAAmBC,EAAAA,EAAAA,WAAkB,GAU1D,OACErD,EAAAA,EAAAA,IAAA,OAAKnG,IAAKyJ,EAAOC,QAAQpJ,SAAA,EACvBd,EAAAA,EAAAA,GAACmK,EAAAA,EAAM,CACL5J,YAAY,4EACZJ,UAAU,mBACVK,IAAKyJ,EAAOG,cAAcP,GAC1BlI,KAAMkI,EACNQ,QAXYC,KAChBP,GAAgB,EAAK,EAWjB,cAAY,sBAAqBjJ,SAEhCgJ,KAEH9J,EAAAA,EAAAA,GAACuJ,EAAgB,CAACL,aAAcA,EAAcC,UApBhCA,KAChBY,GAAgB,EAAM,MAsB1B,CAEA,MAAME,EAAS,CACbG,cAAgBP,GACC,YAAfA,EACI,CACEU,OAAQ,OACRrE,MAAO,eAET,CAAEsE,QAAS,OACjBN,QAAS,CAAEO,QAAS,W,sECtCmD,IAAAC,EAAA,CAAA1D,KAAA,SAAAiD,OAAA,mEAQlE,MAAMU,EAA8BzK,IAIpC,IAJqC,kBAC1C0K,EAAoB,iBAGrB1K,EACC,MAAM,cAAEiH,IAAkB0D,EAAAA,EAAAA,KACpBC,GAAsBC,EAAAA,EAAAA,IAAc,CAAA/J,GAAA,SACxCC,eAAe,4GAMX+J,EAAY7D,EAAc2D,EAAqB,CAAEG,QAAS,IAAKC,UAAW,UAEhF,OACEvE,EAAAA,EAAAA,IAACwE,EAAAA,GAAQC,KAAI,CAAC7K,YAAY,sFAAqFO,SAAA,EAC7Gd,EAAAA,EAAAA,GAACmL,EAAAA,GAAQE,QAAO,CACd,aAAYL,EACZxK,IAAGkK,EAAkF5J,UAErFd,EAAAA,EAAAA,GAACsL,EAAAA,EAAQ,OAEX3E,EAAAA,EAAAA,IAACwE,EAAAA,GAAQI,QAAO,CAACC,MAAM,QAAO1K,SAAA,EAC5B6F,EAAAA,EAAAA,IAAA,OAAA7F,SAAA,EACEd,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,IAAK+J,EAAqBxG,OAAQ,CAAE2G,SAASjL,EAAAA,EAAAA,GAAA,SAAQkL,WAAWlL,EAAAA,EAAAA,GAAA,KAAAc,SAAG,aAAgB,KACpGd,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,0BAEfqD,OAAQ,CACNoD,KAAOC,IACL3H,EAAAA,EAAAA,GAACyL,EAAAA,EAAWC,KAAI,CACdnL,YAAY,sFACZqH,KAAM+D,EAAAA,GAA+B,UACrCC,cAAY,EAAA9K,SAEX6G,QAKT3H,EAAAA,EAAAA,GAAA,UACAA,EAAAA,EAAAA,GAAA,UACAA,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,eACjCjB,EAAAA,EAAAA,GAAA,SAAM,mCAENA,EAAAA,EAAAA,GAAA,SAAM,uBAAgB4K,EAAkB,sCAE1C5K,EAAAA,EAAAA,GAACmL,EAAAA,GAAQU,MAAK,SAEH,EAINC,EAAmBC,IAKF,IALG,aAE/BC,EAAY,qBACZC,EAAoB,WACpBC,GACsBH,EACtB,MAAM7E,GAAO2D,EAAAA,EAAAA,MAENsB,EAAsBC,IAA2BpC,EAAAA,EAAAA,UAASgC,IAKjEK,EAAAA,EAAAA,YAAU,KACRD,EAAwBJ,EAAa,GACpC,CAACA,IAMJ,OACErF,EAAAA,EAAAA,IAAC2F,EAAAA,IAAiB,CAAAxL,SAAA,EAChBd,EAAAA,EAAAA,GAACuM,EAAAA,IAAgB,CACfhM,YAAY,uFACZiM,YAAatF,EAAKC,cAAc,CAAAnG,GAAA,SAC9BC,eAAe,6CAGjBkD,SAnBgBsI,KACpBR,EAAqBE,EAAqB,EAmBtCO,QAASA,KACPN,EAAwB,IACxBH,EAAqB,GAAG,EAE1BU,SAAW/H,GAAMwH,EAAwBxH,EAAEiD,OAAO9E,OAClD,cAAY,qBACZ6J,QAAQ5M,EAAAA,EAAAA,GAAC2K,EAA2B,IACpC5H,MAAOoJ,EACPU,kBAAgB,IAEjBX,IACClM,EAAAA,EAAAA,GAACmK,EAAAA,EAAM,CACL5J,YAAY,uFACZoB,KAAK,WACL0I,QA3BMyC,KACZb,EAAqB,GAAG,EA2BlB,cAAY,4BAA2BnL,UAEvCd,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,sBAGnB,E,0DC5GxB,MAAM8L,EAAYA,KAAM/M,EAAAA,EAAAA,GAAAgN,EAAAA,GAAA,CAAAlM,SAAE,WAAW,IAAA4J,EAAA,CAAA1D,KAAA,UAAAiD,OAAA,0CAE9B,MAAMgD,EAAoB/M,IAA2C,IAA1C,KAAEgN,GAAkChN,EACpE,MACM,MAAEE,IAAUC,EAAAA,EAAAA,MACX8M,EAAUC,IAAepD,EAAAA,EAAAA,WAAS,GAEnCqD,EAAgB,OAAJH,QAAI,IAAJA,OAAI,EAAJA,EAAMI,QAAQC,IAASA,EAAIzK,IAAI0K,WAAWC,EAAAA,MAEtDC,EAAyB,OAATL,QAAS,IAATA,OAAS,EAATA,EAAWM,MAAM,EAAGR,OAAW5J,EANzB,GAQ5B,GAAc,OAAT8J,QAAS,IAATA,IAAAA,EAAW/J,OACd,OAAOtD,EAAAA,EAAAA,GAAC+M,EAAS,IAGnB,MAAMa,GACJ5N,EAAAA,EAAAA,GAAA,MAAAc,UACEd,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SAAqDC,eAAe,cAIzF,OACE0F,EAAAA,EAAAA,IAAA,OAAA7F,SAAA,CACG4M,EAAc7H,KAAK0H,IAClBvN,EAAAA,EAAAA,GAAC6N,EAAAA,IAAa,CAEZ5H,OACEU,EAAAA,EAAAA,IAAAqG,EAAAA,GAAA,CAAAlM,SAAA,CACGyM,EAAIzK,IAAI,KAAGyK,EAAIxK,OAAS6K,KAG7BE,UAAU,OAAMhN,UAEhB6F,EAAAA,EAAAA,IAAA,OAEEnG,IAAGkK,EACH,cAAY,yBAAwB5J,SAAA,EAEpCd,EAAAA,EAAAA,GAACyL,EAAAA,EAAWsC,KAAI,CAACC,MAAI,EAAAlN,SAAEyM,EAAIzK,MAAsB,KAAGyK,EAAIxK,OAAS6K,IAJ5DL,EAAIzK,MATNyK,EAAIzK,OAiBZoK,EAAK5J,OAvCkB,IAwCtBtD,EAAAA,EAAAA,GAACmK,EAAAA,EAAM,CACL5J,YAAY,6FACZC,KAAGC,EAAAA,EAAAA,IAAE,CAAEwN,UAAW7N,EAAMO,QAAQuN,IAAI,IACpCC,KAAK,QACL9D,QAASA,IAAM+C,GAAaD,GAC5BiB,KAAMjB,GAAWnN,EAAAA,EAAAA,GAACqO,EAAAA,IAAmB,KAAMrO,EAAAA,EAAAA,GAACsO,EAAAA,IAAqB,IACjE,cAAY,8BAA6BxN,SAExCqM,GACCnN,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,eAIjBjB,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,eAEfqD,OAAQ,CAAEvB,MAAOsK,EAAU/J,OAzDX,SA8DpB,EAOGiL,EAA2BxC,IAAwE,IAAvE,cAAEyC,EAAa,KAAExH,GAAgD+E,EACxG,OAAKyC,GAIHxO,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uCAEfqD,OAAQ,CACNkK,gBACA9G,KAAO+G,IAAczO,EAAAA,EAAAA,GAAC0L,EAAAA,GAAI,CAACgD,GAAIhG,EAAAA,GAAoBiG,yBAAyB3H,EAAMwH,GAAe1N,SAAE2N,QARhGzO,EAAAA,EAAAA,GAAC+M,EAAS,GAUf,E,eC3FN,MAAM6B,GAAe7D,EAAAA,EAAAA,IAAc,CAAA/J,GAAA,SACjCC,eAAe,sBAEd,IAAA8K,EAAA,CAAA/E,KAAA,UAAAiD,OAAA,iCAAA4E,EAAA,CAAA7H,KAAA,UAAAiD,OAAA,iCAMI,MAAM6E,GAAiC5O,IAAqD,IAApD,MAAE6O,GAA4C7O,EAC3F,MAAM,QAAE8O,GAAYD,GACd,MAAE3O,IAAUC,EAAAA,EAAAA,KAElB,GAAY,OAAP2O,QAAO,IAAPA,IAAAA,EAAS1L,OACZ,OAAO,KAIT,MAAM2L,GAAyBC,EAAAA,EAAAA,QAAOF,GAAStE,IAAA,IAAC,QAAElJ,GAASkJ,EAAA,OAAKyE,SAAS3N,EAAS,KAAO,CAAC,IAAE4N,UAEtFC,GAAqBC,EAAAA,EAAAA,OAAML,GAGjC,IAAKI,EACH,OAAO,KAGT,MAAME,EAAeN,EAAuB3B,QAAQkC,GAAUA,IAAUH,IAExE,OACE1I,EAAAA,EAAAA,IAAA,OAAA7F,SAAA,EACE6F,EAAAA,EAAAA,IAAC+E,EAAAA,GAAI,CAACgD,GAAIhG,EAAAA,GAAoBiG,yBAAyBI,EAAM/H,KAAMqI,EAAmB7N,SAASV,SAAA,EAC7Fd,EAAAA,EAAAA,GAACyP,EAAAA,EAAoB,CAAC1M,MAAOsM,EAAmBG,MAAOhP,IAAGuL,IAA2C,MACnG/L,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,IAAK6N,EAActK,OAAQ,CAAE9C,QAAS6N,EAAmB7N,cAE7E+N,EAAajM,OAAS,IACrBqD,EAAAA,EAAAA,IAAC+I,EAAAA,IAAatE,KAAI,CAACuE,OAAO,EAAM7O,SAAA,EAC9Bd,EAAAA,EAAAA,GAAC0P,EAAAA,IAAarE,QAAO,CAACuE,SAAO,EAAA9O,UAC3B6F,EAAAA,EAAAA,IAACwD,EAAAA,EAAM,CACL5J,YAAY,iGACZ4N,KAAK,QACL3N,KAAGC,EAAAA,EAAAA,IAAE,CAAEoP,aAAc,GAAInP,WAAYN,EAAMO,QAAQC,IAAI,IAACE,SAAA,CACzD,IACGkO,EAAQ1L,OAAS,QAGvBtD,EAAAA,EAAAA,GAAC0P,EAAAA,IAAanE,QAAO,CAACC,MAAM,QAAO1K,SAChCyO,EAAa1J,KAAIiK,IAAA,IAAC,MAAEN,EAAK,QAAEhO,GAASsO,EAAA,OACnC9P,EAAAA,EAAAA,GAAC0P,EAAAA,IAAa3I,KAAI,CAChBxG,YAAY,iGAAgGO,UAG5G6F,EAAAA,EAAAA,IAAC+E,EAAAA,GAAI,CAACgD,GAAIhG,EAAAA,GAAoBiG,yBAAyBI,EAAM/H,KAAMxF,GAASV,SAAA,EAC1Ed,EAAAA,EAAAA,GAACyP,EAAAA,EAAoB,CAAC1M,MAAOyM,EAAOhP,IAAGqO,IAA2C,IAAE,KACpF7O,EAAAA,EAAAA,GAAA,QAAMQ,KAAGC,EAAAA,EAAAA,IAAE,CAAEI,MAAOT,EAAM2P,OAAOC,2BAA2B,IAAClP,UAC3Dd,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,IAAK6N,EAActK,OAAQ,CAAE9C,mBAL7CgO,EAQa,WAKxB,E,4BC3CV,MAAMS,GAAgCA,CAACC,EAA0CC,KAC/E,MAAMC,EAAeF,GAAkBA,EAAeG,MAAMC,GAAMA,EAAEC,gBAAkBJ,IACtF,OAAOC,GAAgBA,EAAa5O,OAAO,EAC3C,IAEGgP,GAAU,SAAVA,GAAU,OAAVA,EAAU,YAAVA,EAAU,iCAAVA,EAAU,0BAAVA,EAAU,qBAAVA,EAAU,8BAAVA,EAAU,oCAAVA,EAAU,YAAVA,EAAU,oCAAVA,CAAU,EAAVA,IAAU,IA4BR,MAAMC,GAAiBvQ,IASF,IATG,WAC7BwQ,EAAU,WACVC,EAAU,WACVC,EAAU,aACVC,EAAY,UACZC,EAAS,MACTC,EAAK,WACL7E,EAAU,WACV8E,GACoB9Q,EACpB,MAAMgH,GAAO2D,EAAAA,EAAAA,MAEP,kBAAEoG,IAAsBC,EAAAA,GAAAA,KAExBC,EAA4CT,EAAW7K,KAAKkJ,GACzDA,IAGHqC,GAAeC,EAAAA,EAAAA,UAAQ,KAC3B,MAAMC,EAA6B,CACjC,CACEtQ,GAAIwP,GAAWe,KACfC,eAAe,EACfC,OAAQvK,EAAKC,cAAc,CAAAnG,GAAA,SACzBC,eAAe,SAGjByQ,YAAa,OACbC,KAAMjH,IAAA,IAAC,SAAEkH,GAAUlH,EAAA,OACjB1K,EAAAA,EAAAA,GAAC0L,EAAAA,GAAI,CAACgD,GAAIhG,EAAAA,GAAoBC,kBAAkBkJ,OAAOD,MAAa9Q,UAClEd,EAAAA,EAAAA,GAAC6N,EAAAA,IAAa,CAAC5H,MAAO2L,IAAW9Q,SAAE8Q,OAC9B,EAETE,KAAM,CAAE7H,OAAQ,CAAE8H,SAAU,IAAKC,KAAM,KAEzC,CACEhR,GAAIwP,GAAWyB,eACfT,eAAe,EAEfC,OAAQvK,EAAKC,cAAc,CAAAnG,GAAA,SACzBC,eAAe,mBAGjByQ,YAAa,kBACbC,KAAM5F,IAAsC,IAArC,SAAE6F,EAAUM,KAAK,SAAEC,IAAYpG,EACpC,MAAM,KAAE/E,GAASmL,EACXjC,EAAiB0B,IACjBQ,EACHC,QAAsB,OAAdnC,QAAc,IAAdA,OAAc,EAAdA,EAAgB5M,SACvBgP,KAAKC,OAAOrC,EAAerK,KAAKyK,GAAMnB,SAASmB,EAAE9O,QAAS,OAAMgR,YAClE,GACF,OAAOxS,EAAAA,EAAAA,GAACuO,EAAwB,CAACvH,KAAMA,EAAMwH,cAAe4D,GAAuB,EAErFN,KAAM,CAAE7H,OAAQ,CAAEwI,SAAU,QA6FhC,OA1FIxB,EAEFK,EAAQ3N,KAAK,CACX3C,GAAIwP,GAAWkC,iBACflB,eAAe,EAEfC,OAAQvK,EAAKC,cAAc,CAAAnG,GAAA,SACzBC,eAAe,qBAGjB0Q,KAAM7B,IAAyC,IAAtCoC,KAAOC,SAAUQ,IAAe7C,EACvC,OAAO9P,EAAAA,EAAAA,GAAC8O,GAA8B,CAACC,MAAO4D,GAAe,EAE/Db,KAAM,CAAE7H,OAAQ,CAAE8H,SAAU,QAI9BT,EAAQ3N,KACN,CACE3C,GAAIwP,GAAWoC,cACfpB,eAAe,EAEfC,OAAQvK,EAAKC,cAAc,CAAAnG,GAAA,SACzBC,eAAe,YAGjB0Q,KAAM9C,IAA4B,IAAzBqD,KAAK,SAAEC,IAAYtD,EAC1B,MAAM,gBAAEgE,EAAe,KAAE7L,GAASmL,EAC5B3D,EAAgByB,GAA8B4C,EAAiBC,EAAAA,GAAOC,SAC5E,OAAO/S,EAAAA,EAAAA,GAACuO,EAAwB,CAACvH,KAAMA,EAAMwH,cAAeA,GAAiB,EAE/EsD,KAAM,CAAE7H,OAAQ,CAAEwI,SAAU,OAE9B,CACEzR,GAAIwP,GAAWwC,iBACfxB,eAAe,EAEfC,OAAQvK,EAAKC,cAAc,CAAAnG,GAAA,SACzBC,eAAe,eAGjB0Q,KAAMsB,IAA4B,IAAzBf,KAAK,SAAEC,IAAYc,EAC1B,MAAM,gBAAEJ,EAAe,KAAE7L,GAASmL,EAC5B3D,EAAgByB,GAA8B4C,EAAiBC,EAAAA,GAAOI,YAC5E,OAAOlT,EAAAA,EAAAA,GAACuO,EAAwB,CAACvH,KAAMA,EAAMwH,cAAeA,GAAiB,EAE/EsD,KAAM,CAAE7H,OAAQ,CAAEwI,SAAU,QAKlCnB,EAAQ3N,KACN,CACE3C,GAAIwP,GAAW2C,WACf1B,OAAQvK,EAAKC,cAAc,CAAAnG,GAAA,SACzBC,eAAe,eAGjByQ,YAAa,UACbF,eAAe,EACfG,KAAMyB,IAAsC,IAArC,SAAExB,EAAUM,KAAK,SAAEC,IAAYiB,EACpC,OAAOpT,EAAAA,EAAAA,GAAA,QAAMiG,MAAO2L,IAAqB9Q,SAAE8Q,KAAkB,EAE/DE,KAAM,CAAE7H,OAAQ,CAAE+H,KAAM,KAE1B,CACEhR,GAAIwP,GAAW6C,cACf7B,eAAe,EACfC,OAAQvK,EAAKC,cAAc,CAAAnG,GAAA,SACzBC,eAAe,kBAGjByQ,YAAa,yBACbC,KAAM2B,IAAA,IAAC,SAAE1B,GAAU0B,EAAA,OAAKtT,EAAAA,EAAAA,GAAA,QAAAc,SAAOiE,EAAAA,EAAMwO,gBAAgB3B,IAAY1K,IAAa,EAC9E4K,KAAM,CAAE7H,OAAQ,CAAE+H,KAAM,EAAGS,SAAU,OAEvC,CACEzR,GAAIwP,GAAWgD,KACf/B,OAAQvK,EAAKC,cAAc,CAAAnG,GAAA,SACzBC,eAAe,SAGjBuQ,eAAe,EACfE,YAAa,OACbC,KAAM8B,IAAmB,IAAlB,SAAE7B,GAAU6B,EACjB,OAAOzT,EAAAA,EAAAA,GAACiN,EAAiB,CAACC,KAAM0E,KAAkC,IAKjEN,CAAO,GACb,CAEDpK,EACA+J,IAGIyC,EAAwB,CAAC,CAAE1S,GAAI4P,EAAY+C,MAAOhD,IAUxD,IAAIiD,EAAsBC,EAAAA,GAE1B,MAAMC,GAEF9T,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yEAKf8S,EAAiBhD,GACrB/Q,EAAAA,EAAAA,GAACgU,EAAAA,IAAK,CACJC,OAAOjU,EAAAA,EAAAA,GAACkU,EAAAA,EAAW,IACnBC,YAAapD,aAAiBqD,GAAAA,EAAerD,EAAMsD,kBAAoBtD,EAAMzJ,QAC7ErB,OACEjG,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,4BAKnBiL,GAEFlM,EAAAA,EAAAA,GAACgU,EAAAA,IAAK,CAACG,YAAaL,EAAsBG,OAAOjU,EAAAA,EAAAA,GAACsU,EAAAA,EAAU,IAAK,cAAY,2BAG7EtU,EAAAA,EAAAA,GAACgU,EAAAA,IAAK,CACJG,aACEnU,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,8EAEfqD,OAAQ,CACNoD,KAAO6M,IACLvU,EAAAA,EAAAA,GAAA,KAAG6H,OAAO,SAAS2M,IAAI,sBAAsB5M,KAAMgM,EAAoB9S,SACpEyT,OAMXN,OAAOjU,EAAAA,EAAAA,GAACyU,EAAAA,IAAQ,IAChBC,QACE1U,EAAAA,EAAAA,GAAC4J,EAAiB,CAChBC,WAAW,UACXC,YACE9J,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,uBASrC0T,GAAQC,EAAAA,EAAAA,IAAmC,CAC/CC,KAAM1D,EACNG,QAASF,EACTrN,MAAO,CACL2P,WAEFoB,iBAAiBA,EAAAA,EAAAA,MACjBC,SAAUC,IAAA,IAAC,GAAEhU,GAAIgU,EAAA,OAAKhU,CAAE,EACxBiU,gBAtEkBC,IAClB,MAAOC,GAAwC,oBAAjBD,EAA8BA,EAAaxB,GAAWwB,EAChFC,GACFtE,EAAa,CAAED,WAAYuE,EAAanU,GAAI2P,YAAawE,EAAaxB,MACxE,IAqEF,OACE3T,EAAAA,EAAAA,GAAAgN,EAAAA,GAAA,CAAAlM,UACE6F,EAAAA,EAAAA,IAACyO,EAAAA,IAAK,CACJ,cAAY,mBACZpE,WAAYA,EACZqE,YAAU,EACVC,OAnBkBxE,GAAiD,IAApC6D,EAAMY,cAAcC,KAAKlS,QAAiByN,EAmBtDgD,OAAiBxQ,EAAUzC,SAAA,EAE9Cd,EAAAA,EAAAA,GAACyV,EAAAA,IAAQ,CAACC,UAAQ,EAAA5U,SACf6T,EAAMgB,iBAAiB9P,KAAK4L,IAAM,IAAAmE,EAAA,OACjC5V,EAAAA,EAAAA,GAAC6V,EAAAA,IAAW,CACVtV,YAAY,qFACZuV,UAAQ,EAERC,SAAUtE,EAAOuE,OAAOC,aACxBC,cAAezE,EAAOuE,OAAOG,eAAiB,OAC9CC,aAAcA,KACZ,MAAOC,GAAqB3C,EAEtB4C,KADoB7E,EAAOuE,OAAOhV,KAAOqV,EAAkBrV,MAC3BqV,EAAkB1C,KACxDlC,EAAOuE,OAAOO,cAAcD,EAAS,EAEvC9V,IAAsD,QAAnDoV,EAAGnE,EAAOuE,OAAOQ,UAA8B1E,YAAI,IAAA8D,OAAA,EAAjDA,EAAmD3L,OAAOnJ,UAE9D2V,EAAAA,EAAAA,IAAWhF,EAAOuE,OAAOQ,UAAU/E,OAAQA,EAAOiF,eAX9CjF,EAAOzQ,GAYA,MAGjB8P,GACC9Q,EAAAA,EAAAA,GAAC2W,EAAAA,IAAiB,CAAChC,MAAOA,IAE1BA,EAAMY,cAAcC,KAAK3P,KAAKqM,IAC5BlS,EAAAA,EAAAA,GAACyV,EAAAA,IAAQ,CAAA3U,SACNoR,EAAI0E,cAAc/Q,KAAK8L,IAAI,IAAAkF,EAAA,OAC1B7W,EAAAA,EAAAA,GAAC8W,EAAAA,IAAS,CAAChB,UAAQ,EAAetV,IAAoD,QAAjDqW,EAAGlF,EAAKqE,OAAOQ,UAA8B1E,YAAI,IAAA+E,OAAA,EAA/CA,EAAiD5M,OAAOnJ,UAC5F2V,EAAAA,EAAAA,IAAW9E,EAAKqE,OAAOQ,UAAU7E,KAAMA,EAAK+E,eADtB/E,EAAK3Q,GAElB,KAJDkR,EAAIlR,UAUxB,E,4BC7SqD,IAAA+K,GAAA,CAAA/E,KAAA,UAAAiD,OAAA,8CAAA6F,GAAA,CAAA9I,KAAA,SAAAiD,OAAA,UA6BrD,MAAM8M,WAA0B7S,EAAAA,UACrCzC,WAAAA,CAAYhC,GACVuX,MAAMvX,GAAO,KA8BfwX,aAAe,CAACC,EAAYC,KACrB,OAALD,QAAK,IAALA,GAAAA,EAAOE,iBACPvV,KAAKpC,MAAM4X,SAASF,EAAY,EAChC,KAaFG,uBAAyBpX,IAAsC,IAArC,WAAE0Q,EAAU,WAAED,GAAiBzQ,EAGvD,MAAMqX,EAEJ,CACEC,UAAW,0BACX5G,IAAeA,EAEnB/O,KAAK4V,uBAAkBlU,OAAWA,EAAW,CAC3CmU,MAAOH,EACPI,MAAOhH,EAAa,YAAc,WAClC,EACF,KAEF8G,kBAAoB,CAACzG,EAAiBxN,EAAcoU,KAClD/V,KAAKpC,MAAMoY,sBAAsBd,GAAkBe,iBAAiBF,EAAOF,OAAQE,EAAOD,MAAM,EAChG,KAMFI,gBAAkB,KAChBlW,KAAKpC,MAAMuY,aAAa,EACxB,KAEFC,gBAAkB,KAChBpW,KAAKpC,MAAMyY,aAAa,EACxB,KAEFC,mBAAqBzN,IAA4C,IAA3C,KAAE0N,EAAI,IAAEtV,EAAG,QAAEuV,EAAO,SAAEC,GAAe5N,EACzD7I,KAAKpC,MAAM8Y,eAAezV,EAAI,EA5E9BjB,KAAKkC,MAAQ,CACXyU,oBAAqBC,EAAAA,GAEzB,CAOAC,uBAAAA,GACqC3B,GAAkB4B,cAAcC,EAAAA,IACxCjW,QAAQ,qBAAsB,QAC3D,CAMA,oBAAOgW,CAAc7V,GACnB,OAAO5B,EAAAA,EAAkBC,qBAAqB,gBAAiB2B,EACjE,CAEA+V,iBAAAA,GAEE9T,EAAAA,EAAM+T,gBADY,gBAEpB,CAqDAvT,MAAAA,GAEE,MAAM,OACJwT,EAAM,YACNC,EAAW,cACXC,EAAa,YACb9B,GACEtV,KAAKpC,OACH,QAAEyZ,EAAO,MAAEnI,GAAUlP,KAAKpC,MAG1ByM,EAEJmG,QAAQ8E,GAEJlR,GACJjG,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sBAInB,OACE0F,EAAAA,EAAAA,IAACwS,GAAAA,EAAa,CAAC,eAAa,0BAA0BC,gBAAc,EAAAtY,SAAA,EAClE6F,EAAAA,EAAAA,IAAA,OAAA7F,SAAA,EACEd,EAAAA,EAAAA,GAACqZ,EAAAA,EAAU,CAACpT,MAAOA,EAAOqT,WAAW,KAAIxY,UACvCd,EAAAA,EAAAA,GAAC4J,EAAiB,OAGpBjD,EAAAA,EAAAA,IAAC8E,EAAAA,EAAW8N,KAAI,CAAAzY,SAAA,CACbiW,GAAkByC,4BAA6B,KAChDxZ,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,0BAEfqD,OAAQ,CACNoD,KAAOC,IACL3H,EAAAA,EAAAA,GAACyL,EAAAA,EAAWC,KAAI,CACdnL,YAAY,yEACZqH,KAAMmP,GAAkBrQ,sBACxBkF,cAAY,EAAA9K,SAEX6G,WAMX3H,EAAAA,EAAAA,GAACyZ,EAAAA,EAAM,KAEPzZ,EAAAA,EAAAA,GAAC8L,EAAgB,CACfE,aAAcnK,KAAKpC,MAAM0X,YACzBlL,qBAAuBlJ,GAAUlB,KAAKoV,aAAa,KAAMlU,GACzDmJ,WAAYA,QAGhBlM,EAAAA,EAAAA,GAACyQ,GAAc,CACbC,WAAYqI,EACZlI,aAAchP,KAAKyV,uBACnB1G,WAAY/O,KAAKpC,MAAMmR,WACvBD,WAAY9O,KAAKpC,MAAMkR,WACvBG,UAAWoI,IAAW,EACtBnI,MAAOA,EACPC,YACErK,EAAAA,EAAAA,IAAA,OACE,cAAY,6BACZnG,IAAGuL,GAA2DjL,SAAA,EAE9Dd,EAAAA,EAAAA,GAAA,OAAKQ,IAAGsP,GAAchP,UAAE4Y,EAAAA,EAAAA,QAA4B1Z,EAAAA,EAAAA,GAAC2Z,GAAAA,EAAwB,OAC7E3Z,EAAAA,EAAAA,GAAA,OAAAc,UACEd,EAAAA,EAAAA,GAAC4Z,EAAAA,IAAgB,CACfrZ,YAAY,yEACZsZ,YAAaxH,QAAQ4G,GACrBa,gBAAiBd,EAAc,EAC/Be,WAAYlY,KAAKkW,gBACjBiC,eAAgBnY,KAAKoW,gBACrBgC,eAAgB,CACdtN,SAAWuN,GAAQrY,KAAKsW,mBAAmB,CAAErV,IAAKoX,IAClDC,QAAStY,KAAKpC,MAAM2a,eACpBC,QAAS,CAAC,GAAI,GAAI,GAAI,aAMhCnO,WAAYA,MAIpB,EA1KW6K,GASJuD,aAAe,CACpBvB,OAAQ,GACR5B,YAAa,IAXJJ,GAqCJe,iBAAoB9B,IACzB,OAAQA,GACN,IArEoB,OAsElB,OAAO9S,EAAAA,GACT,IAtE6B,yBAuE3B,OAAOqX,EAAAA,GACT,QACE,OAAO,KACX,EA7CSxD,GAmEJrQ,oBAAsB,IAAMoB,EAAAA,GAnExBiP,GAqEJyC,0BAA4B,IAAMgB,EAAAA,GAwGpC,MAAMC,IAAgBC,EAAAA,GAAAA,IAAwB1S,EAAAA,EAAAA,IAA2C+O,K,4BC9LzF,MAAM4D,WAA0BzW,EAAAA,UACrCzC,WAAAA,CAAYhC,GAAgC,IAADmb,EACzC5D,MAAMvX,GAAMmb,EAAA/Y,KAAC,KAYfgZ,sBAAwB,qBAAqB,KAC7CC,2BAA6B,CAAE,EAAG,MAAO,KACzCC,oCAAqC5S,EAAAA,EAAAA,MAAU,KAC/C6S,6BAA8B7S,EAAAA,EAAAA,MAAU,KACxC8S,0BAA4B,CAACpZ,KAAKkZ,oCAAoC,KAgGtEG,oBAAc,OAEdC,oBAAuBpY,IACbA,IAAUA,EAAMqY,oBAAsBrY,EAAMsY,gBACpD,KAeFC,gBAAkB,SAACC,GAA8B,IAAnBC,EAAQnY,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACxC,MAAM4V,EAAgB2B,EAAKa,6BAA6BD,GACxDZ,EAAKvW,UACFqX,IAAc,CACb1C,YAAauC,EAEbI,WAAY,IACPD,EAAUC,WACb,CAACJ,EAAO,GAAItC,OAGhB,KACE2B,EAAKgB,uBAAuBhB,EAAK7W,MAAM4X,WAAW,GAGxD,EAAE,KAEF1E,aAAgBE,IACdtV,KAAKga,oBACLha,KAAKwC,SAAS,CAAE8S,YAAaA,IAAe,KAC1CtV,KAAKia,SAAS,GAAG,EAAM,GACvB,EAGJ,KACAC,wBAA2B5E,IACzBtV,KAAKwC,SAAS,CAAE8S,YAAaA,GAAc,EAC3C,KAEF6E,0BAA4B,CAAC7E,EAAkBvG,EAAiBD,EAAiB4K,KAC/E,MAAMU,EAAY,CAAC,EACf9E,IAEF8E,EAAuB,YAAI9E,GAEzBvG,GAAcA,IAAe1N,EAAAA,KAE/B+Y,EAAsB,WAAIrL,IAET,IAAfD,IAEFsL,EAAsB,WAAItL,GAExB4K,GAAiB,IAATA,IAEVU,EAAgB,KAAIV,GAEtB,MAAMW,GAASC,EAAAA,EAAAA,IAAsB,WAAWpX,EAAAA,EAAMqX,sBAAsBH,MACxEC,IAAWra,KAAKpC,MAAMC,SAAS2c,SAAWxa,KAAKpC,MAAMC,SAAS4c,QAChEza,KAAKpC,MAAMG,SAASsc,EACtB,EACA,KAEFK,uBAA0BzZ,IACxBjB,KAAKwC,SAAS,CAAEmU,oBAAqBrJ,SAASrM,EAAK,MAAO,KACxDjB,KAAKga,oBACL,MAAM,oBAAErD,GAAwB3W,KAAKkC,MACrClC,KAAK2a,qBAAqBhE,GAC1B3W,KAAKia,SAAS,GAAG,EAAM,GACvB,EACF,KAEF/D,gBAAkB,KAChB,MAAM,YAAEiB,GAAgBnX,KAAKkC,MAC7BlC,KAAKia,SAAS9C,EAAc,GAAG,EAAM,EACrC,KAEFf,gBAAkB,KAChB,MAAM,YAAEe,GAAgBnX,KAAKkC,MAC7BlC,KAAKia,SAAS9C,EAAc,GAAG,EAAM,EACrC,KAEFyD,0BAA4B,CAAC7L,EAAiB8L,KAC5C,MAAM/L,EAAa+L,IAAcC,EAAAA,GAAmBC,KACpD/a,KAAKwC,SAAS,CAAEuM,aAAYD,eAAc,KACxC9O,KAAKga,oBACLha,KAAKia,SAAS,GAAG,EAAM,GACvB,EACF,KAEFe,uBAAyB,IAChBhb,KAAKkC,MAAMyU,oBAnNlB3W,KAAKkC,MAAQ,CACX6M,WAAY1N,EAAAA,GACZyN,YAAY,EACZqI,YAAa,EACbR,oBAAqB3W,KAAKib,yBAC1BnB,WAAY,CAAC,EACbzC,SAAS,EACTnI,WAAOxN,EACP4T,aAAavT,EAAAA,GAAAA,IAAiC/B,KAAKkb,eAEvD,CAOAA,WAAAA,GACE,OAAOlb,KAAKpC,MAAMC,SAAWqF,EAAAA,EAAMiY,uBAAuBnb,KAAKpC,MAAMC,SAAS4c,QAAU,CAAC,CAC3F,CAEAzD,iBAAAA,GACE,MAAMhV,EAAWhC,KAAKkb,cAChBE,EAAsBpb,KAAKqb,yBAC3BC,EAAsBtb,KAAKib,yBAEjCjb,KAAKwC,SACH,CAEEuM,gBAAoCrN,IAAxBM,EAAS+M,WAA2B/O,KAAKkC,MAAM6M,WAAa/M,EAAS+M,WACjFD,gBAE0BpN,IAAxBM,EAAS8M,WACL9O,KAAKkC,MAAM4M,WAEa,SAAxB9M,EAAS8M,WACfqI,iBAEoBzV,IAAlBM,EAAS0X,MAAuB1X,EAAiB0X,QAAQ0B,EAErD9N,SAAStL,EAAS0X,KAAM,IACxB1Z,KAAKkC,MAAMiV,YACjBR,oBAAqB2E,EACrBxB,WAAYsB,IAEd,KACEpb,KAAKub,YAAW,EAAK,GAG3B,CAEAF,sBAAAA,GACE,MAAMG,EAAQ1C,GAAkBhC,cAAc9W,KAAKgZ,uBACnD,OAAIwC,GAASA,EAAMlb,QAAQ,eAClBE,KAAKC,MAAM+a,EAAMlb,QAAQ,gBAEzBN,KAAKiZ,0BAEhB,CAEAc,sBAAAA,CAAuB0B,GACrB,MAAMD,EAAQ1C,GAAkBhC,cAAc9W,KAAKgZ,uBAC/CwC,GACFA,EAAM1a,QAAQ,cAAeN,KAAKO,UAAU0a,GAEhD,CAEAR,sBAAAA,GACE,MAAMO,EAAQ1C,GAAkBhC,cAAc9W,KAAKgZ,uBACnD,OAAIwC,GAASA,EAAMlb,QAAQ,eAClBgN,SAASkO,EAAMlb,QAAQ,eAAgB,IAEvCsW,EAAAA,EAEX,CAEA+D,oBAAAA,CAAqBe,GACL5C,GAAkBhC,cAAc9W,KAAKgZ,uBAC7ClY,QAAQ,cAAe4a,EAAY/K,WAC3C,CAMA,oBAAOmG,CAAc7V,GACnB,OAAO5B,EAAAA,EAAkBK,kCAAkC,gBAAiBuB,EAC9E,CAGAsa,UAAAA,GAAsC,IAA3BI,EAAgBna,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GACzBxB,KAAKia,SAASja,KAAKkC,MAAMiV,YAAawE,EACxC,CAEA3B,iBAAAA,GACEha,KAAKwC,UAAUqX,IAAc,CAC3B1C,YAAa,EACb2C,WAAY9Z,KAAKiZ,+BAEnBjZ,KAAK+Z,uBAAuB/Z,KAAKiZ,2BACnC,CAiBAW,4BAAAA,CAA6BD,GAC3B,MAAM,MAAEzY,GAAUyY,EAClB,OAAI3Z,KAAKsZ,oBAAoBpY,GAKpB,KAEAA,EAAMsY,eAEjB,CAsFAS,QAAAA,CAASP,EAAWiC,GAClB,MAAM,YACJrG,EAAW,WACXwE,EAAU,WACV/K,EAAU,WACVD,GAEE9O,KAAKkC,MACTlC,KAAKwC,SAAS,CAAE6U,SAAS,EAAMnI,WAAOxN,IACtC1B,KAAKma,0BAA0B7E,EAAavG,EAAYD,EAAY4K,GACpE1Z,KAAKpC,MACFge,2BACCra,EAAAA,GAAAA,IAAwB,CACtBH,MAAOkU,IAGTtV,KAAKkC,MAAMyU,oBACXmC,GAAkB+C,eAAe9M,EAAYD,GAC7CgL,EAAWJ,GACXiC,EAAmB3b,KAAKkZ,mCAAqClZ,KAAKmZ,6BAEnE2C,MAAMC,IACL/b,KAAKyZ,gBAAgBC,EAAMqC,EAAE,IAE9BC,OAAOjZ,IACN/C,KAAKwC,SAAS,CAAE2U,YAAa,EAAGjI,MAAOnM,IACvC/C,KAAKga,mBAAmB,IAEzBiC,SAAQ,KACPjc,KAAKwC,SAAS,CAAE6U,SAAS,GAAQ,GAEvC,CAEA3T,MAAAA,GACE,MAAM,WACJqL,EAAU,WACVD,EAAU,YACVqI,EAAW,WACX2C,EAAU,YACVxE,GAEEtV,KAAKkC,OACH,OAAEgV,GAAWlX,KAAKpC,MACxB,OACEO,EAAAA,EAAAA,GAAC+d,GAAAA,EAAqB,CAAAjd,UACpBd,EAAAA,EAAAA,GAACya,GACC,CACA1B,OAAQA,EACRG,QAASrX,KAAKkC,MAAMmV,QACpBnI,MAAOlP,KAAKkC,MAAMgN,MAClBoG,YAAaA,EACbvG,WAAYA,EACZD,WAAYA,EACZqI,YAAaA,EACbC,cAAe0C,EAAW3C,EAAc,GACxC3B,SAAUxV,KAAKoV,aACfe,YAAanW,KAAKkW,gBAClBG,YAAarW,KAAKoW,gBAClBJ,sBAAuBhW,KAAK4a,0BAC5BlE,eAAgB1W,KAAK0a,uBACrBnC,eAAgBvY,KAAKgb,4BAI7B,EAzRWlC,GA+GJ+C,eAAiB,CAAC9M,EAAiBD,IACxCC,EAAa,GAAGA,KAAcD,EAAa,MAAQ,SAAW,GA4KlE,MAOMvH,GAAqB,CACzBqU,0BACF,MAIaO,IAFmBze,EAAAA,EAAAA,IAAe+J,EAAAA,EAAAA,KAXtBvF,IAEhB,CACLgV,OAFakF,OAAO3Z,OAAOP,EAAMma,SAASC,gBAU0B/U,GAAzBE,CAA6CqR,KC3U/EyD,IAAuB5U,EAAAA,EAAAA,GAClCC,EAAAA,EAAWC,eAAeC,gBAJK0U,KACxBre,EAAAA,EAAAA,GAACge,GAAa,MAOvB,S,2KCqBO,SAASM,EAAYpe,GAA+B,IAA9B,KAAEqe,GAAyBre,EACtD,MAAMse,GACJxe,EAAAA,EAAAA,GAACye,EAAAA,IAAI,CAAA3d,SAEFyd,EAAK1Y,KAAI6E,IAAA,IAAC,GAAE1J,EAAE,SAAE0d,EAAQ,QAAErU,EAAO,KAAEzC,KAAS+W,GAAYjU,EAAA,OAEvD1K,EAAAA,EAAAA,GAACye,EAAAA,IAAK1X,KAAI,CAAUsD,QAASA,EAASzC,KAAMA,EAAM,eAAc5G,KAAQ2d,EAAU7d,SAC/E4d,GADa1d,EAEJ,MAMlB,OAAOud,EAAKjb,OAAS,GACnBtD,EAAAA,EAAAA,GAAC4e,EAAAA,IAAQ,CAACC,QAASL,EAAcM,QAAS,CAAC,SAAUhR,UAAU,aAAaiR,OAAK,EAAAje,UAC/Ed,EAAAA,EAAAA,GAACmK,EAAAA,EAAM,CACL5J,YAAY,kEACZ6N,MAAMpO,EAAAA,EAAAA,GAACgf,EAAAA,IAAY,IACnB,eAAa,wBACb,aAAW,gCAGb,IACN,CAAC,IAAAjT,EAAA,CAAA/E,KAAA,UAAAiD,OAAA,iBAsBM,SAASoP,EAAW5Z,GACzB,MAAM,MACJwG,EAAK,YACLgZ,EAAc,GAAE,YAChBC,EAAc,GAAE,QAChBC,EAAO,SACPre,EAAQ,WACRwY,EAAU,WACV8F,GAAa,EAAK,4BAClBC,GACE5f,GACE,MAAEW,IAAUC,EAAAA,EAAAA,MACLwK,EAAAA,EAAAA,KAEb,OACElE,EAAAA,EAAAA,IAAAqG,EAAAA,GAAA,CAAAlM,SAAA,EACEd,EAAAA,EAAAA,GAACsf,EAAAA,IAAM,CACLL,YACEA,EAAY3b,OAAS,IACnBtD,EAAAA,EAAAA,GAACuf,EAAAA,IAAU,CAACC,sBAAoB,EAAA1e,SAC7Bme,EAAYpZ,KAAI,CAAC4Z,EAAGC,KACnB1f,EAAAA,EAAAA,GAACuf,EAAAA,IAAWxY,KAAI,CAAAjG,SAAU2e,GAAJC,OAK9BC,QAAS7e,EACTmF,MAAOA,EAEPiZ,aACEvY,EAAAA,EAAAA,IAAAqG,EAAAA,GAAA,CAAAlM,SAAA,CACGqe,IAAWnf,EAAAA,EAAAA,GAACC,EAAAA,EAAY,CAACO,IAAGuL,IAC5BmT,KAGLG,4BAA6BA,KAE/Brf,EAAAA,EAAAA,GAACyZ,EAAAA,EACC,CACAjZ,KAAGC,EAAAA,EAAAA,IAAE,CAEHmf,WAAY,KACRR,EAAa,CAAE3U,QAAS,QAAW,CAAC,GACzC,IACD0D,KAAMmL,MAId,C,yeC1HO,MAAMxG,EAAS,CACpB+M,KAAM,OACN9M,QAAS,UACTG,WAAY,aACZ4M,SAAU,YAGCC,EAAgB,CAACjN,EAAOC,QAASD,EAAOI,YAExC8M,EAAc,CACzB,CAAClN,EAAO+M,MAAO,OACf,CAAC/M,EAAOC,SAAU,UAClB,CAACD,EAAOI,YAAa,aACrB,CAACJ,EAAOgN,UAAW,YAGRG,EAAqB,CAChC,CAACnN,EAAO+M,OACN7f,EAAAA,EAAAA,GAACM,EAAAA,IAAG,CAACC,YAAY,yDAAwDO,SAAEkf,EAAYlN,EAAO+M,QAEhG,CAAC/M,EAAOC,UACN/S,EAAAA,EAAAA,GAACM,EAAAA,IAAG,CAACC,YAAY,yDAAyDM,MAAM,QAAOC,SACpFkf,EAAYlN,EAAOC,WAGxB,CAACD,EAAOI,aACNlT,EAAAA,EAAAA,GAACM,EAAAA,IAAG,CAACC,YAAY,yDAAyDM,MAAM,OAAMC,SACnFkf,EAAYlN,EAAOI,cAGxB,CAACJ,EAAOgN,WACN9f,EAAAA,EAAAA,GAACM,EAAAA,IAAG,CAACC,YAAY,yDAAyDM,MAAM,WAAUC,SACvFkf,EAAYlN,EAAOgN,aAiBnB,IAAKI,EAAa,SAAbA,GAAa,OAAbA,EAAa,wCAAbA,EAAa,4CAAbA,EAAa,sCAAbA,EAAa,sCAAbA,EAAa,oCAAbA,EAAa,oCAAbA,EAAa,0BAAbA,CAAa,OAealgB,EAAAA,EAAAA,GAAA,OAAKmgB,MAAO,CAAElS,WAAY,IAAKnN,SAAC,MAA/D,MAEMsf,EAAqB,CAChCC,MAAO,SAGIC,EAAoC,CAC/C,CAACF,EAAmBC,QAClBrgB,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,YAIxBsf,EAAiC,CAC5C,CAACH,EAAmBC,QAClBrgB,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAMRuf,EAA0B,CACrC,CAACJ,EAAmBC,QAAQrgB,EAAAA,EAAAA,GAACygB,EAAAA,GAAS,KAG3BC,EAAqC,IAOrCjI,EAAqC,GAErCkI,EAA2C,GAE3Czd,EAAsC,OAEtCqX,EAA2C,YAE3CoC,EAAqB,CAChCiE,IAAK,SACLhE,KAAM,WAGKiE,EAAqCC,IAChD9gB,EAAAA,EAAAA,GAACe,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sFAIfqD,OAAQ,CAAEwc,aAAcA,KAIfC,EACX,mF,kFC1HF,IAAArW,EAAA,CAAA1D,KAAA,SAAAiD,OAAA,4BAGO,MAAM8T,EAAwB7d,IAAiF,IAAhF,SAAEY,EAAQ,UAAEX,GAA8DD,EAC9G,OACEF,EAAAA,EAAAA,GAACghB,EAAAA,IACC,CACAxgB,IAAGkK,EACHvK,UAAWA,EAAUW,SAEpBA,GACW,C,yGCVX,MAAMmgB,EAA8BC,GAClC,CAACC,EAAepe,EAA2Bqe,KAC3Cre,EAIMme,IAA6Bxd,SAASX,GAI/Cqe,EAAS,eAAere,sBAGxBse,EAAAA,EAAcC,oBAAoB,CAAEC,gBAAiBxe,IAClD4a,MAAM6D,GACLJ,EAAS,eAAere,6TAKzB8a,OAAOjZ,GAAMwc,OAAS7d,KAfzB6d,OAAS7d,EAgBX,EAISuF,EAAqBA,CAACqY,EAAena,EAA0Boa,KACrEpa,EAKLya,EAAAA,EAAqBC,mBAAmB,CAAE1a,KAAMA,IAC7C2W,MAAK,IAAMyD,EAAS,UAAUpa,wBAC9B6W,OAAOjZ,GAAMwc,OAAS7d,KANvB6d,OAAS7d,EAMyB,C", "sources": ["common/utils/withRouterNext.tsx", "shared/building_blocks/PreviewBadge.tsx", "common/utils/LocalStorageUtils.ts", "model-registry/utils/SearchUtils.ts", "experiment-tracking/components/modals/GenericInputModal.tsx", "model-registry/components/CreateModelForm.tsx", "model-registry/components/CreateModelModal.tsx", "model-registry/components/CreateModelButton.tsx", "model-registry/components/model-list/ModelListFilters.tsx", "model-registry/components/model-list/ModelTableCellRenderers.tsx", "model-registry/components/aliases/ModelsTableAliasedVersionsCell.tsx", "model-registry/components/model-list/ModelListTable.tsx", "model-registry/components/ModelListView.tsx", "model-registry/components/ModelListPage.tsx", "model-registry/components/ModelListPageWrapper.tsx", "shared/building_blocks/PageHeader.tsx", "model-registry/constants.tsx", "common/components/ScrollablePageWrapper.tsx", "common/forms/validations.ts"], "sourcesContent": ["import React from 'react';\n\nimport {\n  type Location,\n  type Params as RouterDOMParams,\n  type NavigateOptions,\n  type To,\n  useLocation,\n  useNavigate,\n  useParams,\n} from './RoutingUtils';\n\nexport interface WithRouterNextProps<Params extends RouterDOMParams = RouterDOMParams> {\n  navigate: ReturnType<typeof useNavigate>;\n  location: Location;\n  params: Params;\n}\n\n/**\n * This HoC serves as a retrofit for class components enabling them to use\n * react-router v6's location, navigate and params being injected via props.\n */\nexport const withRouterNext =\n  <\n    T,\n    Props extends JSX.IntrinsicAttributes &\n      JSX.LibraryManagedAttributes<React.ComponentType<T>, React.PropsWithChildren<T>>,\n    Params extends RouterDOMParams = RouterDOMParams,\n  >(\n    Component: React.ComponentType<T>,\n  ) =>\n  (\n    props: Omit<\n      Props,\n      | 'location'\n      | 'navigate'\n      | 'params'\n      | 'navigationType'\n      /* prettier-ignore*/\n    >,\n  ) => {\n    const location = useLocation();\n    const navigate = useNavigate();\n    const params = useParams<Params>();\n\n    return (\n      <Component\n        /* prettier-ignore */\n        params={params as Params}\n        location={location}\n        navigate={navigate}\n        {...(props as Props)}\n      />\n    );\n  };\n", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport { Tag, useDesignSystemTheme } from '@databricks/design-system';\nexport const PreviewBadge = ({ className }: { className?: string }) => {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <Tag\n      componentId=\"codegen_mlflow_app_src_shared_building_blocks_previewbadge.tsx_14\"\n      className={className}\n      css={{ marginLeft: theme.spacing.xs }}\n      color=\"turquoise\"\n    >\n      <FormattedMessage\n        defaultMessage=\"Experimental\"\n        description=\"Experimental badge shown for features which are experimental\"\n      />\n    </Tag>\n  );\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\n/**\n * Utils for working with local storage.\n */\nexport default class LocalStorageUtils {\n  /**\n   * Protocol version of MLflow's local storage. Should be incremented on any breaking change in how\n   * data persisted in local storage is used, to prevent old (invalid) cached data from being loaded\n   * and breaking the application.\n   */\n  static version = '1.1';\n\n  /**\n   * Return a LocalStorageStore corresponding to the specified component and ID, where the ID\n   * can be used to disambiguate between multiple instances of cached data for the same component\n   * (e.g. cached data for multiple experiments).\n   */\n  static getStoreForComponent(componentName: any, id: any) {\n    return new LocalStorageStore([componentName, id].join('-'), 'localStorage');\n  }\n\n  static getSessionScopedStoreForComponent(componentName: any, id: any) {\n    return new LocalStorageStore([componentName, id].join('-'), 'sessionStorage');\n  }\n}\n\n/**\n * Interface to browser local storage that allows for setting key-value pairs under the specified\n * \"scope\".\n */\nclass LocalStorageStore {\n  constructor(scope: any, type: any) {\n    this.scope = scope;\n    if (type === 'localStorage') {\n      this.storageObj = window.localStorage;\n    } else {\n      this.storageObj = window.sessionStorage;\n    }\n  }\n  static reactComponentStateKey = 'ReactComponentState';\n\n  scope: any;\n  storageObj: any;\n\n  /**\n   * Loads React component state cached in local storage into a vanilla JS object.\n   */\n  loadComponentState() {\n    const storedVal = this.getItem(LocalStorageStore.reactComponentStateKey);\n    if (storedVal) {\n      return JSON.parse(storedVal);\n    }\n    return {};\n  }\n\n  /**\n   * Save React component state in local storage.\n   * @param stateRecord: Immutable.Record instance or plain object containing component state.\n   */\n  saveComponentState(stateRecord: any) {\n    const targetValue = typeof stateRecord.toJSON === 'function' ? stateRecord.toJSON() : stateRecord;\n    this.setItem(LocalStorageStore.reactComponentStateKey, JSON.stringify(targetValue));\n  }\n\n  /**\n   * Helper method for constructing a scoped key to use for setting/getting values in\n   * local storage.\n   */\n  withScopePrefix(key: any) {\n    return ['MLflowLocalStorage', LocalStorageUtils.version, this.scope, key].join('-');\n  }\n\n  /** Save the specified key-value pair in local storage. */\n  setItem(key: any, value: any) {\n    this.storageObj.setItem(this.withScopePrefix(key), value);\n  }\n\n  /** Fetch the value corresponding to the passed-in key from local storage. */\n  getItem(key: any) {\n    return this.storageObj.getItem(this.withScopePrefix(key));\n  }\n}\n", "import { REGISTERED_MODELS_SEARCH_NAME_FIELD } from '../constants';\nimport { resolveFilterValue } from '../actions';\n\nexport function getModelNameFilter(query: string): string {\n  if (query) {\n    return `${REGISTERED_MODELS_SEARCH_NAME_FIELD} ilike ${resolveFilterValue(query, true)}`;\n  } else {\n    return '';\n  }\n}\n\nexport function getCombinedSearchFilter({\n  query = '',\n}: {\n  query?: string;\n} = {}) {\n  const filters = [];\n  const initialFilter = query.includes('tags.') ? query : getModelNameFilter(query);\n  if (initialFilter) filters.push(initialFilter);\n  return filters.join(' AND ');\n}\n\nexport function constructSearchInputFromURLState(urlState: Record<string, string>): string {\n  if ('searchInput' in urlState) {\n    return urlState['searchInput'];\n  }\n  if ('nameSearchInput' in urlState && 'tagSearchInput' in urlState) {\n    return getModelNameFilter(urlState['nameSearchInput']) + ` AND ` + urlState['tagSearchInput'];\n  }\n  if ('tagSearchInput' in urlState) {\n    return urlState['tagSearchInput'];\n  }\n  if ('nameSearchInput' in urlState) {\n    return urlState['nameSearchInput'];\n  }\n  return '';\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport { Modal } from '@databricks/design-system';\n\nimport Utils from '../../../common/utils/Utils';\n\ntype Props = {\n  okText?: string;\n  cancelText?: string;\n  isOpen?: boolean;\n  onClose: (...args: any[]) => any;\n  onCancel?: () => void;\n  className?: string;\n  footer?: React.ReactNode;\n  handleSubmit: (...args: any[]) => any;\n  title: React.ReactNode;\n};\n\ntype State = {\n  isSubmitting: boolean;\n};\n\n/**\n * Generic modal that has a title and an input field with a save/submit button.\n * As of now, it is used to display the 'Rename Run' and 'Rename Experiment' modals.\n */\nexport class GenericInputModal extends Component<Props, State> {\n  state = {\n    isSubmitting: false,\n  };\n\n  formRef = React.createRef();\n\n  onSubmit = async () => {\n    this.setState({ isSubmitting: true });\n    try {\n      const values = await (this as any).formRef.current.validateFields();\n      await this.props.handleSubmit(values);\n      this.resetAndClearModalForm();\n      this.onRequestCloseHandler();\n    } catch (e) {\n      this.handleSubmitFailure(e);\n    }\n  };\n\n  resetAndClearModalForm = () => {\n    this.setState({ isSubmitting: false });\n    (this as any).formRef.current.resetFields();\n  };\n\n  handleSubmitFailure = (e: any) => {\n    this.setState({ isSubmitting: false });\n    Utils.logErrorAndNotifyUser(e);\n  };\n\n  onRequestCloseHandler = () => {\n    this.resetAndClearModalForm();\n    this.props.onClose();\n  };\n\n  handleCancel = () => {\n    this.onRequestCloseHandler();\n    this.props.onCancel?.();\n  };\n\n  render() {\n    const { isSubmitting } = this.state;\n    const { okText, cancelText, isOpen, footer, children } = this.props;\n\n    // add props (ref) to passed component\n    const displayForm = React.Children.map(children, (child) => {\n      // Checking isValidElement is the safe way and avoids a typescript\n      // error too.\n      if (React.isValidElement(child)) {\n        // @ts-expect-error TODO: fix this\n        return React.cloneElement(child, { innerRef: this.formRef });\n      }\n      return child;\n    });\n\n    return (\n      <Modal\n        data-testid=\"mlflow-input-modal\"\n        className={this.props.className}\n        title={this.props.title}\n        // @ts-expect-error TS(2322): Type '{ children: {}[] | null | undefined; \"data-t... Remove this comment to see the full error message\n        width={540}\n        visible={isOpen}\n        onOk={this.onSubmit}\n        okText={okText}\n        cancelText={cancelText}\n        confirmLoading={isSubmitting}\n        onCancel={this.handleCancel}\n        footer={footer}\n        centered\n      >\n        {displayForm}\n      </Modal>\n    );\n  }\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\n\nimport { LegacyForm, Input } from '@databricks/design-system';\nimport { ModelRegistryDocUrl } from '../../common/constants';\nimport { FormattedMessage, injectIntl } from 'react-intl';\n\nexport const MODEL_NAME_FIELD = 'modelName';\n\ntype Props = {\n  visible: boolean;\n  validator?: (...args: any[]) => any;\n  intl?: any;\n  innerRef: any;\n};\n\n/**\n * Component that renders a form for creating a new experiment.\n */\nclass CreateModelFormImpl extends Component<Props> {\n  static getLearnMoreLinkUrl = () => ModelRegistryDocUrl;\n\n  render() {\n    const learnMoreLinkUrl = CreateModelFormImpl.getLearnMoreLinkUrl();\n    return (\n      // @ts-expect-error TS(2322)\n      <LegacyForm ref={this.props.innerRef} layout=\"vertical\" data-testid=\"create-model-form-modal\">\n        <LegacyForm.Item\n          name={MODEL_NAME_FIELD}\n          label={this.props.intl.formatMessage({\n            defaultMessage: 'Model name',\n            description: 'Text for form title on creating model in the model registry',\n          })}\n          rules={[\n            {\n              required: true,\n              message: this.props.intl.formatMessage({\n                defaultMessage: 'Please input a name for the new model.',\n                description: 'Error message for having no input for creating models in the model registry',\n              }),\n            },\n            { validator: this.props.validator },\n          ]}\n        >\n          <Input componentId=\"codegen_mlflow_app_src_model-registry_components_createmodelform.tsx_62\" autoFocus />\n        </LegacyForm.Item>\n        <p className=\"create-modal-explanatory-text\">\n          <FormattedMessage\n            defaultMessage=\"After creation, you can register logged models as new versions.&nbsp;\"\n            description=\"Text for form description on creating model in the model registry\"\n          />\n          <FormattedMessage\n            defaultMessage=\"<link>Learn more</link>\"\n            description=\"Learn more link on the form for creating model in the model registry\"\n            values={{\n              link: (\n                chunks: any, // Reported during ESLint upgrade\n              ) => (\n                // eslint-disable-next-line react/jsx-no-target-blank\n                <a href={learnMoreLinkUrl} target=\"_blank\">\n                  {chunks}\n                </a>\n              ),\n            }}\n          />\n          .\n        </p>\n      </LegacyForm>\n    );\n  }\n}\n\n// @ts-expect-error TS(2769): No overload matches this call.\nexport const CreateModelForm = injectIntl(CreateModelFormImpl);\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { GenericInputModal } from '../../experiment-tracking/components/modals/GenericInputModal';\nimport { CreateModelForm, MODEL_NAME_FIELD } from './CreateModelForm';\nimport { connect } from 'react-redux';\nimport { createRegisteredModelApi } from '../actions';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport { ModelRegistryRoutes } from '../routes';\nimport { debounce } from 'lodash';\nimport { modelNameValidator } from '../../common/forms/validations';\nimport { IntlShape, injectIntl } from 'react-intl';\nimport { withRouterNext } from '../../common/utils/withRouterNext';\nimport type { WithRouterNextProps } from '../../common/utils/withRouterNext';\nimport { withErrorBoundary } from '../../common/utils/withErrorBoundary';\nimport ErrorUtils from '../../common/utils/ErrorUtils';\n\ntype Props = WithRouterNextProps & {\n  createRegisteredModelApi: (...args: any[]) => any;\n  modalVisible: boolean;\n  hideModal: (...args: any[]) => any;\n  navigateBackOnCancel?: boolean;\n  intl: IntlShape;\n};\n\nclass CreateModelModalImpl extends React.Component<Props> {\n  createRegisteredModelRequestId = getUUID();\n\n  handleCreateRegisteredModel = async (values: any) => {\n    const result = await this.props.createRegisteredModelApi(\n      values[MODEL_NAME_FIELD],\n      this.createRegisteredModelRequestId,\n    );\n    const newModel = result.value && result.value.registered_model;\n    if (newModel) {\n      // Jump to the page of newly created model. Here we are yielding to next tick to allow modal\n      // and form to finish closing and cleaning up.\n      setTimeout(() => this.props.navigate(ModelRegistryRoutes.getModelPageRoute(newModel.name)));\n    }\n  };\n\n  debouncedModelNameValidator = debounce(modelNameValidator, 400);\n\n  handleOnCancel = () => {\n    if (this.props.navigateBackOnCancel) {\n      this.props.navigate(ModelRegistryRoutes.modelListPageRoute);\n    }\n  };\n\n  render() {\n    const { modalVisible, hideModal } = this.props;\n    return (\n      <GenericInputModal\n        title={this.props.intl.formatMessage({\n          defaultMessage: 'Create Model',\n          description: 'Title text for creating model in the model registry',\n        })}\n        okText={this.props.intl.formatMessage({\n          defaultMessage: 'Create',\n          description: 'Create button text for creating model in the model registry',\n        })}\n        cancelText={this.props.intl.formatMessage({\n          defaultMessage: 'Cancel',\n          description: 'Cancel button text for creating model in the model registry',\n        })}\n        isOpen={modalVisible}\n        handleSubmit={this.handleCreateRegisteredModel}\n        onClose={hideModal}\n        onCancel={this.handleOnCancel}\n      >\n        {/* @ts-expect-error TS(2322): Type '{ visible: boolean; validator: ((rule: any, ... Remove this comment to see the full error message */}\n        <CreateModelForm visible={modalVisible} validator={this.debouncedModelNameValidator} />\n      </GenericInputModal>\n    );\n  }\n}\n\nconst mapDispatchToProps = {\n  createRegisteredModelApi,\n};\n\nconst CreateModelModalWithRouter = withRouterNext(\n  connect(undefined, mapDispatchToProps)(injectIntl<'intl', Props>(CreateModelModalImpl)),\n);\n\nexport const CreateModelModal = withErrorBoundary(ErrorUtils.mlflowServices.MODEL_REGISTRY, CreateModelModalWithRouter);\n", "import React, { useState } from 'react';\nimport { Button, ButtonProps } from '@databricks/design-system';\nimport { CreateModelModal } from './CreateModelModal';\nimport { FormattedMessage } from 'react-intl';\n\ntype Props = {\n  buttonType?: ButtonProps['type'];\n  buttonText?: React.ReactNode;\n};\n\nexport function CreateModelButton({\n  buttonType = 'primary',\n  buttonText = <FormattedMessage defaultMessage=\"Create Model\" description=\"Create button to register a new model\" />,\n}: Props) {\n  const [modalVisible, setModalVisible] = useState<boolean>(false);\n\n  const hideModal = () => {\n    setModalVisible(false);\n  };\n\n  const showModal = () => {\n    setModalVisible(true);\n  };\n\n  return (\n    <div css={styles.wrapper}>\n      <Button\n        componentId=\"codegen_mlflow_app_src_model-registry_components_CreateModelButton.tsx_28\"\n        className=\"create-model-btn\"\n        css={styles.getButtonSize(buttonType)}\n        type={buttonType}\n        onClick={showModal}\n        data-testid=\"create-model-button\"\n      >\n        {buttonText}\n      </Button>\n      <CreateModelModal modalVisible={modalVisible} hideModal={hideModal} />\n    </div>\n  );\n}\n\nconst styles = {\n  getButtonSize: (buttonType: string) =>\n    buttonType === 'primary'\n      ? {\n          height: '40px',\n          width: 'fit-content',\n        }\n      : { padding: '0px' },\n  wrapper: { display: 'inline' },\n};\n", "import {\n  Legacy<PERSON>ool<PERSON>,\n  TableFilterLayout,\n  Button,\n  TableFilterInput,\n  InfoIcon,\n  Popover,\n  Typography,\n} from '@databricks/design-system';\nimport { useEffect, useState } from 'react';\nimport { FormattedMessage, defineMessage, useIntl } from 'react-intl';\nimport { ExperimentSearchSyntaxDocUrl } from '../../../common/constants';\n\nexport interface ModelListFiltersProps {\n  searchFilter: string;\n  onSearchFilterChange: (newValue: string) => void;\n  isFiltered: boolean;\n}\n\nexport const ModelSearchInputHelpTooltip = ({\n  exampleEntityName = 'my_model_name',\n}: {\n  exampleEntityName?: string;\n}) => {\n  const { formatMessage } = useIntl();\n  const tooltipIntroMessage = defineMessage({\n    defaultMessage:\n      'To search by tags or by names and tags, use a simplified version{newline}of the SQL {whereBold} clause.',\n    description: 'Tooltip string to explain how to search models from the model registry table',\n  });\n\n  // Tooltips are not expected to contain links.\n  const labelText = formatMessage(tooltipIntroMessage, { newline: ' ', whereBold: 'WHERE' });\n\n  return (\n    <Popover.Root componentId=\"codegen_mlflow_app_src_model-registry_components_model-list_modellistfilters.tsx_46\">\n      <Popover.Trigger\n        aria-label={labelText}\n        css={{ border: 0, background: 'none', padding: 0, lineHeight: 0, cursor: 'pointer' }}\n      >\n        <InfoIcon />\n      </Popover.Trigger>\n      <Popover.Content align=\"start\">\n        <div>\n          <FormattedMessage {...tooltipIntroMessage} values={{ newline: <br />, whereBold: <b>WHERE</b> }} />{' '}\n          <FormattedMessage\n            defaultMessage=\"<link>Learn more</link>\"\n            description=\"Learn more tooltip link to learn more on how to search models\"\n            values={{\n              link: (chunks) => (\n                <Typography.Link\n                  componentId=\"codegen_mlflow_app_src_model-registry_components_model-list_modellistfilters.tsx_61\"\n                  href={ExperimentSearchSyntaxDocUrl + '#syntax'}\n                  openInNewTab\n                >\n                  {chunks}\n                </Typography.Link>\n              ),\n            }}\n          />\n          <br />\n          <br />\n          <FormattedMessage defaultMessage=\"Examples:\" description=\"Text header for examples of mlflow search syntax\" />\n          <br />\n          • tags.my_key = \"my_value\"\n          <br />• name ilike \"%{exampleEntityName}%\" and tags.my_key = \"my_value\"\n        </div>\n        <Popover.Arrow />\n      </Popover.Content>\n    </Popover.Root>\n  );\n};\n\nexport const ModelListFilters = ({\n  // prettier-ignore\n  searchFilter,\n  onSearchFilterChange,\n  isFiltered,\n}: ModelListFiltersProps) => {\n  const intl = useIntl();\n\n  const [internalSearchFilter, setInternalSearchFilter] = useState(searchFilter);\n\n  const triggerSearch = () => {\n    onSearchFilterChange(internalSearchFilter);\n  };\n  useEffect(() => {\n    setInternalSearchFilter(searchFilter);\n  }, [searchFilter]);\n\n  const reset = () => {\n    onSearchFilterChange('');\n  };\n\n  return (\n    <TableFilterLayout>\n      <TableFilterInput\n        componentId=\"codegen_mlflow_app_src_model-registry_components_model-list_modellistfilters.tsx_118\"\n        placeholder={intl.formatMessage({\n          defaultMessage: 'Filter registered models by name or tags',\n          description: 'Placeholder text inside model search bar',\n        })}\n        onSubmit={triggerSearch}\n        onClear={() => {\n          setInternalSearchFilter('');\n          onSearchFilterChange('');\n        }}\n        onChange={(e) => setInternalSearchFilter(e.target.value)}\n        data-testid=\"model-search-input\"\n        suffix={<ModelSearchInputHelpTooltip />}\n        value={internalSearchFilter}\n        showSearchButton\n      />\n      {isFiltered && (\n        <Button\n          componentId=\"codegen_mlflow_app_src_model-registry_components_model-list_modellistfilters.tsx_152\"\n          type=\"tertiary\"\n          onClick={reset}\n          data-testid=\"models-list-filters-reset\"\n        >\n          <FormattedMessage defaultMessage=\"Reset filters\" description=\"Reset filters button in list\" />\n        </Button>\n      )}\n    </TableFilterLayout>\n  );\n};\n", "import { FormattedMessage } from 'react-intl';\nimport { Link } from '../../../common/utils/RoutingUtils';\nimport { useState } from 'react';\nimport {\n  Button,\n  ChevronDoubleDownIcon,\n  ChevronDoubleUpIcon,\n  LegacyTooltip,\n  Typography,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport { ModelRegistryRoutes } from '../../routes';\nimport { KeyValueEntity } from '../../../experiment-tracking/types';\nimport { MLFLOW_INTERNAL_PREFIX } from '../../../common/utils/TagUtils';\n\nconst EmptyCell = () => <>&mdash;</>;\n\nexport const ModelListTagsCell = ({ tags }: { tags: KeyValueEntity[] }) => {\n  const tagsToShowInitially = 3;\n  const { theme } = useDesignSystemTheme();\n  const [showMore, setShowMore] = useState(false);\n\n  const validTags = tags?.filter((tag) => !tag.key.startsWith(MLFLOW_INTERNAL_PREFIX));\n\n  const tagsToDisplay = validTags?.slice(0, showMore ? undefined : tagsToShowInitially);\n\n  if (!validTags?.length) {\n    return <EmptyCell />;\n  }\n\n  const noValue = (\n    <em>\n      <FormattedMessage description=\"Models table > tags column > no value\" defaultMessage=\"(empty)\" />\n    </em>\n  );\n\n  return (\n    <div>\n      {tagsToDisplay.map((tag) => (\n        <LegacyTooltip\n          key={tag.key}\n          title={\n            <>\n              {tag.key}: {tag.value || noValue}\n            </>\n          }\n          placement=\"left\"\n        >\n          <div\n            key={tag.key}\n            css={{ overflow: 'hidden', textOverflow: 'ellipsis' }}\n            data-testid=\"models-table-tag-entry\"\n          >\n            <Typography.Text bold>{tag.key}</Typography.Text>: {tag.value || noValue}\n          </div>\n        </LegacyTooltip>\n      ))}\n      {tags.length > tagsToShowInitially && (\n        <Button\n          componentId=\"codegen_mlflow_app_src_model-registry_components_model-list_modeltablecellrenderers.tsx_65\"\n          css={{ marginTop: theme.spacing.sm }}\n          size=\"small\"\n          onClick={() => setShowMore(!showMore)}\n          icon={showMore ? <ChevronDoubleUpIcon /> : <ChevronDoubleDownIcon />}\n          data-testid=\"models-table-show-more-tags\"\n        >\n          {showMore ? (\n            <FormattedMessage\n              defaultMessage=\"Show less\"\n              description=\"Models table > tags column > show less toggle button\"\n            />\n          ) : (\n            <FormattedMessage\n              defaultMessage=\"{value} more\"\n              description=\"Models table > tags column > show more toggle button\"\n              values={{ value: validTags.length - tagsToShowInitially }}\n            />\n          )}\n        </Button>\n      )}\n    </div>\n  );\n};\n\n/**\n * Renders model version with the link in the models table\n */\nexport const ModelListVersionLinkCell = ({ versionNumber, name }: { versionNumber?: string; name: string }) => {\n  if (!versionNumber) {\n    return <EmptyCell />;\n  }\n  return (\n    <FormattedMessage\n      defaultMessage=\"<link>Version {versionNumber}</link>\"\n      description=\"Row entry for version columns in the registered model page\"\n      values={{\n        versionNumber,\n        link: (text: any) => <Link to={ModelRegistryRoutes.getModelVersionPageRoute(name, versionNumber)}>{text}</Link>,\n      }}\n    />\n  );\n};\n", "import { first, sortBy } from 'lodash';\nimport { ModelEntity } from '../../../experiment-tracking/types';\nimport { ModelVersionAliasTag } from './ModelVersionAliasTag';\nimport { Button, DropdownMenu, useDesignSystemTheme } from '@databricks/design-system';\nimport { Link } from '../../../common/utils/RoutingUtils';\nimport { ModelRegistryRoutes } from '../../routes';\nimport { FormattedMessage, defineMessage } from 'react-intl';\n\nconst versionLabel = defineMessage({\n  defaultMessage: 'Version {version}',\n  description: 'Model registry > models table > aliases column > version indicator',\n});\n\ninterface ModelsTableAliasedVersionsCellProps {\n  model: ModelEntity;\n}\n\nexport const ModelsTableAliasedVersionsCell = ({ model }: ModelsTableAliasedVersionsCellProps) => {\n  const { aliases } = model;\n  const { theme } = useDesignSystemTheme();\n\n  if (!aliases?.length) {\n    return null;\n  }\n\n  // Sort alias entries by version, descending\n  const aliasesByVersionSorted = sortBy(aliases, ({ version }) => parseInt(version, 10) || 0).reverse();\n\n  const latestVersionAlias = first(aliasesByVersionSorted);\n\n  // Return nothing if there's not a single alias present\n  if (!latestVersionAlias) {\n    return null;\n  }\n\n  const otherAliases = aliasesByVersionSorted.filter((alias) => alias !== latestVersionAlias);\n\n  return (\n    <div>\n      <Link to={ModelRegistryRoutes.getModelVersionPageRoute(model.name, latestVersionAlias.version)}>\n        <ModelVersionAliasTag value={latestVersionAlias.alias} css={{ marginRight: 0, cursor: 'pointer' }} />\n        : <FormattedMessage {...versionLabel} values={{ version: latestVersionAlias.version }} />\n      </Link>\n      {otherAliases.length > 0 && (\n        <DropdownMenu.Root modal={false}>\n          <DropdownMenu.Trigger asChild>\n            <Button\n              componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelstablealiasedversionscell.tsx_47\"\n              size=\"small\"\n              css={{ borderRadius: 12, marginLeft: theme.spacing.xs }}\n            >\n              +{aliases.length - 1}\n            </Button>\n          </DropdownMenu.Trigger>\n          <DropdownMenu.Content align=\"start\">\n            {otherAliases.map(({ alias, version }) => (\n              <DropdownMenu.Item\n                componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelstablealiasedversionscell.tsx_57\"\n                key={alias}\n              >\n                <Link to={ModelRegistryRoutes.getModelVersionPageRoute(model.name, version)}>\n                  <ModelVersionAliasTag value={alias} css={{ marginRight: 0, cursor: 'pointer' }} />:{' '}\n                  <span css={{ color: theme.colors.actionTertiaryTextDefault }}>\n                    <FormattedMessage {...versionLabel} values={{ version }} />\n                  </span>\n                </Link>\n              </DropdownMenu.Item>\n            ))}\n          </DropdownMenu.Content>\n        </DropdownMenu.Root>\n      )}\n    </div>\n  );\n};\n", "import {\n  SearchIcon,\n  Table,\n  TableCell,\n  TableHeader,\n  TableRow,\n  LegacyTooltip,\n  Empty,\n  PlusIcon,\n  TableSkeletonRows,\n  WarningIcon,\n} from '@databricks/design-system';\nimport { Interpolation, Theme } from '@emotion/react';\nimport { ColumnDef, flexRender, getCoreRowModel, SortingState, useReactTable } from '@tanstack/react-table';\nimport { useMemo } from 'react';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { Link } from '../../../common/utils/RoutingUtils';\nimport { ModelListTagsCell, ModelListVersionLinkCell } from './ModelTableCellRenderers';\nimport { RegisteringModelDocUrl } from '../../../common/constants';\nimport Utils from '../../../common/utils/Utils';\nimport type { KeyValueEntity, ModelEntity, ModelVersionInfoEntity } from '../../../experiment-tracking/types';\nimport { Stages } from '../../constants';\nimport { ModelRegistryRoutes } from '../../routes';\nimport { CreateModelButton } from '../CreateModelButton';\nimport { ModelsTableAliasedVersionsCell } from '../aliases/ModelsTableAliasedVersionsCell';\nimport { useNextModelsUIContext } from '../../hooks/useNextModelsUI';\nimport { ErrorWrapper } from '../../../common/utils/ErrorWrapper';\n\nconst getLatestVersionNumberByStage = (latestVersions: ModelVersionInfoEntity[], stage: string) => {\n  const modelVersion = latestVersions && latestVersions.find((v) => v.current_stage === stage);\n  return modelVersion && modelVersion.version;\n};\n\nenum ColumnKeys {\n  NAME = 'name',\n  LATEST_VERSION = 'latest_versions',\n  LAST_MODIFIED = 'timestamp',\n  CREATED_BY = 'user_id',\n  STAGE_STAGING = 'stage_staging',\n  STAGE_PRODUCTION = 'stage_production',\n  TAGS = 'tags',\n  ALIASED_VERSIONS = 'aliased_versions',\n}\n\nexport interface ModelListTableProps {\n  modelsData: ModelEntity[];\n  pagination: React.ReactElement;\n  orderByKey: string;\n  orderByAsc: boolean;\n  isLoading: boolean;\n  error?: Error;\n  isFiltered: boolean;\n  onSortChange: (params: { orderByKey: string; orderByAsc: boolean }) => void;\n}\n\ntype EnrichedModelEntity = ModelEntity;\ntype ModelsColumnDef = ColumnDef<EnrichedModelEntity> & {\n  // Our experiments column definition houses style definitions in the metadata field\n  meta?: { styles?: Interpolation<Theme> };\n};\n\nexport const ModelListTable = ({\n  modelsData,\n  orderByAsc,\n  orderByKey,\n  onSortChange,\n  isLoading,\n  error,\n  isFiltered,\n  pagination,\n}: ModelListTableProps) => {\n  const intl = useIntl();\n\n  const { usingNextModelsUI } = useNextModelsUIContext();\n\n  const enrichedModelsData: EnrichedModelEntity[] = modelsData.map((model) => {\n    return model;\n  });\n\n  const tableColumns = useMemo(() => {\n    const columns: ModelsColumnDef[] = [\n      {\n        id: ColumnKeys.NAME,\n        enableSorting: true,\n        header: intl.formatMessage({\n          defaultMessage: 'Name',\n          description: 'Column title for model name in the registered model page',\n        }),\n        accessorKey: 'name',\n        cell: ({ getValue }) => (\n          <Link to={ModelRegistryRoutes.getModelPageRoute(String(getValue()))}>\n            <LegacyTooltip title={getValue()}>{getValue()}</LegacyTooltip>\n          </Link>\n        ),\n        meta: { styles: { minWidth: 200, flex: 1 } },\n      },\n      {\n        id: ColumnKeys.LATEST_VERSION,\n        enableSorting: false,\n\n        header: intl.formatMessage({\n          defaultMessage: 'Latest version',\n          description: 'Column title for latest model version in the registered model page',\n        }),\n        accessorKey: 'latest_versions',\n        cell: ({ getValue, row: { original } }) => {\n          const { name } = original;\n          const latestVersions = getValue() as ModelVersionInfoEntity[];\n          const latestVersionNumber =\n            (Boolean(latestVersions?.length) &&\n              Math.max(...latestVersions.map((v) => parseInt(v.version, 10))).toString()) ||\n            '';\n          return <ModelListVersionLinkCell name={name} versionNumber={latestVersionNumber} />;\n        },\n        meta: { styles: { maxWidth: 120 } },\n      },\n    ];\n    if (usingNextModelsUI) {\n      // Display aliases column only when \"new models UI\" is flipped\n      columns.push({\n        id: ColumnKeys.ALIASED_VERSIONS,\n        enableSorting: false,\n\n        header: intl.formatMessage({\n          defaultMessage: 'Aliased versions',\n          description: 'Column title for aliased versions in the registered model page',\n        }),\n        cell: ({ row: { original: modelEntity } }) => {\n          return <ModelsTableAliasedVersionsCell model={modelEntity} />;\n        },\n        meta: { styles: { minWidth: 150 } },\n      });\n    } else {\n      // If not, display legacy \"Stage\" columns\n      columns.push(\n        {\n          id: ColumnKeys.STAGE_STAGING,\n          enableSorting: false,\n\n          header: intl.formatMessage({\n            defaultMessage: 'Staging',\n            description: 'Column title for staging phase version in the registered model page',\n          }),\n          cell: ({ row: { original } }) => {\n            const { latest_versions, name } = original;\n            const versionNumber = getLatestVersionNumberByStage(latest_versions, Stages.STAGING);\n            return <ModelListVersionLinkCell name={name} versionNumber={versionNumber} />;\n          },\n          meta: { styles: { maxWidth: 120 } },\n        },\n        {\n          id: ColumnKeys.STAGE_PRODUCTION,\n          enableSorting: false,\n\n          header: intl.formatMessage({\n            defaultMessage: 'Production',\n            description: 'Column title for production phase version in the registered model page',\n          }),\n          cell: ({ row: { original } }) => {\n            const { latest_versions, name } = original;\n            const versionNumber = getLatestVersionNumberByStage(latest_versions, Stages.PRODUCTION);\n            return <ModelListVersionLinkCell name={name} versionNumber={versionNumber} />;\n          },\n          meta: { styles: { maxWidth: 120 } },\n        },\n      );\n    }\n\n    columns.push(\n      {\n        id: ColumnKeys.CREATED_BY,\n        header: intl.formatMessage({\n          defaultMessage: 'Created by',\n          description: 'Column title for created by column for a model in the registered model page',\n        }),\n        accessorKey: 'user_id',\n        enableSorting: false,\n        cell: ({ getValue, row: { original } }) => {\n          return <span title={getValue() as string}>{getValue()}</span>;\n        },\n        meta: { styles: { flex: 1 } },\n      },\n      {\n        id: ColumnKeys.LAST_MODIFIED,\n        enableSorting: true,\n        header: intl.formatMessage({\n          defaultMessage: 'Last modified',\n          description: 'Column title for last modified timestamp for a model in the registered model page',\n        }),\n        accessorKey: 'last_updated_timestamp',\n        cell: ({ getValue }) => <span>{Utils.formatTimestamp(getValue(), intl)}</span>,\n        meta: { styles: { flex: 1, maxWidth: 150 } },\n      },\n      {\n        id: ColumnKeys.TAGS,\n        header: intl.formatMessage({\n          defaultMessage: 'Tags',\n          description: 'Column title for model tags in the registered model page',\n        }),\n        enableSorting: false,\n        accessorKey: 'tags',\n        cell: ({ getValue }) => {\n          return <ModelListTagsCell tags={getValue() as KeyValueEntity[]} />;\n        },\n      },\n    );\n\n    return columns;\n  }, [\n    // prettier-ignore\n    intl,\n    usingNextModelsUI,\n  ]);\n\n  const sorting: SortingState = [{ id: orderByKey, desc: !orderByAsc }];\n\n  const setSorting = (stateUpdater: SortingState | ((state: SortingState) => SortingState)) => {\n    const [newSortState] = typeof stateUpdater === 'function' ? stateUpdater(sorting) : stateUpdater;\n    if (newSortState) {\n      onSortChange({ orderByKey: newSortState.id, orderByAsc: !newSortState.desc });\n    }\n  };\n\n  // eslint-disable-next-line prefer-const\n  let registerModelDocUrl = RegisteringModelDocUrl;\n\n  const noResultsDescription = (() => {\n    return (\n      <FormattedMessage\n        defaultMessage=\"No results. Try using a different keyword or adjusting your filters.\"\n        description=\"Models table > no results after filtering\"\n      />\n    );\n  })();\n  const emptyComponent = error ? (\n    <Empty\n      image={<WarningIcon />}\n      description={error instanceof ErrorWrapper ? error.getMessageField() : error.message}\n      title={\n        <FormattedMessage\n          defaultMessage=\"Error fetching models\"\n          description=\"Workspace models page > Error empty state title\"\n        />\n      }\n    />\n  ) : isFiltered ? (\n    // Displayed when there is no results, but any filters have been applied\n    <Empty description={noResultsDescription} image={<SearchIcon />} data-testid=\"model-list-no-results\" />\n  ) : (\n    // Displayed when there is no results with no filters applied\n    <Empty\n      description={\n        <FormattedMessage\n          defaultMessage=\"No models registered yet. <link>Learn more about registering models</link>.\"\n          description=\"Models table > no models present yet\"\n          values={{\n            link: (content: any) => (\n              <a target=\"_blank\" rel=\"noopener noreferrer\" href={registerModelDocUrl}>\n                {content}\n              </a>\n            ),\n          }}\n        />\n      }\n      image={<PlusIcon />}\n      button={\n        <CreateModelButton\n          buttonType=\"primary\"\n          buttonText={\n            <FormattedMessage defaultMessage=\"Create a model\" description=\"Create button to register a new model\" />\n          }\n        />\n      }\n    />\n  );\n\n  const isEmpty = () => (!isLoading && table.getRowModel().rows.length === 0) || error;\n\n  const table = useReactTable<EnrichedModelEntity>({\n    data: enrichedModelsData,\n    columns: tableColumns,\n    state: {\n      sorting,\n    },\n    getCoreRowModel: getCoreRowModel(),\n    getRowId: ({ id }) => id,\n    onSortingChange: setSorting,\n  });\n\n  return (\n    <>\n      <Table\n        data-testid=\"model-list-table\"\n        pagination={pagination}\n        scrollable\n        empty={isEmpty() ? emptyComponent : undefined}\n      >\n        <TableRow isHeader>\n          {table.getLeafHeaders().map((header) => (\n            <TableHeader\n              componentId=\"codegen_mlflow_app_src_model-registry_components_model-list_modellisttable.tsx_412\"\n              ellipsis\n              key={header.id}\n              sortable={header.column.getCanSort()}\n              sortDirection={header.column.getIsSorted() || 'none'}\n              onToggleSort={() => {\n                const [currentSortColumn] = sorting;\n                const changingDirection = header.column.id === currentSortColumn.id;\n                const sortDesc = changingDirection ? !currentSortColumn.desc : false;\n                header.column.toggleSorting(sortDesc);\n              }}\n              css={(header.column.columnDef as ModelsColumnDef).meta?.styles}\n            >\n              {flexRender(header.column.columnDef.header, header.getContext())}\n            </TableHeader>\n          ))}\n        </TableRow>\n        {isLoading ? (\n          <TableSkeletonRows table={table} />\n        ) : (\n          table.getRowModel().rows.map((row) => (\n            <TableRow key={row.id}>\n              {row.getAllCells().map((cell) => (\n                <TableCell ellipsis key={cell.id} css={(cell.column.columnDef as ModelsColumnDef).meta?.styles}>\n                  {flexRender(cell.column.columnDef.cell, cell.getContext())}\n                </TableCell>\n              ))}\n            </TableRow>\n          ))\n        )}\n      </Table>\n    </>\n  );\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport './ModelListView.css';\nimport Utils from '../../common/utils/Utils';\nimport {\n  REGISTERED_MODELS_PER_PAGE_COMPACT,\n  REGISTERED_MODELS_SEARCH_NAME_FIELD,\n  REGISTERED_MODELS_SEARCH_TIMESTAMP_FIELD,\n} from '../constants';\nimport { ModelRegistryDocUrl, ModelRegistryOnboardingString, onboarding } from '../../common/constants';\nimport { CreateModelButton } from './CreateModelButton';\nimport LocalStorageUtils from '../../common/utils/LocalStorageUtils';\nimport { PageHeader } from '../../shared/building_blocks/PageHeader';\n\nimport { FormattedMessage, type IntlShape, injectIntl } from 'react-intl';\nimport { Alert, CursorPagination, Spacer as DuBoisSpacer, Spacer, Typography } from '@databricks/design-system';\nimport { shouldShowModelsNextUI } from '../../common/utils/FeatureUtils';\nimport { ModelListFilters } from './model-list/ModelListFilters';\nimport { ModelListTable } from './model-list/ModelListTable';\nimport { PageContainer } from '../../common/components/PageContainer';\nimport { ModelsNextUIToggleSwitch } from './ModelsNextUIToggleSwitch';\nimport { withNextModelsUIContext } from '../hooks/useNextModelsUI';\n\nconst NAME_COLUMN_INDEX = 'name';\nconst LAST_MODIFIED_COLUMN_INDEX = 'last_updated_timestamp';\n\ntype ModelListViewImplProps = {\n  models: any[];\n  endpoints?: any;\n  showEditPermissionModal: (...args: any[]) => any;\n  permissionLevel: string;\n  selectedOwnerFilter: string;\n  selectedStatusFilter: string;\n  onOwnerFilterChange: (...args: any[]) => any;\n  onStatusFilterChange: (...args: any[]) => any;\n  searchInput: string;\n  orderByKey: string;\n  orderByAsc: boolean;\n  currentPage: number;\n  nextPageToken: string | null;\n  loading?: boolean;\n  error?: Error;\n  onSearch: (...args: any[]) => any;\n  onClickNext: (...args: any[]) => any;\n  onClickPrev: (...args: any[]) => any;\n  onClickSortableColumn: (...args: any[]) => any;\n  onSetMaxResult: (...args: any[]) => any;\n  maxResultValue: number;\n  intl: IntlShape;\n};\n\ntype ModelListViewImplState = any;\n\nexport class ModelListViewImpl extends React.Component<ModelListViewImplProps, ModelListViewImplState> {\n  constructor(props: ModelListViewImplProps) {\n    super(props);\n\n    this.state = {\n      maxResultsSelection: REGISTERED_MODELS_PER_PAGE_COMPACT,\n    };\n  }\n\n  static defaultProps = {\n    models: [],\n    searchInput: '',\n  };\n\n  disableOnboardingHelper() {\n    const onboardingInformationStore = ModelListViewImpl.getLocalStore(onboarding);\n    onboardingInformationStore.setItem('showRegistryHelper', 'false');\n  }\n\n  /**\n   * Returns a LocalStorageStore instance that can be used to persist data associated with the\n   * ModelRegistry component.\n   */\n  static getLocalStore(key: any) {\n    return LocalStorageUtils.getStoreForComponent('ModelListView', key);\n  }\n\n  componentDidMount() {\n    const pageTitle = 'MLflow Models';\n    Utils.updatePageTitle(pageTitle);\n  }\n\n  handleSearch = (event: any, searchInput: any) => {\n    event?.preventDefault();\n    this.props.onSearch(searchInput);\n  };\n\n  static getSortFieldName = (column: any) => {\n    switch (column) {\n      case NAME_COLUMN_INDEX:\n        return REGISTERED_MODELS_SEARCH_NAME_FIELD;\n      case LAST_MODIFIED_COLUMN_INDEX:\n        return REGISTERED_MODELS_SEARCH_TIMESTAMP_FIELD;\n      default:\n        return null;\n    }\n  };\n\n  unifiedTableSortChange = ({ orderByKey, orderByAsc }: any) => {\n    // Different column keys are used for sorting and data accessing,\n    // mapping to proper keys happens below\n    const fieldMappedToSortKey =\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      {\n        timestamp: 'last_updated_timestamp',\n      }[orderByKey] || orderByKey;\n\n    this.handleTableChange(undefined, undefined, {\n      field: fieldMappedToSortKey,\n      order: orderByAsc ? 'undefined' : 'descend',\n    });\n  };\n\n  handleTableChange = (pagination: any, filters: any, sorter: any) => {\n    this.props.onClickSortableColumn(ModelListViewImpl.getSortFieldName(sorter.field), sorter.order);\n  };\n\n  static getLearnMoreLinkUrl = () => ModelRegistryDocUrl;\n\n  static getLearnMoreDisplayString = () => ModelRegistryOnboardingString;\n\n  handleClickNext = () => {\n    this.props.onClickNext();\n  };\n\n  handleClickPrev = () => {\n    this.props.onClickPrev();\n  };\n\n  handleSetMaxResult = ({ item, key, keyPath, domEvent }: any) => {\n    this.props.onSetMaxResult(key);\n  };\n\n  render() {\n    // prettier-ignore\n    const {\n      models,\n      currentPage,\n      nextPageToken,\n      searchInput,\n    } = this.props;\n    const { loading, error } = this.props;\n\n    // Determine if we use any filters at the moment\n    const isFiltered =\n      // prettier-ignore\n      Boolean(searchInput);\n\n    const title = (\n      <FormattedMessage\n        defaultMessage=\"Registered Models\"\n        description=\"Header for displaying models in the model registry\"\n      />\n    );\n    return (\n      <PageContainer data-test-id=\"ModelListView-container\" usesFullHeight>\n        <div>\n          <PageHeader title={title} spacerSize=\"xs\">\n            <CreateModelButton />\n          </PageHeader>\n          {/* TODO[SHIP-6202]: Move the description to the Header prop 'description' once it's been added */}\n          <Typography.Hint>\n            {ModelListViewImpl.getLearnMoreDisplayString()}{' '}\n            <FormattedMessage\n              defaultMessage=\"<link>Learn more</link>\"\n              description=\"Learn more link on the model list page with cloud-specific link\"\n              values={{\n                link: (chunks) => (\n                  <Typography.Link\n                    componentId=\"codegen_mlflow_app_src_model-registry_components_modellistview.tsx_244\"\n                    href={ModelListViewImpl.getLearnMoreLinkUrl()}\n                    openInNewTab\n                  >\n                    {chunks}\n                  </Typography.Link>\n                ),\n              }}\n            />\n          </Typography.Hint>\n          <Spacer />\n\n          <ModelListFilters\n            searchFilter={this.props.searchInput}\n            onSearchFilterChange={(value) => this.handleSearch(null, value)}\n            isFiltered={isFiltered}\n          />\n        </div>\n        <ModelListTable\n          modelsData={models}\n          onSortChange={this.unifiedTableSortChange}\n          orderByKey={this.props.orderByKey}\n          orderByAsc={this.props.orderByAsc}\n          isLoading={loading || false}\n          error={error}\n          pagination={\n            <div\n              data-testid=\"model-list-view-pagination\"\n              css={{ width: '100%', alignItems: 'center', display: 'flex' }}\n            >\n              <div css={{ flex: 1 }}>{shouldShowModelsNextUI() && <ModelsNextUIToggleSwitch />}</div>\n              <div>\n                <CursorPagination\n                  componentId=\"codegen_mlflow_app_src_model-registry_components_modellistview.tsx_305\"\n                  hasNextPage={Boolean(nextPageToken)}\n                  hasPreviousPage={currentPage > 1}\n                  onNextPage={this.handleClickNext}\n                  onPreviousPage={this.handleClickPrev}\n                  pageSizeSelect={{\n                    onChange: (num) => this.handleSetMaxResult({ key: num }),\n                    default: this.props.maxResultValue,\n                    options: [10, 25, 50, 100],\n                  }}\n                />\n              </div>\n            </div>\n          }\n          isFiltered={isFiltered}\n        />\n      </PageContainer>\n    );\n  }\n}\n\nexport const ModelListView = withNextModelsUIContext(injectIntl<'intl', ModelListViewImplProps>(ModelListViewImpl));\n\nconst styles = {\n  nameSearchBox: {\n    width: '446px',\n  },\n  searchFlexBar: {\n    marginBottom: '24px',\n  },\n  questionMark: {\n    marginLeft: 4,\n    cursor: 'pointer',\n    color: '#888',\n  },\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { ModelListView } from './ModelListView';\nimport { connect } from 'react-redux';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport Utils from '../../common/utils/Utils';\nimport { getCombinedSearchFilter, constructSearchInputFromURLState } from '../utils/SearchUtils';\nimport {\n  AntdTableSortOrder,\n  REGISTERED_MODELS_PER_PAGE_COMPACT,\n  REGISTERED_MODELS_SEARCH_NAME_FIELD,\n} from '../constants';\nimport { searchRegisteredModelsApi } from '../actions';\nimport LocalStorageUtils from '../../common/utils/LocalStorageUtils';\nimport { withRouterNext } from '../../common/utils/withRouterNext';\nimport type { WithRouterNextProps } from '../../common/utils/withRouterNext';\nimport { ScrollablePageWrapper } from '../../common/components/ScrollablePageWrapper';\nimport { createMLflowRoutePath } from '../../common/utils/RoutingUtils';\nimport { ErrorWrapper } from '../../common/utils/ErrorWrapper';\n\ntype ModelListPageImplProps = WithRouterNextProps & {\n  models?: any[];\n  searchRegisteredModelsApi: (...args: any[]) => any;\n};\n\ntype ModelListPageImplState = {\n  orderByKey: string;\n  orderByAsc: boolean;\n  currentPage: number;\n  maxResultsSelection: number;\n  pageTokens: Record<number, string | null>;\n  loading: boolean;\n  error: Error | undefined;\n  searchInput: string;\n};\n\nexport class ModelListPageImpl extends React.Component<ModelListPageImplProps, ModelListPageImplState> {\n  constructor(props: ModelListPageImplProps) {\n    super(props);\n    this.state = {\n      orderByKey: REGISTERED_MODELS_SEARCH_NAME_FIELD,\n      orderByAsc: true,\n      currentPage: 1,\n      maxResultsSelection: this.getPersistedMaxResults(),\n      pageTokens: {},\n      loading: true,\n      error: undefined,\n      searchInput: constructSearchInputFromURLState(this.getUrlState() as Record<string, string>),\n    };\n  }\n  modelListPageStoreKey = 'ModelListPageStore';\n  defaultPersistedPageTokens = { 1: null };\n  initialSearchRegisteredModelsApiId = getUUID();\n  searchRegisteredModelsApiId = getUUID();\n  criticalInitialRequestIds = [this.initialSearchRegisteredModelsApiId];\n\n  getUrlState() {\n    return this.props.location ? Utils.getSearchParamsFromUrl(this.props.location.search) : {};\n  }\n\n  componentDidMount() {\n    const urlState = this.getUrlState();\n    const persistedPageTokens = this.getPersistedPageTokens();\n    const maxResultsForTokens = this.getPersistedMaxResults();\n    // eslint-disable-next-line react/no-did-mount-set-state\n    this.setState(\n      {\n        // @ts-expect-error TS(4111): Property 'orderByKey' comes from an index signatur... Remove this comment to see the full error message\n        orderByKey: urlState.orderByKey === undefined ? this.state.orderByKey : urlState.orderByKey,\n        orderByAsc:\n          // @ts-expect-error TS(4111): Property 'orderByAsc' comes from an index signatur... Remove this comment to see the full error message\n          urlState.orderByAsc === undefined\n            ? this.state.orderByAsc\n            : // @ts-expect-error TS(4111): Property 'orderByAsc' comes from an index signatur... Remove this comment to see the full error message\n              urlState.orderByAsc === 'true',\n        currentPage:\n          // @ts-expect-error TS(4111): Property 'page' comes from an index signature, so ... Remove this comment to see the full error message\n          urlState.page !== undefined && (urlState as any).page in persistedPageTokens\n            ? // @ts-expect-error TS(2345): Argument of type 'unknown' is not assignable to pa... Remove this comment to see the full error message\n              parseInt(urlState.page, 10)\n            : this.state.currentPage,\n        maxResultsSelection: maxResultsForTokens,\n        pageTokens: persistedPageTokens,\n      },\n      () => {\n        this.loadModels(true);\n      },\n    );\n  }\n\n  getPersistedPageTokens() {\n    const store = ModelListPageImpl.getLocalStore(this.modelListPageStoreKey);\n    if (store && store.getItem('page_tokens')) {\n      return JSON.parse(store.getItem('page_tokens'));\n    } else {\n      return this.defaultPersistedPageTokens;\n    }\n  }\n\n  setPersistedPageTokens(page_tokens: any) {\n    const store = ModelListPageImpl.getLocalStore(this.modelListPageStoreKey);\n    if (store) {\n      store.setItem('page_tokens', JSON.stringify(page_tokens));\n    }\n  }\n\n  getPersistedMaxResults() {\n    const store = ModelListPageImpl.getLocalStore(this.modelListPageStoreKey);\n    if (store && store.getItem('max_results')) {\n      return parseInt(store.getItem('max_results'), 10);\n    } else {\n      return REGISTERED_MODELS_PER_PAGE_COMPACT;\n    }\n  }\n\n  setMaxResultsInStore(max_results: any) {\n    const store = ModelListPageImpl.getLocalStore(this.modelListPageStoreKey);\n    store.setItem('max_results', max_results.toString());\n  }\n\n  /**\n   * Returns a LocalStorageStore instance that can be used to persist data associated with the\n   * ModelRegistry component.\n   */\n  static getLocalStore(key: any) {\n    return LocalStorageUtils.getSessionScopedStoreForComponent('ModelListPage', key);\n  }\n\n  // Loads the initial set of models.\n  loadModels(isInitialLoading = false) {\n    this.loadPage(this.state.currentPage, isInitialLoading);\n  }\n\n  resetHistoryState() {\n    this.setState((prevState: any) => ({\n      currentPage: 1,\n      pageTokens: this.defaultPersistedPageTokens,\n    }));\n    this.setPersistedPageTokens(this.defaultPersistedPageTokens);\n  }\n\n  /**\n   *\n   * @param orderByKey column key to sort by\n   * @param orderByAsc is sort by ascending order\n   * @returns {string} ex. 'name ASC'\n   */\n  static getOrderByExpr = (orderByKey: any, orderByAsc: any) =>\n    orderByKey ? `${orderByKey} ${orderByAsc ? 'ASC' : 'DESC'}` : '';\n\n  pollIntervalId: any;\n\n  isEmptyPageResponse = (value: any) => {\n    return !value || !value.registered_models || !value.next_page_token;\n  };\n\n  getNextPageTokenFromResponse(response: any) {\n    const { value } = response;\n    if (this.isEmptyPageResponse(value)) {\n      // Why we could be here:\n      // 1. There are no models returned: we went to the previous page but all models after that\n      //    page's token has been deleted.\n      // 2. If `next_page_token` is not returned, assume there is no next page.\n      return null;\n    } else {\n      return value.next_page_token;\n    }\n  }\n\n  updatePageState = (page: any, response = {}) => {\n    const nextPageToken = this.getNextPageTokenFromResponse(response);\n    this.setState(\n      (prevState: any) => ({\n        currentPage: page,\n\n        pageTokens: {\n          ...prevState.pageTokens,\n          [page + 1]: nextPageToken,\n        },\n      }),\n      () => {\n        this.setPersistedPageTokens(this.state.pageTokens);\n      },\n    );\n  };\n\n  handleSearch = (searchInput: any) => {\n    this.resetHistoryState();\n    this.setState({ searchInput: searchInput }, () => {\n      this.loadPage(1, false);\n    });\n  };\n\n  // Note: this method is no longer used by the UI but is used in tests. Probably best to refactor at some point.\n  handleSearchInputChange = (searchInput: any) => {\n    this.setState({ searchInput: searchInput });\n  };\n\n  updateUrlWithSearchFilter = (searchInput: any, orderByKey: any, orderByAsc: any, page: any) => {\n    const urlParams = {};\n    if (searchInput) {\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      urlParams['searchInput'] = searchInput;\n    }\n    if (orderByKey && orderByKey !== REGISTERED_MODELS_SEARCH_NAME_FIELD) {\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      urlParams['orderByKey'] = orderByKey;\n    }\n    if (orderByAsc === false) {\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      urlParams['orderByAsc'] = orderByAsc;\n    }\n    if (page && page !== 1) {\n      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n      urlParams['page'] = page;\n    }\n    const newUrl = createMLflowRoutePath(`/models?${Utils.getSearchUrlFromState(urlParams)}`);\n    if (newUrl !== this.props.location.pathname + this.props.location.search) {\n      this.props.navigate(newUrl);\n    }\n  };\n\n  handleMaxResultsChange = (key: any) => {\n    this.setState({ maxResultsSelection: parseInt(key, 10) }, () => {\n      this.resetHistoryState();\n      const { maxResultsSelection } = this.state;\n      this.setMaxResultsInStore(maxResultsSelection);\n      this.loadPage(1, false);\n    });\n  };\n\n  handleClickNext = () => {\n    const { currentPage } = this.state;\n    this.loadPage(currentPage + 1, false);\n  };\n\n  handleClickPrev = () => {\n    const { currentPage } = this.state;\n    this.loadPage(currentPage - 1, false);\n  };\n\n  handleClickSortableColumn = (orderByKey: any, sortOrder: any) => {\n    const orderByAsc = sortOrder !== AntdTableSortOrder.DESC; // default to true\n    this.setState({ orderByKey, orderByAsc }, () => {\n      this.resetHistoryState();\n      this.loadPage(1, false);\n    });\n  };\n\n  getMaxResultsSelection = () => {\n    return this.state.maxResultsSelection;\n  };\n\n  loadPage(page: any, isInitialLoading: any) {\n    const {\n      searchInput,\n      pageTokens,\n      orderByKey,\n      orderByAsc,\n      // eslint-disable-nextline\n    } = this.state;\n    this.setState({ loading: true, error: undefined });\n    this.updateUrlWithSearchFilter(searchInput, orderByKey, orderByAsc, page);\n    this.props\n      .searchRegisteredModelsApi(\n        getCombinedSearchFilter({\n          query: searchInput,\n          // eslint-disable-nextline\n        }),\n        this.state.maxResultsSelection,\n        ModelListPageImpl.getOrderByExpr(orderByKey, orderByAsc),\n        pageTokens[page],\n        isInitialLoading ? this.initialSearchRegisteredModelsApiId : this.searchRegisteredModelsApiId,\n      )\n      .then((r: any) => {\n        this.updatePageState(page, r);\n      })\n      .catch((e: any) => {\n        this.setState({ currentPage: 1, error: e });\n        this.resetHistoryState();\n      })\n      .finally(() => {\n        this.setState({ loading: false });\n      });\n  }\n\n  render() {\n    const {\n      orderByKey,\n      orderByAsc,\n      currentPage,\n      pageTokens,\n      searchInput,\n      // eslint-disable-nextline\n    } = this.state;\n    const { models } = this.props;\n    return (\n      <ScrollablePageWrapper>\n        <ModelListView\n          // @ts-expect-error TS(2322): Type '{ models: any[] | undefined; loading: any; e... Remove this comment to see the full error message\n          models={models}\n          loading={this.state.loading}\n          error={this.state.error}\n          searchInput={searchInput}\n          orderByKey={orderByKey}\n          orderByAsc={orderByAsc}\n          currentPage={currentPage}\n          nextPageToken={pageTokens[currentPage + 1]}\n          onSearch={this.handleSearch}\n          onClickNext={this.handleClickNext}\n          onClickPrev={this.handleClickPrev}\n          onClickSortableColumn={this.handleClickSortableColumn}\n          onSetMaxResult={this.handleMaxResultsChange}\n          maxResultValue={this.getMaxResultsSelection()}\n        />\n      </ScrollablePageWrapper>\n    );\n  }\n}\n\nconst mapStateToProps = (state: any) => {\n  const models = Object.values(state.entities.modelByName);\n  return {\n    models,\n  };\n};\n\nconst mapDispatchToProps = {\n  searchRegisteredModelsApi,\n};\n\nconst ModelListPageWithRouter = withRouterNext(connect(mapStateToProps, mapDispatchToProps)(ModelListPageImpl));\n\nexport const ModelListPage = ModelListPageWithRouter;\n", "import { withErrorBoundary } from '../../common/utils/withErrorBoundary';\nimport ErrorUtils from '../../common/utils/ErrorUtils';\nimport { ModelListPage } from './ModelListPage';\nconst ModelListPageWrapperImpl = () => {\n  return <ModelListPage />;\n};\nexport const ModelListPageWrapper = withErrorBoundary(\n  ErrorUtils.mlflowServices.MODEL_REGISTRY,\n  ModelListPageWrapperImpl,\n);\n\nexport default ModelListPageWrapper;\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON>b,\n  Button,\n  Spacer,\n  Dropdown,\n  Menu,\n  Header,\n  OverflowIcon,\n  useDesignSystemTheme,\n  type HeaderProps,\n} from '@databricks/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { PreviewBadge } from './PreviewBadge';\n\ntype OverflowMenuProps = {\n  menu?: {\n    id: string;\n    itemName: React.ReactNode;\n    onClick?: (...args: any[]) => any;\n    href?: string;\n  }[];\n};\n\nexport function OverflowMenu({ menu }: OverflowMenuProps) {\n  const overflowMenu = (\n    <Menu>\n      {/* @ts-expect-error TS(2532): Object is possibly 'undefined'. */}\n      {menu.map(({ id, itemName, onClick, href, ...otherProps }) => (\n        // @ts-expect-error TS(2769): No overload matches this call.\n        <Menu.Item key={id} onClick={onClick} href={href} data-test-id={id} {...otherProps}>\n          {itemName}\n        </Menu.Item>\n      ))}\n    </Menu>\n  );\n\n  // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n  return menu.length > 0 ? (\n    <Dropdown overlay={overflowMenu} trigger={['click']} placement=\"bottomLeft\" arrow>\n      <Button\n        componentId=\"codegen_mlflow_app_src_shared_building_blocks_pageheader.tsx_54\"\n        icon={<OverflowIcon />}\n        data-test-id=\"overflow-menu-trigger\"\n        aria-label=\"Open header dropdown menu\"\n      />\n    </Dropdown>\n  ) : null;\n}\n\ntype PageHeaderProps = Pick<HeaderProps, 'dangerouslyAppendEmotionCSS'> & {\n  title: React.ReactNode;\n  breadcrumbs?: React.ReactNode[];\n  preview?: boolean;\n  feedbackOrigin?: string;\n  infoPopover?: React.ReactNode;\n  children?: React.ReactNode;\n  spacerSize?: 'xs' | 'sm' | 'md' | 'lg';\n  hideSpacer?: boolean;\n  titleAddOns?: React.ReactNode | React.ReactNode[];\n};\n\n/**\n * A page header that includes:\n *   - title,\n *   - optional breadcrumb content,\n *   - optional preview mark,\n *   - optional feedback origin: shows the \"Send feedback\" button when not empty, and\n *   - optional info popover, safe to have link inside.\n */\nexport function PageHeader(props: PageHeaderProps) {\n  const {\n    title, // required\n    breadcrumbs = [],\n    titleAddOns = [],\n    preview,\n    children,\n    spacerSize,\n    hideSpacer = false,\n    dangerouslyAppendEmotionCSS,\n  } = props;\n  const { theme } = useDesignSystemTheme();\n  const intl = useIntl();\n\n  return (\n    <>\n      <Header\n        breadcrumbs={\n          breadcrumbs.length > 0 && (\n            <Breadcrumb includeTrailingCaret>\n              {breadcrumbs.map((b, i) => (\n                <Breadcrumb.Item key={i}>{b}</Breadcrumb.Item>\n              ))}\n            </Breadcrumb>\n          )\n        }\n        buttons={children}\n        title={title}\n        // prettier-ignore\n        titleAddOns={\n          <>\n            {preview && <PreviewBadge css={{ marginLeft: 0 }} />}\n            {titleAddOns}\n          </>\n        }\n        dangerouslyAppendEmotionCSS={dangerouslyAppendEmotionCSS}\n      />\n      <Spacer\n        // @ts-expect-error TS(2322): Type '{ css: { flexShrink: number; }; }' is not as... Remove this comment to see the full error message\n        css={{\n          // Ensure spacer's fixed height\n          flexShrink: 0,\n          ...(hideSpacer ? { display: 'none' } : {}),\n        }}\n        size={spacerSize}\n      />\n    </>\n  );\n}\n", "import { Tag } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport { ReadyIcon } from './utils';\n\nexport const Stages = {\n  NONE: 'None',\n  STAGING: 'Staging',\n  PRODUCTION: 'Production',\n  ARCHIVED: 'Archived',\n};\n\nexport const ACTIVE_STAGES = [Stages.STAGING, Stages.PRODUCTION];\n\nexport const StageLabels = {\n  [Stages.NONE]: 'None',\n  [Stages.STAGING]: 'Staging',\n  [Stages.PRODUCTION]: 'Production',\n  [Stages.ARCHIVED]: 'Archived',\n};\n\nexport const StageTagComponents = {\n  [Stages.NONE]: (\n    <Tag componentId=\"codegen_mlflow_app_src_model-registry_constants.tsx_37\">{StageLabels[Stages.NONE]}</Tag>\n  ),\n  [Stages.STAGING]: (\n    <Tag componentId=\"codegen_mlflow_app_src_model-registry_constants.tsx_38\" color=\"lemon\">\n      {StageLabels[Stages.STAGING]}\n    </Tag>\n  ),\n  [Stages.PRODUCTION]: (\n    <Tag componentId=\"codegen_mlflow_app_src_model-registry_constants.tsx_39\" color=\"lime\">\n      {StageLabels[Stages.PRODUCTION]}\n    </Tag>\n  ),\n  [Stages.ARCHIVED]: (\n    <Tag componentId=\"codegen_mlflow_app_src_model-registry_constants.tsx_40\" color=\"charcoal\">\n      {StageLabels[Stages.ARCHIVED]}\n    </Tag>\n  ),\n};\n\nexport interface ModelVersionActivity {\n  creation_timestamp?: number;\n  user_id?: string;\n  activity_type: ActivityTypes;\n  comment?: string;\n  last_updated_timestamp?: number;\n  from_stage?: string;\n  to_stage?: string;\n  system_comment?: string;\n  id?: string;\n}\n\nexport enum ActivityTypes {\n  APPLIED_TRANSITION = 'APPLIED_TRANSITION',\n  REQUESTED_TRANSITION = 'REQUESTED_TRANSITION',\n  SYSTEM_TRANSITION = 'SYSTEM_TRANSITION',\n  CANCELLED_REQUEST = 'CANCELLED_REQUEST',\n  APPROVED_REQUEST = 'APPROVED_REQUEST',\n  REJECTED_REQUEST = 'REJECTED_REQUEST',\n  NEW_COMMENT = 'NEW_COMMENT',\n}\n\nexport interface PendingModelVersionActivity {\n  type: ActivityTypes;\n  to_stage: string;\n}\n\nexport const EMPTY_CELL_PLACEHOLDER = <div style={{ marginTop: -12 }}>_</div>;\n\nexport const ModelVersionStatus = {\n  READY: 'READY',\n};\n\nexport const DefaultModelVersionStatusMessages = {\n  [ModelVersionStatus.READY]: (\n    <FormattedMessage defaultMessage=\"Ready.\" description=\"Default status message for model versions that are ready\" />\n  ),\n};\n\nexport const modelVersionStatusIconTooltips = {\n  [ModelVersionStatus.READY]: (\n    <FormattedMessage\n      defaultMessage=\"Ready\"\n      description=\"Tooltip text for ready model version status icon in model view page\"\n    />\n  ),\n};\n\nexport const ModelVersionStatusIcons = {\n  [ModelVersionStatus.READY]: <ReadyIcon />,\n};\n\nexport const MODEL_VERSION_STATUS_POLL_INTERVAL = 10000;\n\n// Number of registered models initially shown on the model registry list page\nconst REGISTERED_MODELS_PER_PAGE = 10;\n\n// Variant for compact tables (unified list pattern), this is\n// going to become a default soon\nexport const REGISTERED_MODELS_PER_PAGE_COMPACT = 25;\n\nexport const MAX_RUNS_IN_SEARCH_MODEL_VERSIONS_FILTER = 75; // request size has a limit of 4KB\n\nexport const REGISTERED_MODELS_SEARCH_NAME_FIELD = 'name';\n\nexport const REGISTERED_MODELS_SEARCH_TIMESTAMP_FIELD = 'timestamp';\n\nexport const AntdTableSortOrder = {\n  ASC: 'ascend',\n  DESC: 'descend',\n};\n\nexport const archiveExistingVersionToolTipText = (currentStage: string) => (\n  <FormattedMessage\n    defaultMessage=\"Model versions in the `{currentStage}` stage will be moved to the\n     `Archived` stage.\"\n    description=\"Tooltip text for transitioning existing model versions in stage to archived\n     in the model versions page\"\n    values={{ currentStage: currentStage }}\n  />\n);\n\nexport const mlflowAliasesLearnMoreLink =\n  'https://mlflow.org/docs/latest/model-registry.html#using-registered-model-aliases';\n", "import { PageWrapper } from '@databricks/design-system';\n\n/**\n * Wraps the page content in the scrollable container so e.g. constrained tables behave correctly.\n */\nexport const ScrollablePageWrapper = ({ children, className }: { children: React.ReactNode; className?: string }) => {\n  return (\n    <PageWrapper\n      // Subtract header height\n      css={{ height: 'calc(100% - 60px)' }}\n      className={className}\n    >\n      {children}\n    </PageWrapper>\n  );\n};\n", "import { MlflowService } from '../../experiment-tracking/sdk/MlflowService';\nimport { Services as ModelRegistryService } from '../../model-registry/services';\n\nexport const getExperimentNameValidator = (getExistingExperimentNames: () => string[]) => {\n  return (rule: unknown, value: string | undefined, callback: (arg?: string) => void) => {\n    if (!value) {\n      // no need to execute below validations when no value is entered\n      // eslint-disable-next-line callback-return\n      callback(undefined);\n    } else if (getExistingExperimentNames().includes(value)) {\n      // getExistingExperimentNames returns the names of all active experiments\n      // check whether the passed value is part of the list\n      // eslint-disable-next-line callback-return\n      callback(`Experiment \"${value}\" already exists.`);\n    } else {\n      // on-demand validation whether experiment already exists in deleted state\n      MlflowService.getExperimentByName({ experiment_name: value })\n        .then((res) =>\n          callback(`Experiment \"${value}\" already exists in deleted state.\n                                 You can restore the experiment, or permanently delete the\n                                 experiment from the .trash folder (under tracking server's\n                                 root folder) in order to use this experiment name again.`),\n        )\n        .catch((e) => callback(undefined)); // no experiment returned\n    }\n  };\n};\n\nexport const modelNameValidator = (rule: unknown, name: string | undefined, callback: (arg?: string) => void) => {\n  if (!name) {\n    callback(undefined);\n    return;\n  }\n\n  ModelRegistryService.getRegisteredModel({ name: name })\n    .then(() => callback(`Model \"${name}\" already exists.`))\n    .catch((e) => callback(undefined));\n};\n"], "names": ["withRouterNext", "Component", "props", "location", "useLocation", "navigate", "useNavigate", "params", "useParams", "_jsx", "PreviewBadge", "_ref", "className", "theme", "useDesignSystemTheme", "Tag", "componentId", "css", "_css", "marginLeft", "spacing", "xs", "color", "children", "FormattedMessage", "id", "defaultMessage", "LocalStorageUtils", "getStoreForComponent", "componentName", "LocalStorageStore", "join", "getSessionScopedStoreForComponent", "version", "constructor", "scope", "type", "storageObj", "this", "window", "localStorage", "sessionStorage", "loadComponentState", "storedVal", "getItem", "reactComponentStateKey", "JSON", "parse", "saveComponentState", "stateRecord", "targetValue", "toJSON", "setItem", "stringify", "withScopePrefix", "key", "value", "getModelNameFilter", "query", "REGISTERED_MODELS_SEARCH_NAME_FIELD", "resolveFilterValue", "getCombinedSearchFilter", "arguments", "length", "undefined", "filters", "initialFilter", "includes", "push", "constructSearchInputFromURLState", "urlState", "GenericInputModal", "state", "isSubmitting", "formRef", "React", "onSubmit", "async", "setState", "values", "current", "validateFields", "handleSubmit", "resetAndClearModalForm", "onRequestCloseHandler", "e", "handleSubmitFailure", "resetFields", "Utils", "logErrorAndNotifyUser", "onClose", "handleCancel", "_this$props$onCancel", "_this$props", "onCancel", "call", "render", "okText", "cancelText", "isOpen", "footer", "displayForm", "map", "child", "innerRef", "Modal", "title", "width", "visible", "onOk", "confirmLoading", "centered", "MODEL_NAME_FIELD", "CreateModelFormImpl", "learnMoreLinkUrl", "getLearnMoreLinkUrl", "_jsxs", "LegacyForm", "ref", "layout", "<PERSON><PERSON>", "name", "label", "intl", "formatMessage", "rules", "required", "message", "validator", "Input", "autoFocus", "link", "chunks", "href", "target", "ModelRegistryDocUrl", "CreateModelForm", "injectIntl", "CreateModelModalImpl", "createRegisteredModelRequestId", "getUUID", "handleCreateRegisteredModel", "result", "createRegisteredModelApi", "newModel", "registered_model", "setTimeout", "ModelRegistryRoutes", "getModelPageRoute", "debouncedModelNameValidator", "debounce", "modelNameValidator", "handleOnCancel", "navigateBackOnCancel", "modelListPageRoute", "modalVisible", "hideModal", "mapDispatchToProps", "CreateModelModalWithRouter", "connect", "CreateModelModal", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "MODEL_REGISTRY", "CreateModelButton", "buttonType", "buttonText", "setModalVisible", "useState", "styles", "wrapper", "<PERSON><PERSON>", "getButtonSize", "onClick", "showModal", "height", "padding", "display", "_ref2", "ModelSearchInputHelpTooltip", "exampleEntityName", "useIntl", "tooltipIntroMessage", "defineMessage", "labelText", "newline", "whereBold", "Popover", "Root", "<PERSON><PERSON>", "InfoIcon", "Content", "align", "Typography", "Link", "ExperimentSearchSyntaxDocUrl", "openInNewTab", "Arrow", "ModelListFilters", "_ref3", "searchFilter", "onSearchFilterChange", "isFiltered", "internalSearchFilter", "setInternalSearchFilter", "useEffect", "TableFilterLayout", "TableFilterInput", "placeholder", "triggerSearch", "onClear", "onChange", "suffix", "showSearchButton", "reset", "EmptyCell", "_Fragment", "ModelListTagsCell", "tags", "showMore", "setShowMore", "validTags", "filter", "tag", "startsWith", "MLFLOW_INTERNAL_PREFIX", "tagsToDisplay", "slice", "noValue", "LegacyTooltip", "placement", "Text", "bold", "marginTop", "sm", "size", "icon", "ChevronDoubleUpIcon", "ChevronDoubleDownIcon", "ModelListVersionLinkCell", "versionNumber", "text", "to", "getModelVersionPageRoute", "versionLabel", "_ref5", "ModelsTableAliasedVersionsCell", "model", "aliases", "aliasesByVersionSorted", "sortBy", "parseInt", "reverse", "latestVersionAlias", "first", "otherAliases", "alias", "ModelVersionAliasTag", "DropdownMenu", "modal", "<PERSON><PERSON><PERSON><PERSON>", "borderRadius", "_ref4", "colors", "actionTertiaryTextDefault", "getLatestVersionNumberByStage", "latestVersions", "stage", "modelVersion", "find", "v", "current_stage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ModelListTable", "modelsData", "orderByAsc", "orderByKey", "onSortChange", "isLoading", "error", "pagination", "usingNextModelsUI", "useNextModelsUIContext", "enrichedModelsData", "tableColumns", "useMemo", "columns", "NAME", "enableSorting", "header", "accessorKey", "cell", "getValue", "String", "meta", "min<PERSON><PERSON><PERSON>", "flex", "LATEST_VERSION", "row", "original", "latestVersionNumber", "Boolean", "Math", "max", "toString", "max<PERSON><PERSON><PERSON>", "ALIASED_VERSIONS", "modelEntity", "STAGE_STAGING", "latest_versions", "Stages", "STAGING", "STAGE_PRODUCTION", "_ref6", "PRODUCTION", "CREATED_BY", "_ref7", "LAST_MODIFIED", "_ref8", "formatTimestamp", "TAGS", "_ref9", "sorting", "desc", "registerModelDocUrl", "RegisteringModelDocUrl", "noResultsDescription", "emptyComponent", "Empty", "image", "WarningIcon", "description", "ErrorWrapper", "getMessageField", "SearchIcon", "content", "rel", "PlusIcon", "button", "table", "useReactTable", "data", "getCoreRowModel", "getRowId", "_ref0", "onSortingChange", "stateUpdater", "newSortState", "Table", "scrollable", "empty", "getRowModel", "rows", "TableRow", "<PERSON><PERSON><PERSON><PERSON>", "getLeafHeaders", "_meta", "TableHeader", "ellipsis", "sortable", "column", "getCanSort", "sortDirection", "getIsSorted", "onToggleSort", "currentSortColumn", "sortDesc", "toggleSorting", "columnDef", "flexRender", "getContext", "TableSkeletonRows", "getAllCells", "_meta2", "TableCell", "ModelListViewImpl", "super", "handleSearch", "event", "searchInput", "preventDefault", "onSearch", "unifiedTableSortChange", "fieldMappedToSortKey", "timestamp", "handleTableChange", "field", "order", "sorter", "onClickSortableColumn", "getSortFieldName", "handleClickNext", "onClickNext", "handleClickPrev", "onClickPrev", "handleSetMaxResult", "item", "keyP<PERSON>", "domEvent", "onSetMaxResult", "maxResultsSelection", "REGISTERED_MODELS_PER_PAGE_COMPACT", "disable<PERSON>n<PERSON><PERSON>el<PERSON>", "getLocalStore", "onboarding", "componentDidMount", "updatePageTitle", "models", "currentPage", "nextPageToken", "loading", "<PERSON><PERSON><PERSON><PERSON>", "usesFullHeight", "<PERSON><PERSON><PERSON><PERSON>", "spacerSize", "Hint", "getLearnMoreDisplayString", "Spacer", "shouldShowModelsNextUI", "ModelsNextUIToggleSwitch", "CursorPagination", "hasNextPage", "hasPreviousPage", "onNextPage", "onPreviousPage", "pageSizeSelect", "num", "default", "maxResultValue", "options", "defaultProps", "REGISTERED_MODELS_SEARCH_TIMESTAMP_FIELD", "ModelRegistryOnboardingString", "ModelList<PERSON>iew", "withNextModelsUIContext", "ModelListPageImpl", "_this", "modelListPageStoreKey", "defaultPersistedPageTokens", "initialSearchRegisteredModelsApiId", "searchRegisteredModelsApiId", "criticalInitialRequestIds", "pollIntervalId", "isEmptyPageResponse", "registered_models", "next_page_token", "updatePageState", "page", "response", "getNextPageTokenFromResponse", "prevState", "pageTokens", "setPersistedPageTokens", "resetHistoryState", "loadPage", "handleSearchInputChange", "updateUrlWithSearchFilter", "urlParams", "newUrl", "createMLflowRoutePath", "getSearchUrlFromState", "pathname", "search", "handleMaxResultsChange", "setMaxResultsInStore", "handleClickSortableColumn", "sortOrder", "AntdTableSortOrder", "DESC", "getMaxResultsSelection", "getPersistedMaxResults", "getUrlState", "getSearchParamsFromUrl", "persistedPageTokens", "getPersistedPageTokens", "maxResultsForTokens", "loadModels", "store", "page_tokens", "max_results", "isInitialLoading", "searchRegisteredModelsApi", "getOrderByExpr", "then", "r", "catch", "finally", "ScrollablePageWrapper", "ModelListPage", "Object", "entities", "modelByName", "ModelListPageWrapper", "ModelListPageWrapperImpl", "OverflowMenu", "menu", "overflowMenu", "<PERSON><PERSON>", "itemName", "otherProps", "Dropdown", "overlay", "trigger", "arrow", "OverflowIcon", "breadcrumbs", "titleAddOns", "preview", "hideSpacer", "dangerouslyAppendEmotionCSS", "Header", "Breadcrumb", "includeTrailingCaret", "b", "i", "buttons", "flexShrink", "NONE", "ARCHIVED", "ACTIVE_STAGES", "StageLabels", "StageTagComponents", "ActivityTypes", "style", "ModelVersionStatus", "READY", "DefaultModelVersionStatusMessages", "modelVersionStatusIconTooltips", "ModelVersionStatusIcons", "ReadyIcon", "MODEL_VERSION_STATUS_POLL_INTERVAL", "MAX_RUNS_IN_SEARCH_MODEL_VERSIONS_FILTER", "ASC", "archiveExistingVersionToolTipText", "currentStage", "mlflowAliasesLearnMoreLink", "PageWrapper", "getExperimentNameValidator", "getExistingExperimentNames", "rule", "callback", "MlflowService", "getExperimentByName", "experiment_name", "res", "ModelRegistryService", "getRegisteredModel"], "sourceRoot": ""}