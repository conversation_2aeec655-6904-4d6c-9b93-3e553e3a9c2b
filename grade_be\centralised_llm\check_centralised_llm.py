import asyncio
import json
import mlflow
import sys
from pathlib import Path

# Add src to Python path so we can import from it
sys.path.append(str(Path(__file__).parent / "src"))

from llm_manager import handle_request
from utils.logging import configure_logging

async def main():
    configure_logging()
    # mlflow.set_tracking_uri("mlruns")
    # mlflow.set_experiment("Working LLMS")

    # Single OpenAI request
    openai_single_request = {
        
    "model": "openai",
    "prompt": "Summarize the latest GPT capabilities.",
    "parameters": {
        "temperature": 0.3,
        "model": "gpt-4.1-nano",
    },
    }

    Deepseek_single_request = {
        
    "model": "deepseek",
    "prompt": "Summarize the latest deepseek capabilities.",
    "parameters": {
        "temperature": 0.3,
        "model": "deepseek-ai/DeepSeek-V3",
    },
    }

    Metallama_single_request = {
        
    "model": "meta_llama",
    "prompt": "Summarize the latest metallama capabilities.",
    "parameters": {
        "temperature": 0.3,
        "model": "meta-llama/Llama-3.3-70B-Instruct-Turbo",
    },
    }

    Qwen_single_request = {
        
    "model": "qwen",
    "prompt": "Summarize the latest qwen capabilities.",
    "parameters": {
        "temperature": 0.3,
        "model": "Qwen/Qwen2.5-72B-Instruct-Turbo",
    },
    }

    # Single Mistral request
    mistral_single_request = {
        "model": "mistral",
        "prompt": "Explain Mistral's key features in simple terms.",
       
    }
    
    gemini_request = {
        "model": "gemini",
        "prompt": "Explain Mistral's key features in simple terms.",
    }

    # Process single requests
    openai_single_response = await handle_request(json.dumps(openai_single_request))
    print("\nOpenAI Single Response:")
    print(json.dumps(openai_single_response, indent=2))

    Deepseek_single_response = await handle_request(json.dumps(Deepseek_single_request))
    print("\nDeepseek Single Response:")
    print(json.dumps(Deepseek_single_response, indent=2))

    mistral_single_response = await handle_request(json.dumps(mistral_single_request))
    print("\nMistral Single Response:")
    print(json.dumps(mistral_single_response, indent=2))

    Metallama_single_response = await handle_request(json.dumps(Metallama_single_request))
    print("\nMetallama Single Response:")
    print(json.dumps(Metallama_single_response, indent=2))

    Qwen_single_response = await handle_request(json.dumps( Qwen_single_request))
    print("\nQwen Single Response:")
    print(json.dumps(Qwen_single_response, indent=2))

    gemini_single_response = await handle_request(json.dumps(gemini_request))
    print("\nGemini Single Response:")
    print(json.dumps(gemini_single_response, indent=2))

    # Batch requests
    openai_batch = [
        {
            "model": "openai",
            "prompt": "Summarize the latest GPT capabilities.",
            "parameters": {
                "temperature": 0.3,
                "model": "gpt-4.1-nano",
            },
           
        },
        {
            "model": "openai",
            "prompt": "What are the main differences between GPT-3 and GPT-4?",
        }
    ]

    mistral_batch = [
        {
            "model": "mistral",
            "prompt": "Summarize the latest Mistral capabilities.",
        },
        {
            "model": "mistral",
            "prompt": "Where is burj khalifa located?",
        }
    ]

    # Process batches
    openai_batch_response = await handle_request(json.dumps(openai_batch))
    print("\nOpenAI Batch Response:")
    print(json.dumps(openai_batch_response, indent=2))

    mistral_batch_response = await handle_request(json.dumps(mistral_batch))
    print("\nMistral Batch Response:")
    print(json.dumps(mistral_batch_response, indent=2))

if __name__ == "__main__":
    asyncio.run(main())