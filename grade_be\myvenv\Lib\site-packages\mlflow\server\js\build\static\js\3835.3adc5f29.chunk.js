"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[3835],{20109:function(r,e,t){t.d(e,{X:function(){return l}});t(31014);var n=t(28486),o=t(48012),a=t(32599),s=t(88443),u=t(50111);function i(){return(0,u.Y)(o.SvL,{"data-testid":"fallback",title:(0,u.Y)(s.A,{id:"qAdWdK",defaultMessage:"Error"}),description:(0,u.Y)(s.A,{id:"RzZVxC",defaultMessage:"An error occurred while rendering this component."}),image:(0,u.Y)(a.j,{})})}function c(r){let{children:e,customFallbackComponent:t}=r;function o(r,e){console.error("Caught Unexpected Error: ",r,e.componentStack)}return t?(0,u.Y)(n.tH,{onError:o,FallbackComponent:t,children:e}):(0,u.Y)(n.tH,{onError:o,fallback:(0,u.Y)(i,{}),children:e})}function l(r,e,t,n){return function(r){return(0,u.Y)(c,{customFallbackComponent:n,children:(0,u.Y)(e,{...r})})}}},25869:function(r,e,t){t.d(e,{h:function(){return a}});t(31014);var n=t(93215),o=t(50111);const a=r=>e=>{const t=(0,n.zy)(),a=(0,n.Zp)(),s=(0,n.g)();return(0,o.Y)(r,{params:s,location:t,navigate:a,...e})}},28486:function(r,e,t){t.d(e,{tH:function(){return u}});var n=t(31014);function o(r,e,t,n){Object.defineProperty(r,e,{get:t,set:n,enumerable:!0,configurable:!0})}o({},"ErrorBoundary",(()=>u));o({},"ErrorBoundaryContext",(()=>a));const a=(0,n.createContext)(null),s={didCatch:!1,error:null};class u extends n.Component{state=(()=>s)();static getDerivedStateFromError(r){return{didCatch:!0,error:r}}resetErrorBoundary=(()=>{var r=this;return function(){const{error:e}=r.state;if(null!==e){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];r.props.onReset?.({args:n,reason:"imperative-api"}),r.setState(s)}}})();componentDidCatch(r,e){this.props.onError?.(r,e)}componentDidUpdate(r,e){const{didCatch:t}=this.state,{resetKeys:n}=this.props;t&&null!==e.error&&function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return r.length!==e.length||r.some(((r,t)=>!Object.is(r,e[t])))}(r.resetKeys,n)&&(this.props.onReset?.({next:n,prev:r.resetKeys,reason:"keys"}),this.setState(s))}render(){const{children:r,fallbackRender:e,FallbackComponent:t,fallback:o}=this.props,{didCatch:s,error:u}=this.state;let i=r;if(s){const r={error:u,resetErrorBoundary:this.resetErrorBoundary};if((0,n.isValidElement)(o))i=o;else if("function"===typeof e)i=e(r);else{if(!t)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");i=(0,n.createElement)(t,r)}}return(0,n.createElement)(a.Provider,{value:{didCatch:s,error:u,resetErrorBoundary:this.resetErrorBoundary}},i)}}function i(r){if(null==r||"boolean"!==typeof r.didCatch||"function"!==typeof r.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function c(){const r=(0,n.useContext)(a);i(r);const[e,t]=(0,n.useState)({error:null,hasError:!1}),o=(0,n.useMemo)((()=>({resetBoundary:()=>{r?.resetErrorBoundary(),t({error:null,hasError:!1})},showBoundary:r=>t({error:r,hasError:!0})})),[r?.resetErrorBoundary]);if(e.hasError)throw e.error;return o}o({},"useErrorBoundary",(()=>c));function l(r,e){const t=t=>(0,n.createElement)(u,e,(0,n.createElement)(r,t)),o=r.displayName||r.name||"Unknown";return t.displayName=`withErrorBoundary(${o})`,t}o({},"withErrorBoundary",(()=>l))},33835:function(r,e,t){t.r(e),t.d(e,{DirectRunPage:function(){return m}});var n=t(48012),o=t(31014),a=t(10811),s=t(93215),u=t(76010),i=t(26809),c=t(58481),l=t(45395),d=t(25869),f=t(20109),E=t(62448),h=t(50111);const p=(0,d.h)((0,a.Ng)(((r,e)=>({runInfo:r.entities.runInfosByUuid[e.params.runUuid]})))((r=>{const{runUuid:e}=(0,s.g)(),[t,d]=(0,o.useState)(),f=(0,s.Zp)(),E=(0,a.wA)();return(0,o.useEffect)((()=>{d(void 0)}),[e]),(0,o.useEffect)((()=>{if(!r.runInfo&&e){const r=(0,i.aO)(e);r.payload.catch((r=>{u.A.logErrorAndNotifyUser(r),d(r)})),E(r)}}),[E,e,r.runInfo]),(0,o.useEffect)((()=>{var e;null!==(e=r.runInfo)&&void 0!==e&&e.experimentId&&f(c.h.getRunPageRoute(r.runInfo.experimentId,r.runInfo.runUuid),{replace:!0})}),[f,r.runInfo]),404===(null===t||void 0===t?void 0:t.status)?(0,h.Y)(l.o,{}):(0,h.Y)(n.ffj,{children:(0,h.Y)(n.PLz,{})})}))),m=(0,f.X)(E.A.mlflowServices.RUN_TRACKING,p);e.default=m},62448:function(r,e,t){t.d(e,{h:function(){return u}});var n=t(39416),o=t(52350),a=t(47664);class s{}s.mlflowServices={MODEL_REGISTRY:"Model Registry",EXPERIMENTS:"Experiments",MODEL_SERVING:"Model Serving",RUN_TRACKING:"Run Tracking"};const u=(r,e)=>{if(!(r instanceof o.s))return;const{status:t}=r;let s;const u={status:t};r.getErrorCode()===a.tG.RESOURCE_DOES_NOT_EXIST&&(s=new n.m_(u)),r.getErrorCode()===a.tG.PERMISSION_DENIED&&(s=new n.i_(u)),r.getErrorCode()===a.tG.INTERNAL_ERROR&&(s=new n.PO(u)),r.getErrorCode()===a.tG.INVALID_PARAMETER_VALUE&&(s=new n.v7(u));const i=r.getMessageField();return s&&i&&(s.message=i),s};e.A=s}}]);
//# sourceMappingURL=3835.3adc5f29.chunk.js.map