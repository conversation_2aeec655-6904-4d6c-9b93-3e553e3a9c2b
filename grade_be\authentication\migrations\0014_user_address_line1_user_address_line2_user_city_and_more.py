# Generated by Django 5.0.6 on 2025-02-13 09:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0013_remove_user_subjects_alter_role_name"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="address_line1",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="address_line2",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="city",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="country",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="user",
            name="full_name",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.Add<PERSON><PERSON>(
            model_name="user",
            name="phone_number",
            field=models.Char<PERSON>ield(blank=True, max_length=20, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="user",
            name="state",
            field=models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True),
        ),
    ]
