# Generated by Django 5.1.9 on 2025-06-04 13:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0032_alter_user_google_id_alter_user_is_allowed"),
    ]

    operations = [
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("email", models.EmailField(max_length=254, unique=True)),
                ("password", models.<PERSON>r<PERSON>ield(max_length=255)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("status", models.BooleanField(default=True)),
            ],
        ),
        migrations.AddField(
            model_name="user",
            name="role_org",
            field=models.Char<PERSON>ield(
                blank=True,
                choices=[("admin", "Admin"), ("student", "Student")],
                max_length=20,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="users",
                to="authentication.organization",
            ),
        ),
    ]
