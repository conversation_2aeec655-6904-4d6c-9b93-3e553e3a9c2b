# Generated by Django 5.1.9 on 2025-06-04 16:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentication",
            "0033_organization_user_role_org_user_organization",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="account_locked_until",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="failed_login_attempts",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="organization",
            name="google_id",
            field=models.CharField(
                blank=True, max_length=255, null=True, unique=True
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="is_email_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="organization",
            name="otp",
            field=models.Char<PERSON>ield(blank=True, max_length=6, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="organization",
            name="otp_created_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="organization",
            name="password_reset_expires",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="password_reset_token",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
    ]
