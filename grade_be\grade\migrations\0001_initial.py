# Generated by Django 5.1.9 on 2025-05-28 08:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AnswerUpload",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("file", models.FileField(upload_to="answer_uploads/")),
                ("user_id", models.IntegerField(blank=True, null=True)),
                ("question_paper_type", models.CharField(max_length=20)),
                (
                    "question_paper_id",
                    models.IntegerField(blank=True, null=True),
                ),
                ("upload_date", models.DateTimeField(auto_now_add=True)),
                (
                    "ocr_updated_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("ocr_processed", models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                (
                    "ocr_json_path",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "ocr_images_dir",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                (
                    "roll_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("ocr_error", models.TextField(blank=True, null=True)),
                (
                    "ocr_processed_at",
                    models.DateTimeField(blank=True, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Board",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="GeneratedQuestionPaper",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "updated_by",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                ("upload_date", models.DateTimeField(auto_now_add=True)),
                (
                    "test_title",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "board",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "subject",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("questions", models.JSONField(default=list)),
                ("total_marks", models.IntegerField(default=0)),
                ("total_questions", models.IntegerField(default=0)),
                (
                    "file",
                    models.FileField(upload_to="question_papers/generated/"),
                ),
                (
                    "user_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Language",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="PreviousYearQuestionPaper",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "updated_by",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                ("upload_date", models.DateTimeField(auto_now_add=True)),
                (
                    "test_title",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "board",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "subject",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("questions", models.JSONField(default=list)),
                ("total_marks", models.IntegerField(default=0)),
                ("total_questions", models.IntegerField(default=0)),
                (
                    "file",
                    models.FileField(
                        upload_to="question_papers/previous_year/"
                    ),
                ),
                ("year", models.PositiveIntegerField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Question",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("subject", models.CharField(max_length=100)),
                ("topic", models.CharField(max_length=200)),
                (
                    "exam_type",
                    models.CharField(
                        choices=[
                            ("CBSE", "CBSE"),
                            ("ICSE", "ICSE"),
                            ("State Board", "State Board"),
                            ("JEE", "JEE"),
                            ("NEET", "NEET"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "complexity",
                    models.CharField(
                        choices=[
                            ("easy", "Easy"),
                            ("medium", "Medium"),
                            ("hard", "Hard"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "question_type",
                    models.CharField(
                        choices=[
                            ("multiple-choice", "Multiple Choice"),
                            ("short-answer", "Short Answer"),
                            ("long-answer", "Long Answer"),
                        ],
                        max_length=50,
                    ),
                ),
                ("marks", models.PositiveIntegerField(default=1)),
                ("question_text", models.TextField()),
                (
                    "question_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="question_images/"
                    ),
                ),
                ("options", models.JSONField(blank=True, null=True)),
                ("correct_answer", models.TextField(blank=True, null=True)),
                ("explanation", models.TextField(blank=True, null=True)),
                (
                    "explanation_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="explanation_images/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="Questions",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "updated_by",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                ("upload_date", models.DateTimeField(auto_now_add=True)),
                (
                    "test_title",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "board",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "subject",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("questions", models.JSONField(default=list)),
                ("total_marks", models.IntegerField(default=0)),
                ("total_questions", models.IntegerField(default=0)),
                (
                    "file",
                    models.FileField(upload_to="question_papers/qp_uploader"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="SampleQuestionPaper",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "updated_by",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                ("upload_date", models.DateTimeField(auto_now_add=True)),
                (
                    "test_title",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "board",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "subject",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("questions", models.JSONField(default=list)),
                ("total_marks", models.IntegerField(default=0)),
                ("total_questions", models.IntegerField(default=0)),
                (
                    "file",
                    models.FileField(upload_to="question_papers/sample/"),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Subject",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name="AnswerAssignment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("assigned_date", models.DateTimeField(auto_now_add=True)),
                ("completed", models.BooleanField(default=False)),
                (
                    "evaluator",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_answers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "answer_upload",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="grade.answerupload",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Feedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("question_number", models.IntegerField()),
                (
                    "marks_obtained",
                    models.DecimalField(decimal_places=2, max_digits=5),
                ),
                ("feedback", models.TextField()),
                ("marks_out_of", models.FloatField()),
                (
                    "complexity",
                    models.CharField(
                        choices=[
                            ("easy", "Easy"),
                            ("medium", "Medium"),
                            ("hard", "Hard"),
                        ],
                        max_length=10,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "answer_upload",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feedbacks",
                        to="grade.answerupload",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="answerupload",
            name="generated_question_paper",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="grade.generatedquestionpaper",
            ),
        ),
        migrations.CreateModel(
            name="MainRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("role", models.CharField(max_length=50)),
                ("resume", models.FileField(upload_to="resumes/")),
                (
                    "board",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("CBSE", "CBSE"),
                            ("ICSE", "ICSE"),
                            ("Stateboard", "Stateboard"),
                            ("Neet", "NEET"),
                            ("Jee", "JEE"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "subject",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("submitted_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="main_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MentorshipRequest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Pending", "Pending"),
                            ("Accepted", "Accepted"),
                            ("Rejected", "Rejected"),
                        ],
                        default="Pending",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "mentor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="received_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "sender_role",
                    models.CharField(
                        choices=[
                            ("student", "Student"),
                            ("admin", "Admin"),
                            ("evaluator", "Evaluator"),
                            ("mentor", "Mentor"),
                        ],
                        default="mentor",
                        max_length=20,
                    ),
                ),
                (
                    "recipient_role",
                    models.CharField(
                        choices=[
                            ("student", "Student"),
                            ("admin", "Admin"),
                            ("evaluator", "Evaluator"),
                            ("mentor", "Mentor"),
                        ],
                        default="student",
                        max_length=20,
                    ),
                ),
                ("message", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("is_read", models.BooleanField(default=False)),
                ("mentor_request", models.BooleanField(default=False)),
                (
                    "recipient",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="received_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_notifications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="answerupload",
            name="previous_year_question_paper",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="grade.previousyearquestionpaper",
            ),
        ),
        migrations.CreateModel(
            name="QuestionFeedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("feedback_text", models.TextField(blank=True, null=True)),
                ("marks_obtained", models.IntegerField(default=0)),
                ("created_date", models.DateTimeField(auto_now_add=True)),
                (
                    "answer_upload",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feedback",
                        to="grade.answerupload",
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name="answerupload",
            name="questions",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="grade.questions",
            ),
        ),
        migrations.AddField(
            model_name="answerupload",
            name="sample_question_paper",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="grade.samplequestionpaper",
            ),
        ),
        migrations.CreateModel(
            name="Evaluator",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("rating", models.FloatField(default=0.0)),
                (
                    "resume",
                    models.FileField(
                        blank=True, null=True, upload_to="resumes/"
                    ),
                ),
                (
                    "boards",
                    models.ManyToManyField(
                        related_name="evaluators", to="grade.board"
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="evaluator_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "languages",
                    models.ManyToManyField(
                        related_name="evaluators", to="grade.language"
                    ),
                ),
                (
                    "subjects",
                    models.ManyToManyField(
                        related_name="evaluators", to="grade.subject"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MentorStudent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "mentor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentored_students",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "student",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="monitored_by_mentors",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("mentor", "student")},
            },
        ),
        migrations.AlterUniqueTogether(
            name="answerupload",
            unique_together={
                ("user_id", "question_paper_type", "question_paper_id")
            },
        ),
    ]
