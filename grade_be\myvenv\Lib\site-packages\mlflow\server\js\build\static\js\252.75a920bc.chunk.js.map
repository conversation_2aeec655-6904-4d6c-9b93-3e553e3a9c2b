{"version": 3, "file": "static/js/252.75a920bc.chunk.js", "mappings": "6RAoBA,MAAM,KAAEA,GAASC,EAAAA,EAcjB,SAASC,EAAkBC,EAAwBC,GACjD,MAAM,KAAEC,GAASF,EAEXG,EAAc,IAAIC,OAhBC,EAgBMH,GAC/B,GAAa,WAATC,EAAmB,CAUrB,MAAO,GAAGC,OATYE,OAAOC,KAAKN,EAAWO,YAAYC,KAAKC,IAC5D,MAAMC,EAAWV,EAAWO,WAAWE,GACjCE,EAAeD,EAASE,SAAW,GAAK,cACxCC,EAAed,EAAkBW,EAAUT,EAAmB,GAC9Da,EAtBe,GAsBCb,EAAmB,GAEzC,MAAO,GAAG,IAAIG,OAAOU,KAAgBL,MAAiBI,EAAaE,MAAMD,GAAgBH,GAAc,IAGhEK,KAAK,WAAWb,IAC3D,CAEA,GAAa,UAATD,EAAkB,CACpB,MAAMY,EA/BiB,EA+BFb,EAErB,MAAO,GAAGE,UADYJ,EAAkBC,EAAWiB,MAAOhB,GAAkBc,MAAMD,KAEpF,CAEA,MAAO,GAAGX,IAAcD,GAC1B,CAAC,IAAAgB,EAAA,CAAAC,KAAA,SAAAC,OAAA,oBAED,SAASC,EAAUC,GAAmE,IAAlE,KAAEC,GAAyCD,EACzDV,GAAW,OACOY,IAAlBD,EAAKX,WACJA,YAAaW,QACWC,IAAlBD,EAAKE,UAA0BF,EAAKE,WAC7Cb,GAAW,GAEb,MAAMc,EAAcd,GAAWe,EAAAA,EAAAA,GAAC9B,EAAI,CAAC+B,MAAI,EAAAC,SAAC,gBAAoBF,EAAAA,EAAAA,GAAC9B,EAAI,CAACiC,MAAM,YAAWD,SAAC,eAEhFV,EAAO,SAAUI,EAAOA,EAAKJ,KAAO,IAE1C,OACEY,EAAAA,EAAAA,IAAClC,EAAI,CAACmC,IAAGd,EAAqBW,SAAA,CAC3BV,EAAK,IAAEO,IAGd,CAEA,SAASO,EAAYC,GAAmE,IAAlE,KAAEX,GAAyCW,EAC/D,MAAM,MAAEC,IAAUC,EAAAA,EAAAA,KACZC,EAAqB,WAAdd,EAAKrB,KAlDX,mBADkBoC,EAmD+Bf,GAlDpB,eAAegB,kBAAkBD,EAAW,eAAeE,UAkD/BzC,EAAkBwB,EAAM,GAnD1F,IAA2Be,EAqDzB,OACEX,EAAAA,EAAAA,GAAA,OACEK,KAAGS,EAAAA,EAAAA,IAAE,CACHC,WAAY,WACZC,QAASR,EAAMS,QAAQC,GACvBC,UAAWX,EAAMS,QAAQC,GACzBE,aAAcZ,EAAMS,QAAQC,IAC7B,IAAChB,SAEDQ,GAGP,CAAC,IAAAW,EAAA,CAAA7B,KAAA,UAAAC,OAAA,6BAAA6B,EAAA,CAAA9B,KAAA,SAAAC,OAAA,6BAED,MAAM8B,EAAiBC,IAAmE,IAAlE,WAAEC,GAA0DD,EAClF,MAAME,GAAgBC,EAAAA,EAAAA,SAAQF,GACxBG,GAAOC,EAAAA,EAAAA,KAGPC,EAAgBC,QAAQN,GAAcA,EAAWO,OA/E3B,MAgFrBC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAS,IAGvCC,GAAqBC,EAAAA,EAAAA,GAAqBJ,GAE1CK,GAAqBC,EAAAA,EAAAA,UAAQ,KACjC,IAAKT,EACH,OAAOL,EAET,MAAMe,EAAuBJ,EAAmBK,cAChD,OAAiB,OAAVhB,QAAU,IAAVA,OAAU,EAAVA,EACHiB,QAAQC,GACD,SAAUA,GAAaA,EAAUnD,KAAKiD,cAAcG,SAASJ,KAErEpD,MAAM,EA9FiB,IA8FQ,GACjC,CAACqC,EAAYW,EAAoBN,IAEpC,OAAIJ,GAEA1B,EAAAA,EAAAA,GAAC6C,EAAAA,IAAQ,CAAA3C,UACPF,EAAAA,EAAAA,GAAC8C,EAAAA,IAAS,CAAA5C,UACRF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sGAIfC,OAAQ,CACNC,KAAOC,IACLpD,EAAAA,EAAAA,GAAA,KAAGqD,KAAMC,EAAAA,GAA0BC,OAAO,SAASC,IAAI,aAAYtD,SAChEkD,YAWfhD,EAAAA,EAAAA,IAAAqD,EAAAA,GAAA,CAAAvD,SAAA,CACG4B,IACC1B,EAAAA,EAAAA,IAAAqD,EAAAA,GAAA,CAAAvD,SAAA,EACEF,EAAAA,EAAAA,GAAC0D,EAAAA,EAAM,KACP1D,EAAAA,EAAAA,GAAC7B,EAAAA,EAAWwF,KAAI,CAAAzD,UACdF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,2KAEfC,OAAQ,CACNU,eAAkC,OAAlBtB,QAAkB,IAAlBA,OAAkB,EAAlBA,EAAoBN,OACpC6B,WAAsB,OAAVpC,QAAU,IAAVA,OAAU,EAAVA,EAAYO,aAI9BhC,EAAAA,EAAAA,GAAC0D,EAAAA,EAAM,KACP1D,EAAAA,EAAAA,GAAC8D,EAAAA,EAAK,CACJC,YAAanC,EAAKoC,cAAc,CAAAhB,GAAA,SAC9BC,eAAe,uBAGjBgB,YAAY,mCACZC,MAAOjC,EACPkC,SAAWC,GAAMlC,EAAckC,EAAEb,OAAOW,UAE1ClE,EAAAA,EAAAA,GAAC0D,EAAAA,EAAM,OAGQ,OAAlBpB,QAAkB,IAAlBA,OAAkB,EAAlBA,EAAoBzD,KAAI,CAAC8D,EAAW0B,KACnCjE,EAAAA,EAAAA,IAACyC,EAAAA,IAAQ,CAAA3C,SAAA,EACPF,EAAAA,EAAAA,GAAC8C,EAAAA,IAAS,CAACzC,IAAGgB,EAAoCnB,UAChDF,EAAAA,EAAAA,GAACN,EAAU,CAACE,KAAM+C,OAEpB3C,EAAAA,EAAAA,GAAC8C,EAAAA,IAAS,CAACzC,IAAGiB,EAAoCpB,UAChDF,EAAAA,EAAAA,GAACM,EAAY,CAACV,KAAM+C,QALT0B,OAShB,EAEL,IAAAC,EAAA,CAAA9E,KAAA,SAAAC,OAAA,mBAAA8E,EAAA,CAAA/E,KAAA,UAAAC,OAAA,UAAA+E,EAAA,CAAAhF,KAAA,UAAAC,OAAA,UAAAgF,EAAA,CAAAjF,KAAA,SAAAC,OAAA,kBAAAiF,EAAA,CAAAlF,KAAA,SAAAC,OAAA,kBAEK,MAAMkF,EAAcC,IAA8C,IAA7C,OAAEC,EAAM,qBAAEC,GAA6BF,EACjE,MAAM,MAAEpE,IAAUC,EAAAA,EAAAA,MACXsE,EAAgBC,IAAqB7C,EAAAA,EAAAA,UAAS2C,IAC9CG,EAAiBC,IAAsB/C,EAAAA,EAAAA,UAAS2C,GAEvD,OACE1E,EAAAA,EAAAA,IAAC+E,EAAAA,IAAK,CAAC9E,IAAGiE,EAAoBpE,SAAA,EAC5BE,EAAAA,EAAAA,IAACyC,EAAAA,IAAQ,CAACuC,UAAQ,EAAAlF,SAAA,EAChBF,EAAAA,EAAAA,GAACqF,EAAAA,IAAW,CAACpB,YAAY,kCAAkC5D,IAAGkE,EAAcrE,UAC1EF,EAAAA,EAAAA,GAAC9B,EAAI,CAAC+B,MAAI,EAACI,KAAGS,EAAAA,EAAAA,IAAE,CAAEwE,YAAa9E,EAAMS,QAAQsE,GAAK/E,EAAMS,QAAQuE,IAAI,IAACtF,UACnEF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,cAKrBjD,EAAAA,EAAAA,GAACqF,EAAAA,IAAW,CAACpB,YAAY,kCAAkC5D,IAAGmE,EAActE,UAC1EF,EAAAA,EAAAA,GAAC9B,EAAI,CAAC+B,MAAI,EAAAC,UACRF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iBAMvB7C,EAAAA,EAAAA,IAAAqD,EAAAA,GAAA,CAAAvD,SAAA,EACEF,EAAAA,EAAAA,GAAC6C,EAAAA,IAAQ,CAAC4C,QAASA,IAAMT,GAAmBD,GAAiB1E,IAAGoE,EAAwBvE,UACtFF,EAAAA,EAAAA,GAAC8C,EAAAA,IAAS,CAAA5C,UACRE,EAAAA,EAAAA,IAAA,OAAKC,KAAGS,EAAAA,EAAAA,IAAE,CAAE4E,QAAS,OAAQC,WAAY,SAAUC,IAAKpF,EAAMS,QAAQuE,IAAI,IAACtF,SAAA,EACzEF,EAAAA,EAAAA,GAAA,OACEK,KAAGS,EAAAA,EAAAA,IAAE,CACH+E,MAAOrF,EAAMS,QAAQsE,GACrBO,OAAQtF,EAAMS,QAAQsE,GACtBG,QAAS,OACTC,WAAY,SACZI,eAAgB,SAChBC,IAAK,CACH7F,MAAOK,EAAMyF,OAAOC,gBAEvB,IAAChG,SAED6E,GAAiB/E,EAAAA,EAAAA,GAACmG,EAAAA,IAAe,KAAMnG,EAAAA,EAAAA,GAACoG,EAAAA,IAAc,OAEzDpG,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uBAEfC,OAAQ,CACNmD,UAAWxB,EAAOyB,OAAOtE,iBAMlC+C,IAAkB/E,EAAAA,EAAAA,GAACuB,EAAc,CAACE,WAAYoD,EAAOyB,UACtDtG,EAAAA,EAAAA,GAAC6C,EAAAA,IAAQ,CAAC4C,QAASA,IAAMP,GAAoBD,GAAkB5E,IAAGqE,EAAwBxE,UACxFF,EAAAA,EAAAA,GAAC8C,EAAAA,IAAS,CAAA5C,UACRE,EAAAA,EAAAA,IAAA,OAAKC,KAAGS,EAAAA,EAAAA,IAAE,CAAE4E,QAAS,OAAQC,WAAY,SAAUC,IAAKpF,EAAMS,QAAQuE,IAAI,IAACtF,SAAA,EACzEF,EAAAA,EAAAA,GAAA,OACEK,KAAGS,EAAAA,EAAAA,IAAE,CACH+E,MAAOrF,EAAMS,QAAQsE,GACrBO,OAAQtF,EAAMS,QAAQsE,GACtBG,QAAS,OACTC,WAAY,SACZI,eAAgB,SAChBC,IAAK,CACH7F,MAAOK,EAAMyF,OAAOC,gBAEvB,IAAChG,SAED+E,GAAkBjF,EAAAA,EAAAA,GAACmG,EAAAA,IAAe,KAAMnG,EAAAA,EAAAA,GAACoG,EAAAA,IAAc,OAE1DpG,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yBAEfC,OAAQ,CACNqD,WAAY1B,EAAO2B,QAAQxE,iBAMpCiD,IAAmBjF,EAAAA,EAAAA,GAACuB,EAAc,CAACE,WAAYoD,EAAO2B,eAEnD,C,8VCrP2B,IAAA7G,EAAA,CAAAH,KAAA,UAAAC,OAAA,oBAMhC,MAAMgH,EAAsBC,IACjC,MAAM9E,GAAOC,EAAAA,EAAAA,KACP8E,GAAWC,EAAAA,EAAAA,MAEXC,GAAiCC,EAAAA,EAAAA,SAAOC,EAAAA,EAAAA,OACxCC,GAA8BF,EAAAA,EAAAA,SAAOC,EAAAA,EAAAA,QAErC,aAAEE,GAAiBP,GAClBQ,EAASC,IAAchF,EAAAA,EAAAA,WAAS,IAChCiF,EAAgBC,IAAqBlF,EAAAA,EAAAA,WAAS,GAC/CmF,GAAWC,EAAAA,EAAAA,MAEXC,GAAcC,EAAAA,EAAAA,KAAaC,GAAsBA,EAAMC,SAASH,cAEhEI,GAAOd,EAAAA,EAAAA,UASPe,EAAyBA,KAC7BV,GAAW,GACXE,GAAkB,EAAM,EAGpBS,EAA6B1D,IACjCiD,GAAkB,GAClBU,EAAAA,EAAMC,sBAAsB5D,EAAE,EAG1B6D,GAA+BC,EAAAA,EAAAA,cAClCC,IACCb,GAASc,EAAAA,EAAAA,KAA0BC,EAAAA,EAAAA,IAAmBF,GAzCvB,GAyC6D,GAE9F,CAACb,IAGGgB,GAAwC/F,EAAAA,EAAAA,UAC5C,KAAMgG,EAAAA,EAAAA,UAASN,EAA8B,MAC7C,CAACA,KAgDHO,EAAAA,EAAAA,YAAU,KACRlB,GAASc,EAAAA,EAAAA,MAA4B,GACpC,CAACd,KAEJkB,EAAAA,EAAAA,YAAU,KACJtB,GACFI,GAASc,EAAAA,EAAAA,MACX,GACC,CAACd,EAAUJ,IAsCd,OACE9G,EAAAA,EAAAA,IAAA,OAAKqI,UAAU,4BAA2BvI,SAAA,EACxCF,EAAAA,EAAAA,GAAC0I,EAAAA,EAAM,CACLzE,YAAY,8EACZwE,UAAU,oBACVlK,KAAK,UACLkH,QA/HoBkD,KACxBxB,GAAW,EAAK,EA8HejH,UAE3BF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qBAInBjD,EAAAA,EAAAA,GAAC4I,EAAAA,EAAK,CACJC,OACE7I,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yDAEfC,OAAQ,CAAE4F,gBAAiB7B,EAAazH,KAAMuJ,mBAAoB9B,EAAa+B,WAInFnD,MAAO,IACPqB,QAASA,EACT+B,KAnHkBC,KACtBtB,EAAKuB,QAAQC,iBAAiBC,MAAMnG,IAClCmE,GAAkB,GAClB,MAAMiC,EAAoBpG,EAAOqG,EAAAA,IAC3BC,EAAa,WAAavC,EAAazH,KAAO,IAAMyH,EAAa+B,QACvE,GAAIM,IAAsBG,EAAAA,GAA+B,CACvD,MAAMC,EAAexG,EAAOyG,EAAAA,IAC5BrC,GAASsC,EAAAA,EAAAA,IAAyBF,EAAc7C,EAA+BsC,UAC5EE,MAAK,IACJ/B,GACEuC,EAAAA,EAAAA,IACEH,EACAF,EACAvC,EAAa6C,OACb7C,EAAa8C,KACb/C,EAA4BmC,YAIjCE,MAAMW,IACLnC,IACA,MAAM,QAAEmB,GAAYgB,EAAS9F,MAAqB,cAClDyC,EAASsD,EAAAA,GAAoBC,yBAAyBR,EAAcV,GAAS,IAE9EmB,MAAMrC,EACX,MACER,GACEuC,EAAAA,EAAAA,IACEP,EACAE,EACAvC,EAAa6C,OACb7C,EAAa8C,KACb/C,EAA4BmC,UAG7BE,MAAMW,IACLnC,IACA,MAAM,QAAEmB,GAAYgB,EAAS9F,MAAqB,cAClDyC,EAASsD,EAAAA,GAAoBC,yBAAyBZ,EAAmBN,GAAS,IAEnFmB,MAAMrC,EACX,GACA,EA0EEsC,OAAQxI,EAAKoC,cAAc,CAAAhB,GAAA,SACzBC,eAAe,YAGjBoH,WAAYzI,EAAKoC,cAAc,CAAAhB,GAAA,SAC7BC,eAAe,WAGjBmE,eAAgBA,EAChBkD,SAvJoBC,KACxBpD,GAAW,EAAM,EAuJbqD,UAAQ,EAAAtK,UArEVE,EAAAA,EAAAA,IAAAqD,EAAAA,GAAA,CAAAvD,SAAA,EACEF,EAAAA,EAAAA,GAAC7B,EAAAA,EAAWsM,UAAS,CAACpK,IAAGV,EAAyBO,UAChDF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gRAKfC,OAAQ,CACNC,KAAOC,IACLpD,EAAAA,EAAAA,GAAC7B,EAAAA,EAAWuM,KAAI,CACdzG,YAAY,8EACZZ,KACE,mGAGFsH,cAAY,EAAAzK,SAEXkD,UAMXpD,EAAAA,EAAAA,GAAC4K,EAAAA,GAAiB,CAChBpD,YAAaA,EACbqD,SAAUjD,EACVkD,yBAA0BxC,EAC1ByC,QAAM,WA6CN,E,2DCvLH,IAAKC,EAAiC,SAAjCA,GAAiC,OAAjCA,EAAAA,EAAiC,qCAAjCA,EAAAA,EAAiC,qBAAjCA,EAAAA,EAAiC,mBAAjCA,EAAAA,EAAiC,mBAAjCA,CAAiC,MAOtC,MAAMC,EAAgCtL,IAcK,IAdJ,QAC5CuH,EAAO,SACPoD,EAAQ,QACRY,EAAO,+BACPC,EAA8B,sBAC9BC,EAAqB,UACrBC,EAAS,KACTC,EAAON,EAAkCO,iBAOC5L,EAC1C,MAAM,MAAEa,IAAUC,EAAAA,EAAAA,KACZmH,GAAO4D,EAAAA,EAAAA,IAA6C,CACxDC,cAAe,CACbC,QAAS,GACTC,yBAAyB,KA4C7B,OANAnD,EAAAA,EAAAA,YAAU,KACJtB,GACFU,EAAKgE,OACP,GACC,CAAChE,EAAMV,KAGR9G,EAAAA,EAAAA,IAACwI,EAAAA,EAAK,CACJC,MAzCEyC,IAASN,EAAkCa,SAE3C7L,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,4BAKjBqI,IAASN,EAAkCc,QAE3C9L,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,2BAKjBqI,IAASN,EAAkCe,QAE3C/L,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,4BAMnBjD,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qBAgBjBgB,YAAY,kDACZiD,QAASA,EACToD,SAAUA,EACVF,QACEpK,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,OAInBoH,YACErK,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAInBgG,KAAMoC,GAAazD,EAAKoE,aAAaX,GAAWnL,SAAA,CAE/CkL,GACDpL,EAAAA,EAAAA,GAAC0D,EAAAA,EAAM,CAACuI,KAAK,QACbjM,EAAAA,EAAAA,GAACkM,EAAAA,IAAOC,MAAK,CAACC,QAAQ,0DAAyDlM,SAAC,aAChFF,EAAAA,EAAAA,GAACqM,EAAAA,IAAwBC,SAAQ,CAC/B9M,KAAK,UACLwD,GAAG,0DACHiB,YAAY,0DACZsI,QAAS3E,EAAK2E,QACdC,KAAM,KAERxM,EAAAA,EAAAA,GAAC0D,EAAAA,EAAM,CAACuI,KAAK,OAEZd,GAAkCD,IACjClL,EAAAA,EAAAA,GAACqM,EAAAA,IAAwBI,SAAQ,CAC/BjN,KAAK,0BACLyE,YAAY,4EACZsI,QAAS3E,EAAK2E,QAAQrM,UAEtBF,EAAAA,EAAAA,GAAC0M,EAAAA,EAAO,CACNzI,YAAY,oFACZ0I,SAASC,EAAAA,EAAAA,IAAkC1B,GAAShL,UAEpDF,EAAAA,EAAAA,GAAA,QAAMK,KAAGS,EAAAA,EAAAA,IAAE,CAAE,gBAAiB,CAAE+L,YAAarM,EAAMS,QAAQuE,KAAM,IAACtF,UAChEF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sEAGfC,OAAQ,CACN4J,cAAc9M,EAAAA,EAAAA,GAAA,QAAMK,KAAGS,EAAAA,EAAAA,IAAE,CAAEiM,WAAYvM,EAAMS,QAAQuE,IAAI,IAACtF,SAAE8M,EAAAA,GAAmB9B,KAC/E+B,eACEjN,EAAAA,EAAAA,GAAA,QAAMK,KAAGS,EAAAA,EAAAA,IAAE,CAAEiM,WAAYvM,EAAMS,QAAQuE,IAAI,IAACtF,SAAE8M,EAAAA,GAAmBE,EAAAA,GAAOC,uBAQhF,EC1HL,MAAMC,UAAqCC,EAAAA,UAGhDC,WAAAA,GAAA,SAAAC,WAAA,KAKA7F,MAA2C,CACzC8F,qBAAqB,EACrBC,mBAAoB,KACpBC,mBAAe7N,GACf,KAEF8N,oBAAuBC,IACrB,MAAM,SAAEC,GAAaC,KAAKpH,MAC1BoH,KAAKC,SAAS,CACZP,qBAAqB,EACrBC,mBAAoBG,EACpBF,cACEG,GAAQ,CACN3K,IAGA,GAFA4K,KAAKC,SAAS,CAAEP,qBAAqB,IAEjCtK,EAAJ,CACE,MAAM,wBAAEyI,GAA0B,GAAUzI,EAE5C2K,EAASD,EAAUjC,EAErB,MACD,IACH,EACF,KAEFqC,yBAA2B,KACzBF,KAAKC,SAAS,CAAEP,qBAAqB,GAAQ,EAC7C,KAEFS,qBAAwBnB,IACtB,MAAMoB,EAASxP,OAAOwE,OAAOgK,EAAAA,IAE7B,OADAiB,IAAAA,OAASD,GAASE,GAAMA,IAAMtB,IACvBoB,CAAM,CACb,CAEFG,OAAAA,GACE,MAAM,aAAEvB,GAAiBgB,KAAKpH,MACxB4H,EAAmBR,KAAKG,qBAAqBnB,GACnD,OACE9M,EAAAA,EAAAA,GAACuO,EAAAA,IAAI,CAAArO,SACFoO,EAAiBzP,KAAK2P,IACrBpO,EAAAA,EAAAA,IAACmO,EAAAA,IAAKE,KAAI,CAERhJ,QAASA,IACPqI,KAAKH,oBAAoB,CACvBpP,KAAMmQ,EAAAA,GAAcC,mBACpBC,SAAUJ,IAEbtO,SAAA,EAEDF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAGf,gBAEFjD,EAAAA,EAAAA,GAAC6O,EAAAA,IAAc,IAAG,eAEjB7B,EAAAA,GAAmBwB,KAhBf,iBAAiBA,QAqBhC,CAEAM,kBAAAA,GACE,MAAM,oBAAEtB,EAAmB,mBAAEC,EAAkB,cAAEC,GAAkBI,KAAKpG,MAExE,IAAK+F,EACH,OAAO,KAGT,MAAMtC,EACJsC,EAAmBlP,OAASmQ,EAAAA,GAAcC,oBAC1CI,EAAAA,GAAcnM,SAAS6K,EAAmBmB,UAE5C,OACE5O,EAAAA,EAAAA,GAACiL,EAA6B,CAC5B/D,QAASsG,EACTtC,QAASuC,EAAmBmB,SAC5BvD,UAAWqC,EACXpD,SAAUwD,KAAKE,yBACf5C,sBAAuB4D,EAA0BvB,GACjDtC,+BAAgCA,GAGtC,CAEA8D,MAAAA,GACE,MAAM,aAAEnC,GAAiBgB,KAAKpH,MAC9B,OACEtG,EAAAA,EAAAA,IAAA,QAAAF,SAAA,EACEF,EAAAA,EAAAA,GAACkP,EAAAA,IAAQ,CAACC,QAASrB,KAAKO,UAAWe,QAAS,CAAC,SAAU3G,UAAU,4BAA2BvI,UAC1FE,EAAAA,EAAAA,IAAA,QAAAF,SAAA,CACG8M,EAAAA,GAA+B,OAAZF,QAAY,IAAZA,EAAAA,EAAgBI,EAAAA,GAAOmC,OAC3CrP,EAAAA,EAAAA,GAACsP,EAAAA,IAAe,CAACjP,KAAGS,EAAAA,EAAAA,IAAE,CAAEyO,OAAQ,UAAWxC,YAAa,GAAG,WAG9De,KAAKgB,uBAGZ,EA9GW1B,EAIJoC,aAAe,CACpB1C,aAAcI,EAAAA,GAAOmC,MA4GlB,MAAML,EAA6BpB,GACpCA,GAEAxN,EAAAA,EAAAA,IAAA,OAAAF,SAAA,EACEF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,kBAGf,gBAEFjD,EAAAA,EAAAA,GAAC6O,EAAAA,IAAc,IAAG,eAEjB7B,EAAAA,GAAmBY,EAASgB,aAI5B,K,uJCtJ2B,IAAArP,EAAA,CAAAC,KAAA,SAAAC,OAAA,kDAE7B,MAAMgQ,EAA8B9P,IAUpC,IAVqC,QAC1C+P,EAAU,GAAE,YACZC,EAAW,QACX3G,EAAO,kBACP4G,GAMDjQ,EACC,MAAM,iBAAEkQ,EAAgB,qBAAEC,IAAyBC,EAAAA,EAAAA,GAAmC,CACpFC,MAAOL,GAAe,KACtBM,UAAWL,IAEPM,GAAYhI,EAAAA,EAAAA,cAAY,KAC5B4H,EAAqB9G,EAAQ,GAC5B,CAAC8G,EAAsB9G,IAC1B,OACE5I,EAAAA,EAAAA,IAAAqD,EAAAA,GAAA,CAAAvD,SAAA,CACG2P,EACAH,EAAQ1N,OAAS,GAChBhC,EAAAA,EAAAA,GAAC0I,EAAAA,EAAM,CACLzE,YAAY,8FACZgI,KAAK,QACL1N,KAAK,OACLkH,QAASyK,EACTrH,MAAM,cAAa3I,SACpB,SAIDE,EAAAA,EAAAA,IAAA,OAAKC,IAAGd,EAA8DW,SAAA,CACnEwP,EAAQ7Q,KAAKsR,IACZnQ,EAAAA,EAAAA,GAACoQ,EAAAA,EAAoB,CAACC,SAAO,EAACnM,MAAOiM,GAAYA,MAEnDnQ,EAAAA,EAAAA,GAAC0I,EAAAA,EAAM,CACLzE,YAAY,8FACZgI,KAAK,QACLqE,MAAMtQ,EAAAA,EAAAA,GAACuQ,EAAAA,IAAU,IACjB9K,QAASyK,EACTrH,MAAM,sBAIX,E,eCTwD,IAAAxH,EAAA,CAAA7B,KAAA,SAAAC,OAAA,mCAAA6B,EAAA,CAAA9B,KAAA,SAAAC,OAAA,oBAAAmF,EAAA,CAAApF,KAAA,SAAAC,OAAA,wDAgCxD,MAAM+Q,UAA6BnD,EAAAA,UAAsEC,WAAAA,GAAA,SAAAC,WAAA,KAC9G7F,MAAQ,CACN+I,sBAAsB,EACtBC,6BAA6B,EAC7BC,uBAAuB,EACvBC,sBAAsB,GACtB,KAEFC,QAAUxD,EAAAA,YAAkB,KAO5ByD,oBAAsB,KACpB,MAAM,UAAEC,EAAY,GAAE,aAAE9J,EAAY,SAAEN,GAAamH,KAAKpH,OAClD,QAAEsC,GAAY/B,EACpB6G,KAAKkD,qBACLlD,KAAKpH,MACFuK,sBAAsBF,EAAW/H,GACjCK,MAAK,KACJ1C,EAASsD,EAAAA,GAAoBiH,kBAAkBH,GAAW,IAE3D5G,OAAO/F,IACN0J,KAAKqD,qBACLpJ,EAAAA,EAAMC,sBAAsB5D,EAAE,GAC9B,EACJ,KAEFgN,gBAAkB,KAChBtD,KAAKC,SAAS,CAAE0C,sBAAsB,GAAO,EAC7C,KAEFY,gBAAkB,KAChBvD,KAAKC,SAAS,CAAE0C,sBAAsB,GAAQ,EAC9C,KAEFO,mBAAqB,KACnBlD,KAAKC,SAAS,CAAE2C,6BAA6B,GAAO,EACpD,KAEFS,mBAAqB,KACnBrD,KAAKC,SAAS,CAAE2C,6BAA6B,GAAQ,EACrD,KAEFY,4BAA8B,KAC5BxD,KAAKC,SAAS,CAAE4C,uBAAuB,GAAQ,EAC/C,KAEFY,4BAA+BC,GACtB1D,KAAKpH,MAAM+K,sBAAsBD,GAAanI,MAAK,KACxDyE,KAAKC,SAAS,CAAE4C,uBAAuB,GAAQ,IAEjD,KAEFe,wBAA2BtN,IACzBA,EAAEuN,kBACF7D,KAAKC,SAAS,CAAE4C,uBAAuB,GAAO,EAC9C,KAEFiB,aAAgB1O,IACd,MAAM0E,EAAOkG,KAAK+C,QAAQ1H,SACpB,UAAE4H,GAAcjD,KAAKpH,OACrB,QAAEsC,GAAY8E,KAAKpH,MAAMO,aAC/B6G,KAAKC,SAAS,CAAE6C,sBAAsB,IACtC9C,KAAKpH,MACFmL,sBAAsBd,EAAW/H,EAAS9F,EAAO1D,KAAM0D,EAAOgB,OAC9DmF,MAAK,KACJyE,KAAKC,SAAS,CAAE6C,sBAAsB,IACrChJ,EAAakK,aAAa,IAE5B3H,OAAO4H,IACNjE,KAAKC,SAAS,CAAE6C,sBAAsB,IAEtCoB,QAAQC,MAAMF,GAEd,MAAMG,EAAmBH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGM,QAEhFtK,EAAAA,EAAMuK,+BACJxE,KAAKpH,MAAM9E,KAAKoC,cACd,CAAAhB,GAAA,SACEC,eAAe,gDAGjB,CACEiP,qBAGL,GACD,EACJ,KAEFK,eAAiB5S,IAA2B,IAA1B,KAAEH,EAAI,MAAE0E,GAAYvE,EACpC,MAAM,UAAEoR,GAAcjD,KAAKpH,OACrB,QAAEsC,GAAY8E,KAAKpH,MAAMO,aAC/B,OAAO6G,KAAKpH,MAAMmL,sBAAsBd,EAAW/H,EAASxJ,EAAM0E,GAAOiG,OAAO4H,IAE9EC,QAAQC,MAAMF,GAEd,MAAMG,EAAmBH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGM,QAEhFtK,EAAAA,EAAMuK,+BACJxE,KAAKpH,MAAM9E,KAAKoC,cACd,CAAAhB,GAAA,SACEC,eAAe,gDAGjB,CACEiP,qBAGL,GACD,EACF,KAEFM,gBAAkBjT,IAAoB,IAAnB,KAAEC,GAAWD,EAC9B,MAAM,UAAEwR,GAAcjD,KAAKpH,OACrB,QAAEsC,GAAY8E,KAAKpH,MAAMO,aAC/B,OAAO6G,KAAKpH,MAAM+L,yBAAyB1B,EAAW/H,EAASxJ,GAAM2K,OAAO4H,IAE1EC,QAAQC,MAAMF,GAEd,MAAMG,EAAmBH,aAAcI,EAAAA,EAAeJ,EAAGK,kBAAoBL,EAAGM,QAEhFtK,EAAAA,EAAMuK,+BACJxE,KAAKpH,MAAM9E,KAAKoC,cACd,CAAAhB,GAAA,SACEC,eAAe,mDAGjB,CACEiP,qBAGL,GACD,EACF,KAwKFQ,kBAAoB,KAAO,IAADC,EAAAC,EAExB,MAAMC,EAAiB/E,KAAKpH,MAAMO,aAAa+B,QACzC8J,GACkB,QAAtBH,EAAA7E,KAAKpH,MAAMiJ,mBAAW,IAAAgD,GAAS,QAATC,EAAtBD,EAAwBjD,eAAO,IAAAkD,OAAT,EAAtBA,EAAiClQ,QAAOnC,IAAA,IAAC,QAAEyI,GAASzI,EAAA,OAAKyI,IAAY6J,CAAc,IAAEhU,KAAI2C,IAAA,IAAC,MAAE2O,GAAO3O,EAAA,OAAK2O,CAAK,MAC7G,GACF,OACEnQ,EAAAA,EAAAA,GAAC+S,EAAAA,EAAatE,KAAI,CAEhBuE,MAAOlF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,YAEd/C,UAEHF,EAAAA,EAAAA,GAACyP,EAA2B,CAC1BC,QAASoD,EACT9J,QAAS8E,KAAKpH,MAAMO,aAAa+B,QACjC2G,YAAa7B,KAAKpH,MAAMiJ,YACxBC,kBAAmB9B,KAAKpH,MAAMkJ,qBAV5B,0BAYc,CAEtB,CA7TFqD,iBAAAA,GACE,MAAMC,EAAY,GAAGpF,KAAKpH,MAAMqK,cAAcjD,KAAKpH,MAAMO,aAAa+B,yBACtEjB,EAAAA,EAAMoL,gBAAgBD,EACxB,CA8HAE,sBAAAA,GACE,OAAO,CACT,CAEAC,mBAAAA,CAAoBpM,GAClB,MAAM,oCAAEqM,GAAwCxF,KAAKpH,MACrD,OACE1G,EAAAA,EAAAA,GAAC+S,EAAAA,EAAatE,KAAI,CAEhBuE,MAAOlF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,UAEd/C,SAEF+G,EAAasM,SAAWC,EAAAA,GAAmBC,OAC1CzT,EAAAA,EAAAA,GAACoN,EAA4B,CAC3BN,aAAc7F,EAAayM,cAC3BC,gBAAiB1M,EAAa2M,iBAC9B/F,SAAUyF,IAGZtG,EAAAA,GAAmB/F,EAAayM,gBAb9B,wBAiBV,CAEAG,mBAAAA,CAAoB5M,GAClB,MAAM6M,GACJ9T,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,2GAGfC,OAAQ,CACNC,KAAOC,IACLpD,EAAAA,EAAAA,GAAC7B,EAAAA,EAAWuM,KAAI,CACdzG,YAAY,4EACZZ,KAAM0Q,EAAAA,GACNpJ,cAAY,EAAAzK,SAEXkD,OAMX,OACEpD,EAAAA,EAAAA,GAAC+S,EAAAA,EAAatE,KAAI,CAEhBuE,MAAOlF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,uBAEd/C,UAEHE,EAAAA,EAAAA,IAAA,OAAKC,IAAGgB,EAA4CnB,SAAA,CACjD8T,EAAAA,GAAY/M,EAAayM,gBAC1B1T,EAAAA,EAAAA,GAACiU,EAAAA,IAAa,CAACpL,MAAOiL,EAAgBI,UAAU,SAAQhU,UACtDF,EAAAA,EAAAA,GAACmU,EAAAA,EAAQ,CAAC9T,IAAGiB,UATb,iCAcV,CAEA8S,oCAAAA,CAAqCC,GACnC,OACErU,EAAAA,EAAAA,GAAC+S,EAAAA,EAAatE,KAAI,CAEhBuE,MAAOlF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,kBAEd/C,SAEF6H,EAAAA,EAAMuM,gBAAgBD,EAAoBvG,KAAKpH,MAAM9E,OANlD,2BASV,CAEA2S,wBAAAA,CAAyBC,GACvB,OACEA,IACExU,EAAAA,EAAAA,GAAC+S,EAAAA,EAAatE,KAAI,CAEhBuE,MAAOlF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,YAEd/C,SAEFsU,GANG,0BAUZ,CAEAC,6BAAAA,CAA8BC,GAC5B,OACE1U,EAAAA,EAAAA,GAAC+S,EAAAA,EAAatE,KAAI,CAEhBuE,MAAOlF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,kBAEd/C,SAEF6H,EAAAA,EAAMuM,gBAAgBI,EAAwB5G,KAAKpH,MAAM9E,OANtD,2BASV,CAEA+S,0BAAAA,GAA8B,IAADC,EAE3B,OAA4B,QAAxBA,EAAC9G,KAAKpH,MAAMO,oBAAY,IAAA2N,GAAvBA,EAAyB9K,QAI5B9J,EAAAA,EAAAA,GAAC+S,EAAAA,EAAatE,KAAI,CAEhBuE,MAAOlF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,eAIjBwF,UAAU,aAAYvI,SAErB4N,KAAK+G,kBARF,8BAJC,IAeX,CAEAC,oBAAAA,GACE,MAAM,OAAEC,GAAWjH,KAAKpH,MAAMO,aAE9B,IAAK8N,IADiB,0BACQC,KAAKD,GACjC,OAAO,KAET,MAAME,EAAcF,EAAOG,MAAM,KAC3BpM,EAAkBmM,EAAY,GAC9BlM,EAAqBkM,EAAY,GACjC9R,GACJ/C,EAAAA,EAAAA,IAAAqD,EAAAA,GAAA,CAAAvD,SAAA,EACEF,EAAAA,EAAAA,GAAC0K,EAAAA,GAAI,CACH,eAAa,mBACbyK,GAAIlL,EAAAA,GAAoBC,yBAAyBpB,EAAiBC,GAAoB7I,SAErF4I,IACI,QAEP9I,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,iCAEfC,OAAQ,CAAE6F,2BAIhB,OACE/I,EAAAA,EAAAA,GAAC+S,EAAAA,EAAatE,KAAI,CAEhBuE,MAAOlF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,gBAEd/C,SAEFiD,GANG,8BASV,CA0BAiS,eAAAA,CAAgBnO,GACd,MAAM,kBAAEoO,GAAsBvH,KAAKpH,MAWnC,MATqB,CACnBoH,KAAKsG,qCAAqCnN,EAAaoN,oBACvDvG,KAAKyG,yBAAyBtN,EAAauN,SAC3C1G,KAAK2G,8BAA8BxN,EAAayN,wBAChD5G,KAAK6G,6BACL7G,KAAKgH,uBACLO,EAAoBvH,KAAK4E,oBAAsB5E,KAAKuF,oBAAoBpM,GACxEoO,EAAoBvH,KAAK+F,oBAAoB5M,GAAgB,MAE3CvE,QAAQ4S,GAAkB,OAATA,GACvC,CAEAC,cAAAA,CAAetO,GACb,OAEEjH,EAAAA,EAAAA,GAAC+S,EAAAA,EAAY,CAACtK,UAAU,gBAAevI,SAAE4N,KAAKsH,gBAAgBnO,IAElE,CAEAuO,iBAAAA,GACE,MAAM,OAAEjC,EAAM,eAAEkC,GAAmB3H,KAAKpH,MAAMO,aAC9C,GAAIsM,IAAWC,EAAAA,GAAmBC,MAAO,CACvC,MAAMxQ,EAAiByS,EAAAA,GAAkCnC,GAGnDhV,EAAOgV,IAAWC,EAAAA,GAAmBmC,oBAAsB,QAAU,OAC3E,OACE3V,EAAAA,EAAAA,GAAC4V,EAAAA,IAAK,CACJrX,KAAMA,EACNkK,UAAW,6BAA6BlK,IACxC8T,QAASoD,GAAkBxS,EAE3BqN,KAAMuF,EAAAA,GAAwBtC,GAC9BuC,QAAM,GAGZ,CACA,OAAO,IACT,CAEAC,yBAAAA,GACE,OACE3V,EAAAA,EAAAA,IAACsI,EAAAA,EAAM,CACLzE,YAAY,4EACZ,eAAa,wBACb1F,KAAK,OACLkH,QAASqI,KAAK4D,wBAAwBxR,SAAA,EAEtCF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,SAGd,MAGT,CAEA4R,cAAAA,GACE,MAAM,aAAE5N,EAAY,QAAE+O,GAAYlI,KAAKpH,MACvC,GAAIO,EAAagP,SACf,OAGEjW,EAAAA,EAAAA,GAAA,KAAGuD,OAAO,SAASF,KAAM4D,EAAagP,SAAS/V,SAC5C4N,KAAKoI,mBAGL,GAAIF,EAAS,CAAC,IAADG,EAClB,IAAIC,EAAe,KACnB,MAAMC,EAAqC,QAA1BF,EAAGrI,KAAKpH,MAAMO,oBAAY,IAAAkP,OAAA,EAAvBA,EAAyBpB,OAI7C,OAHIsB,IACFD,EC1dD,SAA4CC,EAAqBC,GAAgB,IAADC,EACrF,OAAgE,QAAhEA,EAAOF,EAAYG,MAAM,IAAIC,OAAO,IAAIH,4BAAwB,IAAAC,OAAA,EAAzDA,EAA4D,EACrE,CDwduBG,CAAmCL,EAAaL,EAAQW,WAGvE3W,EAAAA,EAAAA,GAAC0K,EAAAA,GAAI,CAACyK,GAAIyB,EAAAA,EAAQC,gBAAgBb,EAAQc,aAAcd,EAAQW,QAASP,GAAclW,SACpF4N,KAAKoI,kBAGZ,CACA,OAAO,IACT,CAEAA,cAAAA,GACE,MAAM,aAAEjP,EAAY,QAAE+O,EAAO,eAAEe,GAAmBjJ,KAAKpH,MACvD,OAAIO,EAAagP,SAGRhP,EAAagP,SAASe,OAAO,EAAG,IAAM,MACpChB,EACFe,GAAkBf,EAAQW,QAE1B,IAEX,CAEAM,uBAAAA,GACE,MAAM,aAAEhQ,EAAY,kBAAEoO,EAAiB,SAAE1O,GAAamH,KAAKpH,MAC3D,OAAO2O,GAAoBrV,EAAAA,EAAAA,GAACyG,EAAkB,CAACQ,aAAcA,IAAmB,IAClF,CAEAiQ,aAAAA,CAAcrO,EAAYsO,GACxB,MAAMC,EAAO,CACX,CACEpU,GAAI,SACJqU,UACErX,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAInBwC,QAASqI,KAAKsD,gBACdkG,SAAUvI,EAAAA,GAAcnM,SAASkL,KAAKpH,MAAMO,aAAayM,iBAG7D,OACEtT,EAAAA,EAAAA,IAACmX,EAAAA,EAAU,CAAC1O,MAAOA,EAAOsO,YAAaA,EAAYjX,SAAA,EAC/C4N,KAAKsF,2BAA4BpT,EAAAA,EAAAA,GAACwX,EAAAA,EAAY,CAACJ,KAAMA,IACtDtJ,KAAKmJ,4BAGZ,CAEAhI,MAAAA,GACE,MAAM,UAAE8B,EAAY,GAAE,aAAE9J,EAAY,KAAE8C,EAAI,OAAElF,GAAWiJ,KAAKpH,OACtD,YAAE8K,GAAgBvK,GAClB,qBAAEwJ,EAAoB,4BAAEC,EAA2B,sBAAEC,EAAqB,qBAAEC,GAChF9C,KAAKpG,MACDmB,GACJ7I,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,uBAEfC,OAAQ,CAAEuU,WAAYxQ,EAAa+B,WAGjCmO,EAAc,EAClBnX,EAAAA,EAAAA,GAAC0K,EAAAA,GAAI,CAACyK,GAAIlL,EAAAA,GAAoByN,mBAAmBxX,UAC/CF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,yBAKnBjD,EAAAA,EAAAA,GAAC0K,EAAAA,GAAI,CAAC,eAAa,4BAA4ByK,GAAIlL,EAAAA,GAAoBiH,kBAAkBH,GAAW7Q,SACjG6Q,KAGL,OACE3Q,EAAAA,EAAAA,IAAA,OAAAF,SAAA,CACG4N,KAAKoJ,cAAcrO,EAAOsO,GAC1BrJ,KAAK0H,oBAGL1H,KAAKyH,eAAetO,IAGpB0Q,EAAAA,EAAAA,QACC3X,EAAAA,EAAAA,GAAA,OAAKK,IAAGuE,EAAgE1E,UACtEF,EAAAA,EAAAA,GAAC4X,EAAAA,EAAwB,OAK7B5X,EAAAA,EAAAA,GAAC6X,EAAAA,EAAkB,CACjBhP,OACEzI,EAAAA,EAAAA,IAAA,QAAAF,SAAA,EACEF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,gBAEd,IACD0N,EAA2D,KAAnC7C,KAAKiI,+BAGnC+B,UAAWnH,EACXoH,kBAAmBvG,EACnB,eAAa,oCAAmCtR,UAEhDF,EAAAA,EAAAA,GAACgY,EAAAA,EAAY,CACXC,gBAAiBzG,EACjB0G,SAAUpK,KAAKyD,4BACfjH,SAAUwD,KAAKwD,4BACf6G,WAAYxH,OAGhB3Q,EAAAA,EAAAA,GAAA,OAAK,eAAa,eAAcE,UAC9BF,EAAAA,EAAAA,GAAC6X,EAAAA,EAAkB,CACjBhP,OACE7I,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,SAInB8U,iBAA6D,IAA3ChQ,EAAAA,EAAMqQ,oBAAoBrO,GAAM/H,OAClD,eAAa,6BAA4B9B,UAEzCF,EAAAA,EAAAA,GAACqY,EAAAA,EACC,CACAxN,SAAUiD,KAAK+C,QACfe,aAAc9D,KAAK8D,aACnBY,gBAAiB1E,KAAK0E,gBACtBD,eAAgBzE,KAAKyE,eACrBxI,KAAMA,EACNuO,iBAAkB1H,SAIxB5Q,EAAAA,EAAAA,GAAC6X,EAAAA,EAAkB,CACjBhP,OACE7I,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,WAInB,eAAa,+BAA8B/C,UAE3CF,EAAAA,EAAAA,GAAC2E,EAAAA,EAAW,CAACE,OAAQA,OAEvB7E,EAAAA,EAAAA,GAAC4I,EAAAA,EAAK,CACJC,MAAOiF,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACnCC,eAAe,yBAGjBiE,QAASuJ,EACTrJ,eAAgBsJ,EAChBzH,KAAM6E,KAAKgD,oBACX1G,OAAQ0D,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACpCC,eAAe,WAIjBsV,OAAO,SACPjO,SAAUwD,KAAKuD,gBACfhH,WAAYyD,KAAKpH,MAAM9E,KAAKoC,cAAc,CAAAhB,GAAA,SACxCC,eAAe,WAEd/C,UAEHF,EAAAA,EAAAA,GAAA,QAAAE,UACEF,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,qFAIfC,OAAQ,CAAEuU,WAAYxQ,EAAa+B,iBAM/C,EAGF,MAMMwP,EAAqB,CAAE3G,sBAAqB,KAAEY,yBAAyB,MAEhEgG,GAAmBC,EAAAA,EAAAA,KARRC,CAACjR,EAAYkR,KACnC,MAAM,UAAE7H,GAAc6H,GAChB,QAAE5P,GAAY4P,EAAS3R,aAE7B,MAAO,CAAE8C,MADI8O,EAAAA,EAAAA,IAAoB9H,EAAW/H,EAAStB,GACtC,GAMf8Q,EAF8BE,EAG9BI,EAAAA,EAAAA,IAAwBC,EAAAA,EAAAA,IAA8CvI,K,gHExlBjE,MAAMwI,WAA6B3L,EAAAA,UAAsEC,WAAAA,GAAA,SAAAC,WAAA,KAC9G0L,6BAAuB,OACvBC,oBAAc,OAEdC,qCAAsCpS,EAAAA,EAAAA,MAAU,KAChDqS,iBAAkBrS,EAAAA,EAAAA,MAAU,KAC5BsS,6BAA8BtS,EAAAA,EAAAA,MAAU,KACxCuS,sCAAuCvS,EAAAA,EAAAA,MAAU,KACjDwS,iCAAkCxS,EAAAA,EAAAA,MAAU,KAC5CyS,6BAA8BzS,EAAAA,EAAAA,MAAU,KACxCW,MAAQ,CACN+R,0BAA2B,CAAC3L,KAAKqL,oCAAqCrL,KAAK0L,8BAC3E,KAEFE,yBAA2B,CAAC5L,KAAKyL,gCAAiCzL,KAAKsL,iBAAiB,KAExFO,yBAA2B,IACzB7L,KAAK4L,yBAAyBE,OAAOC,IACnC,MAAMC,EAAUhM,KAAKpH,MAAMqT,KAAKF,GAChC,OAAO9X,QAAQ+X,GAAWA,EAAQE,OAAO,IACxC,KAELC,SAAYC,IACV,MAAMC,EAAW,CAACrM,KAAKsM,gCAAgCF,IACvD,OAAOG,QAAQC,IAAIH,EAAS,EAC5B,KAEFI,SAAW,KACT,MAAM,UAAExJ,EAAS,QAAE/H,EAAO,SAAErC,GAAamH,KAAKpH,MAC9C,OAAKoH,KAAK6L,4BAA8B5R,EAAAA,EAAMyS,sBAErC1M,KAAKmM,WAAW9P,OAAO/F,IACH,4BAArBA,EAAEqW,gBACJ1S,EAAAA,EAAMC,sBAAsB5D,GAC5B0J,KAAKpH,MAAMuK,sBAAsBF,EAAW/H,OAASnJ,GAAW,GAChE8G,EAASsD,EAAAA,GAAoBiH,kBAAkBH,KAG/CiB,QAAQC,MAAM7N,EAChB,IAGGiW,QAAQK,SAAS,EAwC1B,KACApH,oCAAsC,CACpC1F,EACAjC,KAEA,MAAM,UAAEoF,EAAS,QAAE/H,GAAY8E,KAAKpH,MAC9BwE,EAAU0C,EAASgB,SACrBhB,EAASrP,OAASmQ,EAAAA,GAAcC,oBAClCb,KAAKpH,MACFiU,+BACC5J,EACA/H,EAAQ4R,WACR1P,EACAS,EACAmC,KAAKwL,sCAENjQ,KAAKyE,KAAKmM,UACV9P,MAAMpC,EAAAA,EAAMC,sBACjB,EACA,KAEFyJ,sBAAyBD,IACvB,MAAM,UAAET,EAAS,QAAE/H,GAAY8E,KAAKpH,MACpC,OACEoH,KAAKpH,MACFmU,sBAAsB9J,EAAW/H,EAASwI,EAAa1D,KAAKuL,6BAC5DhQ,KAAKyE,KAAKmM,UAEV9P,MAAM6H,QAAQC,MAAM,EAEzB,KAUF6I,yBAA2B,KACzBhN,KAAKpH,MAAMqU,sBAAsBjN,KAAKpH,MAAMqK,UAAU,CACtD,CA5EFqJ,+BAAAA,CAAgCF,GAC9B,MAAM,UAAEnJ,EAAS,QAAE/H,GAAY8E,KAAKpH,MACpC,OAAOoH,KAAKpH,MACTsU,mBACCjK,EACA/H,GACqB,IAArBkR,EAA4BpM,KAAKqL,oCAAsCrL,KAAKyL,iCAE7ElQ,MAAK1J,IAAqB,IAADsb,EAAA,IAAnB,MAAE/W,GAAYvE,EAEfuE,IAAUA,GAAMgX,EAAAA,GAAAA,IAAc,kBAAkBjF,UAAiD,QAAzCgF,EAAI/W,GAAMgX,EAAAA,GAAAA,IAAc,yBAAiB,IAAAD,GAArCA,EAAuCnR,QACrGgE,KAAKpH,MAAMyU,UAAUjX,GAAMgX,EAAAA,GAAAA,IAAc,kBAAkBpR,OAAQgE,KAAKsL,gBAC1E,GAEN,CAGAgC,0BAAAA,GACE,MAAM,UAAErK,EAAS,QAAE/H,GAAY8E,KAAKpH,MACpCoH,KAAKpH,MACF2U,2BAA2BtK,EAAW/H,GACtCK,MAAMsD,GACLmB,KAAKpH,MAAM4U,iBAAiBvK,EAAW/H,EAAS2D,EAAQzI,MAAO4J,KAAK0L,+BAErErP,OAAM,KAIL2D,KAAKC,UAAUwN,IAAc,CAC3B9B,0BAA2BtL,IAAAA,QAAUoN,EAAU9B,0BAA2B3L,KAAK0L,gCAC9E,GAET,CAkCAvG,iBAAAA,GAEEnF,KAAKmM,UAAS,GAAM9P,MAAM6H,QAAQC,OAClCnE,KAAKgN,2BACLhN,KAAKoL,eAAiBsC,YAAY1N,KAAKyM,SAAUkB,EAAAA,IACjD3N,KAAKsN,4BACP,CAOAM,kBAAAA,CAAmBC,GACb7N,KAAKpH,MAAMsC,UAAY2S,EAAU3S,SAAW8E,KAAKpH,MAAMqK,YAAc4K,EAAU5K,YAEjFjD,KAAKmM,UAAS,GAAM9P,MAAM6H,QAAQC,OAClCnE,KAAKsN,6BAET,CAEAQ,oBAAAA,GACEC,cAAc/N,KAAKoL,eACrB,CAEAjK,MAAAA,GACE,MAAM,UAAE8B,EAAS,QAAE/H,EAAO,aAAE/B,EAAY,QAAE+O,EAAO,eAAEe,EAAc,SAAEpQ,EAAQ,OAAE9B,EAAM,YAAE8K,GAAgB7B,KAAKpH,MAE1G,OACE1G,EAAAA,EAAAA,GAAC8b,GAAAA,EAAa,CAAA5b,UACZF,EAAAA,EAAAA,GAAC+b,GAAAA,GAAmB,CAClBC,WAAYlO,KAAKpG,MAAM+R,0BACvBvZ,SAECA,CAAC+b,EAAcC,EAAeC,KAC7B,GAAID,EAAU,CACZL,cAAc/N,KAAKoL,gBACnB,MAAMkD,EAAwBrU,EAAAA,EAAMsU,yBAClCF,EACArO,KAAKpG,MAAM+R,2BAEb,GAAI2C,EACF,OACEpc,EAAAA,EAAAA,GAACsc,GAAAA,EAAS,CACRC,WAAY,IACZC,WAAYJ,EAAsBnK,MAAMG,kBACxCqK,2BAA4BxS,EAAAA,GAAoByN,qBAItD,GAAI3P,EAAAA,EAAM2U,gBAAgBP,EAAUrO,KAAKpG,MAAM+R,2BAC7C,OACEzZ,EAAAA,EAAAA,GAACsc,GAAAA,EAAS,CACRC,WAAY,IACZC,WAAY,SAASzL,MAAc/H,mBACnCyT,2BAA4BxS,EAAAA,GAAoByN,qBAKtD,MAAMiF,EAAyBR,EAASzZ,QAAQoX,IAAkB,IAAD8C,EAC/D,OACE9O,KAAKpG,MAAM+R,0BAA0B7W,SAASkX,EAAQ9W,MACzC,QAAb4Z,EAAA9C,EAAQ7H,aAAK,IAAA2K,OAAA,EAAbA,EAAenC,kBAAmBoC,EAAAA,GAAWC,iBAAiB,IAGR,IAADC,EAAzD,GAAIJ,GAA0BA,EAAuB,GACnD,OACE3c,EAAAA,EAAAA,GAACsc,GAAAA,EAAS,CACRC,WAAY,IACZC,WAAY1O,KAAKpH,MAAM9E,KAAKoC,cAC1B,CAAAhB,GAAA,SACEC,eAAe,4EAGjB,CACE8N,UAAWA,EACX/H,QAASA,EACTgU,SAAyC,QAAjCD,EAAEJ,EAAuB,GAAG1K,aAAK,IAAA8K,OAAA,EAA/BA,EAAiC3K,oBAG/CqK,2BAA4BxS,EAAAA,GAAoByN,sBAItDuF,EAAAA,GAAAA,IAAad,EACf,KAAO,IAAIF,EACT,OAAOjc,EAAAA,EAAAA,GAACkd,GAAAA,EAAO,IACV,GAAIjW,EAET,OACEjH,EAAAA,EAAAA,GAACyY,EAAgB,CACf1H,UAAWA,EACX9J,aAAcA,EACd0I,YAAaA,EACbqG,QAASA,EACTe,eAAgBA,EAChBtF,sBAAuB3D,KAAK2D,sBAC5BR,sBAAuBnD,KAAKpH,MAAMuK,sBAClCtK,SAAUA,EACV2M,oCAAqCxF,KAAKwF,oCAC1CzO,OAAQA,EACR+K,kBAAmB9B,KAAKgN,0BAG9B,CACA,OAAO,IAAI,KAKrB,EAGF,MAyBMtC,GAAqB,CACzBwC,mBAAkB,KAClBD,sBAAqB,KACrBF,sBAAqB,KACrBF,+BAA8B,KAC9BU,2BAA0B,KAC1BC,iBAAgB,KAChBrK,sBAAqB,KACrBkK,UACF,MAEMgC,IAA6BC,EAAAA,GAAAA,IAEjC1E,EAAAA,EAAAA,KAtCsBC,CAACjR,EAAmBkR,KAC1C,MAAM7H,EAAYsM,mBAAmBzE,EAAS0E,OAAOvM,YAC/C,QAAE/H,GAAY4P,EAAS0E,OACvBrW,GAAesW,EAAAA,EAAAA,IAAgB7V,EAAOqJ,EAAW/H,GACjDnE,GAAS2Y,EAAAA,EAAAA,IAAuB9V,EAAOqJ,EAAW/H,GACxD,IAAIgN,EAAgC,KAChC/O,IAAiBA,EAAagP,WAChCD,GAAUyH,EAAAA,GAAAA,IAAWxW,GAAgBA,EAAa6C,OAAQpC,IAE5D,MACMqP,EADOf,IAAW0H,EAAAA,GAAAA,IAAW1H,EAAQW,QAASjP,IACrBsO,GAAWjO,EAAAA,EAAM4V,kBAAkB3H,EAASA,EAAQW,SAC7EhH,EAAcjI,EAAMC,SAASH,YAAYuJ,IACzC,KAAEgJ,GAASrS,EACjB,MAAO,CACLqJ,YACA/H,UACA/B,eACApC,SACAmR,UACAe,iBACAgD,OACApK,cACD,GAgBwB6I,GAAzBE,EAA6CK,EAAAA,EAAAA,IAAWC,MAG7C4E,IAAmBC,EAAAA,GAAAA,GAAkBC,GAAAA,EAAWC,eAAeC,eAAgBb,IAE5F,S,mIChVO,SAAS9U,EAAmB4V,GACjC,OAAIA,EACK,GAAGC,EAAAA,aAA6CC,EAAAA,EAAAA,IAAmBF,GAAO,KAE1E,EAEX,CAEO,SAASG,IAIP,IAJ+B,MACtCH,EAAQ,IAGT1Q,UAAAvL,OAAA,QAAAnC,IAAA0N,UAAA,GAAAA,UAAA,GAAG,CAAC,EACH,MAAM8Q,EAAU,GACVC,EAAgBL,EAAMrb,SAAS,SAAWqb,EAAQ5V,EAAmB4V,GAE3E,OADIK,GAAeD,EAAQE,KAAKD,GACzBD,EAAQhf,KAAK,QACtB,CAEO,SAASmf,EAAiCC,GAC/C,MAAI,gBAAiBA,EACZA,EAAsB,YAE3B,oBAAqBA,GAAY,mBAAoBA,EAChDpW,EAAmBoW,EAA0B,iBAAK,QAAUA,EAAyB,eAE1F,mBAAoBA,EACfA,EAAyB,eAE9B,oBAAqBA,EAChBA,EAA0B,gBAE5B,EACT,C,iFC7BO,MAAMpc,GACoBqc,EAAAA,EAAAA,YAAWrR,EAAAA,kBAA0BA,EAAAA,iBAAyBsR,EAAAA,Q,4LCK/F,MAAM,OAAEC,EAAM,SAAEC,GAAaC,EAAAA,IAEvBC,EAAyB,mBAGlBtV,EAAgC,QAAQsV,SACxCxV,EAAuB,gBACvBI,EAAmB,YAYzB,MAAMiB,UAA0ByC,EAAAA,UAA8BC,WAAAA,GAAA,SAAAC,WAAA,KACnE7F,MAAQ,CACNsX,cAAe,MACf,KAEFC,wBAA2BD,IACzBlR,KAAKC,SAAS,CAAEiR,iBAAgB,EAChC,KAEFE,mBAAqB,CAACC,EAAWjb,EAAYkb,KAC3C,MAAM,YAAE5X,GAAgBsG,KAAKpH,MAC7B0Y,EAAS5X,EAAYtD,GAAS,UAAUA,0BAA2BrE,EAAU,EAC7E,KAEFwf,mBAAqB,CAAClX,EAAYmX,KAE6B,KAD9CA,GAAUA,EAAOpb,OAAU,IAC7BzB,cAAc8c,QAAQpX,EAAM1F,cACzC,CAEF+c,qBAAAA,GACE,MAAM,OAAEzU,GAAW+C,KAAKpH,OAClB,cAAEsY,GAAkBlR,KAAKpG,MAG/B,IAAKsX,GAFoBA,IAAkBvV,EAGzC,OAAO,KAGT,MAAMgW,EAAc1U,GAClB/K,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,wEAEfC,OAAQ,CAAE8b,cAAeA,MAG3Bhf,EAAAA,EAAAA,GAAC+C,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,oEAEfC,OAAQ,CAAE8b,cAAeA,KAI7B,OAAOhf,EAAAA,EAAAA,GAAA,KAAGyI,UAAU,yBAAwBvI,SAAEuf,GAChD,CAEAC,WAAAA,CAAY1P,GACV,OACEhQ,EAAAA,EAAAA,GAAC4e,EAAM,CAAC1a,MAAO8L,EAAMxQ,KAAKU,SACvB8P,EAAMxQ,MADuBwQ,EAAMxQ,KAI1C,CACAyP,MAAAA,GACE,MAAM,YAAEzH,EAAW,SAAEqD,EAAQ,OAAEE,GAAW+C,KAAKpH,OACzC,cAAEsY,GAAkBlR,KAAKpG,MACzBiY,EAAmBX,IAAkBvV,EAC3C,OAEErJ,EAAAA,EAAAA,IAACwf,EAAAA,IAAU,CAACC,IAAKhV,EAAUiV,OAAO,WAAWrX,UAAU,sBAAqBvI,SAAA,EAE1EF,EAAAA,EAAAA,GAAC4f,EAAAA,IAAWnR,KAAI,CACduE,MAAOjI,GAAS/K,EAAAA,EAAAA,GAAA,KAAAE,SAAG,kBAAoB,QACvCV,KAAM+J,EACNwW,MAAO,CAAC,CAAE9gB,UAAU,EAAMoT,QAAS,+CAAgDnS,UAEnFE,EAAAA,EAAAA,IAAC0e,EAAAA,IAAY,CACXkB,kBAAkB,wBAClB7b,SAAU2J,KAAKmR,wBACflb,YAAY,iBACZkc,aAAcnS,KAAKuR,mBACnBa,SAAUpS,KAAKpH,MAAMoE,yBAErBqV,YAAU,EAAAjgB,SAAA,EAEVE,EAAAA,EAAAA,IAACwe,EAAM,CAAC1a,MAAOuF,EAA+BhB,UAAU,0BAAyBvI,SAAA,EAC/EF,EAAAA,EAAAA,GAAA,KAAGyI,UAAU,mBAAmB2X,MAAO,CAAEC,SAAU,MAAQ,IAAEtB,MAE/D/e,EAAAA,EAAAA,GAAC6e,EAAQ,CAAC7L,MAAM,SAAQ9S,SAAExB,OAAOwE,OAAOsE,GAAa3I,KAAKmR,GAAUlC,KAAK4R,YAAY1P,YAKxF2P,GACC3f,EAAAA,EAAAA,GAAC4f,EAAAA,IAAWnR,KAAI,CACduE,MAAM,aACNxT,KAAMmK,EACNoW,MAAO,CACL,CAAE9gB,UAAU,EAAMoT,QAAS,0CAC3B,CAAEiO,UAAWxS,KAAKoR,qBAClBhf,UAEFF,EAAAA,EAAAA,GAAC8D,EAAAA,EAAK,CACJG,YAAY,6EACZF,YAAY,yBAGd,KAGH+J,KAAK0R,0BAGZ,E", "sources": ["model-registry/components/SchemaTable.tsx", "model-registry/components/PromoteModelButton.tsx", "model-registry/components/ModelStageTransitionFormModal.tsx", "model-registry/components/ModelStageTransitionDropdown.tsx", "model-registry/components/aliases/ModelVersionViewAliasEditor.tsx", "model-registry/components/ModelVersionView.tsx", "model-registry/utils/VersionUtils.ts", "model-registry/components/ModelVersionPage.tsx", "model-registry/utils/SearchUtils.ts", "common/hooks/useSafeDeferredValue.ts", "model-registry/components/RegisterModelForm.tsx"], "sourcesContent": ["import React, { useMemo, useState } from 'react';\nimport {\n  Table,\n  TableCell,\n  TableHeader,\n  TableRow,\n  Typography,\n  useDesignSystemTheme,\n  MinusSquareIcon,\n  PlusSquareIcon,\n  Input,\n  Spacer,\n} from '@databricks/design-system';\nimport { LogModelWithSignatureUrl } from '../../common/constants';\nimport { ColumnSpec, TensorSpec, ColumnType } from '../types/model-schema';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { Interpolation, Theme } from '@emotion/react';\nimport { identity, isEmpty, isFunction } from 'lodash';\nimport { useSafeDeferredValue } from '../../common/hooks/useSafeDeferredValue';\n\nconst { Text } = Typography;\nconst INDENTATION_SPACES = 2;\nconst LIMIT_VISIBLE_COLUMNS = 100;\n\ntype Props = {\n  schema?: any;\n  defaultExpandAllRows?: boolean;\n};\n\nfunction getTensorTypeRepr(tensorType: TensorSpec): string {\n  return `Tensor (dtype: ${tensorType['tensor-spec'].dtype}, shape: [${tensorType['tensor-spec'].shape}])`;\n}\n\n// return a formatted string representation of the column type\nfunction getColumnTypeRepr(columnType: ColumnType, indentationLevel: number): string {\n  const { type } = columnType;\n\n  const indentation = ' '.repeat(indentationLevel * INDENTATION_SPACES);\n  if (type === 'object') {\n    const propertyReprs = Object.keys(columnType.properties).map((propertyName) => {\n      const property = columnType.properties[propertyName];\n      const requiredRepr = property.required ? '' : ' (optional)';\n      const propertyRepr = getColumnTypeRepr(property, indentationLevel + 1);\n      const indentOffset = (indentationLevel + 1) * INDENTATION_SPACES;\n\n      return `${' '.repeat(indentOffset)}${propertyName}: ${propertyRepr.slice(indentOffset) + requiredRepr}`;\n    });\n\n    return `${indentation}{\\n${propertyReprs.join(',\\n')}\\n${indentation}}`;\n  }\n\n  if (type === 'array') {\n    const indentOffset = indentationLevel * INDENTATION_SPACES;\n    const itemsTypeRepr = getColumnTypeRepr(columnType.items, indentationLevel).slice(indentOffset);\n    return `${indentation}Array(${itemsTypeRepr})`;\n  }\n\n  return `${indentation}${type}`;\n}\n\nfunction ColumnName({ spec }: { spec: ColumnSpec | TensorSpec }): React.ReactElement {\n  let required = true;\n  if (spec.required !== undefined) {\n    ({ required } = spec);\n  } else if (spec.optional !== undefined && spec.optional) {\n    required = false;\n  }\n  const requiredTag = required ? <Text bold>(required)</Text> : <Text color=\"secondary\">(optional)</Text>;\n\n  const name = 'name' in spec ? spec.name : '-';\n\n  return (\n    <Text css={{ marginLeft: 32 }}>\n      {name} {requiredTag}\n    </Text>\n  );\n}\n\nfunction ColumnSchema({ spec }: { spec: ColumnSpec | TensorSpec }): React.ReactElement {\n  const { theme } = useDesignSystemTheme();\n  const repr = spec.type === 'tensor' ? getTensorTypeRepr(spec) : getColumnTypeRepr(spec, 0);\n\n  return (\n    <pre\n      css={{\n        whiteSpace: 'pre-wrap',\n        padding: theme.spacing.sm,\n        marginTop: theme.spacing.sm,\n        marginBottom: theme.spacing.sm,\n      }}\n    >\n      {repr}\n    </pre>\n  );\n}\n\nconst SchemaTableRow = ({ schemaData }: { schemaData?: (ColumnSpec | TensorSpec)[] }) => {\n  const isEmptySchema = isEmpty(schemaData);\n  const intl = useIntl();\n\n  // Determine if the schema is too large (more than LIMIT_VISIBLE_COLUMNS = 100 rows) to display all at once\n  const isLargeSchema = Boolean(schemaData && schemaData.length > LIMIT_VISIBLE_COLUMNS);\n  const [searchText, setSearchText] = useState('');\n\n  // Defer the search text to avoid blocking the UI when typing\n  const deferredSearchText = useSafeDeferredValue(searchText);\n\n  const filteredSchemaData = useMemo(() => {\n    if (!isLargeSchema) {\n      return schemaData;\n    }\n    const normalizedSearchText = deferredSearchText.toLowerCase();\n    return schemaData\n      ?.filter((schemaRow) => {\n        return 'name' in schemaRow && schemaRow.name.toLowerCase().includes(normalizedSearchText);\n      })\n      .slice(0, LIMIT_VISIBLE_COLUMNS);\n  }, [schemaData, deferredSearchText, isLargeSchema]);\n\n  if (isEmptySchema) {\n    return (\n      <TableRow>\n        <TableCell>\n          <FormattedMessage\n            defaultMessage=\"No schema. See <link>MLflow docs</link> for how to include\n                     input and output schema with your model.\"\n            description=\"Text for schema table when no schema exists in the model version\n                     page\"\n            values={{\n              link: (chunks: any) => (\n                <a href={LogModelWithSignatureUrl} target=\"_blank\" rel=\"noreferrer\">\n                  {chunks}\n                </a>\n              ),\n            }}\n          />\n        </TableCell>\n      </TableRow>\n    );\n  }\n\n  return (\n    <>\n      {isLargeSchema && (\n        <>\n          <Spacer />\n          <Typography.Hint>\n            <FormattedMessage\n              defaultMessage=\"Schema is too large to display all rows. Please search for a column name to filter the results. Currently showing {currentResults} results from {allResults} total rows.\"\n              description=\"Text for model inputs/outputs schema table when schema is too large to display all rows\"\n              values={{\n                currentResults: filteredSchemaData?.length,\n                allResults: schemaData?.length,\n              }}\n            />\n          </Typography.Hint>\n          <Spacer />\n          <Input\n            placeholder={intl.formatMessage({\n              defaultMessage: 'Search for a field',\n              description: 'Placeholder for search input in schema table',\n            })}\n            componentId=\"mlflow.schema_table.search_input\"\n            value={searchText}\n            onChange={(e) => setSearchText(e.target.value)}\n          />\n          <Spacer />\n        </>\n      )}\n      {filteredSchemaData?.map((schemaRow, index) => (\n        <TableRow key={index}>\n          <TableCell css={{ flex: 2, alignItems: 'center' }}>\n            <ColumnName spec={schemaRow} />\n          </TableCell>\n          <TableCell css={{ flex: 3, alignItems: 'center' }}>\n            <ColumnSchema spec={schemaRow} />\n          </TableCell>\n        </TableRow>\n      ))}\n    </>\n  );\n};\n\nexport const SchemaTable = ({ schema, defaultExpandAllRows }: Props) => {\n  const { theme } = useDesignSystemTheme();\n  const [inputsExpanded, setInputsExpanded] = useState(defaultExpandAllRows);\n  const [outputsExpanded, setOutputsExpanded] = useState(defaultExpandAllRows);\n\n  return (\n    <Table css={{ maxWidth: 800 }}>\n      <TableRow isHeader>\n        <TableHeader componentId=\"mlflow.schema_table.header.name\" css={{ flex: 2 }}>\n          <Text bold css={{ paddingLeft: theme.spacing.lg + theme.spacing.xs }}>\n            <FormattedMessage\n              defaultMessage=\"Name\"\n              description=\"Text for name column in schema table in model version page\"\n            />\n          </Text>\n        </TableHeader>\n        <TableHeader componentId=\"mlflow.schema_table.header.type\" css={{ flex: 3 }}>\n          <Text bold>\n            <FormattedMessage\n              defaultMessage=\"Type\"\n              description=\"Text for type column in schema table in model version page\"\n            />\n          </Text>\n        </TableHeader>\n      </TableRow>\n      <>\n        <TableRow onClick={() => setInputsExpanded(!inputsExpanded)} css={{ cursor: 'pointer' }}>\n          <TableCell>\n            <div css={{ display: 'flex', alignItems: 'center', gap: theme.spacing.xs }}>\n              <div\n                css={{\n                  width: theme.spacing.lg,\n                  height: theme.spacing.lg,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  svg: {\n                    color: theme.colors.textSecondary,\n                  },\n                }}\n              >\n                {inputsExpanded ? <MinusSquareIcon /> : <PlusSquareIcon />}\n              </div>\n              <FormattedMessage\n                defaultMessage=\"Inputs ({numInputs})\"\n                description=\"Input section header for schema table in model version page\"\n                values={{\n                  numInputs: schema.inputs.length,\n                }}\n              />\n            </div>\n          </TableCell>\n        </TableRow>\n        {inputsExpanded && <SchemaTableRow schemaData={schema.inputs} />}\n        <TableRow onClick={() => setOutputsExpanded(!outputsExpanded)} css={{ cursor: 'pointer' }}>\n          <TableCell>\n            <div css={{ display: 'flex', alignItems: 'center', gap: theme.spacing.xs }}>\n              <div\n                css={{\n                  width: theme.spacing.lg,\n                  height: theme.spacing.lg,\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  svg: {\n                    color: theme.colors.textSecondary,\n                  },\n                }}\n              >\n                {outputsExpanded ? <MinusSquareIcon /> : <PlusSquareIcon />}\n              </div>\n              <FormattedMessage\n                defaultMessage=\"Outputs ({numOutputs})\"\n                description=\"Input section header for schema table in model version page\"\n                values={{\n                  numOutputs: schema.outputs.length,\n                }}\n              />\n            </div>\n          </TableCell>\n        </TableRow>\n        {outputsExpanded && <SchemaTableRow schemaData={schema.outputs} />}\n      </>\n    </Table>\n  );\n};\n", "import { Button, Modal, Typography } from '@databricks/design-system';\nimport { debounce } from 'lodash';\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { FormattedMessage, useIntl } from 'react-intl';\n\nimport { useDispatch, useSelector } from 'react-redux';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport { useNavigate } from '../../common/utils/RoutingUtils';\nimport Utils from '../../common/utils/Utils';\nimport { getModelNameFilter } from '../utils/SearchUtils';\nimport { ReduxState, ThunkDispatch } from '../../redux-types';\nimport { createModelVersionApi, createRegisteredModelApi, searchRegisteredModelsApi } from '../actions';\nimport { ModelRegistryRoutes } from '../routes';\nimport {\n  CREATE_NEW_MODEL_OPTION_VALUE,\n  MODEL_NAME_FIELD,\n  RegisterModelForm,\n  SELECTED_MODEL_FIELD,\n} from './RegisterModelForm';\nimport { ModelVersionInfoEntity } from '../../experiment-tracking/types';\n\nconst MAX_SEARCH_REGISTERED_MODELS = 5;\n\ntype PromoteModelButtonImplProps = {\n  modelVersion: ModelVersionInfoEntity;\n};\n\nexport const PromoteModelButton = (props: PromoteModelButtonImplProps) => {\n  const intl = useIntl();\n  const navigate = useNavigate();\n\n  const createRegisteredModelRequestId = useRef(getUUID());\n  const createModelVersionRequestId = useRef(getUUID());\n\n  const { modelVersion } = props;\n  const [visible, setVisible] = useState(false);\n  const [confirmLoading, setConfirmLoading] = useState(false);\n  const dispatch = useDispatch<ThunkDispatch>();\n\n  const modelByName = useSelector((state: ReduxState) => state.entities.modelByName);\n\n  const form = useRef<any>();\n  const showRegisterModal = () => {\n    setVisible(true);\n  };\n\n  const hideRegisterModal = () => {\n    setVisible(false);\n  };\n\n  const resetAndClearModalForm = () => {\n    setVisible(false);\n    setConfirmLoading(false);\n  };\n\n  const handleRegistrationFailure = (e: any) => {\n    setConfirmLoading(false);\n    Utils.logErrorAndNotifyUser(e);\n  };\n\n  const handleSearchRegisteredModels = useCallback(\n    (input: any) => {\n      dispatch(searchRegisteredModelsApi(getModelNameFilter(input), MAX_SEARCH_REGISTERED_MODELS));\n    },\n    [dispatch],\n  );\n\n  const debouncedHandleSearchRegisteredModels = useMemo(\n    () => debounce(handleSearchRegisteredModels, 300),\n    [handleSearchRegisteredModels],\n  );\n\n  const handleCopyModel = () => {\n    form.current.validateFields().then((values: any) => {\n      setConfirmLoading(true);\n      const selectedModelName = values[SELECTED_MODEL_FIELD];\n      const copySource = 'models:/' + modelVersion.name + '/' + modelVersion.version;\n      if (selectedModelName === CREATE_NEW_MODEL_OPTION_VALUE) {\n        const newModelName = values[MODEL_NAME_FIELD];\n        dispatch(createRegisteredModelApi(newModelName, createRegisteredModelRequestId.current))\n          .then(() =>\n            dispatch(\n              createModelVersionApi(\n                newModelName,\n                copySource,\n                modelVersion.run_id,\n                modelVersion.tags,\n                createModelVersionRequestId.current,\n              ),\n            ),\n          )\n          .then((mvResult: any) => {\n            resetAndClearModalForm();\n            const { version } = mvResult.value['model_version'];\n            navigate(ModelRegistryRoutes.getModelVersionPageRoute(newModelName, version));\n          })\n          .catch(handleRegistrationFailure);\n      } else {\n        dispatch(\n          createModelVersionApi(\n            selectedModelName,\n            copySource,\n            modelVersion.run_id,\n            modelVersion.tags,\n            createModelVersionRequestId.current,\n          ),\n        )\n          .then((mvResult: any) => {\n            resetAndClearModalForm();\n            const { version } = mvResult.value['model_version'];\n            navigate(ModelRegistryRoutes.getModelVersionPageRoute(selectedModelName, version));\n          })\n          .catch(handleRegistrationFailure);\n      }\n    });\n  };\n\n  useEffect(() => {\n    dispatch(searchRegisteredModelsApi());\n  }, [dispatch]);\n\n  useEffect(() => {\n    if (visible) {\n      dispatch(searchRegisteredModelsApi());\n    }\n  }, [dispatch, visible]);\n\n  const renderRegisterModelForm = () => {\n    return (\n      <>\n        <Typography.Paragraph css={{ marginTop: '-12px' }}>\n          <FormattedMessage\n            defaultMessage=\"Copy your MLflow models to another registered model for\n            simple model promotion across environments. For more mature production-grade setups, we\n            recommend setting up automated model training workflows to produce models in controlled\n            environments. <link>Learn more</link>\"\n            description=\"Model registry > OSS Promote model modal > description paragraph body\"\n            values={{\n              link: (chunks) => (\n                <Typography.Link\n                  componentId=\"codegen_mlflow_app_src_model-registry_components_promotemodelbutton.tsx_140\"\n                  href={\n                    'https://mlflow.org/docs/latest/model-registry.html' +\n                    '#promoting-an-mlflow-model-across-environments'\n                  }\n                  openInNewTab\n                >\n                  {chunks}\n                </Typography.Link>\n              ),\n            }}\n          />\n        </Typography.Paragraph>\n        <RegisterModelForm\n          modelByName={modelByName}\n          innerRef={form}\n          onSearchRegisteredModels={debouncedHandleSearchRegisteredModels}\n          isCopy\n        />\n      </>\n    );\n  };\n\n  return (\n    <div className=\"promote-model-btn-wrapper\">\n      <Button\n        componentId=\"codegen_mlflow_app_src_model-registry_components_promotemodelbutton.tsx_165\"\n        className=\"promote-model-btn\"\n        type=\"primary\"\n        onClick={showRegisterModal}\n      >\n        <FormattedMessage\n          defaultMessage=\"Promote model\"\n          description=\"Button text to pomote the model to a different registered model\"\n        />\n      </Button>\n      <Modal\n        title={\n          <FormattedMessage\n            defaultMessage=\"Promote {sourceModelName} version {sourceModelVersion}\"\n            description=\"Modal title to pomote the model to a different registered model\"\n            values={{ sourceModelName: modelVersion.name, sourceModelVersion: modelVersion.version }}\n          />\n        }\n        // @ts-expect-error TS(2322): Type '{ children: Element; title: any; width: numb... Remove this comment to see the full error message\n        width={640}\n        visible={visible}\n        onOk={handleCopyModel}\n        okText={intl.formatMessage({\n          defaultMessage: 'Promote',\n          description: 'Confirmation text to promote the model',\n        })}\n        cancelText={intl.formatMessage({\n          defaultMessage: 'Cancel',\n          description: 'Cancel text to cancel the flow to copy the model',\n        })}\n        confirmLoading={confirmLoading}\n        onCancel={hideRegisterModal}\n        centered\n      >\n        {renderRegisterModelForm()}\n      </Modal>\n    </div>\n  );\n};\n", "import {\n  FormUI,\n  Modal,\n  ModalProps,\n  RHFControlledComponents,\n  Spacer,\n  Tooltip,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport { archiveExistingVersionToolTipText, Stages, StageTagComponents } from '../constants';\nimport { useForm } from 'react-hook-form';\nimport { FormattedMessage } from 'react-intl';\nimport { useEffect } from 'react';\n\nexport interface ModelStageTransitionFormModalValues {\n  comment: string;\n  archiveExistingVersions: boolean;\n}\n\nexport enum ModelStageTransitionFormModalMode {\n  RequestOrDirect,\n  Approve,\n  Reject,\n  Cancel,\n}\n\nexport const ModelStageTransitionFormModal = ({\n  visible,\n  onCancel,\n  toStage,\n  allowArchivingExistingVersions,\n  transitionDescription,\n  onConfirm,\n  mode = ModelStageTransitionFormModalMode.RequestOrDirect,\n}: {\n  toStage?: string;\n  transitionDescription: React.ReactNode;\n  allowArchivingExistingVersions?: boolean;\n  onConfirm?: (values: ModelStageTransitionFormModalValues) => void;\n  mode?: ModelStageTransitionFormModalMode;\n} & Pick<ModalProps, 'visible' | 'onCancel'>) => {\n  const { theme } = useDesignSystemTheme();\n  const form = useForm<ModelStageTransitionFormModalValues>({\n    defaultValues: {\n      comment: '',\n      archiveExistingVersions: false,\n    },\n  });\n\n  const getModalTitle = () => {\n    if (mode === ModelStageTransitionFormModalMode.Approve) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"Approve pending request\"\n          description=\"Title for a model version stage transition modal when approving a pending request\"\n        />\n      );\n    }\n    if (mode === ModelStageTransitionFormModalMode.Reject) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"Reject pending request\"\n          description=\"Title for a model version stage transition modal when rejecting a pending request\"\n        />\n      );\n    }\n    if (mode === ModelStageTransitionFormModalMode.Cancel) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"Cancel pending request\"\n          description=\"Title for a model version stage transition modal when cancelling a pending request\"\n        />\n      );\n    }\n    return (\n      <FormattedMessage\n        defaultMessage=\"Stage transition\"\n        description=\"Title for a model version stage transition modal\"\n      />\n    );\n  };\n\n  // Reset form values when modal is reopened\n  useEffect(() => {\n    if (visible) {\n      form.reset();\n    }\n  }, [form, visible]);\n\n  return (\n    <Modal\n      title={getModalTitle()}\n      componentId=\"mlflow.model_registry.stage_transition_modal_v2\"\n      visible={visible}\n      onCancel={onCancel}\n      okText={\n        <FormattedMessage\n          defaultMessage=\"OK\"\n          description=\"Confirmation button text on the model version stage transition request/approval modal\"\n        />\n      }\n      cancelText={\n        <FormattedMessage\n          defaultMessage=\"Cancel\"\n          description=\"Cancellation button text on the model version stage transition request/approval modal\"\n        />\n      }\n      onOk={onConfirm && form.handleSubmit(onConfirm)}\n    >\n      {transitionDescription}\n      <Spacer size=\"sm\" />\n      <FormUI.Label htmlFor=\"mlflow.model_registry.stage_transition_modal_v2.comment\">Comment</FormUI.Label>\n      <RHFControlledComponents.TextArea\n        name=\"comment\"\n        id=\"mlflow.model_registry.stage_transition_modal_v2.comment\"\n        componentId=\"mlflow.model_registry.stage_transition_modal_v2.comment\"\n        control={form.control}\n        rows={4}\n      />\n      <Spacer size=\"sm\" />\n\n      {allowArchivingExistingVersions && toStage && (\n        <RHFControlledComponents.Checkbox\n          name=\"archiveExistingVersions\"\n          componentId=\"mlflow.model_registry.stage_transition_modal_v2.archive_existing_versions\"\n          control={form.control}\n        >\n          <Tooltip\n            componentId=\"mlflow.model_registry.stage_transition_modal_v2.archive_existing_versions.tooltip\"\n            content={archiveExistingVersionToolTipText(toStage)}\n          >\n            <span css={{ '[role=status]': { marginRight: theme.spacing.xs } }}>\n              <FormattedMessage\n                defaultMessage=\"Transition existing {currentStage} model version to {archivedStage}\"\n                description=\"Description text for checkbox for archiving existing model versions\n                  in the toStage for model version stage transition request\"\n                values={{\n                  currentStage: <span css={{ marginLeft: theme.spacing.xs }}>{StageTagComponents[toStage]}</span>,\n                  archivedStage: (\n                    <span css={{ marginLeft: theme.spacing.xs }}>{StageTagComponents[Stages.ARCHIVED]}</span>\n                  ),\n                }}\n              />\n            </span>\n          </Tooltip>\n        </RHFControlledComponents.Checkbox>\n      )}\n    </Modal>\n  );\n};\n", "import React from 'react';\nimport { Dropdown, Menu, ChevronDownIcon, ArrowRightIcon } from '@databricks/design-system';\nimport {\n  Stages,\n  StageTagComponents,\n  ActivityTypes,\n  type PendingModelVersionActivity,\n  ACTIVE_STAGES,\n} from '../constants';\nimport _ from 'lodash';\nimport { FormattedMessage } from 'react-intl';\nimport { ModelStageTransitionFormModal, ModelStageTransitionFormModalValues } from './ModelStageTransitionFormModal';\n\ntype ModelStageTransitionDropdownProps = {\n  currentStage?: string;\n  permissionLevel?: string;\n  onSelect?: (activity: PendingModelVersionActivity, comment?: string, archiveExistingVersions?: boolean) => void;\n};\n\ntype ModelStageTransitionDropdownState = {\n  confirmModalVisible: boolean;\n  confirmingActivity: PendingModelVersionActivity | null;\n  handleConfirm: ((values: ModelStageTransitionFormModalValues) => void) | undefined;\n};\n\nexport class ModelStageTransitionDropdown extends React.Component<\n  ModelStageTransitionDropdownProps,\n  ModelStageTransitionDropdownState\n> {\n  static defaultProps = {\n    currentStage: Stages.NONE,\n  };\n\n  state: ModelStageTransitionDropdownState = {\n    confirmModalVisible: false,\n    confirmingActivity: null,\n    handleConfirm: undefined,\n  };\n\n  handleMenuItemClick = (activity: PendingModelVersionActivity) => {\n    const { onSelect } = this.props;\n    this.setState({\n      confirmModalVisible: true,\n      confirmingActivity: activity,\n      handleConfirm:\n        onSelect &&\n        ((values: ModelStageTransitionFormModalValues) => {\n          this.setState({ confirmModalVisible: false });\n\n          if (values) {\n            const { archiveExistingVersions = false } = values;\n            // @ts-expect-error TS(2722): Cannot invoke an object which is possibly 'undefin... Remove this comment to see the full error message\n            onSelect(activity, archiveExistingVersions);\n            return;\n          }\n        }),\n    });\n  };\n\n  handleConfirmModalCancel = () => {\n    this.setState({ confirmModalVisible: false });\n  };\n\n  getNoneCurrentStages = (currentStage?: string) => {\n    const stages = Object.values(Stages);\n    _.remove(stages, (s) => s === currentStage);\n    return stages;\n  };\n\n  getMenu() {\n    const { currentStage } = this.props;\n    const nonCurrentStages = this.getNoneCurrentStages(currentStage);\n    return (\n      <Menu>\n        {nonCurrentStages.map((stage) => (\n          <Menu.Item\n            key={`transition-to-${stage}`}\n            onClick={() =>\n              this.handleMenuItemClick({\n                type: ActivityTypes.APPLIED_TRANSITION,\n                to_stage: stage,\n              })\n            }\n          >\n            <FormattedMessage\n              defaultMessage=\"Transition to\"\n              description=\"Text for transitioning a model version to a different stage under\n                 dropdown menu in model version page\"\n            />\n            &nbsp;&nbsp;&nbsp;\n            <ArrowRightIcon />\n            &nbsp;&nbsp;&nbsp;\n            {StageTagComponents[stage]}\n          </Menu.Item>\n        ))}\n      </Menu>\n    );\n  }\n\n  renderConfirmModal() {\n    const { confirmModalVisible, confirmingActivity, handleConfirm } = this.state;\n\n    if (!confirmingActivity) {\n      return null;\n    }\n\n    const allowArchivingExistingVersions =\n      confirmingActivity.type === ActivityTypes.APPLIED_TRANSITION &&\n      ACTIVE_STAGES.includes(confirmingActivity.to_stage);\n\n    return (\n      <ModelStageTransitionFormModal\n        visible={confirmModalVisible}\n        toStage={confirmingActivity.to_stage}\n        onConfirm={handleConfirm}\n        onCancel={this.handleConfirmModalCancel}\n        transitionDescription={renderActivityDescription(confirmingActivity)}\n        allowArchivingExistingVersions={allowArchivingExistingVersions}\n      />\n    );\n  }\n\n  render() {\n    const { currentStage } = this.props;\n    return (\n      <span>\n        <Dropdown overlay={this.getMenu()} trigger={['click']} className=\"stage-transition-dropdown\">\n          <span>\n            {StageTagComponents[currentStage ?? Stages.NONE]}\n            <ChevronDownIcon css={{ cursor: 'pointer', marginLeft: -4 }} />\n          </span>\n        </Dropdown>\n        {this.renderConfirmModal()}\n      </span>\n    );\n  }\n}\n\nexport const renderActivityDescription = (activity: PendingModelVersionActivity) => {\n  if (activity) {\n    return (\n      <div>\n        <FormattedMessage\n          defaultMessage=\"Transition to\"\n          description=\"Text for activity description under confirmation modal for model\n             version stage transition\"\n        />\n        &nbsp;&nbsp;&nbsp;\n        <ArrowRightIcon />\n        &nbsp;&nbsp;&nbsp;\n        {StageTagComponents[activity.to_stage]}\n      </div>\n    );\n  }\n  return null;\n};\n", "import { Button, PencilIcon } from '@databricks/design-system';\nimport type { ModelEntity } from '../../../experiment-tracking/types';\nimport { useEditRegisteredModelAliasesModal } from '../../hooks/useEditRegisteredModelAliasesModal';\nimport { ModelVersionAliasTag } from './ModelVersionAliasTag';\nimport { useCallback } from 'react';\n\nexport const ModelVersionViewAliasEditor = ({\n  aliases = [],\n  modelEntity,\n  version,\n  onAliasesModified,\n}: {\n  modelEntity?: ModelEntity;\n  aliases?: string[];\n  version: string;\n  onAliasesModified?: () => void;\n}) => {\n  const { EditAliasesModal, showEditAliasesModal } = useEditRegisteredModelAliasesModal({\n    model: modelEntity || null,\n    onSuccess: onAliasesModified,\n  });\n  const onAddEdit = useCallback(() => {\n    showEditAliasesModal(version);\n  }, [showEditAliasesModal, version]);\n  return (\n    <>\n      {EditAliasesModal}\n      {aliases.length < 1 ? (\n        <Button\n          componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelversionviewaliaseditor.tsx_29\"\n          size=\"small\"\n          type=\"link\"\n          onClick={onAddEdit}\n          title=\"Add aliases\"\n        >\n          Add\n        </Button>\n      ) : (\n        <div css={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>\n          {aliases.map((alias) => (\n            <ModelVersionAliasTag compact value={alias} key={alias} />\n          ))}\n          <Button\n            componentId=\"codegen_mlflow_app_src_model-registry_components_aliases_modelversionviewaliaseditor.tsx_37\"\n            size=\"small\"\n            icon={<PencilIcon />}\n            onClick={onAddEdit}\n            title=\"Edit aliases\"\n          />\n        </div>\n      )}\n    </>\n  );\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { Link, NavigateFunction } from '../../common/utils/RoutingUtils';\nimport { ModelRegistryRoutes } from '../routes';\nimport { PromoteModelButton } from './PromoteModelButton';\nimport { SchemaTable } from './SchemaTable';\nimport Utils from '../../common/utils/Utils';\nimport { ModelStageTransitionDropdown } from './ModelStageTransitionDropdown';\nimport { Descriptions } from '../../common/components/Descriptions';\nimport { modelStagesMigrationGuideLink } from '../../common/constants';\nimport { Alert, Modal, Button, InfoIcon, LegacyTooltip, Typography } from '@databricks/design-system';\nimport {\n  ModelVersionStatus,\n  StageLabels,\n  StageTagComponents,\n  ModelVersionStatusIcons,\n  DefaultModelVersionStatusMessages,\n  ACTIVE_STAGES,\n  type ModelVersionActivity,\n  type PendingModelVersionActivity,\n} from '../constants';\nimport Routers from '../../experiment-tracking/routes';\nimport { CollapsibleSection } from '../../common/components/CollapsibleSection';\nimport { EditableNote } from '../../common/components/EditableNote';\nimport { EditableTagsTableView } from '../../common/components/EditableTagsTableView';\nimport { getModelVersionTags } from '../reducers';\nimport { setModelVersionTagApi, deleteModelVersionTagApi } from '../actions';\nimport { connect } from 'react-redux';\nimport { OverflowMenu, PageHeader } from '../../shared/building_blocks/PageHeader';\nimport { FormattedMessage, type IntlShape, injectIntl } from 'react-intl';\nimport { extractArtifactPathFromModelSource } from '../utils/VersionUtils';\nimport { withNextModelsUIContext } from '../hooks/useNextModelsUI';\nimport { ModelsNextUIToggleSwitch } from './ModelsNextUIToggleSwitch';\nimport { shouldShowModelsNextUI } from '../../common/utils/FeatureUtils';\nimport { ModelVersionViewAliasEditor } from './aliases/ModelVersionViewAliasEditor';\nimport type { ModelEntity, RunInfoEntity } from '../../experiment-tracking/types';\nimport { ErrorWrapper } from '../../common/utils/ErrorWrapper';\n\ntype ModelVersionViewImplProps = {\n  modelName?: string;\n  modelVersion?: any;\n  modelEntity?: ModelEntity;\n  schema?: any;\n  activities?: ModelVersionActivity[];\n  transitionRequests?: Record<string, unknown>[];\n  onCreateComment: (...args: any[]) => any;\n  onEditComment: (...args: any[]) => any;\n  onDeleteComment: (...args: any[]) => any;\n  runInfo?: RunInfoEntity;\n  runDisplayName?: string;\n  handleStageTransitionDropdownSelect: (\n    activity: PendingModelVersionActivity,\n    comment?: string,\n    archiveExistingVersions?: boolean,\n  ) => void;\n  deleteModelVersionApi: (...args: any[]) => any;\n  handleEditDescription: (...args: any[]) => any;\n  onAliasesModified: () => void;\n  navigate: NavigateFunction;\n  tags: any;\n  setModelVersionTagApi: (...args: any[]) => any;\n  deleteModelVersionTagApi: (...args: any[]) => any;\n  intl: IntlShape;\n  usingNextModelsUI: boolean;\n};\n\ntype ModelVersionViewImplState = any;\n\nexport class ModelVersionViewImpl extends React.Component<ModelVersionViewImplProps, ModelVersionViewImplState> {\n  state = {\n    isDeleteModalVisible: false,\n    isDeleteModalConfirmLoading: false,\n    showDescriptionEditor: false,\n    isTagsRequestPending: false,\n  };\n\n  formRef = React.createRef();\n\n  componentDidMount() {\n    const pageTitle = `${this.props.modelName} v${this.props.modelVersion.version} - MLflow Model`;\n    Utils.updatePageTitle(pageTitle);\n  }\n\n  handleDeleteConfirm = () => {\n    const { modelName = '', modelVersion, navigate } = this.props;\n    const { version } = modelVersion;\n    this.showConfirmLoading();\n    this.props\n      .deleteModelVersionApi(modelName, version)\n      .then(() => {\n        navigate(ModelRegistryRoutes.getModelPageRoute(modelName));\n      })\n      .catch((e: any) => {\n        this.hideConfirmLoading();\n        Utils.logErrorAndNotifyUser(e);\n      });\n  };\n\n  showDeleteModal = () => {\n    this.setState({ isDeleteModalVisible: true });\n  };\n\n  hideDeleteModal = () => {\n    this.setState({ isDeleteModalVisible: false });\n  };\n\n  showConfirmLoading = () => {\n    this.setState({ isDeleteModalConfirmLoading: true });\n  };\n\n  hideConfirmLoading = () => {\n    this.setState({ isDeleteModalConfirmLoading: false });\n  };\n\n  handleCancelEditDescription = () => {\n    this.setState({ showDescriptionEditor: false });\n  };\n\n  handleSubmitEditDescription = (description: any) => {\n    return this.props.handleEditDescription(description).then(() => {\n      this.setState({ showDescriptionEditor: false });\n    });\n  };\n\n  startEditingDescription = (e: any) => {\n    e.stopPropagation();\n    this.setState({ showDescriptionEditor: true });\n  };\n\n  handleAddTag = (values: any) => {\n    const form = this.formRef.current;\n    const { modelName } = this.props;\n    const { version } = this.props.modelVersion;\n    this.setState({ isTagsRequestPending: true });\n    this.props\n      .setModelVersionTagApi(modelName, version, values.name, values.value)\n      .then(() => {\n        this.setState({ isTagsRequestPending: false });\n        (form as any).resetFields();\n      })\n      .catch((ex: ErrorWrapper | Error) => {\n        this.setState({ isTagsRequestPending: false });\n        // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n        console.error(ex);\n\n        const userVisibleError = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n\n        Utils.displayGlobalErrorNotification(\n          this.props.intl.formatMessage(\n            {\n              defaultMessage: 'Failed to add tag. Error: {userVisibleError}',\n              description: 'Text for user visible error when adding tag in model version view',\n            },\n            {\n              userVisibleError,\n            },\n          ),\n        );\n      });\n  };\n\n  handleSaveEdit = ({ name, value }: any) => {\n    const { modelName } = this.props;\n    const { version } = this.props.modelVersion;\n    return this.props.setModelVersionTagApi(modelName, version, name, value).catch((ex: ErrorWrapper | Error) => {\n      // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n      console.error(ex);\n\n      const userVisibleError = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n\n      Utils.displayGlobalErrorNotification(\n        this.props.intl.formatMessage(\n          {\n            defaultMessage: 'Failed to set tag. Error: {userVisibleError}',\n            description: 'Text for user visible error when setting tag in model version view',\n          },\n          {\n            userVisibleError,\n          },\n        ),\n      );\n    });\n  };\n\n  handleDeleteTag = ({ name }: any) => {\n    const { modelName } = this.props;\n    const { version } = this.props.modelVersion;\n    return this.props.deleteModelVersionTagApi(modelName, version, name).catch((ex: ErrorWrapper | Error) => {\n      // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n      console.error(ex);\n\n      const userVisibleError = ex instanceof ErrorWrapper ? ex.getMessageField() : ex.message;\n\n      Utils.displayGlobalErrorNotification(\n        this.props.intl.formatMessage(\n          {\n            defaultMessage: 'Failed to delete tag. Error: {userVisibleError}',\n            description: 'Text for user visible error when deleting tag in model version view',\n          },\n          {\n            userVisibleError,\n          },\n        ),\n      );\n    });\n  };\n\n  shouldHideDeleteOption() {\n    return false;\n  }\n\n  renderStageDropdown(modelVersion: any) {\n    const { handleStageTransitionDropdownSelect } = this.props;\n    return (\n      <Descriptions.Item\n        key=\"description-key-stage\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Stage',\n          description: 'Label name for stage metadata in model version page',\n        })}\n      >\n        {modelVersion.status === ModelVersionStatus.READY ? (\n          <ModelStageTransitionDropdown\n            currentStage={modelVersion.current_stage}\n            permissionLevel={modelVersion.permission_level}\n            onSelect={handleStageTransitionDropdownSelect}\n          />\n        ) : (\n          StageTagComponents[modelVersion.current_stage]\n        )}\n      </Descriptions.Item>\n    );\n  }\n\n  renderDisabledStage(modelVersion: any) {\n    const tooltipContent = (\n      <FormattedMessage\n        defaultMessage=\"Stages have been deprecated in the new Model Registry UI. Learn how to\n      migrate models <link>here</link>.\"\n        description=\"Tooltip content for the disabled stage metadata in model version page\"\n        values={{\n          link: (chunks: any) => (\n            <Typography.Link\n              componentId=\"codegen_mlflow_app_src_model-registry_components_modelversionview.tsx_301\"\n              href={modelStagesMigrationGuideLink}\n              openInNewTab\n            >\n              {chunks}\n            </Typography.Link>\n          ),\n        }}\n      />\n    );\n    return (\n      <Descriptions.Item\n        key=\"description-key-stage-disabled\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Stage (deprecated)',\n          description: 'Label name for the deprecated stage metadata in model version page',\n        })}\n      >\n        <div css={{ display: 'flex', alignItems: 'center' }}>\n          {StageLabels[modelVersion.current_stage]}\n          <LegacyTooltip title={tooltipContent} placement=\"bottom\">\n            <InfoIcon css={{ paddingLeft: '4px' }} />\n          </LegacyTooltip>\n        </div>\n      </Descriptions.Item>\n    );\n  }\n\n  renderRegisteredTimestampDescription(creation_timestamp: any) {\n    return (\n      <Descriptions.Item\n        key=\"description-key-register\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Registered At',\n          description: 'Label name for registered timestamp metadata in model version page',\n        })}\n      >\n        {Utils.formatTimestamp(creation_timestamp, this.props.intl)}\n      </Descriptions.Item>\n    );\n  }\n\n  renderCreatorDescription(user_id: any) {\n    return (\n      user_id && (\n        <Descriptions.Item\n          key=\"description-key-creator\"\n          label={this.props.intl.formatMessage({\n            defaultMessage: 'Creator',\n            description: 'Label name for creator metadata in model version page',\n          })}\n        >\n          {user_id}\n        </Descriptions.Item>\n      )\n    );\n  }\n\n  renderLastModifiedDescription(last_updated_timestamp: any) {\n    return (\n      <Descriptions.Item\n        key=\"description-key-modified\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Last Modified',\n          description: 'Label name for last modified timestamp metadata in model version page',\n        })}\n      >\n        {Utils.formatTimestamp(last_updated_timestamp, this.props.intl)}\n      </Descriptions.Item>\n    );\n  }\n\n  renderSourceRunDescription() {\n    // We don't show the source run link if the model version is not created from a run\n    if (!this.props.modelVersion?.run_id) {\n      return null;\n    }\n    return (\n      <Descriptions.Item\n        key=\"description-key-source-run\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Source Run',\n          description: 'Label name for source run metadata in model version page',\n        })}\n        // @ts-expect-error TS(2322): Type '{ children: Element | null; key: string; lab... Remove this comment to see the full error message\n        className=\"linked-run\"\n      >\n        {this.resolveRunLink()}\n      </Descriptions.Item>\n    );\n  }\n\n  renderCopiedFromLink() {\n    const { source } = this.props.modelVersion;\n    const modelUriRegex = /^models:\\/[^/]+\\/[^/]+$/;\n    if (!source || !modelUriRegex.test(source)) {\n      return null;\n    }\n    const sourceParts = source.split('/');\n    const sourceModelName = sourceParts[1];\n    const sourceModelVersion = sourceParts[2];\n    const link = (\n      <>\n        <Link\n          data-test-id=\"copied-from-link\"\n          to={ModelRegistryRoutes.getModelVersionPageRoute(sourceModelName, sourceModelVersion)}\n        >\n          {sourceModelName}\n        </Link>\n        &nbsp;\n        <FormattedMessage\n          defaultMessage=\"(Version {sourceModelVersion})\"\n          description=\"Version number of the source model version\"\n          values={{ sourceModelVersion }}\n        />\n      </>\n    );\n    return (\n      <Descriptions.Item\n        key=\"description-key-copied-from\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Copied from',\n          description: 'Label name for source model version metadata in model version page',\n        })}\n      >\n        {link}\n      </Descriptions.Item>\n    );\n  }\n\n  renderAliasEditor = () => {\n    // Extract aliases for the currently displayed model version from the model entity object\n    const currentVersion = this.props.modelVersion.version;\n    const currentVersionAliases =\n      this.props.modelEntity?.aliases?.filter(({ version }) => version === currentVersion).map(({ alias }) => alias) ||\n      [];\n    return (\n      <Descriptions.Item\n        key=\"description-key-aliases\"\n        label={this.props.intl.formatMessage({\n          defaultMessage: 'Aliases',\n          description: 'Aliases section in the metadata on model version page',\n        })}\n      >\n        <ModelVersionViewAliasEditor\n          aliases={currentVersionAliases}\n          version={this.props.modelVersion.version}\n          modelEntity={this.props.modelEntity}\n          onAliasesModified={this.props.onAliasesModified}\n        />\n      </Descriptions.Item>\n    );\n  };\n\n  getDescriptions(modelVersion: any) {\n    const { usingNextModelsUI } = this.props;\n\n    const defaultOrder = [\n      this.renderRegisteredTimestampDescription(modelVersion.creation_timestamp),\n      this.renderCreatorDescription(modelVersion.user_id),\n      this.renderLastModifiedDescription(modelVersion.last_updated_timestamp),\n      this.renderSourceRunDescription(),\n      this.renderCopiedFromLink(),\n      usingNextModelsUI ? this.renderAliasEditor() : this.renderStageDropdown(modelVersion),\n      usingNextModelsUI ? this.renderDisabledStage(modelVersion) : null,\n    ];\n    return defaultOrder.filter((item) => item !== null);\n  }\n\n  renderMetadata(modelVersion: any) {\n    return (\n      // @ts-expect-error TS(2322): Type '{ children: any[]; className: string; }' is ... Remove this comment to see the full error message\n      <Descriptions className=\"metadata-list\">{this.getDescriptions(modelVersion)}</Descriptions>\n    );\n  }\n\n  renderStatusAlert() {\n    const { status, status_message } = this.props.modelVersion;\n    if (status !== ModelVersionStatus.READY) {\n      const defaultMessage = DefaultModelVersionStatusMessages[status];\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore - OSS specific ignore\n      const type = status === ModelVersionStatus.FAILED_REGISTRATION ? 'error' : 'info';\n      return (\n        <Alert\n          type={type}\n          className={`status-alert status-alert-${type}`}\n          message={status_message || defaultMessage}\n          // @ts-expect-error TS(2322): Type '{ type: \"error\" | \"info\"; className: string;... Remove this comment to see the full error message\n          icon={ModelVersionStatusIcons[status]}\n          banner\n        />\n      );\n    }\n    return null;\n  }\n\n  renderDescriptionEditIcon() {\n    return (\n      <Button\n        componentId=\"codegen_mlflow_app_src_model-registry_components_modelversionview.tsx_516\"\n        data-test-id=\"descriptionEditButton\"\n        type=\"link\"\n        onClick={this.startEditingDescription}\n      >\n        <FormattedMessage\n          defaultMessage=\"Edit\"\n          description=\"Text for the edit button next to the description section title on\n             the model version view page\"\n        />{' '}\n      </Button>\n    );\n  }\n\n  resolveRunLink() {\n    const { modelVersion, runInfo } = this.props;\n    if (modelVersion.run_link) {\n      return (\n        // Reported during ESLint upgrade\n        // eslint-disable-next-line react/jsx-no-target-blank\n        <a target=\"_blank\" href={modelVersion.run_link}>\n          {this.resolveRunName()}\n        </a>\n      );\n    } else if (runInfo) {\n      let artifactPath = null;\n      const modelSource = this.props.modelVersion?.source;\n      if (modelSource) {\n        artifactPath = extractArtifactPathFromModelSource(modelSource, runInfo.runUuid);\n      }\n      return (\n        <Link to={Routers.getRunPageRoute(runInfo.experimentId, runInfo.runUuid, artifactPath)}>\n          {this.resolveRunName()}\n        </Link>\n      );\n    }\n    return null;\n  }\n\n  resolveRunName() {\n    const { modelVersion, runInfo, runDisplayName } = this.props;\n    if (modelVersion.run_link) {\n      // We use the first 37 chars to stay consistent with runDisplayName, which is typically:\n      // Run: [ID]\n      return modelVersion.run_link.substr(0, 37) + '...';\n    } else if (runInfo) {\n      return runDisplayName || runInfo.runUuid;\n    } else {\n      return null;\n    }\n  }\n\n  renderPomoteModelButton() {\n    const { modelVersion, usingNextModelsUI, navigate } = this.props;\n    return usingNextModelsUI ? <PromoteModelButton modelVersion={modelVersion} /> : null;\n  }\n\n  getPageHeader(title: any, breadcrumbs: any) {\n    const menu = [\n      {\n        id: 'delete',\n        itemName: (\n          <FormattedMessage\n            defaultMessage=\"Delete\"\n            description=\"Text for delete button on model version view page header\"\n          />\n        ),\n        onClick: this.showDeleteModal,\n        disabled: ACTIVE_STAGES.includes(this.props.modelVersion.current_stage),\n      },\n    ];\n    return (\n      <PageHeader title={title} breadcrumbs={breadcrumbs}>\n        {!this.shouldHideDeleteOption() && <OverflowMenu menu={menu} />}\n        {this.renderPomoteModelButton()}\n      </PageHeader>\n    );\n  }\n\n  render() {\n    const { modelName = '', modelVersion, tags, schema } = this.props;\n    const { description } = modelVersion;\n    const { isDeleteModalVisible, isDeleteModalConfirmLoading, showDescriptionEditor, isTagsRequestPending } =\n      this.state;\n    const title = (\n      <FormattedMessage\n        defaultMessage=\"Version {versionNum}\"\n        description=\"Title text for model version page\"\n        values={{ versionNum: modelVersion.version }}\n      />\n    );\n    const breadcrumbs = [\n      <Link to={ModelRegistryRoutes.modelListPageRoute}>\n        <FormattedMessage\n          defaultMessage=\"Registered Models\"\n          description=\"Text for link back to models page under the header on the model version\n             view page\"\n        />\n      </Link>,\n      <Link data-test-id=\"breadcrumbRegisteredModel\" to={ModelRegistryRoutes.getModelPageRoute(modelName)}>\n        {modelName}\n      </Link>,\n    ];\n    return (\n      <div>\n        {this.getPageHeader(title, breadcrumbs)}\n        {this.renderStatusAlert()}\n\n        {/* Metadata List */}\n        {this.renderMetadata(modelVersion)}\n\n        {/* New models UI switch */}\n        {shouldShowModelsNextUI() && (\n          <div css={{ marginTop: 8, display: 'flex', justifyContent: 'flex-end' }}>\n            <ModelsNextUIToggleSwitch />\n          </div>\n        )}\n\n        {/* Page Sections */}\n        <CollapsibleSection\n          title={\n            <span>\n              <FormattedMessage\n                defaultMessage=\"Description\"\n                description=\"Title text for the description section on the model version view page\"\n              />{' '}\n              {!showDescriptionEditor ? this.renderDescriptionEditIcon() : null}\n            </span>\n          }\n          forceOpen={showDescriptionEditor}\n          defaultCollapsed={!description}\n          data-test-id=\"model-version-description-section\"\n        >\n          <EditableNote\n            defaultMarkdown={description}\n            onSubmit={this.handleSubmitEditDescription}\n            onCancel={this.handleCancelEditDescription}\n            showEditor={showDescriptionEditor}\n          />\n        </CollapsibleSection>\n        <div data-test-id=\"tags-section\">\n          <CollapsibleSection\n            title={\n              <FormattedMessage\n                defaultMessage=\"Tags\"\n                description=\"Title text for the tags section on the model versions view page\"\n              />\n            }\n            defaultCollapsed={Utils.getVisibleTagValues(tags).length === 0}\n            data-test-id=\"model-version-tags-section\"\n          >\n            <EditableTagsTableView\n              // @ts-expect-error TS(2322): Type '{ innerRef: RefObject<unknown>; handleAddTag... Remove this comment to see the full error message\n              innerRef={this.formRef}\n              handleAddTag={this.handleAddTag}\n              handleDeleteTag={this.handleDeleteTag}\n              handleSaveEdit={this.handleSaveEdit}\n              tags={tags}\n              isRequestPending={isTagsRequestPending}\n            />\n          </CollapsibleSection>\n        </div>\n        <CollapsibleSection\n          title={\n            <FormattedMessage\n              defaultMessage=\"Schema\"\n              description=\"Title text for the schema section on the model versions view page\"\n            />\n          }\n          data-test-id=\"model-version-schema-section\"\n        >\n          <SchemaTable schema={schema} />\n        </CollapsibleSection>\n        <Modal\n          title={this.props.intl.formatMessage({\n            defaultMessage: 'Delete Model Version',\n            description: 'Title text for model version deletion modal in model versions view page',\n          })}\n          visible={isDeleteModalVisible}\n          confirmLoading={isDeleteModalConfirmLoading}\n          onOk={this.handleDeleteConfirm}\n          okText={this.props.intl.formatMessage({\n            defaultMessage: 'Delete',\n            description: 'OK button text for model version deletion modal in model versions view page',\n          })}\n          // @ts-expect-error TS(2322): Type '{ children: Element; title: any; visible: bo... Remove this comment to see the full error message\n          okType=\"danger\"\n          onCancel={this.hideDeleteModal}\n          cancelText={this.props.intl.formatMessage({\n            defaultMessage: 'Cancel',\n            description: 'Cancel button text for model version deletion modal in model versions view page',\n          })}\n        >\n          <span>\n            <FormattedMessage\n              defaultMessage=\"Are you sure you want to delete model version {versionNum}? This\n                 cannot be undone.\"\n              description=\"Comment text for model version deletion modal in model versions view\n                 page\"\n              values={{ versionNum: modelVersion.version }}\n            />\n          </span>\n        </Modal>\n      </div>\n    );\n  }\n}\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const { modelName } = ownProps;\n  const { version } = ownProps.modelVersion;\n  const tags = getModelVersionTags(modelName, version, state);\n  return { tags };\n};\nconst mapDispatchToProps = { setModelVersionTagApi, deleteModelVersionTagApi };\n\nexport const ModelVersionView = connect(\n  mapStateToProps,\n  mapDispatchToProps,\n)(withNextModelsUIContext(injectIntl<'intl', ModelVersionViewImplProps>(ModelVersionViewImpl)));\n", "/**\n * Extract artifact path from provided `modelSource` string\n */\nexport function extractArtifactPathFromModelSource(modelSource: string, runId: string) {\n  return modelSource.match(new RegExp(`/${runId}/artifacts/(.+)`))?.[1];\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { connect } from 'react-redux';\nimport {\n  getModelVersionApi,\n  getRegisteredModelApi,\n  updateModelVersionApi,\n  deleteModelVersionApi,\n  transitionModelVersionStageApi,\n  getModelVersionArtifactApi,\n  parseMlModelFile,\n} from '../actions';\nimport { getRunApi } from '../../experiment-tracking/actions';\nimport { getModelVersion, getModelVersionSchemas } from '../reducers';\nimport { ModelVersionView } from './ModelVersionView';\nimport {\n  ActivityTypes,\n  PendingModelVersionActivity,\n  MODEL_VERSION_STATUS_POLL_INTERVAL as POLL_INTERVAL,\n} from '../constants';\nimport Utils from '../../common/utils/Utils';\nimport { getRunInfo, getRunTags } from '../../experiment-tracking/reducers/Reducers';\nimport RequestStateWrapper, { triggerError } from '../../common/components/RequestStateWrapper';\nimport { ErrorView } from '../../common/components/ErrorView';\nimport { Spinner } from '../../common/components/Spinner';\nimport { ModelRegistryRoutes } from '../routes';\nimport { getProtoField } from '../utils';\nimport { getUUID } from '../../common/utils/ActionUtils';\nimport _ from 'lodash';\nimport { PageContainer } from '../../common/components/PageContainer';\nimport { withRouterNext } from '../../common/utils/withRouterNext';\nimport type { WithRouterNextProps } from '../../common/utils/withRouterNext';\nimport { withErrorBoundary } from '../../common/utils/withErrorBoundary';\nimport ErrorUtils from '../../common/utils/ErrorUtils';\nimport type { ModelEntity, RunInfoEntity } from '../../experiment-tracking/types';\nimport { ReduxState } from '../../redux-types';\nimport { ErrorCodes } from '../../common/constants';\nimport { injectIntl } from 'react-intl';\n\ntype ModelVersionPageImplProps = WithRouterNextProps & {\n  modelName: string;\n  version: string;\n  modelVersion?: any;\n  runInfo?: any;\n  runDisplayName?: string;\n  modelEntity?: ModelEntity;\n  getModelVersionApi: (...args: any[]) => any;\n  getRegisteredModelApi: typeof getRegisteredModelApi;\n  updateModelVersionApi: (...args: any[]) => any;\n  transitionModelVersionStageApi: (...args: any[]) => any;\n  deleteModelVersionApi: (...args: any[]) => any;\n  getRunApi: (...args: any[]) => any;\n  apis: any;\n  getModelVersionArtifactApi: (...args: any[]) => any;\n  parseMlModelFile: (...args: any[]) => any;\n  schema?: any;\n  activities?: Record<string, unknown>[];\n  intl?: any;\n};\n\ntype ModelVersionPageImplState = any;\n\nexport class ModelVersionPageImpl extends React.Component<ModelVersionPageImplProps, ModelVersionPageImplState> {\n  listTransitionRequestId: any;\n  pollIntervalId: any;\n\n  initGetModelVersionDetailsRequestId = getUUID();\n  getRunRequestId = getUUID();\n  updateModelVersionRequestId = getUUID();\n  transitionModelVersionStageRequestId = getUUID();\n  getModelVersionDetailsRequestId = getUUID();\n  initGetMlModelFileRequestId = getUUID();\n  state = {\n    criticalInitialRequestIds: [this.initGetModelVersionDetailsRequestId, this.initGetMlModelFileRequestId],\n  };\n\n  pollingRelatedRequestIds = [this.getModelVersionDetailsRequestId, this.getRunRequestId];\n\n  hasPendingPollingRequest = () =>\n    this.pollingRelatedRequestIds.every((requestId) => {\n      const request = this.props.apis[requestId];\n      return Boolean(request && request.active);\n    });\n\n  loadData = (isInitialLoading: any) => {\n    const promises = [this.getModelVersionDetailAndRunInfo(isInitialLoading)];\n    return Promise.all(promises);\n  };\n\n  pollData = () => {\n    const { modelName, version, navigate } = this.props;\n    if (!this.hasPendingPollingRequest() && Utils.isBrowserTabVisible()) {\n      // @ts-expect-error TS(2554): Expected 1 arguments, but got 0.\n      return this.loadData().catch((e) => {\n        if (e.getErrorCode() === 'RESOURCE_DOES_NOT_EXIST') {\n          Utils.logErrorAndNotifyUser(e);\n          this.props.deleteModelVersionApi(modelName, version, undefined, true);\n          navigate(ModelRegistryRoutes.getModelPageRoute(modelName));\n        } else {\n          // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n          console.error(e);\n        }\n      });\n    }\n    return Promise.resolve();\n  };\n\n  // We need to do this because currently the ModelVersionDetailed we got does not contain\n  // experimentId. We need experimentId to construct a link to the source run. This workaround can\n  // be removed after the availability of experimentId.\n  getModelVersionDetailAndRunInfo(isInitialLoading: any) {\n    const { modelName, version } = this.props;\n    return this.props\n      .getModelVersionApi(\n        modelName,\n        version,\n        isInitialLoading === true ? this.initGetModelVersionDetailsRequestId : this.getModelVersionDetailsRequestId,\n      )\n      .then(({ value }: any) => {\n        // Do not fetch run info if there is no run_id (e.g. model version created directly from a logged model)\n        if (value && !value[getProtoField('model_version')].run_link && value[getProtoField('model_version')]?.run_id) {\n          this.props.getRunApi(value[getProtoField('model_version')].run_id, this.getRunRequestId);\n        }\n      });\n  }\n  // We need this for getting mlModel artifact file,\n  // this will be replaced with a single backend call in the future when supported\n  getModelVersionMlModelFile() {\n    const { modelName, version } = this.props;\n    this.props\n      .getModelVersionArtifactApi(modelName, version)\n      .then((content: any) =>\n        this.props.parseMlModelFile(modelName, version, content.value, this.initGetMlModelFileRequestId),\n      )\n      .catch(() => {\n        // Failure of this call chain should not block the page. Here we remove\n        // `initGetMlModelFileRequestId` from `criticalInitialRequestIds`\n        // to unblock RequestStateWrapper from rendering its content\n        this.setState((prevState: any) => ({\n          criticalInitialRequestIds: _.without(prevState.criticalInitialRequestIds, this.initGetMlModelFileRequestId),\n        }));\n      });\n  }\n\n  // prettier-ignore\n  handleStageTransitionDropdownSelect = (\n    activity: PendingModelVersionActivity,\n    archiveExistingVersions?: boolean,\n  ) => {\n    const { modelName, version } = this.props;\n    const toStage = activity.to_stage;\n    if (activity.type === ActivityTypes.APPLIED_TRANSITION) {\n      this.props\n        .transitionModelVersionStageApi(\n          modelName,\n          version.toString(),\n          toStage,\n          archiveExistingVersions,\n          this.transitionModelVersionStageRequestId,\n        )\n        .then(this.loadData)\n        .catch(Utils.logErrorAndNotifyUser);\n    }\n  };\n\n  handleEditDescription = (description: any) => {\n    const { modelName, version } = this.props;\n    return (\n      this.props\n        .updateModelVersionApi(modelName, version, description, this.updateModelVersionRequestId)\n        .then(this.loadData)\n        // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n        .catch(console.error)\n    );\n  };\n\n  componentDidMount() {\n    // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n    this.loadData(true).catch(console.error);\n    this.loadModelDataWithAliases();\n    this.pollIntervalId = setInterval(this.pollData, POLL_INTERVAL);\n    this.getModelVersionMlModelFile();\n  }\n\n  loadModelDataWithAliases = () => {\n    this.props.getRegisteredModelApi(this.props.modelName);\n  };\n\n  // Make a new initial load if model version or name has changed\n  componentDidUpdate(prevProps: ModelVersionPageImplProps) {\n    if (this.props.version !== prevProps.version || this.props.modelName !== prevProps.modelName) {\n      // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n      this.loadData(true).catch(console.error);\n      this.getModelVersionMlModelFile();\n    }\n  }\n\n  componentWillUnmount() {\n    clearInterval(this.pollIntervalId);\n  }\n\n  render() {\n    const { modelName, version, modelVersion, runInfo, runDisplayName, navigate, schema, modelEntity } = this.props;\n\n    return (\n      <PageContainer>\n        <RequestStateWrapper\n          requestIds={this.state.criticalInitialRequestIds}\n          // eslint-disable-next-line no-trailing-spaces\n        >\n          {(loading: any, hasError: any, requests: any) => {\n            if (hasError) {\n              clearInterval(this.pollIntervalId);\n              const resourceConflictError = Utils.getResourceConflictError(\n                requests,\n                this.state.criticalInitialRequestIds,\n              );\n              if (resourceConflictError) {\n                return (\n                  <ErrorView\n                    statusCode={409}\n                    subMessage={resourceConflictError.error.getMessageField()}\n                    fallbackHomePageReactRoute={ModelRegistryRoutes.modelListPageRoute}\n                  />\n                );\n              }\n              if (Utils.shouldRender404(requests, this.state.criticalInitialRequestIds)) {\n                return (\n                  <ErrorView\n                    statusCode={404}\n                    subMessage={`Model ${modelName} v${version} does not exist`}\n                    fallbackHomePageReactRoute={ModelRegistryRoutes.modelListPageRoute}\n                  />\n                );\n              }\n              // TODO(Zangr) Have a more generic boundary to handle all errors, not just 404.\n              const permissionDeniedErrors = requests.filter((request: any) => {\n                return (\n                  this.state.criticalInitialRequestIds.includes(request.id) &&\n                  request.error?.getErrorCode() === ErrorCodes.PERMISSION_DENIED\n                );\n              });\n              if (permissionDeniedErrors && permissionDeniedErrors[0]) {\n                return (\n                  <ErrorView\n                    statusCode={403}\n                    subMessage={this.props.intl.formatMessage(\n                      {\n                        defaultMessage: 'Permission denied for {modelName} version {version}. Error: \"{errorMsg}\"',\n                        description: 'Permission denied error message on model version detail page',\n                      },\n                      {\n                        modelName: modelName,\n                        version: version,\n                        errorMsg: permissionDeniedErrors[0].error?.getMessageField(),\n                      },\n                    )}\n                    fallbackHomePageReactRoute={ModelRegistryRoutes.modelListPageRoute}\n                  />\n                );\n              }\n              triggerError(requests);\n            } else if (loading) {\n              return <Spinner />;\n            } else if (modelVersion) {\n              // Null check to prevent NPE after delete operation\n              return (\n                <ModelVersionView\n                  modelName={modelName}\n                  modelVersion={modelVersion}\n                  modelEntity={modelEntity}\n                  runInfo={runInfo}\n                  runDisplayName={runDisplayName}\n                  handleEditDescription={this.handleEditDescription}\n                  deleteModelVersionApi={this.props.deleteModelVersionApi}\n                  navigate={navigate}\n                  handleStageTransitionDropdownSelect={this.handleStageTransitionDropdownSelect}\n                  schema={schema}\n                  onAliasesModified={this.loadModelDataWithAliases}\n                />\n              );\n            }\n            return null;\n          }}\n        </RequestStateWrapper>\n      </PageContainer>\n    );\n  }\n}\n\nconst mapStateToProps = (state: ReduxState, ownProps: WithRouterNextProps<{ modelName: string; version: string }>) => {\n  const modelName = decodeURIComponent(ownProps.params.modelName);\n  const { version } = ownProps.params;\n  const modelVersion = getModelVersion(state, modelName, version);\n  const schema = getModelVersionSchemas(state, modelName, version);\n  let runInfo: RunInfoEntity | null = null;\n  if (modelVersion && !modelVersion.run_link) {\n    runInfo = getRunInfo(modelVersion && modelVersion.run_id, state);\n  }\n  const tags = runInfo && getRunTags(runInfo.runUuid, state);\n  const runDisplayName = tags && runInfo && Utils.getRunDisplayName(runInfo, runInfo.runUuid);\n  const modelEntity = state.entities.modelByName[modelName];\n  const { apis } = state;\n  return {\n    modelName,\n    version,\n    modelVersion,\n    schema,\n    runInfo,\n    runDisplayName,\n    apis,\n    modelEntity,\n  };\n};\n\nconst mapDispatchToProps = {\n  getModelVersionApi,\n  getRegisteredModelApi,\n  updateModelVersionApi,\n  transitionModelVersionStageApi,\n  getModelVersionArtifactApi,\n  parseMlModelFile,\n  deleteModelVersionApi,\n  getRunApi,\n};\n\nconst ModelVersionPageWithRouter = withRouterNext(\n  // @ts-expect-error TS(2769): No overload matches this call.\n  connect(mapStateToProps, mapDispatchToProps)(injectIntl(ModelVersionPageImpl)),\n);\n\nexport const ModelVersionPage = withErrorBoundary(ErrorUtils.mlflowServices.MODEL_REGISTRY, ModelVersionPageWithRouter);\n\nexport default ModelVersionPage;\n", "import { REGISTERED_MODELS_SEARCH_NAME_FIELD } from '../constants';\nimport { resolveFilterValue } from '../actions';\n\nexport function getModelNameFilter(query: string): string {\n  if (query) {\n    return `${REGISTERED_MODELS_SEARCH_NAME_FIELD} ilike ${resolveFilterValue(query, true)}`;\n  } else {\n    return '';\n  }\n}\n\nexport function getCombinedSearchFilter({\n  query = '',\n}: {\n  query?: string;\n} = {}) {\n  const filters = [];\n  const initialFilter = query.includes('tags.') ? query : getModelNameFilter(query);\n  if (initialFilter) filters.push(initialFilter);\n  return filters.join(' AND ');\n}\n\nexport function constructSearchInputFromURLState(urlState: Record<string, string>): string {\n  if ('searchInput' in urlState) {\n    return urlState['searchInput'];\n  }\n  if ('nameSearchInput' in urlState && 'tagSearchInput' in urlState) {\n    return getModelNameFilter(urlState['nameSearchInput']) + ` AND ` + urlState['tagSearchInput'];\n  }\n  if ('tagSearchInput' in urlState) {\n    return urlState['tagSearchInput'];\n  }\n  if ('nameSearchInput' in urlState) {\n    return urlState['nameSearchInput'];\n  }\n  return '';\n}\n", "import { identity, isFunction } from 'lodash';\nimport React from 'react';\n\n/**\n * A safe version of `useDeferredValue` that falls back to identity (A->A) if `useDeferredValue` is not supported\n * by current React version.\n */\nexport const useSafeDeferredValue: <T>(value: T) => T =\n  'useDeferredValue' in React && isFunction(React.useDeferredValue) ? React.useDeferredValue : identity;\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { LegacyForm, Input, LegacySelect } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\nimport './RegisterModelForm.css';\n\nconst { Option, OptGroup } = LegacySelect;\n\nconst CREATE_NEW_MODEL_LABEL = 'Create New Model';\n// Include 'CREATE_NEW_MODEL_LABEL' as part of the value for filtering to work properly. Also added\n// prefix and postfix to avoid value conflict with actual model names.\nexport const CREATE_NEW_MODEL_OPTION_VALUE = `$$$__${CREATE_NEW_MODEL_LABEL}__$$$`;\nexport const SELECTED_MODEL_FIELD = 'selectedModel';\nexport const MODEL_NAME_FIELD = 'modelName';\nconst DESCRIPTION_FIELD = 'description';\n\ntype Props = {\n  modelByName?: any;\n  isCopy?: boolean;\n  onSearchRegisteredModels: (...args: any[]) => any;\n  innerRef: any;\n};\n\ntype State = any;\n\nexport class RegisterModelForm extends React.Component<Props, State> {\n  state = {\n    selectedModel: null,\n  };\n\n  handleModelSelectChange = (selectedModel: any) => {\n    this.setState({ selectedModel });\n  };\n\n  modelNameValidator = (rule: any, value: any, callback: any) => {\n    const { modelByName } = this.props;\n    callback(modelByName[value] ? `Model \"${value}\" already exists.` : undefined);\n  };\n\n  handleFilterOption = (input: any, option: any) => {\n    const value = (option && option.value) || '';\n    return value.toLowerCase().indexOf(input.toLowerCase()) !== -1;\n  };\n\n  renderExplanatoryText() {\n    const { isCopy } = this.props;\n    const { selectedModel } = this.state;\n    const creatingNewModel = selectedModel === CREATE_NEW_MODEL_OPTION_VALUE;\n\n    if (!selectedModel || creatingNewModel) {\n      return null;\n    }\n\n    const explanation = isCopy ? (\n      <FormattedMessage\n        defaultMessage=\"The model version will be copied to {selectedModel} as a new version.\"\n        description=\"Model registry > OSS Promote model modal > copy explanatory text\"\n        values={{ selectedModel: selectedModel }}\n      />\n    ) : (\n      <FormattedMessage\n        defaultMessage=\"The model will be registered as a new version of {selectedModel}.\"\n        description=\"Explantory text for registering a model\"\n        values={{ selectedModel: selectedModel }}\n      />\n    );\n\n    return <p className=\"modal-explanatory-text\">{explanation}</p>;\n  }\n\n  renderModel(model: any) {\n    return (\n      <Option value={model.name} key={model.name}>\n        {model.name}\n      </Option>\n    );\n  }\n  render() {\n    const { modelByName, innerRef, isCopy } = this.props;\n    const { selectedModel } = this.state;\n    const creatingNewModel = selectedModel === CREATE_NEW_MODEL_OPTION_VALUE;\n    return (\n      // @ts-expect-error TS(2322): Type '{ children: (Element | null)[]; ref: any; la... Remove this comment to see the full error message\n      <LegacyForm ref={innerRef} layout=\"vertical\" className=\"register-model-form\">\n        {/* \"+ Create new model\" OR \"Select existing model\" */}\n        <LegacyForm.Item\n          label={isCopy ? <b>Copy to model</b> : 'Model'}\n          name={SELECTED_MODEL_FIELD}\n          rules={[{ required: true, message: 'Please select a model or create a new one.' }]}\n        >\n          <LegacySelect\n            dropdownClassName=\"model-select-dropdown\"\n            onChange={this.handleModelSelectChange}\n            placeholder=\"Select a model\"\n            filterOption={this.handleFilterOption}\n            onSearch={this.props.onSearchRegisteredModels}\n            // @ts-expect-error TS(2769): No overload matches this call.\n            showSearch\n          >\n            <Option value={CREATE_NEW_MODEL_OPTION_VALUE} className=\"create-new-model-option\">\n              <i className=\"fa fa-plus fa-fw\" style={{ fontSize: 13 }} /> {CREATE_NEW_MODEL_LABEL}\n            </Option>\n            <OptGroup label=\"Models\">{Object.values(modelByName).map((model) => this.renderModel(model))}</OptGroup>\n          </LegacySelect>\n        </LegacyForm.Item>\n\n        {/* Name the new model when \"+ Create new model\" is selected */}\n        {creatingNewModel ? (\n          <LegacyForm.Item\n            label=\"Model Name\"\n            name={MODEL_NAME_FIELD}\n            rules={[\n              { required: true, message: 'Please input a name for the new model.' },\n              { validator: this.modelNameValidator },\n            ]}\n          >\n            <Input\n              componentId=\"codegen_mlflow_app_src_model-registry_components_registermodelform.tsx_132\"\n              placeholder=\"Input a model name\"\n            />\n          </LegacyForm.Item>\n        ) : null}\n\n        {/* Explanatory text shown when existing model is selected */}\n        {this.renderExplanatoryText()}\n      </LegacyForm>\n    );\n  }\n}\n"], "names": ["Text", "Typography", "getColumnTypeRepr", "columnType", "indentationLevel", "type", "indentation", "repeat", "Object", "keys", "properties", "map", "propertyName", "property", "requiredRepr", "required", "propertyRepr", "indentOffset", "slice", "join", "items", "_ref2", "name", "styles", "ColumnName", "_ref", "spec", "undefined", "optional", "requiredTag", "_jsx", "bold", "children", "color", "_jsxs", "css", "ColumnSchema", "_ref3", "theme", "useDesignSystemTheme", "repr", "tensorType", "dtype", "shape", "_css", "whiteSpace", "padding", "spacing", "sm", "marginTop", "marginBottom", "_ref5", "_ref6", "SchemaTableRow", "_ref4", "schemaData", "isEmptySchema", "isEmpty", "intl", "useIntl", "isLargeSchema", "Boolean", "length", "searchText", "setSearchText", "useState", "deferredSearchText", "useSafeDeferredValue", "filteredSchemaData", "useMemo", "normalizedSearchText", "toLowerCase", "filter", "schemaRow", "includes", "TableRow", "TableCell", "FormattedMessage", "id", "defaultMessage", "values", "link", "chunks", "href", "LogModelWithSignatureUrl", "target", "rel", "_Fragment", "Spacer", "Hint", "currentResults", "allResults", "Input", "placeholder", "formatMessage", "componentId", "value", "onChange", "e", "index", "_ref8", "_ref9", "_ref0", "_ref1", "_ref10", "SchemaTable", "_ref7", "schema", "defaultExpandAllRows", "inputsExpanded", "setInputsExpanded", "outputsExpanded", "setOutputsExpanded", "Table", "<PERSON><PERSON><PERSON><PERSON>", "TableHeader", "paddingLeft", "lg", "xs", "onClick", "display", "alignItems", "gap", "width", "height", "justifyContent", "svg", "colors", "textSecondary", "MinusSquareIcon", "PlusSquareIcon", "numInputs", "inputs", "numOutputs", "outputs", "PromoteModelButton", "props", "navigate", "useNavigate", "createRegisteredModelRequestId", "useRef", "getUUID", "createModelVersionRequestId", "modelVersion", "visible", "setVisible", "confirmLoading", "setConfirmLoading", "dispatch", "useDispatch", "modelByName", "useSelector", "state", "entities", "form", "resetAndClearModalForm", "handleRegistrationFailure", "Utils", "logErrorAndNotifyUser", "handleSearchRegisteredModels", "useCallback", "input", "searchRegisteredModelsApi", "getModelNameFilter", "debouncedHandleSearchRegisteredModels", "debounce", "useEffect", "className", "<PERSON><PERSON>", "showRegisterModal", "Modal", "title", "sourceModelName", "sourceModelVersion", "version", "onOk", "handleCopyModel", "current", "validateFields", "then", "selectedModelName", "SELECTED_MODEL_FIELD", "copySource", "CREATE_NEW_MODEL_OPTION_VALUE", "newModelName", "MODEL_NAME_FIELD", "createRegisteredModelApi", "createModelVersionApi", "run_id", "tags", "mvResult", "ModelRegistryRoutes", "getModelVersionPageRoute", "catch", "okText", "cancelText", "onCancel", "hideRegisterModal", "centered", "Paragraph", "Link", "openInNewTab", "RegisterModelForm", "innerRef", "onSearchRegisteredModels", "isCopy", "ModelStageTransitionFormModalMode", "ModelStageTransitionFormModal", "toStage", "allowArchivingExistingVersions", "transitionDescription", "onConfirm", "mode", "RequestOrDirect", "useForm", "defaultValues", "comment", "archiveExistingVersions", "reset", "Approve", "Reject", "Cancel", "handleSubmit", "size", "FormUI", "Label", "htmlFor", "RHFControlledComponents", "TextArea", "control", "rows", "Checkbox", "<PERSON><PERSON><PERSON>", "content", "archiveExistingVersionToolTipText", "marginRight", "currentStage", "marginLeft", "StageTagComponents", "archivedStage", "Stages", "ARCHIVED", "ModelStageTransitionDropdown", "React", "constructor", "arguments", "confirmModalVisible", "confirmingActivity", "handleConfirm", "handleMenuItemClick", "activity", "onSelect", "this", "setState", "handleConfirmModalCancel", "getNoneCurrentStages", "stages", "_", "s", "getMenu", "nonCurrentStages", "<PERSON><PERSON>", "stage", "<PERSON><PERSON>", "ActivityTypes", "APPLIED_TRANSITION", "to_stage", "ArrowRightIcon", "renderConfirmModal", "ACTIVE_STAGES", "renderActivityDescription", "render", "Dropdown", "overlay", "trigger", "NONE", "ChevronDownIcon", "cursor", "defaultProps", "ModelVersionViewAliasEditor", "aliases", "modelEntity", "onAliasesModified", "EditAliasesModal", "showEditAliasesModal", "useEditRegisteredModelAliasesModal", "model", "onSuccess", "onAddEdit", "alias", "ModelVersionAliasTag", "compact", "icon", "PencilIcon", "ModelVersionViewImpl", "isDeleteModalVisible", "isDeleteModalConfirmLoading", "showDescriptionEditor", "isTagsRequestPending", "formRef", "handleDeleteConfirm", "modelName", "showConfirmLoading", "deleteModelVersionApi", "getModelPageRoute", "hideConfirmLoading", "showDeleteModal", "hideDeleteModal", "handleCancelEditDescription", "handleSubmitEditDescription", "description", "handleEditDescription", "startEditingDescription", "stopPropagation", "handleAddTag", "setModelVersionTagApi", "resetFields", "ex", "console", "error", "userVisibleError", "ErrorWrapper", "getMessageField", "message", "displayGlobalErrorNotification", "handleSaveEdit", "handleDeleteTag", "deleteModelVersionTagApi", "renderAliasEditor", "_this$props$modelEnti", "_this$props$modelEnti2", "currentVersion", "currentVersionAliases", "Descriptions", "label", "componentDidMount", "pageTitle", "updatePageTitle", "shouldHideDeleteOption", "renderStageDropdown", "handleStageTransitionDropdownSelect", "status", "ModelVersionStatus", "READY", "current_stage", "permissionLevel", "permission_level", "renderDisabledStage", "tooltipContent", "modelStagesMigrationGuideLink", "StageLabels", "LegacyTooltip", "placement", "InfoIcon", "renderRegisteredTimestampDescription", "creation_timestamp", "formatTimestamp", "renderCreatorDescription", "user_id", "renderLastModifiedDescription", "last_updated_timestamp", "renderSourceRunDescription", "_this$props$modelVers", "resolveRunLink", "renderCopiedFromLink", "source", "test", "sourceParts", "split", "to", "getDescriptions", "usingNextModelsUI", "item", "renderMetadata", "renderStatusAlert", "status_message", "DefaultModelVersionStatusMessages", "FAILED_REGISTRATION", "<PERSON><PERSON>", "ModelVersionStatusIcons", "banner", "renderDescriptionEditIcon", "runInfo", "run_link", "resolveRunName", "_this$props$modelVers2", "artifactPath", "modelSource", "runId", "_modelSource$match", "match", "RegExp", "extractArtifactPathFromModelSource", "runUuid", "Routers", "getRunPageRoute", "experimentId", "runDisplayName", "substr", "renderPomoteModelButton", "getPageHeader", "breadcrumbs", "menu", "itemName", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "OverflowMenu", "versionNum", "modelListPageRoute", "shouldShowModelsNextUI", "ModelsNextUIToggleSwitch", "CollapsibleSection", "forceOpen", "defaultCollapsed", "EditableNote", "defaultMarkdown", "onSubmit", "showEditor", "getVisibleTagValues", "EditableTagsTableView", "isRequestPending", "okType", "mapDispatchToProps", "ModelVersionView", "connect", "mapStateToProps", "ownProps", "getModelVersionTags", "withNextModelsUIContext", "injectIntl", "ModelVersionPageImpl", "listTransitionRequestId", "pollIntervalId", "initGetModelVersionDetailsRequestId", "getRunRequestId", "updateModelVersionRequestId", "transitionModelVersionStageRequestId", "getModelVersionDetailsRequestId", "initGetMlModelFileRequestId", "criticalInitialRequestIds", "pollingRelatedRequestIds", "hasPendingPollingRequest", "every", "requestId", "request", "apis", "active", "loadData", "isInitialLoading", "promises", "getModelVersionDetailAndRunInfo", "Promise", "all", "pollData", "isBrowserTabVisible", "getErrorCode", "resolve", "transitionModelVersionStageApi", "toString", "updateModelVersionApi", "loadModelDataWithAliases", "getRegisteredModelApi", "getModelVersionApi", "_value$getProtoField", "getProtoField", "getRunApi", "getModelVersionMlModelFile", "getModelVersionArtifactApi", "parseMlModelFile", "prevState", "setInterval", "POLL_INTERVAL", "componentDidUpdate", "prevProps", "componentWillUnmount", "clearInterval", "<PERSON><PERSON><PERSON><PERSON>", "RequestStateWrapper", "requestIds", "loading", "<PERSON><PERSON><PERSON><PERSON>", "requests", "resourceConflictError", "getResourceConflictError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "statusCode", "subMessage", "fallbackHomePageReactRoute", "shouldRender404", "permissionDeniedErrors", "_request$error", "ErrorCodes", "PERMISSION_DENIED", "_permissionDeniedErro", "errorMsg", "triggerError", "Spinner", "ModelVersionPageWithRouter", "withRouterNext", "decodeURIComponent", "params", "getModelVersion", "getModelVersionSchemas", "getRunInfo", "getRunTags", "getRunDisplayName", "ModelVersionPage", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "MODEL_REGISTRY", "query", "REGISTERED_MODELS_SEARCH_NAME_FIELD", "resolveFilterValue", "getCombinedSearchFilter", "filters", "initialFilter", "push", "constructSearchInputFromURLState", "urlState", "isFunction", "identity", "Option", "OptGroup", "LegacySelect", "CREATE_NEW_MODEL_LABEL", "selected<PERSON><PERSON>l", "handleModelSelectChange", "modelNameValidator", "rule", "callback", "handleFilterOption", "option", "indexOf", "renderExplanatoryText", "explanation", "renderModel", "creatingNewModel", "LegacyForm", "ref", "layout", "rules", "dropdownClassName", "filterOption", "onSearch", "showSearch", "style", "fontSize", "validator"], "sourceRoot": ""}