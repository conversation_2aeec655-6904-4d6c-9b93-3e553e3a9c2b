# Generated by Django 5.1.9 on 2025-06-06 08:45

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0034_organization_account_locked_until_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="organization",
            name="account_locked_until",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="failed_login_attempts",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="google_id",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="is_email_verified",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="otp",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="otp_created_at",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="password",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="password_reset_expires",
        ),
        migrations.RemoveField(
            model_name="organization",
            name="password_reset_token",
        ),
        migrations.AddField(
            model_name="organization",
            name="address",
            field=models.TextField(default="address"),
        ),
        migrations.AddField(
            model_name="organization",
            name="description",
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="is_verified",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="organization",
            name="phone_number",
            field=models.CharField(max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="registration_date",
            field=models.DateField(default=datetime.date.today),
        ),
        migrations.AddField(
            model_name="organization",
            name="registration_proof",
            field=models.FileField(null=True, upload_to="organization_docs/"),
        ),
        migrations.AddField(
            model_name="organization",
            name="verification_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="verified_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="verified_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="verified_organizations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="organization",
            name="status",
            field=models.BooleanField(default=False),
        ),
    ]
