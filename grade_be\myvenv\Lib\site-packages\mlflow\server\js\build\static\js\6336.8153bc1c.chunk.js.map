{"version": 3, "file": "static/js/6336.8153bc1c.chunk.js", "mappings": "6sBAAWA,ECAAC,EAwCAC,E,WAQJ,SAASC,EAAiBC,GAC7B,OAAOA,EAAGC,OAASJ,EAAKK,OAC5B,CACO,SAASC,EAAkBH,GAC9B,OAAOA,EAAGC,OAASJ,EAAKO,QAC5B,CACO,SAASC,EAAgBL,GAC5B,OAAOA,EAAGC,OAASJ,EAAKS,MAC5B,CACO,SAASC,EAAcP,GAC1B,OAAOA,EAAGC,OAASJ,EAAKW,IAC5B,CACO,SAASC,EAAcT,GAC1B,OAAOA,EAAGC,OAASJ,EAAKa,IAC5B,CACO,SAASC,EAAgBX,GAC5B,OAAOA,EAAGC,OAASJ,EAAKe,MAC5B,CACO,SAASC,EAAgBb,GAC5B,OAAOA,EAAGC,OAASJ,EAAKiB,MAC5B,CACO,SAASC,EAAef,GAC3B,OAAOA,EAAGC,OAASJ,EAAKmB,KAC5B,CACO,SAASC,EAAajB,GACzB,OAAOA,EAAGC,OAASJ,EAAKqB,GAC5B,CACO,SAASC,EAAiBnB,GAC7B,SAAUA,GAAoB,kBAAPA,GAAmBA,EAAGC,OAASH,EAAcQ,OACxE,CACO,SAASc,EAAmBpB,GAC/B,SAAUA,GAAoB,kBAAPA,GAAmBA,EAAGC,OAASH,EAAcuB,SACxE,CACO,SAASC,EAAqBC,GACjC,MAAO,CACHtB,KAAMJ,EAAKK,QACXqB,MAAOA,EAEf,CACO,SAASC,EAAoBD,EAAOE,GACvC,MAAO,CACHxB,KAAMJ,EAAKS,OACXiB,MAAOA,EACPE,MAAOA,EAEf,ED5FA,SAAW7B,GAEPA,EAAUA,EAAyC,8BAAI,GAAK,gCAE5DA,EAAUA,EAA0B,eAAI,GAAK,iBAE7CA,EAAUA,EAA8B,mBAAI,GAAK,qBAEjDA,EAAUA,EAAgC,qBAAI,GAAK,uBAEnDA,EAAUA,EAAiC,sBAAI,GAAK,wBAEpDA,EAAUA,EAAiC,sBAAI,GAAK,wBAEpDA,EAAUA,EAAmC,wBAAI,GAAK,0BAEtDA,EAAUA,EAAsC,2BAAI,GAAK,6BAEzDA,EAAUA,EAAkC,uBAAI,GAAK,yBAErDA,EAAUA,EAAqC,0BAAI,IAAM,4BAEzDA,EAAUA,EAA4C,iCAAI,IAAM,mCAEhEA,EAAUA,EAA0C,+BAAI,IAAM,iCAE9DA,EAAUA,EAA+C,oCAAI,IAAM,sCAEnEA,EAAUA,EAAgD,qCAAI,IAAM,uCAEpEA,EAAUA,EAA2C,gCAAI,IAAM,kCAE/DA,EAAUA,EAA2C,gCAAI,IAAM,kCAE/DA,EAAUA,EAAoD,yCAAI,IAAM,2CAKxEA,EAAUA,EAAoD,yCAAI,IAAM,2CAExEA,EAAUA,EAA4C,iCAAI,IAAM,mCAKhEA,EAAUA,EAA8C,mCAAI,IAAM,qCAIlEA,EAAUA,EAA8C,mCAAI,IAAM,qCAElEA,EAAUA,EAAgC,qBAAI,IAAM,uBAEpDA,EAAUA,EAAuB,YAAI,IAAM,cAE3CA,EAAUA,EAA4B,iBAAI,IAAM,mBAEhDA,EAAUA,EAAiC,sBAAI,IAAM,wBAErDA,EAAUA,EAAwB,aAAI,IAAM,cAC/C,CA7DD,CA6DGA,IAAcA,EAAY,CAAC,IC7D9B,SAAWC,GAIPA,EAAKA,EAAc,QAAI,GAAK,UAI5BA,EAAKA,EAAe,SAAI,GAAK,WAI7BA,EAAKA,EAAa,OAAI,GAAK,SAI3BA,EAAKA,EAAW,KAAI,GAAK,OAIzBA,EAAKA,EAAW,KAAI,GAAK,OAIzBA,EAAKA,EAAa,OAAI,GAAK,SAI3BA,EAAKA,EAAa,OAAI,GAAK,SAK3BA,EAAKA,EAAY,MAAI,GAAK,QAI1BA,EAAKA,EAAU,IAAI,GAAK,KAC3B,CAtCD,CAsCGA,IAASA,EAAO,CAAC,IAEpB,SAAWC,GACPA,EAAcA,EAAsB,OAAI,GAAK,SAC7CA,EAAcA,EAAwB,SAAI,GAAK,UAClD,CAHD,CAGGA,IAAkBA,EAAgB,CAAC,IC3C/B,IAAI4B,EAAwB,+CCI/BC,EAAkB,4KAOf,SAASC,EAAsBC,GAClC,IAAIC,EAAS,CAAC,EA0Gd,OAzGAD,EAASE,QAAQJ,GAAiB,SAAUK,GACxC,IAAIC,EAAMD,EAAME,OAChB,OAAQF,EAAM,IAEV,IAAK,IACDF,EAAOK,IAAc,IAARF,EAAY,OAAiB,IAARA,EAAY,SAAW,QACzD,MAEJ,IAAK,IACDH,EAAOM,KAAe,IAARH,EAAY,UAAY,UACtC,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAII,WAAW,gEAEzB,IAAK,IACL,IAAK,IACD,MAAM,IAAIA,WAAW,8CAEzB,IAAK,IACL,IAAK,IACDP,EAAOQ,MAAQ,CAAC,UAAW,UAAW,QAAS,OAAQ,UAAUL,EAAM,GACvE,MAEJ,IAAK,IACL,IAAK,IACD,MAAM,IAAII,WAAW,2CACzB,IAAK,IACDP,EAAOS,IAAM,CAAC,UAAW,WAAWN,EAAM,GAC1C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAII,WAAW,6DAEzB,IAAK,IACDP,EAAOU,QAAkB,IAARP,EAAY,QAAkB,IAARA,EAAY,SAAW,QAC9D,MACJ,IAAK,IACD,GAAIA,EAAM,EACN,MAAM,IAAII,WAAW,iDAEzBP,EAAOU,QAAU,CAAC,QAAS,OAAQ,SAAU,SAASP,EAAM,GAC5D,MACJ,IAAK,IACD,GAAIA,EAAM,EACN,MAAM,IAAII,WAAW,iDAEzBP,EAAOU,QAAU,CAAC,QAAS,OAAQ,SAAU,SAASP,EAAM,GAC5D,MAEJ,IAAK,IACDH,EAAOW,QAAS,EAChB,MACJ,IAAK,IACL,IAAK,IACD,MAAM,IAAIJ,WAAW,8DAEzB,IAAK,IACDP,EAAOY,UAAY,MACnBZ,EAAOa,KAAO,CAAC,UAAW,WAAWV,EAAM,GAC3C,MACJ,IAAK,IACDH,EAAOY,UAAY,MACnBZ,EAAOa,KAAO,CAAC,UAAW,WAAWV,EAAM,GAC3C,MACJ,IAAK,IACDH,EAAOY,UAAY,MACnBZ,EAAOa,KAAO,CAAC,UAAW,WAAWV,EAAM,GAC3C,MACJ,IAAK,IACDH,EAAOY,UAAY,MACnBZ,EAAOa,KAAO,CAAC,UAAW,WAAWV,EAAM,GAC3C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAII,WAAW,oEAEzB,IAAK,IACDP,EAAOc,OAAS,CAAC,UAAW,WAAWX,EAAM,GAC7C,MAEJ,IAAK,IACDH,EAAOe,OAAS,CAAC,UAAW,WAAWZ,EAAM,GAC7C,MACJ,IAAK,IACL,IAAK,IACD,MAAM,IAAII,WAAW,8DAEzB,IAAK,IACDP,EAAOgB,aAAeb,EAAM,EAAI,QAAU,OAC1C,MACJ,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACD,MAAM,IAAII,WAAW,wEAE7B,MAAO,EACX,IACOP,CACX,CCvHO,IAAIiB,EAAoB,wCC8B/B,IAAIC,EAA2B,mCAC3BC,EAA8B,wBAC9BC,EAAsB,0BACtBC,EAA8B,SAClC,SAASC,EAA0BC,GAC/B,IAAIvB,EAAS,CAAC,EA6Bd,MA5B4B,MAAxBuB,EAAIA,EAAInB,OAAS,GACjBJ,EAAOwB,iBAAmB,gBAEG,MAAxBD,EAAIA,EAAInB,OAAS,KACtBJ,EAAOwB,iBAAmB,iBAE9BD,EAAItB,QAAQkB,GAA6B,SAAUM,EAAGC,EAAIC,GAoBtD,MAlBkB,kBAAPA,GACP3B,EAAO4B,yBAA2BF,EAAGtB,OACrCJ,EAAO6B,yBAA2BH,EAAGtB,QAGzB,MAAPuB,EACL3B,EAAO4B,yBAA2BF,EAAGtB,OAGtB,MAAVsB,EAAG,GACR1B,EAAO6B,yBAA2BH,EAAGtB,QAIrCJ,EAAO4B,yBAA2BF,EAAGtB,OACrCJ,EAAO6B,yBACHH,EAAGtB,QAAwB,kBAAPuB,EAAkBA,EAAGvB,OAAS,IAEnD,EACX,IACOJ,CACX,CACA,SAAS8B,EAAUP,GACf,OAAQA,GACJ,IAAK,YACD,MAAO,CACHQ,YAAa,QAErB,IAAK,kBACL,IAAK,KACD,MAAO,CACHC,aAAc,cAEtB,IAAK,cACL,IAAK,KACD,MAAO,CACHD,YAAa,UAErB,IAAK,yBACL,IAAK,MACD,MAAO,CACHA,YAAa,SACbC,aAAc,cAEtB,IAAK,mBACL,IAAK,KACD,MAAO,CACHD,YAAa,cAErB,IAAK,8BACL,IAAK,MACD,MAAO,CACHA,YAAa,aACbC,aAAc,cAEtB,IAAK,aACL,IAAK,KACD,MAAO,CACHD,YAAa,SAG7B,CACA,SAASE,EAAyCC,GAE9C,IAAIlC,EAaJ,GAZgB,MAAZkC,EAAK,IAA0B,MAAZA,EAAK,IACxBlC,EAAS,CACLmC,SAAU,eAEdD,EAAOA,EAAKE,MAAM,IAED,MAAZF,EAAK,KACVlC,EAAS,CACLmC,SAAU,cAEdD,EAAOA,EAAKE,MAAM,IAElBpC,EAAQ,CACR,IAAI+B,EAAcG,EAAKE,MAAM,EAAG,GAShC,GARoB,OAAhBL,GACA/B,EAAO+B,YAAc,SACrBG,EAAOA,EAAKE,MAAM,IAEG,OAAhBL,IACL/B,EAAO+B,YAAc,aACrBG,EAAOA,EAAKE,MAAM,KAEjBf,EAA4BgB,KAAKH,GAClC,MAAM,IAAII,MAAM,6CAEpBtC,EAAOuC,qBAAuBL,EAAK9B,MACvC,CACA,OAAOJ,CACX,CACA,SAASwC,EAAqBC,GAC1B,IACIC,EAAWZ,EAAUW,GACzB,OAAIC,GAFS,CAAC,CAMlB,CAIO,SAASC,EAAoBC,GAEhC,IADA,IAAI5C,EAAS,CAAC,EACL6C,EAAK,EAAGC,EAAWF,EAAQC,EAAKC,EAAS1C,OAAQyC,IAAM,CAC5D,IAAIE,EAAQD,EAASD,GACrB,OAAQE,EAAMb,MACV,IAAK,UACL,IAAK,IACDlC,EAAOL,MAAQ,UACf,SACJ,IAAK,QACDK,EAAOL,MAAQ,UACfK,EAAOgD,MAAQ,IACf,SACJ,IAAK,WACDhD,EAAOL,MAAQ,WACfK,EAAOiD,SAAWF,EAAMG,QAAQ,GAChC,SACJ,IAAK,YACL,IAAK,KACDlD,EAAOmD,aAAc,EACrB,SACJ,IAAK,oBACL,IAAK,IACDnD,EAAOoD,sBAAwB,EAC/B,SACJ,IAAK,eACL,IAAK,OACDpD,EAAOL,MAAQ,OACfK,EAAOqD,KAAqBN,EAAMG,QAAQ,GArJ1CjD,QAAQ,UAAW,IAsJnB,SACJ,IAAK,gBACL,IAAK,IACDD,EAAOmC,SAAW,UAClBnC,EAAOsD,eAAiB,QACxB,SACJ,IAAK,eACL,IAAK,KACDtD,EAAOmC,SAAW,UAClBnC,EAAOsD,eAAiB,OACxB,SACJ,IAAK,aACDtD,GAASuD,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGvD,GAAS,CAAEmC,SAAU,eAAiBY,EAAMG,QAAQM,QAAO,SAAUC,EAAKhB,GAAO,OAAQc,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGE,GAAMjB,EAAqBC,GAAQ,GAAG,CAAC,IAC9L,SACJ,IAAK,cACDzC,GAASuD,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGvD,GAAS,CAAEmC,SAAU,gBAAkBY,EAAMG,QAAQM,QAAO,SAAUC,EAAKhB,GAAO,OAAQc,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGE,GAAMjB,EAAqBC,GAAQ,GAAG,CAAC,IAC/L,SACJ,IAAK,kBACDzC,EAAOmC,SAAW,WAClB,SAEJ,IAAK,oBACDnC,EAAO0D,gBAAkB,eACzB1D,EAAO2D,YAAc,SACrB,SACJ,IAAK,mBACD3D,EAAO0D,gBAAkB,OACzB1D,EAAO2D,YAAc,QACrB,SACJ,IAAK,uBACD3D,EAAO0D,gBAAkB,OACzB1D,EAAO2D,YAAc,OACrB,SACJ,IAAK,sBACD3D,EAAO0D,gBAAkB,SACzB,SACJ,IAAK,QACD1D,EAAOgD,MAAQY,WAAWb,EAAMG,QAAQ,IACxC,SAEJ,IAAK,gBACD,GAAIH,EAAMG,QAAQ9C,OAAS,EACvB,MAAM,IAAIG,WAAW,4DAEzBwC,EAAMG,QAAQ,GAAGjD,QAAQmB,GAAqB,SAAUK,EAAGC,EAAIC,EAAIkC,EAAIC,EAAIC,GACvE,GAAIrC,EACA1B,EAAOuC,qBAAuBZ,EAAGvB,WAEhC,IAAIyD,GAAMC,EACX,MAAM,IAAIxB,MAAM,sDAEf,GAAIyB,EACL,MAAM,IAAIzB,MAAM,mDACpB,CACA,MAAO,EACX,IACA,SAGR,GAAIjB,EAA4BgB,KAAKU,EAAMb,MACvClC,EAAOuC,qBAAuBQ,EAAMb,KAAK9B,YAG7C,GAAIc,EAAyBmB,KAAKU,EAAMb,MAAxC,CAII,GAAIa,EAAMG,QAAQ9C,OAAS,EACvB,MAAM,IAAIG,WAAW,iEAEzBwC,EAAMb,KAAKjC,QAAQiB,GAA0B,SAAUO,EAAGC,EAAIC,EAAIkC,EAAIC,EAAIC,GAkBtE,MAhBW,MAAPpC,EACA3B,EAAOgE,sBAAwBtC,EAAGtB,OAG7ByD,GAAgB,MAAVA,EAAG,GACd7D,EAAOoD,sBAAwBS,EAAGzD,OAG7B0D,GAAMC,GACX/D,EAAOgE,sBAAwBF,EAAG1D,OAClCJ,EAAOoD,sBAAwBU,EAAG1D,OAAS2D,EAAG3D,SAG9CJ,EAAOgE,sBAAwBtC,EAAGtB,OAClCJ,EAAOoD,sBAAwB1B,EAAGtB,QAE/B,EACX,IACA,IAAIqC,EAAMM,EAAMG,QAAQ,GAEZ,MAART,EACAzC,GAASuD,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGvD,GAAS,CAAEiE,oBAAqB,mBAE1DxB,IACLzC,GAASuD,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGvD,GAASsB,EAA0BmB,IAG1E,MAEA,GAAItB,EAA4BkB,KAAKU,EAAMb,MACvClC,GAASuD,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGvD,GAASsB,EAA0ByB,EAAMb,WAD5E,CAIA,IAAIQ,EAAWZ,EAAUiB,EAAMb,MAC3BQ,IACA1C,GAASuD,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGvD,GAAS0C,IAE5C,IAAIwB,EAAsCjC,EAAyCc,EAAMb,MACrFgC,IACAlE,GAASuD,EAAAA,EAAAA,WAASA,EAAAA,EAAAA,UAAS,CAAC,EAAGvD,GAASkE,GAP5C,CASJ,CACA,OAAOlE,CACX,CCpSO,ICFHmE,EDEOC,EAAW,CAClB,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,GAAM,CACF,IACA,KAEJ,MAAO,CACH,IACA,KAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,QAAS,CACL,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,QAAS,CACL,IACA,IACA,MAEJ,QAAS,CACL,IACA,IACA,MAEJ,QAAS,CACL,IACA,IACA,MAEJ,QAAS,CACL,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,QAAS,CACL,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,QAAS,CACL,IACA,IACA,KACA,MAEJ,QAAS,CACL,IACA,IACA,KACA,MAEJ,QAAS,CACL,IACA,IACA,KACA,MAEJ,QAAS,CACL,IACA,IACA,KACA,MAEJ,QAAS,CACL,IACA,IACA,KACA,MAEJ,QAAS,CACL,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KACA,MAEJ,GAAM,CACF,IACA,IACA,KAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,MAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,SAAU,CACN,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,IACA,MAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,SAAU,CACN,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,GAAM,CACF,IACA,KACA,KACA,KAEJ,QAAS,CACL,IACA,KACA,KACA,KAEJ,GAAM,CACF,KACA,KAEJ,GAAM,CACF,KACA,KAEJ,QAAS,CACL,KACA,IACA,KAEJ,QAAS,CACL,KACA,IACA,KAEJ,QAAS,CACL,KACA,IACA,KAEJ,QAAS,CACL,KACA,IACA,KAEJ,GAAM,CACF,KACA,IACA,IACA,MAEJ,QAAS,CACL,KACA,IACA,KACA,KAEJ,GAAM,CACF,KACA,KACA,IACA,KAEJ,GAAM,CACF,KACA,KACA,IACA,KAEJ,GAAM,CACF,KACA,KACA,IACA,KAEJ,QAAS,CACL,KACA,KACA,IACA,KAEJ,QAAS,CACL,KACA,KACA,IACA,KAEJ,QAAS,CACL,KACA,KACA,IACA,KAEJ,GAAM,CACF,KACA,KACA,IACA,KAEJ,GAAM,CACF,KACA,KACA,IACA,KAEJ,GAAM,CACF,KACA,KACA,IACA,KAEJ,GAAM,CACF,KACA,KACA,IACA,KAEJ,GAAM,CACF,KACA,KACA,IACA,MEzwCR,SAASC,EAA+BC,GACpC,IAAI1D,EAAY0D,EAAO1D,UASvB,QARkB2D,IAAd3D,GAEA0D,EAAOE,YAEPF,EAAOE,WAAWpE,SAElBQ,EAAY0D,EAAOE,WAAW,IAE9B5D,EACA,OAAQA,GACJ,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,IAAK,MACD,MAAO,IACX,QACI,MAAM,IAAI0B,MAAM,qBAI5B,IACImC,EADAC,EAAcJ,EAAOK,SASzB,MAPoB,SAAhBD,IACAD,EAAYH,EAAOM,WAAWC,SAEjBT,EAASK,GAAa,KACnCL,EAASM,GAAe,KACxBN,EAAS,GAAGU,OAAOJ,EAAa,UAChCN,EAAS,QACK,EACtB,CD3EA,IAAIW,EAA8B,IAAIC,OAAO,IAAIF,OAAOlF,EAAsBqF,OAAQ,MAClFC,EAA4B,IAAIF,OAAO,GAAGF,OAAOlF,EAAsBqF,OAAQ,OACnF,SAASE,EAAeC,EAAOC,GAC3B,MAAO,CAAED,MAAOA,EAAOC,IAAKA,EAChC,CAGA,IAAIC,IAAwBC,OAAOC,UAAUC,WACzCC,IAA2BH,OAAOI,cAClCC,IAAyBC,OAAOC,YAChCC,IAAyBR,OAAOC,UAAUQ,YAC1CC,IAAiBV,OAAOC,UAAUU,UAClCC,IAAeZ,OAAOC,UAAUY,QAEhCC,IAD2BC,OAAOD,cAEhCC,OAAOD,cACP,SAAUE,GACR,MAAqB,kBAANA,GACXC,SAASD,IACTE,KAAKC,MAAMH,KAAOA,GAClBE,KAAKE,IAAIJ,IAAM,gBACvB,EAEAK,GAAyB,EAC7B,IAQIA,EAA8F,OAA5C,QAAvBzC,EAPlB0C,GAAG,4CAA6C,MAOtBC,KAAK,YAAyB,IAAP3C,OAAgB,EAASA,EAAG,GAC1F,CACA,MAAO1C,IACHmF,GAAyB,CAC7B,CACA,IAyFIG,EAzFAtB,EAAaH,EAET,SAAoB0B,EAAGC,EAAQC,GAC3B,OAAOF,EAAEvB,WAAWwB,EAAQC,EAChC,EAEA,SAAoBF,EAAGC,EAAQC,GAC3B,OAAOF,EAAE5E,MAAM8E,EAAUA,EAAWD,EAAO7G,UAAY6G,CAC3D,EACJtB,EAAgBD,EACdH,OAAOI,cAEL,WAEI,IADA,IAAIwB,EAAa,GACRtE,EAAK,EAAGA,EAAKuE,UAAUhH,OAAQyC,IACpCsE,EAAWtE,GAAMuE,UAAUvE,GAM/B,IAJA,IAGIwE,EAHAC,EAAW,GACXlH,EAAS+G,EAAW/G,OACpBmH,EAAI,EAEDnH,EAASmH,GAAG,CAEf,IADAF,EAAOF,EAAWI,MACP,QACP,MAAMhH,WAAW8G,EAAO,8BAC5BC,GACID,EAAO,MACD9B,OAAOiC,aAAaH,GACpB9B,OAAOiC,aAAyC,QAA1BH,GAAQ,QAAY,IAAeA,EAAO,KAAS,MACvF,CACA,OAAOC,CACX,EACJxB,EAEJF,EACMC,OAAOC,YAEL,SAAqB2B,GAEjB,IADA,IAAIC,EAAM,CAAC,EACF7E,EAAK,EAAG8E,EAAYF,EAAS5E,EAAK8E,EAAUvH,OAAQyC,IAAM,CAC/D,IAAIsB,EAAKwD,EAAU9E,GAAK+E,EAAIzD,EAAG,GAAI0D,EAAI1D,EAAG,GAC1CuD,EAAIE,GAAKC,CACb,CACA,OAAOH,CACX,EACJ1B,EAAcD,EAEV,SAAqBiB,EAAGc,GACpB,OAAOd,EAAEhB,YAAY8B,EACzB,EAEA,SAAqBd,EAAGc,GACpB,IAAIC,EAAOf,EAAE5G,OACb,KAAI0H,EAAQ,GAAKA,GAASC,GAA1B,CAGA,IACIhH,EADAiH,EAAQhB,EAAEiB,WAAWH,GAEzB,OAAOE,EAAQ,OACXA,EAAQ,OACRF,EAAQ,IAAMC,IACbhH,EAASiG,EAAEiB,WAAWH,EAAQ,IAAM,OACrC/G,EAAS,MACPiH,EAC4BjH,EAAS,OAAnCiH,EAAQ,OAAW,IAA0B,KATrD,CAUJ,EACJ9B,EAAYD,EAER,SAAmBe,GACf,OAAOA,EAAEd,WACb,EAEA,SAAmBc,GACf,OAAOA,EAAE/G,QAAQ8E,EAA6B,GAClD,EACJqB,EAAUD,EAEN,SAAiBa,GACb,OAAOA,EAAEZ,SACb,EAEA,SAAiBY,GACb,OAAOA,EAAE/G,QAAQiF,EAA2B,GAChD,EAER,SAAS2B,GAAGG,EAAGkB,GACX,OAAO,IAAIlD,OAAOgC,EAAGkB,EACzB,CAGA,GAAItB,EAAwB,CAExB,IAAIuB,GAAyBtB,GAAG,4CAA6C,MAC7EE,EAAyB,SAAgCC,EAAGc,GACxD,IAAI3D,EAGJ,OAFAgE,GAAuBC,UAAYN,EAER,QAAnB3D,EADIgE,GAAuBrB,KAAKE,GACrB,UAAuB,IAAP7C,EAAgBA,EAAK,EAC5D,CACJ,MAGI4C,EAAyB,SAAgCC,EAAGc,GAExD,IADA,IAAI5H,EAAQ,KACC,CACT,IAAImI,EAAIrC,EAAYgB,EAAGc,GACvB,QAAUvD,IAAN8D,GAAmBC,GAAcD,IAAME,GAAiBF,GACxD,MAEJnI,EAAMsI,KAAKH,GACXP,GAASO,GAAK,MAAU,EAAI,CAChC,CACA,OAAO1C,EAAc8C,WAAM,EAAQvI,EACvC,EAEJ,IAAIwI,GAAwB,WACxB,SAASA,EAAOC,EAASzF,QACL,IAAZA,IAAsBA,EAAU,CAAC,GACrC0F,KAAKD,QAAUA,EACfC,KAAK1B,SAAW,CAAE2B,OAAQ,EAAGC,KAAM,EAAGC,OAAQ,GAC9CH,KAAKI,YAAc9F,EAAQ8F,UAC3BJ,KAAKtE,OAASpB,EAAQoB,OACtBsE,KAAKK,sBAAwB/F,EAAQ+F,oBACrCL,KAAKM,uBAAyBhG,EAAQgG,oBAC1C,CAqyBA,OApyBAR,EAAOlD,UAAU2D,MAAQ,WACrB,GAAsB,IAAlBP,KAAKC,SACL,MAAMvG,MAAM,gCAEhB,OAAOsG,KAAKQ,aAAa,EAAG,IAAI,EACpC,EACAV,EAAOlD,UAAU4D,aAAe,SAAUC,EAAcC,EAAeC,GAEnE,IADA,IAAIjC,EAAW,IACPsB,KAAKY,SAAS,CAClB,IAAIC,EAAOb,KAAKa,OAChB,GAAa,MAATA,EAAwB,CAExB,IADIzJ,EAAS4I,KAAKc,cAAcL,EAAcE,IACnCI,IACP,OAAO3J,EAEXsH,EAASkB,KAAKxI,EAAO4J,IACzB,KACK,IAAa,MAATH,GAA0BJ,EAAe,EAC9C,MAEC,GAAa,KAATI,GACc,WAAlBH,GAAgD,kBAAlBA,EAQ9B,IAAa,KAATG,IACJb,KAAKI,WACU,KAAhBJ,KAAKiB,OACP,CACE,GAAIN,EACA,MAGA,OAAOX,KAAKkB,MAAMhM,EAAUiM,sBAAuB5E,EAAeyD,KAAKoB,gBAAiBpB,KAAKoB,iBAErG,CACK,GAAa,KAATP,IACJb,KAAKI,WACNiB,GAASrB,KAAKiB,QAAU,GAAI,CAE5B,IADI7J,EAAS4I,KAAKsB,SAASb,EAAcC,IAC9BK,IACP,OAAO3J,EAEXsH,EAASkB,KAAKxI,EAAO4J,IACzB,KACK,CACD,IAAI5J,EACJ,IADIA,EAAS4I,KAAKuB,aAAad,EAAcC,IAClCK,IACP,OAAO3J,EAEXsH,EAASkB,KAAKxI,EAAO4J,IACzB,MAlCuE,CACnE,IAAI1C,EAAW0B,KAAKoB,gBACpBpB,KAAKwB,OACL9C,EAASkB,KAAK,CACVrK,KAAMJ,EAAKmB,MACXmL,SAAUlF,EAAe+B,EAAU0B,KAAKoB,kBAEhD,CA2BA,CACJ,CACA,MAAO,CAAEJ,IAAKtC,EAAUqC,IAAK,KACjC,EAmBAjB,EAAOlD,UAAU0E,SAAW,SAAUb,EAAcC,GAChD,IAAIgB,EAAgB1B,KAAKoB,gBACzBpB,KAAKwB,OACL,IAAIG,EAAU3B,KAAK4B,eAEnB,GADA5B,KAAK6B,YACD7B,KAAK8B,OAAO,MAEZ,MAAO,CACHd,IAAK,CACDzL,KAAMJ,EAAKK,QACXqB,MAAO,IAAIqF,OAAOyF,EAAS,MAC3BF,SAAUlF,EAAemF,EAAe1B,KAAKoB,kBAEjDL,IAAK,MAGR,GAAIf,KAAK8B,OAAO,KAAM,CACvB,IAAIC,EAAiB/B,KAAKQ,aAAaC,EAAe,EAAGC,GAAe,GACxE,GAAIqB,EAAehB,IACf,OAAOgB,EAEX,IAAIC,EAAWD,EAAef,IAE1BiB,EAAsBjC,KAAKoB,gBAC/B,GAAIpB,KAAK8B,OAAO,MAAO,CACnB,GAAI9B,KAAKY,UAAYS,GAASrB,KAAKa,QAC/B,OAAOb,KAAKkB,MAAMhM,EAAUgN,YAAa3F,EAAe0F,EAAqBjC,KAAKoB,kBAEtF,IAAIe,EAA8BnC,KAAKoB,gBAEvC,OAAIO,IADiB3B,KAAK4B,eAEf5B,KAAKkB,MAAMhM,EAAUiM,sBAAuB5E,EAAe4F,EAA6BnC,KAAKoB,mBAExGpB,KAAK6B,YACA7B,KAAK8B,OAAO,KAGV,CACHd,IAAK,CACDzL,KAAMJ,EAAKqB,IACXK,MAAO8K,EACPK,SAAUA,EACVP,SAAUlF,EAAemF,EAAe1B,KAAKoB,kBAEjDL,IAAK,MATEf,KAAKkB,MAAMhM,EAAUgN,YAAa3F,EAAe0F,EAAqBjC,KAAKoB,kBAW1F,CAEI,OAAOpB,KAAKkB,MAAMhM,EAAUkN,aAAc7F,EAAemF,EAAe1B,KAAKoB,iBAErF,CAEI,OAAOpB,KAAKkB,MAAMhM,EAAUgN,YAAa3F,EAAemF,EAAe1B,KAAKoB,iBAEpF,EAIAtB,EAAOlD,UAAUgF,aAAe,WAC5B,IA4qB6BnC,EA5qBzB4C,EAAcrC,KAAKC,SAEvB,IADAD,KAAKwB,QACGxB,KAAKY,UA2qBH,MADmBnB,EA1qBuBO,KAAKa,SA4qBnD,KAANpB,GACCA,GAAK,IAAMA,GAAK,IACX,KAANA,GACCA,GAAK,IAAMA,GAAK,KAChBA,GAAK,IAAMA,GAAK,IACZ,KAALA,GACCA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAAQA,GAAK,KAClBA,GAAK,KAASA,GAAK,MACnBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAWA,GAAK,SA5rBlBO,KAAKwB,OAET,OAAOxB,KAAKD,QAAQvG,MAAM6I,EAAarC,KAAKC,SAChD,EACAH,EAAOlD,UAAU2E,aAAe,SAAUd,EAAcC,GAGpD,IAFA,IAAIlE,EAAQwD,KAAKoB,gBACbvK,EAAQ,KACC,CACT,IAAIyL,EAAmBtC,KAAKuC,cAAc7B,GAC1C,GAAI4B,EACAzL,GAASyL,MADb,CAIA,IAAIE,EAAsBxC,KAAKyC,iBAAiBhC,EAAcC,GAC9D,GAAI8B,EACA3L,GAAS2L,MADb,CAIA,IAAIE,EAAuB1C,KAAK2C,2BAChC,IAAID,EAIJ,MAHI7L,GAAS6L,CAHb,CALA,CAYJ,CACA,IAAIjB,EAAWlF,EAAeC,EAAOwD,KAAKoB,iBAC1C,MAAO,CACHJ,IAAK,CAAEzL,KAAMJ,EAAKK,QAASqB,MAAOA,EAAO4K,SAAUA,GACnDV,IAAK,KAEb,EACAjB,EAAOlD,UAAU+F,yBAA2B,WACxC,OAAK3C,KAAKY,SACU,KAAhBZ,KAAKa,SACJb,KAAKI,YAooBPiB,GADcuB,EAjoBQ5C,KAAKiB,QAAU,IAkoBA,KAAd2B,GA9nBnB,MAHH5C,KAAKwB,OACE,KA+nBnB,IAAyBoB,CA5nBrB,EAMA9C,EAAOlD,UAAU2F,cAAgB,SAAU7B,GACvC,GAAIV,KAAKY,SAA2B,KAAhBZ,KAAKa,OACrB,OAAO,KAIX,OAAQb,KAAKiB,QACT,KAAK,GAID,OAFAjB,KAAKwB,OACLxB,KAAKwB,OACE,IAEX,KAAK,IACL,KAAK,GACL,KAAK,GACL,KAAK,IACD,MACJ,KAAK,GACD,GAAsB,WAAlBd,GAAgD,kBAAlBA,EAC9B,MAEJ,OAAO,KACX,QACI,OAAO,KAEfV,KAAKwB,OACL,IAAIjD,EAAa,CAACyB,KAAKa,QAGvB,IAFAb,KAAKwB,QAEGxB,KAAKY,SAAS,CAClB,IAAIiC,EAAK7C,KAAKa,OACd,GAAW,KAAPgC,EAAqB,CACrB,GAAoB,KAAhB7C,KAAKiB,OAKJ,CAEDjB,KAAKwB,OACL,KACJ,CARIjD,EAAWqB,KAAK,IAEhBI,KAAKwB,MAOb,MAEIjD,EAAWqB,KAAKiD,GAEpB7C,KAAKwB,MACT,CACA,OAAOzE,EAAc8C,WAAM,EAAQtB,EACvC,EACAuB,EAAOlD,UAAU6F,iBAAmB,SAAUhC,EAAcC,GACxD,GAAIV,KAAKY,QACL,OAAO,KAEX,IAAIiC,EAAK7C,KAAKa,OACd,OAAW,KAAPgC,GACO,MAAPA,GACQ,KAAPA,IACsB,WAAlBnC,GAAgD,kBAAlBA,IAC3B,MAAPmC,GAAwBpC,EAAe,EACjC,MAGPT,KAAKwB,OACEzE,EAAc8F,GAE7B,EACA/C,EAAOlD,UAAUkE,cAAgB,SAAUL,EAAcE,GACrD,IAAImC,EAAuB9C,KAAKoB,gBAGhC,GAFApB,KAAKwB,OACLxB,KAAK6B,YACD7B,KAAKY,QACL,OAAOZ,KAAKkB,MAAMhM,EAAU6N,8BAA+BxG,EAAeuG,EAAsB9C,KAAKoB,kBAEzG,GAAoB,MAAhBpB,KAAKa,OAEL,OADAb,KAAKwB,OACExB,KAAKkB,MAAMhM,EAAU8N,eAAgBzG,EAAeuG,EAAsB9C,KAAKoB,kBAG1F,IAAIvK,EAAQmJ,KAAKiD,4BAA4BpM,MAC7C,IAAKA,EACD,OAAOmJ,KAAKkB,MAAMhM,EAAUgO,mBAAoB3G,EAAeuG,EAAsB9C,KAAKoB,kBAG9F,GADApB,KAAK6B,YACD7B,KAAKY,QACL,OAAOZ,KAAKkB,MAAMhM,EAAU6N,8BAA+BxG,EAAeuG,EAAsB9C,KAAKoB,kBAEzG,OAAQpB,KAAKa,QAET,KAAK,IAED,OADAb,KAAKwB,OACE,CACHR,IAAK,CACDzL,KAAMJ,EAAKO,SAEXmB,MAAOA,EACP4K,SAAUlF,EAAeuG,EAAsB9C,KAAKoB,kBAExDL,IAAK,MAIb,KAAK,GAGD,OAFAf,KAAKwB,OACLxB,KAAK6B,YACD7B,KAAKY,QACEZ,KAAKkB,MAAMhM,EAAU6N,8BAA+BxG,EAAeuG,EAAsB9C,KAAKoB,kBAElGpB,KAAKmD,qBAAqB1C,EAAcE,EAAmB9J,EAAOiM,GAE7E,QACI,OAAO9C,KAAKkB,MAAMhM,EAAUgO,mBAAoB3G,EAAeuG,EAAsB9C,KAAKoB,kBAEtG,EAKAtB,EAAOlD,UAAUqG,0BAA4B,WACzC,IAAIG,EAAmBpD,KAAKoB,gBACxBiB,EAAcrC,KAAKC,SACnBpJ,EAAQsH,EAAuB6B,KAAKD,QAASsC,GAC7CgB,EAAYhB,EAAcxL,EAAMW,OAIpC,OAHAwI,KAAKsD,OAAOD,GAGL,CAAExM,MAAOA,EAAO4K,SADRlF,EAAe6G,EADZpD,KAAKoB,iBAG3B,EACAtB,EAAOlD,UAAUuG,qBAAuB,SAAU1C,EAAcE,EAAmB9J,EAAOiM,GACtF,IAAIvH,EAIAgI,EAAoBvD,KAAKoB,gBACzBoC,EAAUxD,KAAKiD,4BAA4BpM,MAC3C4M,EAAkBzD,KAAKoB,gBAC3B,OAAQoC,GACJ,IAAK,GAED,OAAOxD,KAAKkB,MAAMhM,EAAUwO,qBAAsBnH,EAAegH,EAAmBE,IACxF,IAAK,SACL,IAAK,OACL,IAAK,OAIDzD,KAAK6B,YACL,IAAI8B,EAAmB,KACvB,GAAI3D,KAAK8B,OAAO,KAAM,CAClB9B,KAAK6B,YACL,IAAI+B,EAAqB5D,KAAKoB,gBAE9B,IADIhK,EAAS4I,KAAK6D,iCACP9C,IACP,OAAO3J,EAGX,GAAqB,KADjBL,EAAQyG,EAAQpG,EAAO4J,MACjBxJ,OACN,OAAOwI,KAAKkB,MAAMhM,EAAU4O,sBAAuBvH,EAAeyD,KAAKoB,gBAAiBpB,KAAKoB,kBAGjGuC,EAAmB,CAAE5M,MAAOA,EAAOgN,cADfxH,EAAeqH,EAAoB5D,KAAKoB,iBAEhE,CAEA,IADI4C,EAAiBhE,KAAKiE,sBAAsBnB,IAC7B/B,IACf,OAAOiD,EAEX,IAAIE,EAAa3H,EAAeuG,EAAsB9C,KAAKoB,iBAE3D,GAAIuC,GAAoB9G,EAAgC,OAArB8G,QAAkD,IAArBA,OAA8B,EAASA,EAAiB5M,MAAO,KAAM,GAAI,CAErI,IAAII,EAAWmG,EAAUqG,EAAiB5M,MAAMyC,MAAM,IACtD,GAAgB,WAAZgK,EAEA,OADIpM,EAAS4I,KAAKmE,8BAA8BhN,EAAUwM,EAAiBI,gBAChEhD,IACA3J,EAEJ,CACH4J,IAAK,CAAEzL,KAAMJ,EAAKS,OAAQiB,MAAOA,EAAO4K,SAAUyC,EAAYnN,MAAOK,EAAO4J,KAC5ED,IAAK,MAIT,GAAwB,IAApB5J,EAASK,OACT,OAAOwI,KAAKkB,MAAMhM,EAAUkP,0BAA2BF,GAE3D,IAAIG,EAAkBlN,EAIlB6I,KAAKtE,SACL2I,EC1hBrB,SAAwBlN,EAAUuE,GAErC,IADA,IAAI4I,EAAe,GACVC,EAAa,EAAGA,EAAapN,EAASK,OAAQ+M,IAAc,CACjE,IAAIC,EAAcrN,EAASsN,OAAOF,GAClC,GAAoB,MAAhBC,EAAqB,CAErB,IADA,IAAIE,EAAc,EACXH,EAAa,EAAIpN,EAASK,QAC7BL,EAASsN,OAAOF,EAAa,KAAOC,GACpCE,IACAH,IAEJ,IAAII,EAAU,GAAmB,EAAdD,GACfE,EAAeF,EAAc,EAAI,EAAI,GAAKA,GAAe,GAEzDG,EAAWpJ,EAA+BC,GAI9C,IAHgB,KAAZmJ,GAA+B,KAAZA,IACnBD,EAAe,GAEZA,KAAiB,GACpBN,GANgB,IAQpB,KAAOK,KAAY,GACfL,EAAeO,EAAWP,CAElC,MAEIA,GADqB,MAAhBE,EACW,IAGAA,CAExB,CACA,OAAOF,CACX,CDyf8CQ,CAAe3N,EAAU6I,KAAKtE,SAEpD,IAAI3E,EAAQ,CACRxB,KAAMH,EAAcuB,SACpBoO,QAASV,EACT5C,SAAUkC,EAAiBI,cAC3BiB,cAAehF,KAAKM,qBACdpJ,EAAsBmN,GACtB,CAAC,GAGX,MAAO,CACHrD,IAAK,CAAEzL,KAFY,SAAZiO,EAAqBrO,EAAKW,KAAOX,EAAKa,KAE1Ba,MAAOA,EAAO4K,SAAUyC,EAAYnN,MAAOA,GAC9DgK,IAAK,KAGjB,CAEA,MAAO,CACHC,IAAK,CACDzL,KAAkB,WAAZiO,EACArO,EAAKS,OACO,SAAZ4N,EACIrO,EAAKW,KACLX,EAAKa,KACfa,MAAOA,EACP4K,SAAUyC,EACVnN,MAA6G,QAArGwE,EAA0B,OAArBoI,QAAkD,IAArBA,OAA8B,EAASA,EAAiB5M,aAA0B,IAAPwE,EAAgBA,EAAK,MAE9IwF,IAAK,MAGb,IAAK,SACL,IAAK,gBACL,IAAK,SAID,IAAIkE,EAAoBjF,KAAKoB,gBAE7B,GADApB,KAAK6B,aACA7B,KAAK8B,OAAO,KACb,OAAO9B,KAAKkB,MAAMhM,EAAUgQ,+BAAgC3I,EAAe0I,GAAmBtK,EAAAA,EAAAA,UAAS,CAAC,EAAGsK,KAE/GjF,KAAK6B,YASL,IAAIsD,EAAwBnF,KAAKiD,4BAC7BmC,EAAe,EACnB,GAAgB,WAAZ5B,GAAwD,WAAhC2B,EAAsBtO,MAAoB,CAClE,IAAKmJ,KAAK8B,OAAO,KACb,OAAO9B,KAAKkB,MAAMhM,EAAUmQ,oCAAqC9I,EAAeyD,KAAKoB,gBAAiBpB,KAAKoB,kBAG/G,IAAIhK,EACJ,GAFA4I,KAAK6B,aACDzK,EAAS4I,KAAKsF,uBAAuBpQ,EAAUmQ,oCAAqCnQ,EAAUqQ,uCACvFxE,IACP,OAAO3J,EAGX4I,KAAK6B,YACLsD,EAAwBnF,KAAKiD,4BAC7BmC,EAAehO,EAAO4J,GAC1B,CACA,IAIIgD,EAJAwB,EAAgBxF,KAAKyF,8BAA8BhF,EAAc+C,EAAS7C,EAAmBwE,GACjG,GAAIK,EAAczE,IACd,OAAOyE,EAGX,IADIxB,EAAiBhE,KAAKiE,sBAAsBnB,IAC7B/B,IACf,OAAOiD,EAEX,IAAI0B,EAAanJ,EAAeuG,EAAsB9C,KAAKoB,iBAC3D,MAAgB,WAAZoC,EACO,CACHxC,IAAK,CACDzL,KAAMJ,EAAKe,OACXW,MAAOA,EACPyD,QAAS4C,EAAYsI,EAAcxE,KACnCS,SAAUiE,GAEd3E,IAAK,MAIF,CACHC,IAAK,CACDzL,KAAMJ,EAAKiB,OACXS,MAAOA,EACPyD,QAAS4C,EAAYsI,EAAcxE,KACnCf,OAAQmF,EACRO,WAAwB,WAAZnC,EAAuB,WAAa,UAChD/B,SAAUiE,GAEd3E,IAAK,MAIjB,QACI,OAAOf,KAAKkB,MAAMhM,EAAU0Q,sBAAuBrJ,EAAegH,EAAmBE,IAEjG,EACA3D,EAAOlD,UAAUqH,sBAAwB,SAAUnB,GAG/C,OAAI9C,KAAKY,SAA2B,MAAhBZ,KAAKa,OACdb,KAAKkB,MAAMhM,EAAU6N,8BAA+BxG,EAAeuG,EAAsB9C,KAAKoB,mBAEzGpB,KAAKwB,OACE,CAAER,KAAK,EAAMD,IAAK,MAC7B,EAIAjB,EAAOlD,UAAUiH,8BAAgC,WAG7C,IAFA,IAAIgC,EAAe,EACfnE,EAAgB1B,KAAKoB,iBACjBpB,KAAKY,SAAS,CAElB,OADSZ,KAAKa,QAEV,KAAK,GAGDb,KAAKwB,OACL,IAAIsE,EAAqB9F,KAAKoB,gBAC9B,IAAKpB,KAAK+F,UAAU,KAChB,OAAO/F,KAAKkB,MAAMhM,EAAU8Q,iCAAkCzJ,EAAeuJ,EAAoB9F,KAAKoB,kBAE1GpB,KAAKwB,OACL,MAEJ,KAAK,IACDqE,GAAgB,EAChB7F,KAAKwB,OACL,MAEJ,KAAK,IACD,KAAIqE,EAAe,GAIf,MAAO,CACH7E,IAAKhB,KAAKD,QAAQvG,MAAMkI,EAAczB,OAAQD,KAAKC,UACnDc,IAAK,MALT8E,GAAgB,EAQpB,MAEJ,QACI7F,KAAKwB,OAGjB,CACA,MAAO,CACHR,IAAKhB,KAAKD,QAAQvG,MAAMkI,EAAczB,OAAQD,KAAKC,UACnDc,IAAK,KAEb,EACAjB,EAAOlD,UAAUuH,8BAAgC,SAAUhN,EAAUsK,GACjE,IAAIzH,EAAS,GACb,IACIA,EFrsBL,SAAuC7C,GAC1C,GAAwB,IAApBA,EAASK,OACT,MAAM,IAAIkC,MAAM,mCAOpB,IAJA,IAGIM,EAAS,GACJC,EAAK,EAAGgM,EAJE9O,EACd+O,MAAM7N,GACN8N,QAAO,SAAUC,GAAK,OAAOA,EAAE5O,OAAS,CAAG,IAEAyC,EAAKgM,EAAezO,OAAQyC,IAAM,CAC9E,IACIoM,EADcJ,EAAehM,GACAiM,MAAM,KACvC,GAA8B,IAA1BG,EAAe7O,OACf,MAAM,IAAIkC,MAAM,2BAGpB,IADA,IAAIJ,EAAO+M,EAAe,GAAI/L,EAAU+L,EAAe7M,MAAM,GACpD+B,EAAK,EAAG+K,EAAYhM,EAASiB,EAAK+K,EAAU9O,OAAQ+D,IAEzD,GAAsB,IADT+K,EAAU/K,GACZ/D,OACP,MAAM,IAAIkC,MAAM,2BAGxBM,EAAO4F,KAAK,CAAEtG,KAAMA,EAAMgB,QAASA,GACvC,CACA,OAAON,CACX,CE4qBqBmK,CAA8BhN,EAC3C,CACA,MAAOoP,GACH,OAAOvG,KAAKkB,MAAMhM,EAAUsR,wBAAyB/E,EACzD,CACA,MAAO,CACHT,IAAK,CACDzL,KAAMH,EAAcQ,OACpBoE,OAAQA,EACRyH,SAAUA,EACVuD,cAAehF,KAAKM,qBACdvG,EAAoBC,GACpB,CAAC,GAEX+G,IAAK,KAEb,EAWAjB,EAAOlD,UAAU6I,8BAAgC,SAAUhF,EAAcC,EAAe+F,EAAgBC,GASpG,IARA,IAAInL,EACAoL,GAAiB,EACjBrM,EAAU,GACVsM,EAAkB,IAAIC,IACtBC,EAAWJ,EAAsB7P,MAAOkQ,EAAmBL,EAAsBjF,WAIxE,CACT,GAAwB,IAApBqF,EAAStP,OAAc,CACvB,IAAIkK,EAAgB1B,KAAKoB,gBACzB,GAAsB,WAAlBV,IAA8BV,KAAK8B,OAAO,KAU1C,MARA,IAAI1K,EAAS4I,KAAKsF,uBAAuBpQ,EAAU8R,gCAAiC9R,EAAU+R,kCAC9F,GAAI7P,EAAO2J,IACP,OAAO3J,EAEX2P,EAAmBxK,EAAemF,EAAe1B,KAAKoB,iBACtD0F,EAAW9G,KAAKD,QAAQvG,MAAMkI,EAAczB,OAAQD,KAAKC,SAKjE,CAEA,GAAI2G,EAAgBM,IAAIJ,GACpB,OAAO9G,KAAKkB,MAAwB,WAAlBR,EACZxL,EAAUiS,mCACVjS,EAAUkS,mCAAoCL,GAEvC,UAAbD,IACAH,GAAiB,GAKrB3G,KAAK6B,YACL,IAAIiB,EAAuB9C,KAAKoB,gBAChC,IAAKpB,KAAK8B,OAAO,KACb,OAAO9B,KAAKkB,MAAwB,WAAlBR,EACZxL,EAAUmS,yCACVnS,EAAUoS,yCAA0C/K,EAAeyD,KAAKoB,gBAAiBpB,KAAKoB,kBAExG,IAAImG,EAAiBvH,KAAKQ,aAAaC,EAAe,EAAGC,EAAe+F,GACxE,GAAIc,EAAexG,IACf,OAAOwG,EAEX,IAAIvD,EAAiBhE,KAAKiE,sBAAsBnB,GAChD,GAAIkB,EAAejD,IACf,OAAOiD,EAEX1J,EAAQsF,KAAK,CACTkH,EACA,CACIjQ,MAAO0Q,EAAevG,IACtBS,SAAUlF,EAAeuG,EAAsB9C,KAAKoB,oBAI5DwF,EAAgBY,IAAIV,GAEpB9G,KAAK6B,YACmCiF,GAAvCvL,EAAKyE,KAAKiD,6BAA2CpM,MAAOkQ,EAAmBxL,EAAGkG,QACvF,CACA,OAAuB,IAAnBnH,EAAQ9C,OACDwI,KAAKkB,MAAwB,WAAlBR,EACZxL,EAAUuS,gCACVvS,EAAU8R,gCAAiCzK,EAAeyD,KAAKoB,gBAAiBpB,KAAKoB,kBAE3FpB,KAAKK,sBAAwBsG,EACtB3G,KAAKkB,MAAMhM,EAAUwS,qBAAsBnL,EAAeyD,KAAKoB,gBAAiBpB,KAAKoB,kBAEzF,CAAEJ,IAAK1G,EAASyG,IAAK,KAChC,EACAjB,EAAOlD,UAAU0I,uBAAyB,SAAUqC,EAAmBC,GACnE,IAAIC,EAAO,EACPzE,EAAmBpD,KAAKoB,gBACxBpB,KAAK8B,OAAO,MAEP9B,KAAK8B,OAAO,OACjB+F,GAAQ,GAIZ,IAFA,IAAIC,GAAY,EACZC,EAAU,GACN/H,KAAKY,SAAS,CAClB,IAAIiC,EAAK7C,KAAKa,OACd,KAAIgC,GAAM,IAAgBA,GAAM,IAM5B,MALAiF,GAAY,EACZC,EAAoB,GAAVA,GAAgBlF,EAAK,IAC/B7C,KAAKwB,MAKb,CACA,IAAIC,EAAWlF,EAAe6G,EAAkBpD,KAAKoB,iBACrD,OAAK0G,EAIArK,EADLsK,GAAWF,GAIJ,CAAE7G,IAAK+G,EAAShH,IAAK,MAFjBf,KAAKkB,MAAM0G,EAAoBnG,GAJ/BzB,KAAKkB,MAAMyG,EAAmBlG,EAO7C,EACA3B,EAAOlD,UAAUqD,OAAS,WACtB,OAAOD,KAAK1B,SAAS2B,MACzB,EACAH,EAAOlD,UAAUgE,MAAQ,WACrB,OAAOZ,KAAKC,WAAaD,KAAKD,QAAQvI,MAC1C,EACAsI,EAAOlD,UAAUwE,cAAgB,WAE7B,MAAO,CACHnB,OAAQD,KAAK1B,SAAS2B,OACtBC,KAAMF,KAAK1B,SAAS4B,KACpBC,OAAQH,KAAK1B,SAAS6B,OAE9B,EAKAL,EAAOlD,UAAUiE,KAAO,WACpB,IAAIZ,EAASD,KAAK1B,SAAS2B,OAC3B,GAAIA,GAAUD,KAAKD,QAAQvI,OACvB,MAAMkC,MAAM,gBAEhB,IAAI+E,EAAOrB,EAAY4C,KAAKD,QAASE,GACrC,QAAatE,IAAT8C,EACA,MAAM/E,MAAM,UAAUwC,OAAO+D,EAAQ,6CAEzC,OAAOxB,CACX,EACAqB,EAAOlD,UAAUsE,MAAQ,SAAU8G,EAAMvG,GACrC,MAAO,CACHT,IAAK,KACLD,IAAK,CACDiH,KAAMA,EACNjI,QAASC,KAAKD,QACd0B,SAAUA,GAGtB,EAEA3B,EAAOlD,UAAU4E,KAAO,WACpB,IAAIxB,KAAKY,QAAT,CAGA,IAAInC,EAAOuB,KAAKa,OACH,KAATpC,GACAuB,KAAK1B,SAAS4B,MAAQ,EACtBF,KAAK1B,SAAS6B,OAAS,EACvBH,KAAK1B,SAAS2B,QAAU,IAGxBD,KAAK1B,SAAS6B,QAAU,EAExBH,KAAK1B,SAAS2B,QAAUxB,EAAO,MAAU,EAAI,EAVjD,CAYJ,EAOAqB,EAAOlD,UAAUkF,OAAS,SAAUmG,GAChC,GAAIpL,EAAWmD,KAAKD,QAASkI,EAAQjI,KAAKC,UAAW,CACjD,IAAK,IAAItB,EAAI,EAAGA,EAAIsJ,EAAOzQ,OAAQmH,IAC/BqB,KAAKwB,OAET,OAAO,CACX,CACA,OAAO,CACX,EAKA1B,EAAOlD,UAAUmJ,UAAY,SAAUhB,GACnC,IAAImD,EAAgBlI,KAAKC,SACrBf,EAAQc,KAAKD,QAAQoI,QAAQpD,EAASmD,GAC1C,OAAIhJ,GAAS,GACTc,KAAKsD,OAAOpE,IACL,IAGPc,KAAKsD,OAAOtD,KAAKD,QAAQvI,SAClB,EAEf,EAKAsI,EAAOlD,UAAU0G,OAAS,SAAU8E,GAChC,GAAIpI,KAAKC,SAAWmI,EAChB,MAAM1O,MAAM,gBAAgBwC,OAAOkM,EAAc,yDAAyDlM,OAAO8D,KAAKC,WAG1H,IADAmI,EAAevK,KAAKwK,IAAID,EAAcpI,KAAKD,QAAQvI,UACtC,CACT,IAAIyI,EAASD,KAAKC,SAClB,GAAIA,IAAWmI,EACX,MAEJ,GAAInI,EAASmI,EACT,MAAM1O,MAAM,gBAAgBwC,OAAOkM,EAAc,6CAGrD,GADApI,KAAKwB,OACDxB,KAAKY,QACL,KAER,CACJ,EAEAd,EAAOlD,UAAUiF,UAAY,WACzB,MAAQ7B,KAAKY,SAAWlB,GAAcM,KAAKa,SACvCb,KAAKwB,MAEb,EAKA1B,EAAOlD,UAAUqE,KAAO,WACpB,GAAIjB,KAAKY,QACL,OAAO,KAEX,IAAInC,EAAOuB,KAAKa,OACZZ,EAASD,KAAKC,SACdqI,EAAWtI,KAAKD,QAAQV,WAAWY,GAAUxB,GAAQ,MAAU,EAAI,IACvE,OAAoB,OAAb6J,QAAkC,IAAbA,EAAsBA,EAAW,IACjE,EACOxI,CACX,CA/yB4B,GAszB5B,SAASuB,GAASuB,GACd,OAASA,GAAa,IAAMA,GAAa,KACpCA,GAAa,IAAMA,GAAa,EACzC,CA8BA,SAASlD,GAAcD,GACnB,OAASA,GAAK,GAAUA,GAAK,IACnB,KAANA,GACM,MAANA,GACCA,GAAK,MAAUA,GAAK,MACf,OAANA,GACM,OAANA,CACR,CAKA,SAASE,GAAiBF,GACtB,OAASA,GAAK,IAAUA,GAAK,IACnB,KAANA,GACCA,GAAK,IAAUA,GAAK,IACf,KAANA,GACM,KAANA,GACM,KAANA,GACM,KAANA,GACM,KAANA,GACM,KAANA,GACCA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACpBA,GAAK,IAAUA,GAAK,IACf,KAANA,GACM,KAANA,GACM,KAANA,GACM,KAANA,GACM,KAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACCA,GAAK,KAAUA,GAAK,KACf,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACM,MAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACf,OAANA,GACM,OAANA,GACM,OAANA,GACCA,GAAK,MAAUA,GAAK,MACf,OAANA,GACM,OAANA,GACM,OAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACf,OAANA,GACM,OAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACf,OAANA,GACM,OAANA,GACM,OAANA,GACCA,GAAK,MAAUA,GAAK,MACf,OAANA,GACM,OAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACf,OAANA,GACCA,GAAK,MAAUA,GAAK,MACf,OAANA,GACCA,GAAK,MAAUA,GAAK,MACf,OAANA,GACCA,GAAK,MAAUA,GAAK,MACf,OAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACf,OAANA,GACM,OAANA,GACM,OAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACf,OAANA,GACM,OAANA,GACM,OAANA,GACM,OAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,KACf,OAANA,GACM,OAANA,GACCA,GAAK,MAAUA,GAAK,MACf,OAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACf,OAANA,GACCA,GAAK,MAAUA,GAAK,MACf,OAANA,GACCA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACpBA,GAAK,MAAUA,GAAK,MACf,OAANA,GACCA,GAAK,MAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACf,QAANA,GACCA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACCA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACf,QAANA,GACCA,GAAK,OAAUA,GAAK,OACpBA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,OACf,QAANA,GACM,QAANA,GACM,QAANA,GACM,QAANA,GACCA,GAAK,OAAUA,GAAK,KAC7B,CEvvCA,SAAS8I,GAAcC,GACnBA,EAAIC,SAAQ,SAAUnT,GAElB,UADOA,EAAGmM,SACNxL,EAAgBX,IAAOa,EAAgBb,GACvC,IAAK,IAAI0J,KAAK1J,EAAGgF,eACNhF,EAAGgF,QAAQ0E,GAAGyC,SACrB8G,GAAcjT,EAAGgF,QAAQ0E,GAAGnI,YAG3BlB,EAAgBL,IAAOmB,EAAiBnB,EAAGyB,SAG1ClB,EAAcP,IAAOS,EAAcT,KACzCoB,EAAmBpB,EAAGyB,cAHfzB,EAAGyB,MAAM0K,SAMXlL,EAAajB,IAClBiT,GAAcjT,EAAG0M,SAEzB,GACJ,CACO,SAASzB,GAAMR,EAAS2I,QACd,IAATA,IAAmBA,EAAO,CAAC,GAC/BA,GAAO/N,EAAAA,EAAAA,UAAS,CAAE2F,sBAAsB,EAAMD,qBAAqB,GAAQqI,GAC3E,IAAItR,EAAS,IAAI0I,GAAOC,EAAS2I,GAAMnI,QACvC,GAAInJ,EAAO2J,IAAK,CACZ,IAAIG,EAAQyH,YAAYzT,EAAUkC,EAAO2J,IAAIiH,OAK7C,MAHA9G,EAAMO,SAAWrK,EAAO2J,IAAIU,SAE5BP,EAAM0H,gBAAkBxR,EAAO2J,IAAIhB,QAC7BmB,CACV,CAIA,OAHe,OAATwH,QAA0B,IAATA,OAAkB,EAASA,EAAKG,kBACnDN,GAAcnR,EAAO4J,KAElB5J,EAAO4J,GAClB,C,wBCvCuB8H,EAAQC,kBAAoF,EACnH,IAAIC,EAAUC,EAAQ,OAClBC,EAA6BD,EAAQ,OAwCzC,IAAIE,EAAQ,uDACRC,EAAiB,mTA4BrBN,EAAQC,aA3BR,SAASA,EAAaM,GAClB,IAAIC,EAAqB,kBAARD,GAAmB,EAAIH,EAA2B3I,OAAO8I,GAAOA,EAwBjF,OAvBAC,EAAIb,SAAQ,SAAUnT,GAClB,IAAI,EAAI4T,EAA2B7T,kBAAkBC,GACjDA,EAAGuB,MAAQvB,EAAGuB,MACTqP,MAAM,IACNqD,KAAI,SAAU9J,GACf,IAAId,EAAIwK,EAAMhB,QAAQ1I,GACtB,OAAId,EAAI,EACGc,EAEJ2J,EAAezK,EAC1B,IACK6K,KAAK,SAET,IAAI,EAAIN,EAA2B/S,iBAAiBb,KAAO,EAAI4T,EAA2BjT,iBAAiBX,GAC5G,IAAK,IAAI2E,EAAK,EAAGsB,EAAK0B,OAAOwM,OAAOnU,EAAGgF,SAAUL,EAAKsB,EAAG/D,OAAQyC,IAAM,CAEnE8O,EADUxN,EAAGtB,GACIpD,MACrB,MAEK,EAAIqS,EAA2B3S,cAAcjB,IAClDyT,EAAazT,EAAG0M,SAExB,IACOsH,CACX,C", "sources": ["../node_modules/@formatjs/cli/node_modules/@formatjs/icu-messageformat-parser/lib/error.js", "../node_modules/@formatjs/cli/node_modules/@formatjs/icu-messageformat-parser/lib/types.js", "../node_modules/@formatjs/cli/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js", "../node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js", "../node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js", "../node_modules/@formatjs/icu-skeleton-parser/lib/number.js", "../node_modules/@formatjs/cli/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js", "../node_modules/@formatjs/cli/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js", "../node_modules/@formatjs/cli/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js", "../node_modules/@formatjs/cli/node_modules/@formatjs/icu-messageformat-parser/lib/index.js", "../node_modules/@formatjs/cli/src/pseudo_locale.js"], "sourcesContent": ["export var ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n", "export var TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nexport var SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nexport function isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nexport function isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nexport function isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nexport function isDateElement(el) {\n    return el.type === TYPE.date;\n}\nexport function isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nexport function isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nexport function isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nexport function isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nexport function isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nexport function isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nexport function isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nexport function createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nexport function createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n", "// @generated from regex-gen.ts\nexport var SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n", "/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nexport function parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'short' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: miliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n", "// @generated from regex-gen.ts\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n", "import { __assign } from \"tslib\";\nimport { WHITE_SPACE_REGEX } from './regex.generated';\nexport function parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nexport function parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = __assign(__assign(__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = __assign(__assign(__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = __assign(__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = __assign(__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = __assign(__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = __assign(__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = __assign(__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n", "// @generated from time-data-gen.ts\n// prettier-ignore  \nexport var timeData = {\n    \"AX\": [\n        \"H\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CL\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-BO\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-EC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-PE\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GT\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HN\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MX\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NI\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PY\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SV\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UY\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"h\",\n        \"K\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BO\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ]\n};\n", "var _a;\nimport { __assign } from \"tslib\";\nimport { <PERSON>rrorKind } from './error';\nimport { SKELETON_TYPE, TYPE, } from './types';\nimport { SPACE_SEPARATOR_REGEX } from './regex.generated';\nimport { parseNumberSkeleton, parseNumberSkeletonFromString, parseDateTimeSkeleton, } from '@formatjs/icu-skeleton-parser';\nimport { getBestPattern } from './date-time-pattern-generator';\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith;\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = getBestPattern(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? parseDateTimeSkeleton(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? TYPE.date : TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? TYPE.number\n                            : argType === 'date'\n                                ? TYPE.date\n                                : TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, __assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = parseNumberSkeletonFromString(skeleton);\n        }\n        catch (e) {\n            return this.error(ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? parseNumberSkeleton(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexport { Parser };\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n", "import { timeData } from './time-data.generated';\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nexport function getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = timeData[regionTag || ''] ||\n        timeData[languageTag || ''] ||\n        timeData[\"\".concat(languageTag, \"-001\")] ||\n        timeData['001'];\n    return hourCycles[0];\n}\n", "import { __assign } from \"tslib\";\nimport { ErrorKind } from './error';\nimport { Parser } from './parser';\nimport { isDateElement, isDateTimeSkeleton, isNumberElement, isNumberSkeleton, isPluralElement, isSelectElement, isTagElement, isTimeElement, } from './types';\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if (isSelectElement(el) || isPluralElement(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if (isNumberElement(el) && isNumberSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if ((isDateElement(el) || isTimeElement(el)) &&\n            isDateTimeSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if (isTagElement(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nexport function parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = __assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\nexport * from './types';\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.generateENXB = exports.generateENXA = exports.generateXXHA = exports.generateXXAC = exports.generateXXLS = void 0;\nvar tslib_1 = require(\"tslib\");\nvar icu_messageformat_parser_1 = require(\"@formatjs/icu-messageformat-parser\");\nfunction generateXXLS(msg) {\n    var ast = typeof msg === 'string' ? (0, icu_messageformat_parser_1.parse)(msg) : msg;\n    var lastChunk = ast.pop();\n    if (lastChunk && (0, icu_messageformat_parser_1.isLiteralElement)(lastChunk)) {\n        lastChunk.value += 'SSSSSSSSSSSSSSSSSSSSSSSSS';\n        return (0, tslib_1.__spreadArray)((0, tslib_1.__spreadArray)([], ast, true), [lastChunk], false);\n    }\n    return (0, tslib_1.__spreadArray)((0, tslib_1.__spreadArray)([], ast, true), [{ type: icu_messageformat_parser_1.TYPE.literal, value: 'SSSSSSSSSSSSSSSSSSSSSSSSS' }], false);\n}\nexports.generateXXLS = generateXXLS;\nfunction generateXXAC(msg) {\n    var ast = typeof msg === 'string' ? (0, icu_messageformat_parser_1.parse)(msg) : msg;\n    ast.forEach(function (el) {\n        if ((0, icu_messageformat_parser_1.isLiteralElement)(el)) {\n            el.value = el.value.toUpperCase();\n        }\n        else if ((0, icu_messageformat_parser_1.isPluralElement)(el) || (0, icu_messageformat_parser_1.isSelectElement)(el)) {\n            for (var _i = 0, _a = Object.values(el.options); _i < _a.length; _i++) {\n                var opt = _a[_i];\n                generateXXAC(opt.value);\n            }\n        }\n        else if ((0, icu_messageformat_parser_1.isTagElement)(el)) {\n            generateXXAC(el.children);\n        }\n    });\n    return ast;\n}\nexports.generateXXAC = generateXXAC;\nfunction generateXXHA(msg) {\n    var ast = typeof msg === 'string' ? (0, icu_messageformat_parser_1.parse)(msg) : msg;\n    var firstChunk = ast.shift();\n    if (firstChunk && (0, icu_messageformat_parser_1.isLiteralElement)(firstChunk)) {\n        firstChunk.value = '[javascript]' + firstChunk.value;\n        return (0, tslib_1.__spreadArray)([firstChunk], ast, true);\n    }\n    return (0, tslib_1.__spreadArray)([{ type: icu_messageformat_parser_1.TYPE.literal, value: '[javascript]' }], ast, true);\n}\nexports.generateXXHA = generateXXHA;\nvar ASCII = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\nvar ACCENTED_ASCII = 'âḃćḋèḟĝḫíĵǩĺṁńŏṗɋŕśṭůṿẘẋẏẓḀḂḈḊḔḞḠḢḬĴḴĻḾŊÕṔɊŔṠṮŨṼẄẌŸƵ';\nfunction generateENXA(msg) {\n    var ast = typeof msg === 'string' ? (0, icu_messageformat_parser_1.parse)(msg) : msg;\n    ast.forEach(function (el) {\n        if ((0, icu_messageformat_parser_1.isLiteralElement)(el)) {\n            el.value = el.value\n                .split('')\n                .map(function (c) {\n                var i = ASCII.indexOf(c);\n                if (i < 0) {\n                    return c;\n                }\n                return ACCENTED_ASCII[i];\n            })\n                .join('');\n        }\n        else if ((0, icu_messageformat_parser_1.isPluralElement)(el) || (0, icu_messageformat_parser_1.isSelectElement)(el)) {\n            for (var _i = 0, _a = Object.values(el.options); _i < _a.length; _i++) {\n                var opt = _a[_i];\n                generateENXA(opt.value);\n            }\n        }\n        else if ((0, icu_messageformat_parser_1.isTagElement)(el)) {\n            generateENXA(el.children);\n        }\n    });\n    return ast;\n}\nexports.generateENXA = generateENXA;\nfunction generateENXB(msg) {\n    var ast = typeof msg === 'string' ? (0, icu_messageformat_parser_1.parse)(msg) : msg;\n    ast.forEach(function (el) {\n        if ((0, icu_messageformat_parser_1.isLiteralElement)(el)) {\n            var pseudoString = el.value\n                .split('')\n                .map(function (c, index) {\n                var i = ASCII.indexOf(c);\n                var canPad = (index + 1) % 3 === 0;\n                if (i < 0) {\n                    return c;\n                }\n                return canPad ? ACCENTED_ASCII[i].repeat(3) : ACCENTED_ASCII[i];\n            })\n                .join('');\n            el.value = \"[!! \".concat(pseudoString, \" !!]\");\n        }\n        else if ((0, icu_messageformat_parser_1.isPluralElement)(el) || (0, icu_messageformat_parser_1.isSelectElement)(el)) {\n            for (var _i = 0, _a = Object.values(el.options); _i < _a.length; _i++) {\n                var opt = _a[_i];\n                generateENXB(opt.value);\n            }\n        }\n        else if ((0, icu_messageformat_parser_1.isTagElement)(el)) {\n            generateENXB(el.children);\n        }\n    });\n    return ast;\n}\nexports.generateENXB = generateENXB;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "TYPE", "SKELETON_TYPE", "isLiteralElement", "el", "type", "literal", "isArgumentElement", "argument", "isNumberElement", "number", "isDateElement", "date", "isTimeElement", "time", "isSelectElement", "select", "isPluralElement", "plural", "isPoundElement", "pound", "isTagElement", "tag", "isNumberSkeleton", "isDateTimeSkeleton", "dateTime", "createLiteralElement", "value", "createNumberElement", "style", "SPACE_SEPARATOR_REGEX", "DATE_TIME_REGEX", "parseDateTimeSkeleton", "skeleton", "result", "replace", "match", "len", "length", "era", "year", "RangeError", "month", "day", "weekday", "hour12", "hourCycle", "hour", "minute", "second", "timeZoneName", "WHITE_SPACE_REGEX", "FRACTION_PRECISION_REGEX", "SIGNIFICANT_PRECISION_REGEX", "INTEGER_WIDTH_REGEX", "CONCISE_INTEGER_WIDTH_REGEX", "parseSignificantPrecision", "str", "roundingPriority", "_", "g1", "g2", "minimumSignificantDigits", "maximumSignificantDigits", "parseSign", "signDisplay", "currencySign", "parseConciseScientificAndEngineeringStem", "stem", "notation", "slice", "test", "Error", "minimumIntegerDigits", "parseNotationOptions", "opt", "signOpts", "parseNumberSkeleton", "tokens", "_i", "tokens_1", "token", "scale", "currency", "options", "useGrouping", "maximumFractionDigits", "unit", "compactDisplay", "__assign", "reduce", "all", "currencyDisplay", "unitDisplay", "parseFloat", "g3", "g4", "g5", "minimumFractionDigits", "trailingZeroDisplay", "conciseScientificAndEngineeringOpts", "_a", "timeData", "getDefaultHourSymbolFromLocale", "locale", "undefined", "hourCycles", "regionTag", "languageTag", "language", "maximize", "region", "concat", "SPACE_SEPARATOR_START_REGEX", "RegExp", "source", "SPACE_SEPARATOR_END_REGEX", "createLocation", "start", "end", "hasNativeStartsWith", "String", "prototype", "startsWith", "hasNativeFromCodePoint", "fromCodePoint", "hasNativeFromEntries", "Object", "fromEntries", "hasNativeCodePointAt", "codePointAt", "hasTrimStart", "trimStart", "hasTrimEnd", "trimEnd", "isSafeInteger", "Number", "n", "isFinite", "Math", "floor", "abs", "REGEX_SUPPORTS_U_AND_Y", "RE", "exec", "matchIdentifierAtIndex", "s", "search", "position", "codePoints", "arguments", "code", "elements", "i", "fromCharCode", "entries", "obj", "entries_1", "k", "v", "index", "size", "first", "charCodeAt", "flag", "IDENTIFIER_PREFIX_RE_1", "lastIndex", "c", "_isWhiteSpace", "_isPatternSyntax", "push", "apply", "<PERSON><PERSON><PERSON>", "message", "this", "offset", "line", "column", "ignoreTag", "requiresOther<PERSON>lause", "shouldParseSkeletons", "parse", "parseMessage", "nestingLevel", "parentArgType", "expectingCloseTag", "isEOF", "char", "parseArgument", "err", "val", "peek", "error", "UNMATCHED_CLOSING_TAG", "clonePosition", "_isAlpha", "parseTag", "parseLiteral", "bump", "location", "startPosition", "tagName", "parseTagName", "bumpSpace", "bumpIf", "childrenResult", "children", "endTagStartPosition", "INVALID_TAG", "closingTagNameStartPosition", "UNCLOSED_TAG", "startOffset", "parseQuoteResult", "tryParseQuote", "parseUnquotedResult", "tryParseUnquoted", "parseLeftAngleResult", "tryParseLeftAngleBracket", "codepoint", "ch", "openingBracePosition", "EXPECT_ARGUMENT_CLOSING_BRACE", "EMPTY_ARGUMENT", "parseIdentifierIfPossible", "MALFORMED_ARGUMENT", "parseArgumentOptions", "startingPosition", "endOffset", "bumpTo", "typeStartPosition", "argType", "typeEndPosition", "EXPECT_ARGUMENT_TYPE", "styleAndLocation", "styleStartPosition", "parseSimpleArgStyleIfPossible", "EXPECT_ARGUMENT_STYLE", "styleLocation", "argCloseResult", "tryParseArgumentClose", "location_1", "parseNumberSkeletonFromString", "EXPECT_DATE_TIME_SKELETON", "dateTimePattern", "skeletonCopy", "patternPos", "patternChar", "char<PERSON>t", "extraLength", "hourLen", "dayPeriodLen", "hourChar", "getBestPattern", "pattern", "parsedOptions", "typeEndPosition_1", "EXPECT_SELECT_ARGUMENT_OPTIONS", "identifierAndLocation", "pluralOffset", "EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE", "tryParseDecimalInteger", "INVALID_PLURAL_ARGUMENT_OFFSET_VALUE", "optionsResult", "tryParsePluralOrSelectOptions", "location_2", "pluralType", "INVALID_ARGUMENT_TYPE", "nestedBraces", "apostrophePosition", "bumpUntil", "UNCLOSED_QUOTE_IN_ARGUMENT_STYLE", "stringTokens_1", "split", "filter", "x", "stemAndOptions", "options_1", "e", "INVALID_NUMBER_SKELETON", "expectCloseTag", "parsedFirstIdentifier", "has<PERSON>ther<PERSON><PERSON><PERSON>", "parsedSelectors", "Set", "selector", "selectorLocation", "EXPECT_PLURAL_ARGUMENT_SELECTOR", "INVALID_PLURAL_ARGUMENT_SELECTOR", "has", "DUPLICATE_SELECT_ARGUMENT_SELECTOR", "DUPLICATE_PLURAL_ARGUMENT_SELECTOR", "EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT", "EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT", "fragmentResult", "add", "EXPECT_SELECT_ARGUMENT_SELECTOR", "MISSING_OTHER_CLAUSE", "expectNumberError", "invalidNumberError", "sign", "hasDigits", "decimal", "kind", "prefix", "currentOffset", "indexOf", "targetOffset", "min", "nextCode", "pruneLocation", "els", "for<PERSON>ach", "opts", "SyntaxError", "originalMessage", "captureLocation", "exports", "generateENXA", "tslib_1", "require", "icu_messageformat_parser_1", "ASCII", "ACCENTED_ASCII", "msg", "ast", "map", "join", "values"], "sourceRoot": ""}