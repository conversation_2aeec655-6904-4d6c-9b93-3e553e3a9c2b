{"version": 3, "file": "static/js/3835.3adc5f29.chunk.js", "mappings": "2NAWA,SAASA,IACP,OACEC,EAAAA,EAAAA,GAACC,EAAAA,IAAK,CACJ,cAAY,WACZC,OAAOF,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SAACC,eAAe,UACxCC,aACEN,EAAAA,EAAAA,GAACG,EAAAA,EAAgB,CAAAC,GAAA,SACfC,eAAe,sDAInBE,OAAOP,EAAAA,EAAAA,GAACQ,EAAAA,EAAU,KAGxB,CAEA,SAASC,EAAmBC,GAAsF,IAArF,SAAEC,EAAQ,wBAAEC,GAAsEF,EAC7G,SAASG,EAAkBC,EAAcC,GAEvCC,QAAQF,MAAM,4BAA6BA,EAAOC,EAAKE,eACzD,CAEA,OAAIL,GAEAZ,EAAAA,EAAAA,GAACkB,EAAAA,GAAa,CAACC,QAASN,EAAmBO,kBAAmBR,EAAwBD,SACnFA,KAMLX,EAAAA,EAAAA,GAACkB,EAAAA,GAAa,CAACC,QAASN,EAAmBQ,UAAUrB,EAAAA,EAAAA,GAACD,EAAa,IAAIY,SACpEA,GAGP,CAEO,SAASW,EACdC,EACAC,EACAC,EACAb,GAEA,OAAO,SAAoCc,GACzC,OACE1B,EAAAA,EAAAA,GAACS,EAAmB,CAACG,wBAAyBA,EAAwBD,UAEpEX,EAAAA,EAAAA,GAACwB,EAAS,IAAKE,KAGrB,CACF,C,2FCxCO,MAAMC,EAOTH,GAGAE,IASA,MAAME,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,MACXC,GAASC,EAAAA,EAAAA,KAEf,OACEjC,EAAAA,EAAAA,GAACwB,EACC,CACAQ,OAAQA,EACRJ,SAAUA,EACVE,SAAUA,KACLJ,GACL,C,sOC5CD,MAAMQ,GACX,EAAAC,EAAAA,eAA+C,MCK3CC,EAAmC,CACvCC,UAAU,EACVvB,MAAO,MAGF,MAAMwB,UAAsBC,EAAAA,UAIjCC,MAAA,KAAQJ,EAAR,GAEA,+BAAOK,CAAyB3B,GAC9B,MAAO,CAAEuB,UAAU,E,MAAMvB,EAC3B,CAEA4B,mBAAA,UAAAC,EAAA,YAAqB,WACnB,MAAM7B,MAAEA,GAAU6B,EAAKH,MAEvB,GAAc,OAAV1B,EAAgB,SAAA8B,EAAAC,UAAAC,OAHGC,EAAA,IAAAC,MAAAJ,GAAAK,EAAA,EAAAA,EAAAL,EAAAK,IAAAF,EAAAE,GAAAJ,UAAAI,GAIrBN,EAAKjB,MAAMwB,UAAU,C,KACnBH,EACAI,OAAQ,mBAGVR,EAAKS,SAAShB,EAChB,CACF,CAAC,EAXD,GAaAiB,iBAAAA,CAAkBvC,EAAcC,GAC9BuC,KAAK5B,MAAMP,UAAUL,EAAOC,EAC9B,CAEAwC,kBAAAA,CACEC,EACAC,GAEA,MAAMpB,SAAEA,GAAaiB,KAAKd,OACpBkB,UAAEA,GAAcJ,KAAK5B,MAQzBW,GACoB,OAApBoB,EAAU3C,OAqDhB,WAAuD,IAA9B6C,EAAAd,UAAAC,OAAA,QAAAc,IAAAf,UAAA,GAAAA,UAAA,GAAW,GAAIgB,EAAAhB,UAAAC,OAAA,QAAAc,IAAAf,UAAA,GAAAA,UAAA,GAAW,GACjD,OACEc,EAAEb,SAAWe,EAAEf,QAAUa,EAAEG,MAAK,CAACC,EAAMC,KAAWC,OAAOC,GAAGH,EAAMF,EAAEG,KAExE,CAxDMG,CAAgBX,EAAUE,UAAWA,KAErCJ,KAAK5B,MAAMwB,UAAU,CACnBkB,KAAMV,EACNW,KAAMb,EAAUE,UAChBP,OAAQ,SAGVG,KAAKF,SAAShB,GAElB,CAEAkC,MAAAA,GACE,MAAM3D,SAAEA,EAAQ4D,eAAEA,EAAcnD,kBAAEA,EAAiBC,SAAEA,GACnDiC,KAAK5B,OACDW,SAAEA,EAAQvB,MAAEA,GAAUwC,KAAKd,MAEjC,IAAIgC,EAAgB7D,EAEpB,GAAI0B,EAAU,CACZ,MAAMX,EAAuB,C,MAC3BZ,EACA4B,mBAAoBY,KAAKZ,oBAG3B,IAAI,EAAA+B,EAAAA,gBAAepD,GACjBmD,EAAgBnD,OACX,GAA8B,oBAAnBkD,EAChBC,EAAgBD,EAAe7C,OAC1B,KAAIN,EAGT,MAAM,IAAIsD,MACR,8FAHFF,GAAgB,EAAAG,EAAAA,eAAcvD,EAAmBM,EAG/C,CAGN,CAEA,OAAO,EAAAiD,EAAAA,eACLzC,EAAqB0C,SACrB,CACEC,MAAO,C,SACLxC,E,MACAvB,EACA4B,mBAAoBY,KAAKZ,qBAG7B8B,EAEJ,EC5GK,SAASM,EACdD,GAEA,GACW,MAATA,GAC0B,mBAAnBA,EAAMxC,UACuB,oBAA7BwC,EAAMnC,mBAEb,MAAM,IAAIgC,MAAM,kCAGlB,OAAO,CACT,CCLO,SAASK,IACd,MAAMC,GAAU,EAAAC,EAAAA,YAAW/C,GAE3B4C,EAA2BE,GAE3B,MAAOxC,EAAOY,IAAY,EAAA8B,EAAAA,UAGvB,CACDpE,MAAO,KACPqE,UAAU,IAGNC,GAAW,EAAAC,EAAAA,UACf,KAAM,CACJC,cAAeA,KACbN,GAAStC,qBACTU,EAAS,CAAEtC,MAAO,KAAMqE,UAAU,GAAQ,EAE5CI,aAAezE,GACbsC,EAAS,C,MACPtC,EACAqE,UAAU,OAGhB,CAACH,GAAStC,qBAGZ,GAAIF,EAAM2C,SACR,MAAM3C,EAAM1B,MAGd,OAAOsE,CACT,C,iCCtCO,SAASI,EACdhE,EACAiE,GAEA,MAAMC,EAAiChE,IAC9B,EAAAiD,EAAAA,eACLrC,EACAmD,GACA,EAAAd,EAAAA,eAAcnD,EAAWE,IAKvBiE,EAAOnE,EAAUoE,aAAepE,EAAUmE,MAAQ,UAGxD,OAFAD,EAAQE,YAAc,qBAAqBD,KAEpCD,CACT,C,oPCPA,MA6CMG,GAA0BlE,EAAAA,EAAAA,IAC9BmE,EAAAA,EAAAA,KAAQ,CAACtD,EAAmBuD,KACnB,CAAEC,QAASxD,EAAMyD,SAASC,eAAeH,EAAS/D,OAAOmE,YADlEL,EA9CyBpE,IACzB,MAAM,QAAEyE,IAAYlE,EAAAA,EAAAA,MACbnB,EAAOsF,IAAYC,EAAAA,EAAAA,YACpBvE,GAAWC,EAAAA,EAAAA,MAEXuE,GAAWC,EAAAA,EAAAA,MA4BjB,OAzBAC,EAAAA,EAAAA,YAAU,KACRJ,OAASxC,EAAU,GAClB,CAACuC,KAEJK,EAAAA,EAAAA,YAAU,KAER,IAAK9E,EAAMsE,SAAWG,EAAS,CAC7B,MAAMM,GAASC,EAAAA,EAAAA,IAAUP,GACzBM,EAAOE,QAAQC,OAAOC,IACpBC,EAAAA,EAAMC,sBAAsBF,GAC5BT,EAASS,EAAE,IAEbP,EAASG,EACX,IACC,CAACH,EAAUH,EAASzE,EAAMsE,WAE7BQ,EAAAA,EAAAA,YAAU,KAAO,IAADQ,EACG,QAAjBA,EAAItF,EAAMsE,eAAO,IAAAgB,GAAbA,EAAeC,cACjBnF,EAASoF,EAAAA,EAAOC,gBAAgBzF,EAAMsE,QAAQiB,aAAcvF,EAAMsE,QAAQG,SAAU,CAClFiB,SAAS,GAEb,GACC,CAACtF,EAAUJ,EAAMsE,UAGE,OAAb,OAALlF,QAAK,IAALA,OAAK,EAALA,EAAOuG,SACFrH,EAAAA,EAAAA,GAACsH,EAAAA,EAAgB,KAKxBtH,EAAAA,EAAAA,GAACuH,EAAAA,IAAW,CAAA5G,UACVX,EAAAA,EAAAA,GAACwH,EAAAA,IAAc,KACH,KAULC,GAAgBnG,EAAAA,EAAAA,GAAkBoG,EAAAA,EAAWC,eAAeC,aAAc/B,GAEvF,W,6FC9DA,MAAM6B,GAAAA,EACGC,eAAiB,CACtBE,eAAgB,iBAChBC,YAAa,cACbC,cAAe,gBACfH,aAAc,gBAOX,MAAMI,EAAmCA,CAACC,EAA4BC,KAC3E,KAAMD,aAAwBE,EAAAA,GAC5B,OAEF,MAAM,OAAEd,GAAWY,EACnB,IAAInH,EACJ,MAAMsH,EAAsB,CAAEf,UAC1BY,EAAaI,iBAAmBC,EAAAA,GAAWC,0BAC7CzH,EAAQ,IAAI0H,EAAAA,GAAcJ,IAExBH,EAAaI,iBAAmBC,EAAAA,GAAWG,oBAC7C3H,EAAQ,IAAI4H,EAAAA,GAAgBN,IAE1BH,EAAaI,iBAAmBC,EAAAA,GAAWK,iBAC7C7H,EAAQ,IAAI8H,EAAAA,GAAoBR,IAE9BH,EAAaI,iBAAmBC,EAAAA,GAAWO,0BAC7C/H,EAAQ,IAAIgI,EAAAA,GAAgBV,IAI9B,MAAMW,EAA0Bd,EAAae,kBAK7C,OAJIlI,GAASiI,IACXjI,EAAMmI,QAAUF,GAGXjI,CAAK,EAEd,K", "sources": ["common/utils/withErrorBoundary.tsx", "common/utils/withRouterNext.tsx", "../node_modules/react-error-boundary/dist/src/ErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/ErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/assertErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/useErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/withErrorBoundary.ts", "experiment-tracking/components/DirectRunPage.tsx", "common/utils/ErrorUtils.tsx"], "sourcesContent": ["import React from 'react';\nimport { ErrorBoundary, ErrorBoundaryPropsWithComponent, FallbackProps } from 'react-error-boundary';\nimport ErrorUtils from './ErrorUtils';\nimport { DangerIcon, Empty } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\nexport type ErrorBoundaryProps = {\n  children: React.Component;\n  customFallbackComponent?: ErrorBoundaryPropsWithComponent['FallbackComponent'];\n};\n\nfunction ErrorFallback() {\n  return (\n    <Empty\n      data-testid=\"fallback\"\n      title={<FormattedMessage defaultMessage=\"Error\" description=\"Title of editor error fallback component\" />}\n      description={\n        <FormattedMessage\n          defaultMessage=\"An error occurred while rendering this component.\"\n          description=\"Description of error fallback component\"\n        />\n      }\n      image={<DangerIcon />}\n    />\n  );\n}\n\nfunction CustomErrorBoundary({ children, customFallbackComponent }: React.PropsWithChildren<ErrorBoundaryProps>) {\n  function logErrorToConsole(error: Error, info: { componentStack: string }) {\n    // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n    console.error('Caught Unexpected Error: ', error, info.componentStack);\n  }\n\n  if (customFallbackComponent) {\n    return (\n      <ErrorBoundary onError={logErrorToConsole} FallbackComponent={customFallbackComponent}>\n        {children}\n      </ErrorBoundary>\n    );\n  }\n\n  return (\n    <ErrorBoundary onError={logErrorToConsole} fallback={<ErrorFallback />}>\n      {children}\n    </ErrorBoundary>\n  );\n}\n\nexport function withErrorBoundary<P>(\n  service: string,\n  Component: React.ComponentType<P>,\n  errorMessage?: React.ReactNode,\n  customFallbackComponent?: React.ComponentType<FallbackProps>,\n): React.ComponentType<P> {\n  return function CustomErrorBoundaryWrapper(props: P) {\n    return (\n      <CustomErrorBoundary customFallbackComponent={customFallbackComponent}>\n        {/* @ts-expect-error Generics don't play well with WithConditionalCSSProp type coming @emotion/react jsx typing to validate css= prop values typing. More details here: emotion-js/emotion#2169 */}\n        <Component {...props} />\n      </CustomErrorBoundary>\n    );\n  };\n}\n", "import React from 'react';\n\nimport {\n  type Location,\n  type Params as RouterDOMParams,\n  type NavigateOptions,\n  type To,\n  useLocation,\n  useNavigate,\n  useParams,\n} from './RoutingUtils';\n\nexport interface WithRouterNextProps<Params extends RouterDOMParams = RouterDOMParams> {\n  navigate: ReturnType<typeof useNavigate>;\n  location: Location;\n  params: Params;\n}\n\n/**\n * This HoC serves as a retrofit for class components enabling them to use\n * react-router v6's location, navigate and params being injected via props.\n */\nexport const withRouterNext =\n  <\n    T,\n    Props extends JSX.IntrinsicAttributes &\n      JSX.LibraryManagedAttributes<React.ComponentType<T>, React.PropsWithChildren<T>>,\n    Params extends RouterDOMParams = RouterDOMParams,\n  >(\n    Component: React.ComponentType<T>,\n  ) =>\n  (\n    props: Omit<\n      Props,\n      | 'location'\n      | 'navigate'\n      | 'params'\n      | 'navigationType'\n      /* prettier-ignore*/\n    >,\n  ) => {\n    const location = useLocation();\n    const navigate = useNavigate();\n    const params = useParams<Params>();\n\n    return (\n      <Component\n        /* prettier-ignore */\n        params={params as Params}\n        location={location}\n        navigate={navigate}\n        {...(props as Props)}\n      />\n    );\n  };\n", "import { createContext } from \"react\";\n\nexport type ErrorBoundaryContextType = {\n  didCatch: boolean;\n  error: any;\n  resetErrorBoundary: (...args: any[]) => void;\n};\n\nexport const ErrorBoundaryContext =\n  createContext<ErrorBoundaryContextType | null>(null);\n", "import {\n  Component,\n  createElement,\n  ErrorInfo,\n  isValidElement,\n  PropsWithChildren,\n  PropsWithRef,\n  ReactElement,\n} from \"react\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\nimport { ErrorBoundaryProps, FallbackProps } from \"./types\";\n\ntype ErrorBoundaryState = { didCatch: boolean; error: any };\n\nconst initialState: ErrorBoundaryState = {\n  didCatch: false,\n  error: null,\n};\n\nexport class ErrorBoundary extends Component<\n  PropsWithRef<PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  state = initialState;\n\n  static getDerivedStateFromError(error: Error) {\n    return { didCatch: true, error };\n  }\n\n  resetErrorBoundary = (...args: any[]) => {\n    const { error } = this.state;\n\n    if (error !== null) {\n      this.props.onReset?.({\n        args,\n        reason: \"imperative-api\",\n      });\n\n      this.setState(initialState);\n    }\n  };\n\n  componentDidCatch(error: Error, info: ErrorInfo) {\n    this.props.onError?.(error, info);\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState\n  ) {\n    const { didCatch } = this.state;\n    const { resetKeys } = this.props;\n\n    // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n    // we'd end up resetting the error boundary immediately.\n    // This would likely trigger a second error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n\n    if (\n      didCatch &&\n      prevState.error !== null &&\n      hasArrayChanged(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onReset?.({\n        next: resetKeys,\n        prev: prevProps.resetKeys,\n        reason: \"keys\",\n      });\n\n      this.setState(initialState);\n    }\n  }\n\n  render() {\n    const { children, fallbackRender, FallbackComponent, fallback } =\n      this.props;\n    const { didCatch, error } = this.state;\n\n    let childToRender = children;\n\n    if (didCatch) {\n      const props: FallbackProps = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      };\n\n      if (isValidElement(fallback)) {\n        childToRender = fallback;\n      } else if (typeof fallbackRender === \"function\") {\n        childToRender = fallbackRender(props);\n      } else if (FallbackComponent) {\n        childToRender = createElement(FallbackComponent, props);\n      } else {\n        throw new Error(\n          \"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\"\n        );\n      }\n    }\n\n    return createElement(\n      ErrorBoundaryContext.Provider,\n      {\n        value: {\n          didCatch,\n          error,\n          resetErrorBoundary: this.resetErrorBoundary,\n        },\n      },\n      childToRender\n    ) as ReactElement;\n  }\n}\n\nfunction hasArrayChanged(a: any[] = [], b: any[] = []) {\n  return (\n    a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n  );\n}\n", "import { ErrorBoundaryContextType } from \"./ErrorBoundaryContext\";\n\nexport function assertErrorBoundaryContext(\n  value: any\n): value is ErrorBoundaryContextType {\n  if (\n    value == null ||\n    typeof value.didCatch !== \"boolean\" ||\n    typeof value.resetErrorBoundary !== \"function\"\n  ) {\n    throw new Error(\"ErrorBoundaryContext not found\");\n  }\n\n  return true;\n}\n", "import { useContext, useMemo, useState } from \"react\";\nimport { assertErrorBoundaryContext } from \"./assertErrorBoundaryContext\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\n\nexport type UseErrorBoundaryApi<Error> = {\n  resetBoundary: () => void;\n  showBoundary: (error: Error) => void;\n};\n\nexport function useErrorBoundary<Error = any>(): UseErrorBoundaryApi<Error> {\n  const context = useContext(ErrorBoundaryContext);\n\n  assertErrorBoundaryContext(context);\n\n  const [state, setState] = useState<{\n    error: Error | null;\n    hasError: boolean;\n  }>({\n    error: null,\n    hasError: false,\n  });\n\n  const memoized = useMemo(\n    () => ({\n      resetBoundary: () => {\n        context?.resetErrorBoundary();\n        setState({ error: null, hasError: false });\n      },\n      showBoundary: (error: Error) =>\n        setState({\n          error,\n          hasError: true,\n        }),\n    }),\n    [context?.resetErrorBoundary]\n  );\n\n  if (state.hasError) {\n    throw state.error;\n  }\n\n  return memoized;\n}\n", "import { ComponentType, createElement } from \"react\";\nimport { ErrorBoundary } from \"./ErrorBoundary\";\nimport { ErrorBoundaryProps } from \"./types\";\n\nexport function withErrorBoundary<Props extends Object>(\n  Component: ComponentType<Props>,\n  errorBoundaryProps: ErrorBoundaryProps\n): ComponentType<Props> {\n  const Wrapped: ComponentType<Props> = (props: Props) => {\n    return createElement(\n      ErrorBoundary,\n      errorBoundaryProps,\n      createElement(Component, props)\n    );\n  };\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || \"Unknown\";\n  Wrapped.displayName = `withErrorBoundary(${name})`;\n\n  return Wrapped;\n}\n", "import { PageWrapper, LegacySkeleton } from '@databricks/design-system';\nimport { useEffect, useState } from 'react';\nimport { connect, useDispatch } from 'react-redux';\nimport { useParams, useNavigate } from '../../common/utils/RoutingUtils';\nimport { ErrorWrapper } from '../../common/utils/ErrorWrapper';\nimport Utils from '../../common/utils/Utils';\nimport { ReduxState } from '../../redux-types';\nimport { getRunApi } from '../actions';\nimport Routes from '../routes';\nimport { PageNotFoundView } from '../../common/components/PageNotFoundView';\nimport { WithRouterNextProps, withRouterNext } from '../../common/utils/withRouterNext';\nimport { withErrorBoundary } from '../../common/utils/withErrorBoundary';\nimport ErrorUtils from '../../common/utils/ErrorUtils';\n\nconst DirectRunPageImpl = (props: any) => {\n  const { runUuid } = useParams<{ runUuid: string }>();\n  const [error, setError] = useState<ErrorWrapper>();\n  const navigate = useNavigate();\n\n  const dispatch = useDispatch();\n\n  // Reset error after changing requested run\n  useEffect(() => {\n    setError(undefined);\n  }, [runUuid]);\n\n  useEffect(() => {\n    // Start fetching run info if it doesn't exist in the store yet\n    if (!props.runInfo && runUuid) {\n      const action = getRunApi(runUuid);\n      action.payload.catch((e) => {\n        Utils.logErrorAndNotifyUser(e);\n        setError(e);\n      });\n      dispatch(action);\n    }\n  }, [dispatch, runUuid, props.runInfo]);\n\n  useEffect(() => {\n    if (props.runInfo?.experimentId) {\n      navigate(Routes.getRunPageRoute(props.runInfo.experimentId, props.runInfo.runUuid), {\n        replace: true,\n      });\n    }\n  }, [navigate, props.runInfo]);\n\n  // If encountered 404 error, display a proper component\n  if (error?.status === 404) {\n    return <PageNotFoundView />;\n  }\n\n  // If the run is loading, display skeleton\n  return (\n    <PageWrapper>\n      <LegacySkeleton />\n    </PageWrapper>\n  );\n};\n\nconst DirectRunPageWithRouter = withRouterNext(\n  connect((state: ReduxState, ownProps: WithRouterNextProps<{ runUuid: string }>) => {\n    return { runInfo: state.entities.runInfosByUuid[ownProps.params.runUuid] };\n  })(DirectRunPageImpl),\n);\n\nexport const DirectRunPage = withErrorBoundary(ErrorUtils.mlflowServices.RUN_TRACKING, DirectRunPageWithRouter);\n\nexport default DirectRunPage;\n", "import React from 'react';\nimport { BadRequestError, InternalServerError, NotFoundError, PermissionError } from '@databricks/web-shared/errors';\nimport { ErrorWrapper } from './ErrorWrapper';\nimport { ErrorCodes } from '../constants';\n\nclass ErrorUtils {\n  static mlflowServices = {\n    MODEL_REGISTRY: 'Model Registry',\n    EXPERIMENTS: 'Experiments',\n    MODEL_SERVING: 'Model Serving',\n    RUN_TRACKING: 'Run Tracking',\n  };\n}\n\n/**\n * Maps known types of ErrorWrapper (legacy) to platform's predefined error instances.\n */\nexport const mapErrorWrapperToPredefinedError = (errorWrapper: ErrorWrapper, requestId?: string) => {\n  if (!(errorWrapper instanceof ErrorWrapper)) {\n    return undefined;\n  }\n  const { status } = errorWrapper;\n  let error: Error | undefined = undefined;\n  const networkErrorDetails = { status };\n  if (errorWrapper.getErrorCode() === ErrorCodes.RESOURCE_DOES_NOT_EXIST) {\n    error = new NotFoundError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.PERMISSION_DENIED) {\n    error = new PermissionError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.INTERNAL_ERROR) {\n    error = new InternalServerError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.INVALID_PARAMETER_VALUE) {\n    error = new BadRequestError(networkErrorDetails);\n  }\n\n  // Attempt to extract message from error wrapper and assign it to the error instance.\n  const messageFromErrorWrapper = errorWrapper.getMessageField();\n  if (error && messageFromErrorWrapper) {\n    error.message = messageFromErrorWrapper;\n  }\n\n  return error;\n};\nexport default ErrorUtils;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_jsx", "Empty", "title", "FormattedMessage", "id", "defaultMessage", "description", "image", "DangerIcon", "CustomErrorBoundary", "_ref", "children", "customFallbackComponent", "logErrorToConsole", "error", "info", "console", "componentStack", "Error<PERSON>ou<PERSON><PERSON>", "onError", "FallbackComponent", "fallback", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "service", "Component", "errorMessage", "props", "withRouterNext", "location", "useLocation", "navigate", "useNavigate", "params", "useParams", "$ebb31c7feaa4405e$export$b16d9fb1a22de840", "$hgUW1$createContext", "$44d7e150ebc754d2$var$initialState", "didCatch", "$44d7e150ebc754d2$export$e926676385687eaf", "$hgUW1$Component", "state", "getDerivedStateFromError", "resetErrorBoundary", "_this", "_len", "arguments", "length", "args", "Array", "_key", "onReset", "reason", "setState", "componentDidCatch", "this", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "a", "undefined", "b", "some", "item", "index", "Object", "is", "$44d7e150ebc754d2$var$hasArrayChanged", "next", "prev", "render", "fallback<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$hgUW1$isValidElement", "Error", "$hgUW1$createElement", "Provider", "value", "$75c9d331f9c1ed1a$export$f20aa86254872370", "$7c3c25b3f398a9d6$export$c052f6604b7d51fe", "context", "$hgUW1$useContext", "$hgUW1$useState", "<PERSON><PERSON><PERSON><PERSON>", "memoized", "$hgUW1$useMemo", "resetBoundary", "showBoundary", "$62ff477d53f02a5b$export$f0c7a449e0cfaec7", "errorBoundaryProps", "Wrapped", "name", "displayName", "DirectRunPageWithRouter", "connect", "ownProps", "runInfo", "entities", "runInfosByUuid", "runUuid", "setError", "useState", "dispatch", "useDispatch", "useEffect", "action", "getRunApi", "payload", "catch", "e", "Utils", "logErrorAndNotifyUser", "_props$runInfo", "experimentId", "Routes", "getRunPageRoute", "replace", "status", "PageNotFoundView", "PageWrapper", "LegacySkeleton", "DirectRunPage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "RUN_TRACKING", "MODEL_REGISTRY", "EXPERIMENTS", "MODEL_SERVING", "mapErrorWrapperToPredefinedError", "errorWrapper", "requestId", "ErrorWrapper", "networkErrorDetails", "getErrorCode", "ErrorCodes", "RESOURCE_DOES_NOT_EXIST", "NotFoundError", "PERMISSION_DENIED", "PermissionError", "INTERNAL_ERROR", "InternalServerError", "INVALID_PARAMETER_VALUE", "BadRequestError", "messageFromErrorWrapper", "getMessageField", "message"], "sourceRoot": ""}