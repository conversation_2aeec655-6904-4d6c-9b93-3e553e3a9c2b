"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[7367],{28486:function(e,t,r){r.d(t,{tH:function(){return s}});var o=r(31014);function a(e,t,r,o){Object.defineProperty(e,t,{get:r,set:o,enumerable:!0,configurable:!0})}a({},"ErrorBoundary",(()=>s));a({},"ErrorBoundaryContext",(()=>n));const n=(0,o.createContext)(null),i={didCatch:!1,error:null};class s extends o.Component{state=(()=>i)();static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary=(()=>{var e=this;return function(){const{error:t}=e.state;if(null!==t){for(var r=arguments.length,o=new Array(r),a=0;a<r;a++)o[a]=arguments[a];e.props.onReset?.({args:o,reason:"imperative-api"}),e.setState(i)}}})();componentDidCatch(e,t){this.props.onError?.(e,t)}componentDidUpdate(e,t){const{didCatch:r}=this.state,{resetKeys:o}=this.props;r&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some(((e,r)=>!Object.is(e,t[r])))}(e.resetKeys,o)&&(this.props.onReset?.({next:o,prev:e.resetKeys,reason:"keys"}),this.setState(i))}render(){const{children:e,fallbackRender:t,FallbackComponent:r,fallback:a}=this.props,{didCatch:i,error:s}=this.state;let l=e;if(i){const e={error:s,resetErrorBoundary:this.resetErrorBoundary};if((0,o.isValidElement)(a))l=a;else if("function"===typeof t)l=t(e);else{if(!r)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");l=(0,o.createElement)(r,e)}}return(0,o.createElement)(n.Provider,{value:{didCatch:i,error:s,resetErrorBoundary:this.resetErrorBoundary}},l)}}function l(e){if(null==e||"boolean"!==typeof e.didCatch||"function"!==typeof e.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function d(){const e=(0,o.useContext)(n);l(e);const[t,r]=(0,o.useState)({error:null,hasError:!1}),a=(0,o.useMemo)((()=>({resetBoundary:()=>{e?.resetErrorBoundary(),r({error:null,hasError:!1})},showBoundary:e=>r({error:e,hasError:!0})})),[e?.resetErrorBoundary]);if(t.hasError)throw t.error;return a}a({},"useErrorBoundary",(()=>d));function c(e,t){const r=r=>(0,o.createElement)(s,t,(0,o.createElement)(e,r)),a=e.displayName||e.name||"Unknown";return r.displayName=`withErrorBoundary(${a})`,r}a({},"withErrorBoundary",(()=>c))},63250:function(e,t,r){r.r(t),r.d(t,{default:function(){return he}});var o=r(89555),a=r(32599),n=r(15579),i=r(48012),s=r(4877),l=r.n(s),d=r(93215),c=r(58481),u=r(92246),m=r(91144),h=r(31014),g=r(46398),p=r(63609),f=r(88464),y=r(88443),v=r(30706),C=r(81313);let b=function(e){return e.TABLE="TABLE",e.CHART="CHART",e}({});const D="viewMode";var S=r(3679),M=r(96070),w=r(98590),R=r(50111);const E=["model_id","model_name","status","artifact_uri","creation_time","last_updated_time"],Y=e=>{let{searchQuery:t,onChangeSearchQuery:r,loggedModelsData:o}=e;const a=(0,h.useMemo)((()=>{const e=(e=>{const t=new Set,r=new Set,o=new Set;for(const c of e){var a,n,i,s,l,d;null===(a=c.data)||void 0===a||null===(n=a.metrics)||void 0===n||n.forEach((e=>e.key&&t.add(e.key))),null===(i=c.data)||void 0===i||null===(s=i.params)||void 0===s||s.forEach((e=>e.key&&r.add(e.key))),null===(l=c.info)||void 0===l||null===(d=l.tags)||void 0===d||d.forEach((e=>e.key&&o.add(e.key)))}return{metricNames:Array.from(t),paramNames:Array.from(r),tagNames:Array.from(o).filter(w.oD)}})(o),t=E.map((e=>({value:`attributes.${e}`})));return(0,M.$K)(e,t)}),[o]);return(0,R.Y)(S.M,{searchFilter:null!==t&&void 0!==t?t:"",onSearchFilterChange:r,defaultActiveFirstOption:!1,baseOptions:a,onClear:()=>r(""),placeholder:"metrics.rmse >= 0.8",tooltipContent:(0,R.FD)("div",{children:[(0,R.Y)(y.A,{id:"8f4/Zi",defaultMessage:"Search logged models using a simplified version of the SQL {whereBold} clause.",values:{whereBold:(0,R.Y)("b",{children:"WHERE"})}})," ",(0,R.Y)("br",{}),(0,R.Y)(y.A,{id:"fyVs2o",defaultMessage:"Examples:"}),(0,R.Y)("br",{}),"\u2022 metrics.rmse >= 0.8",(0,R.Y)("br",{}),"\u2022 metrics.`f1 score` < 1",(0,R.Y)("br",{}),"\u2022 params.type = 'tree'",(0,R.Y)("br",{}),"\u2022 tags.my_tag = 'foobar'",(0,R.Y)("br",{}),"\u2022 attributes.name = 'elasticnet'",(0,R.Y)("br",{})]})})};const A=e=>JSON.stringify([e.dataset_name,e.dataset_digest]);var B={name:"158icaa",styles:"margin-left:4px"};const _=e=>{let{loggedModelsData:t,selectedFilterDatasets:r,onToggleDataset:n,onClearSelectedDatasets:s}=e;const{theme:l}=(0,a.u)(),d=(0,f.A)(),c=(0,h.useRef)(new Map),u=(0,h.useMemo)((()=>{for(const r of t)for(const t of(null===(e=r.data)||void 0===e?void 0:e.metrics)||[]){var e;if(!t.dataset_name||!t.dataset_digest)continue;const r=A(t);c.current.has(r)||c.current.set(r,{hash:r,dataset_name:t.dataset_name,dataset_digest:t.dataset_digest})}return Array.from(c.current.values())}),[t]),m=(0,h.useMemo)((()=>(null===r||void 0===r?void 0:r.map(A))||[]),[r]);return(0,R.FD)(i.AYc,{componentId:"mlflow.logged_model.list_page.datasets_filter",id:"mlflow.logged_model.list_page.datasets_filter",value:m,label:d.formatMessage({id:"K8JcAN",defaultMessage:"Datasets"}),stayOpenOnSelection:!0,children:[(0,R.Y)(i.Qwi,{children:(0,R.FD)(a.B,{endIcon:(0,R.Y)(i.D3D,{}),componentId:"mlflow.logged_model.list_page.datasets_filter.toggle",icon:(0,R.Y)(i.KbA,{}),children:["Datasets",m.length>0?(0,R.FD)(R.FK,{children:[(0,R.Y)(i.Ezx,{css:B,children:m.length}),(0,R.Y)(i.htq,{"aria-hidden":"false",role:"button",onClick:e=>{e.stopPropagation(),e.preventDefault(),null===s||void 0===s||s()},css:(0,o.AH)({color:l.colors.textPlaceholder,fontSize:l.typography.fontSizeSm,marginLeft:l.spacing.xs,":hover":{color:l.colors.actionTertiaryTextHover}},"")})]}):null]})}),(0,R.Y)(i.dn6,{children:(0,R.Y)(i.HI_,{children:u.map((e=>{let{hash:t,dataset_digest:r,dataset_name:o}=e;return(0,R.FD)(i.jTC,{value:t,checked:m.includes(t),onChange:()=>null===n||void 0===n?void 0:n({dataset_digest:r,dataset_name:o}),children:[o," (#",r,")"]},t)}))})})]})};var I=r(41028),F=r(69526),x=r(14343),T=r(92677);const k=(e,t)=>{const r={[T.I7.CreationTime]:(0,F.zR)({id:"ltqNpe",defaultMessage:"Creation time"})}[e];if(r)return t.formatMessage(r);const o=(0,T.TQ)(e);return o?o.metricKey:e};var L={name:"bsla3r",styles:"max-height:300px;overflow:auto"};const N=e=>{let{orderByColumn:t,orderByAsc:r,onChangeOrderBy:n,columnDefs:s=[]}=e;const l=(0,f.A)(),[d,c]=(0,h.useState)(""),{theme:u}=(0,a.u)(),m=(0,h.useMemo)((()=>{const e=d.toLowerCase(),r={groupId:"attributes",headerName:l.formatMessage({id:"Tpj2su",defaultMessage:"Attributes"}),children:[{colId:T.I7.CreationTime,headerName:k(T.I7.CreationTime,l)}].filter((t=>{let{headerName:r}=t;return null===r||void 0===r?void 0:r.toLowerCase().includes(e)}))},o=s.filter((e=>{var t;return null===(t=e.groupId)||void 0===t?void 0:t.startsWith(T.D2)})).map((t=>{var r;return{...t,children:null===(r=t.children)||void 0===r?void 0:r.filter((t=>{let{colId:r}=t;return null===r||void 0===r?void 0:r.includes(e)})),headerName:t.headerName?`Metrics (${t.headerName})`:l.formatMessage({id:"EcjcgN",defaultMessage:"Metrics"})}})),a=[r,...o].filter((e=>e.children&&e.children.length>0));if(!a.some((e=>e.children&&e.children.some((e=>e.colId===t))))){const{metricKey:e}=(0,T.TQ)(t);e&&a.push({groupId:"current",headerName:l.formatMessage({id:"1wfVV5",defaultMessage:"Currently sorted by"}),children:[{colId:t,headerName:e}]})}return a}),[s,l,d,t]);return(0,R.FD)(i.rId.Root,{modal:!1,children:[(0,R.Y)(i.rId.Trigger,{asChild:!0,children:(0,R.Y)(a.B,{componentId:"mlflow.logged_model.list.order_by",icon:r?(0,R.Y)(i.GCP,{}):(0,R.Y)(i.MMv,{}),children:(0,R.Y)(y.A,{id:"YRBURU",defaultMessage:"Sort: {sortBy}",values:{sortBy:k(t,l)}})})}),(0,R.FD)(i.rId.Content,{css:L,children:[(0,R.FD)("div",{css:(0,o.AH)({padding:`${u.spacing.sm}px ${u.spacing.lg/2}px ${u.spacing.sm}px`,width:"100%",display:"flex",gap:u.spacing.xs},""),children:[(0,R.Y)(I.I,{componentId:"mlflow.logged_model.list.order_by.filter",prefix:(0,R.Y)(I.S,{}),value:d,type:"search",onChange:e=>c(e.target.value),placeholder:l.formatMessage({id:"PRwcGm",defaultMessage:"Search"}),autoFocus:!0,allowClear:!0}),(0,R.FD)("div",{css:(0,o.AH)({display:"flex",gap:u.spacing.xs},""),children:[(0,R.Y)(x.k,{pressed:!r,icon:(0,R.Y)(i.ZLN,{}),componentId:"mlflow.logged_model.list.order_by.button_desc",onClick:()=>n(t,!1),"aria-label":l.formatMessage({id:"w/sljb",defaultMessage:"Sort descending"})}),(0,R.Y)(x.k,{pressed:r,icon:(0,R.Y)(i.Kpk,{}),componentId:"mlflow.logged_model.list.order_by.button_asc",onClick:()=>n(t,!0),"aria-label":l.formatMessage({id:"E4eALP",defaultMessage:"Sort ascending"})})]})]}),m.map((e=>{let{headerName:o,children:a,groupId:s}=e;return(0,R.FD)(i.rId.Group,{"aria-label":o,children:[(0,R.Y)(i.rId.Label,{children:o}),null===a||void 0===a?void 0:a.map((e=>{let{headerName:o,colId:a}=e;return(0,R.FD)(i.rId.CheckboxItem,{componentId:"mlflow.logged_model.list.order_by.column_toggle",checked:t===a,onClick:()=>{a&&n(a,Boolean(r))},children:[(0,R.Y)(i.rId.ItemIndicator,{}),o]},a)}))]},s)}))]})]})},K=e=>{let{orderByColumn:t,orderByAsc:r,sortingAndFilteringEnabled:s,onChangeOrderBy:l,onUpdateColumns:d,columnDefs:c,columnVisibility:u={},viewMode:m,setViewMode:h,searchQuery:g="",onChangeSearchQuery:p,loggedModelsData:D,selectedFilterDatasets:S,onToggleDataset:M,onClearSelectedDatasets:w}=e;const E=(0,f.A)(),{theme:A}=(0,a.u)();return(0,R.FD)("div",{css:(0,o.AH)({display:"flex",flexWrap:"wrap",gap:A.spacing.sm},""),children:[(0,R.FD)(i.d98,{componentId:"mlflow.logged_model.list.view-mode",name:"view-mode",value:m,onChange:e=>{h((0,C.SK)(b,e.target.value,b.TABLE))},children:[(0,R.FD)(i.EPn,{value:"TABLE",children:[(0,R.Y)(n.T,{componentId:"mlflow.logged_model.list.view-mode-table-tooltip",content:E.formatMessage({id:"F9Aau+",defaultMessage:"Table view"}),children:(0,R.Y)(n.L,{})}),(0,R.Y)("span",{css:a.O,children:E.formatMessage({id:"F9Aau+",defaultMessage:"Table view"})})]}),(0,R.FD)(i.EPn,{value:"CHART",children:[(0,R.Y)(n.T,{componentId:"mlflow.logged_model.list.view-mode-chart-tooltip",content:E.formatMessage({id:"44XPr5",defaultMessage:"Chart view"}),children:(0,R.Y)(i.SCH,{})}),(0,R.Y)("span",{css:a.O,children:E.formatMessage({id:"44XPr5",defaultMessage:"Chart view"})})]})]}),s?(0,R.FD)(R.FK,{children:[(0,R.Y)(Y,{searchQuery:g,onChangeSearchQuery:p,loggedModelsData:D}),(0,R.Y)(_,{loggedModelsData:D,onToggleDataset:M,onClearSelectedDatasets:w,selectedFilterDatasets:S}),(0,R.Y)(N,{orderByColumn:null!==t&&void 0!==t?t:"",orderByAsc:r,onChangeOrderBy:l,columnDefs:c})]}):(0,R.Y)(a.B,{componentId:"mlflow.logged_model.list.sort",icon:r?(0,R.Y)(i.GCP,{}):(0,R.Y)(i.MMv,{}),onClick:()=>{t&&l(t,!r)},children:(0,R.Y)(y.A,{id:"eDoQXM",defaultMessage:"Sort: Created"})}),(0,R.Y)(v.$,{columnDefs:c,columnVisibility:u,onUpdateColumns:d,disabled:m===b.CHART})]})};var O=r(36506),V=r(31798),H=r(88457),P=r(42550),U=r(9133),$=r(71932),z=r(63617),Q=r(73150),q=r(39045),W=r(75627),j=r(62758),G=r(30152),X=r(23734),J=r(88421);const Z=(e,t)=>t?JSON.stringify([t,e]):null!==e&&void 0!==e?e:"",ee=function(e){return`experiment-logged-models-charts-ui-state-v${arguments.length>1&&void 0!==arguments[1]?arguments[1]:1}-${e}`},te=()=>({compareRunCharts:void 0,compareRunSections:void 0,autoRefreshEnabled:!1,isAccordionReordered:!1,chartsSearchFilter:"",globalLineChartConfig:void 0,isDirty:!1}),re=(e,t)=>{if("UPDATE"===t.type)return{...t.stateSetter(e),isDirty:!0};if("METRICS_UPDATED"===t.type){const{compareRunCharts:r,compareRunSections:o}=(e=>{const t=e.map((e=>{let{dataAccessKey:t,metricKey:r,datasetName:o}=e;return{deleted:!1,type:G.zL.BAR,uuid:`autogen-${t}`,metricSectionId:o?`autogen-${o}`:"default",isGenerated:!0,metricKey:r,dataAccessKey:t,datasetName:o,displayName:o?`(${o}) ${r}`:void 0}})),r=(0,U.uniq)(e.map((e=>{let{datasetName:t}=e;return t}))).map((e=>({display:!0,name:null!==e&&void 0!==e?e:"Metrics",uuid:e?`autogen-${e}`:"default",isReordered:!1})));return(0,U.isEmpty)(r)&&r.push({display:!0,name:"Metrics",uuid:"default",isReordered:!1}),{compareRunCharts:t,compareRunSections:r}})(t.metricsByDatasets);return((e,t)=>{var r,o;if((!e.compareRunCharts||!e.compareRunSections||!e.isDirty)&&(t.compareRunCharts.length>0||t.compareRunSections.length>0))return{...e,compareRunCharts:null!==(r=t.compareRunCharts)&&void 0!==r?r:[],compareRunSections:null!==(o=t.compareRunSections)&&void 0!==o?o:[]};const a=t.compareRunCharts.filter((t=>{var r;return!(null!==(r=e.compareRunCharts)&&void 0!==r&&r.find((e=>e.uuid===t.uuid)))})),n=t.compareRunSections.filter((t=>{var r;return a.find((e=>e.metricSectionId===t.uuid))&&!(null!==(r=e.compareRunSections)&&void 0!==r&&r.find((e=>e.uuid===t.uuid)))}));return n.length>0||a.length>0?{...e,compareRunCharts:e.compareRunCharts?[...e.compareRunCharts,...a]:t.compareRunCharts,compareRunSections:e.compareRunSections?[...e.compareRunSections,...n]:t.compareRunSections}:e})(e,{compareRunCharts:r,compareRunSections:o})}return"INITIALIZE"===t.type&&t.initialConfig?t.initialConfig:e},oe=(e,t)=>{const[r,o]=(0,h.useReducer)(re,void 0,te),[a,n]=(0,h.useState)(!0);(0,h.useEffect)((()=>{n(!0),(async e=>{const t=localStorage.getItem(ee(e));if(t)try{return JSON.parse(t)}catch{return}})(t).then((e=>{o({type:"INITIALIZE",initialConfig:e}),n(!1)}))}),[t]),(0,h.useEffect)((()=>{a||o({type:"METRICS_UPDATED",metricsByDatasets:e})}),[e,a]),(0,h.useEffect)((()=>{r.isDirty&&(async(e,t)=>{localStorage.setItem(ee(e),JSON.stringify(t))})(t,r)}),[t,r]);const i=(0,h.useCallback)((e=>o({type:"UPDATE",stateSetter:e})),[]);return{chartUIState:r,updateUIState:i,loading:a}};var ae=r(19415);var ne={name:"1qmr6ab",styles:"overflow:auto"};const ie=(0,h.memo)((e=>{var t,r;let{chartData:n,uiState:s,metricKeysByDataset:l,minWidth:d}=e;const{theme:u}=(0,a.u)(),{formatMessage:m}=(0,f.A)(),g=(0,h.useMemo)((()=>(0,U.uniq)(n.flatMap((e=>Object.keys(e.metrics))))),[n]),p=(0,h.useMemo)((()=>(0,U.uniq)(n.flatMap((e=>Object.keys(e.params))))),[n]),v=(0,j.g_)(),C=(0,h.useCallback)((e=>{v((t=>({...t,chartsSearchFilter:e})))}),[v]),[b,D]=(0,h.useState)(null),S=(0,h.useCallback)((e=>t=>D(G.i$.getEmptyChartCardByType(t,!1,void 0,e))),[]),M=(0,j.Ez)(),w=(0,j.KP)(),[E,Y]=(0,h.useState)(void 0),A=(0,h.useMemo)((()=>({runs:n})),[n]),B=(0,h.useMemo)((()=>({runs:n,getDataTraceLink:c.h.getExperimentLoggedModelDetailsPageRoute})),[n]),_=(0,R.Y)("div",{css:(0,o.AH)({marginTop:u.spacing.lg},""),children:(0,R.Y)(i.SvL,{description:(0,R.Y)(y.A,{id:"oKgZFA",defaultMessage:"No models found in experiment or all models are hidden. Select at least one model to view charts."})})});return(0,R.Y)("div",{css:(0,o.AH)({backgroundColor:u.colors.backgroundSecondary,paddingLeft:u.spacing.md,paddingRight:u.spacing.md,paddingBottom:u.spacing.md,borderTop:`1px solid ${u.colors.border}`,borderLeft:`1px solid ${u.colors.border}`,flex:1,overflow:"hidden",display:"flex",minWidth:d},""),children:(0,R.FD)("div",{css:(0,o.AH)({display:"flex",flexDirection:"column",gap:u.spacing.sm,paddingTop:u.spacing.sm,overflow:"hidden",flex:1},""),children:[(0,R.Y)(I.I,{componentId:"mlflow.logged_model.list.charts.search",role:"searchbox",prefix:(0,R.Y)(I.S,{}),value:null!==(t=s.chartsSearchFilter)&&void 0!==t?t:"",allowClear:!0,onChange:e=>{let{target:t}=e;return C(t.value)},placeholder:m({id:"7teFL4",defaultMessage:"Search metric charts"})}),(0,R.FD)("div",{css:ne,children:[(0,R.Y)(W.W,{contextData:B,component:Q.X,children:(0,R.Y)($.c_,{visibleChartCards:s.compareRunCharts,children:(0,R.Y)(q.J,{compareRunSections:s.compareRunSections,compareRunCharts:s.compareRunCharts,reorderCharts:U.noop,insertCharts:U.noop,chartData:n,startEditChart:D,removeChart:w,addNewChartCard:S,search:null!==(r=s.chartsSearchFilter)&&void 0!==r?r:"",groupBy:null,setFullScreenChart:Y,autoRefreshEnabled:!1,hideEmptyCharts:!1,globalLineChartConfig:void 0,supportedChartTypes:[G.zL.BAR,G.zL.SCATTER],noRunsSelectedEmptyState:_})})}),(0,R.Y)(z._,{fullScreenChart:E,onCancel:()=>Y(void 0),chartData:n,groupBy:null,tooltipContextValue:A,tooltipComponent:Q.X,autoRefreshEnabled:!1,globalLineChartConfig:void 0}),b&&(0,R.Y)(ae.z,{chartRunData:n,metricKeyList:g,metricKeysByDataset:l,paramKeyList:p,config:b,onSubmit:e=>{M({...e,displayName:void 0}),D(null)},onCancel:()=>D(null),groupBy:null,supportedChartTypes:[G.zL.BAR,G.zL.SCATTER]})]})]})})})),se=(0,h.memo)((e=>{let{loggedModels:t,experimentId:r,minWidth:n}=e;const{theme:i}=(0,a.u)(),s=((e,t)=>{const r=(0,h.useRef)();return r.current&&(0,U.isEqual)(t,r.current.deps)||(r.current={deps:t,value:e()}),r.current.value})((()=>t),[t]),l=(e=>(0,h.useMemo)((()=>{const t=[];return e.forEach((e=>{var r,o;null===(r=e.data)||void 0===r||null===(o=r.metrics)||void 0===o||o.forEach((e=>{let{key:r,dataset_name:o}=e;if(r&&!t.find((e=>e.metricKey===r&&e.datasetName===o))){const e=Z(r,o);t.push({metricKey:r,datasetName:o,dataAccessKey:e})}}))})),(0,U.orderBy)(t,(e=>{let{datasetName:t}=e;return!t}))}),[e]))(s),{chartUIState:d,updateUIState:c,loading:u}=oe(l,r),m=(e=>{const{isRowHidden:t}=(0,J.GP)();return(0,h.useMemo)((()=>(0,U.compact)(e.map(((e,r)=>{var o,a,n,i,s,l,d,c,u,m;return null!==(o=e.info)&&void 0!==o&&o.model_id?{displayName:null!==(a=null!==(n=null===(i=e.info)||void 0===i?void 0:i.name)&&void 0!==n?n:null===(s=e.info)||void 0===s?void 0:s.model_id)&&void 0!==a?a:"Unknown",images:{},metrics:(0,U.keyBy)(null===(l=e.data)||void 0===l||null===(d=l.metrics)||void 0===d?void 0:d.map((e=>{let{dataset_name:t,key:r,value:o,timestamp:a,step:n}=e;return{dataKey:Z(r,t),key:null!==r&&void 0!==r?r:"",value:null!==o&&void 0!==o?o:0,timestamp:null!==a&&void 0!==a?a:0,step:null!==n&&void 0!==n?n:0}})),"dataKey"),params:(0,U.keyBy)(null!==(c=null===(u=e.data)||void 0===u||null===(m=u.params)||void 0===m?void 0:m.map((e=>{let{key:t,value:r}=e;return{key:null!==t&&void 0!==t?t:"",value:null!==r&&void 0!==r?r:""}})).filter((e=>{let{key:t}=e;return t})))&&void 0!==c?c:[],"key"),tags:{},uuid:e.info.model_id,hidden:t(e.info.model_id,r),color:(0,X.T6)(e.info.model_id)}:null})))),[e,t])})(s);return u?(0,R.Y)("div",{css:(0,o.AH)({backgroundColor:i.colors.backgroundSecondary,paddingTop:i.spacing.lg,borderTop:`1px solid ${i.colors.border}`,borderLeft:`1px solid ${i.colors.border}`,flex:1,justifyContent:"center",alignItems:"center",display:"flex"},""),children:(0,R.Y)(a.S,{})}):(0,R.Y)(j.oB,{updateChartsUIState:c,children:(0,R.Y)(ie,{chartData:m,uiState:d,metricKeysByDataset:l,minWidth:n})})}));var le=r(25790),de=r(39416),ce=r(75111);var ue={name:"oww3ap",styles:"display:flex;flex:1;overflow:hidden;position:relative"};const me=()=>{const{experimentId:e}=(0,d.g)(),{theme:t}=(0,a.u)(),r=(0,d.Zp)(),s=(0,m.fS)(),{state:{orderByColumn:u,orderByAsc:f,columnVisibility:y,rowVisibilityMap:v,rowVisibilityMode:S,selectedFilterDatasets:M},searchQuery:w,isFilteringActive:E,setOrderBy:Y,setColumnVisibility:A,setRowVisibilityMode:B,toggleRowVisibility:_,updateSearchQuery:I,toggleDataset:F,clearSelectedDatasets:x}=(0,V.k)();l()(e,"Experiment ID must be defined"),(0,h.useEffect)((()=>{!(0,m.Dz)()&&e&&r(c.h.getExperimentPageRoute(e),{replace:!0})}),[e,r]);const{viewMode:k,setViewMode:L}=(()=>{const[e,t]=(0,d.ok)();return{viewMode:(0,C.SK)(b,e.get(D),b.TABLE),setViewMode:e=>{t({[D]:e})}}})(),{data:N,isFetching:U,isLoading:$,error:z,nextPageToken:Q,loadMoreResults:q}=(0,p.e)({experimentIds:[e],orderByAsc:f,searchQuery:w,selectedFilterDatasets:M,...(()=>{if(!u)return{orderByField:void 0};const e=(0,T.TQ)(u);return e.datasetDigest&&e.datasetName?{orderByField:`metrics.${e.metricKey}`,orderByDatasetName:e.datasetName,orderByDatasetDigest:e.datasetDigest}:{orderByField:u}})()}),W=z instanceof de.v7?z:void 0,{data:j}=(0,H.s)({loggedModels:N}),{columnDefs:G,compactColumnDefs:X}=(0,T.ih)({loggedModels:N,columnVisibility:y,isLoading:$,orderByColumn:u,orderByAsc:f,enableSortingByMetrics:s}),[Z,ee]=(0,h.useState)(295),[te,re]=(0,h.useState)(!1),oe=k!==b.TABLE,ae=oe&&te?(0,R.Y)("div",{css:(0,o.AH)({width:t.spacing.md},"")}):(0,R.Y)(g._,{columnDefs:oe?X:G,loggedModels:null!==N&&void 0!==N?N:[],isLoading:$,isLoadingMore:U,badRequestError:W,moreResultsAvailable:Boolean(Q),onLoadMore:q,onOrderByChange:Y,orderByAsc:f,orderByColumn:u,columnVisibility:y,relatedRunsData:j,isFilteringActive:E}),{resizableMaxWidth:ne,ref:ie}=(0,ce.b)(350);return(0,R.Y)(O.Xs,{children:(0,R.FD)(J.Ap,{visibilityMap:v,visibilityMode:S,setRowVisibilityMode:B,toggleRowVisibility:_,children:[(0,R.Y)(K,{columnDefs:G,columnVisibility:y,onChangeOrderBy:Y,onUpdateColumns:A,orderByColumn:u,orderByAsc:f,viewMode:k,setViewMode:L,searchQuery:w,onChangeSearchQuery:I,loggedModelsData:null!==N&&void 0!==N?N:[],sortingAndFilteringEnabled:s,selectedFilterDatasets:M,onToggleDataset:F,onClearSelectedDatasets:x}),(0,R.Y)(n.S,{size:"sm",shrinks:!1}),(null===z||void 0===z?void 0:z.message)&&!W&&(0,R.FD)(R.FK,{children:[(0,R.Y)(i.FcD,{componentId:"mlflow.logged_models.list.error",message:z.message,type:"error",closable:!1}),(0,R.Y)(n.S,{size:"sm",shrinks:!1})]}),oe?(0,R.Y)(le.Co,{children:(0,R.FD)("div",{ref:ie,css:ue,children:[(0,R.Y)(P.t,{onResize:ee,runListHidden:te,width:Z,onHiddenChange:re,maxWidth:ne,children:ae}),k===b.CHART&&(0,R.Y)(se,{loggedModels:null!==N&&void 0!==N?N:[],experimentId:e,minWidth:350})]})}):ae]})})};var he=()=>(0,R.Y)(u.X,{children:(0,R.Y)(me,{})})},82638:function(e,t,r){r.d(t,{t:function(){return n}});var o=r(9133),a=r(31014);const n=(0,o.isFunction)(a.useDeferredValue)?a.useDeferredValue:o.identity},88457:function(e,t,r){r.d(t,{s:function(){return c}});var o=r(31014),a=r(9133),n=r(77735),i=r(63528),s=r(84174);const l=e=>["USE_RELATED_RUNS_DATA_FOR_LOGGED_MODELS",{runUuid:e}],d=async e=>{let{queryKey:[,{runUuid:t}]}=e;try{const e=await i.x.getRun({run_id:t});return null===e||void 0===e?void 0:e.run}catch(r){return null}},c=e=>{var t;let{loggedModels:r=[]}=e;const i=(0,o.useMemo)((()=>{const e=(0,a.compact)(null===r||void 0===r?void 0:r.flatMap((e=>{var t,r;return null===e||void 0===e||null===(t=e.data)||void 0===t||null===(r=t.metrics)||void 0===r?void 0:r.map((e=>e.run_id))}))),t=(0,a.compact)(null===r||void 0===r?void 0:r.map((e=>{var t;return null===e||void 0===e||null===(t=e.info)||void 0===t?void 0:t.source_run_id})));return(0,a.sortBy)((0,a.uniq)([...e,...t]))}),[r]),c=(0,n.E)({queries:i.map((e=>({queryKey:l(e),queryFn:d,cacheTime:1/0,staleTime:1/0,refetchOnWindowFocus:!1,retry:!1})))}),u=c.some((e=>{let{isLoading:t}=e;return t})),m=null===(t=c.find((e=>{let{error:t}=e;return t})))||void 0===t?void 0:t.error,h=(0,s.z)(c.map((e=>{let{data:t}=e;return t})));return{data:(0,o.useMemo)((()=>h.map((e=>e)).filter(Boolean)),[h]),loading:u,error:m}}},92246:function(e,t,r){r.d(t,{X:function(){return u}});var o=r(37368),a=r(28486),n=r(48012),i=r(32599),s=r(88443),l=r(50111);var d={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"};const c=e=>{var t;let{error:r}=e;return(0,l.Y)(n.ffj,{css:d,children:(0,l.Y)(n.SvL,{"data-testid":"fallback",title:(0,l.Y)(s.A,{id:"AOoWxS",defaultMessage:"Error"}),description:null!==(t=null===r||void 0===r?void 0:r.message)&&void 0!==t?t:(0,l.Y)(s.A,{id:"zmMR5p",defaultMessage:"An error occurred while rendering this component."}),image:(0,l.Y)(i.j,{})})})},u=e=>{let{children:t,resetKey:r}=e;return(0,l.Y)(a.tH,{FallbackComponent:c,resetKeys:[r],children:(0,l.Y)(o.Au,{children:t})})}}}]);
//# sourceMappingURL=7367.cbcc2978.chunk.js.map