/*! For license information please see 3314.3747b2a7.chunk.js.LICENSE.txt */
(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[3314],{3170:function(e,t,r){var n;n=function(){return function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,r){"use strict";var n=r(1),i=r(146),a=r(162),o=r(163),s=r(151),u=r(164),c=r(156),l=r(153);if(r(4)()){var f=r(165).PDFNodeStream;i.setPDFNetworkStreamFactory((function(e){return new f(e)}))}else if("undefined"!==typeof Response&&"body"in Response.prototype&&"undefined"!==typeof ReadableStream){var d=r(168).PDFFetchStream;i.setPDFNetworkStreamFactory((function(e){return new d(e)}))}else{var h=r(169).PDFNetworkStream;i.setPDFNetworkStreamFactory((function(e){return new h(e)}))}t.build=i.build,t.version=i.version,t.getDocument=i.getDocument,t.LoopbackPort=i.LoopbackPort,t.PDFDataRangeTransport=i.PDFDataRangeTransport,t.PDFWorker=i.PDFWorker,t.renderTextLayer=a.renderTextLayer,t.AnnotationLayer=o.AnnotationLayer,t.createPromiseCapability=n.createPromiseCapability,t.PasswordResponses=n.PasswordResponses,t.InvalidPDFException=n.InvalidPDFException,t.MissingPDFException=n.MissingPDFException,t.SVGGraphics=u.SVGGraphics,t.NativeImageDecoding=n.NativeImageDecoding,t.CMapCompressionType=n.CMapCompressionType,t.PermissionFlag=n.PermissionFlag,t.UnexpectedResponseException=n.UnexpectedResponseException,t.OPS=n.OPS,t.VerbosityLevel=n.VerbosityLevel,t.UNSUPPORTED_FEATURES=n.UNSUPPORTED_FEATURES,t.createValidAbsoluteUrl=n.createValidAbsoluteUrl,t.createObjectURL=n.createObjectURL,t.removeNullCharacters=n.removeNullCharacters,t.shadow=n.shadow,t.Util=n.Util,t.ReadableStream=n.ReadableStream,t.URL=n.URL,t.RenderingCancelledException=s.RenderingCancelledException,t.getFilenameFromUrl=s.getFilenameFromUrl,t.LinkTarget=s.LinkTarget,t.addLinkAttributes=s.addLinkAttributes,t.loadScript=s.loadScript,t.GlobalWorkerOptions=c.GlobalWorkerOptions,t.apiCompatibilityParams=l.apiCompatibilityParams},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toRomanNumerals=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];l(Number.isInteger(e)&&e>0,"The number should be a positive integer.");for(var r,n=[];e>=1e3;)e-=1e3,n.push("M");r=e/100|0,e%=100,n.push(x[r]),r=e/10|0,e%=10,n.push(x[10+r]),n.push(x[20+e]);var i=n.join("");return t?i.toLowerCase():i},t.arrayByteLength=w,t.arraysToBytes=function(e){if(1===e.length&&e[0]instanceof Uint8Array)return e[0];var t,r,n,i=0,a=e.length;for(t=0;t<a;t++)i+=n=w(r=e[t]);var o=0,s=new Uint8Array(i);for(t=0;t<a;t++)(r=e[t])instanceof Uint8Array||(r="string"===typeof r?A(r):new Uint8Array(r)),n=r.byteLength,s.set(r,o),o+=n;return s},t.assert=l,t.bytesToString=function(e){l(null!==e&&"object"===a(e)&&void 0!==e.length,"Invalid argument for bytesToString");var t=e.length,r=8192;if(t<r)return String.fromCharCode.apply(null,e);for(var n=[],i=0;i<t;i+=r){var o=Math.min(i+r,t),s=e.subarray(i,o);n.push(String.fromCharCode.apply(null,s))}return n.join("")},t.createPromiseCapability=function(){var e=Object.create(null),t=!1;return Object.defineProperty(e,"settled",{get:function(){return t}}),e.promise=new Promise((function(r,n){e.resolve=function(e){t=!0,r(e)},e.reject=function(e){t=!0,n(e)}})),e},t.deprecated=function(e){console.log("Deprecated API usage: "+e)},t.getInheritableProperty=function(e){for(var t,r=e.dict,n=e.key,i=e.getArray,a=void 0!==i&&i,o=e.stopWhenFound,s=void 0===o||o,c=0;r;){var l=a?r.getArray(n):r.get(n);if(void 0!==l){if(s)return l;t||(t=[]),t.push(l)}if(++c>100){u('getInheritableProperty: maximum loop count exceeded for "'.concat(n,'"'));break}r=r.get("Parent")}return t},t.getLookupTableFactory=function(e){var t;return function(){return e&&(t=Object.create(null),e(t),e=null),t}},t.getVerbosityLevel=function(){return s},t.info=function(e){s>=o.INFOS&&console.log("Info: "+e)},t.isArrayBuffer=function(e){return"object"===a(e)&&null!==e&&void 0!==e.byteLength},t.isBool=function(e){return"boolean"===typeof e},t.isEmptyObj=function(e){for(var t in e)return!1;return!0},t.isNum=function(e){return"number"===typeof e},t.isString=function(e){return"string"===typeof e},t.isSpace=function(e){return 32===e||9===e||13===e||10===e},t.isSameOrigin=function(e,t){try{var r=new i.URL(e);if(!r.origin||"null"===r.origin)return!1}catch(a){return!1}var n=new i.URL(t,r);return r.origin===n.origin},t.createValidAbsoluteUrl=function(e,t){if(!e)return null;try{var r=t?new i.URL(e,t):new i.URL(e);if(function(e){if(!e)return!1;switch(e.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(r))return r}catch(n){}return null},t.isLittleEndian=function(){var e=new Uint8Array(4);return e[0]=1,1===new Uint32Array(e.buffer,0,1)[0]},t.isEvalSupported=function(){try{return new Function(""),!0}catch(e){return!1}},t.log2=function(e){return e<=0?0:Math.ceil(Math.log2(e))},t.readInt8=function(e,t){return e[t]<<24>>24},t.readUint16=function(e,t){return e[t]<<8|e[t+1]},t.readUint32=function(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0},t.removeNullCharacters=function(e){return"string"!==typeof e?(u("The argument for removeNullCharacters must be a string."),e):e.replace(S,"")},t.setVerbosityLevel=function(e){Number.isInteger(e)&&(s=e)},t.shadow=function(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!1}),r},t.string32=function(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)},t.stringToBytes=A,t.stringToPDFString=function(e){var t,r=e.length,n=[];if("\xfe"===e[0]&&"\xff"===e[1])for(t=2;t<r;t+=2)n.push(String.fromCharCode(e.charCodeAt(t)<<8|e.charCodeAt(t+1)));else for(t=0;t<r;++t){var i=P[e.charCodeAt(t)];n.push(i?String.fromCharCode(i):e.charAt(t))}return n.join("")},t.stringToUTF8String=function(e){return decodeURIComponent(escape(e))},t.utf8StringToString=function(e){return unescape(encodeURIComponent(e))},t.warn=u,t.unreachable=c,Object.defineProperty(t,"ReadableStream",{enumerable:!0,get:function(){return n.ReadableStream}}),Object.defineProperty(t,"URL",{enumerable:!0,get:function(){return i.URL}}),t.createObjectURL=t.FormatError=t.XRefParseException=t.XRefEntryException=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.StreamType=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.NativeImageDecoding=t.MissingPDFException=t.MissingDataException=t.InvalidPDFException=t.AbortException=t.CMapCompressionType=t.ImageKind=t.FontType=t.AnnotationType=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationBorderStyleType=t.UNSUPPORTED_FEATURES=t.VerbosityLevel=t.OPS=t.IDENTITY_MATRIX=t.FONT_IDENTITY_MATRIX=void 0,r(2);var n=r(142),i=r(144);function a(e){return a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}t.IDENTITY_MATRIX=[1,0,0,1,0,0],t.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0],t.NativeImageDecoding={NONE:"none",DECODE:"decode",DISPLAY:"display"},t.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},t.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},t.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},t.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},t.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512},t.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864},t.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},t.StreamType={UNKNOWN:0,FLATE:1,LZW:2,DCT:3,JPX:4,JBIG:5,A85:6,AHX:7,CCF:8,RL:9},t.FontType={UNKNOWN:0,TYPE1:1,TYPE1C:2,CIDFONTTYPE0:3,CIDFONTTYPE0C:4,TRUETYPE:5,CIDFONTTYPE2:6,TYPE3:7,OPENTYPE:8,TYPE0:9,MMTYPE1:10};var o={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=o,t.CMapCompressionType={NONE:0,BINARY:1,STREAM:2},t.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},t.UNSUPPORTED_FEATURES={unknown:"unknown",forms:"forms",javaScript:"javaScript",smask:"smask",shadingPattern:"shadingPattern",font:"font"},t.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};var s=o.WARNINGS;function u(e){s>=o.WARNINGS&&console.log("Warning: "+e)}function c(e){throw new Error(e)}function l(e,t){e||c(t)}var f=function(){function e(e,t){this.name="PasswordException",this.message=e,this.code=t}return e.prototype=new Error,e.constructor=e,e}();t.PasswordException=f;var d=function(){function e(e,t){this.name="UnknownErrorException",this.message=e,this.details=t}return e.prototype=new Error,e.constructor=e,e}();t.UnknownErrorException=d;var h=function(){function e(e){this.name="InvalidPDFException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.InvalidPDFException=h;var p=function(){function e(e){this.name="MissingPDFException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.MissingPDFException=p;var v=function(){function e(e,t){this.name="UnexpectedResponseException",this.message=e,this.status=t}return e.prototype=new Error,e.constructor=e,e}();t.UnexpectedResponseException=v;var m=function(){function e(e,t){this.begin=e,this.end=t,this.message="Missing data ["+e+", "+t+")"}return e.prototype=new Error,e.prototype.name="MissingDataException",e.constructor=e,e}();t.MissingDataException=m;var g=function(){function e(e){this.message=e}return e.prototype=new Error,e.prototype.name="XRefEntryException",e.constructor=e,e}();t.XRefEntryException=g;var y=function(){function e(e){this.message=e}return e.prototype=new Error,e.prototype.name="XRefParseException",e.constructor=e,e}();t.XRefParseException=y;var b=function(){function e(e){this.message=e}return e.prototype=new Error,e.prototype.name="FormatError",e.constructor=e,e}();t.FormatError=b;var _=function(){function e(e){this.name="AbortException",this.message=e}return e.prototype=new Error,e.constructor=e,e}();t.AbortException=_;var S=/\x00/g;function A(e){l("string"===typeof e,"Invalid argument for stringToBytes");for(var t=e.length,r=new Uint8Array(t),n=0;n<t;++n)r[n]=255&e.charCodeAt(n);return r}function w(e){return void 0!==e.length?e.length:(l(void 0!==e.byteLength),e.byteLength)}var k=function(){function e(){}var t=["rgb(",0,",",0,",",0,")"];return e.makeCssRgb=function(e,r,n){return t[1]=e,t[3]=r,t[5]=n,t.join("")},e.transform=function(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]},e.applyTransform=function(e,t){return[e[0]*t[0]+e[1]*t[2]+t[4],e[0]*t[1]+e[1]*t[3]+t[5]]},e.applyInverseTransform=function(e,t){var r=t[0]*t[3]-t[1]*t[2];return[(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/r,(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/r]},e.getAxialAlignedBoundingBox=function(t,r){var n=e.applyTransform(t,r),i=e.applyTransform(t.slice(2,4),r),a=e.applyTransform([t[0],t[3]],r),o=e.applyTransform([t[2],t[1]],r);return[Math.min(n[0],i[0],a[0],o[0]),Math.min(n[1],i[1],a[1],o[1]),Math.max(n[0],i[0],a[0],o[0]),Math.max(n[1],i[1],a[1],o[1])]},e.inverseTransform=function(e){var t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]},e.apply3dTransform=function(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2],e[3]*t[0]+e[4]*t[1]+e[5]*t[2],e[6]*t[0]+e[7]*t[1]+e[8]*t[2]]},e.singularValueDecompose2dScale=function(e){var t=[e[0],e[2],e[1],e[3]],r=e[0]*t[0]+e[1]*t[2],n=e[0]*t[1]+e[1]*t[3],i=e[2]*t[0]+e[3]*t[2],a=e[2]*t[1]+e[3]*t[3],o=(r+a)/2,s=Math.sqrt((r+a)*(r+a)-4*(r*a-i*n))/2,u=o+s||1,c=o-s||1;return[Math.sqrt(u),Math.sqrt(c)]},e.normalizeRect=function(e){var t=e.slice(0);return e[0]>e[2]&&(t[0]=e[2],t[2]=e[0]),e[1]>e[3]&&(t[1]=e[3],t[3]=e[1]),t},e.intersect=function(t,r){function n(e,t){return e-t}var i=[t[0],t[2],r[0],r[2]].sort(n),a=[t[1],t[3],r[1],r[3]].sort(n),o=[];return t=e.normalizeRect(t),r=e.normalizeRect(r),(i[0]===t[0]&&i[1]===r[0]||i[0]===r[0]&&i[1]===t[0])&&(o[0]=i[1],o[2]=i[2],(a[0]===t[1]&&a[1]===r[1]||a[0]===r[1]&&a[1]===t[1])&&(o[1]=a[1],o[3]=a[2],o))},e}();t.Util=k;var x=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"],P=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364],R=function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return function(t,r){if(!(arguments.length>2&&void 0!==arguments[2]&&arguments[2])&&i.URL.createObjectURL){var n=new Blob([t],{type:r});return i.URL.createObjectURL(n)}for(var a="data:"+r+";base64,",o=0,s=t.length;o<s;o+=3){var u=255&t[o],c=255&t[o+1],l=255&t[o+2];a+=e[u>>2]+e[(3&u)<<4|c>>4]+e[o+1<s?(15&c)<<2|l>>6:64]+e[o+2<s?63&l:64]}return a}}();t.createObjectURL=R},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var i=r(3);if(!i._pdfjsCompatibilityChecked){i._pdfjsCompatibilityChecked=!0;var a=r(4),o="object"===("undefined"===typeof window?"undefined":n(window))&&"object"===("undefined"===typeof document?"undefined":n(document));!i.btoa&&a()&&(i.btoa=function(e){return Buffer.from(e,"binary").toString("base64")}),!i.atob&&a()&&(i.atob=function(e){return Buffer.from(e,"base64").toString("binary")}),o&&"undefined"===typeof Element.prototype.remove&&(Element.prototype.remove=function(){this.parentNode&&this.parentNode.removeChild(this)}),function(){if(o&&!a()){var e=document.createElement("div");if(e.classList.add("testOne","testTwo"),!0!==e.classList.contains("testOne")||!0!==e.classList.contains("testTwo")){var t=DOMTokenList.prototype.add,r=DOMTokenList.prototype.remove;DOMTokenList.prototype.add=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];for(var i=0;i<r.length;i++){var a=r[i];t.call(this,a)}},DOMTokenList.prototype.remove=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];for(var i=0;i<t.length;i++){var a=t[i];r.call(this,a)}}}}}(),o&&!a()&&!1!==document.createElement("div").classList.toggle("test",0)&&(DOMTokenList.prototype.toggle=function(e){var t=arguments.length>1?!!arguments[1]:!this.contains(e);return this[t?"add":"remove"](e),t}),String.prototype.startsWith||r(5),String.prototype.endsWith||r(35),String.prototype.includes||r(37),Array.prototype.includes||r(39),Array.from||r(46),Object.assign||r(69),Math.log2||(Math.log2=r(74)),Number.isNaN||(Number.isNaN=r(76)),Number.isInteger||(Number.isInteger=r(78)),i.Promise&&i.Promise.prototype&&i.Promise.prototype.finally||(i.Promise=r(81)),i.WeakMap||(i.WeakMap=r(101)),i.WeakSet||(i.WeakSet=r(118)),String.codePointAt||(String.codePointAt=r(122)),String.fromCodePoint||(String.fromCodePoint=r(124)),i.Symbol||r(126),String.prototype.padStart||r(133),String.prototype.padEnd||r(137),Object.values||(Object.values=r(139))}},function(e,t,n){"use strict";e.exports="undefined"!==typeof window&&window.Math===Math?window:"undefined"!==typeof r.g&&r.g.Math===Math?r.g:"undefined"!==typeof self&&self.Math===Math?self:{}},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}e.exports=function(){return"object"===("undefined"===typeof process?"undefined":n(process))&&process+""==="[object process]"&&!process.versions.nw}},function(e,t,r){"use strict";r(6),e.exports=r(9).String.startsWith},function(e,t,r){"use strict";var n=r(7),i=r(25),a=r(27),o="startsWith",s=""[o];n(n.P+n.F*r(34)(o),"String",{startsWith:function(e){var t=a(this,e,o),r=i(Math.min(arguments.length>1?arguments[1]:void 0,t.length)),n=String(e);return s?s.call(t,n,r):t.slice(r,r+n.length)===n}})},function(e,t,r){"use strict";var n=r(8),i=r(9),a=r(10),o=r(20),s=r(23),u="prototype",c=function e(t,r,c){var l,f,d,h,p=t&e.F,v=t&e.G,m=t&e.P,g=t&e.B,y=v?n:t&e.S?n[r]||(n[r]={}):(n[r]||{})[u],b=v?i:i[r]||(i[r]={}),_=b[u]||(b[u]={});for(l in v&&(c=r),c)d=((f=!p&&y&&void 0!==y[l])?y:c)[l],h=g&&f?s(d,n):m&&"function"==typeof d?s(Function.call,d):d,y&&o(y,l,d,t&e.U),b[l]!=d&&a(b,l,h),m&&_[l]!=d&&(_[l]=d)};n.core=i,c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,e.exports=c},function(e,t,r){"use strict";var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t,r){"use strict";var n=e.exports={version:"2.6.2"};"number"==typeof __e&&(__e=n)},function(e,t,r){"use strict";var n=r(11),i=r(19);e.exports=r(15)?function(e,t,r){return n.f(e,t,i(1,r))}:function(e,t,r){return e[t]=r,e}},function(e,t,r){"use strict";var n=r(12),i=r(14),a=r(18),o=Object.defineProperty;t.f=r(15)?Object.defineProperty:function(e,t,r){if(n(e),t=a(t,!0),n(r),i)try{return o(e,t,r)}catch(s){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e){if(!n(e))throw TypeError(e+" is not an object!");return e}},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}e.exports=function(e){return"object"===n(e)?null!==e:"function"===typeof e}},function(e,t,r){"use strict";e.exports=!r(15)&&!r(16)((function(){return 7!=Object.defineProperty(r(17)("div"),"a",{get:function(){return 7}}).a}))},function(e,t,r){"use strict";e.exports=!r(16)((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}))},function(e,t,r){"use strict";e.exports=function(e){try{return!!e()}catch(t){return!0}}},function(e,t,r){"use strict";var n=r(13),i=r(8).document,a=n(i)&&n(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e,t){if(!n(e))return e;var r,i;if(t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;if("function"==typeof(r=e.valueOf)&&!n(i=r.call(e)))return i;if(!t&&"function"==typeof(r=e.toString)&&!n(i=r.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t,r){"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,r){"use strict";var n=r(8),i=r(10),a=r(21),o=r(22)("src"),s="toString",u=Function[s],c=(""+u).split(s);r(9).inspectSource=function(e){return u.call(e)},(e.exports=function(e,t,r,s){var u="function"==typeof r;u&&(a(r,"name")||i(r,"name",t)),e[t]!==r&&(u&&(a(r,o)||i(r,o,e[t]?""+e[t]:c.join(String(t)))),e===n?e[t]=r:s?e[t]?e[t]=r:i(e,t,r):(delete e[t],i(e,t,r)))})(Function.prototype,s,(function(){return"function"==typeof this&&this[o]||u.call(this)}))},function(e,t,r){"use strict";var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t,r){"use strict";var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+i).toString(36))}},function(e,t,r){"use strict";var n=r(24);e.exports=function(e,t,r){if(n(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,n){return e.call(t,r,n)};case 3:return function(r,n,i){return e.call(t,r,n,i)}}return function(){return e.apply(t,arguments)}}},function(e,t,r){"use strict";e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,r){"use strict";var n=r(26),i=Math.min;e.exports=function(e){return e>0?i(n(e),9007199254740991):0}},function(e,t,r){"use strict";var n=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?i:n)(e)}},function(e,t,r){"use strict";var n=r(28),i=r(33);e.exports=function(e,t,r){if(n(t))throw TypeError("String#"+r+" doesn't accept regex!");return String(i(e))}},function(e,t,r){"use strict";var n=r(13),i=r(29),a=r(30)("match");e.exports=function(e){var t;return n(e)&&(void 0!==(t=e[a])?!!t:"RegExp"==i(e))}},function(e,t,r){"use strict";var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,r){"use strict";var n=r(31)("wks"),i=r(22),a=r(8).Symbol,o="function"==typeof a;(e.exports=function(e){return n[e]||(n[e]=o&&a[e]||(o?a:i)("Symbol."+e))}).store=n},function(e,t,r){"use strict";var n=r(9),i=r(8),a="__core-js_shared__",o=i[a]||(i[a]={});(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:n.version,mode:r(32)?"pure":"global",copyright:"\xa9 2019 Denis Pushkarev (zloirock.ru)"})},function(e,t,r){"use strict";e.exports=!1},function(e,t,r){"use strict";e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,r){"use strict";var n=r(30)("match");e.exports=function(e){var t=/./;try{"/./"[e](t)}catch(r){try{return t[n]=!1,!"/./"[e](t)}catch(i){}}return!0}},function(e,t,r){"use strict";r(36),e.exports=r(9).String.endsWith},function(e,t,r){"use strict";var n=r(7),i=r(25),a=r(27),o="endsWith",s=""[o];n(n.P+n.F*r(34)(o),"String",{endsWith:function(e){var t=a(this,e,o),r=arguments.length>1?arguments[1]:void 0,n=i(t.length),u=void 0===r?n:Math.min(i(r),n),c=String(e);return s?s.call(t,c,u):t.slice(u-c.length,u)===c}})},function(e,t,r){"use strict";r(38),e.exports=r(9).String.includes},function(e,t,r){"use strict";var n=r(7),i=r(27),a="includes";n(n.P+n.F*r(34)(a),"String",{includes:function(e){return!!~i(this,e,a).indexOf(e,arguments.length>1?arguments[1]:void 0)}})},function(e,t,r){"use strict";r(40),e.exports=r(9).Array.includes},function(e,t,r){"use strict";var n=r(7),i=r(41)(!0);n(n.P,"Array",{includes:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}}),r(45)("includes")},function(e,t,r){"use strict";var n=r(42),i=r(25),a=r(44);e.exports=function(e){return function(t,r,o){var s,u=n(t),c=i(u.length),l=a(o,c);if(e&&r!=r){for(;c>l;)if((s=u[l++])!=s)return!0}else for(;c>l;l++)if((e||l in u)&&u[l]===r)return e||l||0;return!e&&-1}}},function(e,t,r){"use strict";var n=r(43),i=r(33);e.exports=function(e){return n(i(e))}},function(e,t,r){"use strict";var n=r(29);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==n(e)?e.split(""):Object(e)}},function(e,t,r){"use strict";var n=r(26),i=Math.max,a=Math.min;e.exports=function(e,t){return(e=n(e))<0?i(e+t,0):a(e,t)}},function(e,t,r){"use strict";var n=r(30)("unscopables"),i=Array.prototype;void 0==i[n]&&r(10)(i,n,{}),e.exports=function(e){i[n][e]=!0}},function(e,t,r){"use strict";r(47),r(62),e.exports=r(9).Array.from},function(e,t,r){"use strict";var n=r(48)(!0);r(49)(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,r=this._i;return r>=t.length?{value:void 0,done:!0}:(e=n(t,r),this._i+=e.length,{value:e,done:!1})}))},function(e,t,r){"use strict";var n=r(26),i=r(33);e.exports=function(e){return function(t,r){var a,o,s=String(i(t)),u=n(r),c=s.length;return u<0||u>=c?e?"":void 0:(a=s.charCodeAt(u))<55296||a>56319||u+1===c||(o=s.charCodeAt(u+1))<56320||o>57343?e?s.charAt(u):a:e?s.slice(u,u+2):o-56320+(a-55296<<10)+65536}}},function(e,t,r){"use strict";var n=r(32),i=r(7),a=r(20),o=r(10),s=r(50),u=r(51),c=r(59),l=r(60),f=r(30)("iterator"),d=!([].keys&&"next"in[].keys()),h="keys",p="values",v=function(){return this};e.exports=function(e,t,r,m,g,y,b){u(r,t,m);var _,S,A,w=function(e){if(!d&&e in R)return R[e];switch(e){case h:case p:return function(){return new r(this,e)}}return function(){return new r(this,e)}},k=t+" Iterator",x=g==p,P=!1,R=e.prototype,C=R[f]||R["@@iterator"]||g&&R[g],E=C||w(g),T=g?x?w("entries"):E:void 0,O="Array"==t&&R.entries||C;if(O&&(A=l(O.call(new e)))!==Object.prototype&&A.next&&(c(A,k,!0),n||"function"==typeof A[f]||o(A,f,v)),x&&C&&C.name!==p&&(P=!0,E=function(){return C.call(this)}),n&&!b||!d&&!P&&R[f]||o(R,f,E),s[t]=E,s[k]=v,g)if(_={values:x?E:w(p),keys:y?E:w(h),entries:T},b)for(S in _)S in R||a(R,S,_[S]);else i(i.P+i.F*(d||P),t,_);return _}},function(e,t,r){"use strict";e.exports={}},function(e,t,r){"use strict";var n=r(52),i=r(19),a=r(59),o={};r(10)(o,r(30)("iterator"),(function(){return this})),e.exports=function(e,t,r){e.prototype=n(o,{next:i(1,r)}),a(e,t+" Iterator")}},function(e,t,r){"use strict";var n=r(12),i=r(53),a=r(57),o=r(56)("IE_PROTO"),s=function(){},u="prototype",c=function(){var e,t=r(17)("iframe"),n=a.length;for(t.style.display="none",r(58).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),c=e.F;n--;)delete c[u][a[n]];return c()};e.exports=Object.create||function(e,t){var r;return null!==e?(s[u]=n(e),r=new s,s[u]=null,r[o]=e):r=c(),void 0===t?r:i(r,t)}},function(e,t,r){"use strict";var n=r(11),i=r(12),a=r(54);e.exports=r(15)?Object.defineProperties:function(e,t){i(e);for(var r,o=a(t),s=o.length,u=0;s>u;)n.f(e,r=o[u++],t[r]);return e}},function(e,t,r){"use strict";var n=r(55),i=r(57);e.exports=Object.keys||function(e){return n(e,i)}},function(e,t,r){"use strict";var n=r(21),i=r(42),a=r(41)(!1),o=r(56)("IE_PROTO");e.exports=function(e,t){var r,s=i(e),u=0,c=[];for(r in s)r!=o&&n(s,r)&&c.push(r);for(;t.length>u;)n(s,r=t[u++])&&(~a(c,r)||c.push(r));return c}},function(e,t,r){"use strict";var n=r(31)("keys"),i=r(22);e.exports=function(e){return n[e]||(n[e]=i(e))}},function(e,t,r){"use strict";e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,r){"use strict";var n=r(8).document;e.exports=n&&n.documentElement},function(e,t,r){"use strict";var n=r(11).f,i=r(21),a=r(30)("toStringTag");e.exports=function(e,t,r){e&&!i(e=r?e:e.prototype,a)&&n(e,a,{configurable:!0,value:t})}},function(e,t,r){"use strict";var n=r(21),i=r(61),a=r(56)("IE_PROTO"),o=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=i(e),n(e,a)?e[a]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?o:null}},function(e,t,r){"use strict";var n=r(33);e.exports=function(e){return Object(n(e))}},function(e,t,r){"use strict";var n=r(23),i=r(7),a=r(61),o=r(63),s=r(64),u=r(25),c=r(65),l=r(66);i(i.S+i.F*!r(68)((function(e){Array.from(e)})),"Array",{from:function(e){var t,r,i,f,d=a(e),h="function"==typeof this?this:Array,p=arguments.length,v=p>1?arguments[1]:void 0,m=void 0!==v,g=0,y=l(d);if(m&&(v=n(v,p>2?arguments[2]:void 0,2)),void 0==y||h==Array&&s(y))for(r=new h(t=u(d.length));t>g;g++)c(r,g,m?v(d[g],g):d[g]);else for(f=y.call(d),r=new h;!(i=f.next()).done;g++)c(r,g,m?o(f,v,[i.value,g],!0):i.value);return r.length=g,r}})},function(e,t,r){"use strict";var n=r(12);e.exports=function(e,t,r,i){try{return i?t(n(r)[0],r[1]):t(r)}catch(o){var a=e.return;throw void 0!==a&&n(a.call(e)),o}}},function(e,t,r){"use strict";var n=r(50),i=r(30)("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(n.Array===e||a[i]===e)}},function(e,t,r){"use strict";var n=r(11),i=r(19);e.exports=function(e,t,r){t in e?n.f(e,t,i(0,r)):e[t]=r}},function(e,t,r){"use strict";var n=r(67),i=r(30)("iterator"),a=r(50);e.exports=r(9).getIteratorMethod=function(e){if(void 0!=e)return e[i]||e["@@iterator"]||a[n(e)]}},function(e,t,r){"use strict";var n=r(29),i=r(30)("toStringTag"),a="Arguments"==n(function(){return arguments}());e.exports=function(e){var t,r,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=function(e,t){try{return e[t]}catch(r){}}(t=Object(e),i))?r:a?n(t):"Object"==(o=n(t))&&"function"==typeof t.callee?"Arguments":o}},function(e,t,r){"use strict";var n=r(30)("iterator"),i=!1;try{var a=[7][n]();a.return=function(){i=!0},Array.from(a,(function(){throw 2}))}catch(o){}e.exports=function(e,t){if(!t&&!i)return!1;var r=!1;try{var a=[7],s=a[n]();s.next=function(){return{done:r=!0}},a[n]=function(){return s},e(a)}catch(o){}return r}},function(e,t,r){"use strict";r(70),e.exports=r(9).Object.assign},function(e,t,r){"use strict";var n=r(7);n(n.S+n.F,"Object",{assign:r(71)})},function(e,t,r){"use strict";var n=r(54),i=r(72),a=r(73),o=r(61),s=r(43),u=Object.assign;e.exports=!u||r(16)((function(){var e={},t={},r=Symbol(),n="abcdefghijklmnopqrst";return e[r]=7,n.split("").forEach((function(e){t[e]=e})),7!=u({},e)[r]||Object.keys(u({},t)).join("")!=n}))?function(e,t){for(var r=o(e),u=arguments.length,c=1,l=i.f,f=a.f;u>c;)for(var d,h=s(arguments[c++]),p=l?n(h).concat(l(h)):n(h),v=p.length,m=0;v>m;)f.call(h,d=p[m++])&&(r[d]=h[d]);return r}:u},function(e,t,r){"use strict";t.f=Object.getOwnPropertySymbols},function(e,t,r){"use strict";t.f={}.propertyIsEnumerable},function(e,t,r){"use strict";r(75),e.exports=r(9).Math.log2},function(e,t,r){"use strict";var n=r(7);n(n.S,"Math",{log2:function(e){return Math.log(e)/Math.LN2}})},function(e,t,r){"use strict";r(77),e.exports=r(9).Number.isNaN},function(e,t,r){"use strict";var n=r(7);n(n.S,"Number",{isNaN:function(e){return e!=e}})},function(e,t,r){"use strict";r(79),e.exports=r(9).Number.isInteger},function(e,t,r){"use strict";var n=r(7);n(n.S,"Number",{isInteger:r(80)})},function(e,t,r){"use strict";var n=r(13),i=Math.floor;e.exports=function(e){return!n(e)&&isFinite(e)&&i(e)===e}},function(e,t,r){"use strict";r(82),r(47),r(83),r(86),r(99),r(100),e.exports=r(9).Promise},function(e,t,r){"use strict";var n=r(67),i={};i[r(30)("toStringTag")]="z",i+""!="[object z]"&&r(20)(Object.prototype,"toString",(function(){return"[object "+n(this)+"]"}),!0)},function(e,t,r){"use strict";for(var n=r(84),i=r(54),a=r(20),o=r(8),s=r(10),u=r(50),c=r(30),l=c("iterator"),f=c("toStringTag"),d=u.Array,h={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},p=i(h),v=0;v<p.length;v++){var m,g=p[v],y=h[g],b=o[g],_=b&&b.prototype;if(_&&(_[l]||s(_,l,d),_[f]||s(_,f,g),u[g]=d,y))for(m in n)_[m]||a(_,m,n[m],!0)}},function(e,t,r){"use strict";var n=r(45),i=r(85),a=r(50),o=r(42);e.exports=r(49)(Array,"Array",(function(e,t){this._t=o(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,r=this._i++;return!e||r>=e.length?(this._t=void 0,i(1)):i(0,"keys"==t?r:"values"==t?e[r]:[r,e[r]])}),"values"),a.Arguments=a.Array,n("keys"),n("values"),n("entries")},function(e,t,r){"use strict";e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,r){"use strict";var n,i,a,o,s=r(32),u=r(8),c=r(23),l=r(67),f=r(7),d=r(13),h=r(24),p=r(87),v=r(88),m=r(89),g=r(90).set,y=r(92)(),b=r(93),_=r(94),S=r(95),A=r(96),w="Promise",k=u.TypeError,x=u.process,P=x&&x.versions,R=P&&P.v8||"",C=u[w],E="process"==l(x),T=function(){},O=i=b.f,L=!!function(){try{var e=C.resolve(1),t=(e.constructor={})[r(30)("species")]=function(e){e(T,T)};return(E||"function"==typeof PromiseRejectionEvent)&&e.then(T)instanceof t&&0!==R.indexOf("6.6")&&-1===S.indexOf("Chrome/66")}catch(n){}}(),I=function(e){var t;return!(!d(e)||"function"!=typeof(t=e.then))&&t},F=function(e,t){if(!e._n){e._n=!0;var r=e._c;y((function(){for(var n=e._v,i=1==e._s,a=0,o=function(t){var r,a,o,s=i?t.ok:t.fail,u=t.resolve,c=t.reject,l=t.domain;try{s?(i||(2==e._h&&M(e),e._h=1),!0===s?r=n:(l&&l.enter(),r=s(n),l&&(l.exit(),o=!0)),r===t.promise?c(k("Promise-chain cycle")):(a=I(r))?a.call(r,u,c):u(r)):c(n)}catch(f){l&&!o&&l.exit(),c(f)}};r.length>a;)o(r[a++]);e._c=[],e._n=!1,t&&!e._h&&D(e)}))}},D=function(e){g.call(u,(function(){var t,r,n,i=e._v,a=j(e);if(a&&(t=_((function(){E?x.emit("unhandledRejection",i,e):(r=u.onunhandledrejection)?r({promise:e,reason:i}):(n=u.console)&&n.error&&n.error("Unhandled promise rejection",i)})),e._h=E||j(e)?2:1),e._a=void 0,a&&t.e)throw t.v}))},j=function(e){return 1!==e._h&&0===(e._a||e._c).length},M=function(e){g.call(u,(function(){var t;E?x.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})}))},N=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),F(t,!0))},q=function e(t){var r,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===t)throw k("Promise can't be resolved itself");(r=I(t))?y((function(){var i={_w:n,_d:!1};try{r.call(t,c(e,i,1),c(N,i,1))}catch(a){N.call(i,a)}})):(n._v=t,n._s=1,F(n,!1))}catch(i){N.call({_w:n,_d:!1},i)}}};L||(C=function(e){p(this,C,w,"_h"),h(e),n.call(this);try{e(c(q,this,1),c(N,this,1))}catch(t){N.call(this,t)}},(n=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=r(97)(C.prototype,{then:function(e,t){var r=O(m(this,C));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=E?x.domain:void 0,this._c.push(r),this._a&&this._a.push(r),this._s&&F(this,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),a=function(){var e=new n;this.promise=e,this.resolve=c(q,e,1),this.reject=c(N,e,1)},b.f=O=function(e){return e===C||e===o?new a(e):i(e)}),f(f.G+f.W+f.F*!L,{Promise:C}),r(59)(C,w),r(98)(w),o=r(9)[w],f(f.S+f.F*!L,w,{reject:function(e){var t=O(this);return(0,t.reject)(e),t.promise}}),f(f.S+f.F*(s||!L),w,{resolve:function(e){return A(s&&this===o?C:this,e)}}),f(f.S+f.F*!(L&&r(68)((function(e){C.all(e).catch(T)}))),w,{all:function(e){var t=this,r=O(t),n=r.resolve,i=r.reject,a=_((function(){var r=[],a=0,o=1;v(e,!1,(function(e){var s=a++,u=!1;r.push(void 0),o++,t.resolve(e).then((function(e){u||(u=!0,r[s]=e,--o||n(r))}),i)})),--o||n(r)}));return a.e&&i(a.v),r.promise},race:function(e){var t=this,r=O(t),n=r.reject,i=_((function(){v(e,!1,(function(e){t.resolve(e).then(r.resolve,n)}))}));return i.e&&n(i.v),r.promise}})},function(e,t,r){"use strict";e.exports=function(e,t,r,n){if(!(e instanceof t)||void 0!==n&&n in e)throw TypeError(r+": incorrect invocation!");return e}},function(e,t,r){"use strict";var n=r(23),i=r(63),a=r(64),o=r(12),s=r(25),u=r(66),c={},l={},f=e.exports=function(e,t,r,f,d){var h,p,v,m,g=d?function(){return e}:u(e),y=n(r,f,t?2:1),b=0;if("function"!=typeof g)throw TypeError(e+" is not iterable!");if(a(g)){for(h=s(e.length);h>b;b++)if((m=t?y(o(p=e[b])[0],p[1]):y(e[b]))===c||m===l)return m}else for(v=g.call(e);!(p=v.next()).done;)if((m=i(v,y,p.value,t))===c||m===l)return m};f.BREAK=c,f.RETURN=l},function(e,t,r){"use strict";var n=r(12),i=r(24),a=r(30)("species");e.exports=function(e,t){var r,o=n(e).constructor;return void 0===o||void 0==(r=n(o)[a])?t:i(r)}},function(e,t,r){"use strict";var n,i,a,o=r(23),s=r(91),u=r(58),c=r(17),l=r(8),f=l.process,d=l.setImmediate,h=l.clearImmediate,p=l.MessageChannel,v=l.Dispatch,m=0,g={},y="onreadystatechange",b=function(){var e=+this;if(g.hasOwnProperty(e)){var t=g[e];delete g[e],t()}},_=function(e){b.call(e.data)};d&&h||(d=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return g[++m]=function(){s("function"==typeof e?e:Function(e),t)},n(m),m},h=function(e){delete g[e]},"process"==r(29)(f)?n=function(e){f.nextTick(o(b,e,1))}:v&&v.now?n=function(e){v.now(o(b,e,1))}:p?(a=(i=new p).port2,i.port1.onmessage=_,n=o(a.postMessage,a,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(n=function(e){l.postMessage(e+"","*")},l.addEventListener("message",_,!1)):n=y in c("script")?function(e){u.appendChild(c("script"))[y]=function(){u.removeChild(this),b.call(e)}}:function(e){setTimeout(o(b,e,1),0)}),e.exports={set:d,clear:h}},function(e,t,r){"use strict";e.exports=function(e,t,r){var n=void 0===r;switch(t.length){case 0:return n?e():e.call(r);case 1:return n?e(t[0]):e.call(r,t[0]);case 2:return n?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return n?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return n?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},function(e,t,r){"use strict";var n=r(8),i=r(90).set,a=n.MutationObserver||n.WebKitMutationObserver,o=n.process,s=n.Promise,u="process"==r(29)(o);e.exports=function(){var e,t,r,c=function(){var n,i;for(u&&(n=o.domain)&&n.exit();e;){i=e.fn,e=e.next;try{i()}catch(a){throw e?r():t=void 0,a}}t=void 0,n&&n.enter()};if(u)r=function(){o.nextTick(c)};else if(!a||n.navigator&&n.navigator.standalone)if(s&&s.resolve){var l=s.resolve(void 0);r=function(){l.then(c)}}else r=function(){i.call(n,c)};else{var f=!0,d=document.createTextNode("");new a(c).observe(d,{characterData:!0}),r=function(){d.data=f=!f}}return function(n){var i={fn:n,next:void 0};t&&(t.next=i),e||(e=i,r()),t=i}}},function(e,t,r){"use strict";var n=r(24);function i(e){var t,r;this.promise=new e((function(e,n){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=n})),this.resolve=n(t),this.reject=n(r)}e.exports.f=function(e){return new i(e)}},function(e,t,r){"use strict";e.exports=function(e){try{return{e:!1,v:e()}}catch(t){return{e:!0,v:t}}}},function(e,t,r){"use strict";var n=r(8).navigator;e.exports=n&&n.userAgent||""},function(e,t,r){"use strict";var n=r(12),i=r(13),a=r(93);e.exports=function(e,t){if(n(e),i(t)&&t.constructor===e)return t;var r=a.f(e);return(0,r.resolve)(t),r.promise}},function(e,t,r){"use strict";var n=r(20);e.exports=function(e,t,r){for(var i in t)n(e,i,t[i],r);return e}},function(e,t,r){"use strict";var n=r(8),i=r(11),a=r(15),o=r(30)("species");e.exports=function(e){var t=n[e];a&&t&&!t[o]&&i.f(t,o,{configurable:!0,get:function(){return this}})}},function(e,t,r){"use strict";var n=r(7),i=r(9),a=r(8),o=r(89),s=r(96);n(n.P+n.R,"Promise",{finally:function(e){var t=o(this,i.Promise||a.Promise),r="function"==typeof e;return this.then(r?function(r){return s(t,e()).then((function(){return r}))}:e,r?function(r){return s(t,e()).then((function(){throw r}))}:e)}})},function(e,t,r){"use strict";var n=r(7),i=r(93),a=r(94);n(n.S,"Promise",{try:function(e){var t=i.f(this),r=a(e);return(r.e?t.reject:t.resolve)(r.v),t.promise}})},function(e,t,r){"use strict";r(82),r(83),r(102),r(114),r(116),e.exports=r(9).WeakMap},function(e,t,r){"use strict";var n,i=r(103)(0),a=r(20),o=r(107),s=r(71),u=r(108),c=r(13),l=r(16),f=r(109),d="WeakMap",h=o.getWeak,p=Object.isExtensible,v=u.ufstore,m={},g=function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}},y={get:function(e){if(c(e)){var t=h(e);return!0===t?v(f(this,d)).get(e):t?t[this._i]:void 0}},set:function(e,t){return u.def(f(this,d),e,t)}},b=e.exports=r(110)(d,g,y,u,!0,!0);l((function(){return 7!=(new b).set((Object.freeze||Object)(m),7).get(m)}))&&(s((n=u.getConstructor(g,d)).prototype,y),o.NEED=!0,i(["delete","has","get","set"],(function(e){var t=b.prototype,r=t[e];a(t,e,(function(t,i){if(c(t)&&!p(t)){this._f||(this._f=new n);var a=this._f[e](t,i);return"set"==e?this:a}return r.call(this,t,i)}))})))},function(e,t,r){"use strict";var n=r(23),i=r(43),a=r(61),o=r(25),s=r(104);e.exports=function(e,t){var r=1==e,u=2==e,c=3==e,l=4==e,f=6==e,d=5==e||f,h=t||s;return function(t,s,p){for(var v,m,g=a(t),y=i(g),b=n(s,p,3),_=o(y.length),S=0,A=r?h(t,_):u?h(t,0):void 0;_>S;S++)if((d||S in y)&&(m=b(v=y[S],S,g),e))if(r)A[S]=m;else if(m)switch(e){case 3:return!0;case 5:return v;case 6:return S;case 2:A.push(v)}else if(l)return!1;return f?-1:c||l?l:A}}},function(e,t,r){"use strict";var n=r(105);e.exports=function(e,t){return new(n(e))(t)}},function(e,t,r){"use strict";var n=r(13),i=r(106),a=r(30)("species");e.exports=function(e){var t;return i(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!i(t.prototype)||(t=void 0),n(t)&&null===(t=t[a])&&(t=void 0)),void 0===t?Array:t}},function(e,t,r){"use strict";var n=r(29);e.exports=Array.isArray||function(e){return"Array"==n(e)}},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var i=r(22)("meta"),a=r(13),o=r(21),s=r(11).f,u=0,c=Object.isExtensible||function(){return!0},l=!r(16)((function(){return c(Object.preventExtensions({}))})),f=function(e){s(e,i,{value:{i:"O"+ ++u,w:{}}})},d=e.exports={KEY:i,NEED:!1,fastKey:function(e,t){if(!a(e))return"symbol"==n(e)?e:("string"==typeof e?"S":"P")+e;if(!o(e,i)){if(!c(e))return"F";if(!t)return"E";f(e)}return e[i].i},getWeak:function(e,t){if(!o(e,i)){if(!c(e))return!0;if(!t)return!1;f(e)}return e[i].w},onFreeze:function(e){return l&&d.NEED&&c(e)&&!o(e,i)&&f(e),e}}},function(e,t,r){"use strict";var n=r(97),i=r(107).getWeak,a=r(12),o=r(13),s=r(87),u=r(88),c=r(103),l=r(21),f=r(109),d=c(5),h=c(6),p=0,v=function(e){return e._l||(e._l=new m)},m=function(){this.a=[]},g=function(e,t){return d(e.a,(function(e){return e[0]===t}))};m.prototype={get:function(e){var t=g(this,e);if(t)return t[1]},has:function(e){return!!g(this,e)},set:function(e,t){var r=g(this,e);r?r[1]=t:this.a.push([e,t])},delete:function(e){var t=h(this.a,(function(t){return t[0]===e}));return~t&&this.a.splice(t,1),!!~t}},e.exports={getConstructor:function(e,t,r,a){var c=e((function(e,n){s(e,c,t,"_i"),e._t=t,e._i=p++,e._l=void 0,void 0!=n&&u(n,r,e[a],e)}));return n(c.prototype,{delete:function(e){if(!o(e))return!1;var r=i(e);return!0===r?v(f(this,t)).delete(e):r&&l(r,this._i)&&delete r[this._i]},has:function(e){if(!o(e))return!1;var r=i(e);return!0===r?v(f(this,t)).has(e):r&&l(r,this._i)}}),c},def:function(e,t,r){var n=i(a(t),!0);return!0===n?v(e).set(t,r):n[e._i]=r,e},ufstore:v}},function(e,t,r){"use strict";var n=r(13);e.exports=function(e,t){if(!n(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e}},function(e,t,r){"use strict";var n=r(8),i=r(7),a=r(20),o=r(97),s=r(107),u=r(88),c=r(87),l=r(13),f=r(16),d=r(68),h=r(59),p=r(111);e.exports=function(e,t,r,v,m,g){var y=n[e],b=y,_=m?"set":"add",S=b&&b.prototype,A={},w=function(e){var t=S[e];a(S,e,"delete"==e||"has"==e?function(e){return!(g&&!l(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return g&&!l(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,r){return t.call(this,0===e?0:e,r),this})};if("function"==typeof b&&(g||S.forEach&&!f((function(){(new b).entries().next()})))){var k=new b,x=k[_](g?{}:-0,1)!=k,P=f((function(){k.has(1)})),R=d((function(e){new b(e)})),C=!g&&f((function(){for(var e=new b,t=5;t--;)e[_](t,t);return!e.has(-0)}));R||((b=t((function(t,r){c(t,b,e);var n=p(new y,t,b);return void 0!=r&&u(r,m,n[_],n),n}))).prototype=S,S.constructor=b),(P||C)&&(w("delete"),w("has"),m&&w("get")),(C||x)&&w(_),g&&S.clear&&delete S.clear}else b=v.getConstructor(t,e,m,_),o(b.prototype,r),s.NEED=!0;return h(b,e),A[e]=b,i(i.G+i.W+i.F*(b!=y),A),g||v.setStrong(b,e,m),b}},function(e,t,r){"use strict";var n=r(13),i=r(112).set;e.exports=function(e,t,r){var a,o=t.constructor;return o!==r&&"function"==typeof o&&(a=o.prototype)!==r.prototype&&n(a)&&i&&i(e,a),e}},function(e,t,r){"use strict";var n=r(13),i=r(12),a=function(e,t){if(i(e),!n(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=r(23)(Function.call,r(113).f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(i){t=!0}return function(e,r){return a(e,r),t?e.__proto__=r:n(e,r),e}}({},!1):void 0),check:a}},function(e,t,r){"use strict";var n=r(73),i=r(19),a=r(42),o=r(18),s=r(21),u=r(14),c=Object.getOwnPropertyDescriptor;t.f=r(15)?c:function(e,t){if(e=a(e),t=o(t,!0),u)try{return c(e,t)}catch(r){}if(s(e,t))return i(!n.f.call(e,t),e[t])}},function(e,t,r){"use strict";r(115)("WeakMap")},function(e,t,r){"use strict";var n=r(7);e.exports=function(e){n(n.S,e,{of:function(){for(var e=arguments.length,t=new Array(e);e--;)t[e]=arguments[e];return new this(t)}})}},function(e,t,r){"use strict";r(117)("WeakMap")},function(e,t,r){"use strict";var n=r(7),i=r(24),a=r(23),o=r(88);e.exports=function(e){n(n.S,e,{from:function(e){var t,r,n,s,u=arguments[1];return i(this),(t=void 0!==u)&&i(u),void 0==e?new this:(r=[],t?(n=0,s=a(u,arguments[2],2),o(e,!1,(function(e){r.push(s(e,n++))}))):o(e,!1,r.push,r),new this(r))}})}},function(e,t,r){"use strict";r(82),r(83),r(119),r(120),r(121),e.exports=r(9).WeakSet},function(e,t,r){"use strict";var n=r(108),i=r(109),a="WeakSet";r(110)(a,(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(e){return n.def(i(this,a),e,!0)}},n,!1,!0)},function(e,t,r){"use strict";r(115)("WeakSet")},function(e,t,r){"use strict";r(117)("WeakSet")},function(e,t,r){"use strict";r(123),e.exports=r(9).String.codePointAt},function(e,t,r){"use strict";var n=r(7),i=r(48)(!1);n(n.P,"String",{codePointAt:function(e){return i(this,e)}})},function(e,t,r){"use strict";r(125),e.exports=r(9).String.fromCodePoint},function(e,t,r){"use strict";var n=r(7),i=r(44),a=String.fromCharCode,o=String.fromCodePoint;n(n.S+n.F*(!!o&&1!=o.length),"String",{fromCodePoint:function(e){for(var t,r=[],n=arguments.length,o=0;n>o;){if(t=+arguments[o++],i(t,1114111)!==t)throw RangeError(t+" is not a valid code point");r.push(t<65536?a(t):a(55296+((t-=65536)>>10),t%1024+56320))}return r.join("")}})},function(e,t,r){"use strict";r(127),r(82),e.exports=r(9).Symbol},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var i=r(8),a=r(21),o=r(15),s=r(7),u=r(20),c=r(107).KEY,l=r(16),f=r(31),d=r(59),h=r(22),p=r(30),v=r(128),m=r(129),g=r(130),y=r(106),b=r(12),_=r(13),S=r(42),A=r(18),w=r(19),k=r(52),x=r(131),P=r(113),R=r(11),C=r(54),E=P.f,T=R.f,O=x.f,L=i.Symbol,I=i.JSON,F=I&&I.stringify,D="prototype",j=p("_hidden"),M=p("toPrimitive"),N={}.propertyIsEnumerable,q=f("symbol-registry"),W=f("symbols"),U=f("op-symbols"),B=Object[D],G="function"==typeof L,z=i.QObject,H=!z||!z[D]||!z[D].findChild,X=o&&l((function(){return 7!=k(T({},"a",{get:function(){return T(this,"a",{value:7}).a}})).a}))?function(e,t,r){var n=E(B,t);n&&delete B[t],T(e,t,r),n&&e!==B&&T(B,t,n)}:T,V=function(e){var t=W[e]=k(L[D]);return t._k=e,t},Y=G&&"symbol"==n(L.iterator)?function(e){return"symbol"==n(e)}:function(e){return e instanceof L},K=function(e,t,r){return e===B&&K(U,t,r),b(e),t=A(t,!0),b(r),a(W,t)?(r.enumerable?(a(e,j)&&e[j][t]&&(e[j][t]=!1),r=k(r,{enumerable:w(0,!1)})):(a(e,j)||T(e,j,w(1,{})),e[j][t]=!0),X(e,t,r)):T(e,t,r)},Q=function(e,t){b(e);for(var r,n=g(t=S(t)),i=0,a=n.length;a>i;)K(e,r=n[i++],t[r]);return e},J=function(e){var t=N.call(this,e=A(e,!0));return!(this===B&&a(W,e)&&!a(U,e))&&(!(t||!a(this,e)||!a(W,e)||a(this,j)&&this[j][e])||t)},Z=function(e,t){if(e=S(e),t=A(t,!0),e!==B||!a(W,t)||a(U,t)){var r=E(e,t);return!r||!a(W,t)||a(e,j)&&e[j][t]||(r.enumerable=!0),r}},$=function(e){for(var t,r=O(S(e)),n=[],i=0;r.length>i;)a(W,t=r[i++])||t==j||t==c||n.push(t);return n},ee=function(e){for(var t,r=e===B,n=O(r?U:S(e)),i=[],o=0;n.length>o;)!a(W,t=n[o++])||r&&!a(B,t)||i.push(W[t]);return i};G||(L=function(){if(this instanceof L)throw TypeError("Symbol is not a constructor!");var e=h(arguments.length>0?arguments[0]:void 0);return o&&H&&X(B,e,{configurable:!0,set:function t(r){this===B&&t.call(U,r),a(this,j)&&a(this[j],e)&&(this[j][e]=!1),X(this,e,w(1,r))}}),V(e)},u(L[D],"toString",(function(){return this._k})),P.f=Z,R.f=K,r(132).f=x.f=$,r(73).f=J,r(72).f=ee,o&&!r(32)&&u(B,"propertyIsEnumerable",J,!0),v.f=function(e){return V(p(e))}),s(s.G+s.W+s.F*!G,{Symbol:L});for(var te="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),re=0;te.length>re;)p(te[re++]);for(var ne=C(p.store),ie=0;ne.length>ie;)m(ne[ie++]);s(s.S+s.F*!G,"Symbol",{for:function(e){return a(q,e+="")?q[e]:q[e]=L(e)},keyFor:function(e){if(!Y(e))throw TypeError(e+" is not a symbol!");for(var t in q)if(q[t]===e)return t},useSetter:function(){H=!0},useSimple:function(){H=!1}}),s(s.S+s.F*!G,"Object",{create:function(e,t){return void 0===t?k(e):Q(k(e),t)},defineProperty:K,defineProperties:Q,getOwnPropertyDescriptor:Z,getOwnPropertyNames:$,getOwnPropertySymbols:ee}),I&&s(s.S+s.F*(!G||l((function(){var e=L();return"[null]"!=F([e])||"{}"!=F({a:e})||"{}"!=F(Object(e))}))),"JSON",{stringify:function(e){for(var t,r,n=[e],i=1;arguments.length>i;)n.push(arguments[i++]);if(r=t=n[1],(_(t)||void 0!==e)&&!Y(e))return y(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!Y(t))return t}),n[1]=t,F.apply(I,n)}}),L[D][M]||r(10)(L[D],M,L[D].valueOf),d(L,"Symbol"),d(Math,"Math",!0),d(i.JSON,"JSON",!0)},function(e,t,r){"use strict";t.f=r(30)},function(e,t,r){"use strict";var n=r(8),i=r(9),a=r(32),o=r(128),s=r(11).f;e.exports=function(e){var t=i.Symbol||(i.Symbol=a?{}:n.Symbol||{});"_"==e.charAt(0)||e in t||s(t,e,{value:o.f(e)})}},function(e,t,r){"use strict";var n=r(54),i=r(72),a=r(73);e.exports=function(e){var t=n(e),r=i.f;if(r)for(var o,s=r(e),u=a.f,c=0;s.length>c;)u.call(e,o=s[c++])&&t.push(o);return t}},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var i=r(42),a=r(132).f,o={}.toString,s="object"==("undefined"===typeof window?"undefined":n(window))&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];e.exports.f=function(e){return s&&"[object Window]"==o.call(e)?function(e){try{return a(e)}catch(t){return s.slice()}}(e):a(i(e))}},function(e,t,r){"use strict";var n=r(55),i=r(57).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,i)}},function(e,t,r){"use strict";r(134),e.exports=r(9).String.padStart},function(e,t,r){"use strict";var n=r(7),i=r(135),a=r(95);n(n.P+n.F*/Version\/10\.\d+(\.\d+)? Safari\//.test(a),"String",{padStart:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0,!0)}})},function(e,t,r){"use strict";var n=r(25),i=r(136),a=r(33);e.exports=function(e,t,r,o){var s=String(a(e)),u=s.length,c=void 0===r?" ":String(r),l=n(t);if(l<=u||""==c)return s;var f=l-u,d=i.call(c,Math.ceil(f/c.length));return d.length>f&&(d=d.slice(0,f)),o?d+s:s+d}},function(e,t,r){"use strict";var n=r(26),i=r(33);e.exports=function(e){var t=String(i(this)),r="",a=n(e);if(a<0||a==1/0)throw RangeError("Count can't be negative");for(;a>0;(a>>>=1)&&(t+=t))1&a&&(r+=t);return r}},function(e,t,r){"use strict";r(138),e.exports=r(9).String.padEnd},function(e,t,r){"use strict";var n=r(7),i=r(135),a=r(95);n(n.P+n.F*/Version\/10\.\d+(\.\d+)? Safari\//.test(a),"String",{padEnd:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0,!1)}})},function(e,t,r){"use strict";r(140),e.exports=r(9).Object.values},function(e,t,r){"use strict";var n=r(7),i=r(141)(!1);n(n.S,"Object",{values:function(e){return i(e)}})},function(e,t,r){"use strict";var n=r(54),i=r(42),a=r(73).f;e.exports=function(e){return function(t){for(var r,o=i(t),s=n(o),u=s.length,c=0,l=[];u>c;)a.call(o,r=s[c++])&&l.push(e?[r,o[r]]:o[r]);return l}}},function(e,t,r){"use strict";var n=!1;if("undefined"!==typeof ReadableStream)try{new ReadableStream({start:function(e){e.close()}}),n=!0}catch(i){}t.ReadableStream=n?ReadableStream:r(143).ReadableStream},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}!function(e,t){for(var r in t)e[r]=t[r]}(t,function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.i=function(e){return e},r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=7)}([function(e,t,r){var i="function"===typeof Symbol&&"symbol"===n(Symbol.iterator)?function(e){return n(e)}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":n(e)},a=r(1).assert;function o(e){return"string"===typeof e||"symbol"===("undefined"===typeof e?"undefined":i(e))}function s(e,t,r){if("function"!==typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}t.typeIsObject=function(e){return"object"===("undefined"===typeof e?"undefined":i(e))&&null!==e||"function"===typeof e},t.createDataProperty=function(e,r,n){a(t.typeIsObject(e)),Object.defineProperty(e,r,{value:n,writable:!0,enumerable:!0,configurable:!0})},t.createArrayFromList=function(e){return e.slice()},t.ArrayBufferCopy=function(e,t,r,n,i){new Uint8Array(e).set(new Uint8Array(r,n,i),t)},t.CreateIterResultObject=function(e,t){a("boolean"===typeof t);var r={};return Object.defineProperty(r,"value",{value:e,enumerable:!0,writable:!0,configurable:!0}),Object.defineProperty(r,"done",{value:t,enumerable:!0,writable:!0,configurable:!0}),r},t.IsFiniteNonNegativeNumber=function(e){return!Number.isNaN(e)&&e!==1/0&&!(e<0)},t.InvokeOrNoop=function(e,t,r){a(void 0!==e),a(o(t)),a(Array.isArray(r));var n=e[t];if(void 0!==n)return s(n,e,r)},t.PromiseInvokeOrNoop=function(e,r,n){a(void 0!==e),a(o(r)),a(Array.isArray(n));try{return Promise.resolve(t.InvokeOrNoop(e,r,n))}catch(i){return Promise.reject(i)}},t.PromiseInvokeOrPerformFallback=function(e,t,r,n,i){a(void 0!==e),a(o(t)),a(Array.isArray(r)),a(Array.isArray(i));var u=void 0;try{u=e[t]}catch(c){return Promise.reject(c)}if(void 0===u)return n.apply(null,i);try{return Promise.resolve(s(u,e,r))}catch(l){return Promise.reject(l)}},t.TransferArrayBuffer=function(e){return e.slice()},t.ValidateAndNormalizeHighWaterMark=function(e){if(e=Number(e),Number.isNaN(e)||e<0)throw new RangeError("highWaterMark property of a queuing strategy must be non-negative and non-NaN");return e},t.ValidateAndNormalizeQueuingStrategy=function(e,r){if(void 0!==e&&"function"!==typeof e)throw new TypeError("size property of a queuing strategy must be a function");return{size:e,highWaterMark:r=t.ValidateAndNormalizeHighWaterMark(r)}}},function(e,t,r){function n(e){this.name="AssertionError",this.message=e||"",this.stack=(new Error).stack}n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,e.exports={rethrowAssertionErrorRejection:function(e){e&&e.constructor===n&&setTimeout((function(){throw e}),0)},AssertionError:n,assert:function(e,t){if(!e)throw new n(t)}}},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(0),o=a.InvokeOrNoop,s=a.PromiseInvokeOrNoop,u=a.ValidateAndNormalizeQueuingStrategy,c=a.typeIsObject,l=r(1),f=l.assert,d=l.rethrowAssertionErrorRejection,h=r(3),p=h.DequeueValue,v=h.EnqueueValueWithSize,m=h.PeekQueueValue,g=h.ResetQueue,y=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.size,a=r.highWaterMark,o=void 0===a?1:a;if(i(this,e),this._state="writable",this._storedError=void 0,this._writer=void 0,this._writableStreamController=void 0,this._writeRequests=[],this._inFlightWriteRequest=void 0,this._closeRequest=void 0,this._inFlightCloseRequest=void 0,this._pendingAbortRequest=void 0,this._backpressure=!1,void 0!==t.type)throw new RangeError("Invalid type is specified");this._writableStreamController=new N(this,t,n,o),this._writableStreamController.__startSteps()}return n(e,[{key:"abort",value:function(e){return!1===_(this)?Promise.reject(z("abort")):!0===S(this)?Promise.reject(new TypeError("Cannot abort a stream that already has a writer")):A(this,e)}},{key:"getWriter",value:function(){if(!1===_(this))throw z("getWriter");return b(this)}},{key:"locked",get:function(){if(!1===_(this))throw z("locked");return S(this)}}]),e}();function b(e){return new O(e)}function _(e){return!!c(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")}function S(e){return f(!0===_(e),"IsWritableStreamLocked should only be used on known writable streams"),void 0!==e._writer}function A(e,t){var r=e._state;if("closed"===r)return Promise.resolve(void 0);if("errored"===r)return Promise.reject(e._storedError);var n=new TypeError("Requested to abort");if(void 0!==e._pendingAbortRequest)return Promise.reject(n);f("writable"===r||"erroring"===r,"state must be writable or erroring");var i=!1;"erroring"===r&&(i=!0,t=void 0);var a=new Promise((function(r,n){e._pendingAbortRequest={_resolve:r,_reject:n,_reason:t,_wasAlreadyErroring:i}}));return!1===i&&k(e,n),a}function w(e,t){var r=e._state;"writable"!==r?(f("erroring"===r),x(e)):k(e,t)}function k(e,t){f(void 0===e._storedError,"stream._storedError === undefined"),f("writable"===e._state,"state must be writable");var r=e._writableStreamController;f(void 0!==r,"controller must not be undefined"),e._state="erroring",e._storedError=t;var n=e._writer;void 0!==n&&D(n,t),!1===C(e)&&!0===r._started&&x(e)}function x(e){f("erroring"===e._state,"stream._state === erroring"),f(!1===C(e),"WritableStreamHasOperationMarkedInFlight(stream) === false"),e._state="errored",e._writableStreamController.__errorSteps();for(var t=e._storedError,r=0;r<e._writeRequests.length;r++)e._writeRequests[r]._reject(t);if(e._writeRequests=[],void 0!==e._pendingAbortRequest){var n=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,!0===n._wasAlreadyErroring)return n._reject(t),void E(e);e._writableStreamController.__abortSteps(n._reason).then((function(){n._resolve(),E(e)}),(function(t){n._reject(t),E(e)}))}else E(e)}function P(e){f(void 0!==e._inFlightCloseRequest),e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0;var t=e._state;f("writable"===t||"erroring"===t),"erroring"===t&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&function(e){f(void 0!==e._closedPromise_resolve,"writer._closedPromise_resolve !== undefined"),f(void 0!==e._closedPromise_reject,"writer._closedPromise_reject !== undefined"),f("pending"===e._closedPromiseState,"writer._closedPromiseState is pending"),e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}(r),f(void 0===e._pendingAbortRequest,"stream._pendingAbortRequest === undefined"),f(void 0===e._storedError,"stream._storedError === undefined")}function R(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function C(e){return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest}function E(e){f("errored"===e._state,'_stream_.[[state]] is `"errored"`'),void 0!==e._closeRequest&&(f(void 0===e._inFlightCloseRequest),e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var t=e._writer;void 0!==t&&(Y(t,e._storedError),t._closedPromise.catch((function(){})))}function T(e,t){f("writable"===e._state),f(!1===R(e));var r=e._writer;void 0!==r&&t!==e._backpressure&&(!0===t?function(e){f(void 0===e._readyPromise_resolve,"writer._readyPromise_resolve === undefined"),f(void 0===e._readyPromise_reject,"writer._readyPromise_reject === undefined"),e._readyPromise=new Promise((function(t,r){e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState="pending"}(r):(f(!1===t),J(r))),e._backpressure=t}e.exports={AcquireWritableStreamDefaultWriter:b,IsWritableStream:_,IsWritableStreamLocked:S,WritableStream:y,WritableStreamAbort:A,WritableStreamDefaultControllerError:G,WritableStreamDefaultWriterCloseWithErrorPropagation:function(e){var t=e._ownerWritableStream;f(void 0!==t);var r=t._state;return!0===R(t)||"closed"===r?Promise.resolve():"errored"===r?Promise.reject(t._storedError):(f("writable"===r||"erroring"===r),I(e))},WritableStreamDefaultWriterRelease:j,WritableStreamDefaultWriterWrite:M,WritableStreamCloseQueuedOrInFlight:R};var O=function(){function e(t){if(i(this,e),!1===_(t))throw new TypeError("WritableStreamDefaultWriter can only be constructed with a WritableStream instance");if(!0===S(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;var r,n=t._state;if("writable"===n)!1===R(t)&&!0===t._backpressure?((r=this)._readyPromise=new Promise((function(e,t){r._readyPromise_resolve=e,r._readyPromise_reject=t})),r._readyPromiseState="pending"):Q(this),V(this);else if("erroring"===n)K(this,t._storedError),this._readyPromise.catch((function(){})),V(this);else if("closed"===n)Q(this),function(e){e._closedPromise=Promise.resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved"}(this);else{f("errored"===n,"state must be errored");var a=t._storedError;K(this,a),this._readyPromise.catch((function(){})),function(e,t){e._closedPromise=Promise.reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}(this,a),this._closedPromise.catch((function(){}))}}return n(e,[{key:"abort",value:function(e){return!1===L(this)?Promise.reject(H("abort")):void 0===this._ownerWritableStream?Promise.reject(X("abort")):function(e,t){var r=e._ownerWritableStream;return f(void 0!==r),A(r,t)}(this,e)}},{key:"close",value:function(){if(!1===L(this))return Promise.reject(H("close"));var e=this._ownerWritableStream;return void 0===e?Promise.reject(X("close")):!0===R(e)?Promise.reject(new TypeError("cannot close an already-closing stream")):I(this)}},{key:"releaseLock",value:function(){if(!1===L(this))throw H("releaseLock");var e=this._ownerWritableStream;void 0!==e&&(f(void 0!==e._writer),j(this))}},{key:"write",value:function(e){return!1===L(this)?Promise.reject(H("write")):void 0===this._ownerWritableStream?Promise.reject(X("write to")):M(this,e)}},{key:"closed",get:function(){return!1===L(this)?Promise.reject(H("closed")):this._closedPromise}},{key:"desiredSize",get:function(){if(!1===L(this))throw H("desiredSize");if(void 0===this._ownerWritableStream)throw X("desiredSize");return function(e){var t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:q(t._writableStreamController)}(this)}},{key:"ready",get:function(){return!1===L(this)?Promise.reject(H("ready")):this._readyPromise}}]),e}();function L(e){return!!c(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")}function I(e){var t=e._ownerWritableStream;f(void 0!==t);var r=t._state;if("closed"===r||"errored"===r)return Promise.reject(new TypeError("The stream (in "+r+" state) is not in the writable state and cannot be closed"));f("writable"===r||"erroring"===r),f(!1===R(t));var n,i=new Promise((function(e,r){var n={_resolve:e,_reject:r};t._closeRequest=n}));return!0===t._backpressure&&"writable"===r&&J(e),n=t._writableStreamController,v(n,"close",0),W(n),i}function F(e,t){"pending"===e._closedPromiseState?Y(e,t):function(e,t){f(void 0===e._closedPromise_resolve,"writer._closedPromise_resolve === undefined"),f(void 0===e._closedPromise_reject,"writer._closedPromise_reject === undefined"),f("pending"!==e._closedPromiseState,"writer._closedPromiseState is not pending"),e._closedPromise=Promise.reject(t),e._closedPromiseState="rejected"}(e,t),e._closedPromise.catch((function(){}))}function D(e,t){"pending"===e._readyPromiseState?function(e,t){f(void 0!==e._readyPromise_resolve,"writer._readyPromise_resolve !== undefined"),f(void 0!==e._readyPromise_reject,"writer._readyPromise_reject !== undefined"),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}(e,t):function(e,t){f(void 0===e._readyPromise_resolve,"writer._readyPromise_resolve === undefined"),f(void 0===e._readyPromise_reject,"writer._readyPromise_reject === undefined"),e._readyPromise=Promise.reject(t),e._readyPromiseState="rejected"}(e,t),e._readyPromise.catch((function(){}))}function j(e){var t=e._ownerWritableStream;f(void 0!==t),f(t._writer===e);var r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");D(e,r),F(e,r),t._writer=void 0,e._ownerWritableStream=void 0}function M(e,t){var r=e._ownerWritableStream;f(void 0!==r);var n=r._writableStreamController,i=function(e,t){var r=e._strategySize;if(void 0===r)return 1;try{return r(t)}catch(n){return U(e,n),1}}(n,t);if(r!==e._ownerWritableStream)return Promise.reject(X("write to"));var a=r._state;if("errored"===a)return Promise.reject(r._storedError);if(!0===R(r)||"closed"===a)return Promise.reject(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return Promise.reject(r._storedError);f("writable"===a);var o=function(e){return f(!0===S(e)),f("writable"===e._state),new Promise((function(t,r){var n={_resolve:t,_reject:r};e._writeRequests.push(n)}))}(r);return function(e,t,r){var n={chunk:t};try{v(e,n,r)}catch(a){return void U(e,a)}var i=e._controlledWritableStream;!1===R(i)&&"writable"===i._state&&T(i,B(e)),W(e)}(n,t,i),o}var N=function(){function e(t,r,n,a){if(i(this,e),!1===_(t))throw new TypeError("WritableStreamDefaultController can only be constructed with a WritableStream instance");if(void 0!==t._writableStreamController)throw new TypeError("WritableStreamDefaultController instances can only be created by the WritableStream constructor");this._controlledWritableStream=t,this._underlyingSink=r,this._queue=void 0,this._queueTotalSize=void 0,g(this),this._started=!1;var o=u(n,a);this._strategySize=o.size,this._strategyHWM=o.highWaterMark,T(t,B(this))}return n(e,[{key:"error",value:function(e){if(!1===(!!c(t=this)&&!!Object.prototype.hasOwnProperty.call(t,"_underlyingSink")))throw new TypeError("WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController");var t;"writable"===this._controlledWritableStream._state&&G(this,e)}},{key:"__abortSteps",value:function(e){return s(this._underlyingSink,"abort",[e])}},{key:"__errorSteps",value:function(){g(this)}},{key:"__startSteps",value:function(){var e=this,t=o(this._underlyingSink,"start",[this]),r=this._controlledWritableStream;Promise.resolve(t).then((function(){f("writable"===r._state||"erroring"===r._state),e._started=!0,W(e)}),(function(t){f("writable"===r._state||"erroring"===r._state),e._started=!0,w(r,t)})).catch(d)}}]),e}();function q(e){return e._strategyHWM-e._queueTotalSize}function W(e){var t=e._controlledWritableStream;if(!1!==e._started&&void 0===t._inFlightWriteRequest){var r=t._state;if("closed"!==r&&"errored"!==r)if("erroring"!==r){if(0!==e._queue.length){var n=m(e);"close"===n?function(e){var t=e._controlledWritableStream;(function(e){f(void 0===e._inFlightCloseRequest),f(void 0!==e._closeRequest),e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),p(e),f(0===e._queue.length,"queue must be empty once the final write record is dequeued");var r=s(e._underlyingSink,"close",[]);r.then((function(){P(t)}),(function(e){!function(e,t){f(void 0!==e._inFlightCloseRequest),e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,f("writable"===e._state||"erroring"===e._state),void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),w(e,t)}(t,e)})).catch(d)}(e):function(e,t){var r=e._controlledWritableStream;!function(e){f(void 0===e._inFlightWriteRequest,"there must be no pending write request"),f(0!==e._writeRequests.length,"writeRequests must not be empty"),e._inFlightWriteRequest=e._writeRequests.shift()}(r);var n=s(e._underlyingSink,"write",[t,e]);n.then((function(){!function(e){f(void 0!==e._inFlightWriteRequest),e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);var t=r._state;if(f("writable"===t||"erroring"===t),p(e),!1===R(r)&&"writable"===t){var n=B(e);T(r,n)}W(e)}),(function(e){!function(e,t){f(void 0!==e._inFlightWriteRequest),e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,f("writable"===e._state||"erroring"===e._state),w(e,t)}(r,e)})).catch(d)}(e,n.chunk)}}else x(t)}}function U(e,t){"writable"===e._controlledWritableStream._state&&G(e,t)}function B(e){return q(e)<=0}function G(e,t){var r=e._controlledWritableStream;f("writable"===r._state),k(r,t)}function z(e){return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream")}function H(e){return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter")}function X(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function V(e){e._closedPromise=new Promise((function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"}))}function Y(e,t){f(void 0!==e._closedPromise_resolve,"writer._closedPromise_resolve !== undefined"),f(void 0!==e._closedPromise_reject,"writer._closedPromise_reject !== undefined"),f("pending"===e._closedPromiseState,"writer._closedPromiseState is pending"),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected"}function K(e,t){e._readyPromise=Promise.reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected"}function Q(e){e._readyPromise=Promise.resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}function J(e){f(void 0!==e._readyPromise_resolve,"writer._readyPromise_resolve !== undefined"),f(void 0!==e._readyPromise_reject,"writer._readyPromise_reject !== undefined"),e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled"}},function(e,t,r){var n=r(0).IsFiniteNonNegativeNumber,i=r(1).assert;t.DequeueValue=function(e){i("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: DequeueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."),i(e._queue.length>0,"Spec-level failure: should never dequeue from an empty queue.");var t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value},t.EnqueueValueWithSize=function(e,t,r){if(i("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: EnqueueValueWithSize should only be used on containers with [[queue]] and [[queueTotalSize]]."),r=Number(r),!n(r))throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r},t.PeekQueueValue=function(e){return i("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: PeekQueueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."),i(e._queue.length>0,"Spec-level failure: should never peek at an empty queue."),e._queue[0].value},t.ResetQueue=function(e){i("_queue"in e&&"_queueTotalSize"in e,"Spec-level failure: ResetQueue should only be used on containers with [[queue]] and [[queueTotalSize]]."),e._queue=[],e._queueTotalSize=0}},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(0),o=a.ArrayBufferCopy,s=a.CreateIterResultObject,u=a.IsFiniteNonNegativeNumber,c=a.InvokeOrNoop,l=a.PromiseInvokeOrNoop,f=a.TransferArrayBuffer,d=a.ValidateAndNormalizeQueuingStrategy,h=a.ValidateAndNormalizeHighWaterMark,p=r(0),v=p.createArrayFromList,m=p.createDataProperty,g=p.typeIsObject,y=r(1),b=y.assert,_=y.rethrowAssertionErrorRejection,S=r(3),A=S.DequeueValue,w=S.EnqueueValueWithSize,k=S.ResetQueue,x=r(2),P=x.AcquireWritableStreamDefaultWriter,R=x.IsWritableStream,C=x.IsWritableStreamLocked,E=x.WritableStreamAbort,T=x.WritableStreamDefaultWriterCloseWithErrorPropagation,O=x.WritableStreamDefaultWriterRelease,L=x.WritableStreamDefaultWriterWrite,I=x.WritableStreamCloseQueuedOrInFlight,F=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.size,a=r.highWaterMark;i(this,e),this._state="readable",this._reader=void 0,this._storedError=void 0,this._disturbed=!1,this._readableStreamController=void 0;var o=t.type;if("bytes"===String(o))void 0===a&&(a=0),this._readableStreamController=new de(this,t,a);else{if(void 0!==o)throw new RangeError("Invalid type is specified");void 0===a&&(a=1),this._readableStreamController=new ne(this,t,n,a)}}return n(e,[{key:"cancel",value:function(e){return!1===j(this)?Promise.reject(Ee("cancel")):!0===M(this)?Promise.reject(new TypeError("Cannot cancel a stream that already has a reader")):U(this,e)}},{key:"getReader",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).mode;if(!1===j(this))throw Ee("getReader");if(void 0===e)return D(this);if("byob"===(e=String(e)))return new Q(this);throw new RangeError("Invalid mode is specified")}},{key:"pipeThrough",value:function(e,t){var r=e.writable,n=e.readable;return function(e){try{Promise.prototype.then.call(e,void 0,(function(){}))}catch(t){}}(this.pipeTo(r,t)),n}},{key:"pipeTo",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.preventClose,i=r.preventAbort,a=r.preventCancel;if(!1===j(this))return Promise.reject(Ee("pipeTo"));if(!1===R(e))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));if(n=Boolean(n),i=Boolean(i),a=Boolean(a),!0===M(this))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream"));if(!0===C(e))return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream"));var o=D(this),s=P(e),u=!1,c=Promise.resolve();return new Promise((function(r,l){var f,d,h;if(m(t,o._closedPromise,(function(t){!1===i?g((function(){return E(e,t)}),!0,t):y(!0,t)})),m(e,s._closedPromise,(function(e){!1===a?g((function(){return U(t,e)}),!0,e):y(!0,e)})),f=t,d=o._closedPromise,h=function(){!1===n?g((function(){return T(s)})):y()},"closed"===f._state?h():d.then(h).catch(_),!0===I(e)||"closed"===e._state){var p=new TypeError("the destination writable stream closed before all data could be piped to it");!1===a?g((function(){return U(t,p)}),!0,p):y(!0,p)}function v(){var e=c;return c.then((function(){return e!==c?v():void 0}))}function m(e,t,r){"errored"===e._state?r(e._storedError):t.catch(r).catch(_)}function g(t,r,n){function i(){t().then((function(){return b(r,n)}),(function(e){return b(!0,e)})).catch(_)}!0!==u&&(u=!0,"writable"===e._state&&!1===I(e)?v().then(i):i())}function y(t,r){!0!==u&&(u=!0,"writable"===e._state&&!1===I(e)?v().then((function(){return b(t,r)})).catch(_):b(t,r))}function b(e,t){O(s),te(o),e?l(t):r(void 0)}(function e(){return c=Promise.resolve(),!0===u?Promise.resolve():s._readyPromise.then((function(){return re(o).then((function(e){var t=e.value;!0!==e.done&&(c=L(s,t).catch((function(){})))}))})).then(e)})().catch((function(e){c=Promise.resolve(),_(e)}))}))}},{key:"tee",value:function(){if(!1===j(this))throw Ee("tee");var e=N(this,!1);return v(e)}},{key:"locked",get:function(){if(!1===j(this))throw Ee("locked");return M(this)}}]),e}();function D(e){return new K(e)}function j(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")}function M(e){return b(!0===j(e),"IsReadableStreamLocked should only be used on known readable streams"),void 0!==e._reader}function N(e,t){b(!0===j(e)),b("boolean"===typeof t);var r=D(e),n={closedOrErrored:!1,canceled1:!1,canceled2:!1,reason1:void 0,reason2:void 0};n.promise=new Promise((function(e){n._resolve=e}));var i=function(){function e(){var t=e._reader,r=e._branch1,n=e._branch2,i=e._teeState;return re(t).then((function(e){b(g(e));var t=e.value,a=e.done;if(b("boolean"===typeof a),!0===a&&!1===i.closedOrErrored&&(!1===i.canceled1&&oe(r),!1===i.canceled2&&oe(n),i.closedOrErrored=!0),!0!==i.closedOrErrored){var o=t,s=t;!1===i.canceled1&&se(r,o),!1===i.canceled2&&se(n,s)}}))}return e}();i._reader=r,i._teeState=n,i._cloneForBranch2=t;var a=function(){function e(t){var r=e._stream,n=e._teeState;if(n.canceled1=!0,n.reason1=t,!0===n.canceled2){var i=U(r,v([n.reason1,n.reason2]));n._resolve(i)}return n.promise}return e}();a._stream=e,a._teeState=n;var o=function(){function e(t){var r=e._stream,n=e._teeState;if(n.canceled2=!0,n.reason2=t,!0===n.canceled1){var i=U(r,v([n.reason1,n.reason2]));n._resolve(i)}return n.promise}return e}();o._stream=e,o._teeState=n;var s=Object.create(Object.prototype);m(s,"pull",i),m(s,"cancel",a);var u=new F(s),c=Object.create(Object.prototype);m(c,"pull",i),m(c,"cancel",o);var l=new F(c);return i._branch1=u._readableStreamController,i._branch2=l._readableStreamController,r._closedPromise.catch((function(e){!0!==n.closedOrErrored&&(ue(i._branch1,e),ue(i._branch2,e),n.closedOrErrored=!0)})),[u,l]}function q(e){return b(!0===J(e._reader)),b("readable"===e._state||"closed"===e._state),new Promise((function(t,r){var n={_resolve:t,_reject:r};e._reader._readIntoRequests.push(n)}))}function W(e){return b(!0===Z(e._reader)),b("readable"===e._state),new Promise((function(t,r){var n={_resolve:t,_reject:r};e._reader._readRequests.push(n)}))}function U(e,t){return e._disturbed=!0,"closed"===e._state?Promise.resolve(void 0):"errored"===e._state?Promise.reject(e._storedError):(B(e),e._readableStreamController.__cancelSteps(t).then((function(){})))}function B(e){b("readable"===e._state),e._state="closed";var t=e._reader;if(void 0!==t){if(!0===Z(t)){for(var r=0;r<t._readRequests.length;r++)(0,t._readRequests[r]._resolve)(s(void 0,!0));t._readRequests=[]}!function(e){b(void 0!==e._closedPromise_resolve),b(void 0!==e._closedPromise_reject),e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}(t)}}function G(e,t){b(!0===j(e),"stream must be ReadableStream"),b("readable"===e._state,"state must be readable"),e._state="errored",e._storedError=t;var r=e._reader;if(void 0!==r){if(!0===Z(r)){for(var n=0;n<r._readRequests.length;n++)r._readRequests[n]._reject(t);r._readRequests=[]}else{b(J(r),"reader must be ReadableStreamBYOBReader");for(var i=0;i<r._readIntoRequests.length;i++)r._readIntoRequests[i]._reject(t);r._readIntoRequests=[]}Le(r,t),r._closedPromise.catch((function(){}))}}function z(e,t,r){var n=e._reader;b(n._readRequests.length>0),n._readRequests.shift()._resolve(s(t,r))}function H(e){return e._reader._readIntoRequests.length}function X(e){return e._reader._readRequests.length}function V(e){var t=e._reader;return void 0!==t&&!1!==J(t)}function Y(e){var t=e._reader;return void 0!==t&&!1!==Z(t)}e.exports={ReadableStream:F,IsReadableStreamDisturbed:function(e){return b(!0===j(e),"IsReadableStreamDisturbed should only be used on known readable streams"),e._disturbed},ReadableStreamDefaultControllerClose:oe,ReadableStreamDefaultControllerEnqueue:se,ReadableStreamDefaultControllerError:ue,ReadableStreamDefaultControllerGetDesiredSize:le};var K=function(){function e(t){if(i(this,e),!1===j(t))throw new TypeError("ReadableStreamDefaultReader can only be constructed with a ReadableStream instance");if(!0===M(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");$(this,t),this._readRequests=[]}return n(e,[{key:"cancel",value:function(e){return!1===Z(this)?Promise.reject(Oe("cancel")):void 0===this._ownerReadableStream?Promise.reject(Te("cancel")):ee(this,e)}},{key:"read",value:function(){return!1===Z(this)?Promise.reject(Oe("read")):void 0===this._ownerReadableStream?Promise.reject(Te("read from")):re(this)}},{key:"releaseLock",value:function(){if(!1===Z(this))throw Oe("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");te(this)}}},{key:"closed",get:function(){return!1===Z(this)?Promise.reject(Oe("closed")):this._closedPromise}}]),e}(),Q=function(){function e(t){if(i(this,e),!j(t))throw new TypeError("ReadableStreamBYOBReader can only be constructed with a ReadableStream instance given a byte source");if(!1===he(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");if(M(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");$(this,t),this._readIntoRequests=[]}return n(e,[{key:"cancel",value:function(e){return J(this)?void 0===this._ownerReadableStream?Promise.reject(Te("cancel")):ee(this,e):Promise.reject(Ie("cancel"))}},{key:"read",value:function(e){return J(this)?void 0===this._ownerReadableStream?Promise.reject(Te("read from")):ArrayBuffer.isView(e)?0===e.byteLength?Promise.reject(new TypeError("view must have non-zero byteLength")):function(e,t){var r=e._ownerReadableStream;return b(void 0!==r),r._disturbed=!0,"errored"===r._state?Promise.reject(r._storedError):function(e,t){var r=e._controlledReadableStream,n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);var i=t.constructor,a={buffer:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,ctor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return a.buffer=f(a.buffer),e._pendingPullIntos.push(a),q(r);if("closed"===r._state){var o=new t.constructor(a.buffer,a.byteOffset,0);return Promise.resolve(s(o,!0))}if(e._queueTotalSize>0){if(!0===_e(e,a)){var u=ye(a);return Ae(e),Promise.resolve(s(u,!1))}if(!0===e._closeRequested){var c=new TypeError("Insufficient bytes to fill elements in the given buffer");return Re(e,c),Promise.reject(c)}}a.buffer=f(a.buffer),e._pendingPullIntos.push(a);var l=q(r);return ve(e),l}(r._readableStreamController,t)}(this,e):Promise.reject(new TypeError("view must be an array buffer view")):Promise.reject(Ie("read"))}},{key:"releaseLock",value:function(){if(!J(this))throw Ie("releaseLock");if(void 0!==this._ownerReadableStream){if(this._readIntoRequests.length>0)throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled");te(this)}}},{key:"closed",get:function(){return J(this)?this._closedPromise:Promise.reject(Ie("closed"))}}]),e}();function J(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")}function Z(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")}function $(e,t){e._ownerReadableStream=t,t._reader=e,"readable"===t._state?function(e){e._closedPromise=new Promise((function(t,r){e._closedPromise_resolve=t,e._closedPromise_reject=r}))}(e):"closed"===t._state?function(e){e._closedPromise=Promise.resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}(e):(b("errored"===t._state,"state must be errored"),function(e,t){e._closedPromise=Promise.reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}(e,t._storedError),e._closedPromise.catch((function(){})))}function ee(e,t){var r=e._ownerReadableStream;return b(void 0!==r),U(r,t)}function te(e){b(void 0!==e._ownerReadableStream),b(e._ownerReadableStream._reader===e),"readable"===e._ownerReadableStream._state?Le(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,t){b(void 0===e._closedPromise_resolve),b(void 0===e._closedPromise_reject),e._closedPromise=Promise.reject(t)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),e._closedPromise.catch((function(){})),e._ownerReadableStream._reader=void 0,e._ownerReadableStream=void 0}function re(e){var t=e._ownerReadableStream;return b(void 0!==t),t._disturbed=!0,"closed"===t._state?Promise.resolve(s(void 0,!0)):"errored"===t._state?Promise.reject(t._storedError):(b("readable"===t._state),t._readableStreamController.__pullSteps())}var ne=function(){function e(t,r,n,a){if(i(this,e),!1===j(t))throw new TypeError("ReadableStreamDefaultController can only be constructed with a ReadableStream instance");if(void 0!==t._readableStreamController)throw new TypeError("ReadableStreamDefaultController instances can only be created by the ReadableStream constructor");this._controlledReadableStream=t,this._underlyingSource=r,this._queue=void 0,this._queueTotalSize=void 0,k(this),this._started=!1,this._closeRequested=!1,this._pullAgain=!1,this._pulling=!1;var o=d(n,a);this._strategySize=o.size,this._strategyHWM=o.highWaterMark;var s=this,u=c(r,"start",[this]);Promise.resolve(u).then((function(){s._started=!0,b(!1===s._pulling),b(!1===s._pullAgain),ae(s)}),(function(e){ce(s,e)})).catch(_)}return n(e,[{key:"close",value:function(){if(!1===ie(this))throw Fe("close");if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");oe(this)}},{key:"enqueue",value:function(e){if(!1===ie(this))throw Fe("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");return se(this,e)}},{key:"error",value:function(e){if(!1===ie(this))throw Fe("error");var t=this._controlledReadableStream;if("readable"!==t._state)throw new TypeError("The stream is "+t._state+" and so cannot be errored");ue(this,e)}},{key:"__cancelSteps",value:function(e){return k(this),l(this._underlyingSource,"cancel",[e])}},{key:"__pullSteps",value:function(){var e=this._controlledReadableStream;if(this._queue.length>0){var t=A(this);return!0===this._closeRequested&&0===this._queue.length?B(e):ae(this),Promise.resolve(s(t,!1))}var r=W(e);return ae(this),r}},{key:"desiredSize",get:function(){if(!1===ie(this))throw Fe("desiredSize");return le(this)}}]),e}();function ie(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingSource")}function ae(e){var t=function(e){var t=e._controlledReadableStream;if("closed"===t._state||"errored"===t._state)return!1;if(!0===e._closeRequested)return!1;if(!1===e._started)return!1;if(!0===M(t)&&X(t)>0)return!0;var r=le(e);return r>0}(e);!1!==t&&(!0!==e._pulling?(b(!1===e._pullAgain),e._pulling=!0,l(e._underlyingSource,"pull",[e]).then((function(){if(e._pulling=!1,!0===e._pullAgain)return e._pullAgain=!1,ae(e)}),(function(t){ce(e,t)})).catch(_)):e._pullAgain=!0)}function oe(e){var t=e._controlledReadableStream;b(!1===e._closeRequested),b("readable"===t._state),e._closeRequested=!0,0===e._queue.length&&B(t)}function se(e,t){var r=e._controlledReadableStream;if(b(!1===e._closeRequested),b("readable"===r._state),!0===M(r)&&X(r)>0)z(r,t,!1);else{var n=1;if(void 0!==e._strategySize){var i=e._strategySize;try{n=i(t)}catch(a){throw ce(e,a),a}}try{w(e,t,n)}catch(o){throw ce(e,o),o}}ae(e)}function ue(e,t){var r=e._controlledReadableStream;b("readable"===r._state),k(e),G(r,t)}function ce(e,t){"readable"===e._controlledReadableStream._state&&ue(e,t)}function le(e){var t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}var fe=function(){function e(t,r){i(this,e),this._associatedReadableByteStreamController=t,this._view=r}return n(e,[{key:"respond",value:function(e){if(!1===pe(this))throw De("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");!function(e,t){if(t=Number(t),!1===u(t))throw new RangeError("bytesWritten must be a finite");b(e._pendingPullIntos.length>0),xe(e,t)}(this._associatedReadableByteStreamController,e)}},{key:"respondWithNewView",value:function(e){if(!1===pe(this))throw De("respond");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");!function(e,t){b(e._pendingPullIntos.length>0);var r=e._pendingPullIntos[0];if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.byteLength!==t.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");r.buffer=t.buffer,xe(e,t.byteLength)}(this._associatedReadableByteStreamController,e)}},{key:"view",get:function(){return this._view}}]),e}(),de=function(){function e(t,r,n){if(i(this,e),!1===j(t))throw new TypeError("ReadableByteStreamController can only be constructed with a ReadableStream instance given a byte source");if(void 0!==t._readableStreamController)throw new TypeError("ReadableByteStreamController instances can only be created by the ReadableStream constructor given a byte source");this._controlledReadableStream=t,this._underlyingByteSource=r,this._pullAgain=!1,this._pulling=!1,me(this),this._queue=this._queueTotalSize=void 0,k(this),this._closeRequested=!1,this._started=!1,this._strategyHWM=h(n);var a=r.autoAllocateChunkSize;if(void 0!==a&&(!1===Number.isInteger(a)||a<=0))throw new RangeError("autoAllocateChunkSize must be a positive integer");this._autoAllocateChunkSize=a,this._pendingPullIntos=[];var o=this,s=c(r,"start",[this]);Promise.resolve(s).then((function(){o._started=!0,b(!1===o._pulling),b(!1===o._pullAgain),ve(o)}),(function(e){"readable"===t._state&&Re(o,e)})).catch(_)}return n(e,[{key:"close",value:function(){if(!1===he(this))throw je("close");if(!0===this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableStream._state;if("readable"!==e)throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed");!function(e){var t=e._controlledReadableStream;if(b(!1===e._closeRequested),b("readable"===t._state),e._queueTotalSize>0)e._closeRequested=!0;else{if(e._pendingPullIntos.length>0&&e._pendingPullIntos[0].bytesFilled>0){var r=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Re(e,r),r}B(t)}}(this)}},{key:"enqueue",value:function(e){if(!1===he(this))throw je("enqueue");if(!0===this._closeRequested)throw new TypeError("stream is closed or draining");var t=this._controlledReadableStream._state;if("readable"!==t)throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to");if(!ArrayBuffer.isView(e))throw new TypeError("You can only enqueue array buffer views when using a ReadableByteStreamController");!function(e,t){var r=e._controlledReadableStream;b(!1===e._closeRequested),b("readable"===r._state);var n=t.buffer,i=t.byteOffset,a=t.byteLength,o=f(n);!0===Y(r)?0===X(r)?be(e,o,i,a):(b(0===e._queue.length),z(r,new Uint8Array(o,i,a),!1)):!0===V(r)?(be(e,o,i,a),ke(e)):(b(!1===M(r),"stream must not be locked"),be(e,o,i,a))}(this,e)}},{key:"error",value:function(e){if(!1===he(this))throw je("error");var t=this._controlledReadableStream;if("readable"!==t._state)throw new TypeError("The stream is "+t._state+" and so cannot be errored");Re(this,e)}},{key:"__cancelSteps",value:function(e){return this._pendingPullIntos.length>0&&(this._pendingPullIntos[0].bytesFilled=0),k(this),l(this._underlyingByteSource,"cancel",[e])}},{key:"__pullSteps",value:function(){var e=this._controlledReadableStream;if(b(!0===Y(e)),this._queueTotalSize>0){b(0===X(e));var t=this._queue.shift();this._queueTotalSize-=t.byteLength,Ae(this);var r=void 0;try{r=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}catch(u){return Promise.reject(u)}return Promise.resolve(s(r,!1))}var n=this._autoAllocateChunkSize;if(void 0!==n){var i=void 0;try{i=new ArrayBuffer(n)}catch(c){return Promise.reject(c)}var a={buffer:i,byteOffset:0,byteLength:n,bytesFilled:0,elementSize:1,ctor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(a)}var o=W(e);return ve(this),o}},{key:"byobRequest",get:function(){if(!1===he(this))throw je("byobRequest");if(void 0===this._byobRequest&&this._pendingPullIntos.length>0){var e=this._pendingPullIntos[0],t=new Uint8Array(e.buffer,e.byteOffset+e.bytesFilled,e.byteLength-e.bytesFilled);this._byobRequest=new fe(this,t)}return this._byobRequest}},{key:"desiredSize",get:function(){if(!1===he(this))throw je("desiredSize");return Ce(this)}}]),e}();function he(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_underlyingByteSource")}function pe(e){return!!g(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")}function ve(e){var t=function(e){var t=e._controlledReadableStream;return"readable"===t._state&&(!0!==e._closeRequested&&(!1!==e._started&&(!0===Y(t)&&X(t)>0||(!0===V(t)&&H(t)>0||Ce(e)>0))))}(e);!1!==t&&(!0!==e._pulling?(b(!1===e._pullAgain),e._pulling=!0,l(e._underlyingByteSource,"pull",[e]).then((function(){e._pulling=!1,!0===e._pullAgain&&(e._pullAgain=!1,ve(e))}),(function(t){"readable"===e._controlledReadableStream._state&&Re(e,t)})).catch(_)):e._pullAgain=!0)}function me(e){we(e),e._pendingPullIntos=[]}function ge(e,t){b("errored"!==e._state,"state must not be errored");var r=!1;"closed"===e._state&&(b(0===t.bytesFilled),r=!0);var n=ye(t);"default"===t.readerType?z(e,n,r):(b("byob"===t.readerType),function(e,t,r){var n=e._reader;b(n._readIntoRequests.length>0),n._readIntoRequests.shift()._resolve(s(t,r))}(e,n,r))}function ye(e){var t=e.bytesFilled,r=e.elementSize;return b(t<=e.byteLength),b(t%r===0),new e.ctor(e.buffer,e.byteOffset,t/r)}function be(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n}function _e(e,t){var r=t.elementSize,n=t.bytesFilled-t.bytesFilled%r,i=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+i,s=a-a%r,u=i,c=!1;s>n&&(u=s-t.bytesFilled,c=!0);for(var l=e._queue;u>0;){var f=l[0],d=Math.min(u,f.byteLength),h=t.byteOffset+t.bytesFilled;o(t.buffer,h,f.buffer,f.byteOffset,d),f.byteLength===d?l.shift():(f.byteOffset+=d,f.byteLength-=d),e._queueTotalSize-=d,Se(e,d,t),u-=d}return!1===c&&(b(0===e._queueTotalSize,"queue must be empty"),b(t.bytesFilled>0),b(t.bytesFilled<t.elementSize)),c}function Se(e,t,r){b(0===e._pendingPullIntos.length||e._pendingPullIntos[0]===r),we(e),r.bytesFilled+=t}function Ae(e){b("readable"===e._controlledReadableStream._state),0===e._queueTotalSize&&!0===e._closeRequested?B(e._controlledReadableStream):ve(e)}function we(e){void 0!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=void 0,e._byobRequest=void 0)}function ke(e){for(b(!1===e._closeRequested);e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var t=e._pendingPullIntos[0];!0===_e(e,t)&&(Pe(e),ge(e._controlledReadableStream,t))}}function xe(e,t){var r=e._pendingPullIntos[0],n=e._controlledReadableStream;if("closed"===n._state){if(0!==t)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream");!function(e,t){t.buffer=f(t.buffer),b(0===t.bytesFilled,"bytesFilled must be 0");var r=e._controlledReadableStream;if(!0===V(r))for(;H(r)>0;)ge(r,Pe(e))}(e,r)}else b("readable"===n._state),function(e,t,r){if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range");if(Se(e,t,r),!(r.bytesFilled<r.elementSize)){Pe(e);var n=r.bytesFilled%r.elementSize;if(n>0){var i=r.byteOffset+r.bytesFilled,a=r.buffer.slice(i-n,i);be(e,a,0,a.byteLength)}r.buffer=f(r.buffer),r.bytesFilled-=n,ge(e._controlledReadableStream,r),ke(e)}}(e,t,r)}function Pe(e){var t=e._pendingPullIntos.shift();return we(e),t}function Re(e,t){var r=e._controlledReadableStream;b("readable"===r._state),me(e),k(e),G(r,t)}function Ce(e){var t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function Ee(e){return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream")}function Te(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function Oe(e){return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader")}function Le(e,t){b(void 0!==e._closedPromise_resolve),b(void 0!==e._closedPromise_reject),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0}function Ie(e){return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader")}function Fe(e){return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController")}function De(e){return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest")}function je(e){return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController")}},function(e,t,r){var n=r(6),i=r(4),a=r(2);t.TransformStream=n.TransformStream,t.ReadableStream=i.ReadableStream,t.IsReadableStreamDisturbed=i.IsReadableStreamDisturbed,t.ReadableStreamDefaultControllerClose=i.ReadableStreamDefaultControllerClose,t.ReadableStreamDefaultControllerEnqueue=i.ReadableStreamDefaultControllerEnqueue,t.ReadableStreamDefaultControllerError=i.ReadableStreamDefaultControllerError,t.ReadableStreamDefaultControllerGetDesiredSize=i.ReadableStreamDefaultControllerGetDesiredSize,t.AcquireWritableStreamDefaultWriter=a.AcquireWritableStreamDefaultWriter,t.IsWritableStream=a.IsWritableStream,t.IsWritableStreamLocked=a.IsWritableStreamLocked,t.WritableStream=a.WritableStream,t.WritableStreamAbort=a.WritableStreamAbort,t.WritableStreamDefaultControllerError=a.WritableStreamDefaultControllerError,t.WritableStreamDefaultWriterCloseWithErrorPropagation=a.WritableStreamDefaultWriterCloseWithErrorPropagation,t.WritableStreamDefaultWriterRelease=a.WritableStreamDefaultWriterRelease,t.WritableStreamDefaultWriterWrite=a.WritableStreamDefaultWriterWrite},function(e,t,r){var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=r(1).assert,o=r(0),s=o.InvokeOrNoop,u=o.PromiseInvokeOrPerformFallback,c=o.PromiseInvokeOrNoop,l=o.typeIsObject,f=r(4),d=f.ReadableStream,h=f.ReadableStreamDefaultControllerClose,p=f.ReadableStreamDefaultControllerEnqueue,v=f.ReadableStreamDefaultControllerError,m=f.ReadableStreamDefaultControllerGetDesiredSize,g=r(2),y=g.WritableStream,b=g.WritableStreamDefaultControllerError;function _(e,t){if(!0===e._errored)throw new TypeError("TransformStream is already errored");if(!0===e._readableClosed)throw new TypeError("Readable side is already closed");var r=e._readableController;try{p(r,t)}catch(n){throw e._readableClosed=!0,A(e,n),e._storedError}!0===m(r)<=0&&!1===e._backpressure&&x(e,!0)}function S(e){a(!1===e._errored),a(!1===e._readableClosed);try{h(e._readableController)}catch(t){a(!1)}e._readableClosed=!0}function A(e,t){!1===e._errored&&w(e,t)}function w(e,t){a(!1===e._errored),e._errored=!0,e._storedError=t,!1===e._writableDone&&b(e._writableController,t),!1===e._readableClosed&&v(e._readableController,t)}function k(e){return a(void 0!==e._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),!1===e._backpressure?Promise.resolve():(a(!0===e._backpressure,"_backpressure should have been initialized"),e._backpressureChangePromise)}function x(e,t){a(e._backpressure!==t,"TransformStreamSetBackpressure() should be called only when backpressure is changed"),void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(t),e._backpressureChangePromise=new Promise((function(t){e._backpressureChangePromise_resolve=t})),e._backpressureChangePromise.then((function(e){a(e!==t,"_backpressureChangePromise should be fulfilled only when backpressure is changed")})),e._backpressure=t}function P(e,t){return _(t._controlledTransformStream,e),Promise.resolve()}function R(e){return!!l(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")}function C(e){return!!l(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")}var E=function(){function e(t,r){i(this,e),this._transformStream=t,this._startPromise=r}return n(e,[{key:"start",value:function(e){var t=this._transformStream;return t._writableController=e,this._startPromise.then((function(){return k(t)}))}},{key:"write",value:function(e){return function(e,t){a(!1===e._errored),a(!1===e._transforming),a(!1===e._backpressure),e._transforming=!0;var r=e._transformer,n=e._transformStreamController;return u(r,"transform",[t,n],P,[t,n]).then((function(){return e._transforming=!1,k(e)}),(function(t){return A(e,t),Promise.reject(t)}))}(this._transformStream,e)}},{key:"abort",value:function(){var e=this._transformStream;e._writableDone=!0,w(e,new TypeError("Writable side aborted"))}},{key:"close",value:function(){var e=this._transformStream;return a(!1===e._transforming),e._writableDone=!0,c(e._transformer,"flush",[e._transformStreamController]).then((function(){return!0===e._errored?Promise.reject(e._storedError):(!1===e._readableClosed&&S(e),Promise.resolve())})).catch((function(t){return A(e,t),Promise.reject(e._storedError)}))}}]),e}(),T=function(){function e(t,r){i(this,e),this._transformStream=t,this._startPromise=r}return n(e,[{key:"start",value:function(e){var t=this._transformStream;return t._readableController=e,this._startPromise.then((function(){return a(void 0!==t._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),!0===t._backpressure?Promise.resolve():(a(!1===t._backpressure,"_backpressure should have been initialized"),t._backpressureChangePromise)}))}},{key:"pull",value:function(){var e=this._transformStream;return a(!0===e._backpressure,"pull() should be never called while _backpressure is false"),a(void 0!==e._backpressureChangePromise,"_backpressureChangePromise should have been initialized"),x(e,!1),e._backpressureChangePromise}},{key:"cancel",value:function(){var e=this._transformStream;e._readableClosed=!0,w(e,new TypeError("Readable side canceled"))}}]),e}(),O=function(){function e(t){if(i(this,e),!1===C(t))throw new TypeError("TransformStreamDefaultController can only be constructed with a TransformStream instance");if(void 0!==t._transformStreamController)throw new TypeError("TransformStreamDefaultController instances can only be created by the TransformStream constructor");this._controlledTransformStream=t}return n(e,[{key:"enqueue",value:function(e){if(!1===R(this))throw I("enqueue");_(this._controlledTransformStream,e)}},{key:"close",value:function(){if(!1===R(this))throw I("close");!function(e){if(!0===e._errored)throw new TypeError("TransformStream is already errored");if(!0===e._readableClosed)throw new TypeError("Readable side is already closed");S(e)}(this._controlledTransformStream)}},{key:"error",value:function(e){if(!1===R(this))throw I("error");!function(e,t){if(!0===e._errored)throw new TypeError("TransformStream is already errored");w(e,t)}(this._controlledTransformStream,e)}},{key:"desiredSize",get:function(){if(!1===R(this))throw I("desiredSize");var e=this._controlledTransformStream._readableController;return m(e)}}]),e}(),L=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};i(this,e),this._transformer=t;var r=t.readableStrategy,n=t.writableStrategy;this._transforming=!1,this._errored=!1,this._storedError=void 0,this._writableController=void 0,this._readableController=void 0,this._transformStreamController=void 0,this._writableDone=!1,this._readableClosed=!1,this._backpressure=void 0,this._backpressureChangePromise=void 0,this._backpressureChangePromise_resolve=void 0,this._transformStreamController=new O(this);var o=void 0,u=new Promise((function(e){o=e})),c=new T(this,u);this._readable=new d(c,r);var l=new E(this,u);this._writable=new y(l,n),a(void 0!==this._writableController),a(void 0!==this._readableController),x(this,m(this._readableController)<=0);var f=this,h=s(t,"start",[f._transformStreamController]);o(h),u.catch((function(e){!1===f._errored&&(f._errored=!0,f._storedError=e)}))}return n(e,[{key:"readable",get:function(){if(!1===C(this))throw F("readable");return this._readable}},{key:"writable",get:function(){if(!1===C(this))throw F("writable");return this._writable}}]),e}();function I(e){return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController")}function F(e){return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream")}e.exports={TransformStream:L}},function(e,t,r){e.exports=r(5)}]))},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var i=!1;try{if("function"===typeof URL&&"object"===n(URL.prototype)&&"origin"in URL.prototype){var a=new URL("b","http://a");a.pathname="c%20d",i="http://a/c%20d"===a.href}}catch(u){}if(i)t.URL=URL;else{var o=r(145).URL,s=r(3).URL;s&&(o.createObjectURL=function(e){return s.createObjectURL.apply(s,arguments)},o.revokeObjectURL=function(e){s.revokeObjectURL(e)}),t.URL=o}},function(e,t,r){"use strict";!function(){var e=Object.create(null);e.ftp=21,e.file=0,e.gopher=70,e.http=80,e.https=443,e.ws=80,e.wss=443;var r=Object.create(null);function n(t){return void 0!==e[t]}function i(){d.call(this),this._isInvalid=!0}function a(e){return""===e&&i.call(this),e.toLowerCase()}function o(e){var t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,63,96].indexOf(t)?e:encodeURIComponent(e)}function s(e){var t=e.charCodeAt(0);return t>32&&t<127&&-1===[34,35,60,62,96].indexOf(t)?e:encodeURIComponent(e)}r["%2e"]=".",r[".%2e"]="..",r["%2e."]="..",r["%2e%2e"]="..";var u,c=/[a-zA-Z]/,l=/[a-zA-Z0-9\+\-\.]/;function f(t,f,d){function h(e){b.push(e)}var p=f||"scheme start",v=0,m="",g=!1,y=!1,b=[];e:for(;(t[v-1]!==u||0===v)&&!this._isInvalid;){var _=t[v];switch(p){case"scheme start":if(!_||!c.test(_)){if(f){h("Invalid scheme.");break e}m="",p="no scheme";continue}m+=_.toLowerCase(),p="scheme";break;case"scheme":if(_&&l.test(_))m+=_.toLowerCase();else{if(":"!==_){if(f){if(_===u)break e;h("Code point not allowed in scheme: "+_);break e}m="",v=0,p="no scheme";continue}if(this._scheme=m,m="",f)break e;n(this._scheme)&&(this._isRelative=!0),p="file"===this._scheme?"relative":this._isRelative&&d&&d._scheme===this._scheme?"relative or authority":this._isRelative?"authority first slash":"scheme data"}break;case"scheme data":"?"===_?(this._query="?",p="query"):"#"===_?(this._fragment="#",p="fragment"):_!==u&&"\t"!==_&&"\n"!==_&&"\r"!==_&&(this._schemeData+=o(_));break;case"no scheme":if(d&&n(d._scheme)){p="relative";continue}h("Missing scheme."),i.call(this);break;case"relative or authority":if("/"!==_||"/"!==t[v+1]){h("Expected /, got: "+_),p="relative";continue}p="authority ignore slashes";break;case"relative":if(this._isRelative=!0,"file"!==this._scheme&&(this._scheme=d._scheme),_===u){this._host=d._host,this._port=d._port,this._path=d._path.slice(),this._query=d._query,this._username=d._username,this._password=d._password;break e}if("/"===_||"\\"===_)"\\"===_&&h("\\ is an invalid code point."),p="relative slash";else if("?"===_)this._host=d._host,this._port=d._port,this._path=d._path.slice(),this._query="?",this._username=d._username,this._password=d._password,p="query";else{if("#"!==_){var S=t[v+1],A=t[v+2];("file"!==this._scheme||!c.test(_)||":"!==S&&"|"!==S||A!==u&&"/"!==A&&"\\"!==A&&"?"!==A&&"#"!==A)&&(this._host=d._host,this._port=d._port,this._username=d._username,this._password=d._password,this._path=d._path.slice(),this._path.pop()),p="relative path";continue}this._host=d._host,this._port=d._port,this._path=d._path.slice(),this._query=d._query,this._fragment="#",this._username=d._username,this._password=d._password,p="fragment"}break;case"relative slash":if("/"!==_&&"\\"!==_){"file"!==this._scheme&&(this._host=d._host,this._port=d._port,this._username=d._username,this._password=d._password),p="relative path";continue}"\\"===_&&h("\\ is an invalid code point."),p="file"===this._scheme?"file host":"authority ignore slashes";break;case"authority first slash":if("/"!==_){h("Expected '/', got: "+_),p="authority ignore slashes";continue}p="authority second slash";break;case"authority second slash":if(p="authority ignore slashes","/"!==_){h("Expected '/', got: "+_);continue}break;case"authority ignore slashes":if("/"!==_&&"\\"!==_){p="authority";continue}h("Expected authority, got: "+_);break;case"authority":if("@"===_){g&&(h("@ already seen."),m+="%40"),g=!0;for(var w=0;w<m.length;w++){var k=m[w];if("\t"!==k&&"\n"!==k&&"\r"!==k)if(":"!==k||null!==this._password){var x=o(k);null!==this._password?this._password+=x:this._username+=x}else this._password="";else h("Invalid whitespace in authority.")}m=""}else{if(_===u||"/"===_||"\\"===_||"?"===_||"#"===_){v-=m.length,m="",p="host";continue}m+=_}break;case"file host":if(_===u||"/"===_||"\\"===_||"?"===_||"#"===_){2!==m.length||!c.test(m[0])||":"!==m[1]&&"|"!==m[1]?(0===m.length||(this._host=a.call(this,m),m=""),p="relative path start"):p="relative path";continue}"\t"===_||"\n"===_||"\r"===_?h("Invalid whitespace in file host."):m+=_;break;case"host":case"hostname":if(":"!==_||y){if(_===u||"/"===_||"\\"===_||"?"===_||"#"===_){if(this._host=a.call(this,m),m="",p="relative path start",f)break e;continue}"\t"!==_&&"\n"!==_&&"\r"!==_?("["===_?y=!0:"]"===_&&(y=!1),m+=_):h("Invalid code point in host/hostname: "+_)}else if(this._host=a.call(this,m),m="",p="port","hostname"===f)break e;break;case"port":if(/[0-9]/.test(_))m+=_;else{if(_===u||"/"===_||"\\"===_||"?"===_||"#"===_||f){if(""!==m){var P=parseInt(m,10);P!==e[this._scheme]&&(this._port=P+""),m=""}if(f)break e;p="relative path start";continue}"\t"===_||"\n"===_||"\r"===_?h("Invalid code point in port: "+_):i.call(this)}break;case"relative path start":if("\\"===_&&h("'\\' not allowed in path."),p="relative path","/"!==_&&"\\"!==_)continue;break;case"relative path":var R;_!==u&&"/"!==_&&"\\"!==_&&(f||"?"!==_&&"#"!==_)?"\t"!==_&&"\n"!==_&&"\r"!==_&&(m+=o(_)):("\\"===_&&h("\\ not allowed in relative path."),(R=r[m.toLowerCase()])&&(m=R),".."===m?(this._path.pop(),"/"!==_&&"\\"!==_&&this._path.push("")):"."===m&&"/"!==_&&"\\"!==_?this._path.push(""):"."!==m&&("file"===this._scheme&&0===this._path.length&&2===m.length&&c.test(m[0])&&"|"===m[1]&&(m=m[0]+":"),this._path.push(m)),m="","?"===_?(this._query="?",p="query"):"#"===_&&(this._fragment="#",p="fragment"));break;case"query":f||"#"!==_?_!==u&&"\t"!==_&&"\n"!==_&&"\r"!==_&&(this._query+=s(_)):(this._fragment="#",p="fragment");break;case"fragment":_!==u&&"\t"!==_&&"\n"!==_&&"\r"!==_&&(this._fragment+=_)}v++}}function d(){this._scheme="",this._schemeData="",this._username="",this._password=null,this._host="",this._port="",this._path=[],this._query="",this._fragment="",this._isInvalid=!1,this._isRelative=!1}function h(e,t){void 0===t||t instanceof h||(t=new h(String(t))),this._url=e,d.call(this);var r=e.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,"");f.call(this,r,null,t)}h.prototype={toString:function(){return this.href},get href(){if(this._isInvalid)return this._url;var e="";return""===this._username&&null===this._password||(e=this._username+(null!==this._password?":"+this._password:"")+"@"),this.protocol+(this._isRelative?"//"+e+this.host:"")+this.pathname+this._query+this._fragment},set href(e){d.call(this),f.call(this,e)},get protocol(){return this._scheme+":"},set protocol(e){this._isInvalid||f.call(this,e+":","scheme start")},get host(){return this._isInvalid?"":this._port?this._host+":"+this._port:this._host},set host(e){!this._isInvalid&&this._isRelative&&f.call(this,e,"host")},get hostname(){return this._host},set hostname(e){!this._isInvalid&&this._isRelative&&f.call(this,e,"hostname")},get port(){return this._port},set port(e){!this._isInvalid&&this._isRelative&&f.call(this,e,"port")},get pathname(){return this._isInvalid?"":this._isRelative?"/"+this._path.join("/"):this._schemeData},set pathname(e){!this._isInvalid&&this._isRelative&&(this._path=[],f.call(this,e,"relative path start"))},get search(){return this._isInvalid||!this._query||"?"===this._query?"":this._query},set search(e){!this._isInvalid&&this._isRelative&&(this._query="?","?"===e[0]&&(e=e.slice(1)),f.call(this,e,"query"))},get hash(){return this._isInvalid||!this._fragment||"#"===this._fragment?"":this._fragment},set hash(e){this._isInvalid||(this._fragment="#","#"===e[0]&&(e=e.slice(1)),f.call(this,e,"fragment"))},get origin(){var e;if(this._isInvalid||!this._scheme)return"";switch(this._scheme){case"data":case"file":case"javascript":case"mailto":return"null";case"blob":try{return new h(this._schemeData).origin||"null"}catch(t){}return"null"}return(e=this.host)?this._scheme+"://"+e:""}},t.URL=h}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getDocument=function(e){var t,r=new L;if("string"===typeof e)t={url:e};else if((0,a.isArrayBuffer)(e))t={data:e};else if(e instanceof I)t={range:e};else{if("object"!==w(e))throw new Error("Invalid parameter in getDocument, need either Uint8Array, string or a parameter object");if(!e.url&&!e.data&&!e.range)throw new Error("Invalid parameter object: need either .data, .range or .url");t=e}var n=Object.create(null),i=null,s=null;for(var c in t)if("url"!==c||"undefined"===typeof window)if("range"!==c)if("worker"!==c)if("data"!==c||t[c]instanceof Uint8Array)n[c]=t[c];else{var l=t[c];if("string"===typeof l)n[c]=(0,a.stringToBytes)(l);else if("object"!==w(l)||null===l||isNaN(l.length)){if(!(0,a.isArrayBuffer)(l))throw new Error("Invalid PDF binary data: either typed array, string or array-like object is expected in the data property.");n[c]=new Uint8Array(l)}else n[c]=new Uint8Array(l)}else s=t[c];else i=t[c];else n[c]=new a.URL(t[c],window.location).href;n.rangeChunkSize=n.rangeChunkSize||P,n.CMapReaderFactory=n.CMapReaderFactory||o.DOMCMapReaderFactory,n.ignoreErrors=!0!==n.stopAtErrors,n.pdfBug=!0===n.pdfBug;var h=Object.values(a.NativeImageDecoding);if(void 0!==n.nativeImageDecoderSupport&&h.includes(n.nativeImageDecoderSupport)||(n.nativeImageDecoderSupport=u.apiCompatibilityParams.nativeImageDecoderSupport||a.NativeImageDecoding.DECODE),Number.isInteger(n.maxImageSize)||(n.maxImageSize=-1),"boolean"!==typeof n.isEvalSupported&&(n.isEvalSupported=!0),"boolean"!==typeof n.disableFontFace&&(n.disableFontFace=u.apiCompatibilityParams.disableFontFace||!1),"boolean"!==typeof n.disableRange&&(n.disableRange=!1),"boolean"!==typeof n.disableStream&&(n.disableStream=!1),"boolean"!==typeof n.disableAutoFetch&&(n.disableAutoFetch=!1),"boolean"!==typeof n.disableCreateObjectURL&&(n.disableCreateObjectURL=u.apiCompatibilityParams.disableCreateObjectURL||!1),(0,a.setVerbosityLevel)(n.verbosity),!s){var v={postMessageTransfers:n.postMessageTransfers,verbosity:n.verbosity,port:f.GlobalWorkerOptions.workerPort};s=v.port?M.fromPort(v):new M(v),r._worker=s}var m=r.docId;return s.promise.then((function(){if(r.destroyed)throw new Error("Loading aborted");return function(e,t,r,n){return e.destroyed?Promise.reject(new Error("Worker was destroyed")):(r&&(t.length=r.length,t.initialData=r.initialData),e.messageHandler.sendWithPromise("GetDocRequest",{docId:n,apiVersion:"2.1.266",source:{data:t.data,url:t.url,password:t.password,disableAutoFetch:t.disableAutoFetch,rangeChunkSize:t.rangeChunkSize,length:t.length},maxImageSize:t.maxImageSize,disableFontFace:t.disableFontFace,disableCreateObjectURL:t.disableCreateObjectURL,postMessageTransfers:e.postMessageTransfers,docBaseUrl:t.docBaseUrl,nativeImageDecoderSupport:t.nativeImageDecoderSupport,ignoreErrors:t.ignoreErrors,isEvalSupported:t.isEvalSupported}).then((function(t){if(e.destroyed)throw new Error("Worker was destroyed");return t})))}(s,n,i,m).then((function(e){if(r.destroyed)throw new Error("Loading aborted");var t;i?t=new p.PDFDataTransportStream({length:n.length,initialData:n.initialData,disableRange:n.disableRange,disableStream:n.disableStream},i):n.data||(t=E({url:n.url,length:n.length,httpHeaders:n.httpHeaders,withCredentials:n.withCredentials,rangeChunkSize:n.rangeChunkSize,disableRange:n.disableRange,disableStream:n.disableStream}));var a=new d.MessageHandler(m,e,s.port);a.postMessageTransfers=s.postMessageTransfers;var o=new N(a,r,t,n);r._transport=o,a.send("Ready",null)}))})).catch(r._capability.reject),r},t.setPDFNetworkStreamFactory=function(e){E=e},t.build=t.version=t.PDFPageProxy=t.PDFDocumentProxy=t.PDFWorker=t.PDFDataRangeTransport=t.LoopbackPort=void 0;var i=m(n(147)),a=n(1),o=n(151),s=n(152),u=n(153),c=n(154),l=m(n(3)),f=n(156),d=n(157),h=n(158),p=n(160),v=n(161);function m(e){return e&&e.__esModule?e:{default:e}}function g(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(u){i=!0,a=u}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function b(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function _(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function S(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function A(e,t,r){return t&&S(e.prototype,t),r&&S(e,r),e}function w(e){return w="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w(e)}var k,x,P=65536,R=!1,C=!1;"undefined"===typeof window?(R=!0,C=!0):C=!0,"undefined"!==typeof requirejs&&requirejs.toUrl&&(k=requirejs.toUrl("pdfjs-dist/build/pdf.worker.js"));var E,T="undefined"!==typeof requirejs&&requirejs.load;if(x=C?function(){return new Promise((function(e,t){r.e(620).then(function(){try{var n;n=r(92844),e(n.WorkerMessageHandler)}catch(i){t(i)}}.bind(null,r)).catch(t)}))}:T?function(){return new Promise((function(e,t){requirejs(["pdfjs-dist/build/pdf.worker"],(function(r){try{e(r.WorkerMessageHandler)}catch(n){t(n)}}),t)}))}:null,!k&&"object"===("undefined"===typeof document?"undefined":w(document))&&"currentScript"in document){var O=document.currentScript&&document.currentScript.src;O&&(k=O.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}var L=function(){var e=0,t=function(){function t(){_(this,t),this._capability=(0,a.createPromiseCapability)(),this._transport=null,this._worker=null,this.docId="d"+e++,this.destroyed=!1,this.onPassword=null,this.onProgress=null,this.onUnsupportedFeature=null}return A(t,[{key:"destroy",value:function(){var e=this;return this.destroyed=!0,(this._transport?this._transport.destroy():Promise.resolve()).then((function(){e._transport=null,e._worker&&(e._worker.destroy(),e._worker=null)}))}},{key:"then",value:function(e,t){return(0,a.deprecated)("PDFDocumentLoadingTask.then method, use the `promise` getter instead."),this.promise.then.apply(this.promise,arguments)}},{key:"promise",get:function(){return this._capability.promise}}]),t}();return t}(),I=function(){function e(t,r){_(this,e),this.length=t,this.initialData=r,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._readyCapability=(0,a.createPromiseCapability)()}return A(e,[{key:"addRangeListener",value:function(e){this._rangeListeners.push(e)}},{key:"addProgressListener",value:function(e){this._progressListeners.push(e)}},{key:"addProgressiveReadListener",value:function(e){this._progressiveReadListeners.push(e)}},{key:"onDataRange",value:function(e,t){var r=!0,n=!1,i=void 0;try{for(var a,o=this._rangeListeners[Symbol.iterator]();!(r=(a=o.next()).done);r=!0)(0,a.value)(e,t)}catch(s){n=!0,i=s}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}}},{key:"onDataProgress",value:function(e){var t=this;this._readyCapability.promise.then((function(){var r=!0,n=!1,i=void 0;try{for(var a,o=t._progressListeners[Symbol.iterator]();!(r=(a=o.next()).done);r=!0)(0,a.value)(e)}catch(s){n=!0,i=s}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}}))}},{key:"onDataProgressiveRead",value:function(e){var t=this;this._readyCapability.promise.then((function(){var r=!0,n=!1,i=void 0;try{for(var a,o=t._progressiveReadListeners[Symbol.iterator]();!(r=(a=o.next()).done);r=!0)(0,a.value)(e)}catch(s){n=!0,i=s}finally{try{r||null==o.return||o.return()}finally{if(n)throw i}}}))}},{key:"transportReady",value:function(){this._readyCapability.resolve()}},{key:"requestDataRange",value:function(e,t){(0,a.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}},{key:"abort",value:function(){}}]),e}();t.PDFDataRangeTransport=I;var F=function(){function e(t,r,n){_(this,e),this.loadingTask=n,this._pdfInfo=t,this._transport=r}return A(e,[{key:"getPage",value:function(e){return this._transport.getPage(e)}},{key:"getPageIndex",value:function(e){return this._transport.getPageIndex(e)}},{key:"getDestinations",value:function(){return this._transport.getDestinations()}},{key:"getDestination",value:function(e){return this._transport.getDestination(e)}},{key:"getPageLabels",value:function(){return this._transport.getPageLabels()}},{key:"getPageMode",value:function(){return this._transport.getPageMode()}},{key:"getOpenActionDestination",value:function(){return this._transport.getOpenActionDestination()}},{key:"getAttachments",value:function(){return this._transport.getAttachments()}},{key:"getJavaScript",value:function(){return this._transport.getJavaScript()}},{key:"getOutline",value:function(){return this._transport.getOutline()}},{key:"getPermissions",value:function(){return this._transport.getPermissions()}},{key:"getMetadata",value:function(){return this._transport.getMetadata()}},{key:"getData",value:function(){return this._transport.getData()}},{key:"getDownloadInfo",value:function(){return this._transport.downloadInfoCapability.promise}},{key:"getStats",value:function(){return this._transport.getStats()}},{key:"cleanup",value:function(){this._transport.startCleanup()}},{key:"destroy",value:function(){return this.loadingTask.destroy()}},{key:"numPages",get:function(){return this._pdfInfo.numPages}},{key:"fingerprint",get:function(){return this._pdfInfo.fingerprint}},{key:"loadingParams",get:function(){return this._transport.loadingParams}}]),e}();t.PDFDocumentProxy=F;var D=function(){function e(t,r,n){var i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];_(this,e),this.pageIndex=t,this._pageInfo=r,this._transport=n,this._stats=i?new o.StatTimer:o.DummyStatTimer,this._pdfBug=i,this.commonObjs=n.commonObjs,this.objs=new q,this.cleanupAfterRender=!1,this.pendingCleanup=!1,this.intentStates=Object.create(null),this.destroyed=!1}return A(e,[{key:"getViewport",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.scale,r=e.rotation,n=void 0===r?this.rotate:r,i=e.dontFlip,s=void 0!==i&&i;return(arguments.length>1||"number"===typeof arguments[0])&&((0,a.deprecated)("getViewport is called with obsolete arguments."),t=arguments[0],n="number"===typeof arguments[1]?arguments[1]:this.rotate,s="boolean"===typeof arguments[2]&&arguments[2]),new o.PageViewport({viewBox:this.view,scale:t,rotation:n,dontFlip:s})}},{key:"getAnnotations",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).intent,t=void 0===e?null:e;return this.annotationsPromise&&this.annotationsIntent===t||(this.annotationsPromise=this._transport.getAnnotations(this.pageIndex,t),this.annotationsIntent=t),this.annotationsPromise}},{key:"render",value:function(e){var t=this,r=e.canvasContext,n=e.viewport,i=e.intent,s=void 0===i?"display":i,u=e.enableWebGL,c=void 0!==u&&u,l=e.renderInteractiveForms,f=void 0!==l&&l,d=e.transform,h=void 0===d?null:d,p=e.imageLayer,m=void 0===p?null:p,g=e.canvasFactory,y=void 0===g?null:g,b=e.background,_=void 0===b?null:b,S=this._stats;S.time("Overall"),this.pendingCleanup=!1;var A="print"===s?"print":"display",w=y||new o.DOMCanvasFactory,k=new v.WebGLContext({enable:c});this.intentStates[A]||(this.intentStates[A]=Object.create(null));var x=this.intentStates[A];x.displayReadyCapability||(x.receivingOperatorList=!0,x.displayReadyCapability=(0,a.createPromiseCapability)(),x.operatorList={fnArray:[],argsArray:[],lastChunk:!1},S.time("Page Request"),this._transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageNumber-1,intent:A,renderInteractiveForms:!0===f}));var P=function(e){var r=x.renderTasks.indexOf(R);r>=0&&x.renderTasks.splice(r,1),t.cleanupAfterRender&&(t.pendingCleanup=!0),t._tryCleanup(),e?R.capability.reject(e):R.capability.resolve(),S.timeEnd("Rendering"),S.timeEnd("Overall")},R=new U({callback:P,params:{canvasContext:r,viewport:n,transform:h,imageLayer:m,background:_},objs:this.objs,commonObjs:this.commonObjs,operatorList:x.operatorList,pageNumber:this.pageNumber,canvasFactory:w,webGLContext:k,useRequestAnimationFrame:"print"!==A,pdfBug:this._pdfBug});x.renderTasks||(x.renderTasks=[]),x.renderTasks.push(R);var C=R.task;return x.displayReadyCapability.promise.then((function(e){t.pendingCleanup?P():(S.time("Rendering"),R.initializeGraphics(e),R.operatorListChanged())})).catch(P),C}},{key:"getOperatorList",value:function(){var e="oplist";this.intentStates[e]||(this.intentStates[e]=Object.create(null));var t,r=this.intentStates[e];return r.opListReadCapability||((t={}).operatorListChanged=function(){if(r.operatorList.lastChunk){r.opListReadCapability.resolve(r.operatorList);var e=r.renderTasks.indexOf(t);e>=0&&r.renderTasks.splice(e,1)}},r.receivingOperatorList=!0,r.opListReadCapability=(0,a.createPromiseCapability)(),r.renderTasks=[],r.renderTasks.push(t),r.operatorList={fnArray:[],argsArray:[],lastChunk:!1},this._stats.time("Page Request"),this._transport.messageHandler.send("RenderPageRequest",{pageIndex:this.pageIndex,intent:e})),r.opListReadCapability.promise}},{key:"streamTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.normalizeWhitespace,r=void 0!==t&&t,n=e.disableCombineTextItems,i=void 0!==n&&n;return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this.pageNumber-1,normalizeWhitespace:!0===r,combineTextItems:!0!==i},{highWaterMark:100,size:function(e){return e.items.length}})}},{key:"getTextContent",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=this.streamTextContent(e);return new Promise((function(e,r){var n=t.getReader(),i={items:[],styles:Object.create(null)};!function t(){n.read().then((function(r){var n,a=r.value;r.done?e(i):(Object.assign(i.styles,a.styles),(n=i.items).push.apply(n,b(a.items)),t())}),r)}()}))}},{key:"_destroy",value:function(){this.destroyed=!0,this._transport.pageCache[this.pageIndex]=null;var e=[];return Object.keys(this.intentStates).forEach((function(t){"oplist"!==t&&this.intentStates[t].renderTasks.forEach((function(t){var r=t.capability.promise.catch((function(){}));e.push(r),t.cancel()}))}),this),this.objs.clear(),this.annotationsPromise=null,this.pendingCleanup=!1,Promise.all(e)}},{key:"cleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.pendingCleanup=!0,this._tryCleanup(e)}},{key:"_tryCleanup",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.pendingCleanup&&!Object.keys(this.intentStates).some((function(e){var t=this.intentStates[e];return 0!==t.renderTasks.length||t.receivingOperatorList}),this)&&(Object.keys(this.intentStates).forEach((function(e){delete this.intentStates[e]}),this),this.objs.clear(),this.annotationsPromise=null,e&&this._stats instanceof o.StatTimer&&(this._stats=new o.StatTimer),this.pendingCleanup=!1)}},{key:"_startRenderPage",value:function(e,t){var r=this.intentStates[t];r.displayReadyCapability&&r.displayReadyCapability.resolve(e)}},{key:"_renderPageChunk",value:function(e,t){for(var r=this.intentStates[t],n=0,i=e.length;n<i;n++)r.operatorList.fnArray.push(e.fnArray[n]),r.operatorList.argsArray.push(e.argsArray[n]);r.operatorList.lastChunk=e.lastChunk;for(var a=0;a<r.renderTasks.length;a++)r.renderTasks[a].operatorListChanged();e.lastChunk&&(r.receivingOperatorList=!1,this._tryCleanup())}},{key:"pageNumber",get:function(){return this.pageIndex+1}},{key:"rotate",get:function(){return this._pageInfo.rotate}},{key:"ref",get:function(){return this._pageInfo.ref}},{key:"userUnit",get:function(){return this._pageInfo.userUnit}},{key:"view",get:function(){return this._pageInfo.view}},{key:"stats",get:function(){return this._stats instanceof o.StatTimer?this._stats:null}}]),e}();t.PDFPageProxy=D;var j=function(){function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];_(this,e),this._listeners=[],this._defer=t,this._deferred=Promise.resolve(void 0)}return A(e,[{key:"postMessage",value:function(e,t){var r=this;if(this._defer){var n=new WeakMap,i={data:function e(r){if("object"!==w(r)||null===r)return r;if(n.has(r))return n.get(r);var i,o;if((i=r.buffer)&&(0,a.isArrayBuffer)(i)){var s=t&&t.includes(i);return o=r===i?r:s?new r.constructor(i,r.byteOffset,r.byteLength):new r.constructor(r),n.set(r,o),o}for(var u in o=Array.isArray(r)?[]:{},n.set(r,o),r){for(var c=void 0,l=r;!(c=Object.getOwnPropertyDescriptor(l,u));)l=Object.getPrototypeOf(l);"undefined"!==typeof c.value&&"function"!==typeof c.value&&(o[u]=e(c.value))}return o}(e)};this._deferred.then((function(){r._listeners.forEach((function(e){e.call(this,i)}),r)}))}else this._listeners.forEach((function(t){t.call(this,{data:e})}),this)}},{key:"addEventListener",value:function(e,t){this._listeners.push(t)}},{key:"removeEventListener",value:function(e,t){var r=this._listeners.indexOf(t);this._listeners.splice(r,1)}},{key:"terminate",value:function(){this._listeners=[]}}]),e}();t.LoopbackPort=j;var M=function(){var e,t=new WeakMap,r=0;function n(){if(f.GlobalWorkerOptions.workerSrc)return f.GlobalWorkerOptions.workerSrc;if("undefined"!==typeof k)return k;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}function i(){try{if("undefined"!==typeof window)return window.pdfjsWorker&&window.pdfjsWorker.WorkerMessageHandler}catch(e){}return null}var s=function(){function s(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=e.name,n=void 0===r?null:r,i=e.port,o=void 0===i?null:i,u=e.postMessageTransfers,c=void 0===u||u,l=e.verbosity,f=void 0===l?(0,a.getVerbosityLevel)():l;if(_(this,s),o&&t.has(o))throw new Error("Cannot use more than one PDFWorker per port");if(this.name=n,this.destroyed=!1,this.postMessageTransfers=!1!==c,this.verbosity=f,this._readyCapability=(0,a.createPromiseCapability)(),this._port=null,this._webWorker=null,this._messageHandler=null,o)return t.set(o,this),void this._initializeFromPort(o);this._initialize()}return A(s,[{key:"_initializeFromPort",value:function(e){this._port=e,this._messageHandler=new d.MessageHandler("main","worker",e),this._messageHandler.on("ready",(function(){})),this._readyCapability.resolve()}},{key:"_initialize",value:function(){var e=this;if("undefined"!==typeof Worker&&!R&&!i()){var t=n();try{(0,a.isSameOrigin)(window.location.href,t)||(t=function(e){var t="importScripts('"+e+"');";return a.URL.createObjectURL(new Blob([t]))}(new a.URL(t,window.location).href));var r=new Worker(t),o=new d.MessageHandler("main","worker",r),s=function(){r.removeEventListener("error",u),o.destroy(),r.terminate(),e.destroyed?e._readyCapability.reject(new Error("Worker was destroyed")):e._setupFakeWorker()},u=function(){e._webWorker||s()};r.addEventListener("error",u),o.on("test",(function(t){r.removeEventListener("error",u),e.destroyed?s():t&&t.supportTypedArray?(e._messageHandler=o,e._port=r,e._webWorker=r,t.supportTransfers||(e.postMessageTransfers=!1),e._readyCapability.resolve(),o.send("configure",{verbosity:e.verbosity})):(e._setupFakeWorker(),o.destroy(),r.terminate())})),o.on("ready",(function(t){if(r.removeEventListener("error",u),e.destroyed)s();else try{c()}catch(n){e._setupFakeWorker()}}));var c=function(){var t=new Uint8Array([e.postMessageTransfers?255:0]);try{o.send("test",t,[t.buffer])}catch(r){(0,a.info)("Cannot use postMessage transfers"),t[0]=0,o.send("test",t)}};return void c()}catch(l){(0,a.info)("The worker has been disabled.")}}this._setupFakeWorker()}},{key:"_setupFakeWorker",value:function(){var t=this;R||((0,a.warn)("Setting up fake worker."),R=!0),function(){if(e)return e.promise;e=(0,a.createPromiseCapability)();var t=i();return t?(e.resolve(t),e.promise):((x||function(){return(0,o.loadScript)(n()).then((function(){return window.pdfjsWorker.WorkerMessageHandler}))})().then(e.resolve,e.reject),e.promise)}().then((function(e){if(t.destroyed)t._readyCapability.reject(new Error("Worker was destroyed"));else{var n=new j;t._port=n;var i="fake"+r++,a=new d.MessageHandler(i+"_worker",i,n);e.setup(a,n);var o=new d.MessageHandler(i,i+"_worker",n);t._messageHandler=o,t._readyCapability.resolve()}})).catch((function(e){t._readyCapability.reject(new Error('Setting up fake worker failed: "'.concat(e.message,'".')))}))}},{key:"destroy",value:function(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),t.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}},{key:"promise",get:function(){return this._readyCapability.promise}},{key:"port",get:function(){return this._port}},{key:"messageHandler",get:function(){return this._messageHandler}}],[{key:"fromPort",value:function(e){if(!e||!e.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return t.has(e.port)?t.get(e.port):new s(e)}},{key:"getWorkerSrc",value:function(){return n()}}]),s}();return s}();t.PDFWorker=M;var N=function(){function e(t,r,n,i){_(this,e),this.messageHandler=t,this.loadingTask=r,this.commonObjs=new q,this.fontLoader=new s.FontLoader({docId:r.docId,onUnsupportedFeature:this._onUnsupportedFeature.bind(this)}),this._params=i,this.CMapReaderFactory=new i.CMapReaderFactory({baseUrl:i.cMapUrl,isCompressed:i.cMapPacked}),this.destroyed=!1,this.destroyCapability=null,this._passwordCapability=null,this._networkStream=n,this._fullReader=null,this._lastProgress=null,this.pageCache=[],this.pagePromises=[],this.downloadInfoCapability=(0,a.createPromiseCapability)(),this.setupMessageHandler()}return A(e,[{key:"destroy",value:function(){var e=this;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=(0,a.createPromiseCapability)(),this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));var t=[];this.pageCache.forEach((function(e){e&&t.push(e._destroy())})),this.pageCache=[],this.pagePromises=[];var r=this.messageHandler.sendWithPromise("Terminate",null);return t.push(r),Promise.all(t).then((function(){e.fontLoader.clear(),e._networkStream&&e._networkStream.cancelAllRequests(),e.messageHandler&&(e.messageHandler.destroy(),e.messageHandler=null),e.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise}},{key:"setupMessageHandler",value:function(){var e=this.messageHandler,t=this.loadingTask;e.on("GetReader",(function(e,t){var r=this;(0,a.assert)(this._networkStream),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=function(e){r._lastProgress={loaded:e.loaded,total:e.total}},t.onPull=function(){r._fullReader.read().then((function(e){var r=e.value;e.done?t.close():((0,a.assert)((0,a.isArrayBuffer)(r)),t.enqueue(new Uint8Array(r),1,[r]))})).catch((function(e){t.error(e)}))},t.onCancel=function(e){r._fullReader.cancel(e)}}),this),e.on("ReaderHeadersReady",(function(e){var r=this,n=(0,a.createPromiseCapability)(),i=this._fullReader;return i.headersReady.then((function(){i.isStreamingSupported&&i.isRangeSupported||(r._lastProgress&&t.onProgress&&t.onProgress(r._lastProgress),i.onProgress=function(e){t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})}),n.resolve({isStreamingSupported:i.isStreamingSupported,isRangeSupported:i.isRangeSupported,contentLength:i.contentLength})}),n.reject),n.promise}),this),e.on("GetRangeReader",(function(e,t){(0,a.assert)(this._networkStream);var r=this._networkStream.getRangeReader(e.begin,e.end);t.onPull=function(){r.read().then((function(e){var r=e.value;e.done?t.close():((0,a.assert)((0,a.isArrayBuffer)(r)),t.enqueue(new Uint8Array(r),1,[r]))})).catch((function(e){t.error(e)}))},t.onCancel=function(e){r.cancel(e)}}),this),e.on("GetDoc",(function(e){var r=e.pdfInfo;this.numPages=r.numPages,this.pdfDocument=new F(r,this,t),t._capability.resolve(this.pdfDocument)}),this),e.on("PasswordRequest",(function(e){var r=this;if(this._passwordCapability=(0,a.createPromiseCapability)(),t.onPassword)try{t.onPassword((function(e){r._passwordCapability.resolve({password:e})}),e.code)}catch(n){this._passwordCapability.reject(n)}else this._passwordCapability.reject(new a.PasswordException(e.message,e.code));return this._passwordCapability.promise}),this),e.on("PasswordException",(function(e){t._capability.reject(new a.PasswordException(e.message,e.code))}),this),e.on("InvalidPDF",(function(e){t._capability.reject(new a.InvalidPDFException(e.message))}),this),e.on("MissingPDF",(function(e){t._capability.reject(new a.MissingPDFException(e.message))}),this),e.on("UnexpectedResponse",(function(e){t._capability.reject(new a.UnexpectedResponseException(e.message,e.status))}),this),e.on("UnknownError",(function(e){t._capability.reject(new a.UnknownErrorException(e.message,e.details))}),this),e.on("DataLoaded",(function(e){t.onProgress&&t.onProgress({loaded:e.length,total:e.length}),this.downloadInfoCapability.resolve(e)}),this),e.on("StartRenderPage",(function(e){if(!this.destroyed){var t=this.pageCache[e.pageIndex];t._stats.timeEnd("Page Request"),t._startRenderPage(e.transparency,e.intent)}}),this),e.on("RenderPageChunk",(function(e){this.destroyed||this.pageCache[e.pageIndex]._renderPageChunk(e.operatorList,e.intent)}),this),e.on("commonobj",(function(t){var r=this;if(!this.destroyed){var n=y(t,3),i=n[0],o=n[1],u=n[2];if(!this.commonObjs.has(i))switch(o){case"Font":var c=this._params;if("error"in u){var f=u.error;(0,a.warn)("Error during font loading: ".concat(f)),this.commonObjs.resolve(i,f);break}var d=null;c.pdfBug&&l.default.FontInspector&&l.default.FontInspector.enabled&&(d={registerFont:function(e,t){l.default.FontInspector.fontAdded(e,t)}});var h=new s.FontFaceObject(u,{isEvalSupported:c.isEvalSupported,disableFontFace:c.disableFontFace,ignoreErrors:c.ignoreErrors,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),fontRegistry:d});this.fontLoader.bind(h).then((function(){r.commonObjs.resolve(i,h)}),(function(t){e.sendWithPromise("FontFallback",{id:i}).finally((function(){r.commonObjs.resolve(i,h)}))}));break;case"FontPath":this.commonObjs.resolve(i,u);break;default:throw new Error("Got unknown common object type ".concat(o))}}}),this),e.on("obj",(function(e){if(!this.destroyed){var t=y(e,4),r=t[0],n=t[1],i=t[2],a=t[3],o=this.pageCache[n];if(!o.objs.has(r))switch(i){case"JpegStream":return new Promise((function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=function(){t(new Error("Error during JPEG image loading"))},r.src=a})).then((function(e){o.objs.resolve(r,e)}));case"Image":o.objs.resolve(r,a),a&&"data"in a&&a.data.length>8e6&&(o.cleanupAfterRender=!0);break;default:throw new Error("Got unknown object type ".concat(i))}}}),this),e.on("DocProgress",(function(e){this.destroyed||t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})}),this),e.on("PageError",(function(e){if(!this.destroyed){var t=this.pageCache[e.pageNum-1].intentStates[e.intent];if(!t.displayReadyCapability)throw new Error(e.error);if(t.displayReadyCapability.reject(e.error),t.operatorList){t.operatorList.lastChunk=!0;for(var r=0;r<t.renderTasks.length;r++)t.renderTasks[r].operatorListChanged()}}}),this),e.on("UnsupportedFeature",this._onUnsupportedFeature,this),e.on("JpegDecode",(function(e){if(this.destroyed)return Promise.reject(new Error("Worker was destroyed"));if("undefined"===typeof document)return Promise.reject(new Error('"document" is not defined.'));var t=y(e,2),r=t[0],n=t[1];return 3!==n&&1!==n?Promise.reject(new Error("Only 3 components or 1 component can be returned")):new Promise((function(e,t){var i=new Image;i.onload=function(){var t=i.width,r=i.height,a=t*r,o=4*a,s=new Uint8ClampedArray(a*n),u=document.createElement("canvas");u.width=t,u.height=r;var c=u.getContext("2d");c.drawImage(i,0,0);var l=c.getImageData(0,0,t,r).data;if(3===n)for(var f=0,d=0;f<o;f+=4,d+=3)s[d]=l[f],s[d+1]=l[f+1],s[d+2]=l[f+2];else if(1===n)for(var h=0,p=0;h<o;h+=4,p++)s[p]=l[h];e({data:s,width:t,height:r})},i.onerror=function(){t(new Error("JpegDecode failed to load image"))},i.src=r}))}),this),e.on("FetchBuiltInCMap",(function(e){return this.destroyed?Promise.reject(new Error("Worker was destroyed")):this.CMapReaderFactory.fetch({name:e.name})}),this)}},{key:"_onUnsupportedFeature",value:function(e){var t=e.featureId;this.destroyed||this.loadingTask.onUnsupportedFeature&&this.loadingTask.onUnsupportedFeature(t)}},{key:"getData",value:function(){return this.messageHandler.sendWithPromise("GetData",null)}},{key:"getPage",value:function(e){var t=this;if(!Number.isInteger(e)||e<=0||e>this.numPages)return Promise.reject(new Error("Invalid page request"));var r=e-1;if(r in this.pagePromises)return this.pagePromises[r];var n=this.messageHandler.sendWithPromise("GetPage",{pageIndex:r}).then((function(e){if(t.destroyed)throw new Error("Transport destroyed");var n=new D(r,e,t,t._params.pdfBug);return t.pageCache[r]=n,n}));return this.pagePromises[r]=n,n}},{key:"getPageIndex",value:function(e){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:e}).catch((function(e){return Promise.reject(new Error(e))}))}},{key:"getAnnotations",value:function(e,t){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:e,intent:t})}},{key:"getDestinations",value:function(){return this.messageHandler.sendWithPromise("GetDestinations",null)}},{key:"getDestination",value:function(e){return"string"!==typeof e?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:e})}},{key:"getPageLabels",value:function(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}},{key:"getPageMode",value:function(){return this.messageHandler.sendWithPromise("GetPageMode",null)}},{key:"getOpenActionDestination",value:function(){return this.messageHandler.sendWithPromise("getOpenActionDestination",null)}},{key:"getAttachments",value:function(){return this.messageHandler.sendWithPromise("GetAttachments",null)}},{key:"getJavaScript",value:function(){return this.messageHandler.sendWithPromise("GetJavaScript",null)}},{key:"getOutline",value:function(){return this.messageHandler.sendWithPromise("GetOutline",null)}},{key:"getPermissions",value:function(){return this.messageHandler.sendWithPromise("GetPermissions",null)}},{key:"getMetadata",value:function(){var e=this;return this.messageHandler.sendWithPromise("GetMetadata",null).then((function(t){return{info:t[0],metadata:t[1]?new h.Metadata(t[1]):null,contentDispositionFilename:e._fullReader?e._fullReader.filename:null}}))}},{key:"getStats",value:function(){return this.messageHandler.sendWithPromise("GetStats",null)}},{key:"startCleanup",value:function(){var e=this;this.messageHandler.sendWithPromise("Cleanup",null).then((function(){for(var t=0,r=e.pageCache.length;t<r;t++){var n=e.pageCache[t];n&&n.cleanup()}e.commonObjs.clear(),e.fontLoader.clear()}))}},{key:"loadingParams",get:function(){var e=this._params;return(0,a.shadow)(this,"loadingParams",{disableAutoFetch:e.disableAutoFetch,disableCreateObjectURL:e.disableCreateObjectURL,disableFontFace:e.disableFontFace,nativeImageDecoderSupport:e.nativeImageDecoderSupport})}}]),e}(),q=function(){function e(){_(this,e),this._objs=Object.create(null)}return A(e,[{key:"_ensureObj",value:function(e){return this._objs[e]?this._objs[e]:this._objs[e]={capability:(0,a.createPromiseCapability)(),data:null,resolved:!1}}},{key:"get",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t)return this._ensureObj(e).capability.promise.then(t),null;var r=this._objs[e];if(!r||!r.resolved)throw new Error("Requesting object that isn't resolved yet ".concat(e,"."));return r.data}},{key:"has",value:function(e){var t=this._objs[e];return!!t&&t.resolved}},{key:"resolve",value:function(e,t){var r=this._ensureObj(e);r.resolved=!0,r.data=t,r.capability.resolve(t)}},{key:"clear",value:function(){this._objs=Object.create(null)}}]),e}(),W=function(){function e(t){_(this,e),this._internalRenderTask=t,this.onContinue=null}return A(e,[{key:"cancel",value:function(){this._internalRenderTask.cancel()}},{key:"then",value:function(e,t){return(0,a.deprecated)("RenderTask.then method, use the `promise` getter instead."),this.promise.then.apply(this.promise,arguments)}},{key:"promise",get:function(){return this._internalRenderTask.capability.promise}}]),e}(),U=function(){var e=new WeakSet,t=function(){function t(e){var r=e.callback,n=e.params,i=e.objs,o=e.commonObjs,s=e.operatorList,u=e.pageNumber,c=e.canvasFactory,l=e.webGLContext,f=e.useRequestAnimationFrame,d=void 0!==f&&f,h=e.pdfBug,p=void 0!==h&&h;_(this,t),this.callback=r,this.params=n,this.objs=i,this.commonObjs=o,this.operatorListIdx=null,this.operatorList=s,this.pageNumber=u,this.canvasFactory=c,this.webGLContext=l,this._pdfBug=p,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===d&&"undefined"!==typeof window,this.cancelled=!1,this.capability=(0,a.createPromiseCapability)(),this.task=new W(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=n.canvasContext.canvas}return A(t,[{key:"initializeGraphics",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.cancelled){if(this._canvas){if(e.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");e.add(this._canvas)}this._pdfBug&&l.default.StepperManager&&l.default.StepperManager.enabled&&(this.stepper=l.default.StepperManager.create(this.pageNumber-1),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());var r=this.params,n=r.canvasContext,i=r.viewport,a=r.transform,o=r.imageLayer,s=r.background;this.gfx=new c.CanvasGraphics(n,this.commonObjs,this.objs,this.canvasFactory,this.webGLContext,o),this.gfx.beginDrawing({transform:a,viewport:i,transparency:t,background:s}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback&&this.graphicsReadyCallback()}}},{key:"cancel",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.running=!1,this.cancelled=!0,this.gfx&&this.gfx.endDrawing(),this._canvas&&e.delete(this._canvas),this.callback(t||new o.RenderingCancelledException("Rendering cancelled, page ".concat(this.pageNumber),"canvas"))}},{key:"operatorListChanged",value:function(){this.graphicsReady?(this.stepper&&this.stepper.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}},{key:"_continue",value:function(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}},{key:"_scheduleNext",value:function(){var e=this;this._useRequestAnimationFrame?window.requestAnimationFrame((function(){e._nextBound().catch(e.cancel.bind(e))})):Promise.resolve().then(this._nextBound).catch(this.cancel.bind(this))}},{key:"_next",value:function(){var t,r=(t=i.default.mark((function t(){return i.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!this.cancelled){t.next=2;break}return t.abrupt("return");case 2:this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),this._canvas&&e.delete(this._canvas),this.callback()));case 4:case"end":return t.stop()}}),t,this)})),function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function o(e){g(a,n,i,o,s,"next",e)}function s(e){g(a,n,i,o,s,"throw",e)}o(void 0)}))});return function(){return r.apply(this,arguments)}}()}]),t}();return t}();t.version="2.1.266",t.build="81f5835c"},function(e,t,r){"use strict";e.exports=r(148)},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var i=function(){return this||"object"===("undefined"===typeof self?"undefined":n(self))&&self}()||Function("return this")(),a=i.regeneratorRuntime&&Object.getOwnPropertyNames(i).indexOf("regeneratorRuntime")>=0,o=a&&i.regeneratorRuntime;if(i.regeneratorRuntime=void 0,e.exports=r(149),a)i.regeneratorRuntime=o;else try{delete i.regeneratorRuntime}catch(s){i.regeneratorRuntime=void 0}},function(e,t,r){"use strict";(function(e){function t(e){return t="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}!function(r){var n,i=Object.prototype,a=i.hasOwnProperty,o="function"===typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",u=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag",l="object"===t(e),f=r.regeneratorRuntime;if(f)l&&(e.exports=f);else{(f=r.regeneratorRuntime=l?e.exports:{}).wrap=S;var d="suspendedStart",h="suspendedYield",p="executing",v="completed",m={},g={};g[s]=function(){return this};var y=Object.getPrototypeOf,b=y&&y(y(L([])));b&&b!==i&&a.call(b,s)&&(g=b);var _=x.prototype=w.prototype=Object.create(g);k.prototype=_.constructor=x,x.constructor=k,x[c]=k.displayName="GeneratorFunction",f.isGeneratorFunction=function(e){var t="function"===typeof e&&e.constructor;return!!t&&(t===k||"GeneratorFunction"===(t.displayName||t.name))},f.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,c in e||(e[c]="GeneratorFunction")),e.prototype=Object.create(_),e},f.awrap=function(e){return{__await:e}},P(R.prototype),R.prototype[u]=function(){return this},f.AsyncIterator=R,f.async=function(e,t,r,n){var i=new R(S(e,t,r,n));return f.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},P(_),_[c]="Generator",_[s]=function(){return this},_.toString=function(){return"[object Generator]"},f.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},f.values=L,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(T),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,i){return s.type="throw",s.arg=e,t.next=r,i&&(t.method="next",t.arg=n),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var u=a.call(o,"catchLoc"),c=a.call(o,"finallyLoc");if(u&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(u){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=e,o.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var i=n.arg;T(r)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:L(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=n),m}}}function S(e,t,r,n){var i=t&&t.prototype instanceof w?t:w,a=Object.create(i.prototype),o=new O(n||[]);return a._invoke=function(e,t,r){var n=d;return function(i,a){if(n===p)throw new Error("Generator is already running");if(n===v){if("throw"===i)throw a;return I()}for(r.method=i,r.arg=a;;){var o=r.delegate;if(o){var s=C(o,r);if(s){if(s===m)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===d)throw n=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=p;var u=A(e,t,r);if("normal"===u.type){if(n=r.done?v:h,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n=v,r.method="throw",r.arg=u.arg)}}}(e,r,o),a}function A(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(n){return{type:"throw",arg:n}}}function w(){}function k(){}function x(){}function P(e){["next","throw","return"].forEach((function(t){e[t]=function(e){return this._invoke(t,e)}}))}function R(e){function r(n,i,o,s){var u=A(e[n],e,i);if("throw"!==u.type){var c=u.arg,l=c.value;return l&&"object"===t(l)&&a.call(l,"__await")?Promise.resolve(l.__await).then((function(e){r("next",e,o,s)}),(function(e){r("throw",e,o,s)})):Promise.resolve(l).then((function(e){c.value=e,o(c)}),(function(e){return r("throw",e,o,s)}))}s(u.arg)}var n;this._invoke=function(e,t){function i(){return new Promise((function(n,i){r(e,t,n,i)}))}return n=n?n.then(i,i):i()}}function C(e,t){var r=e.iterator[t.method];if(r===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=n,C(e,t),"throw"===t.method))return m;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return m}var i=A(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,m;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,m):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function E(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function L(e){if(e){var t=e[s];if(t)return t.call(e);if("function"===typeof e.next)return e;if(!isNaN(e.length)){var r=-1,i=function t(){for(;++r<e.length;)if(a.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=n,t.done=!0,t};return i.next=i}}return{next:I}}function I(){return{value:n,done:!0}}}(function(){return this||"object"===("undefined"===typeof self?"undefined":t(self))&&self}()||Function("return this")())}).call(this,r(150)(e))},function(e,t,r){"use strict";e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addLinkAttributes=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=t.url,i=t.target,a=t.rel;if(e.href=e.title=r?(0,n.removeNullCharacters)(r):"",r){var o=Object.values(p).includes(i)?i:p.NONE;e.target=v[o],e.rel="string"===typeof a?a:s}},t.getFilenameFromUrl=function(e){var t=e.indexOf("#"),r=e.indexOf("?"),n=Math.min(t>0?t:e.length,r>0?r:e.length);return e.substring(e.lastIndexOf("/",n)+1,n)},t.loadScript=function(e){return new Promise((function(t,r){var n=document.createElement("script");n.src=e,n.onload=t,n.onerror=function(){r(new Error("Cannot load script at: ".concat(n.src)))},(document.head||document.documentElement).appendChild(n)}))},t.DummyStatTimer=t.StatTimer=t.DOMSVGFactory=t.DOMCMapReaderFactory=t.DOMCanvasFactory=t.DEFAULT_LINK_REL=t.LinkTarget=t.RenderingCancelledException=t.PageViewport=void 0;var n=r(1);function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),e}var s="noopener noreferrer nofollow";t.DEFAULT_LINK_REL=s;var u="http://www.w3.org/2000/svg",c=function(){function e(){i(this,e)}return o(e,[{key:"create",value:function(e,t){if(e<=0||t<=0)throw new Error("invalid canvas size");var r=document.createElement("canvas"),n=r.getContext("2d");return r.width=e,r.height=t,{canvas:r,context:n}}},{key:"reset",value:function(e,t,r){if(!e.canvas)throw new Error("canvas is not specified");if(t<=0||r<=0)throw new Error("invalid canvas size");e.canvas.width=t,e.canvas.height=r}},{key:"destroy",value:function(e){if(!e.canvas)throw new Error("canvas is not specified");e.canvas.width=0,e.canvas.height=0,e.canvas=null,e.context=null}}]),e}();t.DOMCanvasFactory=c;var l=function(){function e(t){var r=t.baseUrl,n=void 0===r?null:r,a=t.isCompressed,o=void 0!==a&&a;i(this,e),this.baseUrl=n,this.isCompressed=o}return o(e,[{key:"fetch",value:function(e){var t=this,r=e.name;return this.baseUrl?r?new Promise((function(e,i){var a=t.baseUrl+r+(t.isCompressed?".bcmap":""),o=new XMLHttpRequest;o.open("GET",a,!0),t.isCompressed&&(o.responseType="arraybuffer"),o.onreadystatechange=function(){if(o.readyState===XMLHttpRequest.DONE){var r;if((200===o.status||0===o.status)&&(t.isCompressed&&o.response?r=new Uint8Array(o.response):!t.isCompressed&&o.responseText&&(r=(0,n.stringToBytes)(o.responseText)),r))return void e({cMapData:r,compressionType:t.isCompressed?n.CMapCompressionType.BINARY:n.CMapCompressionType.NONE});i(new Error("Unable to load "+(t.isCompressed?"binary ":"")+"CMap at: "+a))}},o.send(null)})):Promise.reject(new Error("CMap name must be specified.")):Promise.reject(new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.'))}}]),e}();t.DOMCMapReaderFactory=l;var f=function(){function e(){i(this,e)}return o(e,[{key:"create",value:function(e,t){(0,n.assert)(e>0&&t>0,"Invalid SVG dimensions");var r=document.createElementNS(u,"svg:svg");return r.setAttribute("version","1.1"),r.setAttribute("width",e+"px"),r.setAttribute("height",t+"px"),r.setAttribute("preserveAspectRatio","none"),r.setAttribute("viewBox","0 0 "+e+" "+t),r}},{key:"createElement",value:function(e){return(0,n.assert)("string"===typeof e,"Invalid SVG element type"),document.createElementNS(u,e)}}]),e}();t.DOMSVGFactory=f;var d=function(){function e(t){var r=t.viewBox,n=t.scale,a=t.rotation,o=t.offsetX,s=void 0===o?0:o,u=t.offsetY,c=void 0===u?0:u,l=t.dontFlip,f=void 0!==l&&l;i(this,e),this.viewBox=r,this.scale=n,this.rotation=a,this.offsetX=s,this.offsetY=c;var d,h,p,v,m,g,y,b,_=(r[2]+r[0])/2,S=(r[3]+r[1])/2;switch(a=(a%=360)<0?a+360:a){case 180:d=-1,h=0,p=0,v=1;break;case 90:d=0,h=1,p=1,v=0;break;case 270:d=0,h=-1,p=-1,v=0;break;default:d=1,h=0,p=0,v=-1}f&&(p=-p,v=-v),0===d?(m=Math.abs(S-r[1])*n+s,g=Math.abs(_-r[0])*n+c,y=Math.abs(r[3]-r[1])*n,b=Math.abs(r[2]-r[0])*n):(m=Math.abs(_-r[0])*n+s,g=Math.abs(S-r[1])*n+c,y=Math.abs(r[2]-r[0])*n,b=Math.abs(r[3]-r[1])*n),this.transform=[d*n,h*n,p*n,v*n,m-d*n*_-p*n*S,g-h*n*_-v*n*S],this.width=y,this.height=b}return o(e,[{key:"clone",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.scale,n=void 0===r?this.scale:r,i=t.rotation,a=void 0===i?this.rotation:i,o=t.dontFlip,s=void 0!==o&&o;return new e({viewBox:this.viewBox.slice(),scale:n,rotation:a,offsetX:this.offsetX,offsetY:this.offsetY,dontFlip:s})}},{key:"convertToViewportPoint",value:function(e,t){return n.Util.applyTransform([e,t],this.transform)}},{key:"convertToViewportRectangle",value:function(e){var t=n.Util.applyTransform([e[0],e[1]],this.transform),r=n.Util.applyTransform([e[2],e[3]],this.transform);return[t[0],t[1],r[0],r[1]]}},{key:"convertToPdfPoint",value:function(e,t){return n.Util.applyInverseTransform([e,t],this.transform)}}]),e}();t.PageViewport=d;var h=function(){function e(e,t){this.message=e,this.type=t}return e.prototype=new Error,e.prototype.name="RenderingCancelledException",e.constructor=e,e}();t.RenderingCancelledException=h;var p={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};t.LinkTarget=p;var v=["","_self","_blank","_parent","_top"],m=function(){function e(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];i(this,e),this.enabled=!!t,this.started=Object.create(null),this.times=[]}return o(e,[{key:"time",value:function(e){this.enabled&&(e in this.started&&(0,n.warn)("Timer is already running for "+e),this.started[e]=Date.now())}},{key:"timeEnd",value:function(e){this.enabled&&(e in this.started||(0,n.warn)("Timer has not been started for "+e),this.times.push({name:e,start:this.started[e],end:Date.now()}),delete this.started[e])}},{key:"toString",value:function(){for(var e=this.times,t="",r=0,n=0,i=e.length;n<i;++n){var a=e[n].name;a.length>r&&(r=a.length)}for(var o=0,s=e.length;o<s;++o){var u=e[o],c=u.end-u.start;t+="".concat(u.name.padEnd(r)," ").concat(c,"ms\n")}return t}}]),e}();t.StatTimer=m;var g=function(){function e(){i(this,e),(0,n.unreachable)("Cannot initialize DummyStatTimer.")}return o(e,null,[{key:"time",value:function(e){}},{key:"timeEnd",value:function(e){}},{key:"toString",value:function(){return""}}]),e}();t.DummyStatTimer=g},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FontLoader=t.FontFaceObject=void 0;var n,i=(n=r(147))&&n.__esModule?n:{default:n},a=r(1);function o(e){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function s(e,t){return!t||"object"!==o(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function c(e,t){return c=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},c(e,t)}function l(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function f(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function h(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),e}var p,v=function(){function e(t){var r=t.docId,n=t.onUnsupportedFeature;f(this,e),this.constructor===e&&(0,a.unreachable)("Cannot initialize BaseFontLoader."),this.docId=r,this._onUnsupportedFeature=n,this.nativeFontFaces=[],this.styleElement=null}return h(e,[{key:"addNativeFontFace",value:function(e){this.nativeFontFaces.push(e),document.fonts.add(e)}},{key:"insertRule",value:function(e){var t=this.styleElement;t||((t=this.styleElement=document.createElement("style")).id="PDFJS_FONT_STYLE_TAG_".concat(this.docId),document.documentElement.getElementsByTagName("head")[0].appendChild(t));var r=t.sheet;r.insertRule(e,r.cssRules.length)}},{key:"clear",value:function(){this.nativeFontFaces.forEach((function(e){document.fonts.delete(e)})),this.nativeFontFaces.length=0,this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}},{key:"bind",value:function(){var e,t=(e=i.default.mark((function e(t){var r,n,o=this;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.attached&&!t.missingFile){e.next=2;break}return e.abrupt("return");case 2:if(t.attached=!0,!this.isFontLoadingAPISupported){e.next=19;break}if(!(r=t.createNativeFontFace())){e.next=18;break}return this.addNativeFontFace(r),e.prev=7,e.next=10,r.loaded;case 10:e.next=18;break;case 12:throw e.prev=12,e.t0=e.catch(7),this._onUnsupportedFeature({featureId:a.UNSUPPORTED_FEATURES.font}),(0,a.warn)("Failed to load font '".concat(r.family,"': '").concat(e.t0,"'.")),t.disableFontFace=!0,e.t0;case 18:return e.abrupt("return");case 19:if(!(n=t.createFontFaceRule())){e.next=25;break}if(this.insertRule(n),!this.isSyncFontLoadingSupported){e.next=24;break}return e.abrupt("return");case 24:return e.abrupt("return",new Promise((function(e){var r=o._queueLoadingCallback(e);o._prepareFontLoadEvent([n],[t],r)})));case 25:case"end":return e.stop()}}),e,this,[[7,12]])})),function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){l(a,n,i,o,s,"next",e)}function s(e){l(a,n,i,o,s,"throw",e)}o(void 0)}))});return function(e){return t.apply(this,arguments)}}()},{key:"_queueLoadingCallback",value:function(e){(0,a.unreachable)("Abstract method `_queueLoadingCallback`.")}},{key:"_prepareFontLoadEvent",value:function(e,t,r){(0,a.unreachable)("Abstract method `_prepareFontLoadEvent`.")}},{key:"isFontLoadingAPISupported",get:function(){(0,a.unreachable)("Abstract method `isFontLoadingAPISupported`.")}},{key:"isSyncFontLoadingSupported",get:function(){(0,a.unreachable)("Abstract method `isSyncFontLoadingSupported`.")}},{key:"_loadTestFont",get:function(){(0,a.unreachable)("Abstract method `_loadTestFont`.")}}]),e}();t.FontLoader=p,t.FontLoader=p=function(e){function t(e){var r;return f(this,t),(r=s(this,u(t).call(this,e))).loadingContext={requests:[],nextRequestId:0},r.loadTestFontId=0,r}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&c(e,t)}(t,e),h(t,[{key:"_queueLoadingCallback",value:function(e){var t=this.loadingContext,r={id:"pdfjs-font-loading-".concat(t.nextRequestId++),done:!1,complete:function(){for((0,a.assert)(!r.done,"completeRequest() cannot be called twice."),r.done=!0;t.requests.length>0&&t.requests[0].done;){var e=t.requests.shift();setTimeout(e.callback,0)}},callback:e};return t.requests.push(r),r}},{key:"_prepareFontLoadEvent",value:function(e,t,r){function n(e,t){return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|255&e.charCodeAt(t+3)}function i(e,t,r,n){return e.substring(0,t)+n+e.substring(t+r)}var o,s,u=document.createElement("canvas");u.width=1,u.height=1;var c=u.getContext("2d"),l=0,f="lt".concat(Date.now()).concat(this.loadTestFontId++),d=this._loadTestFont,h=1482184792,p=n(d=i(d,976,f.length,f),16);for(o=0,s=f.length-3;o<s;o+=4)p=p-h+n(f,o)|0;o<f.length&&(p=p-h+n(f+"XXX",o)|0),d=i(d,16,4,(0,a.string32)(p));var v="url(data:font/opentype;base64,".concat(btoa(d),");"),m='@font-face {font-family:"'.concat(f,'";src:').concat(v,"}");this.insertRule(m);var g=[];for(o=0,s=t.length;o<s;o++)g.push(t[o].loadedName);g.push(f);var y=document.createElement("div");for(y.setAttribute("style","visibility: hidden;width: 10px; height: 10px;position: absolute; top: 0px; left: 0px;"),o=0,s=g.length;o<s;++o){var b=document.createElement("span");b.textContent="Hi",b.style.fontFamily=g[o],y.appendChild(b)}document.body.appendChild(y),function e(t,r){if(++l>30)return(0,a.warn)("Load test font never loaded."),void r();c.font="30px "+t,c.fillText(".",0,20),c.getImageData(0,0,1,1).data[3]>0?r():setTimeout(e.bind(null,t,r))}(f,(function(){document.body.removeChild(y),r.complete()}))}},{key:"isFontLoadingAPISupported",get:function(){var e="undefined"!==typeof document&&!!document.fonts;if(e&&"undefined"!==typeof navigator){var t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);t&&t[1]<63&&(e=!1)}return(0,a.shadow)(this,"isFontLoadingAPISupported",e)}},{key:"isSyncFontLoadingSupported",get:function(){var e=!1;if("undefined"===typeof navigator)e=!0;else{var t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent);t&&t[1]>=14&&(e=!0)}return(0,a.shadow)(this,"isSyncFontLoadingSupported",e)}},{key:"_loadTestFont",get:function(){return(0,a.shadow)(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}}]),t}(v);var m={get value(){return(0,a.shadow)(this,"value",(0,a.isEvalSupported)())}},g=function(){function e(t,r){var n=r.isEvalSupported,i=void 0===n||n,a=r.disableFontFace,o=void 0!==a&&a,s=r.ignoreErrors,u=void 0!==s&&s,c=r.onUnsupportedFeature,l=void 0===c?null:c,d=r.fontRegistry,h=void 0===d?null:d;for(var p in f(this,e),this.compiledGlyphs=Object.create(null),t)this[p]=t[p];this.isEvalSupported=!1!==i,this.disableFontFace=!0===o,this.ignoreErrors=!0===u,this._onUnsupportedFeature=l,this.fontRegistry=h}return h(e,[{key:"createNativeFontFace",value:function(){if(!this.data||this.disableFontFace)return null;var e=new FontFace(this.loadedName,this.data,{});return this.fontRegistry&&this.fontRegistry.registerFont(this),e}},{key:"createFontFaceRule",value:function(){if(!this.data||this.disableFontFace)return null;var e=(0,a.bytesToString)(new Uint8Array(this.data)),t="url(data:".concat(this.mimetype,";base64,").concat(btoa(e),");"),r='@font-face {font-family:"'.concat(this.loadedName,'";src:').concat(t,"}");return this.fontRegistry&&this.fontRegistry.registerFont(this,t),r}},{key:"getPathGenerator",value:function(e,t){if(void 0!==this.compiledGlyphs[t])return this.compiledGlyphs[t];var r,n;try{r=e.get(this.loadedName+"_path_"+t)}catch(c){if(!this.ignoreErrors)throw c;return this._onUnsupportedFeature&&this._onUnsupportedFeature({featureId:a.UNSUPPORTED_FEATURES.font}),(0,a.warn)('getPathGenerator - ignoring character: "'.concat(c,'".')),this.compiledGlyphs[t]=function(e,t){}}if(this.isEvalSupported&&m.value){for(var i,o="",s=0,u=r.length;s<u;s++)i=void 0!==(n=r[s]).args?n.args.join(","):"",o+="c."+n.cmd+"("+i+");\n";return this.compiledGlyphs[t]=new Function("c","size",o)}return this.compiledGlyphs[t]=function(e,t){for(var i=0,a=r.length;i<a;i++)"scale"===(n=r[i]).cmd&&(n.args=[t,-t]),e[n.cmd].apply(e,n.args)}}}]),e}();t.FontFaceObject=g},function(e,t,r){"use strict";var n=Object.create(null),i=r(4),a="undefined"!==typeof navigator&&navigator.userAgent||"",o=/Trident/.test(a),s=/CriOS/.test(a);(o||s)&&(n.disableCreateObjectURL=!0),i()&&(n.disableFontFace=!0,n.nativeImageDecoderSupport="none"),t.apiCompatibilityParams=Object.freeze(n)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.CanvasGraphics=void 0;var n=r(1),i=r(155),a=4096,o=16,s={get value(){return(0,n.shadow)(s,"value",(0,n.isLittleEndian)())}};function u(e){e.mozCurrentTransform||(e._originalSave=e.save,e._originalRestore=e.restore,e._originalRotate=e.rotate,e._originalScale=e.scale,e._originalTranslate=e.translate,e._originalTransform=e.transform,e._originalSetTransform=e.setTransform,e._transformMatrix=e._transformMatrix||[1,0,0,1,0,0],e._transformStack=[],Object.defineProperty(e,"mozCurrentTransform",{get:function(){return this._transformMatrix}}),Object.defineProperty(e,"mozCurrentTransformInverse",{get:function(){var e=this._transformMatrix,t=e[0],r=e[1],n=e[2],i=e[3],a=e[4],o=e[5],s=t*i-r*n,u=r*n-t*i;return[i/s,r/u,n/u,t/s,(i*a-n*o)/u,(r*a-t*o)/s]}}),e.save=function(){var e=this._transformMatrix;this._transformStack.push(e),this._transformMatrix=e.slice(0,6),this._originalSave()},e.restore=function(){var e=this._transformStack.pop();e&&(this._transformMatrix=e,this._originalRestore())},e.translate=function(e,t){var r=this._transformMatrix;r[4]=r[0]*e+r[2]*t+r[4],r[5]=r[1]*e+r[3]*t+r[5],this._originalTranslate(e,t)},e.scale=function(e,t){var r=this._transformMatrix;r[0]=r[0]*e,r[1]=r[1]*e,r[2]=r[2]*t,r[3]=r[3]*t,this._originalScale(e,t)},e.transform=function(t,r,n,i,a,o){var s=this._transformMatrix;this._transformMatrix=[s[0]*t+s[2]*r,s[1]*t+s[3]*r,s[0]*n+s[2]*i,s[1]*n+s[3]*i,s[0]*a+s[2]*o+s[4],s[1]*a+s[3]*o+s[5]],e._originalTransform(t,r,n,i,a,o)},e.setTransform=function(t,r,n,i,a,o){this._transformMatrix=[t,r,n,i,a,o],e._originalSetTransform(t,r,n,i,a,o)},e.rotate=function(e){var t=Math.cos(e),r=Math.sin(e),n=this._transformMatrix;this._transformMatrix=[n[0]*t+n[2]*r,n[1]*t+n[3]*r,n[0]*-r+n[2]*t,n[1]*-r+n[3]*t,n[4],n[5]],this._originalRotate(e)})}var c=function(){function e(e){this.canvasFactory=e,this.cache=Object.create(null)}return e.prototype={getCanvas:function(e,t,r,n){var i;return void 0!==this.cache[e]?(i=this.cache[e],this.canvasFactory.reset(i,t,r),i.context.setTransform(1,0,0,1,0,0)):(i=this.canvasFactory.create(t,r),this.cache[e]=i),n&&u(i.context),i},clear:function(){for(var e in this.cache){var t=this.cache[e];this.canvasFactory.destroy(t),delete this.cache[e]}}},e}(),l=function(){function e(){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=n.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=n.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=n.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.resumeSMaskCtx=null}return e.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(e,t){this.x=e,this.y=t}},e}(),f=function(){function e(e,t,r,n,i,a){this.ctx=e,this.current=new l,this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=t,this.objs=r,this.canvasFactory=n,this.webGLContext=i,this.imageLayer=a,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.cachedCanvases=new c(this.canvasFactory),e&&u(e),this._cachedGetSinglePixelWidth=null}function t(e,t){if("undefined"!==typeof ImageData&&t instanceof ImageData)e.putImageData(t,0,0);else{var r,i,a,u,c,l=t.height,f=t.width,d=l%o,h=(l-d)/o,p=0===d?h:h+1,v=e.createImageData(f,o),m=0,g=t.data,y=v.data;if(t.kind===n.ImageKind.GRAYSCALE_1BPP){var b=g.byteLength,_=new Uint32Array(y.buffer,0,y.byteLength>>2),S=_.length,A=f+7>>3,w=4294967295,k=s.value?4278190080:255;for(i=0;i<p;i++){for(u=i<h?o:d,r=0,a=0;a<u;a++){for(var x=b-m,P=0,R=x>A?f:8*x-7,C=-8&R,E=0,T=0;P<C;P+=8)T=g[m++],_[r++]=128&T?w:k,_[r++]=64&T?w:k,_[r++]=32&T?w:k,_[r++]=16&T?w:k,_[r++]=8&T?w:k,_[r++]=4&T?w:k,_[r++]=2&T?w:k,_[r++]=1&T?w:k;for(;P<R;P++)0===E&&(T=g[m++],E=128),_[r++]=T&E?w:k,E>>=1}for(;r<S;)_[r++]=0;e.putImageData(v,0,i*o)}}else if(t.kind===n.ImageKind.RGBA_32BPP){for(a=0,c=f*o*4,i=0;i<h;i++)y.set(g.subarray(m,m+c)),m+=c,e.putImageData(v,0,a),a+=o;i<p&&(c=f*d*4,y.set(g.subarray(m,m+c)),e.putImageData(v,0,a))}else{if(t.kind!==n.ImageKind.RGB_24BPP)throw new Error("bad image kind: ".concat(t.kind));for(c=f*(u=o),i=0;i<p;i++){for(i>=h&&(c=f*(u=d)),r=0,a=c;a--;)y[r++]=g[m++],y[r++]=g[m++],y[r++]=g[m++],y[r++]=255;e.putImageData(v,0,i*o)}}}}function r(e,t){for(var r=t.height,n=t.width,i=r%o,a=(r-i)/o,s=0===i?a:a+1,u=e.createImageData(n,o),c=0,l=t.data,f=u.data,d=0;d<s;d++){for(var h=d<a?o:i,p=3,v=0;v<h;v++)for(var m=0,g=0;g<n;g++){if(!m){var y=l[c++];m=128}f[p]=y&m?0:255,p+=4,m>>=1}e.putImageData(u,0,d*o)}}function f(e,t){for(var r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"],n=0,i=r.length;n<i;n++){var a=r[n];void 0!==e[a]&&(t[a]=e[a])}void 0!==e.setLineDash&&(t.setLineDash(e.getLineDash()),t.lineDashOffset=e.lineDashOffset)}function d(e){e.strokeStyle="#000000",e.fillStyle="#000000",e.fillRule="nonzero",e.globalAlpha=1,e.lineWidth=1,e.lineCap="butt",e.lineJoin="miter",e.miterLimit=10,e.globalCompositeOperation="source-over",e.font="10px sans-serif",void 0!==e.setLineDash&&(e.setLineDash([]),e.lineDashOffset=0)}function h(e,t,r,n){for(var i=e.length,a=3;a<i;a+=4){var o=e[a];if(0===o)e[a-3]=t,e[a-2]=r,e[a-1]=n;else if(o<255){var s=255-o;e[a-3]=e[a-3]*o+t*s>>8,e[a-2]=e[a-2]*o+r*s>>8,e[a-1]=e[a-1]*o+n*s>>8}}}function p(e,t,r){for(var n=e.length,i=1/255,a=3;a<n;a+=4){var o=r?r[e[a]]:e[a];t[a]=t[a]*o*i|0}}function v(e,t,r){for(var n=e.length,i=3;i<n;i+=4){var a=77*e[i-3]+152*e[i-2]+28*e[i-1];t[i]=r?t[i]*r[a>>8]>>8:t[i]*a>>16}}function m(e,t,r,n){var i=t.canvas,a=t.context;e.setTransform(t.scaleX,0,0,t.scaleY,t.offsetX,t.offsetY);var o=t.backdrop||null;if(!t.transferMap&&n.isEnabled){var s=n.composeSMask({layer:r.canvas,mask:i,properties:{subtype:t.subtype,backdrop:o}});return e.setTransform(1,0,0,1,0,0),void e.drawImage(s,t.offsetX,t.offsetY)}!function(e,t,r,n,i,a,o){var s,u=!!a,c=u?a[0]:0,l=u?a[1]:0,f=u?a[2]:0;s="Luminosity"===i?v:p;for(var d=Math.min(n,Math.ceil(1048576/r)),m=0;m<n;m+=d){var g=Math.min(d,n-m),y=e.getImageData(0,m,r,g),b=t.getImageData(0,m,r,g);u&&h(y.data,c,l,f),s(y.data,b.data,o),e.putImageData(b,0,m)}}(a,r,i.width,i.height,t.subtype,o,t.transferMap),e.drawImage(i,0,0)}var g=["butt","round","square"],y=["miter","round","bevel"],b={},_={};for(var S in e.prototype={beginDrawing:function(e){var t=e.transform,r=e.viewport,n=e.transparency,i=void 0!==n&&n,a=e.background,o=void 0===a?null:a,s=this.ctx.canvas.width,u=this.ctx.canvas.height;if(this.ctx.save(),this.ctx.fillStyle=o||"rgb(255, 255, 255)",this.ctx.fillRect(0,0,s,u),this.ctx.restore(),i){var c=this.cachedCanvases.getCanvas("transparent",s,u,!0);this.compositeCtx=this.ctx,this.transparentCanvas=c.canvas,this.ctx=c.context,this.ctx.save(),this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save(),d(this.ctx),t&&this.ctx.transform.apply(this.ctx,t),this.ctx.transform.apply(this.ctx,r.transform),this.baseTransform=this.ctx.mozCurrentTransform.slice(),this.imageLayer&&this.imageLayer.beginLayout()},executeOperatorList:function(e,t,r,i){var a=e.argsArray,o=e.fnArray,s=t||0,u=a.length;if(u===s)return s;for(var c,l=u-s>10&&"function"===typeof r,f=l?Date.now()+15:0,d=0,h=this.commonObjs,p=this.objs;;){if(void 0!==i&&s===i.nextBreakPoint)return i.breakIt(s,r),s;if((c=o[s])!==n.OPS.dependency)this[c].apply(this,a[s]);else for(var v=a[s],m=0,g=v.length;m<g;m++){var y=v[m],b="g"===y[0]&&"_"===y[1]?h:p;if(!b.has(y))return b.get(y,r),s}if(++s===u)return s;if(l&&++d>10){if(Date.now()>f)return r(),s;d=0}}},endDrawing:function(){null!==this.current.activeSMask&&this.endSMaskGroup(),this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null),this.cachedCanvases.clear(),this.webGLContext.clear(),this.imageLayer&&this.imageLayer.endLayout()},setLineWidth:function(e){this.current.lineWidth=e,this.ctx.lineWidth=e},setLineCap:function(e){this.ctx.lineCap=g[e]},setLineJoin:function(e){this.ctx.lineJoin=y[e]},setMiterLimit:function(e){this.ctx.miterLimit=e},setDash:function(e,t){var r=this.ctx;void 0!==r.setLineDash&&(r.setLineDash(e),r.lineDashOffset=t)},setRenderingIntent:function(e){},setFlatness:function(e){},setGState:function(e){for(var t=0,r=e.length;t<r;t++){var n=e[t],i=n[0],a=n[1];switch(i){case"LW":this.setLineWidth(a);break;case"LC":this.setLineCap(a);break;case"LJ":this.setLineJoin(a);break;case"ML":this.setMiterLimit(a);break;case"D":this.setDash(a[0],a[1]);break;case"RI":this.setRenderingIntent(a);break;case"FL":this.setFlatness(a);break;case"Font":this.setFont(a[0],a[1]);break;case"CA":this.current.strokeAlpha=n[1];break;case"ca":this.current.fillAlpha=n[1],this.ctx.globalAlpha=n[1];break;case"BM":this.ctx.globalCompositeOperation=a;break;case"SMask":this.current.activeSMask&&(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask?this.suspendSMaskGroup():this.endSMaskGroup()),this.current.activeSMask=a?this.tempSMask:null,this.current.activeSMask&&this.beginSMaskGroup(),this.tempSMask=null}}},beginSMaskGroup:function(){var e=this.current.activeSMask,t=e.canvas.width,r=e.canvas.height,n="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(n,t,r,!0),a=this.ctx,o=a.mozCurrentTransform;this.ctx.save();var s=i.context;s.scale(1/e.scaleX,1/e.scaleY),s.translate(-e.offsetX,-e.offsetY),s.transform.apply(s,o),e.startTransformInverse=s.mozCurrentTransformInverse,f(a,s),this.ctx=s,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(a),this.groupLevel++},suspendSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),m(this.ctx,this.current.activeSMask,e,this.webGLContext),this.ctx.restore(),this.ctx.save(),f(e,this.ctx),this.current.resumeSMaskCtx=e;var t=n.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t),e.save(),e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,e.canvas.width,e.canvas.height),e.restore()},resumeSMaskGroup:function(){var e=this.current.resumeSMaskCtx,t=this.ctx;this.ctx=e,this.groupStack.push(t),this.groupLevel++},endSMaskGroup:function(){var e=this.ctx;this.groupLevel--,this.ctx=this.groupStack.pop(),m(this.ctx,this.current.activeSMask,e,this.webGLContext),this.ctx.restore(),f(e,this.ctx);var t=n.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t)},save:function(){this.ctx.save();var e=this.current;this.stateStack.push(e),this.current=e.clone(),this.current.resumeSMaskCtx=null},restore:function(){this.current.resumeSMaskCtx&&this.resumeSMaskGroup(),null===this.current.activeSMask||0!==this.stateStack.length&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask||this.endSMaskGroup(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.ctx.restore(),this.pendingClip=null,this._cachedGetSinglePixelWidth=null)},transform:function(e,t,r,n,i,a){this.ctx.transform(e,t,r,n,i,a),this._cachedGetSinglePixelWidth=null},constructPath:function(e,t){for(var r=this.ctx,i=this.current,a=i.x,o=i.y,s=0,u=0,c=e.length;s<c;s++)switch(0|e[s]){case n.OPS.rectangle:a=t[u++],o=t[u++];var l=t[u++],f=t[u++];0===l&&(l=this.getSinglePixelWidth()),0===f&&(f=this.getSinglePixelWidth());var d=a+l,h=o+f;this.ctx.moveTo(a,o),this.ctx.lineTo(d,o),this.ctx.lineTo(d,h),this.ctx.lineTo(a,h),this.ctx.lineTo(a,o),this.ctx.closePath();break;case n.OPS.moveTo:a=t[u++],o=t[u++],r.moveTo(a,o);break;case n.OPS.lineTo:a=t[u++],o=t[u++],r.lineTo(a,o);break;case n.OPS.curveTo:a=t[u+4],o=t[u+5],r.bezierCurveTo(t[u],t[u+1],t[u+2],t[u+3],a,o),u+=6;break;case n.OPS.curveTo2:r.bezierCurveTo(a,o,t[u],t[u+1],t[u+2],t[u+3]),a=t[u+2],o=t[u+3],u+=4;break;case n.OPS.curveTo3:a=t[u+2],o=t[u+3],r.bezierCurveTo(t[u],t[u+1],a,o,a,o),u+=4;break;case n.OPS.closePath:r.closePath()}i.setCurrentPoint(a,o)},closePath:function(){this.ctx.closePath()},stroke:function(e){e="undefined"===typeof e||e;var t=this.ctx,r=this.current.strokeColor;t.lineWidth=Math.max(.65*this.getSinglePixelWidth(),this.current.lineWidth),t.globalAlpha=this.current.strokeAlpha,r&&r.hasOwnProperty("type")&&"Pattern"===r.type?(t.save(),t.strokeStyle=r.getPattern(t,this),t.stroke(),t.restore()):t.stroke(),e&&this.consumePath(),t.globalAlpha=this.current.fillAlpha},closeStroke:function(){this.closePath(),this.stroke()},fill:function(e){e="undefined"===typeof e||e;var t=this.ctx,r=this.current.fillColor,n=!1;this.current.patternFill&&(t.save(),this.baseTransform&&t.setTransform.apply(t,this.baseTransform),t.fillStyle=r.getPattern(t,this),n=!0),this.pendingEOFill?(t.fill("evenodd"),this.pendingEOFill=!1):t.fill(),n&&t.restore(),e&&this.consumePath()},eoFill:function(){this.pendingEOFill=!0,this.fill()},fillStroke:function(){this.fill(!1),this.stroke(!1),this.consumePath()},eoFillStroke:function(){this.pendingEOFill=!0,this.fillStroke()},closeFillStroke:function(){this.closePath(),this.fillStroke()},closeEOFillStroke:function(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()},endPath:function(){this.consumePath()},clip:function(){this.pendingClip=b},eoClip:function(){this.pendingClip=_},beginText:function(){this.current.textMatrix=n.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},endText:function(){var e=this.pendingTextPaths,t=this.ctx;if(void 0!==e){t.save(),t.beginPath();for(var r=0;r<e.length;r++){var n=e[r];t.setTransform.apply(t,n.transform),t.translate(n.x,n.y),n.addToPath(t,n.fontSize)}t.restore(),t.clip(),t.beginPath(),delete this.pendingTextPaths}else t.beginPath()},setCharSpacing:function(e){this.current.charSpacing=e},setWordSpacing:function(e){this.current.wordSpacing=e},setHScale:function(e){this.current.textHScale=e/100},setLeading:function(e){this.current.leading=-e},setFont:function(e,t){var r=this.commonObjs.get(e),i=this.current;if(!r)throw new Error("Can't find font for ".concat(e));if(i.fontMatrix=r.fontMatrix?r.fontMatrix:n.FONT_IDENTITY_MATRIX,0!==i.fontMatrix[0]&&0!==i.fontMatrix[3]||(0,n.warn)("Invalid font matrix for font "+e),t<0?(t=-t,i.fontDirection=-1):i.fontDirection=1,this.current.font=r,this.current.fontSize=t,!r.isType3Font){var a=r.loadedName||"sans-serif",o=r.black?"900":r.bold?"bold":"normal",s=r.italic?"italic":"normal",u='"'.concat(a,'", ').concat(r.fallbackName),c=t<16?16:t>100?100:t;this.current.fontSizeScale=t/c,this.ctx.font="".concat(s," ").concat(o," ").concat(c,"px ").concat(u)}},setTextRenderingMode:function(e){this.current.textRenderingMode=e},setTextRise:function(e){this.current.textRise=e},moveText:function(e,t){this.current.x=this.current.lineX+=e,this.current.y=this.current.lineY+=t},setLeadingMoveText:function(e,t){this.setLeading(-t),this.moveText(e,t)},setTextMatrix:function(e,t,r,n,i,a){this.current.textMatrix=[e,t,r,n,i,a],this.current.textMatrixScale=Math.sqrt(e*e+t*t),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0},nextLine:function(){this.moveText(0,this.current.leading)},paintChar:function(e,t,r,i){var a,o=this.ctx,s=this.current,u=s.font,c=s.textRenderingMode,l=s.fontSize/s.fontSizeScale,f=c&n.TextRenderingMode.FILL_STROKE_MASK,d=!!(c&n.TextRenderingMode.ADD_TO_PATH_FLAG),h=s.patternFill&&u.data;(u.disableFontFace||d||h)&&(a=u.getPathGenerator(this.commonObjs,e)),u.disableFontFace||h?(o.save(),o.translate(t,r),o.beginPath(),a(o,l),i&&o.setTransform.apply(o,i),f!==n.TextRenderingMode.FILL&&f!==n.TextRenderingMode.FILL_STROKE||o.fill(),f!==n.TextRenderingMode.STROKE&&f!==n.TextRenderingMode.FILL_STROKE||o.stroke(),o.restore()):(f!==n.TextRenderingMode.FILL&&f!==n.TextRenderingMode.FILL_STROKE||o.fillText(e,t,r),f!==n.TextRenderingMode.STROKE&&f!==n.TextRenderingMode.FILL_STROKE||o.strokeText(e,t,r)),d&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:o.mozCurrentTransform,x:t,y:r,fontSize:l,addToPath:a})},get isFontSubpixelAAEnabled(){var e=this.canvasFactory.create(10,10).context;e.scale(1.5,1),e.fillText("I",0,10);for(var t=e.getImageData(0,0,10,10).data,r=!1,i=3;i<t.length;i+=4)if(t[i]>0&&t[i]<255){r=!0;break}return(0,n.shadow)(this,"isFontSubpixelAAEnabled",r)},showText:function(e){var t=this.current,r=t.font;if(r.isType3Font)return this.showType3Text(e);var i=t.fontSize;if(0!==i){var a,o=this.ctx,s=t.fontSizeScale,u=t.charSpacing,c=t.wordSpacing,l=t.fontDirection,f=t.textHScale*l,d=e.length,h=r.vertical,p=h?1:-1,v=r.defaultVMetrics,m=i*t.fontMatrix[0],g=t.textRenderingMode===n.TextRenderingMode.FILL&&!r.disableFontFace&&!t.patternFill;if(o.save(),t.patternFill){o.save();var y=t.fillColor.getPattern(o,this);a=o.mozCurrentTransform,o.restore(),o.fillStyle=y}o.transform.apply(o,t.textMatrix),o.translate(t.x,t.y+t.textRise),l>0?o.scale(f,-1):o.scale(f,1);var b=t.lineWidth,_=t.textMatrixScale;if(0===_||0===b){var S=t.textRenderingMode&n.TextRenderingMode.FILL_STROKE_MASK;S!==n.TextRenderingMode.STROKE&&S!==n.TextRenderingMode.FILL_STROKE||(this._cachedGetSinglePixelWidth=null,b=.65*this.getSinglePixelWidth())}else b/=_;1!==s&&(o.scale(s,s),b/=s),o.lineWidth=b;var A,w=0;for(A=0;A<d;++A){var k=e[A];if((0,n.isNum)(k))w+=p*k*i/1e3;else{var x,P,R,C,E,T,O,L=!1,I=(k.isSpace?c:0)+u,F=k.fontChar,D=k.accent,j=k.width;if(h?(E=k.vmetric||v,T=-(T=k.vmetric?E[1]:.5*j)*m,O=E[2]*m,j=E?-E[0]:j,x=T/s,P=(w+O)/s):(x=w/s,P=0),r.remeasure&&j>0){var M=1e3*o.measureText(F).width/i*s;if(j<M&&this.isFontSubpixelAAEnabled){var N=j/M;L=!0,o.save(),o.scale(N,1),x/=N}else j!==M&&(x+=(j-M)/2e3*i/s)}(k.isInFont||r.missingFile)&&(g&&!D?o.fillText(F,x,P):(this.paintChar(F,x,P,a),D&&(R=x+D.offset.x/s,C=P-D.offset.y/s,this.paintChar(D.fontChar,R,C,a)))),w+=j*m+I*l,L&&o.restore()}}h?t.y-=w*f:t.x+=w*f,o.restore()}},showType3Text:function(e){var t,r,i,a,o=this.ctx,s=this.current,u=s.font,c=s.fontSize,l=s.fontDirection,f=u.vertical?1:-1,d=s.charSpacing,h=s.wordSpacing,p=s.textHScale*l,v=s.fontMatrix||n.FONT_IDENTITY_MATRIX,m=e.length;if(s.textRenderingMode!==n.TextRenderingMode.INVISIBLE&&0!==c){for(this._cachedGetSinglePixelWidth=null,o.save(),o.transform.apply(o,s.textMatrix),o.translate(s.x,s.y),o.scale(p,l),t=0;t<m;++t)if(r=e[t],(0,n.isNum)(r))a=f*r*c/1e3,this.ctx.translate(a,0),s.x+=a*p;else{var g=(r.isSpace?h:0)+d,y=u.charProcOperatorList[r.operatorListId];y?(this.processingType3=r,this.save(),o.scale(c,c),o.transform.apply(o,v),this.executeOperatorList(y),this.restore(),i=n.Util.applyTransform([r.width,0],v)[0]*c+g,o.translate(i,0),s.x+=i*p):(0,n.warn)('Type3 character "'.concat(r.operatorListId,'" is not available.'))}o.restore(),this.processingType3=null}},setCharWidth:function(e,t){},setCharWidthAndBounds:function(e,t,r,n,i,a){this.ctx.rect(r,n,i-r,a-n),this.clip(),this.endPath()},getColorN_Pattern:function(t){var r,n=this;if("TilingPattern"===t[0]){var a=t[1],o=this.baseTransform||this.ctx.mozCurrentTransform.slice(),s={createCanvasGraphics:function(t){return new e(t,n.commonObjs,n.objs,n.canvasFactory,n.webGLContext)}};r=new i.TilingPattern(t,a,this.ctx,s,o)}else r=(0,i.getShadingPatternFromIR)(t);return r},setStrokeColorN:function(){this.current.strokeColor=this.getColorN_Pattern(arguments)},setFillColorN:function(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0},setStrokeRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.ctx.strokeStyle=i,this.current.strokeColor=i},setFillRGBColor:function(e,t,r){var i=n.Util.makeCssRgb(e,t,r);this.ctx.fillStyle=i,this.current.fillColor=i,this.current.patternFill=!1},shadingFill:function(e){var t=this.ctx;this.save();var r=(0,i.getShadingPatternFromIR)(e);t.fillStyle=r.getPattern(t,this,!0);var a=t.mozCurrentTransformInverse;if(a){var o=t.canvas,s=o.width,u=o.height,c=n.Util.applyTransform([0,0],a),l=n.Util.applyTransform([0,u],a),f=n.Util.applyTransform([s,0],a),d=n.Util.applyTransform([s,u],a),h=Math.min(c[0],l[0],f[0],d[0]),p=Math.min(c[1],l[1],f[1],d[1]),v=Math.max(c[0],l[0],f[0],d[0]),m=Math.max(c[1],l[1],f[1],d[1]);this.ctx.fillRect(h,p,v-h,m-p)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.restore()},beginInlineImage:function(){(0,n.unreachable)("Should not call beginInlineImage")},beginImageData:function(){(0,n.unreachable)("Should not call beginImageData")},paintFormXObjectBegin:function(e,t){if(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(e)&&6===e.length&&this.transform.apply(this,e),this.baseTransform=this.ctx.mozCurrentTransform,t){var r=t[2]-t[0],n=t[3]-t[1];this.ctx.rect(t[0],t[1],r,n),this.clip(),this.endPath()}},paintFormXObjectEnd:function(){this.restore(),this.baseTransform=this.baseTransformStack.pop()},beginGroup:function(e){this.save();var t=this.ctx;e.isolated||(0,n.info)("TODO: Support non-isolated groups."),e.knockout&&(0,n.warn)("Knockout groups not supported.");var r=t.mozCurrentTransform;if(e.matrix&&t.transform.apply(t,e.matrix),!e.bbox)throw new Error("Bounding box is required.");var i=n.Util.getAxialAlignedBoundingBox(e.bbox,t.mozCurrentTransform),o=[0,0,t.canvas.width,t.canvas.height];i=n.Util.intersect(i,o)||[0,0,0,0];var s=Math.floor(i[0]),u=Math.floor(i[1]),c=Math.max(Math.ceil(i[2])-s,1),l=Math.max(Math.ceil(i[3])-u,1),d=1,h=1;c>a&&(d=c/a,c=a),l>a&&(h=l/a,l=a);var p="groupAt"+this.groupLevel;e.smask&&(p+="_smask_"+this.smaskCounter++%2);var v=this.cachedCanvases.getCanvas(p,c,l,!0),m=v.context;m.scale(1/d,1/h),m.translate(-s,-u),m.transform.apply(m,r),e.smask?this.smaskStack.push({canvas:v.canvas,context:m,offsetX:s,offsetY:u,scaleX:d,scaleY:h,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null}):(t.setTransform(1,0,0,1,0,0),t.translate(s,u),t.scale(d,h)),f(t,m),this.ctx=m,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(t),this.groupLevel++,this.current.activeSMask=null},endGroup:function(e){this.groupLevel--;var t=this.ctx;this.ctx=this.groupStack.pop(),void 0!==this.ctx.imageSmoothingEnabled?this.ctx.imageSmoothingEnabled=!1:this.ctx.mozImageSmoothingEnabled=!1,e.smask?this.tempSMask=this.smaskStack.pop():this.ctx.drawImage(t.canvas,0,0),this.restore()},beginAnnotations:function(){this.save(),this.baseTransform&&this.ctx.setTransform.apply(this.ctx,this.baseTransform)},endAnnotations:function(){this.restore()},beginAnnotation:function(e,t,r){if(this.save(),d(this.ctx),this.current=new l,Array.isArray(e)&&4===e.length){var n=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],n,i),this.clip(),this.endPath()}this.transform.apply(this,t),this.transform.apply(this,r)},endAnnotation:function(){this.restore()},paintJpegXObject:function(e,t,r){var i=this.objs.get(e);if(i){this.save();var a=this.ctx;if(a.scale(1/t,-1/r),a.drawImage(i,0,0,i.width,i.height,0,-r,t,r),this.imageLayer){var o=a.mozCurrentTransformInverse,s=this.getCanvasPosition(0,0);this.imageLayer.appendImage({objId:e,left:s[0],top:s[1],width:t/o[0],height:r/o[3]})}this.restore()}else(0,n.warn)("Dependent image isn't ready yet")},paintImageMaskXObject:function(e){var t=this.ctx,n=e.width,i=e.height,a=this.current.fillColor,o=this.current.patternFill,s=this.processingType3;if(s&&void 0===s.compiled&&(s.compiled=n<=1e3&&i<=1e3?function(e){var t,r,n,i,a=e.width,o=e.height,s=a+1,u=new Uint8Array(s*(o+1)),c=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),l=a+7&-8,f=e.data,d=new Uint8Array(l*o),h=0;for(t=0,i=f.length;t<i;t++)for(var p=128,v=f[t];p>0;)d[h++]=v&p?0:255,p>>=1;var m=0;for(0!==d[h=0]&&(u[0]=1,++m),r=1;r<a;r++)d[h]!==d[h+1]&&(u[r]=d[h]?2:1,++m),h++;for(0!==d[h]&&(u[r]=2,++m),t=1;t<o;t++){n=t*s,d[(h=t*l)-l]!==d[h]&&(u[n]=d[h]?1:8,++m);var g=(d[h]?4:0)+(d[h-l]?8:0);for(r=1;r<a;r++)c[g=(g>>2)+(d[h+1]?4:0)+(d[h-l+1]?8:0)]&&(u[n+r]=c[g],++m),h++;if(d[h-l]!==d[h]&&(u[n+r]=d[h]?2:4,++m),m>1e3)return null}for(n=t*s,0!==d[h=l*(o-1)]&&(u[n]=8,++m),r=1;r<a;r++)d[h]!==d[h+1]&&(u[n+r]=d[h]?4:8,++m),h++;if(0!==d[h]&&(u[n+r]=4,++m),m>1e3)return null;var y=new Int32Array([0,s,-1,0,-s,0,0,0,1]),b=[];for(t=0;m&&t<=o;t++){for(var _=t*s,S=_+a;_<S&&!u[_];)_++;if(_!==S){var A,w=[_%s,t],k=u[_],x=_;do{var P=y[k];do{_+=P}while(!u[_]);5!==(A=u[_])&&10!==A?(k=A,u[_]=0):(k=A&51*k>>4,u[_]&=k>>2|k<<2),w.push(_%s),w.push(_/s|0),--m}while(x!==_);b.push(w),--t}}return function(e){e.save(),e.scale(1/a,-1/o),e.translate(0,-o),e.beginPath();for(var t=0,r=b.length;t<r;t++){var n=b[t];e.moveTo(n[0],n[1]);for(var i=2,s=n.length;i<s;i+=2)e.lineTo(n[i],n[i+1])}e.fill(),e.beginPath(),e.restore()}}({data:e.data,width:n,height:i}):null),s&&s.compiled)s.compiled(t);else{var u=this.cachedCanvases.getCanvas("maskCanvas",n,i),c=u.context;c.save(),r(c,e),c.globalCompositeOperation="source-in",c.fillStyle=o?a.getPattern(c,this):a,c.fillRect(0,0,n,i),c.restore(),this.paintInlineImageXObject(u.canvas)}},paintImageMaskXObjectRepeat:function(e,t,n,i){var a=e.width,o=e.height,s=this.current.fillColor,u=this.current.patternFill,c=this.cachedCanvases.getCanvas("maskCanvas",a,o),l=c.context;l.save(),r(l,e),l.globalCompositeOperation="source-in",l.fillStyle=u?s.getPattern(l,this):s,l.fillRect(0,0,a,o),l.restore();for(var f=this.ctx,d=0,h=i.length;d<h;d+=2)f.save(),f.transform(t,0,0,n,i[d],i[d+1]),f.scale(1,-1),f.drawImage(c.canvas,0,0,a,o,0,-1,1,1),f.restore()},paintImageMaskXObjectGroup:function(e){for(var t=this.ctx,n=this.current.fillColor,i=this.current.patternFill,a=0,o=e.length;a<o;a++){var s=e[a],u=s.width,c=s.height,l=this.cachedCanvases.getCanvas("maskCanvas",u,c),f=l.context;f.save(),r(f,s),f.globalCompositeOperation="source-in",f.fillStyle=i?n.getPattern(f,this):n,f.fillRect(0,0,u,c),f.restore(),t.save(),t.transform.apply(t,s.transform),t.scale(1,-1),t.drawImage(l.canvas,0,0,u,c,0,-1,1,1),t.restore()}},paintImageXObject:function(e){var t=this.objs.get(e);t?this.paintInlineImageXObject(t):(0,n.warn)("Dependent image isn't ready yet")},paintImageXObjectRepeat:function(e,t,r,i){var a=this.objs.get(e);if(a){for(var o=a.width,s=a.height,u=[],c=0,l=i.length;c<l;c+=2)u.push({transform:[t,0,0,r,i[c],i[c+1]],x:0,y:0,w:o,h:s});this.paintInlineImageXObjectGroup(a,u)}else(0,n.warn)("Dependent image isn't ready yet")},paintInlineImageXObject:function(e){var r=e.width,n=e.height,i=this.ctx;this.save(),i.scale(1/r,-1/n);var a,o,s=i.mozCurrentTransformInverse,u=s[0],c=s[1],l=Math.max(Math.sqrt(u*u+c*c),1),f=s[2],d=s[3],h=Math.max(Math.sqrt(f*f+d*d),1);if("function"===typeof HTMLElement&&e instanceof HTMLElement||!e.data)a=e;else{var p=(o=this.cachedCanvases.getCanvas("inlineImage",r,n)).context;t(p,e),a=o.canvas}for(var v=r,m=n,g="prescale1";l>2&&v>1||h>2&&m>1;){var y=v,b=m;l>2&&v>1&&(l/=v/(y=Math.ceil(v/2))),h>2&&m>1&&(h/=m/(b=Math.ceil(m/2))),(p=(o=this.cachedCanvases.getCanvas(g,y,b)).context).clearRect(0,0,y,b),p.drawImage(a,0,0,v,m,0,0,y,b),a=o.canvas,v=y,m=b,g="prescale1"===g?"prescale2":"prescale1"}if(i.drawImage(a,0,0,v,m,0,-n,r,n),this.imageLayer){var _=this.getCanvasPosition(0,-n);this.imageLayer.appendImage({imgData:e,left:_[0],top:_[1],width:r/s[0],height:n/s[3]})}this.restore()},paintInlineImageXObjectGroup:function(e,r){var n=this.ctx,i=e.width,a=e.height,o=this.cachedCanvases.getCanvas("inlineImage",i,a);t(o.context,e);for(var s=0,u=r.length;s<u;s++){var c=r[s];if(n.save(),n.transform.apply(n,c.transform),n.scale(1,-1),n.drawImage(o.canvas,c.x,c.y,c.w,c.h,0,-1,1,1),this.imageLayer){var l=this.getCanvasPosition(c.x,c.y);this.imageLayer.appendImage({imgData:e,left:l[0],top:l[1],width:i,height:a})}n.restore()}},paintSolidColorImageMask:function(){this.ctx.fillRect(0,0,1,1)},paintXObject:function(){(0,n.warn)("Unsupported 'paintXObject' command.")},markPoint:function(e){},markPointProps:function(e,t){},beginMarkedContent:function(e){},beginMarkedContentProps:function(e,t){},endMarkedContent:function(){},beginCompat:function(){},endCompat:function(){},consumePath:function(){var e=this.ctx;this.pendingClip&&(this.pendingClip===_?e.clip("evenodd"):e.clip(),this.pendingClip=null),e.beginPath()},getSinglePixelWidth:function(e){if(null===this._cachedGetSinglePixelWidth){var t=this.ctx.mozCurrentTransformInverse;this._cachedGetSinglePixelWidth=Math.sqrt(Math.max(t[0]*t[0]+t[1]*t[1],t[2]*t[2]+t[3]*t[3]))}return this._cachedGetSinglePixelWidth},getCanvasPosition:function(e,t){var r=this.ctx.mozCurrentTransform;return[r[0]*e+r[2]*t+r[4],r[1]*e+r[3]*t+r[5]]}},n.OPS)e.prototype[n.OPS[S]]=e.prototype[S];return e}();t.CanvasGraphics=f},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getShadingPatternFromIR=function(e){var t=i[e[0]];if(!t)throw new Error("Unknown IR type: ".concat(e[0]));return t.fromIR(e)},t.TilingPattern=void 0;var n=r(1),i={RadialAxial:{fromIR:function(e){var t=e[1],r=e[2],n=e[3],i=e[4],a=e[5],o=e[6];return{type:"Pattern",getPattern:function(e){var s;"axial"===t?s=e.createLinearGradient(n[0],n[1],i[0],i[1]):"radial"===t&&(s=e.createRadialGradient(n[0],n[1],a,i[0],i[1],o));for(var u=0,c=r.length;u<c;++u){var l=r[u];s.addColorStop(l[0],l[1])}return s}}}}},a=function(){function e(e,t,r,n,i,a,o,s){var u,c=t.coords,l=t.colors,f=e.data,d=4*e.width;c[r+1]>c[n+1]&&(u=r,r=n,n=u,u=a,a=o,o=u),c[n+1]>c[i+1]&&(u=n,n=i,i=u,u=o,o=s,s=u),c[r+1]>c[n+1]&&(u=r,r=n,n=u,u=a,a=o,o=u);var h=(c[r]+t.offsetX)*t.scaleX,p=(c[r+1]+t.offsetY)*t.scaleY,v=(c[n]+t.offsetX)*t.scaleX,m=(c[n+1]+t.offsetY)*t.scaleY,g=(c[i]+t.offsetX)*t.scaleX,y=(c[i+1]+t.offsetY)*t.scaleY;if(!(p>=y))for(var b,_,S,A,w,k,x,P,R,C=l[a],E=l[a+1],T=l[a+2],O=l[o],L=l[o+1],I=l[o+2],F=l[s],D=l[s+1],j=l[s+2],M=Math.round(p),N=Math.round(y),q=M;q<=N;q++){q<m?(b=h-(h-v)*(R=q<p?0:p===m?1:(p-q)/(p-m)),_=C-(C-O)*R,S=E-(E-L)*R,A=T-(T-I)*R):(b=v-(v-g)*(R=q>y?1:m===y?0:(m-q)/(m-y)),_=O-(O-F)*R,S=L-(L-D)*R,A=I-(I-j)*R),w=h-(h-g)*(R=q<p?0:q>y?1:(p-q)/(p-y)),k=C-(C-F)*R,x=E-(E-D)*R,P=T-(T-j)*R;for(var W=Math.round(Math.min(b,w)),U=Math.round(Math.max(b,w)),B=d*q+4*W,G=W;G<=U;G++)R=(R=(b-G)/(b-w))<0?0:R>1?1:R,f[B++]=_-(_-k)*R|0,f[B++]=S-(S-x)*R|0,f[B++]=A-(A-P)*R|0,f[B++]=255}}function t(t,r,n){var i,a,o=r.coords,s=r.colors;switch(r.type){case"lattice":var u=r.verticesPerRow,c=Math.floor(o.length/u)-1,l=u-1;for(i=0;i<c;i++)for(var f=i*u,d=0;d<l;d++,f++)e(t,n,o[f],o[f+1],o[f+u],s[f],s[f+1],s[f+u]),e(t,n,o[f+u+1],o[f+1],o[f+u],s[f+u+1],s[f+1],s[f+u]);break;case"triangles":for(i=0,a=o.length;i<a;i+=3)e(t,n,o[i],o[i+1],o[i+2],s[i],s[i+1],s[i+2]);break;default:throw new Error("illegal figure")}}return function(e,r,n,i,a,o,s,u){var c,l,f,d,h=Math.floor(e[0]),p=Math.floor(e[1]),v=Math.ceil(e[2])-h,m=Math.ceil(e[3])-p,g=Math.min(Math.ceil(Math.abs(v*r[0]*1.1)),3e3),y=Math.min(Math.ceil(Math.abs(m*r[1]*1.1)),3e3),b=v/g,_=m/y,S={coords:n,colors:i,offsetX:-h,offsetY:-p,scaleX:1/b,scaleY:1/_},A=g+4,w=y+4;if(u.isEnabled)c=u.drawFigures({width:g,height:y,backgroundColor:o,figures:a,context:S}),(l=s.getCanvas("mesh",A,w,!1)).context.drawImage(c,2,2),c=l.canvas;else{var k=(l=s.getCanvas("mesh",A,w,!1)).context,x=k.createImageData(g,y);if(o){var P=x.data;for(f=0,d=P.length;f<d;f+=4)P[f]=o[0],P[f+1]=o[1],P[f+2]=o[2],P[f+3]=255}for(f=0;f<a.length;f++)t(x,a[f],S);k.putImageData(x,2,2),c=l.canvas}return{canvas:c,offsetX:h-2*b,offsetY:p-2*_,scaleX:b,scaleY:_}}}();i.Mesh={fromIR:function(e){var t=e[2],r=e[3],i=e[4],o=e[5],s=e[6],u=e[8];return{type:"Pattern",getPattern:function(e,c,l){var f;if(l)f=n.Util.singularValueDecompose2dScale(e.mozCurrentTransform);else if(f=n.Util.singularValueDecompose2dScale(c.baseTransform),s){var d=n.Util.singularValueDecompose2dScale(s);f=[f[0]*d[0],f[1]*d[1]]}var h=a(o,f,t,r,i,l?null:u,c.cachedCanvases,c.webGLContext);return l||(e.setTransform.apply(e,c.baseTransform),s&&e.transform.apply(e,s)),e.translate(h.offsetX,h.offsetY),e.scale(h.scaleX,h.scaleY),e.createPattern(h.canvas,"no-repeat")}}}},i.Dummy={fromIR:function(){return{type:"Pattern",getPattern:function(){return"hotpink"}}}};var o=function(){var e=1,t=2;function r(e,t,r,n,i){this.operatorList=e[2],this.matrix=e[3]||[1,0,0,1,0,0],this.bbox=e[4],this.xstep=e[5],this.ystep=e[6],this.paintType=e[7],this.tilingType=e[8],this.color=t,this.canvasGraphicsFactory=n,this.baseTransform=i,this.type="Pattern",this.ctx=r}return r.prototype={createPatternCanvas:function(e){var t=this.operatorList,r=this.bbox,i=this.xstep,a=this.ystep,o=this.paintType,s=this.tilingType,u=this.color,c=this.canvasGraphicsFactory;(0,n.info)("TilingType: "+s);var l=r[0],f=r[1],d=r[2],h=r[3],p=[l,f],v=[l+i,f+a],m=v[0]-p[0],g=v[1]-p[1],y=n.Util.singularValueDecompose2dScale(this.matrix),b=n.Util.singularValueDecompose2dScale(this.baseTransform),_=[y[0]*b[0],y[1]*b[1]];m=Math.min(Math.ceil(Math.abs(m*_[0])),3e3),g=Math.min(Math.ceil(Math.abs(g*_[1])),3e3);var S=e.cachedCanvases.getCanvas("pattern",m,g,!0),A=S.context,w=c.createCanvasGraphics(A);w.groupLevel=e.groupLevel,this.setFillAndStrokeStyleToContext(w,o,u),this.setScale(m,g,i,a),this.transformToScale(w);var k=[1,0,0,1,-p[0],-p[1]];return w.transform.apply(w,k),this.clipBbox(w,r,l,f,d,h),w.executeOperatorList(t),S.canvas},setScale:function(e,t,r,n){this.scale=[e/r,t/n]},transformToScale:function(e){var t=this.scale,r=[t[0],0,0,t[1],0,0];e.transform.apply(e,r)},scaleToContext:function(){var e=this.scale;this.ctx.scale(1/e[0],1/e[1])},clipBbox:function(e,t,r,n,i,a){if(Array.isArray(t)&&4===t.length){var o=i-r,s=a-n;e.ctx.rect(r,n,o,s),e.clip(),e.endPath()}},setFillAndStrokeStyleToContext:function(r,i,a){var o=r.ctx,s=r.current;switch(i){case e:var u=this.ctx;o.fillStyle=u.fillStyle,o.strokeStyle=u.strokeStyle,s.fillColor=u.fillStyle,s.strokeColor=u.strokeStyle;break;case t:var c=n.Util.makeCssRgb(a[0],a[1],a[2]);o.fillStyle=c,o.strokeStyle=c,s.fillColor=c,s.strokeColor=c;break;default:throw new n.FormatError("Unsupported paint type: ".concat(i))}},getPattern:function(e,t){var r=this.createPatternCanvas(t);return(e=this.ctx).setTransform.apply(e,this.baseTransform),e.transform.apply(e,this.matrix),this.scaleToContext(),e.createPattern(r,"repeat")}},r}();t.TilingPattern=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.GlobalWorkerOptions=void 0;var n=Object.create(null);t.GlobalWorkerOptions=n,n.workerPort=void 0===n.workerPort?null:n.workerPort,n.workerSrc=void 0===n.workerSrc?"":n.workerSrc},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MessageHandler=h;var n,i=(n=r(147))&&n.__esModule?n:{default:n},a=r(1);function o(e){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function s(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function u(e,t){return c.apply(this,arguments)}function c(){var e;return e=i.default.mark((function e(t,r){var n,a=arguments;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=a.length>2&&void 0!==a[2]?a[2]:null,t){e.next=3;break}return e.abrupt("return");case 3:return e.abrupt("return",t.apply(n,r));case 4:case"end":return e.stop()}}),e,this)})),c=function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){s(a,n,i,o,u,"next",e)}function u(e){s(a,n,i,o,u,"throw",e)}o(void 0)}))},c.apply(this,arguments)}function l(e){if("object"!==o(e))return e;switch(e.name){case"AbortException":return new a.AbortException(e.message);case"MissingPDFException":return new a.MissingPDFException(e.message);case"UnexpectedResponseException":return new a.UnexpectedResponseException(e.message,e.status);default:return new a.UnknownErrorException(e.message,e.details)}}function f(e){return!(e instanceof Error)||e instanceof a.AbortException||e instanceof a.MissingPDFException||e instanceof a.UnexpectedResponseException||e instanceof a.UnknownErrorException?e:new a.UnknownErrorException(e.message,e.toString())}function d(e,t,r){t?e.resolve():e.reject(r)}function h(e,t,r){var n=this;this.sourceName=e,this.targetName=t,this.comObj=r,this.callbackId=1,this.streamId=1,this.postMessageTransfers=!0,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null);var i=this.callbacksCapabilities=Object.create(null),a=this.actionHandler=Object.create(null);this._onComObjOnMessage=function(e){var t=e.data;if(t.targetName===n.sourceName)if(t.stream)n._processStreamMessage(t);else if(t.isReply){var o=t.callbackId;if(!(t.callbackId in i))throw new Error("Cannot resolve callback ".concat(o));var s=i[o];delete i[o],"error"in t?s.reject(l(t.error)):s.resolve(t.data)}else{if(!(t.action in a))throw new Error("Unknown action from worker: ".concat(t.action));var u=a[t.action];if(t.callbackId){var c=n.sourceName,d=t.sourceName;Promise.resolve().then((function(){return u[0].call(u[1],t.data)})).then((function(e){r.postMessage({sourceName:c,targetName:d,isReply:!0,callbackId:t.callbackId,data:e})}),(function(e){r.postMessage({sourceName:c,targetName:d,isReply:!0,callbackId:t.callbackId,error:f(e)})}))}else t.streamId?n._createStreamSink(t):u[0].call(u[1],t.data)}},r.addEventListener("message",this._onComObjOnMessage)}h.prototype={on:function(e,t,r){var n=this.actionHandler;if(n[e])throw new Error('There is already an actionName called "'.concat(e,'"'));n[e]=[t,r]},send:function(e,t,r){var n={sourceName:this.sourceName,targetName:this.targetName,action:e,data:t};this.postMessage(n,r)},sendWithPromise:function(e,t,r){var n=this.callbackId++,i={sourceName:this.sourceName,targetName:this.targetName,action:e,data:t,callbackId:n},o=(0,a.createPromiseCapability)();this.callbacksCapabilities[n]=o;try{this.postMessage(i,r)}catch(s){o.reject(s)}return o.promise},sendWithStream:function(e,t,r,n){var i=this,o=this.streamId++,s=this.sourceName,u=this.targetName;return new a.ReadableStream({start:function(r){var n=(0,a.createPromiseCapability)();return i.streamControllers[o]={controller:r,startCall:n,isClosed:!1},i.postMessage({sourceName:s,targetName:u,action:e,streamId:o,data:t,desiredSize:r.desiredSize}),n.promise},pull:function(e){var t=(0,a.createPromiseCapability)();return i.streamControllers[o].pullCall=t,i.postMessage({sourceName:s,targetName:u,stream:"pull",streamId:o,desiredSize:e.desiredSize}),t.promise},cancel:function(e){var t=(0,a.createPromiseCapability)();return i.streamControllers[o].cancelCall=t,i.streamControllers[o].isClosed=!0,i.postMessage({sourceName:s,targetName:u,stream:"cancel",reason:e,streamId:o}),t.promise}},r)},_createStreamSink:function(e){var t=this,r=this,n=this.actionHandler[e.action],i=e.streamId,o=e.desiredSize,s=this.sourceName,c=e.sourceName,l=(0,a.createPromiseCapability)(),f=function(e){var r=e.stream,n=e.chunk,a=e.transfers,o=e.success,u=e.reason;t.postMessage({sourceName:s,targetName:c,stream:r,streamId:i,chunk:n,success:o,reason:u},a)},d={enqueue:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2?arguments[2]:void 0;if(!this.isCancelled){var n=this.desiredSize;this.desiredSize-=t,n>0&&this.desiredSize<=0&&(this.sinkCapability=(0,a.createPromiseCapability)(),this.ready=this.sinkCapability.promise),f({stream:"enqueue",chunk:e,transfers:r})}},close:function(){this.isCancelled||(this.isCancelled=!0,f({stream:"close"}),delete r.streamSinks[i])},error:function(e){this.isCancelled||(this.isCancelled=!0,f({stream:"error",reason:e}))},sinkCapability:l,onPull:null,onCancel:null,isCancelled:!1,desiredSize:o,ready:null};d.sinkCapability.resolve(),d.ready=d.sinkCapability.promise,this.streamSinks[i]=d,u(n[0],[e.data,d],n[1]).then((function(){f({stream:"start_complete",success:!0})}),(function(e){f({stream:"start_complete",success:!1,reason:e})}))},_processStreamMessage:function(e){var t=this,r=this.sourceName,n=e.sourceName,i=e.streamId,o=function(e){var a=e.stream,o=e.success,s=e.reason;t.comObj.postMessage({sourceName:r,targetName:n,stream:a,success:o,streamId:i,reason:s})},s=function(){Promise.all([t.streamControllers[e.streamId].startCall,t.streamControllers[e.streamId].pullCall,t.streamControllers[e.streamId].cancelCall].map((function(e){return e&&(t=e.promise,Promise.resolve(t).catch((function(){})));var t}))).then((function(){delete t.streamControllers[e.streamId]}))};switch(e.stream){case"start_complete":d(this.streamControllers[e.streamId].startCall,e.success,l(e.reason));break;case"pull_complete":d(this.streamControllers[e.streamId].pullCall,e.success,l(e.reason));break;case"pull":if(!this.streamSinks[e.streamId]){o({stream:"pull_complete",success:!0});break}this.streamSinks[e.streamId].desiredSize<=0&&e.desiredSize>0&&this.streamSinks[e.streamId].sinkCapability.resolve(),this.streamSinks[e.streamId].desiredSize=e.desiredSize,u(this.streamSinks[e.streamId].onPull).then((function(){o({stream:"pull_complete",success:!0})}),(function(e){o({stream:"pull_complete",success:!1,reason:e})}));break;case"enqueue":(0,a.assert)(this.streamControllers[e.streamId],"enqueue should have stream controller"),this.streamControllers[e.streamId].isClosed||this.streamControllers[e.streamId].controller.enqueue(e.chunk);break;case"close":if((0,a.assert)(this.streamControllers[e.streamId],"close should have stream controller"),this.streamControllers[e.streamId].isClosed)break;this.streamControllers[e.streamId].isClosed=!0,this.streamControllers[e.streamId].controller.close(),s();break;case"error":(0,a.assert)(this.streamControllers[e.streamId],"error should have stream controller"),this.streamControllers[e.streamId].controller.error(l(e.reason)),s();break;case"cancel_complete":d(this.streamControllers[e.streamId].cancelCall,e.success,l(e.reason)),s();break;case"cancel":if(!this.streamSinks[e.streamId])break;u(this.streamSinks[e.streamId].onCancel,[l(e.reason)]).then((function(){o({stream:"cancel_complete",success:!0})}),(function(e){o({stream:"cancel_complete",success:!1,reason:e})})),this.streamSinks[e.streamId].sinkCapability.reject(l(e.reason)),this.streamSinks[e.streamId].isCancelled=!0,delete this.streamSinks[e.streamId];break;default:throw new Error("Unexpected stream case")}},postMessage:function(e,t){t&&this.postMessageTransfers?this.comObj.postMessage(e,t):this.comObj.postMessage(e)},destroy:function(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Metadata=void 0;var n=r(1),i=r(159);function a(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var o=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),(0,n.assert)("string"===typeof t,"Metadata: input is not a string"),t=this._repair(t);var r=(new i.SimpleXMLParser).parseFromString(t);this._metadata=Object.create(null),r&&this._parse(r)}var t,r,o;return t=e,(r=[{key:"_repair",value:function(e){return e.replace(/^([^<]+)/,"").replace(/>\\376\\377([^<]+)/g,(function(e,t){for(var r=t.replace(/\\([0-3])([0-7])([0-7])/g,(function(e,t,r,n){return String.fromCharCode(64*t+8*r+1*n)})).replace(/&(amp|apos|gt|lt|quot);/g,(function(e,t){switch(t){case"amp":return"&";case"apos":return"'";case"gt":return">";case"lt":return"<";case"quot":return'"'}throw new Error("_repair: ".concat(t," isn't defined."))})),n="",i=0,a=r.length;i<a;i+=2){var o=256*r.charCodeAt(i)+r.charCodeAt(i+1);n+=o>=32&&o<127&&60!==o&&62!==o&&38!==o?String.fromCharCode(o):"&#x"+(65536+o).toString(16).substring(1)+";"}return">"+n}))}},{key:"_parse",value:function(e){var t=e.documentElement;if("rdf:rdf"!==t.nodeName.toLowerCase())for(t=t.firstChild;t&&"rdf:rdf"!==t.nodeName.toLowerCase();)t=t.nextSibling;var r=t?t.nodeName.toLowerCase():null;if(t&&"rdf:rdf"===r&&t.hasChildNodes())for(var n=t.childNodes,i=0,a=n.length;i<a;i++){var o=n[i];if("rdf:description"===o.nodeName.toLowerCase())for(var s=0,u=o.childNodes.length;s<u;s++)if("#text"!==o.childNodes[s].nodeName.toLowerCase()){var c=o.childNodes[s],l=c.nodeName.toLowerCase();this._metadata[l]=c.textContent.trim()}}}},{key:"get",value:function(e){var t=this._metadata[e];return"undefined"!==typeof t?t:null}},{key:"getAll",value:function(){return this._metadata}},{key:"has",value:function(e){return"undefined"!==typeof this._metadata[e]}}])&&a(t.prototype,r),o&&a(t,o),e}();t.Metadata=o},function(e,t,r){"use strict";function n(e){return n="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(u){i=!0,a=u}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function a(e,t){return!t||"object"!==n(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function o(e,t,r){return o="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=s(e)););return e}(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}},o(e,t,r||e)}function s(e){return s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},s(e)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function f(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleXMLParser=void 0;var d=0,h=-2,p=-3,v=-4,m=-5,g=-6,y=-9;function b(e,t){var r=e[t];return" "===r||"\n"===r||"\r"===r||"\t"===r}var _=function(){function e(){c(this,e)}return f(e,[{key:"_resolveEntities",value:function(e){var t=this;return e.replace(/&([^;]+);/g,(function(e,r){if("#x"===r.substring(0,2))return String.fromCharCode(parseInt(r.substring(2),16));if("#"===r.substring(0,1))return String.fromCharCode(parseInt(r.substring(1),10));switch(r){case"lt":return"<";case"gt":return">";case"amp":return"&";case"quot":return'"'}return t.onResolveEntity(r)}))}},{key:"_parseContent",value:function(e,t){var r,n=t,i=[];function a(){for(;n<e.length&&b(e,n);)++n}for(;n<e.length&&!b(e,n)&&">"!==e[n]&&"/"!==e[n];)++n;for(r=e.substring(t,n),a();n<e.length&&">"!==e[n]&&"/"!==e[n]&&"?"!==e[n];){a();for(var o,s="";n<e.length&&!b(e,n)&&"="!==e[n];)s+=e[n],++n;if(a(),"="!==e[n])return null;++n,a();var u=e[n];if('"'!==u&&"'"!==u)return null;var c=e.indexOf(u,++n);if(c<0)return null;o=e.substring(n,c),i.push({name:s,value:this._resolveEntities(o)}),n=c+1,a()}return{name:r,attributes:i,parsed:n-t}}},{key:"_parseProcessingInstruction",value:function(e,t){for(var r,n=t;n<e.length&&!b(e,n)&&">"!==e[n]&&"/"!==e[n];)++n;r=e.substring(t,n),function(){for(;n<e.length&&b(e,n);)++n}();for(var i=n;n<e.length&&("?"!==e[n]||">"!==e[n+1]);)++n;return{name:r,value:e.substring(i,n),parsed:n-t}}},{key:"parseXml",value:function(e){for(var t=0;t<e.length;){var r=t;if("<"===e[t]){var n=void 0;switch(e[++r]){case"/":if(++r,(n=e.indexOf(">",r))<0)return void this.onError(y);this.onEndElement(e.substring(r,n)),r=n+1;break;case"?":++r;var i=this._parseProcessingInstruction(e,r);if("?>"!==e.substring(r+i.parsed,r+i.parsed+2))return void this.onError(p);this.onPi(i.name,i.value),r+=i.parsed+2;break;case"!":if("--"===e.substring(r+1,r+3)){if((n=e.indexOf("--\x3e",r+3))<0)return void this.onError(m);this.onComment(e.substring(r+3,n)),r=n+3}else if("[CDATA["===e.substring(r+1,r+8)){if((n=e.indexOf("]]>",r+8))<0)return void this.onError(h);this.onCdata(e.substring(r+8,n)),r=n+3}else{if("DOCTYPE"!==e.substring(r+1,r+8))return void this.onError(g);var a=e.indexOf("[",r+8),o=!1;if((n=e.indexOf(">",r+8))<0)return void this.onError(v);if(a>0&&n>a){if((n=e.indexOf("]>",r+8))<0)return void this.onError(v);o=!0}var s=e.substring(r+8,n+(o?1:0));this.onDoctype(s),r=n+(o?2:1)}break;default:var u=this._parseContent(e,r);if(null===u)return void this.onError(g);var c=!1;if("/>"===e.substring(r+u.parsed,r+u.parsed+2))c=!0;else if(">"!==e.substring(r+u.parsed,r+u.parsed+1))return void this.onError(y);this.onBeginElement(u.name,u.attributes,c),r+=u.parsed+(c?2:1)}}else{for(;r<e.length&&"<"!==e[r];)r++;var l=e.substring(t,r);this.onText(this._resolveEntities(l))}t=r}}},{key:"onResolveEntity",value:function(e){return"&".concat(e,";")}},{key:"onPi",value:function(e,t){}},{key:"onComment",value:function(e){}},{key:"onCdata",value:function(e){}},{key:"onDoctype",value:function(e){}},{key:"onText",value:function(e){}},{key:"onBeginElement",value:function(e,t,r){}},{key:"onEndElement",value:function(e){}},{key:"onError",value:function(e){}}]),e}(),S=function(){function e(t,r){c(this,e),this.nodeName=t,this.nodeValue=r,Object.defineProperty(this,"parentNode",{value:null,writable:!0})}return f(e,[{key:"hasChildNodes",value:function(){return this.childNodes&&this.childNodes.length>0}},{key:"firstChild",get:function(){return this.childNodes&&this.childNodes[0]}},{key:"nextSibling",get:function(){var e=this.parentNode.childNodes;if(e){var t=e.indexOf(this);if(-1!==t)return e[t+1]}}},{key:"textContent",get:function(){return this.childNodes?this.childNodes.map((function(e){return e.textContent})).join(""):this.nodeValue||""}}]),e}(),A=function(e){function t(){var e;return c(this,t),(e=a(this,s(t).call(this)))._currentFragment=null,e._stack=null,e._errorCode=d,e}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}(t,e),f(t,[{key:"parseFromString",value:function(e){if(this._currentFragment=[],this._stack=[],this._errorCode=d,this.parseXml(e),this._errorCode===d){var t=i(this._currentFragment,1)[0];if(t)return{documentElement:t}}}},{key:"onResolveEntity",value:function(e){return"apos"===e?"'":o(s(t.prototype),"onResolveEntity",this).call(this,e)}},{key:"onText",value:function(e){if(!function(e){for(var t=0,r=e.length;t<r;t++)if(!b(e,t))return!1;return!0}(e)){var t=new S("#text",e);this._currentFragment.push(t)}}},{key:"onCdata",value:function(e){var t=new S("#text",e);this._currentFragment.push(t)}},{key:"onBeginElement",value:function(e,t,r){var n=new S(e);n.childNodes=[],this._currentFragment.push(n),r||(this._stack.push(this._currentFragment),this._currentFragment=n.childNodes)}},{key:"onEndElement",value:function(e){this._currentFragment=this._stack.pop()||[];var t=this._currentFragment[this._currentFragment.length-1];if(t)for(var r=0,n=t.childNodes.length;r<n;r++)t.childNodes[r].parentNode=t}},{key:"onError",value:function(e){this._errorCode=e}}]),t}(_);t.SimpleXMLParser=A},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDataTransportStream=void 0;var n,i=(n=r(147))&&n.__esModule?n:{default:n},a=r(1);function o(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function s(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function s(e){o(a,n,i,s,u,"next",e)}function u(e){o(a,n,i,s,u,"throw",e)}s(void 0)}))}}var u=function(){function e(e,t){var r=this;(0,a.assert)(t),this._queuedChunks=[];var n=e.initialData;if(n&&n.length>0){var i=new Uint8Array(n).buffer;this._queuedChunks.push(i)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._contentLength=e.length,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener((function(e,t){r._onReceiveData({begin:e,chunk:t})})),this._pdfDataRangeTransport.addProgressListener((function(e){r._onProgress({loaded:e})})),this._pdfDataRangeTransport.addProgressiveReadListener((function(e){r._onReceiveData({chunk:e})})),this._pdfDataRangeTransport.transportReady()}function t(e,t){this._stream=e,this._done=!1,this._filename=null,this._queuedChunks=t||[],this._requests=[],this._headersReady=Promise.resolve(),e._fullRequestReader=this,this.onProgress=null}function r(e,t,r){this._stream=e,this._begin=t,this._end=r,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}return e.prototype={_onReceiveData:function(e){var t=new Uint8Array(e.chunk).buffer;if(void 0===e.begin)this._fullRequestReader?this._fullRequestReader._enqueue(t):this._queuedChunks.push(t);else{var r=this._rangeReaders.some((function(r){return r._begin===e.begin&&(r._enqueue(t),!0)}));(0,a.assert)(r)}},_onProgress:function(e){if(this._rangeReaders.length>0){var t=this._rangeReaders[0];t.onProgress&&t.onProgress({loaded:e.loaded})}},_removeRangeReader:function(e){var t=this._rangeReaders.indexOf(e);t>=0&&this._rangeReaders.splice(t,1)},getFullReader:function(){(0,a.assert)(!this._fullRequestReader);var e=this._queuedChunks;return this._queuedChunks=null,new t(this,e)},getRangeReader:function(e,t){var n=new r(this,e,t);return this._pdfDataRangeTransport.requestDataRange(e,t),this._rangeReaders.push(n),n},cancelAllRequests:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeReaders.slice(0).forEach((function(t){t.cancel(e)})),this._pdfDataRangeTransport.abort()}},t.prototype={_enqueue:function(e){this._done||(this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunks.push(e))},get headersReady(){return this._headersReady},get filename(){return this._filename},get isRangeSupported(){return this._stream._isRangeSupported},get isStreamingSupported(){return this._stream._isStreamingSupported},get contentLength(){return this._stream._contentLength},read:function(){var e=s(i.default.mark((function e(){var t,r;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(this._queuedChunks.length>0)){e.next=3;break}return t=this._queuedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 3:if(!this._done){e.next=5;break}return e.abrupt("return",{value:void 0,done:!0});case 5:return r=(0,a.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 8:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),cancel:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[]}},r.prototype={_enqueue:function(e){this._done||(0===this._requests.length?this._queuedChunk=e:(this._requests.shift().resolve({value:e,done:!1}),this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[]),this._done=!0,this._stream._removeRangeReader(this))},get isStreamingSupported(){return!1},read:function(){var e=s(i.default.mark((function e(){var t,r;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._queuedChunk){e.next=4;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 4:if(!this._done){e.next=6;break}return e.abrupt("return",{value:void 0,done:!0});case 6:return r=(0,a.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),cancel:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._stream._removeRangeReader(this)}},e}();t.PDFDataTransportStream=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.WebGLContext=void 0;var n=r(1);function i(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var a=function(){function e(t){var r=t.enable,n=void 0!==r&&r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._enabled=!0===n}var t,r,a;return t=e,(r=[{key:"composeSMask",value:function(e){var t=e.layer,r=e.mask,n=e.properties;return o.composeSMask(t,r,n)}},{key:"drawFigures",value:function(e){var t=e.width,r=e.height,n=e.backgroundColor,i=e.figures,a=e.context;return o.drawFigures(t,r,n,i,a)}},{key:"clear",value:function(){o.cleanup()}},{key:"isEnabled",get:function(){var e=this._enabled;return e&&(e=o.tryInitGL()),(0,n.shadow)(this,"isEnabled",e)}}])&&i(t.prototype,r),a&&i(t,a),e}();t.WebGLContext=a;var o=function(){function e(e,t,r){var n=e.createShader(r);if(e.shaderSource(n,t),e.compileShader(n),!e.getShaderParameter(n,e.COMPILE_STATUS)){var i=e.getShaderInfoLog(n);throw new Error("Error during shader compilation: "+i)}return n}function t(t,r){return e(t,r,t.VERTEX_SHADER)}function r(t,r){return e(t,r,t.FRAGMENT_SHADER)}function n(e,t){for(var r=e.createProgram(),n=0,i=t.length;n<i;++n)e.attachShader(r,t[n]);if(e.linkProgram(r),!e.getProgramParameter(r,e.LINK_STATUS)){var a=e.getProgramInfoLog(r);throw new Error("Error during program linking: "+a)}return r}function i(e,t,r){e.activeTexture(r);var n=e.createTexture();return e.bindTexture(e.TEXTURE_2D,n),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t),n}var a,o;function s(){a||(o=document.createElement("canvas"),a=o.getContext("webgl",{premultipliedalpha:!1}))}var u=null,c=null;return{tryInitGL:function(){try{return s(),!!a}catch(e){}return!1},composeSMask:function(e,c,l){var f=e.width,d=e.height;u||function(){var e,i;s(),e=o,o=null,i=a,a=null;var c=n(i,[t(i,"  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             "),r(i,"  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ")]);i.useProgram(c);var l={};l.gl=i,l.canvas=e,l.resolutionLocation=i.getUniformLocation(c,"u_resolution"),l.positionLocation=i.getAttribLocation(c,"a_position"),l.backdropLocation=i.getUniformLocation(c,"u_backdrop"),l.subtypeLocation=i.getUniformLocation(c,"u_subtype");var f=i.getAttribLocation(c,"a_texCoord"),d=i.getUniformLocation(c,"u_image"),h=i.getUniformLocation(c,"u_mask"),p=i.createBuffer();i.bindBuffer(i.ARRAY_BUFFER,p),i.bufferData(i.ARRAY_BUFFER,new Float32Array([0,0,1,0,0,1,0,1,1,0,1,1]),i.STATIC_DRAW),i.enableVertexAttribArray(f),i.vertexAttribPointer(f,2,i.FLOAT,!1,0,0),i.uniform1i(d,0),i.uniform1i(h,1),u=l}();var h=u,p=h.canvas,v=h.gl;p.width=f,p.height=d,v.viewport(0,0,v.drawingBufferWidth,v.drawingBufferHeight),v.uniform2f(h.resolutionLocation,f,d),l.backdrop?v.uniform4f(h.resolutionLocation,l.backdrop[0],l.backdrop[1],l.backdrop[2],1):v.uniform4f(h.resolutionLocation,0,0,0,0),v.uniform1i(h.subtypeLocation,"Luminosity"===l.subtype?1:0);var m=i(v,e,v.TEXTURE0),g=i(v,c,v.TEXTURE1),y=v.createBuffer();return v.bindBuffer(v.ARRAY_BUFFER,y),v.bufferData(v.ARRAY_BUFFER,new Float32Array([0,0,f,0,0,d,0,d,f,0,f,d]),v.STATIC_DRAW),v.enableVertexAttribArray(h.positionLocation),v.vertexAttribPointer(h.positionLocation,2,v.FLOAT,!1,0,0),v.clearColor(0,0,0,0),v.enable(v.BLEND),v.blendFunc(v.ONE,v.ONE_MINUS_SRC_ALPHA),v.clear(v.COLOR_BUFFER_BIT),v.drawArrays(v.TRIANGLES,0,6),v.flush(),v.deleteTexture(m),v.deleteTexture(g),v.deleteBuffer(y),p},drawFigures:function(e,i,u,l,f){c||function(){var e,i;s(),e=o,o=null,i=a,a=null;var u=n(i,[t(i,"  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             "),r(i,"  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ")]);i.useProgram(u);var l={};l.gl=i,l.canvas=e,l.resolutionLocation=i.getUniformLocation(u,"u_resolution"),l.scaleLocation=i.getUniformLocation(u,"u_scale"),l.offsetLocation=i.getUniformLocation(u,"u_offset"),l.positionLocation=i.getAttribLocation(u,"a_position"),l.colorLocation=i.getAttribLocation(u,"a_color"),c=l}();var d=c,h=d.canvas,p=d.gl;h.width=e,h.height=i,p.viewport(0,0,p.drawingBufferWidth,p.drawingBufferHeight),p.uniform2f(d.resolutionLocation,e,i);var v,m,g,y=0;for(v=0,m=l.length;v<m;v++)switch(l[v].type){case"lattice":y+=((g=l[v].coords.length/l[v].verticesPerRow|0)-1)*(l[v].verticesPerRow-1)*6;break;case"triangles":y+=l[v].coords.length}var b=new Float32Array(2*y),_=new Uint8Array(3*y),S=f.coords,A=f.colors,w=0,k=0;for(v=0,m=l.length;v<m;v++){var x=l[v],P=x.coords,R=x.colors;switch(x.type){case"lattice":var C=x.verticesPerRow;g=P.length/C|0;for(var E=1;E<g;E++)for(var T=E*C+1,O=1;O<C;O++,T++)b[w]=S[P[T-C-1]],b[w+1]=S[P[T-C-1]+1],b[w+2]=S[P[T-C]],b[w+3]=S[P[T-C]+1],b[w+4]=S[P[T-1]],b[w+5]=S[P[T-1]+1],_[k]=A[R[T-C-1]],_[k+1]=A[R[T-C-1]+1],_[k+2]=A[R[T-C-1]+2],_[k+3]=A[R[T-C]],_[k+4]=A[R[T-C]+1],_[k+5]=A[R[T-C]+2],_[k+6]=A[R[T-1]],_[k+7]=A[R[T-1]+1],_[k+8]=A[R[T-1]+2],b[w+6]=b[w+2],b[w+7]=b[w+3],b[w+8]=b[w+4],b[w+9]=b[w+5],b[w+10]=S[P[T]],b[w+11]=S[P[T]+1],_[k+9]=_[k+3],_[k+10]=_[k+4],_[k+11]=_[k+5],_[k+12]=_[k+6],_[k+13]=_[k+7],_[k+14]=_[k+8],_[k+15]=A[R[T]],_[k+16]=A[R[T]+1],_[k+17]=A[R[T]+2],w+=12,k+=18;break;case"triangles":for(var L=0,I=P.length;L<I;L++)b[w]=S[P[L]],b[w+1]=S[P[L]+1],_[k]=A[R[L]],_[k+1]=A[R[L]+1],_[k+2]=A[R[L]+2],w+=2,k+=3}}u?p.clearColor(u[0]/255,u[1]/255,u[2]/255,1):p.clearColor(0,0,0,0),p.clear(p.COLOR_BUFFER_BIT);var F=p.createBuffer();p.bindBuffer(p.ARRAY_BUFFER,F),p.bufferData(p.ARRAY_BUFFER,b,p.STATIC_DRAW),p.enableVertexAttribArray(d.positionLocation),p.vertexAttribPointer(d.positionLocation,2,p.FLOAT,!1,0,0);var D=p.createBuffer();return p.bindBuffer(p.ARRAY_BUFFER,D),p.bufferData(p.ARRAY_BUFFER,_,p.STATIC_DRAW),p.enableVertexAttribArray(d.colorLocation),p.vertexAttribPointer(d.colorLocation,3,p.UNSIGNED_BYTE,!1,0,0),p.uniform2f(d.scaleLocation,f.scaleX,f.scaleY),p.uniform2f(d.offsetLocation,f.offsetX,f.offsetY),p.drawArrays(p.TRIANGLES,0,y),p.flush(),p.deleteBuffer(F),p.deleteBuffer(D),h},cleanup:function(){u&&u.canvas&&(u.canvas.width=0,u.canvas.height=0),c&&c.canvas&&(c.canvas.width=0,c.canvas.height=0),u=null,c=null}}}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.renderTextLayer=void 0;var n,i=r(1),a=(n=r(3))&&n.__esModule?n:{default:n},o=function(){var e=/\S/,t=["left: ",0,"px; top: ",0,"px; font-size: ",0,"px; font-family: ","",";"];function r(r,n,a){var o,s=document.createElement("span"),u={style:null,angle:0,canvasWidth:0,isWhitespace:!1,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1};if(r._textDivs.push(s),o=n.str,!e.test(o))return u.isWhitespace=!0,void r._textDivProperties.set(s,u);var c=i.Util.transform(r._viewport.transform,n.transform),l=Math.atan2(c[1],c[0]),f=a[n.fontName];f.vertical&&(l+=Math.PI/2);var d,h,p=Math.sqrt(c[2]*c[2]+c[3]*c[3]),v=p;if(f.ascent?v=f.ascent*v:f.descent&&(v=(1+f.descent)*v),0===l?(d=c[4],h=c[5]-v):(d=c[4]+v*Math.sin(l),h=c[5]-v*Math.cos(l)),t[1]=d,t[3]=h,t[5]=p,t[7]=f.fontFamily,u.style=t.join(""),s.setAttribute("style",u.style),s.textContent=n.str,r._fontInspectorEnabled&&(s.dataset.fontName=n.fontName),0!==l&&(u.angle=l*(180/Math.PI)),n.str.length>1&&(f.vertical?u.canvasWidth=n.height*r._viewport.scale:u.canvasWidth=n.width*r._viewport.scale),r._textDivProperties.set(s,u),r._textContentStream&&r._layoutText(s),r._enhanceTextSelection){var m=1,g=0;0!==l&&(m=Math.cos(l),g=Math.sin(l));var y,b,_=(f.vertical?n.height:n.width)*r._viewport.scale,S=p;0!==l?(y=[m,g,-g,m,d,h],b=i.Util.getAxialAlignedBoundingBox([0,0,_,S],y)):b=[d,h,d+_,h+S],r._bounds.push({left:b[0],top:b[1],right:b[2],bottom:b[3],div:s,size:[_,S],m:y})}}function n(e){if(!e._canceled){var t=e._textDivs,r=e._capability,n=t.length;if(n>1e5)return e._renderingDone=!0,void r.resolve();if(!e._textContentStream)for(var i=0;i<n;i++)e._layoutText(t[i]);e._renderingDone=!0,r.resolve()}}function o(e){for(var t=e._bounds,r=e._viewport,n=function(e,t,r){var n=r.map((function(e,t){return{x1:e.left,y1:e.top,x2:e.right,y2:e.bottom,index:t,x1New:void 0,x2New:void 0}}));s(e,n);var i=new Array(r.length);return n.forEach((function(e){var t=e.index;i[t]={left:e.x1New,top:0,right:e.x2New,bottom:0}})),r.map((function(t,r){var a=i[r],o=n[r];o.x1=t.top,o.y1=e-a.right,o.x2=t.bottom,o.y2=e-a.left,o.index=r,o.x1New=void 0,o.x2New=void 0})),s(t,n),n.forEach((function(e){var t=e.index;i[t].top=e.x1New,i[t].bottom=e.x2New})),i}(r.width,r.height,t),a=0;a<n.length;a++){var o=t[a].div,u=e._textDivProperties.get(o);if(0!==u.angle){var c=n[a],l=t[a],f=l.m,d=f[0],h=f[1],p=[[0,0],[0,l.size[1]],[l.size[0],0],l.size],v=new Float64Array(64);p.forEach((function(e,t){var r=i.Util.applyTransform(e,f);v[t+0]=d&&(c.left-r[0])/d,v[t+4]=h&&(c.top-r[1])/h,v[t+8]=d&&(c.right-r[0])/d,v[t+12]=h&&(c.bottom-r[1])/h,v[t+16]=h&&(c.left-r[0])/-h,v[t+20]=d&&(c.top-r[1])/d,v[t+24]=h&&(c.right-r[0])/-h,v[t+28]=d&&(c.bottom-r[1])/d,v[t+32]=d&&(c.left-r[0])/-d,v[t+36]=h&&(c.top-r[1])/-h,v[t+40]=d&&(c.right-r[0])/-d,v[t+44]=h&&(c.bottom-r[1])/-h,v[t+48]=h&&(c.left-r[0])/h,v[t+52]=d&&(c.top-r[1])/-d,v[t+56]=h&&(c.right-r[0])/h,v[t+60]=d&&(c.bottom-r[1])/-d}));var m=function(e,t,r){for(var n=0,i=0;i<r;i++){var a=e[t++];a>0&&(n=n?Math.min(a,n):a)}return n},g=1+Math.min(Math.abs(d),Math.abs(h));u.paddingLeft=m(v,32,16)/g,u.paddingTop=m(v,48,16)/g,u.paddingRight=m(v,0,16)/g,u.paddingBottom=m(v,16,16)/g,e._textDivProperties.set(o,u)}else u.paddingLeft=t[a].left-n[a].left,u.paddingTop=t[a].top-n[a].top,u.paddingRight=n[a].right-t[a].right,u.paddingBottom=n[a].bottom-t[a].bottom,e._textDivProperties.set(o,u)}}function s(e,t){t.sort((function(e,t){return e.x1-t.x1||e.index-t.index}));var r=[{start:-1/0,end:1/0,boundary:{x1:-1/0,y1:-1/0,x2:0,y2:1/0,index:-1,x1New:0,x2New:0}}];t.forEach((function(e){for(var t=0;t<r.length&&r[t].end<=e.y1;)t++;for(var n,i,a=r.length-1;a>=0&&r[a].start>=e.y2;)a--;var o,s,u=-1/0;for(o=t;o<=a;o++){var c;(c=(i=(n=r[o]).boundary).x2>e.x1?i.index>e.index?i.x1New:e.x1:void 0===i.x2New?(i.x2+e.x1)/2:i.x2New)>u&&(u=c)}for(e.x1New=u,o=t;o<=a;o++)void 0===(i=(n=r[o]).boundary).x2New?i.x2>e.x1?i.index>e.index&&(i.x2New=i.x2):i.x2New=u:i.x2New>u&&(i.x2New=Math.max(u,i.x2));var l=[],f=null;for(o=t;o<=a;o++){var d=(i=(n=r[o]).boundary).x2>e.x2?i:e;f===d?l[l.length-1].end=n.end:(l.push({start:n.start,end:n.end,boundary:d}),f=d)}for(r[t].start<e.y1&&(l[0].start=e.y1,l.unshift({start:r[t].start,end:e.y1,boundary:r[t].boundary})),e.y2<r[a].end&&(l[l.length-1].end=e.y2,l.push({start:e.y2,end:r[a].end,boundary:r[a].boundary})),o=t;o<=a;o++)if(void 0===(i=(n=r[o]).boundary).x2New){var h=!1;for(s=t-1;!h&&s>=0&&r[s].start>=i.y1;s--)h=r[s].boundary===i;for(s=a+1;!h&&s<r.length&&r[s].end<=i.y2;s++)h=r[s].boundary===i;for(s=0;!h&&s<l.length;s++)h=l[s].boundary===i;h||(i.x2New=u)}Array.prototype.splice.apply(r,[t,a-t+1].concat(l))})),r.forEach((function(t){var r=t.boundary;void 0===r.x2New&&(r.x2New=Math.max(e,r.x2))}))}function u(e){var t=e.textContent,r=e.textContentStream,n=e.container,o=e.viewport,s=e.textDivs,u=e.textContentItemsStr,c=e.enhanceTextSelection;this._textContent=t,this._textContentStream=r,this._container=n,this._viewport=o,this._textDivs=s||[],this._textContentItemsStr=u||[],this._enhanceTextSelection=!!c,this._fontInspectorEnabled=!(!a.default.FontInspector||!a.default.FontInspector.enabled),this._reader=null,this._layoutTextLastFontSize=null,this._layoutTextLastFontFamily=null,this._layoutTextCtx=null,this._textDivProperties=new WeakMap,this._renderingDone=!1,this._canceled=!1,this._capability=(0,i.createPromiseCapability)(),this._renderTimer=null,this._bounds=[]}return u.prototype={get promise(){return this._capability.promise},cancel:function(){this._reader&&(this._reader.cancel(new i.AbortException("text layer task cancelled")),this._reader=null),this._canceled=!0,null!==this._renderTimer&&(clearTimeout(this._renderTimer),this._renderTimer=null),this._capability.reject("canceled")},_processItems:function(e,t){for(var n=0,i=e.length;n<i;n++)this._textContentItemsStr.push(e[n].str),r(this,e[n],t)},_layoutText:function(e){var t=this._container,r=this._textDivProperties.get(e);if(!r.isWhitespace){var n=e.style.fontSize,i=e.style.fontFamily;n===this._layoutTextLastFontSize&&i===this._layoutTextLastFontFamily||(this._layoutTextCtx.font=n+" "+i,this._layoutTextLastFontSize=n,this._layoutTextLastFontFamily=i);var a=this._layoutTextCtx.measureText(e.textContent).width,o="";0!==r.canvasWidth&&a>0&&(r.scale=r.canvasWidth/a,o="scaleX(".concat(r.scale,")")),0!==r.angle&&(o="rotate(".concat(r.angle,"deg) ").concat(o)),o.length>0&&(r.originalTransform=o,e.style.transform=o),this._textDivProperties.set(e,r),t.appendChild(e)}},_render:function(e){var t=this,r=(0,i.createPromiseCapability)(),a=Object.create(null),o=document.createElement("canvas");if(o.mozOpaque=!0,this._layoutTextCtx=o.getContext("2d",{alpha:!1}),this._textContent){var s=this._textContent.items,u=this._textContent.styles;this._processItems(s,u),r.resolve()}else{if(!this._textContentStream)throw new Error('Neither "textContent" nor "textContentStream" parameters specified.');this._reader=this._textContentStream.getReader(),function e(){t._reader.read().then((function(n){var i=n.value;n.done?r.resolve():(Object.assign(a,i.styles),t._processItems(i.items,a),e())}),r.reject)}()}r.promise.then((function(){a=null,e?t._renderTimer=setTimeout((function(){n(t),t._renderTimer=null}),e):n(t)}),this._capability.reject)},expandTextDivs:function(e){if(this._enhanceTextSelection&&this._renderingDone){null!==this._bounds&&(o(this),this._bounds=null);for(var t=0,r=this._textDivs.length;t<r;t++){var n=this._textDivs[t],i=this._textDivProperties.get(n);if(!i.isWhitespace)if(e){var a="",s="";1!==i.scale&&(a="scaleX("+i.scale+")"),0!==i.angle&&(a="rotate("+i.angle+"deg) "+a),0!==i.paddingLeft&&(s+=" padding-left: "+i.paddingLeft/i.scale+"px;",a+=" translateX("+-i.paddingLeft/i.scale+"px)"),0!==i.paddingTop&&(s+=" padding-top: "+i.paddingTop+"px;",a+=" translateY("+-i.paddingTop+"px)"),0!==i.paddingRight&&(s+=" padding-right: "+i.paddingRight/i.scale+"px;"),0!==i.paddingBottom&&(s+=" padding-bottom: "+i.paddingBottom+"px;"),""!==s&&n.setAttribute("style",i.style+s),""!==a&&(n.style.transform=a)}else n.style.padding=0,n.style.transform=i.originalTransform||""}}}},function(e){var t=new u({textContent:e.textContent,textContentStream:e.textContentStream,container:e.container,viewport:e.viewport,textDivs:e.textDivs,textContentItemsStr:e.textContentItemsStr,enhanceTextSelection:e.enhanceTextSelection});return t._render(e.timeout),t}}();t.renderTextLayer=o},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationLayer=void 0;var n=r(151),i=r(1);function a(e,t,r){return a="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var n=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=c(e)););return e}(e,t);if(n){var i=Object.getOwnPropertyDescriptor(n,t);return i.get?i.get.call(r):i.value}},a(e,t,r||e)}function o(e){return o="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function s(e,t){return!t||"object"!==o(t)&&"function"!==typeof t?u(e):t}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}function l(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&f(e,t)}function f(e,t){return f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},f(e,t)}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function h(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function p(e,t,r){return t&&h(e.prototype,t),r&&h(e,r),e}var v=function(){function e(){d(this,e)}return p(e,null,[{key:"create",value:function(e){switch(e.data.annotationType){case i.AnnotationType.LINK:return new g(e);case i.AnnotationType.TEXT:return new y(e);case i.AnnotationType.WIDGET:switch(e.data.fieldType){case"Tx":return new _(e);case"Btn":return e.data.radioButton?new A(e):e.data.checkBox?new S(e):new w(e);case"Ch":return new k(e)}return new b(e);case i.AnnotationType.POPUP:return new x(e);case i.AnnotationType.LINE:return new R(e);case i.AnnotationType.SQUARE:return new C(e);case i.AnnotationType.CIRCLE:return new E(e);case i.AnnotationType.POLYLINE:return new T(e);case i.AnnotationType.INK:return new L(e);case i.AnnotationType.POLYGON:return new O(e);case i.AnnotationType.HIGHLIGHT:return new I(e);case i.AnnotationType.UNDERLINE:return new F(e);case i.AnnotationType.SQUIGGLY:return new D(e);case i.AnnotationType.STRIKEOUT:return new j(e);case i.AnnotationType.STAMP:return new M(e);case i.AnnotationType.FILEATTACHMENT:return new N(e);default:return new m(e)}}}]),e}(),m=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];d(this,e),this.isRenderable=r,this.data=t.data,this.layer=t.layer,this.page=t.page,this.viewport=t.viewport,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderInteractiveForms=t.renderInteractiveForms,this.svgFactory=t.svgFactory,r&&(this.container=this._createContainer(n))}return p(e,[{key:"_createContainer",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.data,r=this.page,n=this.viewport,a=document.createElement("section"),o=t.rect[2]-t.rect[0],s=t.rect[3]-t.rect[1];a.setAttribute("data-annotation-id",t.id);var u=i.Util.normalizeRect([t.rect[0],r.view[3]-t.rect[1]+r.view[1],t.rect[2],r.view[3]-t.rect[3]+r.view[1]]);if(a.style.transform="matrix("+n.transform.join(",")+")",a.style.transformOrigin=-u[0]+"px "+-u[1]+"px",!e&&t.borderStyle.width>0){a.style.borderWidth=t.borderStyle.width+"px",t.borderStyle.style!==i.AnnotationBorderStyleType.UNDERLINE&&(o-=2*t.borderStyle.width,s-=2*t.borderStyle.width);var c=t.borderStyle.horizontalCornerRadius,l=t.borderStyle.verticalCornerRadius;if(c>0||l>0){var f=c+"px / "+l+"px";a.style.borderRadius=f}switch(t.borderStyle.style){case i.AnnotationBorderStyleType.SOLID:a.style.borderStyle="solid";break;case i.AnnotationBorderStyleType.DASHED:a.style.borderStyle="dashed";break;case i.AnnotationBorderStyleType.BEVELED:(0,i.warn)("Unimplemented border style: beveled");break;case i.AnnotationBorderStyleType.INSET:(0,i.warn)("Unimplemented border style: inset");break;case i.AnnotationBorderStyleType.UNDERLINE:a.style.borderBottomStyle="solid"}t.color?a.style.borderColor=i.Util.makeCssRgb(0|t.color[0],0|t.color[1],0|t.color[2]):a.style.borderWidth=0}return a.style.left=u[0]+"px",a.style.top=u[1]+"px",a.style.width=o+"px",a.style.height=s+"px",a}},{key:"_createPopup",value:function(e,t,r){t||((t=document.createElement("div")).style.height=e.style.height,t.style.width=e.style.width,e.appendChild(t));var n=new P({container:e,trigger:t,color:r.color,title:r.title,contents:r.contents,hideWrapper:!0}).render();n.style.left=e.style.width,e.appendChild(n)}},{key:"render",value:function(){(0,i.unreachable)("Abstract method `AnnotationElement.render` called")}}]),e}(),g=function(e){function t(e){d(this,t);var r=!!(e.data.url||e.data.dest||e.data.action);return s(this,c(t).call(this,e,r))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="linkAnnotation";var e=this.data,t=this.linkService,r=document.createElement("a");return(0,n.addLinkAttributes)(r,{url:e.url,target:e.newWindow?n.LinkTarget.BLANK:t.externalLinkTarget,rel:t.externalLinkRel}),e.url||(e.action?this._bindNamedAction(r,e.action):this._bindLink(r,e.dest)),this.container.appendChild(r),this.container}},{key:"_bindLink",value:function(e,t){var r=this;e.href=this.linkService.getDestinationHash(t),e.onclick=function(){return t&&r.linkService.navigateTo(t),!1},t&&(e.className="internalLink")}},{key:"_bindNamedAction",value:function(e,t){var r=this;e.href=this.linkService.getAnchorUrl(""),e.onclick=function(){return r.linkService.executeNamedAction(t),!1},e.className="internalLink"}}]),t}(m),y=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="textAnnotation";var e=document.createElement("img");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",e.alt="[{{type}} Annotation]",e.dataset.l10nId="text_annotation_type",e.dataset.l10nArgs=JSON.stringify({type:this.data.name}),this.data.hasPopup||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}}]),t}(m),b=function(e){function t(){return d(this,t),s(this,c(t).apply(this,arguments))}return l(t,e),p(t,[{key:"render",value:function(){return this.container}}]),t}(m),_=function(e){function t(e){d(this,t);var r=e.renderInteractiveForms||!e.data.hasAppearance&&!!e.data.fieldValue;return s(this,c(t).call(this,e,r))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="textWidgetAnnotation";var e=null;if(this.renderInteractiveForms){if(this.data.multiLine?(e=document.createElement("textarea")).textContent=this.data.fieldValue:((e=document.createElement("input")).type="text",e.setAttribute("value",this.data.fieldValue)),e.disabled=this.data.readOnly,null!==this.data.maxLen&&(e.maxLength=this.data.maxLen),this.data.comb){var t=(this.data.rect[2]-this.data.rect[0])/this.data.maxLen;e.classList.add("comb"),e.style.letterSpacing="calc("+t+"px - 1ch)"}}else{(e=document.createElement("div")).textContent=this.data.fieldValue,e.style.verticalAlign="middle",e.style.display="table-cell";var r=null;this.data.fontRefName&&this.page.commonObjs.has(this.data.fontRefName)&&(r=this.page.commonObjs.get(this.data.fontRefName)),this._setTextStyle(e,r)}return null!==this.data.textAlignment&&(e.style.textAlign=["left","center","right"][this.data.textAlignment]),this.container.appendChild(e),this.container}},{key:"_setTextStyle",value:function(e,t){var r=e.style;if(r.fontSize=this.data.fontSize+"px",r.direction=this.data.fontDirection<0?"rtl":"ltr",t){r.fontWeight=t.black?t.bold?"900":"bold":t.bold?"bold":"normal",r.fontStyle=t.italic?"italic":"normal";var n=t.loadedName?'"'+t.loadedName+'", ':"",i=t.fallbackName||"Helvetica, sans-serif";r.fontFamily=n+i}}}]),t}(b),S=function(e){function t(e){return d(this,t),s(this,c(t).call(this,e,e.renderInteractiveForms))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="buttonWidgetAnnotation checkBox";var e=document.createElement("input");return e.disabled=this.data.readOnly,e.type="checkbox",this.data.fieldValue&&"Off"!==this.data.fieldValue&&e.setAttribute("checked",!0),this.container.appendChild(e),this.container}}]),t}(b),A=function(e){function t(e){return d(this,t),s(this,c(t).call(this,e,e.renderInteractiveForms))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="buttonWidgetAnnotation radioButton";var e=document.createElement("input");return e.disabled=this.data.readOnly,e.type="radio",e.name=this.data.fieldName,this.data.fieldValue===this.data.buttonValue&&e.setAttribute("checked",!0),this.container.appendChild(e),this.container}}]),t}(b),w=function(e){function t(){return d(this,t),s(this,c(t).apply(this,arguments))}return l(t,e),p(t,[{key:"render",value:function(){var e=a(c(t.prototype),"render",this).call(this);return e.className="buttonWidgetAnnotation pushButton",e}}]),t}(g),k=function(e){function t(e){return d(this,t),s(this,c(t).call(this,e,e.renderInteractiveForms))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="choiceWidgetAnnotation";var e=document.createElement("select");e.disabled=this.data.readOnly,this.data.combo||(e.size=this.data.options.length,this.data.multiSelect&&(e.multiple=!0));for(var t=0,r=this.data.options.length;t<r;t++){var n=this.data.options[t],i=document.createElement("option");i.textContent=n.displayValue,i.value=n.exportValue,this.data.fieldValue.includes(n.displayValue)&&i.setAttribute("selected",!0),e.appendChild(i)}return this.container.appendChild(e),this.container}}]),t}(b),x=function(e){function t(e){d(this,t);var r=!(!e.data.title&&!e.data.contents);return s(this,c(t).call(this,e,r))}return l(t,e),p(t,[{key:"render",value:function(){if(this.container.className="popupAnnotation",["Line","Square","Circle","PolyLine","Polygon","Ink"].includes(this.data.parentType))return this.container;var e='[data-annotation-id="'+this.data.parentId+'"]',t=this.layer.querySelector(e);if(!t)return this.container;var r=new P({container:this.container,trigger:t,color:this.data.color,title:this.data.title,contents:this.data.contents}),n=parseFloat(t.style.left),i=parseFloat(t.style.width);return this.container.style.transformOrigin=-(n+i)+"px -"+t.style.top,this.container.style.left=n+i+"px",this.container.appendChild(r.render()),this.container}}]),t}(m),P=function(){function e(t){d(this,e),this.container=t.container,this.trigger=t.trigger,this.color=t.color,this.title=t.title,this.contents=t.contents,this.hideWrapper=t.hideWrapper||!1,this.pinned=!1}return p(e,[{key:"render",value:function(){var e=document.createElement("div");e.className="popupWrapper",this.hideElement=this.hideWrapper?e:this.container,this.hideElement.setAttribute("hidden",!0);var t=document.createElement("div");t.className="popup";var r=this.color;if(r){var n=.7*(255-r[0])+r[0],a=.7*(255-r[1])+r[1],o=.7*(255-r[2])+r[2];t.style.backgroundColor=i.Util.makeCssRgb(0|n,0|a,0|o)}var s=this._formatContents(this.contents),u=document.createElement("h1");return u.textContent=this.title,this.trigger.addEventListener("click",this._toggle.bind(this)),this.trigger.addEventListener("mouseover",this._show.bind(this,!1)),this.trigger.addEventListener("mouseout",this._hide.bind(this,!1)),t.addEventListener("click",this._hide.bind(this,!0)),t.appendChild(u),t.appendChild(s),e.appendChild(t),e}},{key:"_formatContents",value:function(e){for(var t=document.createElement("p"),r=e.split(/(?:\r\n?|\n)/),n=0,i=r.length;n<i;++n){var a=r[n];t.appendChild(document.createTextNode(a)),n<i-1&&t.appendChild(document.createElement("br"))}return t}},{key:"_toggle",value:function(){this.pinned?this._hide(!0):this._show(!0)}},{key:"_show",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&(this.pinned=!0),this.hideElement.hasAttribute("hidden")&&(this.hideElement.removeAttribute("hidden"),this.container.style.zIndex+=1)}},{key:"_hide",value:function(){(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.pinned=!1),this.hideElement.hasAttribute("hidden")||this.pinned||(this.hideElement.setAttribute("hidden",!0),this.container.style.zIndex-=1)}}]),e}(),R=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="lineAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=this.svgFactory.createElement("svg:line");return i.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),i.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),i.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),i.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),i.setAttribute("stroke-width",e.borderStyle.width),i.setAttribute("stroke","transparent"),n.appendChild(i),this.container.append(n),this._createPopup(this.container,i,e),this.container}}]),t}(m),C=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="squareAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.borderStyle.width,a=this.svgFactory.createElement("svg:rect");return a.setAttribute("x",i/2),a.setAttribute("y",i/2),a.setAttribute("width",t-i),a.setAttribute("height",r-i),a.setAttribute("stroke-width",i),a.setAttribute("stroke","transparent"),a.setAttribute("fill","none"),n.appendChild(a),this.container.append(n),this._createPopup(this.container,a,e),this.container}}]),t}(m),E=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="circleAnnotation";var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.borderStyle.width,a=this.svgFactory.createElement("svg:ellipse");return a.setAttribute("cx",t/2),a.setAttribute("cy",r/2),a.setAttribute("rx",t/2-i/2),a.setAttribute("ry",r/2-i/2),a.setAttribute("stroke-width",i),a.setAttribute("stroke","transparent"),a.setAttribute("fill","none"),n.appendChild(a),this.container.append(n),this._createPopup(this.container,a,e),this.container}}]),t}(m),T=function(e){function t(e){var r;d(this,t);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return(r=s(this,c(t).call(this,e,n,!0))).containerClassName="polylineAnnotation",r.svgElementName="svg:polyline",r}return l(t,e),p(t,[{key:"render",value:function(){this.container.className=this.containerClassName;for(var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.vertices,a=[],o=0,s=i.length;o<s;o++){var u=i[o].x-e.rect[0],c=e.rect[3]-i[o].y;a.push(u+","+c)}a=a.join(" ");var l=e.borderStyle.width,f=this.svgFactory.createElement(this.svgElementName);return f.setAttribute("points",a),f.setAttribute("stroke-width",l),f.setAttribute("stroke","transparent"),f.setAttribute("fill","none"),n.appendChild(f),this.container.append(n),this._createPopup(this.container,f,e),this.container}}]),t}(m),O=function(e){function t(e){var r;return d(this,t),(r=s(this,c(t).call(this,e))).containerClassName="polygonAnnotation",r.svgElementName="svg:polygon",r}return l(t,e),t}(T),L=function(e){function t(e){var r;d(this,t);var n=!!(e.data.hasPopup||e.data.title||e.data.contents);return(r=s(this,c(t).call(this,e,n,!0))).containerClassName="inkAnnotation",r.svgElementName="svg:polyline",r}return l(t,e),p(t,[{key:"render",value:function(){this.container.className=this.containerClassName;for(var e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],n=this.svgFactory.create(t,r),i=e.inkLists,a=0,o=i.length;a<o;a++){for(var s=i[a],u=[],c=0,l=s.length;c<l;c++){var f=s[c].x-e.rect[0],d=e.rect[3]-s[c].y;u.push(f+","+d)}u=u.join(" ");var h=e.borderStyle.width,p=this.svgFactory.createElement(this.svgElementName);p.setAttribute("points",u),p.setAttribute("stroke-width",h),p.setAttribute("stroke","transparent"),p.setAttribute("fill","none"),this._createPopup(this.container,p,e),n.appendChild(p)}return this.container.append(n),this.container}}]),t}(m),I=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,e),p(t,[{key:"render",value:function(){return this.container.className="highlightAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(m),F=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,e),p(t,[{key:"render",value:function(){return this.container.className="underlineAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(m),D=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,e),p(t,[{key:"render",value:function(){return this.container.className="squigglyAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(m),j=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,e),p(t,[{key:"render",value:function(){return this.container.className="strikeoutAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(m),M=function(e){function t(e){d(this,t);var r=!!(e.data.hasPopup||e.data.title||e.data.contents);return s(this,c(t).call(this,e,r,!0))}return l(t,e),p(t,[{key:"render",value:function(){return this.container.className="stampAnnotation",this.data.hasPopup||this._createPopup(this.container,null,this.data),this.container}}]),t}(m),N=function(e){function t(e){var r;d(this,t);var a=(r=s(this,c(t).call(this,e,!0))).data.file,o=a.filename,l=a.content;return r.filename=(0,n.getFilenameFromUrl)(o),r.content=l,r.linkService.eventBus&&r.linkService.eventBus.dispatch("fileattachmentannotation",{source:u(u(r)),id:(0,i.stringToPDFString)(o),filename:o,content:l}),r}return l(t,e),p(t,[{key:"render",value:function(){this.container.className="fileAttachmentAnnotation";var e=document.createElement("div");return e.style.height=this.container.style.height,e.style.width=this.container.style.width,e.addEventListener("dblclick",this._download.bind(this)),this.data.hasPopup||!this.data.title&&!this.data.contents||this._createPopup(this.container,e,this.data),this.container.appendChild(e),this.container}},{key:"_download",value:function(){this.downloadManager?this.downloadManager.downloadData(this.content,this.filename,""):(0,i.warn)("Download cannot be started due to unavailable download manager")}}]),t}(m),q=function(){function e(){d(this,e)}return p(e,null,[{key:"render",value:function(e){for(var t=0,r=e.annotations.length;t<r;t++){var i=e.annotations[t];if(i){var a=v.create({data:i,layer:e.div,page:e.page,viewport:e.viewport,linkService:e.linkService,downloadManager:e.downloadManager,imageResourcesPath:e.imageResourcesPath||"",renderInteractiveForms:e.renderInteractiveForms||!1,svgFactory:new n.DOMSVGFactory});a.isRenderable&&e.div.appendChild(a.render())}}}},{key:"update",value:function(e){for(var t=0,r=e.annotations.length;t<r;t++){var n=e.annotations[t],i=e.div.querySelector('[data-annotation-id="'+n.id+'"]');i&&(i.style.transform="matrix("+e.viewport.transform.join(",")+")")}e.div.removeAttribute("hidden")}}]),e}();t.AnnotationLayer=q},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SVGGraphics=void 0;var i,a=n(1),o=n(151),s=(i=n(4))&&i.__esModule?i:{default:i},u=function(){throw new Error("Not implemented: SVGGraphics")};t.SVGGraphics=u;var c={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},l=function(){for(var e=new Uint8Array([137,80,78,71,13,10,26,10]),t=new Int32Array(256),n=0;n<256;n++){for(var i=n,o=0;o<8;o++)i=1&i?3988292384^i>>1&2147483647:i>>1&2147483647;t[n]=i}function u(e,r,n,i){var a=i,o=r.length;n[a]=o>>24&255,n[a+1]=o>>16&255,n[a+2]=o>>8&255,n[a+3]=255&o,n[a+=4]=255&e.charCodeAt(0),n[a+1]=255&e.charCodeAt(1),n[a+2]=255&e.charCodeAt(2),n[a+3]=255&e.charCodeAt(3),a+=4,n.set(r,a);var s=function(e,r,n){for(var i=-1,a=r;a<n;a++){var o=255&(i^e[a]);i=i>>>8^t[o]}return~i}(n,i+4,a+=r.length);n[a]=s>>24&255,n[a+1]=s>>16&255,n[a+2]=s>>8&255,n[a+3]=255&s}function c(e){var t=e.length,r=65535,n=Math.ceil(t/r),i=new Uint8Array(2+t+5*n+4),a=0;i[a++]=120,i[a++]=156;for(var o=0;t>r;)i[a++]=0,i[a++]=255,i[a++]=255,i[a++]=0,i[a++]=0,i.set(e.subarray(o,o+r),a),a+=r,o+=r,t-=r;i[a++]=1,i[a++]=255&t,i[a++]=t>>8&255,i[a++]=255&~t,i[a++]=(65535&~t)>>8&255,i.set(e.subarray(o),a),a+=e.length-o;var s=function(e,t,r){for(var n=1,i=0,a=t;a<r;++a)i=(i+(n=(n+(255&e[a]))%65521))%65521;return i<<16|n}(e,0,e.length);return i[a++]=s>>24&255,i[a++]=s>>16&255,i[a++]=s>>8&255,i[a++]=255&s,i}function l(t,n,i,o){var l,f,d,h=t.width,p=t.height,v=t.data;switch(n){case a.ImageKind.GRAYSCALE_1BPP:f=0,l=1,d=h+7>>3;break;case a.ImageKind.RGB_24BPP:f=2,l=8,d=3*h;break;case a.ImageKind.RGBA_32BPP:f=6,l=8,d=4*h;break;default:throw new Error("invalid format")}var m,g,y=new Uint8Array((1+d)*p),b=0,_=0;for(m=0;m<p;++m)y[b++]=0,y.set(v.subarray(_,_+d),b),_+=d,b+=d;if(n===a.ImageKind.GRAYSCALE_1BPP&&o)for(b=0,m=0;m<p;m++)for(b++,g=0;g<d;g++)y[b++]^=255;var S=new Uint8Array([h>>24&255,h>>16&255,h>>8&255,255&h,p>>24&255,p>>16&255,p>>8&255,255&p,l,f,0,0,0]),A=function(e){if(!(0,s.default)())return c(e);try{var t;t=parseInt(process.versions.node)>=8?e:new Buffer(e);var n=r(82787).deflateSync(t,{level:9});return n instanceof Uint8Array?n:new Uint8Array(n)}catch(i){(0,a.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+i)}return c(e)}(y),w=e.length+36+S.length+A.length,k=new Uint8Array(w),x=0;return k.set(e,x),u("IHDR",S,k,x+=e.length),u("IDATA",A,k,x+=12+S.length),x+=12+A.length,u("IEND",new Uint8Array(0),k,x),(0,a.createObjectURL)(k,"image/png",i)}return function(e,t,r){return l(e,void 0===e.kind?a.ImageKind.GRAYSCALE_1BPP:e.kind,t,r)}}(),f=function(){function e(){this.fontSizeScale=1,this.fontWeight=c.fontWeight,this.fontSize=0,this.textMatrix=a.IDENTITY_MATRIX,this.fontMatrix=a.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=a.TextRenderingMode.FILL,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=c.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}return e.prototype={clone:function(){return Object.create(this)},setCurrentPoint:function(e,t){this.x=e,this.y=t}},e}();t.SVGGraphics=u=function(){function e(e){if(Number.isInteger(e))return e.toString();var t=e.toFixed(10),r=t.length-1;if("0"!==t[r])return t;do{r--}while("0"===t[r]);return t.substring(0,"."===t[r]?r:r+1)}function t(t){if(0===t[4]&&0===t[5]){if(0===t[1]&&0===t[2])return 1===t[0]&&1===t[3]?"":"scale("+e(t[0])+" "+e(t[3])+")";if(t[0]===t[3]&&t[1]===-t[2])return"rotate("+e(180*Math.acos(t[0])/Math.PI)+")"}else if(1===t[0]&&0===t[1]&&0===t[2]&&1===t[3])return"translate("+e(t[4])+" "+e(t[5])+")";return"matrix("+e(t[0])+" "+e(t[1])+" "+e(t[2])+" "+e(t[3])+" "+e(t[4])+" "+e(t[5])+")"}function r(e,t,r){this.svgFactory=new o.DOMSVGFactory,this.current=new f,this.transformMatrix=a.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=e,this.objs=t,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!r}var n="http://www.w3.org/1999/xlink",i=["butt","round","square"],s=["miter","round","bevel"],u=0,d=0;return r.prototype={save:function(){this.transformStack.push(this.transformMatrix);var e=this.current;this.extraStack.push(e),this.current=e.clone()},restore:function(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null},group:function(e){this.save(),this.executeOpTree(e),this.restore()},loadDependencies:function(e){for(var t=this,r=e.fnArray,n=r.length,i=e.argsArray,o=0;o<n;o++)if(a.OPS.dependency===r[o])for(var s=i[o],u=0,c=s.length;u<c;u++){var l,f=s[u],d="g_"===f.substring(0,2);l=new Promise(d?function(e){t.commonObjs.get(f,e)}:function(e){t.objs.get(f,e)}),this.current.dependencies.push(l)}return Promise.all(this.current.dependencies)},transform:function(e,t,r,n,i,o){var s=[e,t,r,n,i,o];this.transformMatrix=a.Util.transform(this.transformMatrix,s),this.tgrp=null},getSVG:function(e,t){var r=this;this.viewport=t;var n=this._initialize(t);return this.loadDependencies(e).then((function(){r.transformMatrix=a.IDENTITY_MATRIX;var t=r.convertOpList(e);return r.executeOpTree(t),n}))},convertOpList:function(e){var t=e.argsArray,r=e.fnArray,n=r.length,i=[],o=[];for(var s in a.OPS)i[a.OPS[s]]=s;for(var u=0;u<n;u++){var c=r[u];o.push({fnId:c,fn:i[c],args:t[u]})}return function(e){for(var t=[],r=[],n=e.length,i=0;i<n;i++)"save"!==e[i].fn?"restore"===e[i].fn?t=r.pop():t.push(e[i]):(t.push({fnId:92,fn:"group",items:[]}),r.push(t),t=t[t.length-1].items);return t}(o)},executeOpTree:function(e){for(var t=e.length,r=0;r<t;r++){var n=e[r].fn,i=e[r].fnId,o=e[r].args;switch(0|i){case a.OPS.beginText:this.beginText();break;case a.OPS.dependency:break;case a.OPS.setLeading:this.setLeading(o);break;case a.OPS.setLeadingMoveText:this.setLeadingMoveText(o[0],o[1]);break;case a.OPS.setFont:this.setFont(o);break;case a.OPS.showText:case a.OPS.showSpacedText:this.showText(o[0]);break;case a.OPS.endText:this.endText();break;case a.OPS.moveText:this.moveText(o[0],o[1]);break;case a.OPS.setCharSpacing:this.setCharSpacing(o[0]);break;case a.OPS.setWordSpacing:this.setWordSpacing(o[0]);break;case a.OPS.setHScale:this.setHScale(o[0]);break;case a.OPS.setTextMatrix:this.setTextMatrix(o[0],o[1],o[2],o[3],o[4],o[5]);break;case a.OPS.setTextRise:this.setTextRise(o[0]);break;case a.OPS.setTextRenderingMode:this.setTextRenderingMode(o[0]);break;case a.OPS.setLineWidth:this.setLineWidth(o[0]);break;case a.OPS.setLineJoin:this.setLineJoin(o[0]);break;case a.OPS.setLineCap:this.setLineCap(o[0]);break;case a.OPS.setMiterLimit:this.setMiterLimit(o[0]);break;case a.OPS.setFillRGBColor:this.setFillRGBColor(o[0],o[1],o[2]);break;case a.OPS.setStrokeRGBColor:this.setStrokeRGBColor(o[0],o[1],o[2]);break;case a.OPS.setDash:this.setDash(o[0],o[1]);break;case a.OPS.setGState:this.setGState(o[0]);break;case a.OPS.fill:this.fill();break;case a.OPS.eoFill:this.eoFill();break;case a.OPS.stroke:this.stroke();break;case a.OPS.fillStroke:this.fillStroke();break;case a.OPS.eoFillStroke:this.eoFillStroke();break;case a.OPS.clip:this.clip("nonzero");break;case a.OPS.eoClip:this.clip("evenodd");break;case a.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case a.OPS.paintJpegXObject:this.paintJpegXObject(o[0],o[1],o[2]);break;case a.OPS.paintImageXObject:this.paintImageXObject(o[0]);break;case a.OPS.paintInlineImageXObject:this.paintInlineImageXObject(o[0]);break;case a.OPS.paintImageMaskXObject:this.paintImageMaskXObject(o[0]);break;case a.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(o[0],o[1]);break;case a.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case a.OPS.closePath:this.closePath();break;case a.OPS.closeStroke:this.closeStroke();break;case a.OPS.closeFillStroke:this.closeFillStroke();break;case a.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case a.OPS.nextLine:this.nextLine();break;case a.OPS.transform:this.transform(o[0],o[1],o[2],o[3],o[4],o[5]);break;case a.OPS.constructPath:this.constructPath(o[0],o[1]);break;case a.OPS.endPath:this.endPath();break;case 92:this.group(e[r].items);break;default:(0,a.warn)("Unimplemented operator "+n)}}},setWordSpacing:function(e){this.current.wordSpacing=e},setCharSpacing:function(e){this.current.charSpacing=e},nextLine:function(){this.moveText(0,this.current.leading)},setTextMatrix:function(t,r,n,i,a,o){var s=this.current;this.current.textMatrix=this.current.lineMatrix=[t,r,n,i,a,o],this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0,s.xcoords=[],s.tspan=this.svgFactory.createElement("svg:tspan"),s.tspan.setAttributeNS(null,"font-family",s.fontFamily),s.tspan.setAttributeNS(null,"font-size",e(s.fontSize)+"px"),s.tspan.setAttributeNS(null,"y",e(-s.y)),s.txtElement=this.svgFactory.createElement("svg:text"),s.txtElement.appendChild(s.tspan)},beginText:function(){this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0,this.current.textMatrix=a.IDENTITY_MATRIX,this.current.lineMatrix=a.IDENTITY_MATRIX,this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.txtElement=this.svgFactory.createElement("svg:text"),this.current.txtgrp=this.svgFactory.createElement("svg:g"),this.current.xcoords=[]},moveText:function(t,r){var n=this.current;this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=r,n.xcoords=[],n.tspan=this.svgFactory.createElement("svg:tspan"),n.tspan.setAttributeNS(null,"font-family",n.fontFamily),n.tspan.setAttributeNS(null,"font-size",e(n.fontSize)+"px"),n.tspan.setAttributeNS(null,"y",e(-n.y))},showText:function(r){var n=this.current,i=n.font,o=n.fontSize;if(0!==o){var s,u=n.charSpacing,l=n.wordSpacing,f=n.fontDirection,d=n.textHScale*f,h=r.length,p=i.vertical,v=o*n.fontMatrix[0],m=0;for(s=0;s<h;++s){var g=r[s];if(null!==g)if((0,a.isNum)(g))m+=-g*o*.001;else{var y=g.width,b=g.fontChar,_=y*v+((g.isSpace?l:0)+u)*f;g.isInFont||i.missingFile?(n.xcoords.push(n.x+m*d),n.tspan.textContent+=b,m+=_):m+=_}else m+=f*l}p?n.y-=m*d:n.x+=m*d,n.tspan.setAttributeNS(null,"x",n.xcoords.map(e).join(" ")),n.tspan.setAttributeNS(null,"y",e(-n.y)),n.tspan.setAttributeNS(null,"font-family",n.fontFamily),n.tspan.setAttributeNS(null,"font-size",e(n.fontSize)+"px"),n.fontStyle!==c.fontStyle&&n.tspan.setAttributeNS(null,"font-style",n.fontStyle),n.fontWeight!==c.fontWeight&&n.tspan.setAttributeNS(null,"font-weight",n.fontWeight);var S=n.textRenderingMode&a.TextRenderingMode.FILL_STROKE_MASK;S===a.TextRenderingMode.FILL||S===a.TextRenderingMode.FILL_STROKE?(n.fillColor!==c.fillColor&&n.tspan.setAttributeNS(null,"fill",n.fillColor),n.fillAlpha<1&&n.tspan.setAttributeNS(null,"fill-opacity",n.fillAlpha)):n.textRenderingMode===a.TextRenderingMode.ADD_TO_PATH?n.tspan.setAttributeNS(null,"fill","transparent"):n.tspan.setAttributeNS(null,"fill","none"),S!==a.TextRenderingMode.STROKE&&S!==a.TextRenderingMode.FILL_STROKE||this._setStrokeAttributes(n.tspan);var A=n.textMatrix;0!==n.textRise&&((A=A.slice())[5]+=n.textRise),n.txtElement.setAttributeNS(null,"transform",t(A)+" scale(1, -1)"),n.txtElement.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),n.txtElement.appendChild(n.tspan),n.txtgrp.appendChild(n.txtElement),this._ensureTransformGroup().appendChild(n.txtElement)}},setLeadingMoveText:function(e,t){this.setLeading(-t),this.moveText(e,t)},addFontStyle:function(e){this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.appendChild(this.cssStyle));var t=(0,a.createObjectURL)(e.data,e.mimetype,this.forceDataSchema);this.cssStyle.textContent+='@font-face { font-family: "'+e.loadedName+'"; src: url('+t+"); }\n"},setFont:function(t){var r=this.current,n=this.commonObjs.get(t[0]),i=t[1];this.current.font=n,this.embedFonts&&n.data&&!this.embeddedFonts[n.loadedName]&&(this.addFontStyle(n),this.embeddedFonts[n.loadedName]=n),r.fontMatrix=n.fontMatrix?n.fontMatrix:a.FONT_IDENTITY_MATRIX;var o=n.black?n.bold?"bolder":"bold":n.bold?"bold":"normal",s=n.italic?"italic":"normal";i<0?(i=-i,r.fontDirection=-1):r.fontDirection=1,r.fontSize=i,r.fontFamily=n.loadedName,r.fontWeight=o,r.fontStyle=s,r.tspan=this.svgFactory.createElement("svg:tspan"),r.tspan.setAttributeNS(null,"y",e(-r.y)),r.xcoords=[]},endText:function(){var e=this.current;e.textRenderingMode&a.TextRenderingMode.ADD_TO_PATH_FLAG&&e.txtElement&&e.txtElement.hasChildNodes()&&(e.element=e.txtElement,this.clip("nonzero"),this.endPath())},setLineWidth:function(e){e>0&&(this.current.lineWidth=e)},setLineCap:function(e){this.current.lineCap=i[e]},setLineJoin:function(e){this.current.lineJoin=s[e]},setMiterLimit:function(e){this.current.miterLimit=e},setStrokeAlpha:function(e){this.current.strokeAlpha=e},setStrokeRGBColor:function(e,t,r){var n=a.Util.makeCssRgb(e,t,r);this.current.strokeColor=n},setFillAlpha:function(e){this.current.fillAlpha=e},setFillRGBColor:function(e,t,r){var n=a.Util.makeCssRgb(e,t,r);this.current.fillColor=n,this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[]},setDash:function(e,t){this.current.dashArray=e,this.current.dashPhase=t},constructPath:function(t,r){var n=this.current,i=n.x,o=n.y;n.path=this.svgFactory.createElement("svg:path");for(var s=[],u=t.length,c=0,l=0;c<u;c++)switch(0|t[c]){case a.OPS.rectangle:i=r[l++],o=r[l++];var f=i+r[l++],d=o+r[l++];s.push("M",e(i),e(o),"L",e(f),e(o),"L",e(f),e(d),"L",e(i),e(d),"Z");break;case a.OPS.moveTo:i=r[l++],o=r[l++],s.push("M",e(i),e(o));break;case a.OPS.lineTo:i=r[l++],o=r[l++],s.push("L",e(i),e(o));break;case a.OPS.curveTo:i=r[l+4],o=r[l+5],s.push("C",e(r[l]),e(r[l+1]),e(r[l+2]),e(r[l+3]),e(i),e(o)),l+=6;break;case a.OPS.curveTo2:i=r[l+2],o=r[l+3],s.push("C",e(i),e(o),e(r[l]),e(r[l+1]),e(r[l+2]),e(r[l+3])),l+=4;break;case a.OPS.curveTo3:i=r[l+2],o=r[l+3],s.push("C",e(r[l]),e(r[l+1]),e(i),e(o),e(i),e(o)),l+=4;break;case a.OPS.closePath:s.push("Z")}n.path.setAttributeNS(null,"d",s.join(" ")),n.path.setAttributeNS(null,"fill","none"),this._ensureTransformGroup().appendChild(n.path),n.element=n.path,n.setCurrentPoint(i,o)},endPath:function(){if(this.pendingClip){var e=this.current,r="clippath"+u;u++;var n=this.svgFactory.createElement("svg:clipPath");n.setAttributeNS(null,"id",r),n.setAttributeNS(null,"transform",t(this.transformMatrix));var i=e.element.cloneNode(!0);"evenodd"===this.pendingClip?i.setAttributeNS(null,"clip-rule","evenodd"):i.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,n.appendChild(i),this.defs.appendChild(n),e.activeClipUrl&&(e.clipGroup=null,this.extraStack.forEach((function(e){e.clipGroup=null})),n.setAttributeNS(null,"clip-path",e.activeClipUrl)),e.activeClipUrl="url(#"+r+")",this.tgrp=null}},clip:function(e){this.pendingClip=e},closePath:function(){var e=this.current;if(e.path){var t=e.path.getAttributeNS(null,"d");t+="Z",e.path.setAttributeNS(null,"d",t)}},setLeading:function(e){this.current.leading=-e},setTextRise:function(e){this.current.textRise=e},setTextRenderingMode:function(e){this.current.textRenderingMode=e},setHScale:function(e){this.current.textHScale=e/100},setGState:function(e){for(var t=0,r=e.length;t<r;t++){var n=e[t],i=n[0],o=n[1];switch(i){case"LW":this.setLineWidth(o);break;case"LC":this.setLineCap(o);break;case"LJ":this.setLineJoin(o);break;case"ML":this.setMiterLimit(o);break;case"D":this.setDash(o[0],o[1]);break;case"Font":this.setFont(o);break;case"CA":this.setStrokeAlpha(o);break;case"ca":this.setFillAlpha(o);break;default:(0,a.warn)("Unimplemented graphic state "+i)}}},fill:function(){var e=this.current;e.element&&(e.element.setAttributeNS(null,"fill",e.fillColor),e.element.setAttributeNS(null,"fill-opacity",e.fillAlpha),this.endPath())},stroke:function(){var e=this.current;e.element&&(this._setStrokeAttributes(e.element),e.element.setAttributeNS(null,"fill","none"),this.endPath())},_setStrokeAttributes:function(t){var r=this.current;t.setAttributeNS(null,"stroke",r.strokeColor),t.setAttributeNS(null,"stroke-opacity",r.strokeAlpha),t.setAttributeNS(null,"stroke-miterlimit",e(r.miterLimit)),t.setAttributeNS(null,"stroke-linecap",r.lineCap),t.setAttributeNS(null,"stroke-linejoin",r.lineJoin),t.setAttributeNS(null,"stroke-width",e(r.lineWidth)+"px"),t.setAttributeNS(null,"stroke-dasharray",r.dashArray.map(e).join(" ")),t.setAttributeNS(null,"stroke-dashoffset",e(r.dashPhase)+"px")},eoFill:function(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fill()},fillStroke:function(){this.stroke(),this.fill()},eoFillStroke:function(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()},closeStroke:function(){this.closePath(),this.stroke()},closeFillStroke:function(){this.closePath(),this.fillStroke()},closeEOFillStroke:function(){this.closePath(),this.eoFillStroke()},paintSolidColorImageMask:function(){var e=this.current,t=this.svgFactory.createElement("svg:rect");t.setAttributeNS(null,"x","0"),t.setAttributeNS(null,"y","0"),t.setAttributeNS(null,"width","1px"),t.setAttributeNS(null,"height","1px"),t.setAttributeNS(null,"fill",e.fillColor),this._ensureTransformGroup().appendChild(t)},paintJpegXObject:function(t,r,i){var a=this.objs.get(t),o=this.svgFactory.createElement("svg:image");o.setAttributeNS(n,"xlink:href",a.src),o.setAttributeNS(null,"width",e(r)),o.setAttributeNS(null,"height",e(i)),o.setAttributeNS(null,"x","0"),o.setAttributeNS(null,"y",e(-i)),o.setAttributeNS(null,"transform","scale("+e(1/r)+" "+e(-1/i)+")"),this._ensureTransformGroup().appendChild(o)},paintImageXObject:function(e){var t=this.objs.get(e);t?this.paintInlineImageXObject(t):(0,a.warn)("Dependent image isn't ready yet")},paintInlineImageXObject:function(t,r){var i=t.width,a=t.height,o=l(t,this.forceDataSchema,!!r),s=this.svgFactory.createElement("svg:rect");s.setAttributeNS(null,"x","0"),s.setAttributeNS(null,"y","0"),s.setAttributeNS(null,"width",e(i)),s.setAttributeNS(null,"height",e(a)),this.current.element=s,this.clip("nonzero");var u=this.svgFactory.createElement("svg:image");u.setAttributeNS(n,"xlink:href",o),u.setAttributeNS(null,"x","0"),u.setAttributeNS(null,"y",e(-a)),u.setAttributeNS(null,"width",e(i)+"px"),u.setAttributeNS(null,"height",e(a)+"px"),u.setAttributeNS(null,"transform","scale("+e(1/i)+" "+e(-1/a)+")"),r?r.appendChild(u):this._ensureTransformGroup().appendChild(u)},paintImageMaskXObject:function(t){var r=this.current,n=t.width,i=t.height,a=r.fillColor;r.maskId="mask"+d++;var o=this.svgFactory.createElement("svg:mask");o.setAttributeNS(null,"id",r.maskId);var s=this.svgFactory.createElement("svg:rect");s.setAttributeNS(null,"x","0"),s.setAttributeNS(null,"y","0"),s.setAttributeNS(null,"width",e(n)),s.setAttributeNS(null,"height",e(i)),s.setAttributeNS(null,"fill",a),s.setAttributeNS(null,"mask","url(#"+r.maskId+")"),this.defs.appendChild(o),this._ensureTransformGroup().appendChild(s),this.paintInlineImageXObject(t,o)},paintFormXObjectBegin:function(t,r){if(Array.isArray(t)&&6===t.length&&this.transform(t[0],t[1],t[2],t[3],t[4],t[5]),r){var n=r[2]-r[0],i=r[3]-r[1],a=this.svgFactory.createElement("svg:rect");a.setAttributeNS(null,"x",r[0]),a.setAttributeNS(null,"y",r[1]),a.setAttributeNS(null,"width",e(n)),a.setAttributeNS(null,"height",e(i)),this.current.element=a,this.clip("nonzero"),this.endPath()}},paintFormXObjectEnd:function(){},_initialize:function(e){var r=this.svgFactory.create(e.width,e.height),n=this.svgFactory.createElement("svg:defs");r.appendChild(n),this.defs=n;var i=this.svgFactory.createElement("svg:g");return i.setAttributeNS(null,"transform",t(e.transform)),r.appendChild(i),this.svg=i,r},_ensureClipGroup:function(){if(!this.current.clipGroup){var e=this.svgFactory.createElement("svg:g");e.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.appendChild(e),this.current.clipGroup=e}return this.current.clipGroup},_ensureTransformGroup:function(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",t(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().appendChild(this.tgrp):this.svg.appendChild(this.tgrp)),this.tgrp}},r}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNodeStream=void 0;var i,a=(i=n(147))&&i.__esModule?i:{default:i},o=n(1),s=n(166);function u(e){return u="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(e,t){return!t||"object"!==u(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}function f(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&d(e,t)}function d(e,t){return d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},d(e,t)}function h(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function p(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){h(a,n,i,o,s,"next",e)}function s(e){h(a,n,i,o,s,"throw",e)}o(void 0)}))}}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function g(e,t,r){return t&&m(e.prototype,t),r&&m(e,r),e}var y=r(23237),b=r(97492),_=r(31815),S=r(32569),A=/^file:\/\/\/[a-zA-Z]:\//,w=function(){function e(t){v(this,e),this.source=t,this.url=function(e){var t=S.parse(e);return"file:"===t.protocol||t.host?t:/^[a-z]:[/\\]/i.test(e)?S.parse("file:///".concat(e)):(t.host||(t.protocol="file:"),t)}(t.url),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequest=null,this._rangeRequestReaders=[]}return g(e,[{key:"getFullReader",value:function(){return(0,o.assert)(!this._fullRequest),this._fullRequest=this.isFsUrl?new E(this):new R(this),this._fullRequest}},{key:"getRangeReader",value:function(e,t){var r=this.isFsUrl?new T(this,e,t):new C(this,e,t);return this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequest&&this._fullRequest.cancel(e),this._rangeRequestReaders.slice(0).forEach((function(t){t.cancel(e)}))}}]),e}();t.PDFNodeStream=w;var k=function(){function e(t){v(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;var r=t.source;this._contentLength=r.length,this._loaded=0,this._filename=null,this._disableRange=r.disableRange||!1,this._rangeChunkSize=r.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!r.disableStream,this._isRangeSupported=!r.disableRange,this._readableStream=null,this._readCapability=(0,o.createPromiseCapability)(),this._headersCapability=(0,o.createPromiseCapability)()}return g(e,[{key:"read",value:function(){var e=p(a.default.mark((function e(){var t,r;return a.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:if(!this._done){e.next=4;break}return e.abrupt("return",{value:void 0,done:!0});case 4:if(!this._storedError){e.next=6;break}throw this._storedError;case 6:if(null!==(t=this._readableStream.read())){e.next=10;break}return this._readCapability=(0,o.createPromiseCapability)(),e.abrupt("return",this.read());case 10:return this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),r=new Uint8Array(t).buffer,e.abrupt("return",{value:r,done:!1});case 14:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",(function(){t._readCapability.resolve()})),e.on("end",(function(){e.destroy(),t._done=!0,t._readCapability.resolve()})),e.on("error",(function(e){t._error(e)})),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new o.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}},{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),x=function(){function e(t){v(this,e),this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=(0,o.createPromiseCapability)();var r=t.source;this._isStreamingSupported=!r.disableStream}return g(e,[{key:"read",value:function(){var e=p(a.default.mark((function e(){var t,r;return a.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:if(!this._done){e.next=4;break}return e.abrupt("return",{value:void 0,done:!0});case 4:if(!this._storedError){e.next=6;break}throw this._storedError;case 6:if(null!==(t=this._readableStream.read())){e.next=10;break}return this._readCapability=(0,o.createPromiseCapability)(),e.abrupt("return",this.read());case 10:return this._loaded+=t.length,this.onProgress&&this.onProgress({loaded:this._loaded}),r=new Uint8Array(t).buffer,e.abrupt("return",{value:r,done:!1});case 14:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}},{key:"_error",value:function(e){this._storedError=e,this._readCapability.resolve()}},{key:"_setReadableStream",value:function(e){var t=this;this._readableStream=e,e.on("readable",(function(){t._readCapability.resolve()})),e.on("end",(function(){e.destroy(),t._done=!0,t._readCapability.resolve()})),e.on("error",(function(e){t._error(e)})),this._storedError&&this._readableStream.destroy(this._storedError)}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}();function P(e,t){return{protocol:e.protocol,auth:e.auth,host:e.hostname,port:e.port,path:e.path,method:"GET",headers:t}}var R=function(e){function t(e){var r;v(this,t);var n=function(t){if(404===t.statusCode){var n=new o.MissingPDFException('Missing PDF "'.concat(r._url,'".'));return r._storedError=n,void r._headersCapability.reject(n)}r._headersCapability.resolve(),r._setReadableStream(t);var i=function(e){return r._readableStream.headers[e.toLowerCase()]},a=(0,s.validateRangeRequestCapabilities)({getResponseHeader:i,isHttp:e.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),u=a.allowRangeRequests,c=a.suggestedLength;r._isRangeSupported=u,r._contentLength=c||r._contentLength,r._filename=(0,s.extractFilenameFromHeader)(i)};return(r=c(this,l(t).call(this,e)))._request=null,"http:"===r._url.protocol?r._request=b.request(P(r._url,e.httpHeaders),n):r._request=_.request(P(r._url,e.httpHeaders),n),r._request.on("error",(function(e){r._storedError=e,r._headersCapability.reject(e)})),r._request.end(),r}return f(t,e),t}(k),C=function(e){function t(e,r,n){var i;for(var a in v(this,t),(i=c(this,l(t).call(this,e)))._httpHeaders={},e.httpHeaders){var s=e.httpHeaders[a];"undefined"!==typeof s&&(i._httpHeaders[a]=s)}i._httpHeaders.Range="bytes=".concat(r,"-").concat(n-1);var u=function(e){if(404!==e.statusCode)i._setReadableStream(e);else{var t=new o.MissingPDFException('Missing PDF "'.concat(i._url,'".'));i._storedError=t}};return i._request=null,"http:"===i._url.protocol?i._request=b.request(P(i._url,i._httpHeaders),u):i._request=_.request(P(i._url,i._httpHeaders),u),i._request.on("error",(function(e){i._storedError=e})),i._request.end(),i}return f(t,e),t}(x),E=function(e){function t(e){var r;v(this,t),r=c(this,l(t).call(this,e));var n=decodeURIComponent(r._url.path);return A.test(r._url.href)&&(n=n.replace(/^\//,"")),y.lstat(n,(function(e,t){if(e)return"ENOENT"===e.code&&(e=new o.MissingPDFException('Missing PDF "'.concat(n,'".'))),r._storedError=e,void r._headersCapability.reject(e);r._contentLength=t.size,r._setReadableStream(y.createReadStream(n)),r._headersCapability.resolve()})),r}return f(t,e),t}(k),T=function(e){function t(e,r,n){var i;v(this,t),i=c(this,l(t).call(this,e));var a=decodeURIComponent(i._url.path);return A.test(i._url.href)&&(a=a.replace(/^\//,"")),i._setReadableStream(y.createReadStream(a,{start:r,end:n-1})),i}return f(t,e),t}(x)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createResponseStatusError=function(e,t){return 404===e||0===e&&/^file:/.test(t)?new n.MissingPDFException('Missing PDF "'+t+'".'):new n.UnexpectedResponseException("Unexpected server response ("+e+') while retrieving PDF "'+t+'".',e)},t.extractFilenameFromHeader=function(e){var t=e("Content-Disposition");if(t){var r=(0,i.getFilenameFromContentDispositionHeader)(t);if(/\.pdf$/i.test(r))return r}return null},t.validateRangeRequestCapabilities=function(e){var t=e.getResponseHeader,r=e.isHttp,i=e.rangeChunkSize,a=e.disableRange;(0,n.assert)(i>0,"Range chunk size must be larger than zero");var o={allowRangeRequests:!1,suggestedLength:void 0},s=parseInt(t("Content-Length"),10);return Number.isInteger(s)?(o.suggestedLength=s,s<=2*i||a||!r||"bytes"!==t("Accept-Ranges")||"identity"!==(t("Content-Encoding")||"identity")||(o.allowRangeRequests=!0),o):o},t.validateResponseStatus=function(e){return 200===e||206===e};var n=r(1),i=r(167)},function(e,t,r){"use strict";function n(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(u){i=!0,a=u}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}Object.defineProperty(t,"__esModule",{value:!0}),t.getFilenameFromContentDispositionHeader=function(e){var t=!0,r=o("filename\\*","i").exec(e);if(r){var i=c(r=r[1]);return u(i=f(i=l(i=unescape(i))))}if(r=function(e){for(var t,r=[],i=o("filename\\*((?!0\\d)\\d+)(\\*?)","ig");null!==(t=i.exec(e));){var a=n(t,4),s=a[1],u=a[2],f=a[3];if((s=parseInt(s,10))in r){if(0===s)break}else r[s]=[u,f]}var d=[];for(s=0;s<r.length&&s in r;++s){var h=n(r[s],2);u=h[0],f=c(f=h[1]),u&&(f=unescape(f),0===s&&(f=l(f))),d.push(f)}return d.join("")}(e))return u(f(r));if(r=o("filename","i").exec(e)){var a=c(r=r[1]);return u(a=f(a))}function o(e,t){return new RegExp("(?:^|;)\\s*"+e+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',t)}function s(e,r){if(e){if(!/^[\x00-\xFF]+$/.test(r))return r;try{var n=new TextDecoder(e,{fatal:!0}),i=Array.from(r,(function(e){return 255&e.charCodeAt(0)}));r=n.decode(new Uint8Array(i)),t=!1}catch(a){if(/^utf-?8$/i.test(e))try{r=decodeURIComponent(escape(r)),t=!1}catch(o){}}}return r}function u(e){return t&&/[\x80-\xff]/.test(e)&&(e=s("utf-8",e),t&&(e=s("iso-8859-1",e))),e}function c(e){if(e.startsWith('"')){for(var t=e.slice(1).split('\\"'),r=0;r<t.length;++r){var n=t[r].indexOf('"');-1!==n&&(t[r]=t[r].slice(0,n),t.length=r+1),t[r]=t[r].replace(/\\(.)/g,"$1")}e=t.join('"')}return e}function l(e){var t=e.indexOf("'");return-1===t?e:s(e.slice(0,t),e.slice(t+1).replace(/^[^']*'/,""))}function f(e){return!e.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(e)?e:e.replace(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(e,t,r,n){if("q"===r||"Q"===r)return s(t,n=(n=n.replace(/_/g," ")).replace(/=([0-9a-fA-F]{2})/g,(function(e,t){return String.fromCharCode(parseInt(t,16))})));try{n=atob(n)}catch(i){}return s(t,n)}))}return""}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFetchStream=void 0;var n,i=(n=r(147))&&n.__esModule?n:{default:n},a=r(1),o=r(166);function s(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){s(a,n,i,o,u,"next",e)}function u(e){s(a,n,i,o,u,"throw",e)}o(void 0)}))}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function f(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),e}function d(e,t,r){return{method:"GET",headers:e,signal:r&&r.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}var h=function(){function e(t){c(this,e),this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}return f(e,[{key:"getFullReader",value:function(){return(0,a.assert)(!this._fullRequestReader),this._fullRequestReader=new p(this),this._fullRequestReader}},{key:"getRangeReader",value:function(e,t){var r=new v(this,e,t);return this._rangeRequestReaders.push(r),r}},{key:"cancelAllRequests",value:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeRequestReaders.slice(0).forEach((function(t){t.cancel(e)}))}}]),e}();t.PDFFetchStream=h;var p=function(){function e(t){var r=this;c(this,e),this._stream=t,this._reader=null,this._loaded=0,this._filename=null;var n=t.source;for(var i in this._withCredentials=n.withCredentials,this._contentLength=n.length,this._headersCapability=(0,a.createPromiseCapability)(),this._disableRange=n.disableRange||!1,this._rangeChunkSize=n.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),"undefined"!==typeof AbortController&&(this._abortController=new AbortController),this._isStreamingSupported=!n.disableStream,this._isRangeSupported=!n.disableRange,this._headers=new Headers,this._stream.httpHeaders){var s=this._stream.httpHeaders[i];"undefined"!==typeof s&&this._headers.append(i,s)}var u=n.url;fetch(u,d(this._headers,this._withCredentials,this._abortController)).then((function(e){if(!(0,o.validateResponseStatus)(e.status))throw(0,o.createResponseStatusError)(e.status,u);r._reader=e.body.getReader(),r._headersCapability.resolve();var t=function(t){return e.headers.get(t)},n=(0,o.validateRangeRequestCapabilities)({getResponseHeader:t,isHttp:r._stream.isHttp,rangeChunkSize:r._rangeChunkSize,disableRange:r._disableRange}),i=n.allowRangeRequests,s=n.suggestedLength;r._isRangeSupported=i,r._contentLength=s||r._contentLength,r._filename=(0,o.extractFilenameFromHeader)(t),!r._isStreamingSupported&&r._isRangeSupported&&r.cancel(new a.AbortException("streaming is disabled"))})).catch(this._headersCapability.reject),this.onProgress=null}return f(e,[{key:"read",value:function(){var e=u(i.default.mark((function e(){var t,r,n,a;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._headersCapability.promise;case 2:return e.next=4,this._reader.read();case 4:if(t=e.sent,r=t.value,!(n=t.done)){e.next=9;break}return e.abrupt("return",{value:r,done:n});case 9:return this._loaded+=r.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength}),a=new Uint8Array(r).buffer,e.abrupt("return",{value:a,done:!1});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._reader&&this._reader.cancel(e),this._abortController&&this._abortController.abort()}},{key:"headersReady",get:function(){return this._headersCapability.promise}},{key:"filename",get:function(){return this._filename}},{key:"contentLength",get:function(){return this._contentLength}},{key:"isRangeSupported",get:function(){return this._isRangeSupported}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}(),v=function(){function e(t,r,n){var i=this;c(this,e),this._stream=t,this._reader=null,this._loaded=0;var s=t.source;for(var u in this._withCredentials=s.withCredentials,this._readCapability=(0,a.createPromiseCapability)(),this._isStreamingSupported=!s.disableStream,"undefined"!==typeof AbortController&&(this._abortController=new AbortController),this._headers=new Headers,this._stream.httpHeaders){var l=this._stream.httpHeaders[u];"undefined"!==typeof l&&this._headers.append(u,l)}var f=r+"-"+(n-1);this._headers.append("Range","bytes="+f);var h=s.url;fetch(h,d(this._headers,this._withCredentials,this._abortController)).then((function(e){if(!(0,o.validateResponseStatus)(e.status))throw(0,o.createResponseStatusError)(e.status,h);i._readCapability.resolve(),i._reader=e.body.getReader()})),this.onProgress=null}return f(e,[{key:"read",value:function(){var e=u(i.default.mark((function e(){var t,r,n,a;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._readCapability.promise;case 2:return e.next=4,this._reader.read();case 4:if(t=e.sent,r=t.value,!(n=t.done)){e.next=9;break}return e.abrupt("return",{value:r,done:n});case 9:return this._loaded+=r.byteLength,this.onProgress&&this.onProgress({loaded:this._loaded}),a=new Uint8Array(r).buffer,e.abrupt("return",{value:a,done:!1});case 13:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"cancel",value:function(e){this._reader&&this._reader.cancel(e),this._abortController&&this._abortController.abort()}},{key:"isStreamingSupported",get:function(){return this._isStreamingSupported}}]),e}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PDFNetworkStream=h,t.NetworkManager=l;var n=s(r(147)),i=r(1),a=r(166),o=s(r(3));function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function c(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){u(a,n,i,o,s,"next",e)}function s(e){u(a,n,i,o,s,"throw",e)}o(void 0)}))}}function l(e,t){this.url=e,t=t||{},this.isHttp=/^https?:/i.test(e),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this.withCredentials=t.withCredentials||!1,this.getXhr=t.getXhr||function(){return new XMLHttpRequest},this.currXhrId=0,this.pendingRequests=Object.create(null),this.loadedRequests=Object.create(null)}function f(e){var t=e.response;return"string"!==typeof t?t:(0,i.stringToBytes)(t).buffer}var d=function(){try{var e=new XMLHttpRequest;return e.open("GET",o.default.location.href),e.responseType="moz-chunked-arraybuffer","moz-chunked-arraybuffer"===e.responseType}catch(t){return!1}}();function h(e){this._source=e,this._manager=new l(e.url,{httpHeaders:e.httpHeaders,withCredentials:e.withCredentials}),this._rangeChunkSize=e.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}function p(e,t){this._manager=e;var r={onHeadersReceived:this._onHeadersReceived.bind(this),onProgressiveData:t.disableStream?null:this._onProgressiveData.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url,this._fullRequestId=e.requestFull(r),this._headersReceivedCapability=(0,i.createPromiseCapability)(),this._disableRange=t.disableRange||!1,this._contentLength=t.length,this._rangeChunkSize=t.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}function v(e,t,r){this._manager=e;var n={onDone:this._onDone.bind(this),onProgress:this._onProgress.bind(this)};this._requestId=e.requestRange(t,r,n),this._requests=[],this._queuedChunk=null,this._done=!1,this.onProgress=null,this.onClosed=null}l.prototype={requestRange:function(e,t,r){var n={begin:e,end:t};for(var i in r)n[i]=r[i];return this.request(n)},requestFull:function(e){return this.request(e)},request:function(e){var t=this.getXhr(),r=this.currXhrId++,n=this.pendingRequests[r]={xhr:t};for(var i in t.open("GET",this.url),t.withCredentials=this.withCredentials,this.httpHeaders){var a=this.httpHeaders[i];"undefined"!==typeof a&&t.setRequestHeader(i,a)}if(this.isHttp&&"begin"in e&&"end"in e){var o=e.begin+"-"+(e.end-1);t.setRequestHeader("Range","bytes="+o),n.expectedStatus=206}else n.expectedStatus=200;return d&&e.onProgressiveData?(t.responseType="moz-chunked-arraybuffer",n.onProgressiveData=e.onProgressiveData,n.mozChunked=!0):t.responseType="arraybuffer",e.onError&&(t.onerror=function(r){e.onError(t.status)}),t.onreadystatechange=this.onStateChange.bind(this,r),t.onprogress=this.onProgress.bind(this,r),n.onHeadersReceived=e.onHeadersReceived,n.onDone=e.onDone,n.onError=e.onError,n.onProgress=e.onProgress,t.send(null),r},onProgress:function(e,t){var r=this.pendingRequests[e];if(r){if(r.mozChunked){var n=f(r.xhr);r.onProgressiveData(n)}var i=r.onProgress;i&&i(t)}},onStateChange:function(e,t){var r=this.pendingRequests[e];if(r){var n=r.xhr;if(n.readyState>=2&&r.onHeadersReceived&&(r.onHeadersReceived(),delete r.onHeadersReceived),4===n.readyState&&e in this.pendingRequests)if(delete this.pendingRequests[e],0===n.status&&this.isHttp)r.onError&&r.onError(n.status);else{var i=n.status||200;if(200===i&&206===r.expectedStatus||i===r.expectedStatus){this.loadedRequests[e]=!0;var a=f(n);if(206===i){var o=n.getResponseHeader("Content-Range"),s=/bytes (\d+)-(\d+)\/(\d+)/.exec(o),u=parseInt(s[1],10);r.onDone({begin:u,chunk:a})}else r.onProgressiveData?r.onDone(null):a?r.onDone({begin:0,chunk:a}):r.onError&&r.onError(n.status)}else r.onError&&r.onError(n.status)}}},hasPendingRequests:function(){for(var e in this.pendingRequests)return!0;return!1},getRequestXhr:function(e){return this.pendingRequests[e].xhr},isStreamingRequest:function(e){return!!this.pendingRequests[e].onProgressiveData},isPendingRequest:function(e){return e in this.pendingRequests},isLoadedRequest:function(e){return e in this.loadedRequests},abortAllRequests:function(){for(var e in this.pendingRequests)this.abortRequest(0|e)},abortRequest:function(e){var t=this.pendingRequests[e].xhr;delete this.pendingRequests[e],t.abort()}},h.prototype={_onRangeRequestReaderClosed:function(e){var t=this._rangeRequestReaders.indexOf(e);t>=0&&this._rangeRequestReaders.splice(t,1)},getFullReader:function(){return(0,i.assert)(!this._fullRequestReader),this._fullRequestReader=new p(this._manager,this._source),this._fullRequestReader},getRangeReader:function(e,t){var r=new v(this._manager,e,t);return r.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(r),r},cancelAllRequests:function(e){this._fullRequestReader&&this._fullRequestReader.cancel(e),this._rangeRequestReaders.slice(0).forEach((function(t){t.cancel(e)}))}},p.prototype={_onHeadersReceived:function(){var e=this._fullRequestId,t=this._manager.getRequestXhr(e),r=function(e){return t.getResponseHeader(e)},n=(0,a.validateRangeRequestCapabilities)({getResponseHeader:r,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange}),i=n.allowRangeRequests,o=n.suggestedLength;i&&(this._isRangeSupported=!0),this._contentLength=o||this._contentLength,this._filename=(0,a.extractFilenameFromHeader)(r);var s=this._manager;s.isStreamingRequest(e)?this._isStreamingSupported=!0:this._isRangeSupported&&s.abortRequest(e),this._headersReceivedCapability.resolve()},_onProgressiveData:function(e){this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._cachedChunks.push(e)},_onDone:function(e){e&&this._onProgressiveData(e.chunk),this._done=!0,this._cachedChunks.length>0||(this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[])},_onError:function(e){var t=this._url,r=(0,a.createResponseStatusError)(e,t);this._storedError=r,this._headersReceivedCapability.reject(r),this._requests.forEach((function(e){e.reject(r)})),this._requests=[],this._cachedChunks=[]},_onProgress:function(e){this.onProgress&&this.onProgress({loaded:e.loaded,total:e.lengthComputable?e.total:this._contentLength})},get filename(){return this._filename},get isRangeSupported(){return this._isRangeSupported},get isStreamingSupported(){return this._isStreamingSupported},get contentLength(){return this._contentLength},get headersReady(){return this._headersReceivedCapability.promise},read:function(){var e=c(n.default.mark((function e(){var t,r;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!this._storedError){e.next=2;break}throw this._storedError;case 2:if(!(this._cachedChunks.length>0)){e.next=5;break}return t=this._cachedChunks.shift(),e.abrupt("return",{value:t,done:!1});case 5:if(!this._done){e.next=7;break}return e.abrupt("return",{value:void 0,done:!0});case 7:return r=(0,i.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 10:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),cancel:function(e){this._done=!0,this._headersReceivedCapability.reject(e),this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}},v.prototype={_close:function(){this.onClosed&&this.onClosed(this)},_onDone:function(e){var t=e.chunk;this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunk=t,this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._close()},_onProgress:function(e){!this.isStreamingSupported&&this.onProgress&&this.onProgress({loaded:e.loaded})},get isStreamingSupported(){return!1},read:function(){var e=c(n.default.mark((function e(){var t,r;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(null===this._queuedChunk){e.next=4;break}return t=this._queuedChunk,this._queuedChunk=null,e.abrupt("return",{value:t,done:!1});case 4:if(!this._done){e.next=6;break}return e.abrupt("return",{value:void 0,done:!0});case 6:return r=(0,i.createPromiseCapability)(),this._requests.push(r),e.abrupt("return",r.promise);case 9:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),cancel:function(e){this._done=!0,this._requests.forEach((function(e){e.resolve({value:void 0,done:!0})})),this._requests=[],this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}}])},e.exports=n()},3219:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.OutlineItemInternal=void 0;var a=i(r(94634)),o=i(r(91847)),s=i(r(85715)),u=i(r(54756)),c=i(r(29293)),l=i(r(17383)),f=i(r(34579)),d=i(r(12475)),h=i(r(29511)),p=i(r(28452)),v=i(r(63072)),m=i(r(43693)),g=n(r(31014)),y=i(r(15662)),b=i(r(77998)),_=i(r(3357)),S=i(r(88503)),A=r(13947),w=r(8876);function k(e){return function(){var t,r=(0,v.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,v.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,p.default)(this,t)}}var x=function(e){(0,h.default)(r,e);var t=k(r);function r(){var e;(0,l.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,m.default)((0,d.default)(e),"getDestination",(0,c.default)(u.default.mark((function t(){var r,n,i;return u.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.props,n=r.item,i=r.pdf,(0,A.isDefined)(e.destination)){t.next=9;break}if("string"!==typeof n.dest){t.next=8;break}return t.next=5,i.getDestination(n.dest);case 5:e.destination=t.sent,t.next=9;break;case 8:e.destination=n.dest;case 9:return t.abrupt("return",e.destination);case 10:case"end":return t.stop()}}),t)})))),(0,m.default)((0,d.default)(e),"getPageIndex",(0,c.default)(u.default.mark((function t(){var r,n,i,a;return u.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.props.pdf,(0,A.isDefined)(e.pageIndex)){t.next=10;break}return t.next=4,e.getDestination();case 4:if(!(n=t.sent)){t.next=10;break}return i=(0,s.default)(n,1),a=i[0],t.next=9,r.getPageIndex(new S.default(a));case 9:e.pageIndex=t.sent;case 10:return t.abrupt("return",e.pageIndex);case 11:case"end":return t.stop()}}),t)})))),(0,m.default)((0,d.default)(e),"getPageNumber",(0,c.default)(u.default.mark((function t(){return u.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if((0,A.isDefined)(e.pageNumber)){t.next=5;break}return t.next=3,e.getPageIndex();case 3:t.t0=t.sent,e.pageNumber=t.t0+1;case 5:return t.abrupt("return",e.pageNumber);case 6:case"end":return t.stop()}}),t)})))),(0,m.default)((0,d.default)(e),"onClick",function(){var t=(0,c.default)(u.default.mark((function t(r){var n,i,a;return u.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=e.props.onClick,r.preventDefault(),t.next=4,e.getPageIndex();case 4:return i=t.sent,t.next=7,e.getPageNumber();case 7:a=t.sent,n&&n({pageIndex:i,pageNumber:a});case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()),e}return(0,f.default)(r,[{key:"renderSubitems",value:function(){var e=this.props,t=e.item,n=(0,o.default)(e,["item"]);if(!t.items||!t.items.length)return null;var i=t.items;return g.default.createElement("ul",null,i.map((function(e,t){return g.default.createElement(r,(0,a.default)({key:"string"===typeof e.destination?e.destination:t,item:e},n))})))}},{key:"render",value:function(){var e=this.props.item;return g.default.createElement("li",null,g.default.createElement("a",{href:"#",onClick:this.onClick},e.title),this.renderSubitems())}}]),r}(g.PureComponent);t.OutlineItemInternal=x;var P=y.default.oneOfType([y.default.string,y.default.arrayOf(y.default.any)]);x.propTypes={item:y.default.shape({dest:P,items:y.default.arrayOf(y.default.shape({dest:P,title:y.default.string})),title:y.default.string}).isRequired,onClick:y.default.func,pdf:w.isPdf.isRequired};var R=function(e){return g.default.createElement(b.default.Consumer,null,(function(t){return g.default.createElement(_.default.Consumer,null,(function(r){return g.default.createElement(x,(0,a.default)({},t,r,e))}))}))};t.default=R},3344:function(e,t,r){"use strict";t.decode=t.parse=r(14692),t.encode=t.stringify=r(49772)},3357:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=(0,r(31014).createContext)(null);t.default=n},5901:function(e,t,r){var n=r(70079);e.exports=function(e){if(Array.isArray(e))return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},6305:function(e,t,r){var n=r(73738).default;function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}e.exports=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=n(e)&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var a={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&{}.hasOwnProperty.call(e,s)){var u=o?Object.getOwnPropertyDescriptor(e,s):null;u&&(u.get||u.set)?Object.defineProperty(a,s,u):a[s]=e[s]}return a.default=e,r&&r.set(e,a),a},e.exports.__esModule=!0,e.exports.default=e.exports},8876:function(e,t,r){"use strict";var n=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.isRotate=t.isRenderMode=t.isPdf=t.isPageNumber=t.isPageIndex=t.isPage=t.isLinkTarget=t.isLinkService=t.isFile=t.isClassName=t.eventProps=void 0;var i=n(r(73738)),a=n(r(41132)),o=n(r(15662)),s=r(64608),u=r(13947),c=n(r(41489)),l=function(){var e={};return[].concat((0,a.default)(s.mouseEvents),(0,a.default)(s.touchEvents),(0,a.default)(s.keyboardEvents)).forEach((function(t){e[t]=o.default.func})),e}();t.eventProps=l;var f=[o.default.string,o.default.instanceOf(ArrayBuffer),o.default.shape({data:o.default.object,httpHeaders:o.default.object,range:o.default.object,url:o.default.string,withCredentials:o.default.bool})];"undefined"!==typeof File&&f.push(o.default.instanceOf(File)),"undefined"!==typeof Blob&&f.push(o.default.instanceOf(Blob));var d=o.default.oneOfType([o.default.string,o.default.arrayOf(o.default.string)]);t.isClassName=d;var h=o.default.oneOfType(f);t.isFile=h;var p=o.default.instanceOf(c.default);t.isLinkService=p;var v=o.default.oneOf(["_self","_blank","_parent","_top"]);t.isLinkTarget=v;var m=o.default.shape({_transport:o.default.shape({fontLoader:o.default.object.isRequired}).isRequired,commonObjs:o.default.shape({_objs:o.default.object.isRequired}).isRequired,getAnnotations:o.default.func.isRequired,getTextContent:o.default.func.isRequired,getViewport:o.default.func.isRequired,render:o.default.func.isRequired});t.isPage=m;t.isPageIndex=function(e,t,r){var n=e[t],a=e.pageNumber,o=e.pdf;if(!(0,u.isDefined)(o))return null;if((0,u.isDefined)(n)){if("number"!==typeof n)return new Error("`".concat(t,"` of type `").concat((0,i.default)(n),"` supplied to `").concat(r,"`, expected `number`."));if(n<0)return new Error("Expected `".concat(t,"` to be greater or equal to 0."));var s=o.numPages;if(n+1>s)return new Error("Expected `".concat(t,"` to be less or equal to ").concat(s-1,"."))}else if(!(0,u.isDefined)(a))return new Error("`".concat(t,"` not supplied. Either pageIndex or pageNumber must be supplied to `").concat(r,"`."));return null};t.isPageNumber=function(e,t,r){var n=e[t],a=e.pageIndex,o=e.pdf;if(!(0,u.isDefined)(o))return null;if((0,u.isDefined)(n)){if("number"!==typeof n)return new Error("`".concat(t,"` of type `").concat((0,i.default)(n),"` supplied to `").concat(r,"`, expected `number`."));if(n<1)return new Error("Expected `".concat(t,"` to be greater or equal to 1."));var s=o.numPages;if(n>s)return new Error("Expected `".concat(t,"` to be less or equal to ").concat(s,"."))}else if(!(0,u.isDefined)(a))return new Error("`".concat(t,"` not supplied. Either pageIndex or pageNumber must be supplied to `").concat(r,"`."));return null};var g=o.default.oneOfType([o.default.shape({getDestination:o.default.func.isRequired,getOutline:o.default.func.isRequired,getPage:o.default.func.isRequired,numPages:o.default.number.isRequired}),o.default.bool]);t.isPdf=g;var y=o.default.oneOf(["canvas","none","svg"]);t.isRenderMode=y;var b=o.default.oneOf([0,90,180,270]);t.isRotate=b},10140:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return v.default.createElement(y.default.Consumer,null,(function(t){return v.default.createElement(w,(0,a.default)({},t,e))}))},t.TextLayerInternal=void 0;var a=i(r(94634)),o=i(r(54756)),s=i(r(29293)),u=i(r(17383)),c=i(r(34579)),l=i(r(12475)),f=i(r(29511)),d=i(r(28452)),h=i(r(63072)),p=i(r(43693)),v=n(r(31014)),m=i(r(15662)),g=i(r(76219)),y=i(r(75684)),b=i(r(72815)),_=r(13947),S=r(8876);function A(e){return function(){var t,r=(0,h.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,h.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,d.default)(this,t)}}var w=function(e){(0,f.default)(r,e);var t=A(r);function r(){var e;(0,u.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,p.default)((0,l.default)(e),"state",{textItems:null}),(0,p.default)((0,l.default)(e),"loadTextItems",(0,s.default)(o.default.mark((function t(){var r,n,i,a;return o.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.props.page,t.prev=1,n=(0,g.default)(r.getTextContent()),e.runningTask=n,t.next=6,n.promise;case 6:i=t.sent,a=i.items,e.setState({textItems:a},e.onLoadSuccess),t.next=14;break;case 11:t.prev=11,t.t0=t.catch(1),e.onLoadError(t.t0);case 14:case"end":return t.stop()}}),t,null,[[1,11]])})))),(0,p.default)((0,l.default)(e),"onLoadSuccess",(function(){var t=e.props.onGetTextSuccess,r=e.state.textItems;t&&t(r)})),(0,p.default)((0,l.default)(e),"onLoadError",(function(t){e.setState({textItems:!1}),(0,_.errorOnDev)(t);var r=e.props.onGetTextError;r&&r(t)})),e}return(0,c.default)(r,[{key:"componentDidMount",value:function(){if(!this.props.page)throw new Error("Attempted to load page text content, but no page was specified.");this.loadTextItems()}},{key:"componentDidUpdate",value:function(e){var t=this.props.page;e.page&&t!==e.page&&this.loadTextItems()}},{key:"componentWillUnmount",value:function(){(0,_.cancelRunningTask)(this.runningTask)}},{key:"renderTextItems",value:function(){var e=this.state.textItems;return e?e.map((function(e,t){return v.default.createElement(b.default,(0,a.default)({key:t,itemIndex:t},e))})):null}},{key:"render",value:function(){var e=this.unrotatedViewport,t=this.rotate;return v.default.createElement("div",{className:"react-pdf__Page__textContent",style:{position:"absolute",top:"50%",left:"50%",width:"".concat(e.width,"px"),height:"".concat(e.height,"px"),color:"transparent",transform:"translate(-50%, -50%) rotate(".concat(t,"deg)"),WebkitTransform:"translate(-50%, -50%) rotate(".concat(t,"deg)"),pointerEvents:"none"}},this.renderTextItems())}},{key:"unrotatedViewport",get:function(){var e=this.props,t=e.page,r=e.scale;return t.getViewport({scale:r})}},{key:"rotate",get:function(){var e=this.props,t=e.page;return e.rotate-t.rotate}}]),r}(v.PureComponent);t.TextLayerInternal=w,w.propTypes={onGetTextError:m.default.func,onGetTextSuccess:m.default.func,page:S.isPage.isRequired,rotate:S.isRotate,scale:m.default.number}},12475:function(e){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},13947:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.loadFromFile=t.isCancelException=t.makePageCallback=t.cancelRunningTask=t.displayCORSWarning=t.errorOnDev=t.warnOnDev=t.getPixelRatio=t.dataURItoUint8Array=t.isDataURI=t.isFile=t.isBlob=t.isArrayBuffer=t.isString=t.isProvided=t.isDefined=t.isProduction=t.isLocalFileSystem=t.isBrowser=void 0;var r="undefined"!==typeof window;t.isBrowser=r;var n=r&&"file:"===window.location.protocol;t.isLocalFileSystem=n;var i=!0;t.isProduction=i;var a=function(e){return"undefined"!==typeof e};t.isDefined=a;t.isProvided=function(e){return a(e)&&null!==e};var o=function(e){return"string"===typeof e};t.isString=o;t.isArrayBuffer=function(e){return e instanceof ArrayBuffer};t.isBlob=function(e){if(!r)throw new Error("Attempted to check if a variable is a Blob on a non-browser environment.");return e instanceof Blob};t.isFile=function(e){if(!r)throw new Error("Attempted to check if a variable is a File on a non-browser environment.");return e instanceof File};var s=function(e){return o(e)&&/^data:/.test(e)};t.isDataURI=s;t.dataURItoUint8Array=function(e){if(!s(e))throw new Error("dataURItoUint8Array was provided with an argument which is not a valid data URI.");var t;t=e.split(",")[0].indexOf("base64")>=0?atob(e.split(",")[1]):unescape(e.split(",")[1]);for(var r=new Uint8Array(t.length),n=0;n<t.length;n+=1)r[n]=t.charCodeAt(n);return r};t.getPixelRatio=function(){return r&&window.devicePixelRatio||1};var u=function(e){},c=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.apply(void 0,["warn"].concat(t))};t.warnOnDev=c;t.errorOnDev=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.apply(void 0,["error"].concat(t))};t.displayCORSWarning=function(){n&&c("Loading PDF as base64 strings/URLs might not work on protocols other than HTTP/HTTPS. On Google Chrome, you can use --allow-file-access-from-files flag for debugging purposes.")};t.cancelRunningTask=function(e){e&&e.cancel&&e.cancel()};t.makePageCallback=function(e,t){return Object.defineProperty(e,"width",{get:function(){return this.view[2]*t},configurable:!0}),Object.defineProperty(e,"height",{get:function(){return this.view[3]*t},configurable:!0}),Object.defineProperty(e,"originalWidth",{get:function(){return this.view[2]},configurable:!0}),Object.defineProperty(e,"originalHeight",{get:function(){return this.view[3]},configurable:!0}),e};t.isCancelException=function(e){return"RenderingCancelledException"===e.name};t.loadFromFile=function(e){return new Promise((function(t,r){var n=new FileReader;return n.onload=function(){return t(new Uint8Array(n.result))},n.onerror=function(e){switch(e.target.error.code){case e.target.error.NOT_FOUND_ERR:return r(new Error("Error while reading a file: File not found."));case e.target.error.NOT_READABLE_ERR:return r(new Error("Error while reading a file: File not readable."));case e.target.error.SECURITY_ERR:return r(new Error("Error while reading a file: Security error."));case e.target.error.ABORT_ERR:return r(new Error("Error while reading a file: Aborted."));default:return r(new Error("Error while reading a file."))}},n.readAsArrayBuffer(e),null}))}},14692:function(e){"use strict";function t(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,r,n,i){r=r||"&",n=n||"=";var a={};if("string"!==typeof e||0===e.length)return a;var o=/\+/g;e=e.split(r);var s=1e3;i&&"number"===typeof i.maxKeys&&(s=i.maxKeys);var u=e.length;s>0&&u>s&&(u=s);for(var c=0;c<u;++c){var l,f,d,h,p=e[c].replace(o,"%20"),v=p.indexOf(n);v>=0?(l=p.substr(0,v),f=p.substr(v+1)):(l=p,f=""),d=decodeURIComponent(l),h=decodeURIComponent(f),t(a,d)?Array.isArray(a[d])?a[d].push(h):a[d]=[a[d],h]:a[d]=h}return a}},15368:function(e){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},17122:function(e,t,r){var n=r(70079);e.exports=function(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},17383:function(e){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},20410:function(e,t,r){"use strict";function n(){return Array.prototype.slice.call(arguments).reduce((function(e,t){return e.concat(t)}),[]).filter((function(e){return"string"===typeof e})).join(" ")}r.r(t),r.d(t,{default:function(){return n}})},21346:function(e){"use strict";e.exports={isString:function(e){return"string"===typeof e},isObject:function(e){return"object"===typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},25371:function(e){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},28452:function(e,t,r){var n=r(73738).default,i=r(12475);e.exports=function(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return i(e)},e.exports.__esModule=!0,e.exports.default=e.exports},29293:function(e){function t(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,i)}e.exports=function(e){return function(){var r=this,n=arguments;return new Promise((function(i,a){var o=e.apply(r,n);function s(e){t(o,i,a,s,u,"next",e)}function u(e){t(o,i,a,s,u,"throw",e)}s(void 0)}))}},e.exports.__esModule=!0,e.exports.default=e.exports},29511:function(e,t,r){var n=r(95636);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},32424:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.OutlineInternal=void 0;var a=i(r(94634)),o=i(r(54756)),s=i(r(29293)),u=i(r(17383)),c=i(r(34579)),l=i(r(12475)),f=i(r(29511)),d=i(r(28452)),h=i(r(63072)),p=i(r(43693)),v=n(r(31014)),m=i(r(15662)),g=i(r(76219)),y=i(r(64608)),b=i(r(20410)),_=i(r(77998)),S=i(r(3357)),A=i(r(3219)),w=r(13947),k=r(8876);function x(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function P(e){return function(){var t,r=(0,h.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,h.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,d.default)(this,t)}}var R=function(e){(0,f.default)(r,e);var t=P(r);function r(){var e;(0,u.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,p.default)((0,l.default)(e),"state",{outline:null}),(0,p.default)((0,l.default)(e),"loadOutline",(0,s.default)(o.default.mark((function t(){var r,n,i;return o.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.props.pdf,e.setState((function(e){return e.outline?{outline:null}:null})),t.prev=2,n=(0,g.default)(r.getOutline()),e.runningTask=n,t.next=7,n.promise;case 7:i=t.sent,e.setState({outline:i},e.onLoadSuccess),t.next=14;break;case 11:t.prev=11,t.t0=t.catch(2),e.onLoadError(t.t0);case 14:case"end":return t.stop()}}),t,null,[[2,11]])})))),(0,p.default)((0,l.default)(e),"onLoadSuccess",(function(){var t=e.props.onLoadSuccess,r=e.state.outline;t&&t(r)})),(0,p.default)((0,l.default)(e),"onLoadError",(function(t){e.setState({outline:!1}),(0,w.errorOnDev)(t);var r=e.props.onLoadError;r&&r(t)})),(0,p.default)((0,l.default)(e),"onItemClick",(function(t){var r=t.pageIndex,n=t.pageNumber,i=e.props.onItemClick;i&&i({pageIndex:r,pageNumber:n})})),e}return(0,c.default)(r,[{key:"componentDidMount",value:function(){if(!this.props.pdf)throw new Error("Attempted to load an outline, but no document was specified.");this.loadOutline()}},{key:"componentDidUpdate",value:function(e){var t=this.props.pdf;e.pdf&&t!==e.pdf&&this.loadOutline()}},{key:"componentWillUnmount",value:function(){(0,w.cancelRunningTask)(this.runningTask)}},{key:"renderOutline",value:function(){var e=this.state.outline;return v.default.createElement("ul",null,e.map((function(e,t){return v.default.createElement(A.default,{key:"string"===typeof e.destination?e.destination:t,item:e})})))}},{key:"render",value:function(){var e=this.props.pdf,t=this.state.outline;if(!e||!t)return null;var r=this.props,n=r.className,i=r.inputRef;return v.default.createElement("div",(0,a.default)({className:(0,b.default)("react-pdf__Outline",n),ref:i},this.eventProps),v.default.createElement(S.default.Provider,{value:this.childContext},this.renderOutline()))}},{key:"childContext",get:function(){return{onClick:this.onItemClick}}},{key:"eventProps",get:function(){var e=this;return(0,y.default)(this.props,(function(){return e.state.outline}))}}]),r}(v.PureComponent);t.OutlineInternal=R,R.propTypes=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?x(Object(r),!0).forEach((function(t){(0,p.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({className:k.isClassName,inputRef:m.default.func,onItemClick:m.default.func,onLoadError:m.default.func,onLoadSuccess:m.default.func,pdf:k.isPdf},k.eventProps);var C=v.default.forwardRef((function(e,t){return v.default.createElement(_.default.Consumer,null,(function(r){return v.default.createElement(R,(0,a.default)({ref:t},r,e))}))}));t.default=C},32569:function(e,t,r){"use strict";var n=r(67096),i=r(21346);function a(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}t.parse=b,t.resolve=function(e,t){return b(e,!1,!0).resolve(t)},t.resolveObject=function(e,t){return e?b(e,!1,!0).resolveObject(t):t},t.format=function(e){i.isString(e)&&(e=b(e));return e instanceof a?e.format():a.prototype.format.call(e)},t.Url=a;var o=/^([a-z0-9.+-]+:)/i,s=/:[0-9]*$/,u=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,c=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),l=["'"].concat(c),f=["%","/","?",";","#"].concat(l),d=["/","?","#"],h=/^[+a-z0-9A-Z_-]{0,63}$/,p=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,v={javascript:!0,"javascript:":!0},m={javascript:!0,"javascript:":!0},g={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},y=r(3344);function b(e,t,r){if(e&&i.isObject(e)&&e instanceof a)return e;var n=new a;return n.parse(e,t,r),n}a.prototype.parse=function(e,t,r){if(!i.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var a=e.indexOf("?"),s=-1!==a&&a<e.indexOf("#")?"?":"#",c=e.split(s);c[0]=c[0].replace(/\\/g,"/");var b=e=c.join(s);if(b=b.trim(),!r&&1===e.split("#").length){var _=u.exec(b);if(_)return this.path=b,this.href=b,this.pathname=_[1],_[2]?(this.search=_[2],this.query=t?y.parse(this.search.substr(1)):this.search.substr(1)):t&&(this.search="",this.query={}),this}var S=o.exec(b);if(S){var A=(S=S[0]).toLowerCase();this.protocol=A,b=b.substr(S.length)}if(r||S||b.match(/^\/\/[^@\/]+@[^@\/]+/)){var w="//"===b.substr(0,2);!w||S&&m[S]||(b=b.substr(2),this.slashes=!0)}if(!m[S]&&(w||S&&!g[S])){for(var k,x,P=-1,R=0;R<d.length;R++){-1!==(C=b.indexOf(d[R]))&&(-1===P||C<P)&&(P=C)}-1!==(x=-1===P?b.lastIndexOf("@"):b.lastIndexOf("@",P))&&(k=b.slice(0,x),b=b.slice(x+1),this.auth=decodeURIComponent(k)),P=-1;for(R=0;R<f.length;R++){var C;-1!==(C=b.indexOf(f[R]))&&(-1===P||C<P)&&(P=C)}-1===P&&(P=b.length),this.host=b.slice(0,P),b=b.slice(P),this.parseHost(),this.hostname=this.hostname||"";var E="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!E)for(var T=this.hostname.split(/\./),O=(R=0,T.length);R<O;R++){var L=T[R];if(L&&!L.match(h)){for(var I="",F=0,D=L.length;F<D;F++)L.charCodeAt(F)>127?I+="x":I+=L[F];if(!I.match(h)){var j=T.slice(0,R),M=T.slice(R+1),N=L.match(p);N&&(j.push(N[1]),M.unshift(N[2])),M.length&&(b="/"+M.join(".")+b),this.hostname=j.join(".");break}}}this.hostname.length>255?this.hostname="":this.hostname=this.hostname.toLowerCase(),E||(this.hostname=n.toASCII(this.hostname));var q=this.port?":"+this.port:"",W=this.hostname||"";this.host=W+q,this.href+=this.host,E&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==b[0]&&(b="/"+b))}if(!v[A])for(R=0,O=l.length;R<O;R++){var U=l[R];if(-1!==b.indexOf(U)){var B=encodeURIComponent(U);B===U&&(B=escape(U)),b=b.split(U).join(B)}}var G=b.indexOf("#");-1!==G&&(this.hash=b.substr(G),b=b.slice(0,G));var z=b.indexOf("?");if(-1!==z?(this.search=b.substr(z),this.query=b.substr(z+1),t&&(this.query=y.parse(this.query)),b=b.slice(0,z)):t&&(this.search="",this.query={}),b&&(this.pathname=b),g[A]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){q=this.pathname||"";var H=this.search||"";this.path=q+H}return this.href=this.format(),this},a.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var t=this.protocol||"",r=this.pathname||"",n=this.hash||"",a=!1,o="";this.host?a=e+this.host:this.hostname&&(a=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(a+=":"+this.port)),this.query&&i.isObject(this.query)&&Object.keys(this.query).length&&(o=y.stringify(this.query));var s=this.search||o&&"?"+o||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||g[t])&&!1!==a?(a="//"+(a||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):a||(a=""),n&&"#"!==n.charAt(0)&&(n="#"+n),s&&"?"!==s.charAt(0)&&(s="?"+s),t+a+(r=r.replace(/[?#]/g,(function(e){return encodeURIComponent(e)})))+(s=s.replace("#","%23"))+n},a.prototype.resolve=function(e){return this.resolveObject(b(e,!1,!0)).format()},a.prototype.resolveObject=function(e){if(i.isString(e)){var t=new a;t.parse(e,!1,!0),e=t}for(var r=new a,n=Object.keys(this),o=0;o<n.length;o++){var s=n[o];r[s]=this[s]}if(r.hash=e.hash,""===e.href)return r.href=r.format(),r;if(e.slashes&&!e.protocol){for(var u=Object.keys(e),c=0;c<u.length;c++){var l=u[c];"protocol"!==l&&(r[l]=e[l])}return g[r.protocol]&&r.hostname&&!r.pathname&&(r.path=r.pathname="/"),r.href=r.format(),r}if(e.protocol&&e.protocol!==r.protocol){if(!g[e.protocol]){for(var f=Object.keys(e),d=0;d<f.length;d++){var h=f[d];r[h]=e[h]}return r.href=r.format(),r}if(r.protocol=e.protocol,e.host||m[e.protocol])r.pathname=e.pathname;else{for(var p=(e.pathname||"").split("/");p.length&&!(e.host=p.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==p[0]&&p.unshift(""),p.length<2&&p.unshift(""),r.pathname=p.join("/")}if(r.search=e.search,r.query=e.query,r.host=e.host||"",r.auth=e.auth,r.hostname=e.hostname||e.host,r.port=e.port,r.pathname||r.search){var v=r.pathname||"",y=r.search||"";r.path=v+y}return r.slashes=r.slashes||e.slashes,r.href=r.format(),r}var b=r.pathname&&"/"===r.pathname.charAt(0),_=e.host||e.pathname&&"/"===e.pathname.charAt(0),S=_||b||r.host&&e.pathname,A=S,w=r.pathname&&r.pathname.split("/")||[],k=(p=e.pathname&&e.pathname.split("/")||[],r.protocol&&!g[r.protocol]);if(k&&(r.hostname="",r.port=null,r.host&&(""===w[0]?w[0]=r.host:w.unshift(r.host)),r.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===p[0]?p[0]=e.host:p.unshift(e.host)),e.host=null),S=S&&(""===p[0]||""===w[0])),_)r.host=e.host||""===e.host?e.host:r.host,r.hostname=e.hostname||""===e.hostname?e.hostname:r.hostname,r.search=e.search,r.query=e.query,w=p;else if(p.length)w||(w=[]),w.pop(),w=w.concat(p),r.search=e.search,r.query=e.query;else if(!i.isNullOrUndefined(e.search)){if(k)r.hostname=r.host=w.shift(),(E=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=E.shift(),r.host=r.hostname=E.shift());return r.search=e.search,r.query=e.query,i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!w.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var x=w.slice(-1)[0],P=(r.host||e.host||w.length>1)&&("."===x||".."===x)||""===x,R=0,C=w.length;C>=0;C--)"."===(x=w[C])?w.splice(C,1):".."===x?(w.splice(C,1),R++):R&&(w.splice(C,1),R--);if(!S&&!A)for(;R--;R)w.unshift("..");!S||""===w[0]||w[0]&&"/"===w[0].charAt(0)||w.unshift(""),P&&"/"!==w.join("/").substr(-1)&&w.push("");var E,T=""===w[0]||w[0]&&"/"===w[0].charAt(0);k&&(r.hostname=r.host=T?"":w.length?w.shift():"",(E=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@"))&&(r.auth=E.shift(),r.host=r.hostname=E.shift()));return(S=S||r.host&&w.length)&&!T&&w.unshift(""),w.length?r.pathname=w.join("/"):(r.pathname=null,r.path=null),i.isNull(r.pathname)&&i.isNull(r.search)||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=e.auth||r.auth,r.slashes=r.slashes||e.slashes,r.href=r.format(),r},a.prototype.parseHost=function(){var e=this.host,t=s.exec(e);t&&(":"!==(t=t[0])&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},34579:function(e,t,r){var n=r(77736);function i(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}e.exports=function(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},35317:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i(r(94634)),o=i(r(91847)),s=i(r(73738)),u=i(r(54756)),c=i(r(29293)),l=i(r(17383)),f=i(r(34579)),d=i(r(12475)),h=i(r(29511)),p=i(r(28452)),v=i(r(63072)),m=i(r(43693)),g=n(r(31014)),y=i(r(15662)),b=i(r(64608)),_=i(r(76219)),S=i(r(20410)),A=n(r(3170)),w=i(r(77998)),k=i(r(46129)),x=i(r(41489)),P=i(r(96425)),R=r(13947),C=r(8876);function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach((function(t){(0,m.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function O(e){return function(){var t,r=(0,v.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,v.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,p.default)(this,t)}}var L=function(e){(0,h.default)(r,e);var t=O(r);function r(){var e;(0,l.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,m.default)((0,d.default)(e),"state",{pdf:null}),(0,m.default)((0,d.default)(e),"viewer",{scrollPageIntoView:function(t){var r=t.pageNumber,n=e.props.onItemClick;if(n)n({pageNumber:r});else{var i=e.pages[r-1];i?i.scrollIntoView():(0,R.warnOnDev)("Warning: An internal link leading to page ".concat(r," was clicked, but neither <Document> was provided with onItemClick nor it was able to find the page within itself. Either provide onItemClick to <Document> and handle navigating by yourself or ensure that all pages are rendered within <Document>."))}}}),(0,m.default)((0,d.default)(e),"linkService",new x.default),(0,m.default)((0,d.default)(e),"loadDocument",(0,c.default)(u.default.mark((function t(){var r,n,i,a,o,s,c;return u.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=null,t.prev=1,t.next=4,e.findDocumentSource();case 4:r=t.sent,e.onSourceSuccess(),t.next=11;break;case 8:t.prev=8,t.t0=t.catch(1),e.onSourceError(t.t0);case 11:if(r){t.next=13;break}return t.abrupt("return");case 13:return e.setState((function(e){return e.pdf?{pdf:null}:null})),n=e.props,i=n.options,a=n.onLoadProgress,o=n.onPassword,t.prev=15,(0,R.cancelRunningTask)(e.runningTask),e.loadingTask=A.default.getDocument(T({},r,{},i)),e.loadingTask.onPassword=o,a&&(e.loadingTask.onProgress=a),s=(0,_.default)(e.loadingTask.promise),e.runningTask=s,t.next=24,s.promise;case 24:c=t.sent,e.setState((function(e){return e.pdf&&e.pdf.fingerprint===c.fingerprint?null:{pdf:c}}),e.onLoadSuccess),t.next=31;break;case 28:t.prev=28,t.t1=t.catch(15),e.onLoadError(t.t1);case 31:case"end":return t.stop()}}),t,null,[[1,8],[15,28]])})))),(0,m.default)((0,d.default)(e),"setupLinkService",(function(){e.linkService.setViewer(e.viewer);var t=(0,d.default)(e);Object.defineProperty(e.linkService,"externalLinkTarget",{get:function(){switch(t.props.externalLinkTarget){case"_self":return 1;case"_blank":return 2;case"_parent":return 3;case"_top":return 4;default:return 0}}})})),(0,m.default)((0,d.default)(e),"onSourceSuccess",(function(){var t=e.props.onSourceSuccess;t&&t()})),(0,m.default)((0,d.default)(e),"onSourceError",(function(t){(0,R.errorOnDev)(t);var r=e.props.onSourceError;r&&r(t)})),(0,m.default)((0,d.default)(e),"onLoadSuccess",(function(){var t=e.props.onLoadSuccess,r=e.state.pdf;t&&t(r),e.pages=new Array(r.numPages),e.linkService.setDocument(r)})),(0,m.default)((0,d.default)(e),"onLoadError",(function(t){e.setState({pdf:!1}),(0,R.errorOnDev)(t);var r=e.props.onLoadError;r&&r(t)})),(0,m.default)((0,d.default)(e),"findDocumentSource",(0,c.default)(u.default.mark((function t(){var r,n,i,a,c;return u.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.props.file){t.next=3;break}return t.abrupt("return",null);case 3:if("string"!==typeof r){t.next=9;break}if(!(0,R.isDataURI)(r)){t.next=7;break}return n=(0,R.dataURItoUint8Array)(r),t.abrupt("return",{data:n});case 7:return(0,R.displayCORSWarning)(),t.abrupt("return",{url:r});case 9:if(!(r instanceof A.PDFDataRangeTransport)){t.next=11;break}return t.abrupt("return",{range:r});case 11:if(!(0,R.isArrayBuffer)(r)){t.next=13;break}return t.abrupt("return",{data:r});case 13:if(!R.isBrowser){t.next=19;break}if(!(0,R.isBlob)(r)&&!(0,R.isFile)(r)){t.next=19;break}return t.next=17,(0,R.loadFromFile)(r);case 17:return t.t0=t.sent,t.abrupt("return",{data:t.t0});case 19:if("object"===(0,s.default)(r)){t.next=21;break}throw new Error("Invalid parameter in file, need either Uint8Array, string or a parameter object");case 21:if(r.url||r.data||r.range){t.next=23;break}throw new Error("Invalid parameter object: need either .data, .range or .url");case 23:if("string"!==typeof r.url){t.next=29;break}if(!(0,R.isDataURI)(r.url)){t.next=28;break}return i=r.url,a=(0,o.default)(r,["url"]),c=(0,R.dataURItoUint8Array)(i),t.abrupt("return",T({data:c},a));case 28:(0,R.displayCORSWarning)();case 29:return t.abrupt("return",r);case 30:case"end":return t.stop()}}),t)})))),(0,m.default)((0,d.default)(e),"registerPage",(function(t,r){e.pages[t]=r})),(0,m.default)((0,d.default)(e),"unregisterPage",(function(t){delete e.pages[t]})),e}return(0,f.default)(r,[{key:"componentDidMount",value:function(){this.loadDocument(),this.setupLinkService()}},{key:"componentDidUpdate",value:function(e){this.props.file!==e.file&&this.loadDocument()}},{key:"componentWillUnmount",value:function(){this.loadingTask&&this.loadingTask.destroy(),(0,R.cancelRunningTask)(this.runningTask)}},{key:"renderChildren",value:function(){var e=this.props.children;return g.default.createElement(w.default.Provider,{value:this.childContext},e)}},{key:"renderContent",value:function(){var e=this.props.file,t=this.state.pdf;if(!e){var r=this.props.noData;return g.default.createElement(k.default,{type:"no-data"},"function"===typeof r?r():r)}if(null===t){var n=this.props.loading;return g.default.createElement(k.default,{type:"loading"},"function"===typeof n?n():n)}if(!1===t){var i=this.props.error;return g.default.createElement(k.default,{type:"error"},"function"===typeof i?i():i)}return this.renderChildren()}},{key:"render",value:function(){var e=this.props,t=e.className,r=e.inputRef;return g.default.createElement("div",(0,a.default)({className:(0,S.default)("react-pdf__Document",t),ref:r},this.eventProps),this.renderContent())}},{key:"childContext",get:function(){var e=this.linkService,t=this.registerPage,r=this.unregisterPage,n=this.props,i=n.renderMode,a=n.rotate;return{linkService:e,pdf:this.state.pdf,registerPage:t,renderMode:i,rotate:a,unregisterPage:r}}},{key:"eventProps",get:function(){var e=this;return(0,b.default)(this.props,(function(){return e.state.pdf}))}}]),r}(g.PureComponent);t.default=L,L.defaultProps={error:"Failed to load PDF file.",loading:"Loading PDF\u2026",noData:"No PDF file specified.",onPassword:function(e,t){switch(t){case P.default.NEED_PASSWORD:e(prompt("Enter the password to open this PDF file."));break;case P.default.INCORRECT_PASSWORD:e(prompt("Invalid password. Please try again."))}}};var I=y.default.oneOfType([y.default.func,y.default.node]);L.propTypes=T({},C.eventProps,{children:y.default.node,className:C.isClassName,error:I,file:R.isFile,inputRef:y.default.func,loading:I,noData:I,onItemClick:y.default.func,onLoadError:y.default.func,onLoadProgress:y.default.func,onLoadSuccess:y.default.func,onPassword:y.default.func,onSourceError:y.default.func,onSourceSuccess:y.default.func,rotate:y.default.number})},37563:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleLinkService=t.PDFLinkService=void 0;var n=r(49120);function i(e){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}var u=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.eventBus,i=t.externalLinkTarget,o=void 0===i?null:i,s=t.externalLinkRel,u=void 0===s?null:s;a(this,e),this.eventBus=r||(0,n.getGlobalEventBus)(),this.externalLinkTarget=o,this.externalLinkRel=u,this.baseUrl=null,this.pdfDocument=null,this.pdfViewer=null,this.pdfHistory=null,this._pagesRefCache=null}return s(e,[{key:"setDocument",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.baseUrl=t,this.pdfDocument=e,this._pagesRefCache=Object.create(null)}},{key:"setViewer",value:function(e){this.pdfViewer=e}},{key:"setHistory",value:function(e){this.pdfHistory=e}},{key:"navigateTo",value:function(e){var t=this,r=function r(n){var i,a=n.namedDest,o=n.explicitDest,s=o[0];if(s instanceof Object){if(null===(i=t._cachedPageNumber(s)))return void t.pdfDocument.getPageIndex(s).then((function(e){t.cachePageRef(e+1,s),r({namedDest:a,explicitDest:o})})).catch((function(){console.error('PDFLinkService.navigateTo: "'.concat(s,'" is not ')+'a valid page reference, for dest="'.concat(e,'".'))}))}else{if(!Number.isInteger(s))return void console.error('PDFLinkService.navigateTo: "'.concat(s,'" is not ')+'a valid destination reference, for dest="'.concat(e,'".'));i=s+1}!i||i<1||i>t.pagesCount?console.error('PDFLinkService.navigateTo: "'.concat(i,'" is not ')+'a valid page number, for dest="'.concat(e,'".')):(t.pdfHistory&&(t.pdfHistory.pushCurrentPosition(),t.pdfHistory.push({namedDest:a,explicitDest:o,pageNumber:i})),t.pdfViewer.scrollPageIntoView({pageNumber:i,destArray:o}))};new Promise((function(r,n){"string"!==typeof e?r({namedDest:"",explicitDest:e}):t.pdfDocument.getDestination(e).then((function(t){r({namedDest:e,explicitDest:t})}))})).then((function(t){Array.isArray(t.explicitDest)?r(t):console.error('PDFLinkService.navigateTo: "'.concat(t.explicitDest,'" is')+' not a valid destination array, for dest="'.concat(e,'".'))}))}},{key:"getDestinationHash",value:function(e){if("string"===typeof e)return this.getAnchorUrl("#"+escape(e));if(Array.isArray(e)){var t=JSON.stringify(e);return this.getAnchorUrl("#"+escape(t))}return this.getAnchorUrl("")}},{key:"getAnchorUrl",value:function(e){return(this.baseUrl||"")+e}},{key:"setHash",value:function(e){var t,r;if(e.includes("=")){var a=(0,n.parseQueryString)(e);if("search"in a&&this.eventBus.dispatch("findfromurlhash",{source:this,query:a.search.replace(/"/g,""),phraseSearch:"true"===a.phrase}),"nameddest"in a)return void this.navigateTo(a.nameddest);if("page"in a&&(t=0|a.page||1),"zoom"in a){var o=a.zoom.split(","),s=o[0],u=parseFloat(s);s.includes("Fit")?"Fit"===s||"FitB"===s?r=[null,{name:s}]:"FitH"===s||"FitBH"===s||"FitV"===s||"FitBV"===s?r=[null,{name:s},o.length>1?0|o[1]:null]:"FitR"===s?5!==o.length?console.error('PDFLinkService.setHash: Not enough parameters for "FitR".'):r=[null,{name:s},0|o[1],0|o[2],0|o[3],0|o[4]]:console.error('PDFLinkService.setHash: "'.concat(s,'" is not ')+"a valid zoom value."):r=[null,{name:"XYZ"},o.length>1?0|o[1]:null,o.length>2?0|o[2]:null,u?u/100:s]}r?this.pdfViewer.scrollPageIntoView({pageNumber:t||this.page,destArray:r,allowNegativeOffset:!0}):t&&(this.page=t),"pagemode"in a&&this.eventBus.dispatch("pagemode",{source:this,mode:a.pagemode})}else{r=unescape(e);try{r=JSON.parse(r),Array.isArray(r)||(r=r.toString())}catch(c){}if("string"===typeof r||function(e){if(!Array.isArray(e))return!1;var t=e.length,r=!0;if(t<2)return!1;var n=e[0];if(("object"!==i(n)||!Number.isInteger(n.num)||!Number.isInteger(n.gen))&&!(Number.isInteger(n)&&n>=0))return!1;var a=e[1];if("object"!==i(a)||"string"!==typeof a.name)return!1;switch(a.name){case"XYZ":if(5!==t)return!1;break;case"Fit":case"FitB":return 2===t;case"FitH":case"FitBH":case"FitV":case"FitBV":if(3!==t)return!1;break;case"FitR":if(6!==t)return!1;r=!1;break;default:return!1}for(var o=2;o<t;o++){var s=e[o];if(!("number"===typeof s||r&&null===s))return!1}return!0}(r))return void this.navigateTo(r);console.error('PDFLinkService.setHash: "'.concat(unescape(e),'" is not ')+"a valid destination.")}}},{key:"executeNamedAction",value:function(e){switch(e){case"GoBack":this.pdfHistory&&this.pdfHistory.back();break;case"GoForward":this.pdfHistory&&this.pdfHistory.forward();break;case"NextPage":this.page<this.pagesCount&&this.page++;break;case"PrevPage":this.page>1&&this.page--;break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1}this.eventBus.dispatch("namedaction",{source:this,action:e})}},{key:"cachePageRef",value:function(e,t){if(t){var r=t.num+" "+t.gen+" R";this._pagesRefCache[r]=e}}},{key:"_cachedPageNumber",value:function(e){var t=e.num+" "+e.gen+" R";return this._pagesRefCache&&this._pagesRefCache[t]||null}},{key:"isPageVisible",value:function(e){return this.pdfViewer.isPageVisible(e)}},{key:"pagesCount",get:function(){return this.pdfDocument?this.pdfDocument.numPages:0}},{key:"page",get:function(){return this.pdfViewer.currentPageNumber},set:function(e){this.pdfViewer.currentPageNumber=e}},{key:"rotation",get:function(){return this.pdfViewer.pagesRotation},set:function(e){this.pdfViewer.pagesRotation=e}}]),e}();t.PDFLinkService=u;var c=function(){function e(){a(this,e),this.externalLinkTarget=null,this.externalLinkRel=null}return s(e,[{key:"navigateTo",value:function(e){}},{key:"getDestinationHash",value:function(e){return"#"}},{key:"getAnchorUrl",value:function(e){return"#"}},{key:"setHash",value:function(e){}},{key:"executeNamedAction",value:function(e){}},{key:"cachePageRef",value:function(e,t){}},{key:"isPageVisible",value:function(e){return!0}},{key:"pagesCount",get:function(){return 0}},{key:"page",get:function(){return 0},set:function(e){}},{key:"rotation",get:function(){return 0},set:function(e){}}]),e}();t.SimpleLinkService=c},39569:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return h.default.createElement(v.default.Consumer,null,(function(t){return h.default.createElement(b,(0,a.default)({},t,e))}))},t.PageCanvasInternal=void 0;var a=i(r(94634)),o=i(r(17383)),s=i(r(34579)),u=i(r(12475)),c=i(r(29511)),l=i(r(28452)),f=i(r(63072)),d=i(r(43693)),h=n(r(31014)),p=i(r(15662)),v=i(r(75684)),m=r(13947),g=r(8876);function y(e){return function(){var t,r=(0,f.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,f.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,l.default)(this,t)}}var b=function(e){(0,c.default)(r,e);var t=y(r);function r(){var e;(0,o.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,d.default)((0,u.default)(e),"onRenderSuccess",(function(){e.renderer=null;var t=e.props,r=t.onRenderSuccess,n=t.page,i=t.scale;r&&r((0,m.makePageCallback)(n,i))})),(0,d.default)((0,u.default)(e),"onRenderError",(function(t){if(!(0,m.isCancelException)(t)){(0,m.errorOnDev)(t);var r=e.props.onRenderError;r&&r(t)}})),(0,d.default)((0,u.default)(e),"drawPageOnCanvas",(function(){var t=(0,u.default)(e).canvasLayer;if(!t)return null;var r=(0,u.default)(e),n=r.renderViewport,i=r.viewport,a=e.props,o=a.page,s=a.renderInteractiveForms;t.width=n.width,t.height=n.height,t.style.width="".concat(Math.floor(i.width),"px"),t.style.height="".concat(Math.floor(i.height),"px");var c={get canvasContext(){return t.getContext("2d")},viewport:n,renderInteractiveForms:s};return e.cancelRenderingTask(),e.renderer=o.render(c),e.renderer.promise.then(e.onRenderSuccess).catch(e.onRenderError)})),e}return(0,s.default)(r,[{key:"componentDidMount",value:function(){this.drawPageOnCanvas()}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.page;t.renderInteractiveForms!==e.renderInteractiveForms&&(r.cleanup(),this.drawPageOnCanvas())}},{key:"componentWillUnmount",value:function(){this.cancelRenderingTask(),this.canvasLayer&&(this.canvasLayer.width=0,this.canvasLayer.height=0,this.canvasLayer=null)}},{key:"cancelRenderingTask",value:function(){this.renderer&&this.renderer._internalRenderTask.running&&this.renderer._internalRenderTask.cancel()}},{key:"render",value:function(){var e=this;return h.default.createElement("canvas",{className:"react-pdf__Page__canvas",dir:"ltr",ref:function(t){e.canvasLayer=t},style:{display:"block",userSelect:"none"}})}},{key:"renderViewport",get:function(){var e=this.props,t=e.page,r=e.rotate,n=e.scale,i=(0,m.getPixelRatio)();return t.getViewport({scale:n*i,rotation:r})}},{key:"viewport",get:function(){var e=this.props,t=e.page,r=e.rotate,n=e.scale;return t.getViewport({scale:n,rotation:r})}}]),r}(h.PureComponent);t.PageCanvasInternal=b,b.propTypes={onRenderError:p.default.func,onRenderSuccess:p.default.func,page:g.isPage.isRequired,renderInteractiveForms:p.default.bool,rotate:g.isRotate,scale:p.default.number}},41132:function(e,t,r){var n=r(5901),i=r(99291),a=r(17122),o=r(41869);e.exports=function(e){return n(e)||i(e)||a(e)||o()},e.exports.__esModule=!0,e.exports.default=e.exports},41489:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(37563).PDFLinkService;t.default=n},41869:function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},43693:function(e,t,r){var n=r(77736);e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},46129:function(e,t,r){"use strict";var n=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=o;var i=n(r(31014)),a=n(r(15662));function o(e){var t=e.children,r=e.type;return i.default.createElement("div",{className:"react-pdf__message react-pdf__message--".concat(r)},t)}o.propTypes={children:a.default.node,type:a.default.oneOf(["error","loading","no-data"]).isRequired}},49120:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isValidRotation=function(e){return Number.isInteger(e)&&e%90===0},t.isValidScrollMode=function(e){return Number.isInteger(e)&&Object.values(f).includes(e)&&e!==f.UNKNOWN},t.isValidSpreadMode=function(e){return Number.isInteger(e)&&Object.values(d).includes(e)&&e!==d.UNKNOWN},t.isPortraitOrientation=function(e){return e.width<=e.height},t.getGlobalEventBus=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];_||(_=new b({dispatchToDOM:e}));return _},t.getPDFFileNameFromURL=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"document.pdf";if("string"!==typeof e)return t;if(function(e){var t=0,r=e.length;for(;t<r&&""===e[t].trim();)t++;return"data:"===e.substring(t,t+5).toLowerCase()}(e))return console.warn('getPDFFileNameFromURL: ignoring "data:" URL for performance reasons.'),t;var r=/[^\/?#=]+\.pdf\b(?!.*\.pdf\b)/i,n=/^(?:(?:[^:]+:)?\/\/[^\/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(e),i=r.exec(n[1])||r.exec(n[2])||r.exec(n[3]);if(i&&(i=i[0]).includes("%"))try{i=r.exec(decodeURIComponent(i))[0]}catch(a){}return i||t},t.noContextMenuHandler=function(e){e.preventDefault()},t.parseQueryString=function(e){for(var t=e.split("&"),r=Object.create(null),n=0,i=t.length;n<i;++n){var a=t[n].split("="),o=a[0].toLowerCase(),s=a.length>1?a[1]:null;r[decodeURIComponent(o)]=decodeURIComponent(s)}return r},t.backtrackBeforeAllVisibleElements=m,t.getVisibleElements=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=e.scrollTop,a=i+e.clientHeight,o=e.scrollLeft,s=o+e.clientWidth;var u=[],c=t.length,l=0===c?0:v(t,n?function(e){var t=e.div;return t.offsetLeft+t.clientLeft+t.clientWidth>o}:function(e){var t=e.div;return t.offsetTop+t.clientTop+t.clientHeight>i});l>0&&l<c&&!n&&(l=m(l,t,i));for(var f=n?s:-1,d=l;d<c;d++){var h=t[d],p=h.div,g=p.offsetLeft+p.clientLeft,y=p.offsetTop+p.clientTop,b=p.clientWidth,_=p.clientHeight,S=g+b,A=y+_;if(-1===f)A>=a&&(f=A);else if((n?g:y)>f)break;if(!(A<=i||y>=a||S<=o||g>=s)){var w=(_-(Math.max(0,i-y)+Math.max(0,A-a)))*(b-(Math.max(0,o-g)+Math.max(0,S-s)))*100/_/b|0;u.push({id:h.id,x:g,y:y,view:h,percent:w})}}var k=u[0],x=u[u.length-1];r&&u.sort((function(e,t){var r=e.percent-t.percent;return Math.abs(r)>.001?-r:e.id-t.id}));return{first:k,last:x,views:u}},t.roundToDivide=function(e,t){var r=e%t;return 0===r?e:Math.round(e-r+t)},t.getPageSizeInches=function(e){var t=e.view,r=e.userUnit,n=e.rotate,i=(d=t,h=4,function(e){if(Array.isArray(e))return e}(d)||function(e,t){var r=[],n=!0,i=!1,a=void 0;try{for(var o,s=e[Symbol.iterator]();!(n=(o=s.next()).done)&&(r.push(o.value),!t||r.length!==t);n=!0);}catch(u){i=!0,a=u}finally{try{n||null==s.return||s.return()}finally{if(i)throw a}}return r}(d,h)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()),a=i[0],o=i[1],s=i[2],u=i[3],c=n%180!==0,l=(s-a)/72*r,f=(u-o)/72*r;var d,h;return{width:c?f:l,height:c?l:f}},t.approximateFraction=function(e){if(Math.floor(e)===e)return[e,1];var t=1/e;if(t>8)return[1,8];if(Math.floor(t)===t)return[1,t];var r,n=e>1?t:e,i=0,a=1,o=1,s=1;for(;;){var u=i+o,c=a+s;if(c>8)break;n<=u/c?(o=u,s=c):(i=u,a=c)}r=n-i/a<o/s-n?n===e?[i,a]:[a,i]:n===e?[o,s]:[s,o];return r},t.getOutputScale=function(e){var t=window.devicePixelRatio||1,r=e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1,n=t/r;return{sx:n,sy:n,scaled:1!==n}},t.scrollIntoView=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e.offsetParent;if(!n)return void console.error("offsetParent is not set -- cannot scroll");var i=e.offsetTop+e.clientTop,a=e.offsetLeft+e.clientLeft;for(;n.clientHeight===n.scrollHeight&&n.clientWidth===n.scrollWidth||r&&"hidden"===getComputedStyle(n).overflow;)if(n.dataset._scaleY&&(i/=n.dataset._scaleY,a/=n.dataset._scaleX),i+=n.offsetTop,a+=n.offsetLeft,!(n=n.offsetParent))return;t&&(void 0!==t.top&&(i+=t.top),void 0!==t.left&&(a+=t.left,n.scrollLeft=a));n.scrollTop=i},t.watchScroll=function(e,t){var r=function(r){i||(i=window.requestAnimationFrame((function(){i=null;var r=e.scrollLeft,a=n.lastX;r!==a&&(n.right=r>a),n.lastX=r;var o=e.scrollTop,s=n.lastY;o!==s&&(n.down=o>s),n.lastY=o,t(n)})))},n={right:!0,down:!0,lastX:e.scrollLeft,lastY:e.scrollTop,_eventHandler:r},i=null;return e.addEventListener("scroll",r,!0),n},t.binarySearchFirstItem=v,t.normalizeWheelEventDelta=function(e){var t=Math.sqrt(e.deltaX*e.deltaX+e.deltaY*e.deltaY),r=Math.atan2(e.deltaY,e.deltaX);-.25*Math.PI<r&&r<.75*Math.PI&&(t=-t);0===e.deltaMode?t/=900:1===e.deltaMode&&(t/=30);return t},t.waitOnEventOrTimeout=function(e){var t=e.target,r=e.name,n=e.delay,i=void 0===n?0:n;return new Promise((function(e,n){if("object"!==u(t)||!r||"string"!==typeof r||!(Number.isInteger(i)&&i>=0))throw new Error("waitOnEventOrTimeout - invalid parameters.");function a(n){t instanceof b?t.off(r,o):t.removeEventListener(r,o),c&&clearTimeout(c),e(n)}var o=a.bind(null,g.EVENT);t instanceof b?t.on(r,o):t.addEventListener(r,o);var s=a.bind(null,g.TIMEOUT),c=setTimeout(s,i)}))},t.moveToEndOfArray=function(e,t){for(var r=[],n=e.length,i=0,a=0;a<n;++a)t(e[a])?r.push(e[a]):(e[i]=e[a],++i);for(var o=0;i<n;++o,++i)e[i]=r[o]},t.WaitOnType=t.animationStarted=t.ProgressBar=t.EventBus=t.NullL10n=t.SpreadMode=t.ScrollMode=t.TextLayerMode=t.RendererType=t.PresentationModeState=t.VERTICAL_PADDING=t.SCROLLBAR_PADDING=t.MAX_AUTO_SCALE=t.UNKNOWN_SCALE=t.MAX_SCALE=t.MIN_SCALE=t.DEFAULT_SCALE=t.DEFAULT_SCALE_VALUE=t.CSS_UNITS=void 0;var n,i=(n=r(54756))&&n.__esModule?n:{default:n};function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function s(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),e}function u(e){return u="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function c(e,t,r,n,i,a,o){try{var s=e[a](o),u=s.value}catch(c){return void r(c)}s.done?t(u):Promise.resolve(u).then(n,i)}function l(e){return function(){var t=this,r=arguments;return new Promise((function(n,i){var a=e.apply(t,r);function o(e){c(a,n,i,o,s,"next",e)}function s(e){c(a,n,i,o,s,"throw",e)}o(void 0)}))}}t.CSS_UNITS=96/72;t.DEFAULT_SCALE_VALUE="auto";t.DEFAULT_SCALE=1;t.MIN_SCALE=.1;t.MAX_SCALE=10;t.UNKNOWN_SCALE=0;t.MAX_AUTO_SCALE=1.25;t.SCROLLBAR_PADDING=40;t.VERTICAL_PADDING=5;t.PresentationModeState={UNKNOWN:0,NORMAL:1,CHANGING:2,FULLSCREEN:3};t.RendererType={CANVAS:"canvas",SVG:"svg"};t.TextLayerMode={DISABLE:0,ENABLE:1,ENABLE_ENHANCE:2};var f={UNKNOWN:-1,VERTICAL:0,HORIZONTAL:1,WRAPPED:2};t.ScrollMode=f;var d={UNKNOWN:-1,NONE:0,ODD:1,EVEN:2};function h(e,t){return t?e.replace(/\{\{\s*(\w+)\s*\}\}/g,(function(e,r){return r in t?t[r]:"{{"+r+"}}"})):e}t.SpreadMode=d;var p={getLanguage:function(){var e=l(i.default.mark((function e(){return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return","en-us");case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),getDirection:function(){var e=l(i.default.mark((function e(){return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return","ltr");case 1:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}(),get:function(){var e=l(i.default.mark((function e(t,r,n){return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",h(n,r));case 1:case"end":return e.stop()}}),e,this)})));return function(t,r,n){return e.apply(this,arguments)}}(),translate:function(){var e=l(i.default.mark((function e(t){return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()};function v(e,t){var r=0,n=e.length-1;if(0===e.length||!t(e[n]))return e.length;if(t(e[r]))return r;for(;r<n;){var i=r+n>>1;t(e[i])?n=i:r=i+1}return r}function m(e,t,r){if(e<2)return e;var n=t[e].div,i=n.offsetTop+n.clientTop;i>=r&&(i=(n=t[e-1].div).offsetTop+n.clientTop);for(var a=e-2;a>=0&&!((n=t[a].div).offsetTop+n.clientTop+n.clientHeight<=i);--a)e=a;return e}t.NullL10n=p;var g={EVENT:"event",TIMEOUT:"timeout"};t.WaitOnType=g;var y=new Promise((function(e){"undefined"!==typeof window?window.requestAnimationFrame(e):setTimeout(e,20)}));t.animationStarted=y;var b=function(){function e(){var t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).dispatchToDOM,r=void 0!==t&&t;a(this,e),this._listeners=Object.create(null),this._dispatchToDOM=!0===r}return s(e,[{key:"on",value:function(e,t){var r=this._listeners[e];r||(r=[],this._listeners[e]=r),r.push(t)}},{key:"off",value:function(e,t){var r,n=this._listeners[e];!n||(r=n.indexOf(t))<0||n.splice(r,1)}},{key:"dispatch",value:function(e){var t=this._listeners[e];if(t&&0!==t.length){var r=Array.prototype.slice.call(arguments,1);t.slice(0).forEach((function(e){e.apply(null,r)})),this._dispatchToDOM&&this._dispatchDOMEvent(e,r)}else if(this._dispatchToDOM){var n=Array.prototype.slice.call(arguments,1);this._dispatchDOMEvent(e,n)}}},{key:"_dispatchDOMEvent",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=Object.create(null);if(t&&t.length>0){var n=t[0];for(var i in n){var a=n[i];if("source"!==i)r[i]=a;else if(a===window||a===document)return}}var o=document.createEvent("CustomEvent");o.initCustomEvent(e,!0,!0,r),document.dispatchEvent(o)}}]),e}();t.EventBus=b;var _=null;var S=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.height,i=r.width,o=r.units;a(this,e),this.visible=!0,this.div=document.querySelector(t+" .progress"),this.bar=this.div.parentNode,this.height=n||100,this.width=i||100,this.units=o||"%",this.div.style.height=this.height+this.units,this.percent=0}return s(e,[{key:"_updateBar",value:function(){if(this._indeterminate)return this.div.classList.add("indeterminate"),void(this.div.style.width=this.width+this.units);this.div.classList.remove("indeterminate");var e=this.width*this._percent/100;this.div.style.width=e+this.units}},{key:"setWidth",value:function(e){if(e){var t=e.parentNode.offsetWidth-e.offsetWidth;t>0&&this.bar.setAttribute("style","width: calc(100% - "+t+"px);")}}},{key:"hide",value:function(){this.visible&&(this.visible=!1,this.bar.classList.add("hidden"),document.body.classList.remove("loadingInProgress"))}},{key:"show",value:function(){this.visible||(this.visible=!0,document.body.classList.add("loadingInProgress"),this.bar.classList.remove("hidden"))}},{key:"percent",get:function(){return this._percent},set:function(e){var t,r,n;this._indeterminate=isNaN(e),this._percent=(t=e,r=0,n=100,Math.min(Math.max(t,r),n)),this._updateBar()}}]),e}();t.ProgressBar=S},49772:function(e){"use strict";var t=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,r,n,i){return r=r||"&",n=n||"=",null===e&&(e=void 0),"object"===typeof e?Object.keys(e).map((function(i){var a=encodeURIComponent(t(i))+n;return Array.isArray(e[i])?e[i].map((function(e){return a+encodeURIComponent(t(e))})).join(r):a+encodeURIComponent(t(e[i]))})).join(r):i?encodeURIComponent(t(i))+n+encodeURIComponent(t(e)):""}},54893:function(e){e.exports=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(t.includes(n))continue;r[n]=e[n]}return r},e.exports.__esModule=!0,e.exports.default=e.exports},63072:function(e){function t(r){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},64608:function(e,t,r){"use strict";r.r(t),r.d(t,{allEvents:function(){return _},animationEvents:function(){return g},clipboardEvents:function(){return n},compositionEvents:function(){return i},focusEvents:function(){return o},formEvents:function(){return s},genericEvents:function(){return u},imageEvents:function(){return m},keyboardEvents:function(){return a},mediaEvents:function(){return v},mouseEvents:function(){return c},otherEvents:function(){return b},pointerEvents:function(){return l},selectionEvents:function(){return f},touchEvents:function(){return d},transitionEvents:function(){return y},uiEvents:function(){return h},wheelEvents:function(){return p}});var n=["onCopy","onCut","onPaste"],i=["onCompositionEnd","onCompositionStart","onCompositionUpdate"],a=["onKeyDown","onKeyPress","onKeyUp"],o=["onFocus","onBlur"],s=["onChange","onInput","onInvalid","onReset","onSubmit"],u=["onError","onLoad"],c=["onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp"],l=["onPointerDown","onPointerMove","onPointerUp","onPointerCancel","onGotPointerCapture","onLostPointerCapture","onPointerEnter","onPointerLeave","onPointerOver","onPointerOut"],f=["onSelect"],d=["onTouchCancel","onTouchEnd","onTouchMove","onTouchStart"],h=["onScroll"],p=["onWheel"],v=["onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onError","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting"],m=["onLoad","onError"],g=["onAnimationStart","onAnimationEnd","onAnimationIteration"],y=["onTransitionEnd"],b=["onToggle"],_=[].concat(n,i,a,o,s,u,c,l,f,d,h,p,v,m,g,y,b);t.default=function(e,t){var r={};return _.forEach((function(n){n in e&&(r[n]=t?function(r){return e[n](r,t(n))}:e[n])})),r}},66415:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return h.default.createElement(m.default.Consumer,null,(function(t){return h.default.createElement(_,(0,a.default)({},t,e))}))},t.PageSVGInternal=void 0;var a=i(r(94634)),o=i(r(17383)),s=i(r(34579)),u=i(r(12475)),c=i(r(29511)),l=i(r(28452)),f=i(r(63072)),d=i(r(43693)),h=n(r(31014)),p=i(r(15662)),v=i(r(3170)),m=i(r(75684)),g=r(13947),y=r(8876);function b(e){return function(){var t,r=(0,f.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,f.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,l.default)(this,t)}}var _=function(e){(0,c.default)(r,e);var t=b(r);function r(){var e;(0,o.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,d.default)((0,u.default)(e),"state",{svg:null}),(0,d.default)((0,u.default)(e),"onRenderSuccess",(function(){e.renderer=null;var t=e.props,r=t.onRenderSuccess,n=t.page,i=t.scale;r&&r((0,g.makePageCallback)(n,i))})),(0,d.default)((0,u.default)(e),"onRenderError",(function(t){if(!(0,g.isCancelException)(t)){(0,g.errorOnDev)(t);var r=e.props.onRenderError;r&&r(t)}})),(0,d.default)((0,u.default)(e),"renderSVG",(function(){var t=e.props.page;return e.renderer=t.getOperatorList(),e.renderer.then((function(r){var n=new v.default.SVGGraphics(t.commonObjs,t.objs);e.renderer=n.getSVG(r,e.viewport).then((function(t){e.setState({svg:t},e.onRenderSuccess)})).catch(e.onRenderError)})).catch(e.onRenderError)})),(0,d.default)((0,u.default)(e),"drawPageOnContainer",(function(t){var r=e.state.svg;if(t&&r){t.firstElementChild||t.appendChild(r);var n=e.viewport,i=n.width,a=n.height;r.setAttribute("width",i),r.setAttribute("height",a)}})),e}return(0,s.default)(r,[{key:"componentDidMount",value:function(){this.renderSVG()}},{key:"render",value:function(){var e=this,t=this.viewport,r=t.width,n=t.height;return h.default.createElement("div",{className:"react-pdf__Page__svg",ref:function(t){return e.drawPageOnContainer(t)},style:{display:"block",backgroundColor:"white",overflow:"hidden",width:r,height:n,userSelect:"none"}})}},{key:"viewport",get:function(){var e=this.props,t=e.page,r=e.rotate,n=e.scale;return t.getViewport({scale:n,rotation:r})}}]),r}(h.PureComponent);t.PageSVGInternal=_,_.propTypes={onRenderError:p.default.func,onRenderSuccess:p.default.func,page:y.isPage.isRequired,rotate:y.isRotate,scale:p.default.number}},67096:function(e,t,r){var n;e=r.nmd(e),function(){t&&t.nodeType,e&&e.nodeType;var i="object"==typeof r.g&&r.g;i.global!==i&&i.window!==i&&i.self;var a,o=2147483647,s=36,u=/^xn--/,c=/[^\x20-\x7E]/,l=/[\x2E\u3002\uFF0E\uFF61]/g,f={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},d=Math.floor,h=String.fromCharCode;function p(e){throw RangeError(f[e])}function v(e,t){for(var r=e.length,n=[];r--;)n[r]=t(e[r]);return n}function m(e,t){var r=e.split("@"),n="";return r.length>1&&(n=r[0]+"@",e=r[1]),n+v((e=e.replace(l,".")).split("."),t).join(".")}function g(e){for(var t,r,n=[],i=0,a=e.length;i<a;)(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<a?56320==(64512&(r=e.charCodeAt(i++)))?n.push(((1023&t)<<10)+(1023&r)+65536):(n.push(t),i--):n.push(t);return n}function y(e){return v(e,(function(e){var t="";return e>65535&&(t+=h((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=h(e)})).join("")}function b(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function _(e,t,r){var n=0;for(e=r?d(e/700):e>>1,e+=d(e/t);e>455;n+=s)e=d(e/35);return d(n+36*e/(e+38))}function S(e){var t,r,n,i,a,u,c,l,f,h,v,m=[],g=e.length,b=0,S=128,A=72;for((r=e.lastIndexOf("-"))<0&&(r=0),n=0;n<r;++n)e.charCodeAt(n)>=128&&p("not-basic"),m.push(e.charCodeAt(n));for(i=r>0?r+1:0;i<g;){for(a=b,u=1,c=s;i>=g&&p("invalid-input"),((l=(v=e.charCodeAt(i++))-48<10?v-22:v-65<26?v-65:v-97<26?v-97:s)>=s||l>d((o-b)/u))&&p("overflow"),b+=l*u,!(l<(f=c<=A?1:c>=A+26?26:c-A));c+=s)u>d(o/(h=s-f))&&p("overflow"),u*=h;A=_(b-a,t=m.length+1,0==a),d(b/t)>o-S&&p("overflow"),S+=d(b/t),b%=t,m.splice(b++,0,S)}return y(m)}function A(e){var t,r,n,i,a,u,c,l,f,v,m,y,S,A,w,k=[];for(y=(e=g(e)).length,t=128,r=0,a=72,u=0;u<y;++u)(m=e[u])<128&&k.push(h(m));for(n=i=k.length,i&&k.push("-");n<y;){for(c=o,u=0;u<y;++u)(m=e[u])>=t&&m<c&&(c=m);for(c-t>d((o-r)/(S=n+1))&&p("overflow"),r+=(c-t)*S,t=c,u=0;u<y;++u)if((m=e[u])<t&&++r>o&&p("overflow"),m==t){for(l=r,f=s;!(l<(v=f<=a?1:f>=a+26?26:f-a));f+=s)w=l-v,A=s-v,k.push(h(b(v+w%A,0))),l=d(w/A);k.push(h(b(l,0))),a=_(r,S,n==i),r=0,++n}++r,++t}return k.join("")}a={version:"1.3.2",ucs2:{decode:g,encode:y},decode:S,encode:A,toASCII:function(e){return m(e,(function(e){return c.test(e)?"xn--"+A(e):e}))},toUnicode:function(e){return m(e,(function(e){return u.test(e)?S(e.slice(4).toLowerCase()):e}))}},void 0===(n=function(){return a}.call(t,r,t,e))||(e.exports=n)}()},70079:function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n},e.exports.__esModule=!0,e.exports.default=e.exports},72815:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return m.default.createElement(y.default.Consumer,null,(function(t){return m.default.createElement(S,(0,a.default)({},t,e))}))},t.TextLayerItemInternal=void 0;var a=i(r(94634)),o=i(r(85715)),s=i(r(54756)),u=i(r(29293)),c=i(r(17383)),l=i(r(34579)),f=i(r(12475)),d=i(r(29511)),h=i(r(28452)),p=i(r(63072)),v=i(r(43693)),m=n(r(31014)),g=i(r(15662)),y=i(r(75684)),b=r(8876);function _(e){return function(){var t,r=(0,p.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,p.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,h.default)(this,t)}}var S=function(e){(0,d.default)(r,e);var t=_(r);function r(){var e;(0,c.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,v.default)((0,f.default)(e),"getElementWidth",(function(t){var r=(0,f.default)(e).sideways;return t.getBoundingClientRect()[r?"height":"width"]})),e}return(0,l.default)(r,[{key:"componentDidMount",value:function(){this.alignTextItem()}},{key:"componentDidUpdate",value:function(){this.alignTextItem()}},{key:"getFontData",value:function(){var e=(0,u.default)(s.default.mark((function e(t){var r,n;return s.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=this.props.page,e.next=3,new Promise((function(e){r.commonObjs.get(t,e)}));case 3:return n=e.sent,e.abrupt("return",n);case 5:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}()},{key:"alignTextItem",value:function(){var e=(0,u.default)(s.default.mark((function e(){var t,r,n,i,a,o,u,c,l,f,d;return s.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.item){e.next=3;break}return e.abrupt("return");case 3:return t.style.transform="",r=this.props,n=r.fontName,i=r.scale,a=r.width,t.style.fontFamily="".concat(n,", sans-serif"),e.next=8,this.getFontData(n);case 8:o=e.sent,u=o?o.fallbackName:"sans-serif",t.style.fontFamily="".concat(n,", ").concat(u),c=a*i,l=this.getElementWidth(t),f="scaleX(".concat(c/l,")"),(d=o?o.ascent:0)&&(f+=" translateY(".concat(100*(1-d),"%)")),t.style.transform=f,t.style.WebkitTransform=f;case 18:case"end":return e.stop()}}),e,this)})));return function(){return e.apply(this,arguments)}}()},{key:"render",value:function(){var e=this,t=this.fontSize,r=this.top,n=this.left,i=this.props,a=i.customTextRenderer,o=i.scale,s=i.str;return m.default.createElement("span",{ref:function(t){e.item=t},style:{height:"1em",fontFamily:"sans-serif",fontSize:"".concat(t*o,"px"),position:"absolute",top:"".concat(r*o,"px"),left:"".concat(n*o,"px"),transformOrigin:"left bottom",whiteSpace:"pre",pointerEvents:"all"}},a?a(this.props):s)}},{key:"unrotatedViewport",get:function(){var e=this.props,t=e.page,r=e.scale;return t.getViewport({scale:r})}},{key:"rotate",get:function(){var e=this.props,t=e.page;return e.rotate-t.rotate}},{key:"sideways",get:function(){return this.rotate%180!==0}},{key:"defaultSideways",get:function(){return this.unrotatedViewport.rotation%180!==0}},{key:"fontSize",get:function(){var e=this.props.transform,t=this.defaultSideways,r=(0,o.default)(e,2),n=r[0],i=r[1];return t?i:n}},{key:"top",get:function(){var e=this.props.transform,t=this.unrotatedViewport,r=this.defaultSideways,n=(0,o.default)(e,6),i=n[2],a=n[3],s=n[4],u=n[5],c=(0,o.default)(t.viewBox,4),l=c[1],f=c[3];return r?s+i+l:f-(u+a)}},{key:"left",get:function(){var e=this.props.transform,t=this.unrotatedViewport,r=this.defaultSideways,n=(0,o.default)(e,6),i=n[4],a=n[5],s=(0,o.default)(t.viewBox,1)[0];return r?a-s:i-s}}]),r}(m.PureComponent);t.TextLayerItemInternal=S,S.propTypes={customTextRenderer:g.default.func,fontName:g.default.string.isRequired,itemIndex:g.default.number.isRequired,page:b.isPage.isRequired,rotate:b.isRotate,scale:g.default.number,str:g.default.string.isRequired,transform:g.default.arrayOf(g.default.number).isRequired,width:g.default.number.isRequired}},75684:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=(0,r(31014).createContext)(null);t.default=n},76219:function(e,t,r){"use strict";function n(e){var t=!1,r=new Promise((function(r,n){e.then((function(){return!t&&r.apply(void 0,arguments)})).catch((function(e){return!t&&n(e)}))}));return{promise:r,cancel:function(){t=!0}}}r.r(t),r.d(t,{default:function(){return n}})},77736:function(e,t,r){var n=r(73738).default,i=r(89045);e.exports=function(e){var t=i(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},77998:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=(0,r(31014).createContext)(null);t.default=n},79659:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.PageInternal=void 0;var a=i(r(94634)),o=i(r(54756)),s=i(r(29293)),u=i(r(17383)),c=i(r(34579)),l=i(r(12475)),f=i(r(29511)),d=i(r(28452)),h=i(r(63072)),p=i(r(43693)),v=n(r(31014)),m=i(r(15662)),g=i(r(76219)),y=i(r(64608)),b=i(r(20410)),_=i(r(77998)),S=i(r(75684)),A=i(r(46129)),w=i(r(39569)),k=i(r(66415)),x=i(r(10140)),P=i(r(91458)),R=r(13947),C=r(8876);function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function T(e){return function(){var t,r=(0,h.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,h.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,d.default)(this,t)}}var O=function(e){(0,f.default)(r,e);var t=T(r);function r(){var e;(0,u.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,p.default)((0,l.default)(e),"state",{page:null}),(0,p.default)((0,l.default)(e),"onLoadSuccess",(function(){var t=e.props,r=t.onLoadSuccess,n=t.registerPage,i=e.state.page;r&&r((0,R.makePageCallback)(i,e.scale)),n&&n(e.pageIndex,e.ref)})),(0,p.default)((0,l.default)(e),"onLoadError",(function(t){(0,R.errorOnDev)(t);var r=e.props.onLoadError;r&&r(t)})),(0,p.default)((0,l.default)(e),"loadPage",(0,s.default)(o.default.mark((function t(){var r,n,i,a;return o.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.props.pdf,n=e.getPageNumber()){t.next=4;break}return t.abrupt("return");case 4:return e.setState((function(e){return e.page?{page:null}:null})),t.prev=5,i=(0,g.default)(r.getPage(n)),e.runningTask=i,t.next=10,i.promise;case 10:a=t.sent,e.setState({page:a},e.onLoadSuccess),t.next=18;break;case 14:t.prev=14,t.t0=t.catch(5),e.setState({page:!1}),e.onLoadError(t.t0);case 18:case"end":return t.stop()}}),t,null,[[5,14]])})))),e}return(0,c.default)(r,[{key:"componentDidMount",value:function(){if(!this.props.pdf)throw new Error("Attempted to load a page, but no document was specified.");this.loadPage()}},{key:"componentDidUpdate",value:function(e){var t=this.props.pdf;if(e.pdf&&t!==e.pdf||this.getPageNumber()!==this.getPageNumber(e)){var r=this.props.unregisterPage;r&&r(this.getPageIndex(e)),this.loadPage()}}},{key:"componentWillUnmount",value:function(){var e=this.props.unregisterPage;e&&e(this.pageIndex),(0,R.cancelRunningTask)(this.runningTask)}},{key:"getPageIndex",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props;return(0,R.isProvided)(e.pageNumber)?e.pageNumber-1:(0,R.isProvided)(e.pageIndex)?e.pageIndex:null}},{key:"getPageNumber",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props;return(0,R.isProvided)(e.pageNumber)?e.pageNumber:(0,R.isProvided)(e.pageIndex)?e.pageIndex+1:null}},{key:"renderMainLayer",value:function(){switch(this.props.renderMode){case"none":return null;case"svg":return v.default.createElement(k.default,{key:"".concat(this.pageKeyNoScale,"_svg")});default:return v.default.createElement(w.default,{key:"".concat(this.pageKey,"_canvas")})}}},{key:"renderTextLayer",value:function(){var e=this.props.renderTextLayer;return e?v.default.createElement(x.default,{key:"".concat(this.pageKey,"_text")}):null}},{key:"renderAnnotationLayer",value:function(){var e=this.props.renderAnnotationLayer;return e?v.default.createElement(P.default,{key:"".concat(this.pageKey,"_annotations")}):null}},{key:"renderChildren",value:function(){var e=this.props.children;return v.default.createElement(S.default.Provider,{value:this.childContext},this.renderMainLayer(),this.renderTextLayer(),this.renderAnnotationLayer(),e)}},{key:"renderContent",value:function(){var e=this.pageNumber,t=this.props.pdf,r=this.state.page;if(!e){var n=this.props.noData;return v.default.createElement(A.default,{type:"no-data"},"function"===typeof n?n():n)}if(null===t||null===r){var i=this.props.loading;return v.default.createElement(A.default,{type:"loading"},"function"===typeof i?i():i)}if(!1===t||!1===r){var a=this.props.error;return v.default.createElement(A.default,{type:"error"},"function"===typeof a?a():a)}return this.renderChildren()}},{key:"render",value:function(){var e=this,t=this.pageNumber,r=this.props.className;return v.default.createElement("div",(0,a.default)({className:(0,b.default)("react-pdf__Page",r),"data-page-number":t,ref:function(t){var r=e.props.inputRef;r&&r(t),e.ref=t},style:{position:"relative"}},this.eventProps),this.renderContent())}},{key:"childContext",get:function(){var e=this.state.page;if(!e)return{};var t=this.props;return{customTextRenderer:t.customTextRenderer,onGetAnnotationsError:t.onGetAnnotationsError,onGetAnnotationsSuccess:t.onGetAnnotationsSuccess,onGetTextError:t.onGetTextError,onGetTextSuccess:t.onGetTextSuccess,onRenderAnnotationLayerError:t.onRenderAnnotationLayerError,onRenderAnnotationLayerSuccess:t.onRenderAnnotationLayerSuccess,onRenderError:t.onRenderError,onRenderSuccess:t.onRenderSuccess,page:e,renderInteractiveForms:t.renderInteractiveForms,rotate:this.rotate,scale:this.scale}}},{key:"pageIndex",get:function(){return this.getPageIndex()}},{key:"pageNumber",get:function(){return this.getPageNumber()}},{key:"rotate",get:function(){var e=this.props.rotate;if((0,R.isProvided)(e))return e;var t=this.state.page;return t?t.rotate:null}},{key:"scale",get:function(){var e=this.state.page;if(!e)return null;var t=this.props,r=t.scale,n=t.width,i=t.height,a=this.rotate,o=1,s=null===r?1:r;if(n||i){var u=e.getViewport({scale:1,rotation:a});o=n?n/u.width:i/u.height}return s*o}},{key:"eventProps",get:function(){var e=this;return(0,y.default)(this.props,(function(){var t=e.state.page;return t?(0,R.makePageCallback)(t,e.scale):t}))}},{key:"pageKey",get:function(){var e=this.state.page;return"".concat(e.pageIndex,"@").concat(this.scale,"/").concat(this.rotate)}},{key:"pageKeyNoScale",get:function(){var e=this.state.page;return"".concat(e.pageIndex,"/").concat(this.rotate)}}]),r}(v.PureComponent);t.PageInternal=O,O.defaultProps={error:"Failed to load the page.",loading:"Loading page\u2026",noData:"No page specified.",renderAnnotationLayer:!0,renderInteractiveForms:!1,renderMode:"canvas",renderTextLayer:!0,scale:1};var L=m.default.oneOfType([m.default.func,m.default.node]);O.propTypes=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach((function(t){(0,p.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},C.eventProps,{children:m.default.node,className:C.isClassName,customTextRenderer:m.default.func,error:L,height:m.default.number,inputRef:m.default.func,loading:L,noData:L,onGetTextError:m.default.func,onGetTextSuccess:m.default.func,onLoadError:m.default.func,onLoadSuccess:m.default.func,onRenderError:m.default.func,onRenderSuccess:m.default.func,pageIndex:C.isPageIndex,pageNumber:C.isPageNumber,pdf:C.isPdf,registerPage:m.default.func,renderAnnotationLayer:m.default.bool,renderInteractiveForms:m.default.bool,renderMode:C.isRenderMode,renderTextLayer:m.default.bool,rotate:C.isRotate,scale:m.default.number,unregisterPage:m.default.func,width:m.default.number});var I=v.default.forwardRef((function(e,t){return v.default.createElement(_.default.Consumer,null,(function(r){return v.default.createElement(O,(0,a.default)({ref:t},r,e,{renderAnnotationLayer:"undefined"!==typeof e.renderAnnotationLayer?e.renderAnnotationLayer:e.renderAnnotations}))}))}));t.default=I},81156:function(e){e.exports=function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],u=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);u=!0);}catch(e){c=!0,i=e}finally{try{if(!u&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw i}}return s}},e.exports.__esModule=!0,e.exports.default=e.exports},83314:function(e,t,r){"use strict";var n=r(24994);Object.defineProperty(t,"Uy",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"yo",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"YW",{enumerable:!0,get:function(){return s.default}});var i=n(r(3170)),a=n(r(35317)),o=n(r(32424)),s=n(r(79659)),u=r(13947);u.isLocalFileSystem&&(0,u.warnOnDev)("You are running React-PDF from your local file system. PDF.js Worker may fail to load due to browser's security policies. If you're on Google Chrome, you can use --allow-file-access-from-files flag for debugging purposes."),i.default.GlobalWorkerOptions.workerSrc="pdf.worker.js"},85715:function(e,t,r){var n=r(15368),i=r(81156),a=r(17122),o=r(25371);e.exports=function(e,t){return n(e)||i(e,t)||a(e,t)||o()},e.exports.__esModule=!0,e.exports.default=e.exports},88503:function(e,t,r){"use strict";var n=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n(r(17383)),a=n(r(34579)),o=function(){function e(t){var r=t.num,n=t.gen;(0,i.default)(this,e),this.num=r,this.gen=n}return(0,a.default)(e,[{key:"toString",value:function(){var e="".concat(this.num,"R");return 0!==this.gen&&(e+=this.gen),e}}]),e}();t.default=o},89045:function(e,t,r){var n=r(73738).default;e.exports=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},91458:function(e,t,r){"use strict";var n=r(6305),i=r(24994);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.AnnotationLayerInternal=void 0;var a=i(r(94634)),o=i(r(54756)),s=i(r(29293)),u=i(r(17383)),c=i(r(34579)),l=i(r(12475)),f=i(r(29511)),d=i(r(28452)),h=i(r(63072)),p=i(r(43693)),v=n(r(31014)),m=i(r(15662)),g=i(r(3170)),y=i(r(76219)),b=i(r(77998)),_=i(r(75684)),S=r(13947),A=r(8876);function w(e){return function(){var t,r=(0,h.default)(e);if(function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}()){var n=(0,h.default)(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return(0,d.default)(this,t)}}var k=function(e){(0,f.default)(r,e);var t=w(r);function r(){var e;(0,u.default)(this,r);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=t.call.apply(t,[this].concat(i)),(0,p.default)((0,l.default)(e),"state",{annotations:null}),(0,p.default)((0,l.default)(e),"loadAnnotations",(0,s.default)(o.default.mark((function t(){var r,n,i;return o.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.props.page,t.prev=1,n=(0,y.default)(r.getAnnotations()),e.runningTask=n,t.next=6,n.promise;case 6:i=t.sent,e.setState({annotations:i},e.onLoadSuccess),t.next=13;break;case 10:t.prev=10,t.t0=t.catch(1),e.onLoadError(t.t0);case 13:case"end":return t.stop()}}),t,null,[[1,10]])})))),(0,p.default)((0,l.default)(e),"onLoadSuccess",(function(){var t=e.props.onGetAnnotationsSuccess,r=e.state.annotations;t&&t(r)})),(0,p.default)((0,l.default)(e),"onLoadError",(function(t){e.setState({annotations:!1}),(0,S.errorOnDev)(t);var r=e.props.onGetAnnotationsError;r&&r(t)})),(0,p.default)((0,l.default)(e),"onRenderSuccess",(function(){var t=e.props.onRenderAnnotationLayerSuccess;t&&t()})),(0,p.default)((0,l.default)(e),"onRenderError",(function(t){(0,S.errorOnDev)(t);var r=e.props.onRenderAnnotationLayerError;r&&r(t)})),e}return(0,c.default)(r,[{key:"componentDidMount",value:function(){if(!this.props.page)throw new Error("Attempted to load page annotations, but no page was specified.");this.loadAnnotations()}},{key:"componentDidUpdate",value:function(e){var t=this.props,r=t.page,n=t.renderInteractiveForms;(e.page&&r!==e.page||n!==e.renderInteractiveForms)&&this.loadAnnotations()}},{key:"componentWillUnmount",value:function(){(0,S.cancelRunningTask)(this.runningTask)}},{key:"renderAnnotationLayer",value:function(){var e=this.state.annotations;if(e){var t=this.props,r=t.linkService,n=t.page,i=t.renderInteractiveForms,a=this.viewport.clone({dontFlip:!0}),o={annotations:e,div:this.annotationLayer,linkService:r,page:n,renderInteractiveForms:i,viewport:a};this.annotationLayer.innerHTML="";try{g.default.AnnotationLayer.render(o),this.onRenderSuccess()}catch(s){this.onRenderError(s)}}}},{key:"render",value:function(){var e=this;return v.default.createElement("div",{className:"react-pdf__Page__annotations annotationLayer",ref:function(t){e.annotationLayer=t}},this.renderAnnotationLayer())}},{key:"viewport",get:function(){var e=this.props,t=e.page,r=e.rotate,n=e.scale;return t.getViewport({scale:n,rotation:r})}}]),r}(v.PureComponent);t.AnnotationLayerInternal=k,k.propTypes={linkService:A.isLinkService.isRequired,onGetAnnotationsError:m.default.func,onGetAnnotationsSuccess:m.default.func,onRenderAnnotationLayerError:m.default.func,onRenderAnnotationLayerSuccess:m.default.func,page:A.isPage,renderInteractiveForms:m.default.bool,rotate:A.isRotate,scale:m.default.number};var x=function(e){return v.default.createElement(b.default.Consumer,null,(function(t){return v.default.createElement(_.default.Consumer,null,(function(r){return v.default.createElement(k,(0,a.default)({},t,r,e))}))}))};t.default=x},91847:function(e,t,r){var n=r(54893);e.exports=function(e,t){if(null==e)return{};var r,i,a=n(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],t.includes(r)||{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a},e.exports.__esModule=!0,e.exports.default=e.exports},94634:function(e){function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},95636:function(e){function t(r,n){return e.exports=t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r,n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},96425:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};t.default=r},99291:function(e){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports}}]);
//# sourceMappingURL=3314.3747b2a7.chunk.js.map