# Generated by Django 5.1.9 on 2025-06-01 12:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0028_paymenttransaction_razorpay_invoice_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="UsageHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service_type", models.CharField(max_length=50)),
                ("input_length", models.IntegerField()),
                ("cost", models.DecimalField(decimal_places=7, max_digits=10)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "reference_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Usage History",
                "verbose_name_plural": "Usage History",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="authenticat_user_id_ba64bb_idx",
                    ),
                    models.Index(
                        fields=["service_type"],
                        name="authenticat_service_c60f2d_idx",
                    ),
                ],
            },
        ),
    ]
