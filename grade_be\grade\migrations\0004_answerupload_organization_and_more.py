# Generated by Django 5.1.9 on 2025-06-04 13:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentication",
            "0033_organization_user_role_org_user_organization",
        ),
        ("grade", "0003_alter_answerupload_user_id_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="answerupload",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="answer_uploads",
                to="authentication.organization",
            ),
        ),
        migrations.AddField(
            model_name="generatedquestionpaper",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_papers",
                to="authentication.organization",
            ),
        ),
        migrations.AddField(
            model_name="previousyearquestionpaper",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_papers",
                to="authentication.organization",
            ),
        ),
        migrations.AddField(
            model_name="questions",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_papers",
                to="authentication.organization",
            ),
        ),
        migrations.AddField(
            model_name="samplequestionpaper",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="%(class)s_papers",
                to="authentication.organization",
            ),
        ),
    ]
