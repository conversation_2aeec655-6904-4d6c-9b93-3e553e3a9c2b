import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import logging
import mlflow
import asyncio
import google.generativeai as genai
from google.generativeai.types import GenerateContentResponse
from ..llm_base import ErrorHandlingLLM, CallbackCapableLLM, BaseLLM, GenerateRequest, GenerateResponse
from ..utils.decorators import timeit, memory_usage
from ..utils.logging import logger
from ..utils.config import get_env


class GeminiLLM(CallbackCapableLLM, ErrorHandlingLLM, BaseLLM):
    def __init__(self, 
                 api_key: str = None,
                 model: str = "gemini-1.5-flash",
                 temperature: float = 0.7,
                 max_tokens: Optional[int] = 512,
                 top_p: Optional[float] = None,
                 top_k: Optional[int] = None,
                 n: int = 1,
                 stop_sequences: Optional[List[str]] = None,
                 max_retries: int = 2,
                 timeout: Optional[float] = None,
                 safety_settings: Optional[Dict] = None,
                 system_instruction: Optional[str] = None,
                 response_validation: bool = True,
                 convert_system_message_to_human: bool = False,
                 cached_content: Optional[str] = None,
                 response_modalities: Optional[List[str]] = None,
                 client_options: Optional[Dict] = None,
                 transport: str = "rest",
                 **kwargs):
        
        # Store all parameters
        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.top_p = top_p
        self.top_k = top_k
        self.n = n
        self.stop_sequences = stop_sequences
        self.max_retries = max_retries
        self.timeout = timeout
        self.safety_settings = safety_settings or {}
        self.system_instruction = system_instruction
        self.response_validation = response_validation
        self.convert_system_message_to_human = convert_system_message_to_human
        self.cached_content = cached_content
        self.response_modalities = response_modalities
        self.client_options = client_options
        self.transport = transport
        
        # Get API key from environment or parameter
        env_key = get_env("GOOGLE_API_KEY")
        self.api_key = env_key if env_key else api_key
        
        if not self.api_key:
            raise ValueError("Google API key is required. Set GOOGLE_API_KEY environment variable or pass api_key parameter.")
        
        # Configure Google Generative AI directly
        genai.configure(api_key=self.api_key)
        
        # Initialize the model
        self.genai_model = self._configure_model()
        
        # Load pricing data
        self.pricing_data = self._load_pricing_data()
        
        # Setup logger
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

    def _load_pricing_data(self) -> Dict:
        """Load pricing data from JSON file"""
        pricing_path = Path(__file__).parent.parent / 'config' / 'llm_pricing.json'
        try:
            import json
            with open(pricing_path) as f:
                return json.load(f)['pricing']['Gemini']
        except Exception as e:
            logger.error(f"Error loading pricing data: {e}")
            return {}

    def _configure_model(self):
        """Configure Google Generative AI model directly"""
        try:
            # Prepare generation config
            self.generation_config = {
                "temperature": self.temperature,
                "max_output_tokens": self.max_tokens,
            }
            
            # Add optional parameters
            if self.top_p is not None:
                self.generation_config["top_p"] = self.top_p
            if self.top_k is not None:
                self.generation_config["top_k"] = self.top_k
            if self.stop_sequences:
                self.generation_config["stop_sequences"] = self.stop_sequences
            
            # Create model with generation config
            model = genai.GenerativeModel(
                model_name=self.model,
                generation_config=self.generation_config,
                safety_settings=self.safety_settings,
                system_instruction=self.system_instruction
            )
            
            return model
            
        except Exception as e:
            logger.error(f"Failed to configure Gemini model: {e}")
            raise

    def get_callback_params(self) -> Dict[str, Any]:
        """Implementation of CallbackCapableLLM abstract method"""
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "top_p": self.top_p,
            "top_k": self.top_k,
        }

    def handle_error(self, error: Exception, request: GenerateRequest) -> GenerateResponse:
        """Handle Gemini API specific errors"""
        error_str = str(error).lower()
        error_message = ""
        
        # Parse common Gemini API error patterns
        if "400" in error_str and "invalid_argument" in error_str:
            error_message = "Invalid Argument: The request body is malformed. Check the API reference for request format."
        elif "400" in error_str and "failed_precondition" in error_str:
            error_message = "Failed Precondition: Gemini API free tier is not available in your country. Please enable billing."
        elif "403" in error_str and ("permission_denied" in error_str or "forbidden" in error_str):
            error_message = "Permission Denied: Your API key doesn't have the required permissions."
        elif "404" in error_str and "not_found" in error_str:
            error_message = "Not Found: The requested resource wasn't found. Check if your model name is correct."
        elif "429" in error_str and ("resource_exhausted" in error_str or "quota" in error_str):
            error_message = "Resource Exhausted: You've exceeded the rate limit. Please wait and retry."
        elif "500" in error_str and "internal" in error_str:
            error_message = "Internal Error: An unexpected error occurred on Google's side."
        elif "503" in error_str and "unavailable" in error_str:
            error_message = "Service Unavailable: The service may be temporarily overloaded."
        elif "504" in error_str and "deadline_exceeded" in error_str:
            error_message = "Deadline Exceeded: Your prompt is too large to be processed in time."
        elif "rate limit" in error_str:
            error_message = "Rate Limit Exceeded: You've hit your API rate limit. Please wait and retry."
        elif "timeout" in error_str:
            error_message = "Request Timeout: The request took too long to process."
        elif "api key" in error_str:
            error_message = "API Key Error: Please check your Google API key is valid and has Gemini API access."
        else:
            error_message = f"Gemini API Error: {str(error)}"
        
        # Log the error
        self.logger.error(f"Gemini API Error in {request.model}: {str(error)}")
        self.logger.error(f"Request details - Prompt: {request.prompt[:100]}..., Parameters: {request.parameters}")
        
        return GenerateResponse(
            response="",
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            cost=0.0,
            model=request.model,
            error=error_message
        )

    def _prepare_prompt(self, prompt: str, system_message: Optional[str] = None) -> str:
        """Prepare prompt for Gemini API"""
        if system_message and not self.convert_system_message_to_human:
            # If we have system instruction in model config, just use the prompt
            return prompt
        elif system_message and self.convert_system_message_to_human:
            # Combine system message with prompt
            return f"System: {system_message}\n\nUser: {prompt}"
        else:
            return prompt

    def _extract_token_usage(self, response: GenerateContentResponse) -> tuple:
        """Extract token usage from Gemini response"""
        try:
            if hasattr(response, 'usage_metadata') and response.usage_metadata:
                usage = response.usage_metadata
                prompt_tokens = getattr(usage, 'prompt_token_count', 0)
                completion_tokens = getattr(usage, 'candidates_token_count', 0)
                total_tokens = getattr(usage, 'total_token_count', prompt_tokens + completion_tokens)
                return prompt_tokens, completion_tokens, total_tokens
            
            # Fallback: estimate tokens (rough approximation)
            if hasattr(response, 'text'):
                content_length = len(response.text)
                estimated_completion_tokens = content_length // 4  # Rough estimate: 4 chars per token
                return 0, estimated_completion_tokens, estimated_completion_tokens
            
            return 0, 0, 0
        except Exception as e:
            logger.warning(f"Token usage extraction failed: {e}")
            return 0, 0, 0

    @timeit
    @memory_usage
    async def generate(self, request: GenerateRequest) -> GenerateResponse:
        """Generate response using Google Generative AI directly"""
        try:
            # Prepare prompt
            prepared_prompt = self._prepare_prompt(request.prompt)
            
            # Generate response
            response = await self.genai_model.generate_content_async(
                prepared_prompt,
                generation_config=self.generation_config,
                safety_settings=self.safety_settings
            )
            
            # Extract response content
            response_text = response.text if hasattr(response, 'text') else ""
            
            # Handle blocked responses
            if not response_text and hasattr(response, 'prompt_feedback'):
                if response.prompt_feedback.block_reason:
                    error_msg = f"Content blocked: {response.prompt_feedback.block_reason}"
                    return GenerateResponse(
                        response="",
                        prompt_tokens=0,
                        completion_tokens=0,
                        total_tokens=0,
                        cost=0.0,
                        model=self.model,
                        error=error_msg
                    )
            
            # Extract token usage
            prompt_tokens, completion_tokens, total_tokens = self._extract_token_usage(response)
            
            # Calculate cost
            cost = self._calculate_cost(prompt_tokens, completion_tokens)
            
            # Log to MLflow
            # self._log_to_mlflow(request, response, cost, prompt_tokens, completion_tokens, total_tokens)
            
            return GenerateResponse(
                response=response_text,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=cost,
                model=self.model
            )
            
        except Exception as e:
            logger.error(f"Gemini generation failed: {e}")
            return self.handle_error(e, request)

    def _calculate_cost(self, prompt_tokens: int, completion_tokens: int) -> float:
        """Calculate cost based on token usage"""
        try:
            model_pricing = self.pricing_data.get(self.model, {})
            input_cost = prompt_tokens * model_pricing.get('input', 0) / 1000
            output_cost = completion_tokens * model_pricing.get('output', 0) / 1000
            return input_cost + output_cost
        except Exception as e:
            logger.warning(f"Cost calculation failed: {e}")
            return 0.0

    # def _log_to_mlflow(self, request: GenerateRequest, response, cost: float, 
    #                   prompt_tokens: int, completion_tokens: int, total_tokens: int):
    #     """Log request and response to MLflow"""
    #     try:
    #         with mlflow.start_run(nested=True,run_name=f"Gemini_{self.model}"):
    #             # Log parameters
    #             mlflow.log_params({
    #                 "model": self.model,
    #                 "temperature": self.temperature,
    #                 "max_tokens": self.max_tokens,
    #                 "top_p": self.top_p,
    #                 "top_k": self.top_k,
    #                 "n": self.n,
    #                 "max_retries": self.max_retries,
    #                 "timeout": self.timeout,
    #             })
                
    #             # Log metrics
    #             mlflow.log_metrics({
    #                 "prompt_tokens": prompt_tokens,
    #                 "completion_tokens": completion_tokens,
    #                 "total_tokens": total_tokens,
    #                 "cost": cost
    #             })
                
    #             # Log prompt
    #             mlflow.log_text(request.prompt, "prompt.txt")
    #     except Exception as e:
    #         logger.warning(f"MLflow logging failed: {e}")

    # --------- Batching support ---------
    async def batch_generate(self, requests: List[GenerateRequest]) -> List[GenerateResponse]:
        """Generate responses for multiple requests"""
        try:
            # Create tasks for concurrent execution
            tasks = []
            for req in requests:
                task = self._single_generate_for_batch(req)
                tasks.append(task)
            
            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results and handle any exceptions
            final_results = []
            total_prompt_tokens = 0
            total_completion_tokens = 0
            total_cost = 0
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # Handle exception for this specific request
                    error_response = self.handle_error(result, requests[i])
                    final_results.append(error_response)
                else:
                    final_results.append(result)
                    total_prompt_tokens += result.prompt_tokens
                    total_completion_tokens += result.completion_tokens
                    total_cost += result.cost
            
            # Log batch metrics to MLflow
            # try:
            #     with mlflow.start_run(nested=True):
            #         mlflow.log_params({
            #             "model": self.model,
            #             "temperature": self.temperature,
            #             "max_tokens": self.max_tokens,
            #             "top_p": self.top_p,
            #             "top_k": self.top_k,
            #             "batch_size": len(requests)
            #         })
            #         mlflow.log_metrics({
            #             "total_prompt_tokens": total_prompt_tokens,
            #             "total_completion_tokens": total_completion_tokens,
            #             "total_cost": total_cost
            #         })
            # except Exception as e:
            #     logger.warning(f"MLflow batch logging failed: {e}")
            
            return final_results
            
        except Exception as e:
            logger.error(f"Gemini batch generation failed: {e}")
            # Return error responses for all requests
            return [self.handle_error(e, req) for req in requests]

    async def _single_generate_for_batch(self, request: GenerateRequest) -> GenerateResponse:
        """Helper method for batch processing"""
        try:
            prepared_prompt = self._prepare_prompt(request.prompt)
            
            response = await self.genai_model.generate_content_async(
                prepared_prompt,
                generation_config=self.generation_config,
                safety_settings=self.safety_settings
            )
            
            response_text = response.text if hasattr(response, 'text') else ""
            
            # Handle blocked responses
            if not response_text and hasattr(response, 'prompt_feedback'):
                if response.prompt_feedback.block_reason:
                    error_msg = f"Content blocked: {response.prompt_feedback.block_reason}"
                    return GenerateResponse(
                        response="",
                        prompt_tokens=0,
                        completion_tokens=0,
                        total_tokens=0,
                        cost=0.0,
                        model=self.model,
                        error=error_msg
                    )
            
            prompt_tokens, completion_tokens, total_tokens = self._extract_token_usage(response)
            cost = self._calculate_cost(prompt_tokens, completion_tokens)
            
            return GenerateResponse(
                response=response_text,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=cost,
                model=self.model
            )
            
        except Exception as e:
            raise e  # Re-raise for batch error handling

    # --------- Synchronous methods (for backward compatibility) ---------
    def generate_sync(self, request: GenerateRequest) -> GenerateResponse:
        """Synchronous version of generate method"""
        try:
            prepared_prompt = self._prepare_prompt(request.prompt)
            
            response = self.genai_model.generate_content(
                prepared_prompt,
                generation_config=self.generation_config,
                safety_settings=self.safety_settings
            )
            
            response_text = response.text if hasattr(response, 'text') else ""
            
            # Handle blocked responses
            if not response_text and hasattr(response, 'prompt_feedback'):
                if response.prompt_feedback.block_reason:
                    error_msg = f"Content blocked: {response.prompt_feedback.block_reason}"
                    return GenerateResponse(
                        response="",
                        prompt_tokens=0,
                        completion_tokens=0,
                        total_tokens=0,
                        cost=0.0,
                        model=self.model,
                        error=error_msg
                    )
            
            prompt_tokens, completion_tokens, total_tokens = self._extract_token_usage(response)
            cost = self._calculate_cost(prompt_tokens, completion_tokens)
            
            # self._log_to_mlflow(request, response, cost, prompt_tokens, completion_tokens, total_tokens)
            
            return GenerateResponse(
                response=response_text,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=cost,
                model=self.model
            )
        except Exception as e:
            logger.error(f"Gemini sync generation failed: {e}")
            return self.handle_error(e, request)

    def batch_generate_sync(self, requests: List[GenerateRequest]) -> List[GenerateResponse]:
        """Synchronous version of batch_generate method"""
        try:
            results = []
            for req in requests:
                result = self.generate_sync(req)
                results.append(result)
            return results
        except Exception as e:
            logger.error(f"Gemini sync batch generation failed: {e}")
            return [self.handle_error(e, req) for req in requests]