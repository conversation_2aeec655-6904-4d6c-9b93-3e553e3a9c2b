{"version": 3, "file": "static/js/643.00ebbe24.chunk.js", "mappings": ";sHAGEA,EAAOC,QAAU,EAAjBD,6ECAF,MAAAE,EAAAC,EAAAA,eAAA,GAEOC,EAAAA,IAAAD,EAAAA,WAAAD,GACMA,EAAAG,+ECKb,SAAAC,oBAGIC,WAAAA,KACEC,GAAA,GAEFC,MAAAA,KACED,GAAA,GAEFA,QAAAA,IACEA,EAGL,CAED,MAAAE,EAAAP,EAAAA,cAAAG,KAIOK,EAAAA,IAAAR,EAAAA,WAAAO,mICxBM,MAAAE,EACXC,IAEIA,EAAiBC,UAGuB,kBAA/BD,EAAiBE,YAC1BF,EAAiBE,UAAY,IAEhC,EAGUC,EAAYA,CACvBC,EACAC,IACGD,EAAOE,WAAaF,EAAOG,aAAeF,EAElCG,EAAgBA,CAC3BR,EAGAI,EACAC,KACG,MAAAL,OAAA,EAAAA,EAAkBC,WAAYE,EAAUC,EAAQC,GAExCI,EAAkBA,CAO7BT,EAOAU,EACAC,IAEAD,EACGD,gBAAgBT,GAChBY,MAAKC,IAAc,IAAb,KAAEC,GAAHD,EACJ,MAAAb,EAAiBe,WAAjBf,EAAiBe,UAAYD,GAC7B,MAAAd,EAAiBgB,WAAjBhB,EAAiBgB,UAAYF,EAAM,KAAnC,IAEDG,OAAOC,IACNP,EAAmBjB,aACnB,MAAAM,EAAiBmB,SAAjBnB,EAAiBmB,QAAUD,GAC3B,MAAAlB,EAAiBgB,WAAjBhB,EAAiBgB,eAAYI,EAAWF,EAAxC,uJCxCC,SAASG,EAOdC,EAOAC,GAEA,MAAMC,GAAcC,EAAAA,EAAAA,IAAe,CAAEC,QAASJ,EAAQI,UAChDrB,GAAcd,EAAAA,EAAAA,KACdoB,GAAqBb,EAAAA,EAAAA,KACrBE,EAAmBwB,EAAYG,oBAAoBL,GAGzDtB,EAAiB4B,mBAAqBvB,EAClC,cACA,aAGAL,EAAiBmB,UACnBnB,EAAiBmB,QAAUU,EAAAA,EAAcC,WACvC9B,EAAiBmB,UAIjBnB,EAAiBe,YACnBf,EAAiBe,UAAYc,EAAAA,EAAcC,WACzC9B,EAAiBe,YAIjBf,EAAiBgB,YACnBhB,EAAiBgB,UAAYa,EAAAA,EAAcC,WACzC9B,EAAiBgB,aAIrBjB,EAAAA,EAAAA,IAAgBC,IAChB+B,EAAAA,EAAAA,IAAgC/B,EAAkBW,IAElDqB,EAAAA,EAAAA,IAA2BrB,GAE3B,MAAOD,GAAYpB,EAAAA,UACjB,IACE,IAAIiC,EACFC,EACAxB,KAIAI,EAASM,EAASuB,oBAAoBjC,GA4B5C,IA1BAkC,EAAAA,EAAAA,GACE5C,EAAAA,aACG6C,IACC,MAAMC,EAAc/B,EAChB,KAD2B,EAE3BK,EAAS2B,UAAUR,EAAAA,EAAcC,WAAWK,IAMhD,OAFAzB,EAAS4B,eAEFF,CAAP,GAEF,CAAC1B,EAAUL,KAEb,IAAMK,EAAS6B,qBACf,IAAM7B,EAAS6B,qBAGjBjD,EAAAA,WAAgB,KAGdoB,EAAS8B,WAAWxC,EAAkB,CAAEyC,WAAW,GAAnD,GACC,CAACzC,EAAkBU,KAGlBF,EAAAA,EAAAA,IAAcR,EAAkBI,EAAQC,GAC1C,MAAMI,EAAAA,EAAAA,IAAgBT,EAAkBU,EAAUC,GAIpD,IACE+B,EAAAA,EAAAA,IAAY,CACVtC,SACAO,qBACAgC,iBAAkB3C,EAAiB2C,iBACnCC,MAAOlC,EAASmC,oBAGlB,MAAMzC,EAAOc,MAIf,OAAQlB,EAAiB8C,oBAErB1C,EADAM,EAASqC,YAAY3C,EAE1B,oHCnFM,MAAM4C,UAMHC,EAAAA,EA8BRC,WAAAA,CACEC,EACA7B,GAQA8B,QAEAC,KAAKF,OAASA,EACdE,KAAK/B,QAAUA,EACf+B,KAAKC,aAAe,IAAIC,IACxBF,KAAKG,YAAc,KACnBH,KAAKI,cACLJ,KAAKb,WAAWlB,EACjB,CAESmC,WAAAA,GACRJ,KAAKK,OAASL,KAAKK,OAAOC,KAAKN,MAC/BA,KAAKO,QAAUP,KAAKO,QAAQD,KAAKN,KAClC,CAESQ,WAAAA,GACoB,IAAxBR,KAAKZ,UAAUqB,OACjBT,KAAKU,aAAaC,YAAYX,MAE1BY,EAAmBZ,KAAKU,aAAcV,KAAK/B,UAC7C+B,KAAKa,eAGPb,KAAKc,eAER,CAESC,aAAAA,GACHf,KAAKgB,gBACRhB,KAAKiB,SAER,CAEDC,sBAAAA,GACE,OAAOC,EACLnB,KAAKU,aACLV,KAAK/B,QACL+B,KAAK/B,QAAQmD,mBAEhB,CAEDC,wBAAAA,GACE,OAAOF,EACLnB,KAAKU,aACLV,KAAK/B,QACL+B,KAAK/B,QAAQqD,qBAEhB,CAEDL,OAAAA,GACEjB,KAAKZ,UAAY,IAAIc,IACrBF,KAAKuB,oBACLvB,KAAKwB,uBACLxB,KAAKU,aAAae,eAAezB,KAClC,CAEDb,UAAAA,CACElB,EAOAyD,GAEA,MAAMC,EAAc3B,KAAK/B,QACnB2D,EAAY5B,KAAKU,aAuBvB,GArBAV,KAAK/B,QAAU+B,KAAKF,OAAOxB,oBAAoBL,IAa1C4D,EAAAA,EAAAA,IAAoBF,EAAa3B,KAAK/B,UACzC+B,KAAKF,OAAOgC,gBAAgBC,OAAO,CACjCC,KAAM,yBACNzC,MAAOS,KAAKU,aACZrD,SAAU2C,OAKoB,qBAAzBA,KAAK/B,QAAQgE,SACY,mBAAzBjC,KAAK/B,QAAQgE,QAEpB,MAAM,IAAIC,MAAM,oCAIblC,KAAK/B,QAAQkE,WAChBnC,KAAK/B,QAAQkE,SAAWR,EAAYQ,UAGtCnC,KAAKoC,cAEL,MAAMC,EAAUrC,KAAKgB,eAInBqB,GACAC,EACEtC,KAAKU,aACLkB,EACA5B,KAAK/B,QACL0D,IAGF3B,KAAKa,eAIPb,KAAKf,aAAayC,IAIhBW,GACCrC,KAAKU,eAAiBkB,GACrB5B,KAAK/B,QAAQgE,UAAYN,EAAYM,SACrCjC,KAAK/B,QAAQpB,YAAc8E,EAAY9E,WAEzCmD,KAAKuC,qBAGP,MAAMC,EAAsBxC,KAAKyC,0BAI/BJ,GACCrC,KAAKU,eAAiBkB,GACrB5B,KAAK/B,QAAQgE,UAAYN,EAAYM,SACrCO,IAAwBxC,KAAK0C,wBAE/B1C,KAAK2C,sBAAsBH,EAE9B,CAED5D,mBAAAA,CACEX,GAQA,MAAMsB,EAAQS,KAAKF,OAAOgC,gBAAgBc,MAAM5C,KAAKF,OAAQ7B,GAE7D,OAAO+B,KAAK6C,aAAatD,EAAOtB,EACjC,CAEDiB,gBAAAA,GACE,OAAOc,KAAK8C,aACb,CAEDpD,WAAAA,CACE3C,GAEA,MAAMgG,EAAgB,CAAC,EAavB,OAXAC,OAAOC,KAAKlG,GAAQmG,SAASC,IAC3BH,OAAOI,eAAeL,EAAeI,EAAK,CACxCE,cAAc,EACdC,YAAY,EACZC,IAAKA,KACHvD,KAAKC,aAAauD,IAAIL,GACfpG,EAAOoG,KALlB,IAUKJ,CACR,CAEDvD,eAAAA,GACE,OAAOQ,KAAKU,YACb,CAEDL,MAAAA,GACEL,KAAKF,OAAOgC,gBAAgBzB,OAAOL,KAAKU,aACzC,CAEDH,OAAAA,GAKE,IALiB,YACjBkD,KACGxF,GAFcyF,UAAAC,OAAA,QAAA5F,IAAA2F,UAAA,GAAAA,UAAA,GAGiC,CAAC,EAGnD,OAAO1D,KAAK4D,MAAM,IACb3F,EACH4F,KAAM,CAAEJ,gBAEX,CAEDrG,eAAAA,CACEa,GAQA,MAAMtB,EAAmBqD,KAAKF,OAAOxB,oBAAoBL,GAEnDsB,EAAQS,KAAKF,OAChBgC,gBACAc,MAAM5C,KAAKF,OAAQnD,GAGtB,OAFA4C,EAAMuE,sBAAuB,EAEtBvE,EAAMqE,QAAQrG,MAAK,IAAMyC,KAAK6C,aAAatD,EAAO5C,IAC1D,CAESiH,KAAAA,CACRG,GAC6C,IAAAC,EAC7C,OAAOhE,KAAKa,aAAa,IACpBkD,EACHE,cAA6C,OAA9BD,EAAAD,EAAaE,gBAAiBD,IAC5CzG,MAAK,KACNyC,KAAKf,eACEe,KAAK8C,gBAEf,CAEOjC,YAAAA,CACNkD,GAGA/D,KAAKoC,cAGL,IAAI8B,EAA2ClE,KAAKU,aAAakD,MAC/D5D,KAAK/B,QACL8F,GAOF,OAJI,MAACA,GAAAA,EAAcI,eACjBD,EAAUA,EAAQtG,MAAMwG,EAAAA,KAGnBF,CACR,CAEO3B,kBAAAA,GAGN,GAFAvC,KAAKuB,oBAGH8C,EAAAA,IACArE,KAAK8C,cAAcwB,WAClBC,EAAAA,EAAAA,IAAevE,KAAK/B,QAAQpB,WAE7B,OAGF,MAOM2H,GAPOC,EAAAA,EAAAA,IACXzE,KAAK8C,cAAc4B,cACnB1E,KAAK/B,QAAQpB,WAKQ,EAEvBmD,KAAK2E,eAAiBC,YAAW,KAC1B5E,KAAK8C,cAAcwB,SACtBtE,KAAKf,cACN,GACAuF,EACJ,CAEO/B,sBAAAA,GAAyB,IAAAoC,EAC/B,MAA+C,oBAAjC7E,KAAK/B,QAAQ6G,gBACvB9E,KAAK/B,QAAQ6G,gBAAgB9E,KAAK8C,cAAcrF,KAAMuC,KAAKU,cADxD,OAEHmE,EAAA7E,KAAK/B,QAAQ6G,kBAFVD,CAGR,CAEOlC,qBAAAA,CAAsBoC,GAC5B/E,KAAKwB,uBAELxB,KAAK0C,uBAAyBqC,GAG5BV,EAAAA,KACyB,IAAzBrE,KAAK/B,QAAQgE,UACZsC,EAAAA,EAAAA,IAAevE,KAAK0C,yBACW,IAAhC1C,KAAK0C,yBAKP1C,KAAKgF,kBAAoBC,aAAY,MAEjCjF,KAAK/B,QAAQiH,6BACbC,EAAAA,EAAaC,cAEbpF,KAAKa,cACN,GACAb,KAAK0C,wBACT,CAEO5B,YAAAA,GACNd,KAAKuC,qBACLvC,KAAK2C,sBAAsB3C,KAAKyC,yBACjC,CAEOlB,iBAAAA,GACFvB,KAAK2E,iBACPU,aAAarF,KAAK2E,gBAClB3E,KAAK2E,oBAAiB5G,EAEzB,CAEOyD,oBAAAA,GACFxB,KAAKgF,oBACPM,cAActF,KAAKgF,mBACnBhF,KAAKgF,uBAAoBjH,EAE5B,CAES8E,YAAAA,CACRtD,EACAtB,GAQA,MAAM2D,EAAY5B,KAAKU,aACjBiB,EAAc3B,KAAK/B,QACnBsH,EAAavF,KAAK8C,cAGlB0C,EAAkBxF,KAAKyF,mBACvBC,EAAoB1F,KAAK2F,qBACzBC,EAAcrG,IAAUqC,EACxBiE,EAAoBD,EACtBrG,EAAMuG,MACN9F,KAAK+F,yBACHC,EAAkBJ,EACpB5F,KAAK8C,cACL9C,KAAKiG,qBAEH,MAAEH,GAAUvG,EAClB,IAGI9B,GAHA,cAAEiH,EAAF,MAAiB7G,EAAjB,eAAwBqI,EAAxB,YAAwCC,EAAxC,OAAqDC,GAAWN,EAChEO,GAAiB,EACjBC,GAAoB,EAIxB,GAAIrI,EAAQM,mBAAoB,CAC9B,MAAM8D,EAAUrC,KAAKgB,eAEfuF,GAAgBlE,GAAWzB,EAAmBrB,EAAOtB,GAErDuI,EACJnE,GAAWC,EAAsB/C,EAAOqC,EAAW3D,EAAS0D,IAE1D4E,GAAgBC,KAClBL,GAAcM,EAAAA,EAAAA,IAASlH,EAAMtB,QAAQyI,aACjC,WACA,SACChC,IACH0B,EAAS,YAGsB,gBAA/BnI,EAAQM,qBACV4H,EAAc,OAEjB,CAGD,GACElI,EAAQ0I,mBACPb,EAAMpB,eADP,MAEAsB,GAAAA,EAAiBY,WACN,UAAXR,EAEA3I,EAAOuI,EAAgBvI,KACvBiH,EAAgBsB,EAAgBtB,cAChC0B,EAASJ,EAAgBI,OACzBC,GAAiB,OAGd,GAAIpI,EAAQ4I,QAAgC,qBAAff,EAAMrI,KAEtC,GACE8H,GACAO,EAAMrI,QAAS,MAAA+H,OAAA,EAAAA,EAAiB/H,OAChCQ,EAAQ4I,SAAW7G,KAAK8G,SAExBrJ,EAAOuC,KAAK+G,kBAEZ,IACE/G,KAAK8G,SAAW7I,EAAQ4I,OACxBpJ,EAAOQ,EAAQ4I,OAAOf,EAAMrI,MAC5BA,GAAOuJ,EAAAA,EAAAA,IAAY,MAAAzB,OAAA,EAAAA,EAAY9H,KAAMA,EAAMQ,GAC3C+B,KAAK+G,aAAetJ,EACpBuC,KAAKG,YAAc,KACnB,MAAOA,GACH8G,EAGJjH,KAAKG,YAAcA,CACpB,MAKH1C,EAAOqI,EAAMrI,KAIf,GACqC,qBAA5BQ,EAAQiJ,iBACC,qBAATzJ,GACI,YAAX2I,EACA,CACA,IAAIc,EAGJ,GACE,MAAA3B,GAAAA,EAAYe,mBACZrI,EAAQiJ,mBAAR,MAA4BxB,OAA5B,EAA4BA,EAAmBwB,iBAE/CA,EAAkB3B,EAAW9H,UAM7B,GAJAyJ,EACqC,oBAA5BjJ,EAAQiJ,gBACVjJ,EAAQiJ,kBACTjJ,EAAQiJ,gBACVjJ,EAAQ4I,QAAqC,qBAApBK,EAC3B,IACEA,EAAkBjJ,EAAQ4I,OAAOK,GACjClH,KAAKG,YAAc,KACnB,MAAOA,GACH8G,EAGJjH,KAAKG,YAAcA,CACpB,CAI0B,qBAApB+G,IACTd,EAAS,UACT3I,GAAOuJ,EAAAA,EAAAA,IAAY,MAAAzB,OAAA,EAAAA,EAAY9H,KAAMyJ,EAAiBjJ,GACtDqI,GAAoB,EAEvB,CAEGtG,KAAKG,cACPtC,EAAQmC,KAAKG,YACb1C,EAAOuC,KAAK+G,aACZb,EAAiBiB,KAAKC,MACtBhB,EAAS,SAGX,MAAMlJ,EAA6B,aAAhBiJ,EACblJ,EAAuB,YAAXmJ,EACZiB,EAAqB,UAAXjB,EAgChB,MA9BuD,CACrDA,SACAD,cACAlJ,YACA2J,UAAsB,YAAXR,EACXiB,UACAC,iBAAkBrK,GAAaC,EAC/BO,OACAiH,gBACA7G,QACAqI,iBACAqB,aAAczB,EAAM0B,kBACpBC,cAAe3B,EAAM4B,mBACrBC,iBAAkB7B,EAAM6B,iBACxBC,UAAW9B,EAAM+B,gBAAkB,GAAK/B,EAAM6B,iBAAmB,EACjEG,oBACEhC,EAAM+B,gBAAkBhC,EAAkBgC,iBAC1C/B,EAAM6B,iBAAmB9B,EAAkB8B,iBAC7CzK,aACA6K,aAAc7K,IAAeD,EAC7B+K,eAAgBX,GAAmC,IAAxBvB,EAAMpB,cACjCuD,SAA0B,WAAhB9B,EACVG,oBACAD,iBACA6B,eAAgBb,GAAmC,IAAxBvB,EAAMpB,cACjCJ,QAASA,EAAQ/E,EAAOtB,GACxBsC,QAASP,KAAKO,QACdF,OAAQL,KAAKK,OAIhB,CAEDpB,YAAAA,CAAayC,GACX,MAAM6D,EAAavF,KAAK8C,cAIlBqF,EAAanI,KAAK6C,aAAa7C,KAAKU,aAAcV,KAAK/B,SAK7D,GAJA+B,KAAKyF,mBAAqBzF,KAAKU,aAAaoF,MAC5C9F,KAAK2F,qBAAuB3F,KAAK/B,SAG7B4D,EAAAA,EAAAA,IAAoBsG,EAAY5C,GAClC,OAGFvF,KAAK8C,cAAgBqF,EAGrB,MAAMC,EAAsC,CAAEC,OAAO,IA6BpB,KAA7B,MAAA3G,OAAA,EAAAA,EAAetC,YA3BWkJ,MAC5B,IAAK/C,EACH,OAAO,EAGT,MAAM,oBAAE9F,GAAwBO,KAAK/B,QAErC,GAC0B,QAAxBwB,IACEA,IAAwBO,KAAKC,aAAaQ,KAE5C,OAAO,EAGT,MAAM8H,EAAgB,IAAIrI,IAAJ,MAAQT,EAAAA,EAAuBO,KAAKC,cAM1D,OAJID,KAAK/B,QAAQqB,kBACfiJ,EAAc/E,IAAI,SAGbR,OAAOC,KAAKjD,KAAK8C,eAAe0F,MAAMrF,IAC3C,MAAMsF,EAAWtF,EAEjB,OADgBnD,KAAK8C,cAAc2F,KAAclD,EAAWkD,IAC1CF,EAAcG,IAAID,EAApC,GAHF,EAOwCH,KACxCF,EAAqBhJ,WAAY,GAGnCY,KAAK+B,OAAO,IAAKqG,KAAyB1G,GAC3C,CAEOU,WAAAA,GACN,MAAM7C,EAAQS,KAAKF,OAAOgC,gBAAgBc,MAAM5C,KAAKF,OAAQE,KAAK/B,SAElE,GAAIsB,IAAUS,KAAKU,aACjB,OAGF,MAAMkB,EAAY5B,KAAKU,aAGvBV,KAAKU,aAAenB,EACpBS,KAAK+F,yBAA2BxG,EAAMuG,MACtC9F,KAAKiG,oBAAsBjG,KAAK8C,cAE5B9C,KAAKgB,iBACE,MAATY,GAAAA,EAAWH,eAAezB,MAC1BT,EAAMoB,YAAYX,MAErB,CAED2I,aAAAA,CAAcC,GACZ,MAAMlH,EAA+B,CAAC,EAElB,YAAhBkH,EAAO5G,KACTN,EAAchE,WAAakL,EAAOC,OACT,UAAhBD,EAAO5G,OAAqB8G,EAAAA,EAAAA,IAAiBF,EAAO/K,SAC7D6D,EAAc5D,SAAU,GAG1BkC,KAAKf,aAAayC,GAEd1B,KAAKgB,gBACPhB,KAAKc,cAER,CAEOiB,MAAAA,CAAOL,GACblD,EAAAA,EAAcuK,OAAM,KAEW,IAAAC,EAAAC,EAAAC,EAAAC,EAA7B,GAAIzH,EAAchE,UAChB,OAAAsL,GAAAC,EAAAjJ,KAAK/B,SAAQP,YAAbsL,EAAAI,KAAAH,EAAyBjJ,KAAK8C,cAAcrF,MAC5C,OAAKyL,GAAAC,EAAA,KAAAlL,SAAQN,YAAbuL,EAAAE,KAAAD,EAAyBnJ,KAAK8C,cAAcrF,KAAO,WAC9C,GAAIiE,EAAc5D,QAAS,KAAAuL,EAAAC,EAAAC,EAAAC,EAChC,OAAAH,GAAAC,EAAAtJ,KAAK/B,SAAQH,UAAbuL,EAAAD,KAAAE,EAAuBtJ,KAAK8C,cAAcjF,OAC1C,OAAK0L,GAAAC,EAAA,KAAAvL,SAAQN,YAAb4L,EAAAH,KAAAI,OAAyBzL,EAAWiC,KAAK8C,cAAcjF,MACxD,CAGG6D,EAActC,WAChBY,KAAKZ,UAAU8D,SAAQ1F,IAAkB,IAAjB,SAAEiM,GAAHjM,EACrBiM,EAASzJ,KAAK8C,cAAd,IAKApB,EAAc2G,OAChBrI,KAAKF,OAAOgC,gBAAgBC,OAAO,CACjCxC,MAAOS,KAAKU,aACZsB,KAAM,0BAET,GAEJ,EAcH,SAASpB,EACPrB,EACAtB,GAEA,OAfF,SACEsB,EACAtB,GAEA,OACsB,IAApBA,EAAQgE,UACP1C,EAAMuG,MAAMpB,iBACY,UAAvBnF,EAAMuG,MAAMM,SAA+C,IAAzBnI,EAAQyL,aAE/C,CAOGC,CAAkBpK,EAAOtB,IACxBsB,EAAMuG,MAAMpB,cAAgB,GAC3BvD,EAAc5B,EAAOtB,EAASA,EAAQ2L,eAE3C,CAED,SAASzI,EACP5B,EACAtB,EACA4L,GAIA,IAAwB,IAApB5L,EAAQgE,QAAmB,CAC7B,MAAM6H,EAAyB,oBAAVD,EAAuBA,EAAMtK,GAASsK,EAE3D,MAAiB,WAAVC,IAAiC,IAAVA,GAAmBxF,EAAQ/E,EAAOtB,EACjE,CACD,OAAO,CACR,CAED,SAASqE,EACP/C,EACAqC,EACA3D,EACA0D,GAEA,OACsB,IAApB1D,EAAQgE,UACP1C,IAAUqC,IAAqC,IAAxBD,EAAYM,YAClChE,EAAQrB,UAAmC,UAAvB2C,EAAMuG,MAAMM,SAClC9B,EAAQ/E,EAAOtB,EAElB,CAED,SAASqG,EACP/E,EACAtB,GAEA,OAAOsB,EAAMwK,cAAc9L,EAAQpB,UACpC,qJCjuBCoB,EAAArB,UAAAqB,EAAAqB,oBAEEhC,EAAAhB,+BAGD,EAGUqC,EAAArB,sBAITA,EAAAjB,YAAA,SAIGgD,EAAA7B,IAML,IANK,uDAUL+B,GAJA/B,gIC9CK,MAAAqB,WAAAmL,4CCJA,SAASC,EACdC,EACAC,GAGA,MAAiC,oBAAtBD,EACFA,KAAqBC,KAGrBD,CACV,wDCCD,IAAIjO,EAAQmO,EAAQ,OAIpB,IAAIC,EAAW,oBAAsBrH,OAAOsH,GAAKtH,OAAOsH,GAHxD,SAAYC,EAAGC,GACb,OAAQD,IAAMC,IAAM,IAAMD,GAAK,EAAIA,IAAM,EAAIC,IAAQD,IAAMA,GAAKC,IAAMA,CACxE,EAEEC,EAAWxO,EAAMwO,SACjBC,EAAYzO,EAAMyO,UAClBC,EAAkB1O,EAAM0O,gBACxBC,EAAgB3O,EAAM2O,cA0BxB,SAASC,EAAuBC,GAC9B,IAAIC,EAAoBD,EAAKE,YAC7BF,EAAOA,EAAKhB,MACZ,IACE,IAAImB,EAAYF,IAChB,OAAQV,EAASS,EAAMG,EACzB,CAAE,MAAOpN,GACP,OAAO,CACT,CACF,CAIA,IAAIqN,EACF,qBAAuBC,QACvB,qBAAuBA,OAAOC,UAC9B,qBAAuBD,OAAOC,SAASC,cANzC,SAAgCrM,EAAWgM,GACzC,OAAOA,GACT,EArCA,SAAgChM,EAAWgM,GACzC,IAAIlB,EAAQkB,IACVM,EAAYb,EAAS,CAAEK,KAAM,CAAEhB,MAAOA,EAAOkB,YAAaA,KAC1DF,EAAOQ,EAAU,GAAGR,KACpBS,EAAcD,EAAU,GAmB1B,OAlBAX,GACE,WACEG,EAAKhB,MAAQA,EACbgB,EAAKE,YAAcA,EACnBH,EAAuBC,IAASS,EAAY,CAAET,KAAMA,GACtD,GACA,CAAC9L,EAAW8K,EAAOkB,IAErBN,GACE,WAEE,OADAG,EAAuBC,IAASS,EAAY,CAAET,KAAMA,IAC7C9L,GAAU,WACf6L,EAAuBC,IAASS,EAAY,CAAET,KAAMA,GACtD,GACF,GACA,CAAC9L,IAEH4L,EAAcd,GACPA,CACT,EAoBA/N,EAAQ8C,0BACN,IAAW5C,EAAM4C,qBAAuB5C,EAAM4C,qBAAuBqM", "sources": ["../node_modules/use-sync-external-store/shim/index.js", "../node_modules/@tanstack/react-query/src/isRestoring.tsx", "../node_modules/@tanstack/react-query/src/QueryErrorResetBoundary.tsx", "../node_modules/@tanstack/react-query/src/suspense.ts", "../node_modules/@tanstack/react-query/src/useBaseQuery.ts", "../node_modules/@tanstack/query-core/src/queryObserver.ts", "../node_modules/@tanstack/react-query/src/errorBoundaryUtils.ts", "../node_modules/@tanstack/react-query/src/useSyncExternalStore.ts", "../node_modules/@tanstack/react-query/src/utils.ts", "../node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "'use client'\nimport * as React from 'react'\n\nconst IsRestoringContext = React.createContext(false)\n\nexport const useIsRestoring = () => React.useContext(IsRestoringContext)\nexport const IsRestoringProvider = IsRestoringContext.Provider\n", "'use client'\nimport * as React from 'react'\n\n// CONTEXT\n\nexport interface QueryErrorResetBoundaryValue {\n  clearReset: () => void\n  isReset: () => boolean\n  reset: () => void\n}\n\nfunction createValue(): QueryErrorResetBoundaryValue {\n  let isReset = false\n  return {\n    clearReset: () => {\n      isReset = false\n    },\n    reset: () => {\n      isReset = true\n    },\n    isReset: () => {\n      return isReset\n    },\n  }\n}\n\nconst QueryErrorResetBoundaryContext = React.createContext(createValue())\n\n// HOOK\n\nexport const useQueryErrorResetBoundary = () =>\n  React.useContext(QueryErrorResetBoundaryContext)\n\n// COMPONENT\n\nexport interface QueryErrorResetBoundaryProps {\n  children:\n    | ((value: QueryErrorResetBoundaryValue) => React.ReactNode)\n    | React.ReactNode\n}\n\nexport const QueryErrorResetBoundary = ({\n  children,\n}: QueryErrorResetBoundaryProps) => {\n  const [value] = React.useState(() => createValue())\n  return (\n    <QueryErrorResetBoundaryContext.Provider value={value}>\n      {typeof children === 'function'\n        ? (children as Function)(value)\n        : children}\n    </QueryErrorResetBoundaryContext.Provider>\n  )\n}\n", "import type { DefaultedQueryObserverOptions } from '@tanstack/query-core'\nimport type { QueryObserver } from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\nimport type { QueryObserverResult } from '@tanstack/query-core'\nimport type { QueryKey } from '@tanstack/query-core'\n\nexport const ensureStaleTime = (\n  defaultedOptions: DefaultedQueryObserverOptions<any, any, any, any, any>,\n) => {\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000\n    }\n  }\n}\n\nexport const willFetch = (\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => result.isLoading && result.isFetching && !isRestoring\n\nexport const shouldSuspend = (\n  defaultedOptions:\n    | DefaultedQueryObserverOptions<any, any, any, any, any>\n    | undefined,\n  result: QueryObserverResult<any, any>,\n  isRestoring: boolean,\n) => defaultedOptions?.suspense && willFetch(result, isRestoring)\n\nexport const fetchOptimistic = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  defaultedOptions: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  observer: QueryObserver<TQueryFnData, TError, TData, TQueryData, TQueryKey>,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) =>\n  observer\n    .fetchOptimistic(defaultedOptions)\n    .then(({ data }) => {\n      defaultedOptions.onSuccess?.(data as TData)\n      defaultedOptions.onSettled?.(data, null)\n    })\n    .catch((error) => {\n      errorResetBoundary.clearReset()\n      defaultedOptions.onError?.(error)\n      defaultedOptions.onSettled?.(undefined, error)\n    })\n", "import 'client-only'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport type { Query<PERSON>ey, QueryObserver } from '@tanstack/query-core'\nimport { notifyManager } from '@tanstack/query-core'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { UseBaseQueryOptions } from './types'\nimport { useIsRestoring } from './isRestoring'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport { ensureStaleTime, shouldSuspend, fetchOptimistic } from './suspense'\n\nexport function useBaseQuery<\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  <PERSON><PERSON><PERSON>y<PERSON><PERSON> extends QueryKey,\n>(\n  options: UseBaseQueryOptions<\n    TQueryFnData,\n    T<PERSON><PERSON>r,\n    TData,\n    TQueryData,\n    TQueryK<PERSON>\n  >,\n  Observer: typeof QueryObserver,\n) {\n  const queryClient = useQueryClient({ context: options.context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n  const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n  // Make sure results are optimistically set in fetching state before subscribing or updating options\n  defaultedOptions._optimisticResults = isRestoring\n    ? 'isRestoring'\n    : 'optimistic'\n\n  // Include callbacks in batch renders\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(\n      defaultedOptions.onError,\n    )\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(\n      defaultedOptions.onSuccess,\n    )\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(\n      defaultedOptions.onSettled,\n    )\n  }\n\n  ensureStaleTime(defaultedOptions)\n  ensurePreventErrorBoundaryRetry(defaultedOptions, errorResetBoundary)\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () =>\n      new Observer<TQueryFnData, TError, TData, TQueryData, TQueryKey>(\n        queryClient,\n        defaultedOptions,\n      ),\n  )\n\n  const result = observer.getOptimisticResult(defaultedOptions)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) => {\n        const unsubscribe = isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange))\n\n        // Update result to make sure we did not miss any query updates\n        // between creating the observer and subscribing to it.\n        observer.updateResult()\n\n        return unsubscribe\n      },\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, { listeners: false })\n  }, [defaultedOptions, observer])\n\n  // Handle suspense\n  if (shouldSuspend(defaultedOptions, result, isRestoring)) {\n    throw fetchOptimistic(defaultedOptions, observer, errorResetBoundary)\n  }\n\n  // Handle error boundary\n  if (\n    getHasError({\n      result,\n      errorResetBoundary,\n      useErrorBoundary: defaultedOptions.useErrorBoundary,\n      query: observer.getCurrentQuery(),\n    })\n  ) {\n    throw result.error\n  }\n\n  // Handle result property usage tracking\n  return !defaultedOptions.notifyOnChangeProps\n    ? observer.trackResult(result)\n    : result\n}\n", "import type { DefaultedQueryObserverOptions, RefetchPageFilters } from './types'\nimport {\n  isServer,\n  isValidTimeout,\n  noop,\n  replaceData,\n  shallowEqualObjects,\n  timeUntilStale,\n} from './utils'\nimport { notifyManager } from './notifyManager'\nimport type {\n  PlaceholderDataFunction,\n  QueryKey,\n  QueryObserverBaseResult,\n  QueryObserverOptions,\n  QueryObserverResult,\n  QueryOptions,\n  RefetchOptions,\n} from './types'\nimport type { Query, QueryState, Action, FetchOptions } from './query'\nimport type { QueryClient } from './queryClient'\nimport { focusManager } from './focusManager'\nimport { Subscribable } from './subscribable'\nimport { canFetch, isCancelledError } from './retryer'\n\ntype QueryObserverListener<TData, TError> = (\n  result: QueryObserverResult<TData, TError>,\n) => void\n\nexport interface NotifyOptions {\n  cache?: boolean\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\nexport interface ObserverFetchOptions extends FetchOptions {\n  throwOnError?: boolean\n}\n\nexport class QueryObserver<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> extends Subscribable<QueryObserverListener<TData, TError>> {\n  options: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n\n  private client: QueryClient\n  private currentQuery!: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n  private currentQueryInitialState!: QueryState<TQueryData, TError>\n  private currentResult!: QueryObserverResult<TData, TError>\n  private currentResultState?: QueryState<TQueryData, TError>\n  private currentResultOptions?: QueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >\n  private previousQueryResult?: QueryObserverResult<TData, TError>\n  private selectError: TError | null\n  private selectFn?: (data: TQueryData) => TData\n  private selectResult?: TData\n  private staleTimeoutId?: ReturnType<typeof setTimeout>\n  private refetchIntervalId?: ReturnType<typeof setInterval>\n  private currentRefetchInterval?: number | false\n  private trackedProps!: Set<keyof QueryObserverResult>\n\n  constructor(\n    client: QueryClient,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ) {\n    super()\n\n    this.client = client\n    this.options = options\n    this.trackedProps = new Set()\n    this.selectError = null\n    this.bindMethods()\n    this.setOptions(options)\n  }\n\n  protected bindMethods(): void {\n    this.remove = this.remove.bind(this)\n    this.refetch = this.refetch.bind(this)\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.currentQuery.addObserver(this)\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch()\n      }\n\n      this.updateTimers()\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.destroy()\n    }\n  }\n\n  shouldFetchOnReconnect(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnReconnect,\n    )\n  }\n\n  shouldFetchOnWindowFocus(): boolean {\n    return shouldFetchOn(\n      this.currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus,\n    )\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.clearStaleTimeout()\n    this.clearRefetchInterval()\n    this.currentQuery.removeObserver(this)\n  }\n\n  setOptions(\n    options?: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n    notifyOptions?: NotifyOptions,\n  ): void {\n    const prevOptions = this.options\n    const prevQuery = this.currentQuery\n\n    this.options = this.client.defaultQueryOptions(options)\n\n    if (\n      process.env.NODE_ENV !== 'production' &&\n      typeof options?.isDataEqual !== 'undefined'\n    ) {\n      this.client\n        .getLogger()\n        .error(\n          `The isDataEqual option has been deprecated and will be removed in the next major version. You can achieve the same functionality by passing a function as the structuralSharing option`,\n        )\n    }\n\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getQueryCache().notify({\n        type: 'observerOptionsUpdated',\n        query: this.currentQuery,\n        observer: this,\n      })\n    }\n\n    if (\n      typeof this.options.enabled !== 'undefined' &&\n      typeof this.options.enabled !== 'boolean'\n    ) {\n      throw new Error('Expected enabled to be a boolean')\n    }\n\n    // Keep previous query key if the user does not supply one\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey\n    }\n\n    this.updateQuery()\n\n    const mounted = this.hasListeners()\n\n    // Fetch if there are subscribers\n    if (\n      mounted &&\n      shouldFetchOptionally(\n        this.currentQuery,\n        prevQuery,\n        this.options,\n        prevOptions,\n      )\n    ) {\n      this.executeFetch()\n    }\n\n    // Update result\n    this.updateResult(notifyOptions)\n\n    // Update stale interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        this.options.staleTime !== prevOptions.staleTime)\n    ) {\n      this.updateStaleTimeout()\n    }\n\n    const nextRefetchInterval = this.computeRefetchInterval()\n\n    // Update refetch interval if needed\n    if (\n      mounted &&\n      (this.currentQuery !== prevQuery ||\n        this.options.enabled !== prevOptions.enabled ||\n        nextRefetchInterval !== this.currentRefetchInterval)\n    ) {\n      this.updateRefetchInterval(nextRefetchInterval)\n    }\n  }\n\n  getOptimisticResult(\n    options: DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const query = this.client.getQueryCache().build(this.client, options)\n\n    return this.createResult(query, options)\n  }\n\n  getCurrentResult(): QueryObserverResult<TData, TError> {\n    return this.currentResult\n  }\n\n  trackResult(\n    result: QueryObserverResult<TData, TError>,\n  ): QueryObserverResult<TData, TError> {\n    const trackedResult = {} as QueryObserverResult<TData, TError>\n\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackedProps.add(key as keyof QueryObserverResult)\n          return result[key as keyof QueryObserverResult]\n        },\n      })\n    })\n\n    return trackedResult\n  }\n\n  getCurrentQuery(): Query<TQueryFnData, TError, TQueryData, TQueryKey> {\n    return this.currentQuery\n  }\n\n  remove(): void {\n    this.client.getQueryCache().remove(this.currentQuery)\n  }\n\n  refetch<TPageData>({\n    refetchPage,\n    ...options\n  }: RefetchOptions & RefetchPageFilters<TPageData> = {}): Promise<\n    QueryObserverResult<TData, TError>\n  > {\n    return this.fetch({\n      ...options,\n      meta: { refetchPage },\n    })\n  }\n\n  fetchOptimistic(\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    const defaultedOptions = this.client.defaultQueryOptions(options)\n\n    const query = this.client\n      .getQueryCache()\n      .build(this.client, defaultedOptions)\n    query.isFetchingOptimistic = true\n\n    return query.fetch().then(() => this.createResult(query, defaultedOptions))\n  }\n\n  protected fetch(\n    fetchOptions: ObserverFetchOptions,\n  ): Promise<QueryObserverResult<TData, TError>> {\n    return this.executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true,\n    }).then(() => {\n      this.updateResult()\n      return this.currentResult\n    })\n  }\n\n  private executeFetch(\n    fetchOptions?: ObserverFetchOptions,\n  ): Promise<TQueryData | undefined> {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery()\n\n    // Fetch\n    let promise: Promise<TQueryData | undefined> = this.currentQuery.fetch(\n      this.options as QueryOptions<TQueryFnData, TError, TQueryData, TQueryKey>,\n      fetchOptions,\n    )\n\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop)\n    }\n\n    return promise\n  }\n\n  private updateStaleTimeout(): void {\n    this.clearStaleTimeout()\n\n    if (\n      isServer ||\n      this.currentResult.isStale ||\n      !isValidTimeout(this.options.staleTime)\n    ) {\n      return\n    }\n\n    const time = timeUntilStale(\n      this.currentResult.dataUpdatedAt,\n      this.options.staleTime,\n    )\n\n    // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n    const timeout = time + 1\n\n    this.staleTimeoutId = setTimeout(() => {\n      if (!this.currentResult.isStale) {\n        this.updateResult()\n      }\n    }, timeout)\n  }\n\n  private computeRefetchInterval() {\n    return typeof this.options.refetchInterval === 'function'\n      ? this.options.refetchInterval(this.currentResult.data, this.currentQuery)\n      : this.options.refetchInterval ?? false\n  }\n\n  private updateRefetchInterval(nextInterval: number | false): void {\n    this.clearRefetchInterval()\n\n    this.currentRefetchInterval = nextInterval\n\n    if (\n      isServer ||\n      this.options.enabled === false ||\n      !isValidTimeout(this.currentRefetchInterval) ||\n      this.currentRefetchInterval === 0\n    ) {\n      return\n    }\n\n    this.refetchIntervalId = setInterval(() => {\n      if (\n        this.options.refetchIntervalInBackground ||\n        focusManager.isFocused()\n      ) {\n        this.executeFetch()\n      }\n    }, this.currentRefetchInterval)\n  }\n\n  private updateTimers(): void {\n    this.updateStaleTimeout()\n    this.updateRefetchInterval(this.computeRefetchInterval())\n  }\n\n  private clearStaleTimeout(): void {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId)\n      this.staleTimeoutId = undefined\n    }\n  }\n\n  private clearRefetchInterval(): void {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId)\n      this.refetchIntervalId = undefined\n    }\n  }\n\n  protected createResult(\n    query: Query<TQueryFnData, TError, TQueryData, TQueryKey>,\n    options: QueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >,\n  ): QueryObserverResult<TData, TError> {\n    const prevQuery = this.currentQuery\n    const prevOptions = this.options\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n    const prevResultState = this.currentResultState\n    const prevResultOptions = this.currentResultOptions\n    const queryChange = query !== prevQuery\n    const queryInitialState = queryChange\n      ? query.state\n      : this.currentQueryInitialState\n    const prevQueryResult = queryChange\n      ? this.currentResult\n      : this.previousQueryResult\n\n    const { state } = query\n    let { dataUpdatedAt, error, errorUpdatedAt, fetchStatus, status } = state\n    let isPreviousData = false\n    let isPlaceholderData = false\n    let data: TData | undefined\n\n    // Optimistically set result in fetching state if needed\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners()\n\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options)\n\n      const fetchOptionally =\n        mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions)\n\n      if (fetchOnMount || fetchOptionally) {\n        fetchStatus = canFetch(query.options.networkMode)\n          ? 'fetching'\n          : 'paused'\n        if (!dataUpdatedAt) {\n          status = 'loading'\n        }\n      }\n      if (options._optimisticResults === 'isRestoring') {\n        fetchStatus = 'idle'\n      }\n    }\n\n    // Keep previous data if needed\n    if (\n      options.keepPreviousData &&\n      !state.dataUpdatedAt &&\n      prevQueryResult?.isSuccess &&\n      status !== 'error'\n    ) {\n      data = prevQueryResult.data\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt\n      status = prevQueryResult.status\n      isPreviousData = true\n    }\n    // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n      // Memoize select result\n      if (\n        prevResult &&\n        state.data === prevResultState?.data &&\n        options.select === this.selectFn\n      ) {\n        data = this.selectResult\n      } else {\n        try {\n          this.selectFn = options.select\n          data = options.select(state.data)\n          data = replaceData(prevResult?.data, data, options)\n          this.selectResult = data\n          this.selectError = null\n        } catch (selectError) {\n          if (process.env.NODE_ENV !== 'production') {\n            this.client.getLogger().error(selectError)\n          }\n          this.selectError = selectError as TError\n        }\n      }\n    }\n    // Use query data\n    else {\n      data = state.data as unknown as TData\n    }\n\n    // Show placeholder data if needed\n    if (\n      typeof options.placeholderData !== 'undefined' &&\n      typeof data === 'undefined' &&\n      status === 'loading'\n    ) {\n      let placeholderData\n\n      // Memoize placeholder data\n      if (\n        prevResult?.isPlaceholderData &&\n        options.placeholderData === prevResultOptions?.placeholderData\n      ) {\n        placeholderData = prevResult.data\n      } else {\n        placeholderData =\n          typeof options.placeholderData === 'function'\n            ? (options.placeholderData as PlaceholderDataFunction<TQueryData>)()\n            : options.placeholderData\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData)\n            this.selectError = null\n          } catch (selectError) {\n            if (process.env.NODE_ENV !== 'production') {\n              this.client.getLogger().error(selectError)\n            }\n            this.selectError = selectError as TError\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success'\n        data = replaceData(prevResult?.data, placeholderData, options) as TData\n        isPlaceholderData = true\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError as any\n      data = this.selectResult\n      errorUpdatedAt = Date.now()\n      status = 'error'\n    }\n\n    const isFetching = fetchStatus === 'fetching'\n    const isLoading = status === 'loading'\n    const isError = status === 'error'\n\n    const result: QueryObserverBaseResult<TData, TError> = {\n      status,\n      fetchStatus,\n      isLoading,\n      isSuccess: status === 'success',\n      isError,\n      isInitialLoading: isLoading && isFetching,\n      data,\n      dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      failureReason: state.fetchFailureReason,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount:\n        state.dataUpdateCount > queryInitialState.dataUpdateCount ||\n        state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isLoading,\n      isLoadingError: isError && state.dataUpdatedAt === 0,\n      isPaused: fetchStatus === 'paused',\n      isPlaceholderData,\n      isPreviousData,\n      isRefetchError: isError && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove,\n    }\n\n    return result as QueryObserverResult<TData, TError>\n  }\n\n  updateResult(notifyOptions?: NotifyOptions): void {\n    const prevResult = this.currentResult as\n      | QueryObserverResult<TData, TError>\n      | undefined\n\n    const nextResult = this.createResult(this.currentQuery, this.options)\n    this.currentResultState = this.currentQuery.state\n    this.currentResultOptions = this.options\n\n    // Only notify and update result if something has changed\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return\n    }\n\n    this.currentResult = nextResult\n\n    // Determine which callbacks to trigger\n    const defaultNotifyOptions: NotifyOptions = { cache: true }\n\n    const shouldNotifyListeners = (): boolean => {\n      if (!prevResult) {\n        return true\n      }\n\n      const { notifyOnChangeProps } = this.options\n\n      if (\n        notifyOnChangeProps === 'all' ||\n        (!notifyOnChangeProps && !this.trackedProps.size)\n      ) {\n        return true\n      }\n\n      const includedProps = new Set(notifyOnChangeProps ?? this.trackedProps)\n\n      if (this.options.useErrorBoundary) {\n        includedProps.add('error')\n      }\n\n      return Object.keys(this.currentResult).some((key) => {\n        const typedKey = key as keyof QueryObserverResult\n        const changed = this.currentResult[typedKey] !== prevResult[typedKey]\n        return changed && includedProps.has(typedKey)\n      })\n    }\n\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true\n    }\n\n    this.notify({ ...defaultNotifyOptions, ...notifyOptions })\n  }\n\n  private updateQuery(): void {\n    const query = this.client.getQueryCache().build(this.client, this.options)\n\n    if (query === this.currentQuery) {\n      return\n    }\n\n    const prevQuery = this.currentQuery as\n      | Query<TQueryFnData, TError, TQueryData, TQueryKey>\n      | undefined\n    this.currentQuery = query\n    this.currentQueryInitialState = query.state\n    this.previousQueryResult = this.currentResult\n\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this)\n      query.addObserver(this)\n    }\n  }\n\n  onQueryUpdate(action: Action<TData, TError>): void {\n    const notifyOptions: NotifyOptions = {}\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = !action.manual\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true\n    }\n\n    this.updateResult(notifyOptions)\n\n    if (this.hasListeners()) {\n      this.updateTimers()\n    }\n  }\n\n  private notify(notifyOptions: NotifyOptions): void {\n    notifyManager.batch(() => {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        this.options.onSuccess?.(this.currentResult.data!)\n        this.options.onSettled?.(this.currentResult.data!, null)\n      } else if (notifyOptions.onError) {\n        this.options.onError?.(this.currentResult.error!)\n        this.options.onSettled?.(undefined, this.currentResult.error!)\n      }\n\n      // Then trigger the listeners\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n\n      // Then the cache listeners\n      if (notifyOptions.cache) {\n        this.client.getQueryCache().notify({\n          query: this.currentQuery,\n          type: 'observerResultsUpdated',\n        })\n      }\n    })\n  }\n}\n\nfunction shouldLoadOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    !query.state.dataUpdatedAt &&\n    !(query.state.status === 'error' && options.retryOnMount === false)\n  )\n}\n\nfunction shouldFetchOnMount(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    shouldLoadOnMount(query, options) ||\n    (query.state.dataUpdatedAt > 0 &&\n      shouldFetchOn(query, options, options.refetchOnMount))\n  )\n}\n\nfunction shouldFetchOn(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  field: typeof options['refetchOnMount'] &\n    typeof options['refetchOnWindowFocus'] &\n    typeof options['refetchOnReconnect'],\n) {\n  if (options.enabled !== false) {\n    const value = typeof field === 'function' ? field(query) : field\n\n    return value === 'always' || (value !== false && isStale(query, options))\n  }\n  return false\n}\n\nfunction shouldFetchOptionally(\n  query: Query<any, any, any, any>,\n  prevQuery: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n  prevOptions: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return (\n    options.enabled !== false &&\n    (query !== prevQuery || prevOptions.enabled === false) &&\n    (!options.suspense || query.state.status !== 'error') &&\n    isStale(query, options)\n  )\n}\n\nfunction isStale(\n  query: Query<any, any, any, any>,\n  options: QueryObserverOptions<any, any, any, any, any>,\n): boolean {\n  return query.isStaleByTime(options.staleTime)\n}\n", "'use client'\nimport type {\n  DefaultedQueryObserverOptions,\n  Query,\n  QueryKey,\n  QueryObserverResult,\n  UseErrorBoundary,\n} from '@tanstack/query-core'\nimport type { QueryErrorResetBoundaryValue } from './QueryErrorResetBoundary'\nimport * as React from 'react'\nimport { shouldThrowError } from './utils'\n\nexport const ensurePreventErrorBoundaryRetry = <\n  TQueryFnData,\n  TError,\n  TData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>(\n  options: DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  >,\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  if (options.suspense || options.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      options.retryOnMount = false\n    }\n  }\n}\n\nexport const useClearResetErrorBoundary = (\n  errorResetBoundary: QueryErrorResetBoundaryValue,\n) => {\n  React.useEffect(() => {\n    errorResetBoundary.clearReset()\n  }, [errorResetBoundary])\n}\n\nexport const getHasError = <\n  TData,\n  TError,\n  TQueryFnData,\n  TQueryData,\n  TQueryKey extends QueryKey,\n>({\n  result,\n  errorResetBoundary,\n  useErrorBoundary,\n  query,\n}: {\n  result: QueryObserverResult<TData, TError>\n  errorResetBoundary: QueryErrorResetBoundaryValue\n  useErrorBoundary: UseErrorBoundary<\n    TQueryFnData,\n    TError,\n    TQueryData,\n    TQueryKey\n  >\n  query: Query<TQueryFnData, TError, TQueryData, TQueryKey>\n}) => {\n  return (\n    result.isError &&\n    !errorResetBoundary.isReset() &&\n    !result.isFetching &&\n    shouldThrowError(useErrorBoundary, [result.error, query])\n  )\n}\n", "'use client'\n// Temporary workaround due to an issue with react-native uSES - https://github.com/TanStack/query/pull/3601\nimport { useSyncExternalStore as uSES } from 'use-sync-external-store/shim/index.js'\n\nexport const useSyncExternalStore = uSES\n", "export function shouldThrowError<T extends (...args: any[]) => boolean>(\n  _useErrorBoundary: boolean | T | undefined,\n  params: Parameters<T>,\n): boolean {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary(...params)\n  }\n\n  return !!_useErrorBoundary\n}\n", "/**\n * @license React\n * use-sync-external-store-shim.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar React = require(\"react\");\nfunction is(x, y) {\n  return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\nvar objectIs = \"function\" === typeof Object.is ? Object.is : is,\n  useState = React.useState,\n  useEffect = React.useEffect,\n  useLayoutEffect = React.useLayoutEffect,\n  useDebugValue = React.useDebugValue;\nfunction useSyncExternalStore$2(subscribe, getSnapshot) {\n  var value = getSnapshot(),\n    _useState = useState({ inst: { value: value, getSnapshot: getSnapshot } }),\n    inst = _useState[0].inst,\n    forceUpdate = _useState[1];\n  useLayoutEffect(\n    function () {\n      inst.value = value;\n      inst.getSnapshot = getSnapshot;\n      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n    },\n    [subscribe, value, getSnapshot]\n  );\n  useEffect(\n    function () {\n      checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n      return subscribe(function () {\n        checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n      });\n    },\n    [subscribe]\n  );\n  useDebugValue(value);\n  return value;\n}\nfunction checkIfSnapshotChanged(inst) {\n  var latestGetSnapshot = inst.getSnapshot;\n  inst = inst.value;\n  try {\n    var nextValue = latestGetSnapshot();\n    return !objectIs(inst, nextValue);\n  } catch (error) {\n    return !0;\n  }\n}\nfunction useSyncExternalStore$1(subscribe, getSnapshot) {\n  return getSnapshot();\n}\nvar shim =\n  \"undefined\" === typeof window ||\n  \"undefined\" === typeof window.document ||\n  \"undefined\" === typeof window.document.createElement\n    ? useSyncExternalStore$1\n    : useSyncExternalStore$2;\nexports.useSyncExternalStore =\n  void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n"], "names": ["module", "exports", "IsRestoringContext", "React", "useIsRestoring", "Provider", "createValue", "clear<PERSON><PERSON>t", "isReset", "reset", "QueryErrorResetBoundaryContext", "useQueryErrorResetBoundary", "ensureStaleTime", "defaultedOptions", "suspense", "staleTime", "<PERSON><PERSON><PERSON><PERSON>", "result", "isRestoring", "isLoading", "isFetching", "shouldSuspend", "fetchOptimistic", "observer", "errorResetBoundary", "then", "_ref", "data", "onSuccess", "onSettled", "catch", "error", "onError", "undefined", "useBaseQuery", "options", "Observer", "queryClient", "useQueryClient", "context", "defaultQueryOptions", "_optimisticResults", "notify<PERSON><PERSON>ger", "batchCalls", "ensurePreventErrorBoundaryRetry", "useClearResetErrorBoundary", "getOptimisticResult", "useSyncExternalStore", "onStoreChange", "unsubscribe", "subscribe", "updateResult", "getCurrentResult", "setOptions", "listeners", "getHasError", "useErrorBoundary", "query", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notifyOnChangeProps", "trackResult", "QueryObserver", "Subscribable", "constructor", "client", "super", "this", "trackedProps", "Set", "selectError", "bindMethods", "remove", "bind", "refetch", "onSubscribe", "size", "<PERSON><PERSON><PERSON><PERSON>", "addObserver", "shouldFetchOnMount", "executeFetch", "updateTimers", "onUnsubscribe", "hasListeners", "destroy", "shouldFetchOnReconnect", "shouldFetchOn", "refetchOnReconnect", "shouldFetchOnWindowFocus", "refetchOnWindowFocus", "clearStaleTimeout", "clearRefetchInterval", "removeObserver", "notifyOptions", "prevOptions", "prev<PERSON><PERSON><PERSON>", "shallowEqualObjects", "get<PERSON><PERSON><PERSON><PERSON>ache", "notify", "type", "enabled", "Error", "query<PERSON><PERSON>", "updateQuery", "mounted", "shouldFetchOptionally", "updateStaleTimeout", "nextRefetchInterval", "computeRefetchInterval", "currentRefetchInterval", "updateRefetchInterval", "build", "createResult", "currentResult", "trackedResult", "Object", "keys", "for<PERSON>ach", "key", "defineProperty", "configurable", "enumerable", "get", "add", "refetchPage", "arguments", "length", "fetch", "meta", "isFetchingOptimistic", "fetchOptions", "_fetchOptions$cancelR", "cancelRefetch", "promise", "throwOnError", "noop", "isServer", "isStale", "isValidTimeout", "timeout", "timeUntilStale", "dataUpdatedAt", "staleTimeoutId", "setTimeout", "_this$options$refetch", "refetchInterval", "nextInterval", "refetchIntervalId", "setInterval", "refetchIntervalInBackground", "focusManager", "isFocused", "clearTimeout", "clearInterval", "prevResult", "prevResultState", "currentResultState", "prevResultOptions", "currentResultOptions", "query<PERSON>hange", "queryInitialState", "state", "currentQueryInitialState", "prevQueryResult", "previousQueryResult", "errorUpdatedAt", "fetchStatus", "status", "isPreviousData", "isPlaceholderData", "fetchOnMount", "fetchOptionally", "canFetch", "networkMode", "keepPreviousData", "isSuccess", "select", "selectFn", "selectResult", "replaceData", "process", "placeholderData", "Date", "now", "isError", "isInitialLoading", "failureCount", "fetchFailureCount", "failureReason", "fetchFailureReason", "errorUpdateCount", "isFetched", "dataUpdateCount", "isFetchedAfterMount", "isRefetching", "isLoadingError", "isPaused", "isRefetchError", "nextResult", "defaultNotifyOptions", "cache", "shouldNotifyListeners", "includedProps", "some", "<PERSON><PERSON><PERSON>", "has", "onQueryUpdate", "action", "manual", "isCancelledError", "batch", "_this$options$onSucce", "_this$options", "_this$options$onSettl", "_this$options2", "call", "_this$options$onError", "_this$options3", "_this$options$onSettl2", "_this$options4", "listener", "retryOnMount", "shouldLoadOnMount", "refetchOnMount", "field", "value", "isStaleByTime", "useSyncExternalStore$1", "shouldThrowError", "_useErrorBoundary", "params", "require", "objectIs", "is", "x", "y", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "checkIfSnapshotChanged", "inst", "latestGetSnapshot", "getSnapshot", "nextValue", "shim", "window", "document", "createElement", "_useState", "forceUpdate"], "sourceRoot": ""}