"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[4918],{7871:function(e,t,r){r.d(t,{e:function(){return g}});var s=r(31014),n=r(88464),i=r(88443),a=r(48012),o=r(32599),l=r(79445),c=r(50111);const d="param-";var u={name:"1vbruv4",styles:"width:100%;margin-bottom:16px"};const h=e=>{let{id:t,onChange:r,paramKeys:s,metricKeys:i}=e;const o=(0,n.A)();return(0,c.FD)(a.XAe,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_comparerunbox.tsx_46",id:t,css:u,placeholder:o.formatMessage({id:"9dX4XQ",defaultMessage:"Select parameter or metric"}),onChange:e=>{let{target:t}=e;const{value:s}=t,[n,i]=s.split("-"),a=s.startsWith(d);r({key:i,isParam:a})},children:[(0,c.Y)(a.lQN,{label:"Parameters",children:s.map((e=>(0,c.Y)(a.wS0,{value:d+e,children:e},e)))}),(0,c.Y)(a.lQN,{label:"Metrics",children:i.map((e=>(0,c.Y)(a.wS0,{value:"metric-"+e,children:e},e)))})]})};var p={name:"t6p6gz",styles:"display:flex;width:100%;height:100%;justify-content:center;align-items:center"},m={name:"wo4y18",styles:"width:100%;height:100%;min-height:35vw"};const g=e=>{let{runInfos:t,metricLists:r,paramLists:n}=e;const[d,u]=(0,s.useState)({key:void 0,isParam:void 0}),[g,f]=(0,s.useState)({key:void 0,isParam:void 0}),v=Array.from(new Set(n.flat().map((e=>{let{key:t}=e;return t})))).sort(),A=Array.from(new Set(r.flat().map((e=>{let{key:t}=e;return t})))).sort(),x=()=>{const e={};return t.forEach(((t,s)=>{const i=n[s],a=r[s],o=(d.isParam?i:a).find((e=>{let{key:t}=e;return t===d.key})),l=(g.isParam?i:a).find((e=>{let{key:t}=e;return t===g.key}));void 0!==o&&void 0!==l&&(o.value in e?e[o.value].push(l.value):e[o.value]=[l.value])})),Object.entries(e).map((e=>{let[t,r]=e;return{y:r,type:"box",name:t,jitter:.3,pointpos:-1.5,boxpoints:"all"}}))};return(0,c.FD)(a.fI1,{children:[(0,c.Y)(a.fvL,{span:6,children:(0,c.FD)("div",{css:y.borderSpacer,children:[(0,c.Y)("div",{children:(0,c.Y)(a.D$Q.Label,{htmlFor:"x-axis-selector",children:(0,c.Y)(i.A,{id:"8xji5J",defaultMessage:"X-axis:"})})}),(0,c.Y)(h,{id:"x-axis-selector",onChange:u,paramKeys:v,metricKeys:A}),(0,c.Y)("div",{children:(0,c.Y)(a.D$Q.Label,{htmlFor:"y-axis-selector",children:(0,c.Y)(i.A,{id:"Hk0RSH",defaultMessage:"Y-axis:"})})}),(0,c.Y)(h,{id:"y-axis-selector",onChange:f,paramKeys:v,metricKeys:A})]})}),(0,c.Y)(a.fvL,{span:18,children:d.key&&g.key?(0,c.Y)(l.W,{css:m,data:x(),layout:{margin:{t:30},hovermode:"closest",xaxis:{title:d.key},yaxis:{title:g.key}},config:{responsive:!0,displaylogo:!1,scrollZoom:!0,modeBarButtonsToRemove:["sendDataToCloud","select2d","lasso2d","resetScale2d","hoverClosestCartesian","hoverCompareCartesian"]},useResizeHandler:!0}):(0,c.Y)("div",{css:p,children:(0,c.Y)(o.T.Text,{size:"xl",children:(0,c.Y)(i.A,{id:"XaD7FM",defaultMessage:"Select parameters/metrics to plot."})})})})]})},y={borderSpacer:e=>({paddingLeft:e.spacing.xs})}},20109:function(e,t,r){r.d(t,{X:function(){return d}});r(31014);var s=r(28486),n=r(48012),i=r(32599),a=r(88443),o=r(50111);function l(){return(0,o.Y)(n.SvL,{"data-testid":"fallback",title:(0,o.Y)(a.A,{id:"qAdWdK",defaultMessage:"Error"}),description:(0,o.Y)(a.A,{id:"RzZVxC",defaultMessage:"An error occurred while rendering this component."}),image:(0,o.Y)(i.j,{})})}function c(e){let{children:t,customFallbackComponent:r}=e;function n(e,t){console.error("Caught Unexpected Error: ",e,t.componentStack)}return r?(0,o.Y)(s.tH,{onError:n,FallbackComponent:r,children:t}):(0,o.Y)(s.tH,{onError:n,fallback:(0,o.Y)(l,{}),children:t})}function d(e,t,r,s){return function(e){return(0,o.Y)(c,{customFallbackComponent:s,children:(0,o.Y)(t,{...e})})}}},24406:function(e,t,r){r.d(t,{A:function(){return s}});class s{static findInList(e,t){let r;return e.forEach((e=>{e.key===t&&(r=e)})),r}static getKeys(e,t){const r={};return e.forEach((e=>e.forEach((e=>{e.key in r||(r[e.key]=!0),t&&isNaN(parseFloat(e.value))&&(r[e.key]=!1)})))),Object.keys(r).filter((e=>r[e])).sort()}}},25869:function(e,t,r){r.d(t,{h:function(){return i}});r(31014);var s=r(93215),n=r(50111);const i=e=>t=>{const r=(0,s.zy)(),i=(0,s.Zp)(),a=(0,s.g)();return(0,n.Y)(e,{params:a,location:r,navigate:i,...t})}},27705:function(e,t,r){r.d(t,{g:function(){return a}});var s=r(31014),n=r(76010),i=r(50111);class a extends s.Component{constructor(){super(...arguments),this.state={error:null}}componentDidCatch(e,t){this.setState({error:e}),console.error(e,t)}renderErrorMessage(e){return this.props.showServerError?(0,i.FD)("div",{children:["Error message: ",e.message]}):""}render(){const{children:e}=this.props,{error:t}=this.state;return t?(0,i.Y)("div",{children:(0,i.FD)("p",{children:[(0,i.Y)("i",{"data-testid":"icon-fail",className:"fa fa-exclamation-triangle icon-fail",css:o.wrapper}),(0,i.Y)("span",{children:" Something went wrong with this section. "}),(0,i.Y)("span",{children:"If this error persists, please report an issue "}),(0,i.Y)("a",{href:n.A.getSupportPageUrl(),target:"_blank",children:"here"}),".",this.renderErrorMessage(t)]})}):e}}const o={wrapper:{marginLeft:-2}}},28486:function(e,t,r){r.d(t,{tH:function(){return o}});var s=r(31014);function n(e,t,r,s){Object.defineProperty(e,t,{get:r,set:s,enumerable:!0,configurable:!0})}n({},"ErrorBoundary",(()=>o));n({},"ErrorBoundaryContext",(()=>i));const i=(0,s.createContext)(null),a={didCatch:!1,error:null};class o extends s.Component{state=(()=>a)();static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary=(()=>{var e=this;return function(){const{error:t}=e.state;if(null!==t){for(var r=arguments.length,s=new Array(r),n=0;n<r;n++)s[n]=arguments[n];e.props.onReset?.({args:s,reason:"imperative-api"}),e.setState(a)}}})();componentDidCatch(e,t){this.props.onError?.(e,t)}componentDidUpdate(e,t){const{didCatch:r}=this.state,{resetKeys:s}=this.props;r&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some(((e,r)=>!Object.is(e,t[r])))}(e.resetKeys,s)&&(this.props.onReset?.({next:s,prev:e.resetKeys,reason:"keys"}),this.setState(a))}render(){const{children:e,fallbackRender:t,FallbackComponent:r,fallback:n}=this.props,{didCatch:a,error:o}=this.state;let l=e;if(a){const e={error:o,resetErrorBoundary:this.resetErrorBoundary};if((0,s.isValidElement)(n))l=n;else if("function"===typeof t)l=t(e);else{if(!r)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");l=(0,s.createElement)(r,e)}}return(0,s.createElement)(i.Provider,{value:{didCatch:a,error:o,resetErrorBoundary:this.resetErrorBoundary}},l)}}function l(e){if(null==e||"boolean"!==typeof e.didCatch||"function"!==typeof e.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function c(){const e=(0,s.useContext)(i);l(e);const[t,r]=(0,s.useState)({error:null,hasError:!1}),n=(0,s.useMemo)((()=>({resetBoundary:()=>{e?.resetErrorBoundary(),r({error:null,hasError:!1})},showBoundary:e=>r({error:e,hasError:!0})})),[e?.resetErrorBoundary]);if(t.hasError)throw t.error;return n}n({},"useErrorBoundary",(()=>c));function d(e,t){const r=r=>(0,s.createElement)(o,t,(0,s.createElement)(e,r)),n=e.displayName||e.name||"Unknown";return r.displayName=`withErrorBoundary(${n})`,r}n({},"withErrorBoundary",(()=>d))},30214:function(e,t,r){r.d(t,{W:function(){return l}});var s=r(89555),n=(r(31014),r(88443)),i=r(32599),a=r(48012),o=r(50111);const l=e=>{let{className:t}=e;const{theme:r}=(0,i.u)();return(0,o.Y)(a.vwO,{componentId:"codegen_mlflow_app_src_shared_building_blocks_previewbadge.tsx_14",className:t,css:(0,s.AH)({marginLeft:r.spacing.xs},""),color:"turquoise",children:(0,o.Y)(n.A,{id:"8qJt7/",defaultMessage:"Experimental"})})}},32366:function(e,t,r){r.d(t,{$:function(){return n}});var s=r(50111);const n=e=>(0,s.FD)("div",{css:i.wrapper,children:[(0,s.Y)("div",{css:i.controls,children:e.controls}),(0,s.Y)("div",{css:i.plotWrapper,children:e.children})]}),i={plotWrapper:{overflow:"hidden",width:"100%",height:"100%",minHeight:450},wrapper:{display:"grid",gridTemplateColumns:"minmax(300px, 1fr) 3fr"},controls:e=>({padding:`0 ${e.spacing.xs}px`})}},34860:function(e){e.exports="data:image/png;base64,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"},48588:function(e,t,r){r.d(t,{L:function(){return a}});r(31014);var s=r(48012),n=r(15579),i=r(50111);function a(e){const{usesFullHeight:t,...r}=e;return(0,i.FD)(s.ffj,{css:t?o.useFullHeightLayout:o.wrapper,children:[(0,i.Y)(n.S,{css:o.fixedSpacer}),t?e.children:(0,i.Y)("div",{...r,css:o.container})]})}a.defaultProps={usesFullHeight:!1};const o={useFullHeightLayout:{height:"calc(100% - 60px)",display:"flex",flexDirection:"column","&:last-child":{flexGrow:1}},wrapper:{flex:1},fixedSpacer:{flexShrink:0},container:{width:"100%",flexGrow:1,paddingBottom:24}}},51882:function(e,t,r){r.d(t,{J:function(){return v}});var s=r(9133),n=r(31014),i=r(72877),a=r(10811),o=r(48012),l=r(15579),c=r(76010),d=r(85017),u=r(24406),h=r(88443),p=r(79445),m=r(32366),g=r(50111);class y extends n.Component{constructor(e){super(e),this.metricKeys=void 0,this.paramKeys=void 0,this.metricKeys=u.A.getKeys(this.props.metricLists,!1),this.paramKeys=u.A.getKeys(this.props.paramLists,!1),this.paramKeys.length+this.metricKeys.length<2?this.state={disabled:!0}:this.state={disabled:!1,x:this.paramKeys.length>0?{key:this.paramKeys[0],isMetric:!1}:{key:this.metricKeys[1],isMetric:!0},y:this.metricKeys.length>0?{key:this.metricKeys[0],isMetric:!0}:{key:this.paramKeys[1],isMetric:!1}}}getValue(e,t){let{key:r,isMetric:s}=t;const n=u.A.findInList((s?this.props.metricLists:this.props.paramLists)[e],r);return void 0===n?n:n.value}render(){if(this.state.disabled)return(0,g.Y)("div",{});const e=y.MAX_PLOT_KEY_LENGTH,t=[],r=[],n=[];return this.props.runInfos.forEach(((e,s)=>{const i=this.getValue(s,this.state.x),a=this.getValue(s,this.state.y);void 0!==i&&void 0!==a&&(t.push(i),r.push(a),n.push(this.getPlotlyTooltip(s)))})),(0,g.Y)(m.$,{controls:(0,g.FD)(g.FK,{children:[(0,g.FD)("div",{children:[(0,g.Y)(o.D$Q.Label,{htmlFor:"x-axis-selector",children:(0,g.Y)(h.A,{id:"1/M8+x",defaultMessage:"X-axis:"})}),this.renderSelect("x")]}),(0,g.Y)(l.S,{}),(0,g.FD)("div",{children:[(0,g.Y)(o.D$Q.Label,{htmlFor:"y-axis-selector",children:(0,g.Y)(h.A,{id:"eNUbaN",defaultMessage:"Y-axis:"})}),this.renderSelect("y")]})]}),children:(0,g.Y)(p.W,{data:[{x:t,y:r,text:n,hoverinfo:"text",type:"scattergl",mode:"markers",marker:{size:10,color:"rgba(200, 50, 100, .75)"}}],layout:{margin:{t:30},hovermode:"closest",xaxis:{title:(0,s.escape)(c.A.truncateString(this.state.x.key,e))},yaxis:{title:(0,s.escape)(c.A.truncateString(this.state.y.key,e))}},css:f.plot,config:{responsive:!0,displaylogo:!1,scrollZoom:!0,modeBarButtonsToRemove:["sendDataToCloud","select2d","lasso2d","resetScale2d","hoverClosestCartesian","hoverCompareCartesian"]},useResizeHandler:!0})})}renderSelect(e){return(0,g.FD)(o.XAe,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_comparerunscatter.tsx_182",css:f.select,id:e+"-axis-selector",onChange:t=>{let{target:r}=t;const{value:s}=r,[n,...i]=s.split("-"),a=i.join("-"),o="metric"===n;this.setState({[e]:{isMetric:o,key:a}})},value:(this.state[e].isMetric?"metric-":"param-")+this.state[e].key,children:[(0,g.Y)(o.lQN,{label:"Parameter",children:this.paramKeys.map((e=>(0,g.Y)(o.wS0,{value:"param-"+e,children:e},"param-"+e)))}),(0,g.Y)(o.lQN,{label:"Metric",children:this.metricKeys.map((e=>(0,g.Y)(o.wS0,{value:"metric-"+e,children:e},"metric-"+e)))})]})}getPlotlyTooltip(e){const t=y.MAX_PLOT_KEY_LENGTH,r=y.MAX_PLOT_VALUE_LENGTH,n=this.props.runDisplayNames[e];let i=`<b>${(0,s.escape)(n)}</b><br>`;const a=this.props.paramLists[e];a.forEach((e=>{i+=(0,s.escape)(c.A.truncateString(e.key,t))+": "+(0,s.escape)(c.A.truncateString(e.value,r))+"<br>"}));const o=this.props.metricLists[e];return o.length>0&&(i+=a.length>0?"<br>":"",o.forEach((e=>{i+=(0,s.escape)(c.A.truncateString(e.key,t))+": "+c.A.formatMetric(e.value)+"<br>"}))),i}}y.MAX_PLOT_KEY_LENGTH=40,y.MAX_PLOT_VALUE_LENGTH=60;const f={select:{width:"100%"},plot:{width:"100%"}},v=(0,a.Ng)(((e,t)=>{const r=[],s=[],n=[],{runUuids:a}=t;return a.forEach((t=>{r.push((0,i.K4)(t,e)),s.push(Object.values((0,d.d0)(t,e))),n.push(Object.values((0,i.tI)(t,e)))})),{runInfos:r,metricLists:s,paramLists:n}}))(y)},53140:function(e,t,r){r.d(t,{Ay:function(){return u},dD:function(){return d}});var s=r(31014),n=r(10811),i=r(72877),a=r(96277),o=r(47664),l=r(50111);class c extends s.Component{constructor(){super(...arguments),this.state={shouldRender:!1,shouldRenderError:!1}}static getErrorRequests(e,t){return e.filter((e=>void 0!==e.error&&!(t&&t.includes(e.id)&&e.error.getErrorCode()===o.tG.RESOURCE_DOES_NOT_EXIST)))}static getDerivedStateFromProps(e){const t=!!e.requests.length&&e.requests.every((e=>e&&!1===e.active)),r=c.getErrorRequests(e.requests,e.requestIdsWith404sToIgnore);return{shouldRender:t,shouldRenderError:r.length>0,requestErrors:r}}getRenderedContent(){const{children:e,requests:t,customSpinner:r,permissionDeniedView:s,suppressErrorThrow:n,customRequestErrorHandlerFn:i}=this.props,{shouldRender:c,shouldRenderError:u,requestErrors:h}=this.state,p=h.filter((e=>e.error.getErrorCode()===o.tG.PERMISSION_DENIED));return"function"===typeof e?e(!c,u,t,h):c||u||this.props.shouldOptimisticallyRender?p.length>0&&s?s:(u&&!n&&(i?i(h):d(h)),e):r||(0,l.Y)(a.y,{})}render(){return this.getRenderedContent()}}c.defaultProps={requests:[],requestIdsWith404sToIgnore:[],shouldOptimisticallyRender:!1};const d=e=>{throw console.error("ERROR",e),Error(`A request error occurred.: ${e.error}`)};var u=(0,n.Ng)(((e,t)=>({requests:(0,i.EW)(t.requestIds,e)})))(c)},55406:function(e,t,r){r.d(t,{Ay:function(){return b}});var s=r(31014),n=r(10811),i=r(9133),a=r.n(i),o=r(79445),l=r(50111);const c="unknown";class d extends s.Component{constructor(){super(...arguments),this.state={sequence:[...this.props.paramKeys,...this.props.metricKeys]},this.updateMetricAxisLabelStyle=()=>{const e=new Set(this.props.metricKeys);d.getLabelElementsFromDom().filter((t=>e.has(t.innerHTML))).forEach((e=>{e.style.fill="green",e.style.fontWeight="bold"}))},this.maybeUpdateStateForColorScale=e=>{const t=this.findLastKeyFromState(this.props.metricKeys),r=new Set(this.props.metricKeys);t!==a().findLast(e,(e=>r.has(e)))&&this.setState({sequence:e})},this.handlePlotUpdate=e=>{let{data:[{dimensions:t}]}=e;this.updateMetricAxisLabelStyle(),this.maybeUpdateStateForColorScale(t.map((e=>e.label)))}}static getDerivedStateFromProps(e,t){const r=[...e.paramKeys,...e.metricKeys],s=t.sequence;return a().isEqual(a().sortBy(r),a().sortBy(s))?null:{sequence:r}}getData(){const{sequence:e}=this.state,{paramDimensions:t,metricDimensions:r,metricKeys:s}=this.props,n=this.findLastKeyFromState(s),i=this.props.metricDimensions.find((e=>e.label===n));return[{type:"parcoords",line:{...d.getColorScaleConfigsForDimension(i)},dimensions:d.getDimensionsOrderedBySequence([...t,...r],e)}]}static getDimensionsOrderedBySequence(e,t){return a().sortBy(e,[e=>t.indexOf(e.label)])}findLastKeyFromState(e){const{sequence:t}=this.state,r=new Set(e);return a().findLast(t,(e=>r.has(e)))}static getColorScaleConfigsForDimension(e){if(!e)return null;return{showscale:!0,colorscale:"Jet",cmin:a().min(e.values),cmax:a().max(e.values),color:e.values}}render(){return(0,l.Y)(o.W,{layout:{autosize:!0,margin:{t:50}},useResizeHandler:!0,css:h.plot,data:this.getData(),onUpdate:this.handlePlotUpdate,className:"pcp-plot",config:{displayModeBar:!1}})}}d.getLabelElementsFromDom=()=>Array.from(document.querySelectorAll(".pcp-plot .parcoords .y-axis .axis-heading .axis-title"));const u=(e,t,r)=>{let s={};const n=((e,t,r)=>{for(let s=0;s<t.length;s++)if(r[t[s]][e]){const{value:n}=r[t[s]][e];if("string"===typeof n&&isNaN(Number(n))&&"NaN"!==n)return"string"}return"number"})(e,t,r);if("string"===n)s=(e=>{const t=a().uniq(e).sort();let r=!1;const s=t.filter((e=>(e===c&&(r=!0),e!==c)));r&&s.push(c);const n=a().invert(s),i={};return i.values=e.map((e=>Number(n[e]))),i.tickvals=a().range(s.length),i.ticktext=s.map((e=>e.substring(0,10))),i})(t.map((t=>r[t][e]?r[t][e].value:c)));else{let n=Number.MIN_SAFE_INTEGER;const i=t.map((t=>{if(r[t][e]){const{value:s}=r[t][e],i=Number(s);return n<i&&(n=i),i}return c}));s.values=i.map((e=>e===c?n+.01:e)),s.tickformat=".5f"}return{label:e,...s}},h={plot:{width:"100%"}};var p=(0,n.Ng)(((e,t)=>{const{runUuids:r,paramKeys:s,metricKeys:n}=t,{latestMetricsByRunUuid:i,paramsByRunUuid:a}=e.entities;return{paramDimensions:s.map((e=>u(e,r,a))),metricDimensions:n.map((e=>u(e,r,i)))}}))(d),m=r(48012),g=r(32599),y=r(88443);function f(e){let{paramKeys:t,metricKeys:r,selectedParamKeys:s,selectedMetricKeys:n,handleParamsSelectChange:i,handleMetricsSelectChange:a,onClearAllSelect:o}=e;return(0,l.FD)("div",{css:v.wrapper,children:[(0,l.Y)("div",{children:(0,l.Y)(y.A,{id:"7zNDHj",defaultMessage:"Parameters:"})}),(0,l.Y)(m._vn,{mode:"multiple",css:v.select,placeholder:(0,l.Y)(y.A,{id:"OzyPl3",defaultMessage:"Please select parameters"}),value:s,onChange:i,children:t.map((e=>(0,l.Y)(m._vn.Option,{value:e,children:e},e)))}),(0,l.Y)("div",{style:{marginTop:20},children:(0,l.Y)(y.A,{id:"EKyt+3",defaultMessage:"Metrics:"})}),(0,l.Y)(m._vn,{mode:"multiple",css:v.select,placeholder:(0,l.Y)(y.A,{id:"r5JI+N",defaultMessage:"Please select metrics"}),value:n,onChange:a,children:r.map((e=>(0,l.Y)(m._vn.Option,{value:e,children:e},e)))}),(0,l.Y)("div",{style:{marginTop:20},children:(0,l.Y)(g.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_parallelcoordinatesplotcontrols.tsx_84","data-test-id":"clear-button",onClick:o,children:(0,l.Y)(y.A,{id:"FdDWTo",defaultMessage:"Clear All"})})})]})}const v={wrapper:e=>({padding:`0 ${e.spacing.xs}px`}),select:{width:"100%"}};var A=r(72877),x=r(32366);class k extends s.Component{constructor(){super(...arguments),this.state={selectedParamKeys:this.props.diffParamKeys.sort().slice(0,3),selectedMetricKeys:this.props.sharedMetricKeys.slice(0,1)},this.handleParamsSelectChange=e=>{this.setState({selectedParamKeys:e})},this.handleMetricsSelectChange=e=>{this.setState({selectedMetricKeys:e})},this.onClearAllSelect=()=>{this.setState({selectedParamKeys:[],selectedMetricKeys:[]})}}render(){const{runUuids:e,allParamKeys:t,allMetricKeys:r}=this.props,{selectedParamKeys:s,selectedMetricKeys:n}=this.state;return(0,l.Y)(x.$,{controls:(0,l.Y)(f,{paramKeys:t,metricKeys:r,selectedParamKeys:s,selectedMetricKeys:n,handleMetricsSelectChange:this.handleMetricsSelectChange,handleParamsSelectChange:this.handleParamsSelectChange,onClearAllSelect:this.onClearAllSelect}),children:a().isEmpty(s)&&a().isEmpty(n)?(0,l.FD)("div",{css:C.noValuesSelected,"data-testid":"no-values-selected",children:[(0,l.Y)(g.T.Title,{level:2,children:(0,l.Y)(y.A,{id:"x0K27S",defaultMessage:"Nothing to compare!"})}),(0,l.Y)(y.A,{id:"2NJ8E5",defaultMessage:"Please select parameters and/or metrics to display the comparison."})]}):(0,l.Y)(p,{runUuids:e,paramKeys:s,metricKeys:n})})}}const C={noValuesSelected:e=>({padding:e.spacing.md,textAlign:"center"})};var b=(0,n.Ng)(((e,t)=>{const{runUuids:r}=t,s=(null!==r&&void 0!==r?r:[]).filter((t=>(0,A.K4)(t,e))),n=(0,A.XT)(s,e),i=(0,A.gz)(s,e),a=(0,A.EV)(s,e),{paramsByRunUuid:o}=e.entities,l=((e,t,r)=>{const s=[];return e.forEach((e=>{t.map((t=>r[t][e]&&r[t][e].value)).every(((e,t,r)=>e===r[0]))||s.push(e)})),s})(n,s,o);return{allParamKeys:n,allMetricKeys:i,sharedMetricKeys:a,diffParamKeys:l}}))(k)},62448:function(e,t,r){r.d(t,{h:function(){return o}});var s=r(39416),n=r(52350),i=r(47664);class a{}a.mlflowServices={MODEL_REGISTRY:"Model Registry",EXPERIMENTS:"Experiments",MODEL_SERVING:"Model Serving",RUN_TRACKING:"Run Tracking"};const o=(e,t)=>{if(!(e instanceof n.s))return;const{status:r}=e;let a;const o={status:r};e.getErrorCode()===i.tG.RESOURCE_DOES_NOT_EXIST&&(a=new s.m_(o)),e.getErrorCode()===i.tG.PERMISSION_DENIED&&(a=new s.i_(o)),e.getErrorCode()===i.tG.INTERNAL_ERROR&&(a=new s.PO(o)),e.getErrorCode()===i.tG.INVALID_PARAMETER_VALUE&&(a=new s.v7(o));const l=e.getMessageField();return a&&l&&(a.message=l),a};t.A=a},71832:function(){},79085:function(e,t,r){r.d(t,{o:function(){return d},z:function(){return h}});var s=r(89555),n=(r(31014),r(48012)),i=r(32599),a=r(15579),o=r(88464),l=r(30214),c=r(50111);function d(e){let{menu:t}=e;const r=(0,c.Y)(n.W1t,{children:t.map((e=>{let{id:t,itemName:r,onClick:s,href:i,...a}=e;return(0,c.Y)(n.W1t.Item,{onClick:s,href:i,"data-test-id":t,...a,children:r},t)}))});return t.length>0?(0,c.Y)(n.msM,{overlay:r,trigger:["click"],placement:"bottomLeft",arrow:!0,children:(0,c.Y)(i.B,{componentId:"codegen_mlflow_app_src_shared_building_blocks_pageheader.tsx_54",icon:(0,c.Y)(n.ssM,{}),"data-test-id":"overflow-menu-trigger","aria-label":"Open header dropdown menu"})}):null}var u={name:"1gz4j9a",styles:"margin-left:0"};function h(e){const{title:t,breadcrumbs:r=[],titleAddOns:d=[],preview:h,children:p,spacerSize:m,hideSpacer:g=!1,dangerouslyAppendEmotionCSS:y}=e,{theme:f}=(0,i.u)();(0,o.A)();return(0,c.FD)(c.FK,{children:[(0,c.Y)(n.Y9Y,{breadcrumbs:r.length>0&&(0,c.Y)(n.QpV,{includeTrailingCaret:!0,children:r.map(((e,t)=>(0,c.Y)(n.QpV.Item,{children:e},t)))}),buttons:p,title:t,titleAddOns:(0,c.FD)(c.FK,{children:[h&&(0,c.Y)(l.W,{css:u}),d]}),dangerouslyAppendEmotionCSS:y}),(0,c.Y)(a.S,{css:(0,s.AH)({flexShrink:0,...g?{display:"none"}:{}},""),size:m})]})}},79445:function(e,t,r){r.d(t,{W:function(){return l}});var s=r(31014),n=r(48012),i=r(27705),a=r(50111);const o=s.lazy((()=>r.e(4850).then(r.t.bind(r,84850,23)))),l=e=>{let{fallback:t,...r}=e;return(0,a.Y)(i.g,{children:(0,a.Y)(s.Suspense,{fallback:null!==t&&void 0!==t?t:(0,a.Y)(n.PLz,{active:!0}),children:(0,a.Y)(o,{...r})})})}},87234:function(e,t,r){var s=r(9133),n=r(31014),i=r(48012),a=r(15579),o=r(72877),l=r(10811),c=r(76010),d=r(85017),u=r(24406),h=r(88443),p=r(79445),m=r(32366),g=r(50111);var y={name:"1d3w5wq",styles:"width:100%"};class f extends n.Component{constructor(e){if(super(e),this.metricKeys=void 0,this.paramKeys=void 0,this.metricKeys=u.A.getKeys(this.props.metricLists,!0),this.paramKeys=u.A.getKeys(this.props.paramLists,!0),this.paramKeys.length+this.metricKeys.length<3)this.state={disabled:!0};else{const e={disabled:!1,reverseColor:!1};0===this.metricKeys.length?this.state={...e,xaxis:{key:this.paramKeys[0],isMetric:!1},yaxis:{key:this.paramKeys[1],isMetric:!1},zaxis:{key:this.paramKeys[2],isMetric:!1}}:0===this.paramKeys.length?this.state={...e,xaxis:{key:this.metricKeys[0],isMetric:!0},yaxis:{key:this.metricKeys[1],isMetric:!0},zaxis:{key:this.metricKeys[2],isMetric:!0}}:1===this.paramKeys.length?this.state={...e,xaxis:{key:this.paramKeys[0],isMetric:!1},yaxis:{key:this.metricKeys[0],isMetric:!0},zaxis:{key:this.metricKeys[1],isMetric:!0}}:this.state={...e,xaxis:{key:this.paramKeys[0],isMetric:!1},yaxis:{key:this.paramKeys[1],isMetric:!1},zaxis:{key:this.metricKeys[0],isMetric:!0}}}}getValue(e,t){let{key:r,isMetric:s}=t;const n=u.A.findInList((s?this.props.metricLists:this.props.paramLists)[e],r);return void 0===n?n:n.value}getColorscale(){const e=[[0,"rgb(5,10,172)"],[.35,"rgb(40,60,190)"],[.5,"rgb(70,100,245)"],[.6,"rgb(90,120,245)"],[.7,"rgb(106,137,247)"],[1,"rgb(220,220,220)"]];return this.state.reverseColor?e:e.map(((t,r)=>{let[s]=t;return[s,e[e.length-1-r][1]]}))}render(){if(this.state.disabled)return(0,g.Y)("div",{children:(0,g.Y)(h.A,{id:"81DxPY",defaultMessage:"Contour plots can only be rendered when comparing a group of runs with three or more unique metrics or params. Log more metrics or params to your runs to visualize them using the contour plot."})});const e=f.MAX_PLOT_KEY_LENGTH,t=[],r=[],n=[],o=[];this.props.runInfos.forEach(((e,s)=>{const i=this.getValue(s,this.state.xaxis),a=this.getValue(s,this.state.yaxis),l=this.getValue(s,this.state.zaxis);void 0!==i&&void 0!==a&&void 0!==l&&(t.push(parseFloat(i)),r.push(parseFloat(a)),n.push(parseFloat(l)),o.push(this.getPlotlyTooltip(s)))}));return(0,g.Y)(m.$,{controls:(0,g.FD)(g.FK,{children:[(0,g.FD)("div",{children:[(0,g.Y)(i.D$Q.Label,{htmlFor:"xaxis-selector",children:(0,g.Y)(h.A,{id:"UGAKJp",defaultMessage:"X-axis:"})}),this.renderSelect("xaxis")]}),(0,g.Y)(a.S,{}),(0,g.FD)("div",{children:[(0,g.Y)(i.D$Q.Label,{htmlFor:"yaxis-selector",children:(0,g.Y)(h.A,{id:"0ElvS9",defaultMessage:"Y-axis:"})}),this.renderSelect("yaxis")]}),(0,g.Y)(a.S,{}),(0,g.FD)("div",{children:[(0,g.Y)(i.D$Q.Label,{htmlFor:"zaxis-selector",children:(0,g.Y)(h.A,{id:"WJF+wY",defaultMessage:"Z-axis:"})}),this.renderSelect("zaxis")]}),(0,g.Y)(a.S,{}),(0,g.FD)("div",{className:"inline-control",children:[(0,g.Y)(h.A,{id:"fZPf0W",defaultMessage:"Reverse color:"})," ",(0,g.Y)(i.dOG,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_compareruncontour.tsx_282",className:"show-point-toggle",checked:this.state.reverseColor,onChange:e=>this.setState({reverseColor:e})})]})]}),children:(()=>{const i=[];if(new Set(t).size<2&&i.push("X"),new Set(r).size<2&&i.push("Y"),i.length>0){const e=i.length>1?`The ${i.join(" and ")} axes don't`:`The ${i[0]} axis doesn't`;return(0,g.Y)("div",{css:v.noDataMessage,children:`${e} have enough unique data points to render the contour plot.`})}return(0,g.Y)(p.W,{css:v.plot,data:[{z:n,x:t,y:r,type:"contour",hoverinfo:"none",colorscale:this.getColorscale(),connectgaps:!0,contours:{coloring:"heatmap"}},{x:t,y:r,text:o,hoverinfo:"text",type:"scattergl",mode:"markers",marker:{size:10,color:"rgba(200, 50, 100, .75)"}}],layout:{margin:{t:30},hovermode:"closest",xaxis:{title:(0,s.escape)(c.A.truncateString(this.state.xaxis.key,e)),range:[Math.min(...t),Math.max(...t)]},yaxis:{title:(0,s.escape)(c.A.truncateString(this.state.yaxis.key,e)),range:[Math.min(...r),Math.max(...r)]}},config:{responsive:!0,displaylogo:!1,scrollZoom:!0,modeBarButtonsToRemove:["sendDataToCloud","select2d","lasso2d","resetScale2d","hoverClosestCartesian","hoverCompareCartesian"]},useResizeHandler:!0})})()})}renderSelect(e){return(0,g.FD)(i.XAe,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_compareruncontour.tsx_299",css:y,id:e+"-selector",onChange:t=>{let{target:r}=t;const{value:s}=r,[n,...i]=s.split("-"),a=i.join("-"),o="metric"===n;this.setState({[e]:{isMetric:o,key:a}})},value:(this.state[e].isMetric?"metric-":"param-")+this.state[e].key,children:[(0,g.Y)(i.lQN,{label:"Parameter",children:this.paramKeys.map((e=>(0,g.Y)(i.wS0,{value:"param-"+e,children:e},"param-"+e)))}),(0,g.Y)(i.lQN,{label:"Metric",children:this.metricKeys.map((e=>(0,g.Y)(i.wS0,{value:"metric-"+e,children:e},"metric-"+e)))})]})}getPlotlyTooltip(e){const t=f.MAX_PLOT_KEY_LENGTH,r=f.MAX_PLOT_VALUE_LENGTH,n=this.props.runDisplayNames[e];let i=`<b>${(0,s.escape)(n)}</b><br>`;const a=this.props.paramLists[e];a.forEach((e=>{i+=(0,s.escape)(c.A.truncateString(e.key,t))+": "+(0,s.escape)(c.A.truncateString(e.value,r))+"<br>"}));const o=this.props.metricLists[e];return o.length>0&&(i+=a.length>0?"<br>":"",o.forEach((e=>{i+=(0,s.escape)(c.A.truncateString(e.key,t))+": "+c.A.formatMetric(e.value)+"<br>"}))),i}}f.MAX_PLOT_KEY_LENGTH=40,f.MAX_PLOT_VALUE_LENGTH=60;const v={plot:{width:"100%"},noDataMessage:e=>({padding:e.spacing.sm,display:"flex",justifyContent:"center"})};t.A=(0,l.Ng)(((e,t)=>{const r=[],s=[],n=[],{runUuids:i}=t;return i.forEach((t=>{r.push((0,o.K4)(t,e)),s.push(Object.values((0,d.d0)(t,e))),n.push(Object.values((0,o.tI)(t,e)))})),{runInfos:r,metricLists:s,paramLists:n}}))(f)},96277:function(e,t,r){r.d(t,{y:function(){return a}});var s=r(34860),n=r(89555),i=r(50111);function a(e){let{showImmediately:t}=e;return(0,i.Y)("div",{css:e=>o.spinner(e,t),children:(0,i.Y)("img",{alt:"Page loading...",src:s})})}const o={spinner:(e,t)=>({width:100,marginTop:100,marginLeft:"auto",marginRight:"auto",img:{position:"absolute",opacity:0,top:"50%",left:"50%",width:2*e.general.heightBase,height:2*e.general.heightBase,marginTop:-e.general.heightBase,marginLeft:-e.general.heightBase,animation:`${n.i7`
          0% {
            opacity: 1;
          }
          100% {
            opacity: 1;
            -webkit-transform: rotate(360deg);
                transform: rotate(360deg);
            }
          `} 3s linear infinite`,animationDelay:t?"0s":"0.5s"}})}}}]);
//# sourceMappingURL=4918.de33edfd.chunk.js.map