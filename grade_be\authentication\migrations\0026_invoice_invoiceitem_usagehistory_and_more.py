# Generated by Django 5.1.9 on 2025-05-30 12:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("authentication", "0025_user_active_role_user_roles"),
    ]

    operations = [
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "invoice_number",
                    models.CharField(max_length=50, unique=True),
                ),
                ("issue_date", models.DateField()),
                ("due_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("sent", "Sent"),
                            ("paid", "Paid"),
                            ("overdue", "Overdue"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                (
                    "subtotal",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("tax", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "total",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invoices",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice",
                "verbose_name_plural": "Invoices",
                "ordering": ["-issue_date"],
            },
        ),
        migrations.CreateModel(
            name="InvoiceItem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service_type", models.CharField(max_length=50)),
                ("description", models.CharField(max_length=255)),
                ("quantity", models.IntegerField()),
                (
                    "unit_price",
                    models.DecimalField(decimal_places=7, max_digits=10),
                ),
                (
                    "total",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                (
                    "invoice",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="items",
                        to="authentication.invoice",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice Item",
                "verbose_name_plural": "Invoice Items",
            },
        ),
        migrations.CreateModel(
            name="UsageHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("service_type", models.CharField(max_length=50)),
                ("input_length", models.IntegerField()),
                ("cost", models.DecimalField(decimal_places=7, max_digits=10)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "reference_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Usage History",
                "verbose_name_plural": "Usage History",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["user", "status"],
                name="authenticat_user_id_e58806_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="invoice",
            index=models.Index(
                fields=["invoice_number"],
                name="authenticat_invoice_4380f5_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="usagehistory",
            index=models.Index(
                fields=["user", "timestamp"],
                name="authenticat_user_id_ba64bb_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="usagehistory",
            index=models.Index(
                fields=["service_type"], name="authenticat_service_c60f2d_idx"
            ),
        ),
    ]
