# Generated by Django 5.1.9 on 2025-06-19 05:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "authentication",
            "0035_remove_organization_account_locked_until_and_more",
        ),
    ]

    operations = [
        migrations.RemoveField(
            model_name="user",
            name="roles",
        ),
        migrations.AddField(
            model_name="user",
            name="is_evaluator",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_mentor",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_qp_uploader",
            field=models.<PERSON>olean<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name="user",
            name="is_student",
            field=models.BooleanField(default=False),
        ),
    ]
