{"version": 3, "file": "static/js/8294.6523666e.chunk.js", "mappings": "kHAgDAA,EAAOC,QA5BS,SAASC,EAAWC,EAAQC,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GAOzD,IAAKP,EAAW,CACd,IAAIQ,EACJ,QAAeC,IAAXR,EACFO,EAAQ,IAAIE,MACV,qIAGG,CACL,IAAIC,EAAO,CAACT,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,GACvBK,EAAW,GACfJ,EAAQ,IAAIE,MACVT,EAAOY,QAAQ,OAAO,WAAa,OAAOF,EAAKC,IAAa,MAExDE,KAAO,qBACf,CAGA,MADAN,EAAMO,YAAc,EACdP,CACR,CACF,C,wBC9CA,SAASQ,IAAQ,CAuMjB,SAASC,EAAYC,EAAMC,EAAYC,EAAWC,EAAWC,GAM3D,IALA,IAAIC,EAAe,EACfC,EAAeL,EAAWM,OAC1BC,EAAS,EACTC,EAAS,EAENJ,EAAeC,EAAcD,IAAgB,CAClD,IAAIK,EAAYT,EAAWI,GAE3B,GAAKK,EAAUC,SAuBb,GALAD,EAAUE,MAAQZ,EAAKa,KAAKV,EAAUW,MAAML,EAAQA,EAASC,EAAUK,QACvEN,GAAUC,EAAUK,MAIhBV,GAAgBJ,EAAWI,EAAe,GAAGW,MAAO,CACtD,IAAIC,EAAMhB,EAAWI,EAAe,GACpCJ,EAAWI,EAAe,GAAKJ,EAAWI,GAC1CJ,EAAWI,GAAgBY,CAC7B,MA3BsB,CACtB,IAAKP,EAAUM,OAASZ,EAAiB,CACvC,IAAIQ,EAAQV,EAAUY,MAAMN,EAAQA,EAASE,EAAUK,OACvDH,EAAQA,EAAMM,KAAI,SAAUN,EAAOO,GACjC,IAAIC,EAAWjB,EAAUM,EAASU,GAClC,OAAOC,EAASb,OAASK,EAAML,OAASa,EAAWR,CACrD,IACAF,EAAUE,MAAQZ,EAAKa,KAAKD,EAC9B,MACEF,EAAUE,MAAQZ,EAAKa,KAAKX,EAAUY,MAAMN,EAAQA,EAASE,EAAUK,QAGzEP,GAAUE,EAAUK,MAEfL,EAAUM,QACbP,GAAUC,EAAUK,MAExB,CAYF,CAKA,IAAIM,EAAgBpB,EAAWK,EAAe,GAO9C,OALIA,EAAe,GAAoC,kBAAxBe,EAAcT,QAAuBS,EAAcL,OAASK,EAAcV,UAAYX,EAAKsB,OAAO,GAAID,EAAcT,SACjJX,EAAWK,EAAe,GAAGM,OAASS,EAAcT,MACpDX,EAAWsB,OAGNtB,CACT,C,iCAzPAH,EAAK0B,UAAY,CACfxB,KAAM,SAAcG,EAAWD,GAC7B,IAAIuB,EAAUC,UAAUnB,OAAS,QAAsBhB,IAAjBmC,UAAU,GAAmBA,UAAU,GAAK,CAAC,EAC/EC,EAAWF,EAAQE,SAEA,oBAAZF,IACTE,EAAWF,EACXA,EAAU,CAAC,GAGbG,KAAKH,QAAUA,EACf,IAAII,EAAOD,KAEX,SAASE,EAAKlB,GACZ,OAAIe,GACFI,YAAW,WACTJ,OAASpC,EAAWqB,EACtB,GAAG,IACI,GAEAA,CAEX,CAGAT,EAAYyB,KAAKI,UAAU7B,GAC3BD,EAAY0B,KAAKI,UAAU9B,GAC3BC,EAAYyB,KAAKK,YAAYL,KAAKM,SAAS/B,IAE3C,IAAIgC,GADJjC,EAAY0B,KAAKK,YAAYL,KAAKM,SAAShC,KACpBK,OACnB6B,EAASjC,EAAUI,OACnB8B,EAAa,EACbC,EAAgBH,EAASC,EAEzBX,EAAQa,gBACVA,EAAgBC,KAAKC,IAAIF,EAAeb,EAAQa,gBAGlD,IAAIG,EAAW,CAAC,CACdjC,QAAS,EACTP,WAAY,KAGVQ,EAASmB,KAAKc,cAAcD,EAAS,GAAIvC,EAAWC,EAAW,GAEnE,GAAIsC,EAAS,GAAGjC,OAAS,GAAK2B,GAAU1B,EAAS,GAAK2B,EAEpD,OAAON,EAAK,CAAC,CACXlB,MAAOgB,KAAKf,KAAKX,GACjBa,MAAOb,EAAUK,UAKrB,SAASoC,IACP,IAAK,IAAIC,GAAgB,EAAIP,EAAYO,GAAgBP,EAAYO,GAAgB,EAAG,CACtF,IAAIC,OAAW,EAEXC,EAAUL,EAASG,EAAe,GAClCG,EAAaN,EAASG,EAAe,GACrCI,GAAWD,EAAaA,EAAWvC,OAAS,GAAKoC,EAEjDE,IAEFL,EAASG,EAAe,QAAKrD,GAG/B,IAAI0D,EAASH,GAAWA,EAAQtC,OAAS,EAAI2B,EACzCe,EAAYH,GAAc,GAAKC,GAAWA,EAAUZ,EAExD,GAAKa,GAAWC,EAAhB,CAqBA,IAZKD,GAAUC,GAAaJ,EAAQtC,OAASuC,EAAWvC,QACtDqC,EA4KD,CACLrC,QAFe2C,EA3KYJ,GA6KdvC,OACbP,WAAYkD,EAAKlD,WAAWa,MAAM,IA7K5Be,EAAKuB,cAAcP,EAAS5C,gBAAYV,GAAW,MAEnDsD,EAAWC,GAEFtC,SACTqB,EAAKuB,cAAcP,EAAS5C,YAAY,OAAMV,IAGhDyD,EAAUnB,EAAKa,cAAcG,EAAU3C,EAAWC,EAAWyC,GAEzDC,EAASrC,OAAS,GAAK2B,GAAUa,EAAU,GAAKZ,EAClD,OAAON,EAAK/B,EAAY8B,EAAMgB,EAAS5C,WAAYC,EAAWC,EAAW0B,EAAKzB,kBAG9EqC,EAASG,GAAgBC,CArB3B,MAFEJ,EAASG,QAAgBrD,CAyB7B,CA0JN,IAAmB4D,EAxJbd,GACF,CAMA,GAAIV,GACF,SAAU0B,IACRtB,YAAW,WACT,GAAIM,EAAaC,EACf,OAAOX,IAGJgB,KACHU,GAEJ,GAAG,EACJ,CAVD,QAYA,KAAOhB,GAAcC,GAAe,CAClC,IAAIgB,EAAMX,IAEV,GAAIW,EACF,OAAOA,CAEX,CAEJ,EACAF,cAAe,SAAuBnD,EAAYe,EAAOL,GACvD,IAAI4C,EAAOtD,EAAWA,EAAWM,OAAS,GAEtCgD,GAAQA,EAAKvC,QAAUA,GAASuC,EAAK5C,UAAYA,EAGnDV,EAAWA,EAAWM,OAAS,GAAK,CAClCQ,MAAOwC,EAAKxC,MAAQ,EACpBC,MAAOA,EACPL,QAASA,GAGXV,EAAWuD,KAAK,CACdzC,MAAO,EACPC,MAAOA,EACPL,QAASA,GAGf,EACA+B,cAAe,SAAuBG,EAAU3C,EAAWC,EAAWyC,GAOpE,IANA,IAAIT,EAASjC,EAAUK,OACnB6B,EAASjC,EAAUI,OACnBC,EAASqC,EAASrC,OAClBC,EAASD,EAASoC,EAClBa,EAAc,EAEXjD,EAAS,EAAI2B,GAAU1B,EAAS,EAAI2B,GAAUR,KAAKN,OAAOpB,EAAUM,EAAS,GAAIL,EAAUM,EAAS,KACzGD,IACAC,IACAgD,IAUF,OAPIA,GACFZ,EAAS5C,WAAWuD,KAAK,CACvBzC,MAAO0C,IAIXZ,EAASrC,OAASA,EACXC,CACT,EACAa,OAAQ,SAAgBoC,EAAMC,GAC5B,OAAI/B,KAAKH,QAAQmC,WACRhC,KAAKH,QAAQmC,WAAWF,EAAMC,GAE9BD,IAASC,GAAS/B,KAAKH,QAAQoC,YAAcH,EAAKI,gBAAkBH,EAAMG,aAErF,EACA7B,YAAa,SAAqB8B,GAGhC,IAFA,IAAIT,EAAM,GAEDnC,EAAI,EAAGA,EAAI4C,EAAMxD,OAAQY,IAC5B4C,EAAM5C,IACRmC,EAAIE,KAAKO,EAAM5C,IAInB,OAAOmC,CACT,EACAtB,UAAW,SAAmBpB,GAC5B,OAAOA,CACT,EACAsB,SAAU,SAAkBtB,GAC1B,OAAOA,EAAMoD,MAAM,GACrB,EACAnD,KAAM,SAAcoD,GAClB,OAAOA,EAAMpD,KAAK,GACpB,GA+DkB,IAAIf,EAKxB,SAASoE,EAAgBzC,EAAS0C,GAChC,GAAuB,oBAAZ1C,EACT0C,EAASxC,SAAWF,OACf,GAAIA,EACT,IAAK,IAAI7B,KAAQ6B,EAEXA,EAAQ2C,eAAexE,KACzBuE,EAASvE,GAAQ6B,EAAQ7B,IAK/B,OAAOuE,CACT,CAoBA,IAAIE,EAAoB,gEACpBC,EAAe,KACfC,EAAW,IAAIzE,EA2BnB,SAAS0E,EAAUC,EAAQC,EAAQjD,GAIjC,OAHAA,EAAUyC,EAAgBzC,EAAS,CACjCkD,kBAAkB,IAEbJ,EAASvE,KAAKyE,EAAQC,EAAQjD,EACvC,CA9BA8C,EAASjD,OAAS,SAAUoC,EAAMC,GAMhC,OALI/B,KAAKH,QAAQoC,aACfH,EAAOA,EAAKI,cACZH,EAAQA,EAAMG,eAGTJ,IAASC,GAAS/B,KAAKH,QAAQkD,mBAAqBL,EAAaM,KAAKlB,KAAUY,EAAaM,KAAKjB,EAC3G,EAEAY,EAASrC,SAAW,SAAUtB,GAI5B,IAFA,IAAIiE,EAASjE,EAAMoD,MAAM,mCAEhB7C,EAAI,EAAGA,EAAI0D,EAAOtE,OAAS,EAAGY,KAEhC0D,EAAO1D,EAAI,IAAM0D,EAAO1D,EAAI,IAAMkD,EAAkBO,KAAKC,EAAO1D,KAAOkD,EAAkBO,KAAKC,EAAO1D,EAAI,MAC5G0D,EAAO1D,IAAM0D,EAAO1D,EAAI,GACxB0D,EAAOC,OAAO3D,EAAI,EAAG,GACrBA,KAIJ,OAAO0D,CACT,EAYA,IAAIE,EAAW,IAAIjF,EAEnBiF,EAAS7C,SAAW,SAAUtB,GAC5B,IAAIoE,EAAW,GACXC,EAAmBrE,EAAMoD,MAAM,aAE9BiB,EAAiBA,EAAiB1E,OAAS,IAC9C0E,EAAiB1D,MAInB,IAAK,IAAIJ,EAAI,EAAGA,EAAI8D,EAAiB1E,OAAQY,IAAK,CAChD,IAAI+D,EAAOD,EAAiB9D,GAExBA,EAAI,IAAMS,KAAKH,QAAQ0D,eACzBH,EAASA,EAASzE,OAAS,IAAM2E,GAE7BtD,KAAKH,QAAQkD,mBACfO,EAAOA,EAAKE,QAGdJ,EAASxB,KAAK0B,GAElB,CAEA,OAAOF,CACT,EAYA,IAAIK,EAAe,IAAIvF,EAEvBuF,EAAanD,SAAW,SAAUtB,GAChC,OAAOA,EAAMoD,MAAM,wBACrB,EAMA,IAAIsB,EAAU,IAAIxF,EAUlB,SAASyF,EAAQC,GAaf,OATED,EADoB,oBAAXE,QAAoD,kBAApBA,OAAOC,SACtC,SAAUF,GAClB,cAAcA,CAChB,EAEU,SAAUA,GAClB,OAAOA,GAAyB,oBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOjE,UAAY,gBAAkBgE,CAC3H,EAGKD,EAAQC,EACjB,CAtBAF,EAAQpD,SAAW,SAAUtB,GAC3B,OAAOA,EAAMoD,MAAM,gBACrB,EAuDA,IAAI4B,EAA0BC,OAAOrE,UAAUsE,SAC3CC,EAAW,IAAIjG,EAyBnB,SAASkG,EAAaR,EAAKS,EAAOC,EAAkBC,EAAUC,GAQ5D,IAAIjF,EAQAkF,EANJ,IATAJ,EAAQA,GAAS,GACjBC,EAAmBA,GAAoB,GAEnCC,IACFX,EAAMW,EAASC,EAAKZ,IAKjBrE,EAAI,EAAGA,EAAI8E,EAAM1F,OAAQY,GAAK,EACjC,GAAI8E,EAAM9E,KAAOqE,EACf,OAAOU,EAAiB/E,GAM5B,GAAI,mBAAqByE,EAAwBU,KAAKd,GAAM,CAK1D,IAJAS,EAAMzC,KAAKgC,GACXa,EAAmB,IAAIE,MAAMf,EAAIjF,QACjC2F,EAAiB1C,KAAK6C,GAEjBlF,EAAI,EAAGA,EAAIqE,EAAIjF,OAAQY,GAAK,EAC/BkF,EAAiBlF,GAAK6E,EAAaR,EAAIrE,GAAI8E,EAAOC,EAAkBC,EAAUC,GAKhF,OAFAH,EAAM1E,MACN2E,EAAiB3E,MACV8E,CACT,CAMA,GAJIb,GAAOA,EAAIgB,SACbhB,EAAMA,EAAIgB,UAGS,WAAjBjB,EAAQC,IAA6B,OAARA,EAAc,CAC7CS,EAAMzC,KAAKgC,GACXa,EAAmB,CAAC,EACpBH,EAAiB1C,KAAK6C,GAEtB,IACII,EADAC,EAAa,GAGjB,IAAKD,KAAQjB,EAEPA,EAAIpB,eAAeqC,IACrBC,EAAWlD,KAAKiD,GAMpB,IAFAC,EAAWC,OAENxF,EAAI,EAAGA,EAAIuF,EAAWnG,OAAQY,GAAK,EAEtCkF,EADAI,EAAOC,EAAWvF,IACO6E,EAAaR,EAAIiB,GAAOR,EAAOC,EAAkBC,EAAUM,GAGtFR,EAAM1E,MACN2E,EAAiB3E,KACnB,MACE8E,EAAmBb,EAGrB,OAAOa,CACT,CAvFAN,EAAS3F,iBAAkB,EAC3B2F,EAAS7D,SAAW6C,EAAS7C,SAE7B6D,EAAS/D,UAAY,SAAUpB,GAC7B,IAAIgG,EAAgBhF,KAAKH,QACrBoF,EAAuBD,EAAcC,qBACrCC,EAAwBF,EAAcG,kBACtCA,OAA8C,IAA1BD,EAAmC,SAAUE,EAAGC,GACtE,MAAoB,qBAANA,EAAoBJ,EAAuBI,CAC3D,EAAIH,EACJ,MAAwB,kBAAVlG,EAAqBA,EAAQsG,KAAKC,UAAUnB,EAAapF,EAAO,KAAM,KAAMmG,GAAoBA,EAAmB,KACnI,EAEAhB,EAASzE,OAAS,SAAUoC,EAAMC,GAChC,OAAO7D,EAAK0B,UAAUF,OAAOgF,KAAKP,EAAUrC,EAAK/D,QAAQ,aAAc,MAAOgE,EAAMhE,QAAQ,aAAc,MAC5G,EA0EA,IAAIyH,EAAY,IAAItH,EAEpBsH,EAAUlF,SAAW,SAAUtB,GAC7B,OAAOA,EAAME,OACf,EAEAsG,EAAUvG,KAAOuG,EAAUnF,YAAc,SAAUrB,GACjD,OAAOA,CACT,C,sOCxhBO,MAAMyG,GACX,EAAAC,EAAAA,eAA+C,MCK3CC,EAAmC,CACvCC,UAAU,EACVlI,MAAO,MAGF,MAAMmI,UAAsBC,EAAAA,UAIjCC,MAAA,KAAQJ,EAAR,GAEA,+BAAOK,CAAyBtI,GAC9B,MAAO,CAAEkI,UAAU,E,MAAMlI,EAC3B,CAEAuI,mBAAA,UAAAC,EAAA,YAAqB,WACnB,MAAMxI,MAAEA,GAAUwI,EAAKH,MAEvB,GAAc,OAAVrI,EAAgB,SAAAyI,EAAArG,UAAAnB,OAHGd,EAAA,IAAA8G,MAAAwB,GAAAtB,EAAA,EAAAA,EAAAsB,EAAAtB,IAAAhH,EAAAgH,GAAA/E,UAAA+E,GAIrBqB,EAAKE,MAAMC,UAAU,C,KACnBxI,EACAyI,OAAQ,mBAGVJ,EAAKK,SAASZ,EAChB,CACF,CAAC,EAXD,GAaAa,iBAAAA,CAAkB9I,EAAc+I,GAC9BzG,KAAKoG,MAAMM,UAAUhJ,EAAO+I,EAC9B,CAEAE,kBAAAA,CACEC,EACAC,GAEA,MAAMjB,SAAEA,GAAa5F,KAAK+F,OACpBe,UAAEA,GAAc9G,KAAKoG,MAQzBR,GACoB,OAApBiB,EAAUnJ,OAqDhB,WAAuD,IAA9BN,EAAA0C,UAAAnB,OAAA,QAAAhB,IAAAmC,UAAA,GAAAA,UAAA,GAAW,GAAIzC,EAAAyC,UAAAnB,OAAA,QAAAhB,IAAAmC,UAAA,GAAAA,UAAA,GAAW,GACjD,OACE1C,EAAEuB,SAAWtB,EAAEsB,QAAUvB,EAAE2J,MAAK,CAACC,EAAMC,KAAWhD,OAAOiD,GAAGF,EAAM3J,EAAE4J,KAExE,CAxDME,CAAgBP,EAAUE,UAAWA,KAErC9G,KAAKoG,MAAMC,UAAU,CACnBe,KAAMN,EACNO,KAAMT,EAAUE,UAChBR,OAAQ,SAGVtG,KAAKuG,SAASZ,GAElB,CAEA2B,MAAAA,GACE,MAAMC,SAAEA,EAAQC,eAAEA,EAAcC,kBAAEA,EAAiBC,SAAEA,GACnD1H,KAAKoG,OACDR,SAAEA,EAAQlI,MAAEA,GAAUsC,KAAK+F,MAEjC,IAAI4B,EAAgBJ,EAEpB,GAAI3B,EAAU,CACZ,MAAMQ,EAAuB,C,MAC3B1I,EACAuI,mBAAoBjG,KAAKiG,oBAG3B,IAAI,EAAA2B,EAAAA,gBAAeF,GACjBC,EAAgBD,OACX,GAA8B,oBAAnBF,EAChBG,EAAgBH,EAAepB,OAC1B,KAAIqB,EAGT,MAAM,IAAI7J,MACR,8FAHF+J,GAAgB,EAAAE,EAAAA,eAAcJ,EAAmBrB,EAG/C,CAGN,CAEA,OAAO,EAAAyB,EAAAA,eACLpC,EAAqBqC,SACrB,CACE9I,MAAO,C,SACL4G,E,MACAlI,EACAuI,mBAAoBjG,KAAKiG,qBAG7B0B,EAEJ,EC5GK,SAASI,EACd/I,GAEA,GACW,MAATA,GAC0B,mBAAnBA,EAAM4G,UACuB,oBAA7B5G,EAAMiH,mBAEb,MAAM,IAAIrI,MAAM,kCAGlB,OAAO,CACT,CCLO,SAASoK,IACd,MAAMC,GAAU,EAAAC,EAAAA,YAAWzC,GAE3BsC,EAA2BE,GAE3B,MAAOlC,EAAOQ,IAAY,EAAA4B,EAAAA,UAGvB,CACDzK,MAAO,KACP0K,UAAU,IAGNC,GAAW,EAAAC,EAAAA,UACf,KAAM,CACJC,cAAeA,KACbN,GAAShC,qBACTM,EAAS,CAAE7I,MAAO,KAAM0K,UAAU,GAAQ,EAE5CI,aAAe9K,GACb6I,EAAS,C,MACP7I,EACA0K,UAAU,OAGhB,CAACH,GAAShC,qBAGZ,GAAIF,EAAMqC,SACR,MAAMrC,EAAMrI,MAGd,OAAO2K,CACT,C,iCCtCO,SAASI,EACdC,EACAC,GAEA,MAAMC,EAAiCxC,IAC9B,EAAAyB,EAAAA,eACLhC,EACA8C,GACA,EAAAd,EAAAA,eAAca,EAAWtC,IAKvBpI,EAAO0K,EAAUG,aAAeH,EAAU1K,MAAQ,UAGxD,OAFA4K,EAAQC,YAAc,qBAAqB7K,KAEpC4K,CACT,C,8HCyGO,SAASE,EAMdC,EACAC,EAGAC,GAEA,MAAMC,GAAgBC,EAAAA,EAAAA,IAAeJ,EAAMC,EAAMC,GACjD,OAAOG,EAAAA,EAAAA,GAAaF,EAAeG,EAAAA,EACpC,C,8HCjHM,MAAMC,UAKHC,EAAAA,EAeRxF,WAAAA,CACEyF,EACA3J,GAEA4J,QAEAzJ,KAAKwJ,OAASA,EACdxJ,KAAK0J,WAAW7J,GAChBG,KAAK2J,cACL3J,KAAK4J,cACN,CAESD,WAAAA,GACR3J,KAAK6J,OAAS7J,KAAK6J,OAAOC,KAAK9J,MAC/BA,KAAK+J,MAAQ/J,KAAK+J,MAAMD,KAAK9J,KAC9B,CAED0J,UAAAA,CACE7J,GACA,IAAAmK,EACA,MAAMC,EAAcjK,KAAKH,QACzBG,KAAKH,QAAUG,KAAKwJ,OAAOU,uBAAuBrK,IAC7CsK,EAAAA,EAAAA,IAAoBF,EAAajK,KAAKH,UACzCG,KAAKwJ,OAAOY,mBAAmBC,OAAO,CACpCC,KAAM,yBACNC,SAAUvK,KAAKwK,gBACfC,SAAUzK,OAGd,OAAAgK,EAAAhK,KAAKwK,kBAALR,EAAsBN,WAAW1J,KAAKH,QACvC,CAES6K,aAAAA,GACkB,IAAAC,EAArB3K,KAAK4K,iBACR,OAAAD,EAAA3K,KAAKwK,kBAALG,EAAsBE,eAAe7K,MAExC,CAED8K,gBAAAA,CAAiBC,GACf/K,KAAK4J,eAGL,MAAMoB,EAA+B,CACnCC,WAAW,GAGO,YAAhBF,EAAOT,KACTU,EAAcE,WAAY,EACD,UAAhBH,EAAOT,OAChBU,EAActE,SAAU,GAG1B1G,KAAKqK,OAAOW,EACb,CAEDG,gBAAAA,GAME,OAAOnL,KAAKoL,aACb,CAEDrB,KAAAA,GACE/J,KAAKwK,qBAAkB7M,EACvBqC,KAAK4J,eACL5J,KAAKqK,OAAO,CAAEY,WAAW,GAC1B,CAEDpB,MAAAA,CACEwB,EACAxL,GAgBA,OAdAG,KAAKsL,cAAgBzL,EAEjBG,KAAKwK,iBACPxK,KAAKwK,gBAAgBK,eAAe7K,MAGtCA,KAAKwK,gBAAkBxK,KAAKwJ,OAAOY,mBAAmBmB,MAAMvL,KAAKwJ,OAAQ,IACpExJ,KAAKH,QACRwL,UACuB,qBAAdA,EAA4BA,EAAYrL,KAAKH,QAAQwL,YAGhErL,KAAKwK,gBAAgBgB,YAAYxL,MAE1BA,KAAKwK,gBAAgBiB,SAC7B,CAEO7B,YAAAA,GACN,MAAM7D,EAAQ/F,KAAKwK,gBACfxK,KAAKwK,gBAAgBzE,OACrB2F,EAAAA,EAAAA,KAEEC,EAKF,IACC5F,EACH6F,UAA4B,YAAjB7F,EAAM8F,OACjBC,UAA4B,YAAjB/F,EAAM8F,OACjBE,QAA0B,UAAjBhG,EAAM8F,OACfG,OAAyB,SAAjBjG,EAAM8F,OACdhC,OAAQ7J,KAAK6J,OACbE,MAAO/J,KAAK+J,OAGd/J,KAAKoL,cAAgBO,CAMtB,CAEOtB,MAAAA,CAAOxK,GACboM,EAAAA,EAAcC,OAAM,KAGO,IAAAC,EAAAC,EAAAC,EAAAC,EADzB,GAAItM,KAAKsL,eAAiBtL,KAAK4K,eAC7B,GAAI/K,EAAQqL,UAER,OADFiB,GAAAC,EAAApM,KAAKsL,eAAcJ,YACjBiB,EAAAzH,KAAA0H,EAAApM,KAAKoL,cAAcmB,KACnBvM,KAAKoL,cAAcC,UACnBrL,KAAKoL,cAAcnD,SAErB,OAAAoE,GAAAC,EAAAtM,KAAKsL,eAAckB,YAAnBH,EAAA3H,KAAA4H,EACEtM,KAAKoL,cAAcmB,KACnB,KACAvM,KAAKoL,cAAcC,UACnBrL,KAAKoL,cAAcnD,cAEhB,GAAIpI,EAAQ6G,QAAS,KAAA+F,EAAAC,EAAAC,EAAAC,EAExB,OADFH,GAAAC,EAAA1M,KAAKsL,eAAc5E,UACjB+F,EAAA/H,KAAAgI,EAAA1M,KAAKoL,cAAc1N,MACnBsC,KAAKoL,cAAcC,UACnBrL,KAAKoL,cAAcnD,SAErB,OAAA0E,GAAAC,EAAA5M,KAAKsL,eAAckB,YAAnBG,EAAAjI,KAAAkI,OACEjP,EACAqC,KAAKoL,cAAc1N,MACnBsC,KAAKoL,cAAcC,UACnBrL,KAAKoL,cAAcnD,QAEtB,CAICpI,EAAQoL,WACVjL,KAAKiL,UAAU4B,SAAQC,IAAkB,IAAjB,SAAEC,GAAHD,EACrBC,EAAS/M,KAAKoL,cAAd,GAEH,GAEJ,E,0BC3II,SAAS4B,EAMdjE,EAIAC,EAGAC,GAEA,MAAMpJ,GAAUoN,EAAAA,EAAAA,IAAkBlE,EAAMC,EAAMC,GACxCiE,GAAcC,EAAAA,EAAAA,IAAe,CAAElF,QAASpI,EAAQoI,WAE/CwC,GAAY2C,EAAAA,UACjB,IACE,IAAI9D,EACF4D,EACArN,KAINuN,EAAAA,WAAgB,KACd3C,EAASf,WAAW7J,EAApB,GACC,CAAC4K,EAAU5K,IAEd,MAAM8L,GAAS0B,EAAAA,EAAAA,GACbD,EAAAA,aACGE,GACC7C,EAAS8C,UAAUtB,EAAAA,EAAcuB,WAAWF,KAC9C,CAAC7C,KAEH,IAAMA,EAASU,qBACf,IAAMV,EAASU,qBAGXtB,EAASuD,EAAAA,aAGb,CAAC/B,EAAWC,KACVb,EAASZ,OAAOwB,EAAWC,GAAemC,MAAMC,EAAhD,GAEF,CAACjD,IAGH,GACEkB,EAAOjO,QACPiQ,EAAAA,EAAAA,GAAiBlD,EAAS5K,QAAQ+N,iBAAkB,CAACjC,EAAOjO,QAE5D,MAAMiO,EAAOjO,MAGf,MAAO,IAAKiO,EAAQ9B,SAAQgE,YAAalC,EAAO9B,OACjD,CAGD,SAAS6D,IAAQ,C,8HC/GV,MAAMI,UAAwBvE,EAAAA,EAOnCxF,WAAAA,CAAYyF,EAAqBuE,GAC/BtE,QAEAzJ,KAAKwJ,OAASA,EACdxJ,KAAK+N,QAAU,GACf/N,KAAK2L,OAAS,GACd3L,KAAKgO,UAAY,GACjBhO,KAAKiO,aAAe,CAAC,EAEjBF,GACF/N,KAAKkO,WAAWH,EAEnB,CAESI,WAAAA,GACoB,IAAxBnO,KAAKiL,UAAUmD,MACjBpO,KAAKgO,UAAUnB,SAASpC,IACtBA,EAAS8C,WAAW5B,IAClB3L,KAAKqO,SAAS5D,EAAUkB,EAAxB,GADF,GAKL,CAESjB,aAAAA,GACH1K,KAAKiL,UAAUmD,MAClBpO,KAAKsO,SAER,CAEDA,OAAAA,GACEtO,KAAKiL,UAAY,IAAIsD,IACrBvO,KAAKgO,UAAUnB,SAASpC,IACtBA,EAAS6D,SAAT,GAEH,CAEDJ,UAAAA,CACEH,EACA/C,GAEAhL,KAAK+N,QAAUA,EAEf9B,EAAAA,EAAcC,OAAM,KAClB,MAAMsC,EAAgBxO,KAAKgO,UAErBS,EAAqBzO,KAAK0O,sBAAsB1O,KAAK+N,SAG3DU,EAAmB5B,SAAS8B,GAC1BA,EAAMlE,SAASf,WAAWiF,EAAMC,sBAAuB5D,KAGzD,MAAM6D,EAAeJ,EAAmBnP,KAAKqP,GAAUA,EAAMlE,WACvDqE,EAAkB7K,OAAO8K,YAC7BF,EAAavP,KAAKmL,GAAa,CAACA,EAAS5K,QAAQmP,UAAWvE,MAExDwE,EAAYJ,EAAavP,KAAKmL,GAClCA,EAASU,qBAGL+D,EAAiBL,EAAa9H,MAClC,CAAC0D,EAAUxD,IAAUwD,IAAa+D,EAAcvH,MAE9CuH,EAAc7P,SAAWkQ,EAAalQ,QAAWuQ,KAIrDlP,KAAKgO,UAAYa,EACjB7O,KAAKiO,aAAea,EACpB9O,KAAK2L,OAASsD,EAETjP,KAAK4K,kBAIVuE,EAAAA,EAAAA,IAAWX,EAAeK,GAAchC,SAASpC,IAC/CA,EAAS6D,SAAT,KAGFa,EAAAA,EAAAA,IAAWN,EAAcL,GAAe3B,SAASpC,IAC/CA,EAAS8C,WAAW5B,IAClB3L,KAAKqO,SAAS5D,EAAUkB,EAAxB,GADF,IAKF3L,KAAKqK,UAAL,GAEH,CAEDc,gBAAAA,GACE,OAAOnL,KAAK2L,MACb,CAEDyD,UAAAA,GACE,OAAOpP,KAAKgO,UAAU1O,KAAKmL,GAAaA,EAAS4E,mBAClD,CAEDC,YAAAA,GACE,OAAOtP,KAAKgO,SACb,CAEDuB,mBAAAA,CAAoBxB,GAClB,OAAO/N,KAAK0O,sBAAsBX,GAASzO,KAAKqP,GAC9CA,EAAMlE,SAAS8E,oBAAoBZ,EAAMC,wBAE5C,CAEOF,qBAAAA,CACNX,GAEA,MAAMS,EAAgBxO,KAAKgO,UACrBwB,EAAmB,IAAIC,IAC3BjB,EAAclP,KAAKmL,GAAa,CAACA,EAAS5K,QAAQmP,UAAWvE,MAGzDmE,EAAwBb,EAAQzO,KAAKO,GACzCG,KAAKwJ,OAAOkG,oBAAoB7P,KAG5B8P,EACJf,EAAsBgB,SAASC,IAC7B,MAAMlB,EAAQa,EAAiBM,IAAID,EAAiBb,WACpD,OAAa,MAATL,EACK,CAAC,CAAEC,sBAAuBiB,EAAkBpF,SAAUkE,IAExD,EAAP,IAGEoB,EAAqB,IAAIxB,IAC7BoB,EAAkBrQ,KAAKqP,GAAUA,EAAMC,sBAAsBI,aAEzDgB,EAAmBpB,EAAsBqB,QAC5CJ,IAAsBE,EAAmBG,IAAIL,EAAiBb,aAG3DmB,EAAuB,IAAI5B,IAC/BoB,EAAkBrQ,KAAKqP,GAAUA,EAAMlE,YAEnC2F,EAAqB5B,EAAcyB,QACtCI,IAAkBF,EAAqBD,IAAIG,KAGxCC,EAAezQ,IACnB,MAAMgQ,EAAmB7P,KAAKwJ,OAAOkG,oBAAoB7P,GACnD0Q,EAAkBvQ,KAAKiO,aAAa4B,EAAiBb,WAC3D,OAAO,MAAAuB,EAAAA,EAAmB,IAAIlH,EAAAA,EAAcrJ,KAAKwJ,OAAQqG,EAAzD,EAGIW,EAA6CR,EAAiB1Q,KAClE,CAACO,EAASoH,KACR,GAAIpH,EAAQ4Q,iBAAkB,CAE5B,MAAMC,EAAyBN,EAAmBnJ,GAClD,QAA+BtJ,IAA3B+S,EACF,MAAO,CACL9B,sBAAuB/O,EACvB4K,SAAUiG,EAGf,CACD,MAAO,CACL9B,sBAAuB/O,EACvB4K,SAAU6F,EAAYzQ,GAFxB,IAcJ,OAAO8P,EACJgB,OAAOH,GACPzL,MATiC6L,CAClCxT,EACAC,IAEAuR,EAAsBiC,QAAQzT,EAAEwR,uBAChCA,EAAsBiC,QAAQxT,EAAEuR,wBAKnC,CAEOP,QAAAA,CAAS5D,EAAyBkB,GACxC,MAAM1E,EAAQjH,KAAKgO,UAAU6C,QAAQpG,IACtB,IAAXxD,IACFjH,KAAK2L,QAASmF,EAAAA,EAAAA,IAAU9Q,KAAK2L,OAAQ1E,EAAO0E,GAC5C3L,KAAKqK,SAER,CAEOA,MAAAA,GACN4B,EAAAA,EAAcC,OAAM,KAClBlM,KAAKiL,UAAU4B,SAAQC,IAAkB,IAAjB,SAAEC,GAAHD,EACrBC,EAAS/M,KAAK2L,OAAd,GADF,GAIH,E,2DCjEI,SAASoF,EAATjE,GAMe,IANsB,QAC1CiB,EAD0C,QAE1C9F,GAFK6E,EAOL,MAAMI,GAAcC,EAAAA,EAAAA,IAAe,CAAElF,YAC/B+I,GAAcC,EAAAA,EAAAA,KACdC,GAAqBC,EAAAA,EAAAA,KAErBC,EAAmBhE,EAAAA,SACvB,IACEW,EAAQzO,KAAKO,IACX,MAAMgQ,EAAmB3C,EAAYwC,oBAAoB7P,GAOzD,OAJAgQ,EAAiBwB,mBAAqBL,EAClC,cACA,aAEGnB,CAAP,KAEJ,CAAC9B,EAASb,EAAa8D,IAGzBI,EAAiBvE,SAASyE,KACxBC,EAAAA,EAAAA,IAAgBD,IAChBE,EAAAA,EAAAA,IAAgCF,EAAOJ,EAAvC,KAGFO,EAAAA,EAAAA,IAA2BP,GAE3B,MAAOzG,GAAY2C,EAAAA,UACjB,IAAM,IAAIU,EAAgBZ,EAAakE,KAGnCM,EAAmBjH,EAAS8E,oBAAoB6B,IAEtD/D,EAAAA,EAAAA,GACED,EAAAA,aACGE,GACC0D,EACI,KADO,EAEPvG,EAAS8C,UAAUtB,EAAAA,EAAcuB,WAAWF,KAClD,CAAC7C,EAAUuG,KAEb,IAAMvG,EAASU,qBACf,IAAMV,EAASU,qBAGjBiC,EAAAA,WAAgB,KAGd3C,EAASyD,WAAWkD,EAAkB,CAAEnG,WAAW,GAAnD,GACC,CAACmG,EAAkB3G,IAEtB,MAIMkH,EAJ0BD,EAAiB3K,MAAK,CAAC4E,EAAQ1E,KAC7D2K,EAAAA,EAAAA,IAAcR,EAAiBnK,GAAQ0E,EAAQqF,KAI7CU,EAAiB9B,SAAQ,CAACjE,EAAQ1E,KAChC,MAAMpH,EAAUuR,EAAiBnK,GAC3B4K,EAAgBpH,EAAS6E,eAAerI,GAE9C,GAAIpH,GAAWgS,EAAe,CAC5B,IAAID,EAAAA,EAAAA,IAAc/R,EAAS8L,EAAQqF,GACjC,OAAOc,EAAAA,EAAAA,IAAgBjS,EAASgS,EAAeX,IACtCa,EAAAA,EAAAA,IAAUpG,EAAQqF,KACtBc,EAAAA,EAAAA,IAAgBjS,EAASgS,EAAeX,EAEhD,CACD,MAAO,EAAP,IAEF,GAEJ,GAAIS,EAAiBhT,OAAS,EAC5B,MAAMqT,QAAQC,IAAIN,GAEpB,MAAMO,EAAkBzH,EAAS2E,aAC3B+C,EAAoCT,EAAiBU,MACzD,CAACzG,EAAQ1E,KAAT,IAAAoL,EAAAC,EAAA,OACEC,EAAAA,EAAAA,IAAY,CACV5G,SACAuF,qBACAtD,iBAAgB,OAAAyE,EAAE,OAAFC,EAAElB,EAAiBnK,SAAjB,EAAAqL,EAAyB1E,mBAA3ByE,EAChBf,MAAOY,EAAgBjL,IAL3B,IASF,SAAIkL,GAAAA,EAAmCzU,MACrC,MAAMyU,EAAkCzU,MAG1C,OAAOgU,CACR,C", "sources": ["../node_modules/invariant/browser.js", "../node_modules/diff/lib/index.mjs", "../node_modules/react-error-boundary/dist/src/ErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/ErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/assertErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/useErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/withErrorBoundary.ts", "../node_modules/@tanstack/react-query/src/useQuery.ts", "../node_modules/@tanstack/query-core/src/mutationObserver.ts", "../node_modules/@tanstack/react-query/src/useMutation.ts", "../node_modules/@tanstack/query-core/src/queriesObserver.ts", "../node_modules/@tanstack/react-query/src/useQueries.ts"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar invariant = function(condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error(\n        'Minified exception occurred; use the non-minified dev environment ' +\n        'for the full error message and additional helpful warnings.'\n      );\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(\n        format.replace(/%s/g, function() { return args[argIndex++]; })\n      );\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\n\nmodule.exports = invariant;\n", "function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    this.options = options;\n    var self = this;\n\n    function done(value) {\n      if (callback) {\n        setTimeout(function () {\n          callback(undefined, value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    } // Allow subclasses to massage the input prior to running\n\n\n    oldString = this.castInput(oldString);\n    newString = this.castInput(newString);\n    oldString = this.removeEmpty(this.tokenize(oldString));\n    newString = this.removeEmpty(this.tokenize(newString));\n    var newLen = newString.length,\n        oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n\n    if (options.maxEditLength) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n\n    var bestPath = [{\n      newPos: -1,\n      components: []\n    }]; // Seed editLength = 0, i.e. the content starts with the same values\n\n    var oldPos = this.extractCommon(bestPath[0], newString, oldString, 0);\n\n    if (bestPath[0].newPos + 1 >= newLen && oldPos + 1 >= oldLen) {\n      // Identity per the equality and tokenizer\n      return done([{\n        value: this.join(newString),\n        count: newString.length\n      }]);\n    } // Main worker method. checks all permutations of a given edit length for acceptance.\n\n\n    function execEditLength() {\n      for (var diagonalPath = -1 * editLength; diagonalPath <= editLength; diagonalPath += 2) {\n        var basePath = void 0;\n\n        var addPath = bestPath[diagonalPath - 1],\n            removePath = bestPath[diagonalPath + 1],\n            _oldPos = (removePath ? removePath.newPos : 0) - diagonalPath;\n\n        if (addPath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n\n        var canAdd = addPath && addPath.newPos + 1 < newLen,\n            canRemove = removePath && 0 <= _oldPos && _oldPos < oldLen;\n\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        } // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the new string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n\n\n        if (!canAdd || canRemove && addPath.newPos < removePath.newPos) {\n          basePath = clonePath(removePath);\n          self.pushComponent(basePath.components, undefined, true);\n        } else {\n          basePath = addPath; // No need to clone, we've pulled it from the list\n\n          basePath.newPos++;\n          self.pushComponent(basePath.components, true, undefined);\n        }\n\n        _oldPos = self.extractCommon(basePath, newString, oldString, diagonalPath); // If we have hit the end of both strings, then we are done\n\n        if (basePath.newPos + 1 >= newLen && _oldPos + 1 >= oldLen) {\n          return done(buildValues(self, basePath.components, newString, oldString, self.useLongestToken));\n        } else {\n          // Otherwise track this path as a potential candidate and continue.\n          bestPath[diagonalPath] = basePath;\n        }\n      }\n\n      editLength++;\n    } // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n\n\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength) {\n            return callback();\n          }\n\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength) {\n        var ret = execEditLength();\n\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  pushComponent: function pushComponent(components, added, removed) {\n    var last = components[components.length - 1];\n\n    if (last && last.added === added && last.removed === removed) {\n      // We need to clone here as the component clone operation is just\n      // as shallow array clone\n      components[components.length - 1] = {\n        count: last.count + 1,\n        added: added,\n        removed: removed\n      };\n    } else {\n      components.push({\n        count: 1,\n        added: added,\n        removed: removed\n      });\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath) {\n    var newLen = newString.length,\n        oldLen = oldString.length,\n        newPos = basePath.newPos,\n        oldPos = newPos - diagonalPath,\n        commonCount = 0;\n\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(newString[newPos + 1], oldString[oldPos + 1])) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n    }\n\n    if (commonCount) {\n      basePath.components.push({\n        count: commonCount\n      });\n    }\n\n    basePath.newPos = newPos;\n    return oldPos;\n  },\n  equals: function equals(left, right) {\n    if (this.options.comparator) {\n      return this.options.comparator(left, right);\n    } else {\n      return left === right || this.options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return value.split('');\n  },\n  join: function join(chars) {\n    return chars.join('');\n  }\n};\n\nfunction buildValues(diff, components, newString, oldString, useLongestToken) {\n  var componentPos = 0,\n      componentLen = components.length,\n      newPos = 0,\n      oldPos = 0;\n\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n\n      newPos += component.count; // Common case\n\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count; // Reverse add and remove so removes are output first to match common convention\n      // The diffing algorithm is tied to add then remove output and this is the simplest\n      // route to get the desired output with minimal overhead.\n\n      if (componentPos && components[componentPos - 1].added) {\n        var tmp = components[componentPos - 1];\n        components[componentPos - 1] = components[componentPos];\n        components[componentPos] = tmp;\n      }\n    }\n  } // Special case handle for when one terminal is ignored (i.e. whitespace).\n  // For this case we merge the terminal into the prior string and drop the change.\n  // This is only available for string mode.\n\n\n  var lastComponent = components[componentLen - 1];\n\n  if (componentLen > 1 && typeof lastComponent.value === 'string' && (lastComponent.added || lastComponent.removed) && diff.equals('', lastComponent.value)) {\n    components[componentLen - 2].value += lastComponent.value;\n    components.pop();\n  }\n\n  return components;\n}\n\nfunction clonePath(path) {\n  return {\n    newPos: path.newPos,\n    components: path.components.slice(0)\n  };\n}\n\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\n\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n\n  return defaults;\n}\n\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\n\nvar extendedWordChars = /^[A-Za-z\\xC0-\\u02C6\\u02C8-\\u02D7\\u02DE-\\u02FF\\u1E00-\\u1EFF]+$/;\nvar reWhitespace = /\\S/;\nvar wordDiff = new Diff();\n\nwordDiff.equals = function (left, right) {\n  if (this.options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n\n  return left === right || this.options.ignoreWhitespace && !reWhitespace.test(left) && !reWhitespace.test(right);\n};\n\nwordDiff.tokenize = function (value) {\n  // All whitespace symbols except newline group into one token, each newline - in separate token\n  var tokens = value.split(/([^\\S\\r\\n]+|[()[\\]{}'\"\\r\\n]|\\b)/); // Join the boundary splits that we do not consider to be boundaries. This is primarily the extended Latin character set.\n\n  for (var i = 0; i < tokens.length - 1; i++) {\n    // If we have an empty string in the next field and we have only word chars before and after, merge\n    if (!tokens[i + 1] && tokens[i + 2] && extendedWordChars.test(tokens[i]) && extendedWordChars.test(tokens[i + 2])) {\n      tokens[i] += tokens[i + 2];\n      tokens.splice(i + 1, 2);\n      i--;\n    }\n  }\n\n  return tokens;\n};\n\nfunction diffWords(oldStr, newStr, options) {\n  options = generateOptions(options, {\n    ignoreWhitespace: true\n  });\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordDiff.diff(oldStr, newStr, options);\n}\n\nvar lineDiff = new Diff();\n\nlineDiff.tokenize = function (value) {\n  var retLines = [],\n      linesAndNewlines = value.split(/(\\n|\\r\\n)/); // Ignore the final empty token that occurs if the string ends with a new line\n\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  } // Merge the content and line separators into single tokens\n\n\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n\n    if (i % 2 && !this.options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      if (this.options.ignoreWhitespace) {\n        line = line.trim();\n      }\n\n      retLines.push(line);\n    }\n  }\n\n  return retLines;\n};\n\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\n\nvar sentenceDiff = new Diff();\n\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\n\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\n\nvar cssDiff = new Diff();\n\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\n\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar objectPrototypeToString = Object.prototype.toString;\nvar jsonDiff = new Diff(); // Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\n\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\n\njsonDiff.castInput = function (value) {\n  var _this$options = this.options,\n      undefinedReplacement = _this$options.undefinedReplacement,\n      _this$options$stringi = _this$options.stringifyReplacer,\n      stringifyReplacer = _this$options$stringi === void 0 ? function (k, v) {\n    return typeof v === 'undefined' ? undefinedReplacement : v;\n  } : _this$options$stringi;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\n\njsonDiff.equals = function (left, right) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'));\n};\n\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n} // This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\n\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n\n  var i;\n\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n\n  var canonicalizedObj;\n\n  if ('[object Array]' === objectPrototypeToString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n\n    var sortedKeys = [],\n        _key;\n\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (obj.hasOwnProperty(_key)) {\n        sortedKeys.push(_key);\n      }\n    }\n\n    sortedKeys.sort();\n\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n\n  return canonicalizedObj;\n}\n\nvar arrayDiff = new Diff();\n\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\n\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\n\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\n\nfunction parsePatch(uniDiff) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var diffstr = uniDiff.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = uniDiff.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      list = [],\n      i = 0;\n\n  function parseIndex() {\n    var index = {};\n    list.push(index); // Parse diff metadata\n\n    while (i < diffstr.length) {\n      var line = diffstr[i]; // File header found, end parsing diff metadata\n\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      } // Diff index\n\n\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n\n      if (header) {\n        index.index = header[1];\n      }\n\n      i++;\n    } // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n\n\n    parseFileHeader(index);\n    parseFileHeader(index); // Parse hunks\n\n    index.hunks = [];\n\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n\n      if (/^(Index:|diff|\\-\\-\\-|\\+\\+\\+)\\s/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line && options.strict) {\n        // Ignore unexpected content unless in strict mode\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  } // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n\n\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)$/.exec(diffstr[i]);\n\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  } // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n\n\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n        chunkHeaderLine = diffstr[i++],\n        chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: [],\n      linedelimiters: []\n    }; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n\n    var addCount = 0,\n        removeCount = 0;\n\n    for (; i < diffstr.length; i++) {\n      // Lines starting with '---' could be mistaken for the \"remove line\" operation\n      // But they could be the header for the next file. Therefore prune such cases out.\n      if (diffstr[i].indexOf('--- ') === 0 && i + 2 < diffstr.length && diffstr[i + 1].indexOf('+++ ') === 0 && diffstr[i + 2].indexOf('@@') === 0) {\n        break;\n      }\n\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        hunk.linedelimiters.push(delimiters[i] || '\\n');\n\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        break;\n      }\n    } // Handle the empty block count case\n\n\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    } // Perform optional sanity checking\n\n\n    if (options.strict) {\n      if (addCount !== hunk.newLines) {\n        throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n\n      if (removeCount !== hunk.oldLines) {\n        throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n    }\n\n    return hunk;\n  }\n\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator (start, minLine, maxLine) {\n  var wantForward = true,\n      backwardExhausted = false,\n      forwardExhausted = false,\n      localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      } // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n\n\n      if (start + localOffset <= maxLine) {\n        return localOffset;\n      }\n\n      forwardExhausted = true;\n    }\n\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      } // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n\n\n      if (minLine <= start - localOffset) {\n        return -localOffset++;\n      }\n\n      backwardExhausted = true;\n      return iterator();\n    } // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n\n  };\n}\n\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n\n    uniDiff = uniDiff[0];\n  } // Apply the diff to the input\n\n\n  var lines = source.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = source.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      hunks = uniDiff.hunks,\n      compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n    return line === patchContent;\n  },\n      errorCount = 0,\n      fuzzFactor = options.fuzzFactor || 0,\n      minLine = 0,\n      offset = 0,\n      removeEOFNL,\n      addEOFNL;\n  /**\n   * Checks if the hunk exactly fits on the provided location\n   */\n\n\n  function hunkFits(hunk, toPos) {\n    for (var j = 0; j < hunk.lines.length; j++) {\n      var line = hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line;\n\n      if (operation === ' ' || operation === '-') {\n        // Context sanity check\n        if (!compareLine(toPos + 1, lines[toPos], operation, content)) {\n          errorCount++;\n\n          if (errorCount > fuzzFactor) {\n            return false;\n          }\n        }\n\n        toPos++;\n      }\n    }\n\n    return true;\n  } // Search best fit offsets for each hunk based on the previous ones\n\n\n  for (var i = 0; i < hunks.length; i++) {\n    var hunk = hunks[i],\n        maxLine = lines.length - hunk.oldLines,\n        localOffset = 0,\n        toPos = offset + hunk.oldStart - 1;\n    var iterator = distanceIterator(toPos, minLine, maxLine);\n\n    for (; localOffset !== undefined; localOffset = iterator()) {\n      if (hunkFits(hunk, toPos + localOffset)) {\n        hunk.offset = offset += localOffset;\n        break;\n      }\n    }\n\n    if (localOffset === undefined) {\n      return false;\n    } // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n\n\n    minLine = hunk.offset + hunk.oldStart + hunk.oldLines;\n  } // Apply patch hunks\n\n\n  var diffOffset = 0;\n\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var _hunk = hunks[_i],\n        _toPos = _hunk.oldStart + _hunk.offset + diffOffset - 1;\n\n    diffOffset += _hunk.newLines - _hunk.oldLines;\n\n    for (var j = 0; j < _hunk.lines.length; j++) {\n      var line = _hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line,\n          delimiter = _hunk.linedelimiters[j];\n\n      if (operation === ' ') {\n        _toPos++;\n      } else if (operation === '-') {\n        lines.splice(_toPos, 1);\n        delimiters.splice(_toPos, 1);\n        /* istanbul ignore else */\n      } else if (operation === '+') {\n        lines.splice(_toPos, 0, content);\n        delimiters.splice(_toPos, 0, delimiter);\n        _toPos++;\n      } else if (operation === '\\\\') {\n        var previousOperation = _hunk.lines[j - 1] ? _hunk.lines[j - 1][0] : null;\n\n        if (previousOperation === '+') {\n          removeEOFNL = true;\n        } else if (previousOperation === '-') {\n          addEOFNL = true;\n        }\n      }\n    }\n  } // Handle EOFNL insertion/removal\n\n\n  if (removeEOFNL) {\n    while (!lines[lines.length - 1]) {\n      lines.pop();\n      delimiters.pop();\n    }\n  } else if (addEOFNL) {\n    lines.push('');\n    delimiters.push('\\n');\n  }\n\n  for (var _k = 0; _k < lines.length - 1; _k++) {\n    lines[_k] = lines[_k] + delimiters[_k];\n  }\n\n  return lines.join('');\n} // Wrapper that supports multiple file patches via callbacks.\n\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  var currentIndex = 0;\n\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n\n    if (!index) {\n      return options.complete();\n    }\n\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n\n        processIndex();\n      });\n    });\n  }\n\n  processIndex();\n}\n\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n\n  var diff = diffLines(oldStr, newStr, options);\n\n  if (!diff) {\n    return;\n  }\n\n  diff.push({\n    value: '',\n    lines: []\n  }); // Append an empty value to make cleanup easier\n\n  function contextLines(lines) {\n    return lines.map(function (entry) {\n      return ' ' + entry;\n    });\n  }\n\n  var hunks = [];\n  var oldRangeStart = 0,\n      newRangeStart = 0,\n      curRange = [],\n      oldLine = 1,\n      newLine = 1;\n\n  var _loop = function _loop(i) {\n    var current = diff[i],\n        lines = current.lines || current.value.replace(/\\n$/, '').split('\\n');\n    current.lines = lines;\n\n    if (current.added || current.removed) {\n      var _curRange;\n\n      // If we have previous context, start with that\n      if (!oldRangeStart) {\n        var prev = diff[i - 1];\n        oldRangeStart = oldLine;\n        newRangeStart = newLine;\n\n        if (prev) {\n          curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n          oldRangeStart -= curRange.length;\n          newRangeStart -= curRange.length;\n        }\n      } // Output our changes\n\n\n      (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n        return (current.added ? '+' : '-') + entry;\n      }))); // Track the updated file position\n\n\n      if (current.added) {\n        newLine += lines.length;\n      } else {\n        oldLine += lines.length;\n      }\n    } else {\n      // Identical context lines. Track line changes\n      if (oldRangeStart) {\n        // Close out any changes that have been output (or join overlapping)\n        if (lines.length <= options.context * 2 && i < diff.length - 2) {\n          var _curRange2;\n\n          // Overlapping\n          (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n        } else {\n          var _curRange3;\n\n          // end the range and output\n          var contextSize = Math.min(lines.length, options.context);\n\n          (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n\n          var hunk = {\n            oldStart: oldRangeStart,\n            oldLines: oldLine - oldRangeStart + contextSize,\n            newStart: newRangeStart,\n            newLines: newLine - newRangeStart + contextSize,\n            lines: curRange\n          };\n\n          if (i >= diff.length - 2 && lines.length <= options.context) {\n            // EOF is inside this hunk\n            var oldEOFNewline = /\\n$/.test(oldStr);\n            var newEOFNewline = /\\n$/.test(newStr);\n            var noNlBeforeAdds = lines.length == 0 && curRange.length > hunk.oldLines;\n\n            if (!oldEOFNewline && noNlBeforeAdds && oldStr.length > 0) {\n              // special case: old has no eol and no trailing context; no-nl can end up before adds\n              // however, if the old file is empty, do not output the no-nl line\n              curRange.splice(hunk.oldLines, 0, '\\\\ No newline at end of file');\n            }\n\n            if (!oldEOFNewline && !noNlBeforeAdds || !newEOFNewline) {\n              curRange.push('\\\\ No newline at end of file');\n            }\n          }\n\n          hunks.push(hunk);\n          oldRangeStart = 0;\n          newRangeStart = 0;\n          curRange = [];\n        }\n      }\n\n      oldLine += lines.length;\n      newLine += lines.length;\n    }\n  };\n\n  for (var i = 0; i < diff.length; i++) {\n    _loop(i);\n  }\n\n  return {\n    oldFileName: oldFileName,\n    newFileName: newFileName,\n    oldHeader: oldHeader,\n    newHeader: newHeader,\n    hunks: hunks\n  };\n}\nfunction formatPatch(diff) {\n  var ret = [];\n\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i]; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return formatPatch(structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options));\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\n\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n      oldLines = _calcOldNewLineCount.oldLines,\n      newLines = _calcOldNewLineCount.newLines;\n\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {}; // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n\n  ret.hunks = [];\n  var mineIndex = 0,\n      theirsIndex = 0,\n      mineOffset = 0,\n      theirsOffset = 0;\n\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n      oldStart: Infinity\n    },\n        theirsCurrent = theirs.hunks[theirsIndex] || {\n      oldStart: Infinity\n    };\n\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n\n  return ret;\n}\n\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n\n    return structuredPatch(undefined, undefined, base, param);\n  }\n\n  return param;\n}\n\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\n\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\n\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\n\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\n\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n    offset: mineOffset,\n    lines: mineLines,\n    index: 0\n  },\n      their = {\n    offset: theirOffset,\n    lines: theirLines,\n    index: 0\n  }; // Handle any leading content\n\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine); // Now in the overlap content. Scan through and select the best changes from each.\n\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n        theirCurrent = their.lines[their.index];\n\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  } // Now push anything that may be remaining\n\n\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\n\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectChange(their);\n\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n\n    return;\n  }\n\n  conflict(hunk, myChanges, theirChanges);\n}\n\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectContext(their, myChanges);\n\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\n\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\n\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\n\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\n\nfunction collectChange(state) {\n  var ret = [],\n      operation = state.lines[state.index][0];\n\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index]; // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n\n  return ret;\n}\n\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n      merged = [],\n      matchIndex = 0,\n      contextChanges = false,\n      conflicted = false;\n\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n        match = matchChanges[matchIndex]; // Once we've hit our add, then we are done\n\n    if (match[0] === '+') {\n      break;\n    }\n\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++; // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n\n    if (change[0] === '+') {\n      conflicted = true;\n\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n\n  if (conflicted) {\n    return changes;\n  }\n\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\n\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\n\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n\n  state.index += delta;\n  return true;\n}\n\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n      change,\n      operation;\n\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n\n    ret.push([operation, change.value]);\n  }\n\n  return ret;\n}\n\nfunction convertChangesToXML(changes) {\n  var ret = [];\n\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n\n    ret.push(escapeHTML(change.value));\n\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n\n  return ret.join('');\n}\n\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\n\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, merge, parsePatch, structuredPatch };\n", "import { createContext } from \"react\";\n\nexport type ErrorBoundaryContextType = {\n  didCatch: boolean;\n  error: any;\n  resetErrorBoundary: (...args: any[]) => void;\n};\n\nexport const ErrorBoundaryContext =\n  createContext<ErrorBoundaryContextType | null>(null);\n", "import {\n  Component,\n  createElement,\n  ErrorInfo,\n  isValidElement,\n  PropsWithChildren,\n  PropsWithRef,\n  ReactElement,\n} from \"react\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\nimport { ErrorBoundaryProps, FallbackProps } from \"./types\";\n\ntype ErrorBoundaryState = { didCatch: boolean; error: any };\n\nconst initialState: ErrorBoundaryState = {\n  didCatch: false,\n  error: null,\n};\n\nexport class ErrorBoundary extends Component<\n  PropsWithRef<PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  state = initialState;\n\n  static getDerivedStateFromError(error: Error) {\n    return { didCatch: true, error };\n  }\n\n  resetErrorBoundary = (...args: any[]) => {\n    const { error } = this.state;\n\n    if (error !== null) {\n      this.props.onReset?.({\n        args,\n        reason: \"imperative-api\",\n      });\n\n      this.setState(initialState);\n    }\n  };\n\n  componentDidCatch(error: Error, info: ErrorInfo) {\n    this.props.onError?.(error, info);\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState\n  ) {\n    const { didCatch } = this.state;\n    const { resetKeys } = this.props;\n\n    // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n    // we'd end up resetting the error boundary immediately.\n    // This would likely trigger a second error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n\n    if (\n      didCatch &&\n      prevState.error !== null &&\n      hasArrayChanged(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onReset?.({\n        next: resetKeys,\n        prev: prevProps.resetKeys,\n        reason: \"keys\",\n      });\n\n      this.setState(initialState);\n    }\n  }\n\n  render() {\n    const { children, fallbackRender, FallbackComponent, fallback } =\n      this.props;\n    const { didCatch, error } = this.state;\n\n    let childToRender = children;\n\n    if (didCatch) {\n      const props: FallbackProps = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      };\n\n      if (isValidElement(fallback)) {\n        childToRender = fallback;\n      } else if (typeof fallbackRender === \"function\") {\n        childToRender = fallbackRender(props);\n      } else if (FallbackComponent) {\n        childToRender = createElement(FallbackComponent, props);\n      } else {\n        throw new Error(\n          \"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\"\n        );\n      }\n    }\n\n    return createElement(\n      ErrorBoundaryContext.Provider,\n      {\n        value: {\n          didCatch,\n          error,\n          resetErrorBoundary: this.resetErrorBoundary,\n        },\n      },\n      childToRender\n    ) as ReactElement;\n  }\n}\n\nfunction hasArrayChanged(a: any[] = [], b: any[] = []) {\n  return (\n    a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n  );\n}\n", "import { ErrorBoundaryContextType } from \"./ErrorBoundaryContext\";\n\nexport function assertErrorBoundaryContext(\n  value: any\n): value is ErrorBoundaryContextType {\n  if (\n    value == null ||\n    typeof value.didCatch !== \"boolean\" ||\n    typeof value.resetErrorBoundary !== \"function\"\n  ) {\n    throw new Error(\"ErrorBoundaryContext not found\");\n  }\n\n  return true;\n}\n", "import { useContext, useMemo, useState } from \"react\";\nimport { assertErrorBoundaryContext } from \"./assertErrorBoundaryContext\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\n\nexport type UseErrorBoundaryApi<Error> = {\n  resetBoundary: () => void;\n  showBoundary: (error: Error) => void;\n};\n\nexport function useErrorBoundary<Error = any>(): UseErrorBoundaryApi<Error> {\n  const context = useContext(ErrorBoundaryContext);\n\n  assertErrorBoundaryContext(context);\n\n  const [state, setState] = useState<{\n    error: Error | null;\n    hasError: boolean;\n  }>({\n    error: null,\n    hasError: false,\n  });\n\n  const memoized = useMemo(\n    () => ({\n      resetBoundary: () => {\n        context?.resetErrorBoundary();\n        setState({ error: null, hasError: false });\n      },\n      showBoundary: (error: Error) =>\n        setState({\n          error,\n          hasError: true,\n        }),\n    }),\n    [context?.resetErrorBoundary]\n  );\n\n  if (state.hasError) {\n    throw state.error;\n  }\n\n  return memoized;\n}\n", "import { ComponentType, createElement } from \"react\";\nimport { ErrorBoundary } from \"./ErrorBoundary\";\nimport { ErrorBoundaryProps } from \"./types\";\n\nexport function withErrorBoundary<Props extends Object>(\n  Component: ComponentType<Props>,\n  errorBoundaryProps: ErrorBoundaryProps\n): ComponentType<Props> {\n  const Wrapped: ComponentType<Props> = (props: Props) => {\n    return createElement(\n      ErrorBoundary,\n      errorBoundaryProps,\n      createElement(Component, props)\n    );\n  };\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || \"Unknown\";\n  Wrapped.displayName = `withErrorBoundary(${name})`;\n\n  return Wrapped;\n}\n", "import 'client-only'\nimport type { QueryFunction, QueryKey } from '@tanstack/query-core'\nimport { parseQueryArgs, QueryObserver } from '@tanstack/query-core'\nimport type {\n  DefinedUseQueryResult,\n  UseQueryOptions,\n  UseQueryResult,\n} from './types'\nimport { useBaseQuery } from './useBaseQuery'\n\n// HOOK\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey'\n  >,\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData?: () => undefined },\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn' | 'initialData'\n  > & { initialData: TQueryFnData | (() => TQueryFnData) },\n): DefinedUseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  queryKey: TQueryKey,\n  queryFn: QueryFunction<TQueryFnData, TQueryKey>,\n  options?: Omit<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n    'queryKey' | 'queryFn'\n  >,\n): UseQueryResult<TData, TError>\n\nexport function useQuery<\n  TQueryFnData,\n  TError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  arg1: TQueryKey | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg2?:\n    | QueryFunction<TQueryFnData, TQueryKey>\n    | UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  arg3?: UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UseQueryResult<TData, TError> {\n  const parsedOptions = parseQueryArgs(arg1, arg2, arg3)\n  return useBaseQuery(parsedOptions, QueryObserver)\n}\n", "import type { Action, Mutation } from './mutation'\nimport { getDefaultState } from './mutation'\nimport { notifyManager } from './notifyManager'\nimport type { QueryClient } from './queryClient'\nimport { Subscribable } from './subscribable'\nimport type {\n  MutateOptions,\n  MutationObserverBaseResult,\n  MutationObserverResult,\n  MutationObserverOptions,\n} from './types'\nimport { shallowEqualObjects } from './utils'\n\n// TYPES\n\ntype MutationObserverListener<TData, TError, TVariables, TContext> = (\n  result: MutationObserverResult<TData, TError, TVariables, TContext>,\n) => void\n\ninterface NotifyOptions {\n  listeners?: boolean\n  onError?: boolean\n  onSuccess?: boolean\n}\n\n// CLASS\n\nexport class MutationObserver<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n> extends Subscribable<\n  MutationObserverListener<TData, TError, TVariables, TContext>\n> {\n  options!: MutationObserverOptions<TData, TError, TVariables, TContext>\n\n  private client: QueryClient\n  private currentResult!: MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  >\n  private currentMutation?: Mutation<TData, TError, TVariables, TContext>\n  private mutateOptions?: MutateOptions<TData, TError, TVariables, TContext>\n\n  constructor(\n    client: QueryClient,\n    options: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    super()\n\n    this.client = client\n    this.setOptions(options)\n    this.bindMethods()\n    this.updateResult()\n  }\n\n  protected bindMethods(): void {\n    this.mutate = this.mutate.bind(this)\n    this.reset = this.reset.bind(this)\n  }\n\n  setOptions(\n    options?: MutationObserverOptions<TData, TError, TVariables, TContext>,\n  ) {\n    const prevOptions = this.options\n    this.options = this.client.defaultMutationOptions(options)\n    if (!shallowEqualObjects(prevOptions, this.options)) {\n      this.client.getMutationCache().notify({\n        type: 'observerOptionsUpdated',\n        mutation: this.currentMutation,\n        observer: this,\n      })\n    }\n    this.currentMutation?.setOptions(this.options)\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.hasListeners()) {\n      this.currentMutation?.removeObserver(this)\n    }\n  }\n\n  onMutationUpdate(action: Action<TData, TError, TVariables, TContext>): void {\n    this.updateResult()\n\n    // Determine which callbacks to trigger\n    const notifyOptions: NotifyOptions = {\n      listeners: true,\n    }\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true\n    }\n\n    this.notify(notifyOptions)\n  }\n\n  getCurrentResult(): MutationObserverResult<\n    TData,\n    TError,\n    TVariables,\n    TContext\n  > {\n    return this.currentResult\n  }\n\n  reset(): void {\n    this.currentMutation = undefined\n    this.updateResult()\n    this.notify({ listeners: true })\n  }\n\n  mutate(\n    variables?: TVariables,\n    options?: MutateOptions<TData, TError, TVariables, TContext>,\n  ): Promise<TData> {\n    this.mutateOptions = options\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this)\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, {\n      ...this.options,\n      variables:\n        typeof variables !== 'undefined' ? variables : this.options.variables,\n    })\n\n    this.currentMutation.addObserver(this)\n\n    return this.currentMutation.execute()\n  }\n\n  private updateResult(): void {\n    const state = this.currentMutation\n      ? this.currentMutation.state\n      : getDefaultState<TData, TError, TVariables, TContext>()\n\n    const result: MutationObserverBaseResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    > = {\n      ...state,\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset,\n    }\n\n    this.currentResult = result as MutationObserverResult<\n      TData,\n      TError,\n      TVariables,\n      TContext\n    >\n  }\n\n  private notify(options: NotifyOptions) {\n    notifyManager.batch(() => {\n      // First trigger the mutate callbacks\n      if (this.mutateOptions && this.hasListeners()) {\n        if (options.onSuccess) {\n          this.mutateOptions.onSuccess?.(\n            this.currentResult.data!,\n            this.currentResult.variables!,\n            this.currentResult.context!,\n          )\n          this.mutateOptions.onSettled?.(\n            this.currentResult.data!,\n            null,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        } else if (options.onError) {\n          this.mutateOptions.onError?.(\n            this.currentResult.error!,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n          this.mutateOptions.onSettled?.(\n            undefined,\n            this.currentResult.error,\n            this.currentResult.variables!,\n            this.currentResult.context,\n          )\n        }\n      }\n\n      // Then trigger the listeners\n      if (options.listeners) {\n        this.listeners.forEach(({ listener }) => {\n          listener(this.currentResult)\n        })\n      }\n    })\n  }\n}\n", "import 'client-only'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport type { MutationFunction, MutationKey } from '@tanstack/query-core'\nimport {\n  notifyManager,\n  parseMutationArgs,\n  MutationObserver,\n} from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  UseMutateFunction,\n  UseMutationOptions,\n  UseMutationResult,\n} from './types'\nimport { shouldThrowError } from './utils'\n\n// HOOK\n\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  options: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationFn: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  mutationKey: MutationKey,\n  mutationFn?: MutationFunction<TData, TVariables>,\n  options?: Omit<\n    UseMutationOptions<TData, TError, TVariables, TContext>,\n    'mutationKey' | 'mutationFn'\n  >,\n): UseMutationResult<TData, TError, TVariables, TContext>\nexport function useMutation<\n  TData = unknown,\n  TError = unknown,\n  TVariables = void,\n  TContext = unknown,\n>(\n  arg1:\n    | MutationKey\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg2?:\n    | MutationFunction<TData, TVariables>\n    | UseMutationOptions<TData, TError, TVariables, TContext>,\n  arg3?: UseMutationOptions<TData, TError, TVariables, TContext>,\n): UseMutationResult<TData, TError, TVariables, TContext> {\n  const options = parseMutationArgs(arg1, arg2, arg3)\n  const queryClient = useQueryClient({ context: options.context })\n\n  const [observer] = React.useState(\n    () =>\n      new MutationObserver<TData, TError, TVariables, TContext>(\n        queryClient,\n        options,\n      ),\n  )\n\n  React.useEffect(() => {\n    observer.setOptions(options)\n  }, [observer, options])\n\n  const result = useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  const mutate = React.useCallback<\n    UseMutateFunction<TData, TError, TVariables, TContext>\n  >(\n    (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop)\n    },\n    [observer],\n  )\n\n  if (\n    result.error &&\n    shouldThrowError(observer.options.useErrorBoundary, [result.error])\n  ) {\n    throw result.error\n  }\n\n  return { ...result, mutate, mutateAsync: result.mutate }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n", "import { difference, replaceAt } from './utils'\nimport { notifyManager } from './notifyManager'\nimport type {\n  QueryObserverOptions,\n  QueryObserverResult,\n  DefaultedQueryObserverOptions,\n} from './types'\nimport type { QueryClient } from './queryClient'\nimport type { NotifyOptions } from './queryObserver'\nimport { QueryObserver } from './queryObserver'\nimport { Subscribable } from './subscribable'\n\ntype QueriesObserverListener = (result: QueryObserverResult[]) => void\n\nexport class QueriesObserver extends Subscribable<QueriesObserverListener> {\n  private client: QueryClient\n  private result: QueryObserverResult[]\n  private queries: QueryObserverOptions[]\n  private observers: QueryObserver[]\n  private observersMap: Record<string, QueryObserver>\n\n  constructor(client: QueryClient, queries?: QueryObserverOptions[]) {\n    super()\n\n    this.client = client\n    this.queries = []\n    this.result = []\n    this.observers = []\n    this.observersMap = {}\n\n    if (queries) {\n      this.setQueries(queries)\n    }\n  }\n\n  protected onSubscribe(): void {\n    if (this.listeners.size === 1) {\n      this.observers.forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n    }\n  }\n\n  protected onUnsubscribe(): void {\n    if (!this.listeners.size) {\n      this.destroy()\n    }\n  }\n\n  destroy(): void {\n    this.listeners = new Set()\n    this.observers.forEach((observer) => {\n      observer.destroy()\n    })\n  }\n\n  setQueries(\n    queries: QueryObserverOptions[],\n    notifyOptions?: NotifyOptions,\n  ): void {\n    this.queries = queries\n\n    notifyManager.batch(() => {\n      const prevObservers = this.observers\n\n      const newObserverMatches = this.findMatchingObservers(this.queries)\n\n      // set options for the new observers to notify of changes\n      newObserverMatches.forEach((match) =>\n        match.observer.setOptions(match.defaultedQueryOptions, notifyOptions),\n      )\n\n      const newObservers = newObserverMatches.map((match) => match.observer)\n      const newObserversMap = Object.fromEntries(\n        newObservers.map((observer) => [observer.options.queryHash, observer]),\n      )\n      const newResult = newObservers.map((observer) =>\n        observer.getCurrentResult(),\n      )\n\n      const hasIndexChange = newObservers.some(\n        (observer, index) => observer !== prevObservers[index],\n      )\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return\n      }\n\n      this.observers = newObservers\n      this.observersMap = newObserversMap\n      this.result = newResult\n\n      if (!this.hasListeners()) {\n        return\n      }\n\n      difference(prevObservers, newObservers).forEach((observer) => {\n        observer.destroy()\n      })\n\n      difference(newObservers, prevObservers).forEach((observer) => {\n        observer.subscribe((result) => {\n          this.onUpdate(observer, result)\n        })\n      })\n\n      this.notify()\n    })\n  }\n\n  getCurrentResult(): QueryObserverResult[] {\n    return this.result\n  }\n\n  getQueries() {\n    return this.observers.map((observer) => observer.getCurrentQuery())\n  }\n\n  getObservers() {\n    return this.observers\n  }\n\n  getOptimisticResult(queries: QueryObserverOptions[]): QueryObserverResult[] {\n    return this.findMatchingObservers(queries).map((match) =>\n      match.observer.getOptimisticResult(match.defaultedQueryOptions),\n    )\n  }\n\n  private findMatchingObservers(\n    queries: QueryObserverOptions[],\n  ): QueryObserverMatch[] {\n    const prevObservers = this.observers\n    const prevObserversMap = new Map(\n      prevObservers.map((observer) => [observer.options.queryHash, observer]),\n    )\n\n    const defaultedQueryOptions = queries.map((options) =>\n      this.client.defaultQueryOptions(options),\n    )\n\n    const matchingObservers: QueryObserverMatch[] =\n      defaultedQueryOptions.flatMap((defaultedOptions) => {\n        const match = prevObserversMap.get(defaultedOptions.queryHash)\n        if (match != null) {\n          return [{ defaultedQueryOptions: defaultedOptions, observer: match }]\n        }\n        return []\n      })\n\n    const matchedQueryHashes = new Set(\n      matchingObservers.map((match) => match.defaultedQueryOptions.queryHash),\n    )\n    const unmatchedQueries = defaultedQueryOptions.filter(\n      (defaultedOptions) => !matchedQueryHashes.has(defaultedOptions.queryHash),\n    )\n\n    const matchingObserversSet = new Set(\n      matchingObservers.map((match) => match.observer),\n    )\n    const unmatchedObservers = prevObservers.filter(\n      (prevObserver) => !matchingObserversSet.has(prevObserver),\n    )\n\n    const getObserver = (options: QueryObserverOptions): QueryObserver => {\n      const defaultedOptions = this.client.defaultQueryOptions(options)\n      const currentObserver = this.observersMap[defaultedOptions.queryHash!]\n      return currentObserver ?? new QueryObserver(this.client, defaultedOptions)\n    }\n\n    const newOrReusedObservers: QueryObserverMatch[] = unmatchedQueries.map(\n      (options, index) => {\n        if (options.keepPreviousData) {\n          // return previous data from one of the observers that no longer match\n          const previouslyUsedObserver = unmatchedObservers[index]\n          if (previouslyUsedObserver !== undefined) {\n            return {\n              defaultedQueryOptions: options,\n              observer: previouslyUsedObserver,\n            }\n          }\n        }\n        return {\n          defaultedQueryOptions: options,\n          observer: getObserver(options),\n        }\n      },\n    )\n\n    const sortMatchesByOrderOfQueries = (\n      a: QueryObserverMatch,\n      b: QueryObserverMatch,\n    ): number =>\n      defaultedQueryOptions.indexOf(a.defaultedQueryOptions) -\n      defaultedQueryOptions.indexOf(b.defaultedQueryOptions)\n\n    return matchingObservers\n      .concat(newOrReusedObservers)\n      .sort(sortMatchesByOrderOfQueries)\n  }\n\n  private onUpdate(observer: QueryObserver, result: QueryObserverResult): void {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result)\n      this.notify()\n    }\n  }\n\n  private notify(): void {\n    notifyManager.batch(() => {\n      this.listeners.forEach(({ listener }) => {\n        listener(this.result)\n      })\n    })\n  }\n}\n\ntype QueryObserverMatch = {\n  defaultedQueryOptions: DefaultedQueryObserverOptions\n  observer: QueryObserver\n}\n", "import 'client-only'\nimport * as React from 'react'\nimport { useSyncExternalStore } from './useSyncExternalStore'\n\nimport type { QueryKey, QueryFunction } from '@tanstack/query-core'\nimport { notifyManager, QueriesObserver } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type { UseQueryOptions, UseQueryResult } from './types'\nimport { useIsRestoring } from './isRestoring'\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary'\nimport {\n  ensurePreventErrorBoundaryRetry,\n  getHasError,\n  useClearResetErrorBoundary,\n} from './errorBoundaryUtils'\nimport {\n  ensureStaleTime,\n  shouldSuspend,\n  fetchOptimistic,\n  willFetch,\n} from './suspense'\n\n// This defines the `UseQueryOptions` that are accepted in `QueriesOptions` & `GetOptions`.\n// - `context` is omitted as it is passed as a root-level option to `useQueries` instead.\ntype UseQueryOptionsForUseQueries<\n  TQueryFnData = unknown,\n  TError = unknown,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'context'>\n\n// Avoid TS depth-limit error in case of large array literal\ntype MAXIMUM_DEPTH = 20\n\ntype GetOptions<T> =\n  // Part 1: responsible for applying explicit type parameter to function arguments, if object { queryFnData: TQueryFnData, error: TError, data: TData }\n  T extends {\n    queryFnData: infer TQueryFnData\n    error?: infer TError\n    data: infer TData\n  }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryOptionsForUseQueries<unknown, TError, TData>\n    : // Part 2: responsible for applying explicit type parameter to function arguments, if tuple [TQueryFnData, TError, TData]\n    T extends [infer TQueryFnData, infer TError, infer TData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryOptionsForUseQueries<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryOptionsForUseQueries<TQueryFnData>\n    : // Part 3: responsible for inferring and enforcing type if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryOptionsForUseQueries<TQueryFnData, unknown, TData, TQueryKey>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> }\n    ? UseQueryOptionsForUseQueries<\n        TQueryFnData,\n        unknown,\n        TQueryFnData,\n        TQueryKey\n      >\n    : // Fallback\n      UseQueryOptionsForUseQueries\n\ntype GetResults<T> =\n  // Part 1: responsible for mapping explicit type parameter to function result, if object\n  T extends { queryFnData: any; error?: infer TError; data: infer TData }\n    ? UseQueryResult<TData, TError>\n    : T extends { queryFnData: infer TQueryFnData; error?: infer TError }\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends { data: infer TData; error?: infer TError }\n    ? UseQueryResult<TData, TError>\n    : // Part 2: responsible for mapping explicit type parameter to function result, if tuple\n    T extends [any, infer TError, infer TData]\n    ? UseQueryResult<TData, TError>\n    : T extends [infer TQueryFnData, infer TError]\n    ? UseQueryResult<TQueryFnData, TError>\n    : T extends [infer TQueryFnData]\n    ? UseQueryResult<TQueryFnData>\n    : // Part 3: responsible for mapping inferred type to results, if no explicit parameter was provided\n    T extends {\n        queryFn?: QueryFunction<unknown, any>\n        select: (data: any) => infer TData\n      }\n    ? UseQueryResult<TData>\n    : T extends { queryFn?: QueryFunction<infer TQueryFnData, any> }\n    ? UseQueryResult<TQueryFnData>\n    : // Fallback\n      UseQueryResult\n\n/**\n * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param\n */\nexport type QueriesOptions<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = [],\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryOptionsForUseQueries[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetOptions<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesOptions<[...Tail], [...Result, GetOptions<Head>], [...Depth, 1]>\n  : unknown[] extends T\n  ? T\n  : // If T is *some* array but we couldn't assign unknown[] to it, then it must hold some known/homogenous type!\n  // use this to infer the param types in the case of Array.map() argument\n  T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      infer TQueryKey\n    >[]\n  ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData, TQueryKey>[]\n  : // Fallback\n    UseQueryOptionsForUseQueries[]\n\n/**\n * QueriesResults reducer recursively maps type param to results\n */\nexport type QueriesResults<\n  T extends any[],\n  Result extends any[] = [],\n  Depth extends ReadonlyArray<number> = [],\n> = Depth['length'] extends MAXIMUM_DEPTH\n  ? UseQueryResult[]\n  : T extends []\n  ? []\n  : T extends [infer Head]\n  ? [...Result, GetResults<Head>]\n  : T extends [infer Head, ...infer Tail]\n  ? QueriesResults<[...Tail], [...Result, GetResults<Head>], [...Depth, 1]>\n  : T extends UseQueryOptionsForUseQueries<\n      infer TQueryFnData,\n      infer TError,\n      infer TData,\n      any\n    >[]\n  ? // Dynamic-size (homogenous) UseQueryOptions array: map directly to array of results\n    UseQueryResult<unknown extends TData ? TQueryFnData : TData, TError>[]\n  : // Fallback\n    UseQueryResult[]\n\nexport function useQueries<T extends any[]>({\n  queries,\n  context,\n}: {\n  queries: readonly [...QueriesOptions<T>]\n  context?: UseQueryOptions['context']\n}): QueriesResults<T> {\n  const queryClient = useQueryClient({ context })\n  const isRestoring = useIsRestoring()\n  const errorResetBoundary = useQueryErrorResetBoundary()\n\n  const defaultedQueries = React.useMemo(\n    () =>\n      queries.map((options) => {\n        const defaultedOptions = queryClient.defaultQueryOptions(options)\n\n        // Make sure the results are already in fetching state before subscribing or updating options\n        defaultedOptions._optimisticResults = isRestoring\n          ? 'isRestoring'\n          : 'optimistic'\n\n        return defaultedOptions\n      }),\n    [queries, queryClient, isRestoring],\n  )\n\n  defaultedQueries.forEach((query) => {\n    ensureStaleTime(query)\n    ensurePreventErrorBoundaryRetry(query, errorResetBoundary)\n  })\n\n  useClearResetErrorBoundary(errorResetBoundary)\n\n  const [observer] = React.useState(\n    () => new QueriesObserver(queryClient, defaultedQueries),\n  )\n\n  const optimisticResult = observer.getOptimisticResult(defaultedQueries)\n\n  useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        isRestoring\n          ? () => undefined\n          : observer.subscribe(notifyManager.batchCalls(onStoreChange)),\n      [observer, isRestoring],\n    ),\n    () => observer.getCurrentResult(),\n    () => observer.getCurrentResult(),\n  )\n\n  React.useEffect(() => {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, { listeners: false })\n  }, [defaultedQueries, observer])\n\n  const shouldAtLeastOneSuspend = optimisticResult.some((result, index) =>\n    shouldSuspend(defaultedQueries[index], result, isRestoring),\n  )\n\n  const suspensePromises = shouldAtLeastOneSuspend\n    ? optimisticResult.flatMap((result, index) => {\n        const options = defaultedQueries[index]\n        const queryObserver = observer.getObservers()[index]\n\n        if (options && queryObserver) {\n          if (shouldSuspend(options, result, isRestoring)) {\n            return fetchOptimistic(options, queryObserver, errorResetBoundary)\n          } else if (willFetch(result, isRestoring)) {\n            void fetchOptimistic(options, queryObserver, errorResetBoundary)\n          }\n        }\n        return []\n      })\n    : []\n\n  if (suspensePromises.length > 0) {\n    throw Promise.all(suspensePromises)\n  }\n  const observerQueries = observer.getQueries()\n  const firstSingleResultWhichShouldThrow = optimisticResult.find(\n    (result, index) =>\n      getHasError({\n        result,\n        errorResetBoundary,\n        useErrorBoundary: defaultedQueries[index]?.useErrorBoundary ?? false,\n        query: observerQueries[index]!,\n      }),\n  )\n\n  if (firstSingleResultWhichShouldThrow?.error) {\n    throw firstSingleResultWhichShouldThrow.error\n  }\n\n  return optimisticResult as QueriesResults<T>\n}\n"], "names": ["module", "exports", "condition", "format", "a", "b", "c", "d", "e", "f", "error", "undefined", "Error", "args", "argIndex", "replace", "name", "framesToPop", "Diff", "buildValues", "diff", "components", "newString", "oldString", "useLongestToken", "componentPos", "componentLen", "length", "newPos", "oldPos", "component", "removed", "value", "join", "slice", "count", "added", "tmp", "map", "i", "oldValue", "lastComponent", "equals", "pop", "prototype", "options", "arguments", "callback", "this", "self", "done", "setTimeout", "castInput", "removeEmpty", "tokenize", "newLen", "old<PERSON>en", "edit<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "min", "bestPath", "extractCommon", "execEditLength", "diagonalPath", "basePath", "addPath", "remove<PERSON>ath", "_old<PERSON><PERSON>", "canAdd", "canRemove", "path", "pushComponent", "exec", "ret", "last", "push", "commonCount", "left", "right", "comparator", "ignoreCase", "toLowerCase", "array", "split", "chars", "generateOptions", "defaults", "hasOwnProperty", "extendedWordChars", "reWhitespace", "wordDiff", "diffWords", "oldStr", "newStr", "ignoreWhitespace", "test", "tokens", "splice", "lineDiff", "retLines", "linesAndNewlines", "line", "newlineIsToken", "trim", "sentenceDiff", "cssDiff", "_typeof", "obj", "Symbol", "iterator", "constructor", "objectPrototypeToString", "Object", "toString", "jsonDiff", "canonicalize", "stack", "replacementStack", "replacer", "key", "canonicalizedObj", "call", "Array", "toJSON", "_key", "sortedKeys", "sort", "_this$options", "undefinedReplacement", "_this$options$stringi", "stringifyReplacer", "k", "v", "JSON", "stringify", "arrayDiff", "$ebb31c7feaa4405e$export$b16d9fb1a22de840", "$hgUW1$createContext", "$44d7e150ebc754d2$var$initialState", "didCatch", "$44d7e150ebc754d2$export$e926676385687eaf", "$hgUW1$Component", "state", "getDerivedStateFromError", "resetErrorBoundary", "_this", "_len", "props", "onReset", "reason", "setState", "componentDidCatch", "info", "onError", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "some", "item", "index", "is", "$44d7e150ebc754d2$var$hasArrayChanged", "next", "prev", "render", "children", "fallback<PERSON><PERSON>", "FallbackComponent", "fallback", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$hgUW1$isValidElement", "$hgUW1$createElement", "Provider", "$75c9d331f9c1ed1a$export$f20aa86254872370", "$7c3c25b3f398a9d6$export$c052f6604b7d51fe", "context", "$hgUW1$useContext", "$hgUW1$useState", "<PERSON><PERSON><PERSON><PERSON>", "memoized", "$hgUW1$useMemo", "resetBoundary", "showBoundary", "$62ff477d53f02a5b$export$f0c7a449e0cfaec7", "Component", "errorBoundaryProps", "Wrapped", "displayName", "useQuery", "arg1", "arg2", "arg3", "parsedOptions", "parse<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useBaseQuery", "QueryObserver", "MutationObserver", "Subscribable", "client", "super", "setOptions", "bindMethods", "updateResult", "mutate", "bind", "reset", "_this$currentMutation", "prevOptions", "defaultMutationOptions", "shallowEqualObjects", "getMutationCache", "notify", "type", "mutation", "currentMutation", "observer", "onUnsubscribe", "_this$currentMutation2", "hasListeners", "removeObserver", "onMutationUpdate", "action", "notifyOptions", "listeners", "onSuccess", "getCurrentResult", "currentResult", "variables", "mutateOptions", "build", "addObserver", "execute", "getDefaultState", "result", "isLoading", "status", "isSuccess", "isError", "isIdle", "notify<PERSON><PERSON>ger", "batch", "_this$mutateOptions$o", "_this$mutateOptions", "_this$mutateOptions$o2", "_this$mutateOptions2", "data", "onSettled", "_this$mutateOptions$o3", "_this$mutateOptions3", "_this$mutateOptions$o4", "_this$mutateOptions4", "for<PERSON>ach", "_ref", "listener", "useMutation", "parseMutationArgs", "queryClient", "useQueryClient", "React", "useSyncExternalStore", "onStoreChange", "subscribe", "batchCalls", "catch", "noop", "shouldThrowError", "useErrorBoundary", "mutateAsync", "QueriesObserver", "queries", "observers", "observersMap", "setQueries", "onSubscribe", "size", "onUpdate", "destroy", "Set", "prevObservers", "newObserverMatches", "findMatchingObservers", "match", "defaultedQueryOptions", "newObservers", "newObserversMap", "fromEntries", "queryHash", "newResult", "hasIndexChange", "difference", "getQueries", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getObservers", "getOptimisticResult", "prevObserversMap", "Map", "defaultQueryOptions", "matchingObservers", "flatMap", "defaultedOptions", "get", "matchedQueryHashes", "unmatchedQueries", "filter", "has", "matchingObserversSet", "unmatchedObservers", "prevObserver", "getObserver", "currentObserver", "newOrReusedObservers", "keepPreviousData", "previouslyUsedObserver", "concat", "sortMatchesByOrderOfQueries", "indexOf", "replaceAt", "useQueries", "isRestoring", "useIsRestoring", "errorResetBoundary", "useQueryErrorResetBoundary", "defaultedQueries", "_optimisticResults", "query", "ensureStaleTime", "ensurePreventErrorBoundaryRetry", "useClearResetErrorBoundary", "optimisticResult", "suspensePromises", "shouldSuspend", "queryObserver", "fetchOptimistic", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "all", "observerQueries", "firstSingleResultWhichShouldThrow", "find", "_defaultedQueries$ind", "_defaultedQueries$ind2", "getHasError"], "sourceRoot": ""}