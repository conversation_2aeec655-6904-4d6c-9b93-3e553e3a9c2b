"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[4035],{8296:function(e,t,s){s.r(t),s.d(t,{ModelPage:function(){return le},ModelPageImpl:function(){return se},default:function(){return ie}});var a=s(31014),o=s(10811),l=s(69708),i=s(32599),n=s(48012),r=s(70618),d=s(9856),c=s(88464),g=s(88443),u=s(47664),m=s(81866),h=s(93215),p=s(69869),f=s(76010),v=s(89555),M=s(98597),_=s(50111);const y=e=>{let{tags:t=[],onAddEdit:s}=e;const{theme:a}=(0,i.u)();return(0,_.Y)("div",{css:(0,v.AH)({display:"flex",flexWrap:"wrap","> *":{marginRight:"0 !important"},gap:a.spacing.xs},""),children:t.length<1?(0,_.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetagseditorcell.tsx_29",size:"small",type:"link",onClick:s,children:(0,_.Y)(g.A,{id:"Li72QU",defaultMessage:"Add"})}):(0,_.FD)(_.FK,{children:[t.map((e=>(0,_.Y)(M.t,{tag:e},`${e.key}-${e.value}`))),(0,_.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetagseditorcell.tsx_37",size:"small",icon:(0,_.Y)(n.R2l,{}),onClick:s})]})})};var A=s(43683),S=s(30311),C=s(91164),w=s(45653);var I=function(e){return e.STATUS="STATUS",e.VERSION="VERSION",e.CREATION_TIMESTAMP="CREATION_TIMESTAMP",e.USER_ID="USER_ID",e.TAGS="TAGS",e.STAGE="STAGE",e.DESCRIPTION="DESCRIPTION",e.ALIASES="ALIASES",e}(I||{}),b={name:"10lkuaq",styles:".table-row-select-cell{align-items:flex-start;}"};const T=e=>{let{modelName:t,modelVersions:s,activeStageOnly:v,onChange:M,modelEntity:T,onMetadataUpdated:Y,usingNextModelsUI:R,aliases:D}=e;const k=(0,a.useMemo)((()=>{const e={};return null===D||void 0===D||D.forEach((t=>{let{alias:s,version:a}=t;e[a]||(e[a]=[]),e[a].push(s)})),e}),[D]),x=(0,a.useMemo)((()=>v?(s||[]).filter((e=>{let{current_stage:t}=e;return m.jI.includes(t)})):s),[v,s]),{theme:E}=(0,i.u)(),V=(0,c.A)(),N=(0,a.useMemo)((()=>{const e=(null===x||void 0===x?void 0:x.map((e=>(null===e||void 0===e?void 0:e.tags)||[])).flat())||[];return Array.from(new Set(e.map((e=>{let{key:t}=e;return t})))).sort()}),[x]),F=(0,o.wA)(),{EditTagsModal:P,showEditTagsModal:L}=(0,A.Q)({allAvailableTags:N,saveTagsHandler:async(e,t,s)=>F((0,l.kM)(e,t,s)),onSuccess:Y}),{EditAliasesModal:O,showEditAliasesModal:U}=(0,S.o)({model:T||null,onSuccess:Y}),[B,K]=(0,a.useState)({}),[q,H]=(0,a.useState)({pageSize:10,pageIndex:0});(0,a.useEffect)((()=>{const e=(x||[]).filter((e=>{let{version:t}=e;return B[t]})),t=e.map((e=>{let{version:t}=e;return t}));M(t,e)}),[B,M,x]);const W=(0,a.useMemo)((()=>{const e=[{id:I.STATUS,enableSorting:!1,header:"",meta:{styles:{flexBasis:E.general.heightSm,flexGrow:0}},cell:e=>{let{row:{original:t}}=e;const{status:s,status_message:a}=t||{};return(0,_.Y)(n.paO,{title:a||m.zr[s],children:(0,_.Y)(i.T.Text,{children:m.UA[s]})})}}];return e.push({id:I.VERSION,enableSorting:!1,header:V.formatMessage({id:"1f72BQ",defaultMessage:"Version"}),meta:{className:"model-version"},accessorKey:"version",cell:e=>{let{getValue:s}=e;return(0,_.Y)(g.A,{id:"yjerKR",defaultMessage:"<link>Version {versionNumber}</link>",values:{link:e=>(0,_.Y)(h.N_,{to:p.fM.getModelVersionPageRoute(t,String(s())),children:e}),versionNumber:s()}})}},{id:I.CREATION_TIMESTAMP,enableSorting:!0,meta:{styles:{minWidth:200}},header:V.formatMessage({id:"Nm/Pjx",defaultMessage:"Registered at"}),accessorKey:"creation_timestamp",cell:e=>{let{getValue:t}=e;return f.A.formatTimestamp(t(),V)}},{id:I.USER_ID,enableSorting:!1,meta:{styles:{minWidth:100}},header:V.formatMessage({id:"xRw7H2",defaultMessage:"Created by"}),accessorKey:"user_id",cell:e=>{let{getValue:t}=e;return(0,_.Y)("span",{children:t()})}}),R?e.push({id:I.TAGS,enableSorting:!1,header:V.formatMessage({id:"7NOp4F",defaultMessage:"Tags"}),meta:{styles:{flex:2}},accessorKey:"tags",cell:e=>{let{getValue:t,row:{original:s}}=e;return(0,_.Y)(y,{tags:t(),onAddEdit:()=>{null===L||void 0===L||L(s)}})}},{id:I.ALIASES,accessorKey:"aliases",enableSorting:!1,header:V.formatMessage({id:"wNHR0W",defaultMessage:"Aliases"}),meta:{styles:{flex:2},multiline:!0},cell:e=>{let{getValue:s,row:{original:a}}=e;const o=k[a.version]||[];return(0,_.Y)(C.Y,{modelName:t,version:a.version,aliases:o,onAddEdit:()=>{null===U||void 0===U||U(a.version)}})}}):e.push({id:I.STAGE,enableSorting:!1,header:V.formatMessage({id:"7F/CBv",defaultMessage:"Stage"}),accessorKey:"current_stage",cell:e=>{let{getValue:t}=e;return m.$0[t()]}}),e.push({id:I.DESCRIPTION,enableSorting:!1,header:V.formatMessage({id:"Vm9CvZ",defaultMessage:"Description"}),meta:{styles:{flex:2}},accessorKey:"description",cell:e=>{let{getValue:t}=e;return(0,w.Y7)(t(),32)}}),e}),[E,V,t,L,U,R,k]),[G,j]=(0,a.useState)([{id:I.CREATION_TIMESTAMP,desc:!0}]),z=(0,r.N4)({data:x||[],columns:W,state:{pagination:q,rowSelection:B,sorting:G},getCoreRowModel:(0,d.HT)(),getSortedRowModel:(0,d.h5)(),getPaginationRowModel:(0,d.kW)(),getRowId:e=>{let{version:t}=e;return t},onRowSelectionChange:K,onSortingChange:j}),$=(0,_.Y)(n.dKS,{componentId:"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_403",currentPageIndex:q.pageIndex+1,numTotal:(x||[]).length,onChange:(e,t)=>{H({pageSize:t||q.pageSize,pageIndex:e-1})},pageSize:q.pageSize}),X=(0,_.Y)(n.SvL,{description:(0,_.Y)(g.A,{id:"ynkoR7",defaultMessage:"No models versions are registered yet. <link>Learn more</link> about how to register a model version.",values:{link:e=>(0,_.Y)(i.T.Link,{componentId:"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_425",target:"_blank",href:u.gw,children:e})}}),image:(0,_.Y)(n.c11,{})});return(0,_.FD)(_.FK,{children:[(0,_.FD)(n.XIK,{"data-testid":"model-list-table",pagination:$,scrollable:!0,empty:0===z.getRowModel().rows.length?X:void 0,someRowsSelected:z.getIsSomeRowsSelected()||z.getIsAllRowsSelected(),children:[(0,_.FD)(n.Hjg,{isHeader:!0,children:[(0,_.Y)(n.p4w,{componentId:"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_450",checked:z.getIsAllRowsSelected(),indeterminate:z.getIsSomeRowsSelected(),onChange:z.getToggleAllRowsSelectedHandler()}),z.getLeafHeaders().map((e=>{var t;return(0,_.Y)(n.A0N,{componentId:"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_458",multiline:!1,sortable:e.column.getCanSort(),sortDirection:e.column.getIsSorted()||"none",onToggleSort:e.column.getToggleSortingHandler(),css:null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.styles,children:(0,r.Kv)(e.column.columnDef.header,e.getContext())},e.id)}))]}),z.getRowModel().rows.map((e=>(0,_.FD)(n.Hjg,{css:b,children:[(0,_.Y)(n.p4w,{componentId:"codegen_mlflow_app_src_model-registry_components_modelversiontable.tsx_477",checked:e.getIsSelected(),onChange:e.getToggleSelectedHandler()}),e.getAllCells().map((e=>{var t,s,a;return(0,_.Y)(n.nA6,{className:null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.className,multiline:null===(s=e.column.columnDef.meta)||void 0===s?void 0:s.multiline,css:null===(a=e.column.columnDef.meta)||void 0===a?void 0:a.styles,children:(0,r.Kv)(e.column.columnDef.cell,e.getContext())},e.id)}))]},e.id)))]}),P,O]})};var Y=s(10405),R=s(96034),D=s(41287),k=s(65871),x=s(79085),E=s(64912),V=s(15579),N=s(90925),F=s(91144),P=s(84963),L=s(73414),O=s(52350);const U="ALL",B="ACTIVE";var K={name:"lugakg",styles:"font-weight:normal"},q={name:"1ootkdu",styles:"margin-bottom:8px;display:flex;justify-content:flex-end"};class H extends a.Component{constructor(e){super(e),this.state={stageFilter:U,showDescriptionEditor:!1,isDeleteModalVisible:!1,isDeleteModalConfirmLoading:!1,runsSelected:{},isTagsRequestPending:!1,updatingEmailPreferences:!1},this.formRef=a.createRef(),this.handleStageFilterChange=e=>{this.setState({stageFilter:e.target.value})},this.handleCancelEditDescription=()=>{this.setState({showDescriptionEditor:!1})},this.handleSubmitEditDescription=e=>this.props.handleEditDescription(e).then((()=>{this.setState({showDescriptionEditor:!1})})),this.startEditingDescription=e=>{e.stopPropagation(),this.setState({showDescriptionEditor:!0})},this.showDeleteModal=()=>{this.setState({isDeleteModalVisible:!0})},this.hideDeleteModal=()=>{this.setState({isDeleteModalVisible:!1})},this.showConfirmLoading=()=>{this.setState({isDeleteModalConfirmLoading:!0})},this.hideConfirmLoading=()=>{this.setState({isDeleteModalConfirmLoading:!1})},this.handleDeleteConfirm=()=>{const{navigate:e}=this.props;this.showConfirmLoading(),this.props.handleDelete().then((()=>{e(p.fM.modelListPageRoute)})).catch((e=>{this.hideConfirmLoading(),f.A.logErrorAndNotifyUser(e)}))},this.handleAddTag=e=>{const t=this.formRef.current,{model:s}=this.props,a=s.name;this.setState({isTagsRequestPending:!0}),this.props.setRegisteredModelTagApi(a,e.name,e.value).then((()=>{this.setState({isTagsRequestPending:!1}),t.resetFields()})).catch((e=>{this.setState({isTagsRequestPending:!1}),console.error(e);const t=e instanceof O.s?e.getMessageField():e.message;f.A.displayGlobalErrorNotification("Failed to add tag. Error: "+t)}))},this.handleSaveEdit=e=>{let{name:t,value:s}=e;const{model:a}=this.props,o=a.name;return this.props.setRegisteredModelTagApi(o,t,s).catch((e=>{console.error(e);const t=e instanceof O.s?e.getMessageField():e.message;f.A.displayGlobalErrorNotification("Failed to set tag. Error: "+t)}))},this.handleDeleteTag=e=>{let{name:t}=e;const{model:s}=this.props,a=s.name;return this.props.deleteRegisteredModelTagApi(a,t).catch((e=>{console.error(e);const t=e instanceof O.s?e.getMessageField():e.message;f.A.displayGlobalErrorNotification("Failed to delete tag. Error: "+t)}))},this.onChange=(e,t)=>{const s=Object.assign({},this.state);s.runsSelected={},t.forEach((e=>{s.runsSelected={...s.runsSelected,[e.version]:e.run_id}})),this.setState(s)},this.renderDetails=()=>{const{model:e,modelVersions:t,tags:s}=this.props,{stageFilter:a,showDescriptionEditor:o,isDeleteModalVisible:l,isDeleteModalConfirmLoading:r,isTagsRequestPending:d}=this.state,c=e.name,u=Object.keys(this.state.runsSelected).length<2;return(0,_.FD)("div",{css:G.wrapper,children:[(0,_.FD)(N.K,{columns:3,"data-testid":"model-view-metadata",children:[(0,_.Y)(N.K.Item,{"data-testid":"model-view-metadata-item",label:this.props.intl.formatMessage({id:"At3XhS",defaultMessage:"Created Time"}),children:f.A.formatTimestamp(e.creation_timestamp,this.props.intl)}),(0,_.Y)(N.K.Item,{"data-testid":"model-view-metadata-item",label:this.props.intl.formatMessage({id:"j/pJM6",defaultMessage:"Last Modified"}),children:f.A.formatTimestamp(e.last_updated_timestamp,this.props.intl)}),e.user_id&&(0,_.Y)(N.K.Item,{"data-testid":"model-view-metadata-item",label:this.props.intl.formatMessage({id:"1jPG5D",defaultMessage:"Creator"}),children:(0,_.Y)("div",{children:e.user_id})})]}),(0,_.Y)(Y.i,{css:G.collapsiblePanel,title:(0,_.FD)("span",{children:[(0,_.Y)(g.A,{id:"oHW+ks",defaultMessage:"Description"})," ",o?null:this.renderDescriptionEditIcon()]}),forceOpen:o,defaultCollapsed:!e.description,"data-test-id":"model-description-section",children:(0,_.Y)(R.V,{defaultMarkdown:e.description,onSubmit:this.handleSubmitEditDescription,onCancel:this.handleCancelEditDescription,showEditor:o})}),(0,_.Y)("div",{"data-test-id":"tags-section",children:(0,_.Y)(Y.i,{css:G.collapsiblePanel,title:(0,_.Y)(g.A,{id:"HyBP+D",defaultMessage:"Tags"}),defaultCollapsed:0===f.A.getVisibleTagValues(s).length,"data-test-id":"model-tags-section",children:(0,_.Y)(D.h,{innerRef:this.formRef,handleAddTag:this.handleAddTag,handleDeleteTag:this.handleDeleteTag,handleSaveEdit:this.handleSaveEdit,tags:s,isRequestPending:d})})}),(0,_.FD)(Y.i,{css:G.collapsiblePanel,title:(0,_.Y)(_.FK,{children:(0,_.FD)("div",{css:G.versionsTabButtons,children:[(0,_.Y)("span",{children:(0,_.Y)(g.A,{id:"Z5en2d",defaultMessage:"Versions"})}),!this.props.usingNextModelsUI&&(0,_.FD)(n.d98,{componentId:"codegen_mlflow_app_src_model-registry_components_modelview.tsx_600",name:"stage-filter",value:this.state.stageFilter,onChange:e=>this.handleStageFilterChange(e),css:K,children:[(0,_.Y)(n.EPn,{value:U,children:(0,_.Y)(g.A,{id:"vi2MM7",defaultMessage:"All"})}),(0,_.FD)(n.EPn,{value:B,children:[(0,_.Y)(g.A,{id:"xPkIEE",defaultMessage:"Active"})," ",this.getActiveVersionsCount()]})]}),(0,_.Y)(i.B,{componentId:"codegen_mlflow_app_src_model-registry_components_modelview.tsx_619","data-test-id":"compareButton",disabled:u,onClick:this.onCompare,children:(0,_.Y)(g.A,{id:"FpjDSq",defaultMessage:"Compare"})})]})}),"data-test-id":"model-versions-section",children:[(0,F.WX)()&&(0,_.Y)("div",{css:q,children:(0,_.Y)(P.C,{})}),(0,_.Y)(T,{activeStageOnly:a===B&&!this.props.usingNextModelsUI,modelName:c,modelVersions:t,modelEntity:e,onChange:this.onChange,onMetadataUpdated:this.props.onMetadataUpdated,usingNextModelsUI:this.props.usingNextModelsUI,aliases:null===e||void 0===e?void 0:e.aliases})]}),(0,_.Y)(V.f,{componentId:"codegen_mlflow_app_src_model-registry_components_modelview.tsx_662","data-testid":"mlflow-input-modal",title:this.props.intl.formatMessage({id:"Gg/vLZ",defaultMessage:"Delete Model"}),visible:l,confirmLoading:r,onOk:this.handleDeleteConfirm,okText:this.props.intl.formatMessage({id:"a8rS4O",defaultMessage:"Delete"}),cancelText:this.props.intl.formatMessage({id:"yK/6vg",defaultMessage:"Cancel"}),onCancel:this.hideDeleteModal,children:(0,_.Y)("span",{children:(0,_.Y)(g.A,{id:"HUf9qJ",defaultMessage:"Are you sure you want to delete {modelName}? This cannot be undone.",values:{modelName:c}})})})]})},this.onCompare=this.onCompare.bind(this)}componentDidMount(){const e=`${this.props.model.name} - MLflow Model`;f.A.updatePageTitle(e)}getActiveVersionsCount(){const{modelVersions:e}=this.props;return e?e.filter((e=>m.jI.includes(e.current_stage))).length:0}getOverflowMenuItems(){return[{id:"delete",itemName:(0,_.Y)(g.A,{id:"9fVZg8",defaultMessage:"Delete"}),onClick:this.showDeleteModal,disabled:this.getActiveVersionsCount()>0}]}onCompare(){this.props.model&&this.props.navigate(p.fM.getCompareModelVersionsPageRoute(this.props.model.name,this.state.runsSelected))}renderDescriptionEditIcon(){return(0,_.Y)(i.B,{componentId:"codegen_mlflow_app_src_model-registry_components_modelview.tsx_467","data-test-id":"descriptionEditButton",type:"link",css:G.editButton,onClick:this.startEditingDescription,children:(0,_.Y)(g.A,{id:"36g3aR",defaultMessage:"Edit"})})}renderMainPanel(){return this.renderDetails()}render(){const{model:e}=this.props,t=e.name,s=[(0,_.Y)(h.N_,{to:p.fM.modelListPageRoute,children:(0,_.Y)(g.A,{id:"raa3Ij",defaultMessage:"Registered Models"})})];return(0,_.FD)("div",{children:[(0,_.Y)(x.z,{title:t,breadcrumbs:s,children:(0,_.Y)(x.o,{menu:this.getOverflowMenuItems()})}),this.renderMainPanel()]})}}const W={setRegisteredModelTagApi:l._e,deleteRegisteredModelTagApi:l.bE},G={emailNotificationPreferenceDropdown:e=>({width:300,marginBottom:e.spacing.md}),emailNotificationPreferenceTip:e=>({paddingLeft:e.spacing.sm,paddingRight:e.spacing.sm}),collapsiblePanel:e=>({marginBottom:e.spacing.md}),wrapper:e=>({'div[role="button"][aria-expanded]':{height:e.general.buttonHeight}}),editButton:e=>({marginLeft:e.spacing.md}),versionsTabButtons:e=>({display:"flex",gap:e.spacing.md,alignItems:"center"})},j=(0,o.Ng)(((e,t)=>{const s=t.model.name;return{tags:(0,k.ZG)(s,e)}}),W)((0,L.p)((0,E.Ay)(H)));var z=s(48588),$=s(53140),X=s(96277),Q=s(82214),Z=s(7204),J=s(25869),ee=s(20109),te=s(62448);class se extends a.Component{constructor(){super(...arguments),this.hasUnfilledRequests=void 0,this.pollIntervalId=void 0,this.initSearchModelVersionsApiRequestId=(0,Z.yk)(),this.initgetRegisteredModelApiRequestId=(0,Z.yk)(),this.updateRegisteredModelApiId=(0,Z.yk)(),this.deleteRegisteredModelApiId=(0,Z.yk)(),this.criticalInitialRequestIds=[this.initSearchModelVersionsApiRequestId,this.initgetRegisteredModelApiRequestId],this.handleEditDescription=e=>{const{model:t}=this.props;return this.props.updateRegisteredModelApi(t.name,e,this.updateRegisteredModelApiId).then(this.loadData)},this.handleDelete=()=>{const{model:e}=this.props;return this.props.deleteRegisteredModelApi(e.name,this.deleteRegisteredModelApiId)},this.loadData=e=>{const{modelName:t}=this.props;this.hasUnfilledRequests=!0;const s=[this.props.getRegisteredModelApi(t,!0===e?this.initgetRegisteredModelApiRequestId:null),this.props.searchModelVersionsApi({name:t},!0===e?this.initSearchModelVersionsApiRequestId:null)];return Promise.all(s).then((()=>{this.hasUnfilledRequests=!1}))},this.pollData=()=>{const{modelName:e,navigate:t}=this.props;return!this.hasUnfilledRequests&&f.A.isBrowserTabVisible()?this.loadData().catch((s=>{s instanceof O.s&&"RESOURCE_DOES_NOT_EXIST"===s.getErrorCode()?(f.A.logErrorAndNotifyUser(s),this.props.deleteRegisteredModelApi(e,void 0,!0),t(p.fM.modelListPageRoute)):console.error(s),this.hasUnfilledRequests=!1})):Promise.resolve()}}componentDidMount(){this.loadData(!0).catch(console.error),this.hasUnfilledRequests=!1,this.pollIntervalId=setInterval(this.pollData,m.Gs)}componentWillUnmount(){clearInterval(this.pollIntervalId)}render(){const{model:e,modelVersions:t,navigate:s,modelName:a}=this.props;return(0,_.Y)(z.L,{children:(0,_.Y)($.Ay,{requestIds:this.criticalInitialRequestIds,children:(o,l,i)=>{if(l){if(clearInterval(this.pollIntervalId),f.A.shouldRender404(i,[this.initgetRegisteredModelApiRequestId]))return(0,_.Y)(Q.E,{statusCode:404,subMessage:this.props.intl.formatMessage({id:"tD0/e2",defaultMessage:"Model {modelName} does not exist"},{modelName:a}),fallbackHomePageReactRoute:p.fM.modelListPageRoute});const e=i.filter((e=>{var t;return this.criticalInitialRequestIds.includes(e.id)&&(null===(t=e.error)||void 0===t?void 0:t.getErrorCode())===u.tG.PERMISSION_DENIED}));var n;if(e&&e[0])return(0,_.Y)(Q.E,{statusCode:403,subMessage:this.props.intl.formatMessage({id:"rJitqj",defaultMessage:'Permission denied for {modelName}. Error: "{errorMsg}"'},{modelName:a,errorMsg:null===(n=e[0].error)||void 0===n?void 0:n.getMessageField()}),fallbackHomePageReactRoute:p.fM.modelListPageRoute});(0,$.dD)(i)}else{if(o)return(0,_.Y)(X.y,{});if(e)return(0,_.Y)(j,{model:e,modelVersions:t,handleEditDescription:this.handleEditDescription,handleDelete:this.handleDelete,navigate:s,onMetadataUpdated:this.loadData})}return null}})})}}const ae={searchModelVersionsApi:l.hY,getRegisteredModelApi:l.SY,updateRegisteredModelApi:l.tL,deleteRegisteredModelApi:l.r9},oe=(0,J.h)((0,o.Ng)(((e,t)=>{const s=decodeURIComponent(t.params.modelName);return{modelName:s,model:e.entities.modelByName[s],modelVersions:(0,k.Tr)(e,s)}}),ae)((0,E.Ay)(se))),le=(0,ee.X)(te.A.mlflowServices.MODEL_REGISTRY,oe);var ie=le},43683:function(e,t,s){s.d(t,{Q:function(){return S}});var a=s(89555),o=s(9133),l=s(31014),i=s(32599),n=s(15579),r=s(48012),d=s(88464),c=s(88443),g=s(13369),u=s(50111);var m={name:"4zleql",styles:"display:block"};function h(e){return t=>function(e,t){const s=(0,d.A)(),{theme:n}=(0,i.u)(),c=e.props.searchValue.toLowerCase();return(0,l.useMemo)((()=>{if(!c)return e;if((0,o.sortedIndexOf)(t,c)>=0)return e;const i=/^[^,.:/=\-\s]+$/.test(c);return l.cloneElement(e,{flattenOptions:[{data:{value:c,disabled:!i,style:{color:i?n.colors.actionTertiaryTextDefault:n.colors.actionDisabledText},children:(0,u.Y)(r.paO,{title:i?void 0:s.formatMessage({id:"fWEvZL",defaultMessage:", . : / - = and blank spaces are not allowed"}),placement:"right",children:(0,u.FD)("span",{css:m,children:[(0,u.Y)(r.c11,{css:(0,a.AH)({marginRight:n.spacing.sm},"")}),s.formatMessage({id:"IJbauF",defaultMessage:'Add tag "{tagKey}"'},{tagKey:c})]})})},key:c,groupOption:!1},...e.props.flattenOptions]})}),[t,e,c,s,n])}(t,e)}var p={name:"1d3w5wq",styles:"width:100%"};function f(e){let{allAvailableTags:t,control:s,onKeyChangeCallback:a}=e;const o=(0,d.A)(),[i,n]=(0,l.useState)(!1),c=(0,l.useRef)(null),{field:m,fieldState:f}=(0,g.as)({control:s,name:"key",rules:{required:{message:o.formatMessage({id:"RlBbjb",defaultMessage:"A tag key is required"}),value:!0}}});return(0,u.Y)(r._vn,{allowClear:!0,ref:c,dangerouslySetAntdProps:{showSearch:!0,dropdownRender:h(t)},css:p,placeholder:o.formatMessage({id:"8ALhnh",defaultMessage:"Type a key"}),value:m.value,defaultValue:m.value,open:i,onDropdownVisibleChange:e=>{n(e)},filterOption:(e,t)=>null===t||void 0===t?void 0:t.value.toLowerCase().includes(e.toLowerCase()),onSelect:e=>{m.onChange(e),null===a||void 0===a||a(e)},onClear:()=>{m.onChange(void 0),null===a||void 0===a||a(void 0)},validationState:f.error?"error":void 0,children:t.map((e=>(0,u.Y)(r._vn.Option,{value:e,children:e},e)))})}var v=s(98597),M=s(52350);function _(e){return new Map(e.map((e=>[e.key,e])))}var y={name:"82a6rk",styles:"flex:1"},A={name:"82a6rk",styles:"flex:1"};const S=e=>{let{onSuccess:t,saveTagsHandler:s,allAvailableTags:m,valueRequired:h=!1,title:p}=e;const S=(0,l.useRef)(),[C,I]=(0,l.useState)(""),{theme:b}=(0,i.u)(),[T,Y]=(0,l.useState)(new Map),[R,D]=(0,l.useState)(new Map),[k,x]=(0,l.useState)(!1),E=(0,g.mN)({defaultValues:{key:void 0,value:""}}),V=()=>x(!1),N=(0,l.useCallback)((e=>{S.current=e,Y(_(e.tags||[])),D(_(e.tags||[])),E.reset(),x(!0)}),[E]),F=async()=>{S.current&&(I(""),U(!0),s(S.current,Array.from(T.values()),Array.from(R.values())).then((()=>{V(),null===t||void 0===t||t(),U(!1)})).catch((e=>{var t;U(!1),I(e instanceof M.s?null===(t=e.getUserVisibleError())||void 0===t?void 0:t.message:e.message)})))},P=(0,d.A)(),L=E.watch(),[O,U]=(0,l.useState)(!1),B=(0,l.useMemo)((()=>!(0,o.isEqual)((0,o.sortBy)(Array.from(T.values()),"key"),(0,o.sortBy)(Array.from(R.values()),"key"))),[T,R]),K=L.key||L.value,q=B&&K;return{EditTagsModal:(0,u.FD)(n.d,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_135",destroyOnClose:!0,visible:k,title:null!==p&&void 0!==p?p:(0,u.Y)(c.A,{id:"TBX+Gs",defaultMessage:"Add/Edit tags"}),onCancel:V,footer:(0,u.FD)(i.y,{children:[(0,u.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_147",dangerouslyUseFocusPseudoClass:!0,onClick:V,css:(0,a.AH)({marginRight:B?0:b.spacing.sm},""),children:P.formatMessage({id:"2a/rR8",defaultMessage:"Cancel"})}),q?(0,u.Y)(w,{formValues:L,isLoading:O,onSaveTask:F}):(0,u.Y)(r.paO,{title:B?void 0:P.formatMessage({id:"16onEc",defaultMessage:"Please add or remove one or more tags before saving"}),children:(0,u.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_174",dangerouslyUseFocusPseudoClass:!0,disabled:!B,loading:O,type:"primary",onClick:F,children:P.formatMessage({id:"801Xh4",defaultMessage:"Save tags"})})})]}),children:[(0,u.FD)("form",{onSubmit:E.handleSubmit((()=>{if(h&&!L.value.trim())return;const e=new Map(R);e.set(L.key,L),D(e),E.reset()})),css:(0,a.AH)({display:"flex",alignItems:"flex-end",gap:b.spacing.md},""),children:[(0,u.FD)("div",{css:(0,a.AH)({minWidth:0,display:"flex",gap:b.spacing.md,flex:1},""),children:[(0,u.FD)("div",{css:y,children:[(0,u.Y)(r.D$Q.Label,{htmlFor:"key",children:P.formatMessage({id:"crTWax",defaultMessage:"Key"})}),(0,u.Y)(f,{allAvailableTags:m||[],control:E.control,onKeyChangeCallback:e=>{var t;const s=e?R.get(e):void 0;E.setValue("value",null!==(t=null===s||void 0===s?void 0:s.value)&&void 0!==t?t:"")}})]}),(0,u.FD)("div",{css:A,children:[(0,u.Y)(r.D$Q.Label,{htmlFor:"value",children:h?P.formatMessage({id:"tHrp+A",defaultMessage:"Value"}):P.formatMessage({id:"4B8k46",defaultMessage:"Value (optional)"})}),(0,u.Y)(r.tc_.Input,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_223",name:"value",control:E.control,"aria-label":h?P.formatMessage({id:"tHrp+A",defaultMessage:"Value"}):P.formatMessage({id:"4B8k46",defaultMessage:"Value (optional)"}),placeholder:P.formatMessage({id:"FFe+Ug",defaultMessage:"Type a value"})})]})]}),(0,u.Y)(r.paO,{title:P.formatMessage({id:"tx3aAM",defaultMessage:"Add tag"}),children:(0,u.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_248",htmlType:"submit","aria-label":P.formatMessage({id:"tx3aAM",defaultMessage:"Add tag"}),children:(0,u.Y)(r.c11,{})})})]}),C&&(0,u.Y)(r.D$Q.Message,{type:"error",message:C}),(0,u.Y)("div",{css:(0,a.AH)({display:"flex",rowGap:b.spacing.xs,flexWrap:"wrap",marginTop:b.spacing.sm},""),children:Array.from(R.values()).map((e=>(0,u.Y)(v.t,{isClosable:!0,tag:e,onClose:()=>(e=>{let{key:t}=e;D((e=>(e.delete(t),new Map(e))))})(e)},e.key)))})]}),showEditTagsModal:N,isLoading:O}};var C={name:"1y0ex1",styles:"max-width:400px"};function w(e){let{isLoading:t,formValues:s,onSaveTask:l}=e;const n=(0,d.A)(),{theme:r}=(0,i.u)(),c=`${`${(0,o.truncate)(s.key,{length:20})||"_"}`}${s.value?`:${(0,o.truncate)(s.value,{length:20})}`:""}`,g=n.formatMessage({id:"wcSVYI",defaultMessage:'Are you sure you want to save and close without adding "{tag}"'},{tag:c});return(0,u.FD)(i.av.Root,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_309",children:[(0,u.Y)(i.av.Trigger,{asChild:!0,children:(0,u.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_306",dangerouslyUseFocusPseudoClass:!0,loading:t,type:"primary",children:n.formatMessage({id:"801Xh4",defaultMessage:"Save tags"})})}),(0,u.FD)(i.av.Content,{align:"end","aria-label":g,children:[(0,u.Y)(i.T.Paragraph,{css:C,children:g}),(0,u.Y)(i.av.Close,{asChild:!0,children:(0,u.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_316",onClick:l,children:n.formatMessage({id:"mv6CY3",defaultMessage:"Yes, save and close"})})}),(0,u.Y)(i.av.Close,{asChild:!0,children:(0,u.Y)(i.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_324",type:"primary",css:(0,a.AH)({marginLeft:r.spacing.sm},""),children:n.formatMessage({id:"geizp1",defaultMessage:"Cancel"})})}),(0,u.Y)(i.av.Arrow,{})]})]})}},45653:function(e,t,s){s.d(t,{Qr:function(){return d},U1:function(){return f},Xb:function(){return m},Y7:function(){return i},fx:function(){return n},ib:function(){return v},yv:function(){return c},z7:function(){return p}});var a=s(9133),o=s.n(a);const l=()=>s.e(6386).then(s.t.bind(s,86386,23)),i=(e,t)=>{const s=o().truncate(e,{length:t});return o().takeWhile(s,(e=>"\n"!==e)).join("")},n=(e,t)=>{if(e.length>t){const s=Math.floor((t-3)/2),a=t-3-s;return e.substring(0,s)+"..."+e.substring(e.length-a,e.length)}return e},r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",d=e=>{let t="",s=0;const a=g(e);for(;s<a.length;){const e=a.charCodeAt(s++),o=a.charCodeAt(s++),l=a.charCodeAt(s++),i=e>>2,n=(3&e)<<4|o>>4;let d=(15&o)<<2|l>>6,c=63&l;isNaN(o)?(c=64,d=c):isNaN(l)&&(c=64),t=t+r.charAt(i)+r.charAt(n)+r.charAt(d)+r.charAt(c)}return t},c=e=>{let t="",s=0;const a=(null===e||void 0===e?void 0:e.replace(/[^A-Za-z0-9+/=]/g,""))||"";for(;s<a.length;){const e=r.indexOf(a.charAt(s++)),o=r.indexOf(a.charAt(s++)),l=r.indexOf(a.charAt(s++)),i=r.indexOf(a.charAt(s++)),n=e<<2|o>>4,d=(15&o)<<4|l>>2,c=(3&l)<<6|i;t+=String.fromCharCode(n),64!==l&&(t+=String.fromCharCode(d)),64!==i&&(t+=String.fromCharCode(c))}return u(t)},g=function(){const e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/\r\n/g,"\n");let t="";for(let s=0;s<e.length;s++){const a=e.charCodeAt(s);t+=a<128?String.fromCharCode(a):a>127&&a<2048?String.fromCharCode(a>>6|192)+String.fromCharCode(63&a|128):String.fromCharCode(a>>12|224)+String.fromCharCode(a>>6&63|128)+String.fromCharCode(63&a|128)}return t},u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t="",s=0;for(;s<e.length;){const a=e.charCodeAt(s);if(a<128)t+=String.fromCharCode(a),s++;else if(a>191&&a<224){const o=e.charCodeAt(s+1);t+=String.fromCharCode((31&a)<<6|63&o),s+=2}else{const o=e.charCodeAt(s+1),l=e.charCodeAt(s+2);t+=String.fromCharCode((15&a)<<12|(63&o)<<6|63&l),s+=3}}return t},m=e=>crypto.subtle.digest("SHA-256",(new TextEncoder).encode(e)).then((e=>Array.prototype.map.call(new Uint8Array(e),(e=>("00"+e.toString(16)).slice(-2))).join(""))),h="deflate;",p=async e=>{const t=(await l()).deflate(e);if("undefined"!==typeof Buffer){const e=Buffer.from(t).toString("base64");return`${h}${e}`}const s=Array.from(t,(e=>String.fromCodePoint(e))).join("");return`${h}${btoa(s)}`},f=async e=>{const t=await l();if(!e.startsWith(h))throw new Error("Invalid compressed text, payload header invalid");const s=e.slice(8);if("undefined"!==typeof Buffer){const e=Buffer.from(s,"base64");return t.inflate(e,{to:"string"})}const a=atob(s);return t.inflate(Uint8Array.from(a,(e=>{var t;return null!==(t=e.codePointAt(0))&&void 0!==t?t:0})),{to:"string"})},v=e=>e.startsWith(h)},56412:function(e,t,s){s.d(t,{i:function(){return d}});var a=s(31014),o=s(88443),l=s(48012),i=s(32599),n=s(50111);var r={name:"1739oy8",styles:"z-index:1"};const d=e=>{let{copyText:t,showLabel:s=!0,componentId:d,...c}=e;const[g,u]=(0,a.useState)(!1);return(0,n.Y)(l.paO,{title:(0,n.Y)(o.A,{id:"X+boXI",defaultMessage:"Copied"}),dangerouslySetAntdProps:{visible:g},children:(0,n.Y)(i.B,{componentId:null!==d&&void 0!==d?d:"mlflow.shared.copy_button",type:"primary",onClick:()=>{navigator.clipboard.writeText(t),u(!0),setTimeout((()=>{u(!1)}),3e3)},onMouseLeave:()=>{u(!1)},css:r,children:s?(0,n.Y)(o.A,{id:"1Iq+NW",defaultMessage:"Copy"}):void 0,...c})})}},91164:function(e,t,s){s.d(t,{Y:function(){return d}});var a=s(89555),o=s(32599),l=s(48012),i=s(64756),n=s(88443),r=s(50111);const d=e=>{let{aliases:t=[],onAddEdit:s,className:d}=e;const{theme:c}=(0,o.u)();return(0,r.Y)("div",{css:(0,a.AH)({maxWidth:300,display:"flex",flexWrap:"wrap",alignItems:"flex-start","> *":{marginRight:"0 !important"},rowGap:c.spacing.xs/2,columnGap:c.spacing.xs},""),className:d,children:t.length<1?(0,r.Y)(o.B,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelversiontablealiasescell.tsx_30",size:"small",type:"link",onClick:s,children:(0,r.Y)(n.A,{id:"pU6cxH",defaultMessage:"Add"})}):(0,r.FD)(r.FK,{children:[t.map((e=>(0,r.Y)(i.m,{value:e,css:(0,a.AH)({marginTop:c.spacing.xs/2},"")},e))),(0,r.Y)(o.B,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelversiontablealiasescell.tsx_41",size:"small",icon:(0,r.Y)(l.R2l,{}),onClick:s})]})})}},98597:function(e,t,s){s.d(t,{t:function(){return v}});var a=s(89555),o=s(48012),l=s(32599),i=s(31014),n=s(88464),r=s(15579),d=s(56412),c=s(50111);const{Paragraph:g}=l.T;var u={name:"zjik7",styles:"display:flex"},m={name:"1ff36h2",styles:"flex-grow:1"};const h=i.memo((e=>{const{theme:t}=(0,l.u)();return(0,c.Y)(r.d,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetagfullviewmodal.tsx_17",title:"Tag: "+e.tagKey,visible:e.isKeyValueTagFullViewModalVisible,onCancel:()=>e.setIsKeyValueTagFullViewModalVisible(!1),children:(0,c.FD)("div",{css:u,children:[(0,c.Y)(g,{css:m,children:(0,c.Y)("pre",{css:(0,a.AH)({backgroundColor:t.colors.backgroundPrimary,marginTop:t.spacing.sm,whiteSpace:"pre-wrap",wordBreak:"break-all"},""),children:e.tagValue})}),(0,c.Y)("div",{css:(0,a.AH)({marginTop:t.spacing.sm},""),children:(0,c.Y)(d.i,{copyText:e.tagValue,showLabel:!1,icon:(0,c.Y)(o.TdU,{}),"aria-label":"Copy"})})]})})})),p=30;function f(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?{overflow:"hidden",textOverflow:"ellipsis",textWrap:"nowrap",whiteSpace:"nowrap"}:{whiteSpace:"nowrap"}}const v=e=>{let{isClosable:t=!1,onClose:s,tag:r,enableFullViewModal:d=!1,charLimit:g=p,maxWidth:u=300,className:m}=e;const v=(0,n.A)(),[M,_]=(0,i.useState)(!1),{shouldTruncateKey:y,shouldTruncateValue:A}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p;const{key:s,value:a}=e,o=s.length+a.length,l=s.length>a.length,i=l?a.length:s.length;return o<=t?{shouldTruncateKey:!1,shouldTruncateValue:!1}:i>t/2?{shouldTruncateKey:!0,shouldTruncateValue:!0}:{shouldTruncateKey:l,shouldTruncateValue:!l}}(r,g),S=d&&(y||A),C=v.formatMessage({id:"ZXUtU8",defaultMessage:"Click to see more"});return(0,c.FD)("div",{children:[(0,c.Y)(o.vwO,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetag.tsx_60",closable:t,onClose:s,title:r.key,className:m,children:(0,c.Y)(o.paO,{title:S?C:"",children:(0,c.FD)("span",{css:(0,a.AH)({maxWidth:u,display:"inline-flex"},""),onClick:()=>S?_(!0):void 0,children:[(0,c.Y)(l.T.Text,{bold:!0,title:r.key,css:f(y),children:r.key}),r.value&&(0,c.FD)(l.T.Text,{title:r.value,css:f(A),children:[": ",r.value]})]})})}),(0,c.Y)("div",{children:M&&(0,c.Y)(h,{tagKey:r.key,tagValue:r.value,isKeyValueTagFullViewModalVisible:M,setIsKeyValueTagFullViewModalVisible:_})})]})}}}]);
//# sourceMappingURL=4035.932817ba.chunk.js.map