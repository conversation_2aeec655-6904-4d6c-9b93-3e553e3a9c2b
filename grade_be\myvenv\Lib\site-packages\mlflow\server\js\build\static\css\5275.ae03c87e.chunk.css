.parcoords>canvas,.parcoords>svg{font:14px sans-serif;position:absolute}.parcoords>canvas{pointer-events:none}.parcoords text.label{fill:#000;cursor:default}.parcoords rect.background{fill:transparent}.parcoords rect.background:hover{fill:hsla(0,0%,47%,.2)}.parcoords .resize rect{fill:rgba(0,0,0,.1)}.parcoords rect.extent{fill:hsla(0,0%,100%,.25);stroke:rgba(0,0,0,.6)}.parcoords .axis line,.parcoords .axis path{fill:none;stroke:#222;shape-rendering:crispEdges}.parcoords canvas{opacity:1;-moz-transition:opacity .3s;-webkit-transition:opacity .3s;-o-transition:opacity .3s}.parcoords canvas.faded{opacity:.25}.parcoords canvas.dimmed{opacity:.85}.parcoords{-webkit-touch-callout:none;background-color:#fff;-webkit-user-select:none;-moz-user-select:none;user-select:none}.parcoords>canvas,.parcoords>svg{overflow:visible}.parcoords svg text.label{cursor:pointer}.parcoords svg g.axis-label-tooltip rect{outline:1px solid #000}.parcoords svg g.axis-label-tooltip{pointer-events:none;visibility:hidden}.parcoords svg text.label:hover:not(:active)+g.axis-label-tooltip{visibility:visible}.parcoords svg g.tick-label-tooltip rect{outline:1px solid #000}.parcoords svg g.tick-label-tooltip{pointer-events:none;visibility:hidden}.parcoords svg text:hover:not(:active)+g.tick-label-tooltip{visibility:visible}
/*# sourceMappingURL=5275.ae03c87e.chunk.css.map*/