{"Q1": {"text": "Infrastructure as a service\ncloud computing service model that allows\nauss to virtual computing resources"}, "Q2": {"tables": [{"heading": ["Horizontal Business\nProcess", "Vertical Business\nProcess"], "rows": [{"Horizontal Business\nProcess": "CRM", "Vertical Business\nProcess": "Banking and finance"}, {"Horizontal Business\nProcess": "HR Management", "Vertical Business\nProcess": "Billing"}, {"Horizontal Business\nProcess": "Procurement", "Vertical Business\nProcess": "Tracking payment"}]}]}, "Q3": {"equations": [{"step": 1, "equation": "x^2+x+12=0"}, {"step": 2, "equation": "x^2+4x+3x+12=0"}, {"step": 3, "equation": "x(x+4)+3(x+4)=0"}, {"step": 4, "equation": "(x+4)(x+3) = 0"}, {"step": 5, "equation": "x+4=0"}, {"step": 6, "equation": "x=-4"}, {"step": 7, "equation": "x+3=0"}, {"step": 8, "equation": "x=-3"}]}, "Q4": {"text": "Integration as a Service\ncloud computing service model that allows\nintegration of Application, data, system of\nan organization\n• Connect Desperate systems\n• Ensure seamless data proursing", "tables": [{"heading": ["Types", "Focus", "AWS"], "rows": [{"Types": "Application\nIntegration", "Focus": "Integrating\ndifferent\nApplications", "AWS": "Aws Data Syn"}, {"Types": "Data\nIntegration", "Focus": "Integration\nof Data\nfrom bot\nDifferent\nSources", "AWS": "AWS GLUE"}]}], "diagram": {"1": "C:\\Users\\<USER>\\OneDrive\\Desktop\\intern grade be\\grade_be\\media/output\\c3d5bc88-6f4e-4f4b-9487-57b7c3120660\\images/Q4_22N235_1.png"}}}