"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[7202],{25869:function(e,t,s){s.d(t,{h:function(){return o}});s(31014);var r=s(93215),n=s(50111);const o=e=>t=>{const s=(0,r.zy)(),o=(0,r.Zp)(),a=(0,r.g)();return(0,n.Y)(e,{params:a,location:s,navigate:o,...t})}},30214:function(e,t,s){s.d(t,{W:function(){return l}});var r=s(89555),n=(s(31014),s(88443)),o=s(32599),a=s(48012),i=s(50111);const l=e=>{let{className:t}=e;const{theme:s}=(0,o.u)();return(0,i.Y)(a.vwO,{componentId:"codegen_mlflow_app_src_shared_building_blocks_previewbadge.tsx_14",className:t,css:(0,r.AH)({marginLeft:s.spacing.xs},""),color:"turquoise",children:(0,i.Y)(n.A,{id:"8qJt7/",defaultMessage:"Experimental"})})}},32614:function(e,t,s){s.d(t,{A:function(){return r}});class r{static getStoreForComponent(e,t){return new n([e,t].join("-"),"localStorage")}static getSessionScopedStoreForComponent(e,t){return new n([e,t].join("-"),"sessionStorage")}}r.version="1.1";class n{constructor(e,t){this.scope=void 0,this.storageObj=void 0,this.scope=e,this.storageObj="localStorage"===t?window.localStorage:window.sessionStorage}loadComponentState(){const e=this.getItem(n.reactComponentStateKey);return e?JSON.parse(e):{}}saveComponentState(e){const t="function"===typeof e.toJSON?e.toJSON():e;this.setItem(n.reactComponentStateKey,JSON.stringify(t))}withScopePrefix(e){return["MLflowLocalStorage",r.version,this.scope,e].join("-")}setItem(e,t){this.storageObj.setItem(this.withScopePrefix(e),t)}getItem(e){return this.storageObj.getItem(this.withScopePrefix(e))}}n.reactComponentStateKey="ReactComponentState"},59508:function(e,t,s){s.d(t,{_C:function(){return i},pc:function(){return o},so:function(){return a}});var r=s(81866),n=s(69708);function o(e){return e?`${r.Qs} ilike ${(0,n.GP)(e,!0)}`:""}function a(){let{query:e=""}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=[],s=e.includes("tags.")?e:o(e);return s&&t.push(s),t.join(" AND ")}function i(e){return"searchInput"in e?e.searchInput:"nameSearchInput"in e&&"tagSearchInput"in e?o(e.nameSearchInput)+" AND "+e.tagSearchInput:"tagSearchInput"in e?e.tagSearchInput:"nameSearchInput"in e?e.nameSearchInput:""}},67245:function(e,t,s){s.d(t,{B:function(){return i}});var r=s(31014),n=s(15579),o=s(76010),a=s(50111);class i extends r.Component{constructor(){super(...arguments),this.state={isSubmitting:!1},this.formRef=r.createRef(),this.onSubmit=async()=>{this.setState({isSubmitting:!0});try{const e=await this.formRef.current.validateFields();await this.props.handleSubmit(e),this.resetAndClearModalForm(),this.onRequestCloseHandler()}catch(e){this.handleSubmitFailure(e)}},this.resetAndClearModalForm=()=>{this.setState({isSubmitting:!1}),this.formRef.current.resetFields()},this.handleSubmitFailure=e=>{this.setState({isSubmitting:!1}),o.A.logErrorAndNotifyUser(e)},this.onRequestCloseHandler=()=>{this.resetAndClearModalForm(),this.props.onClose()},this.handleCancel=()=>{var e,t;this.onRequestCloseHandler(),null===(e=(t=this.props).onCancel)||void 0===e||e.call(t)}}render(){const{isSubmitting:e}=this.state,{okText:t,cancelText:s,isOpen:o,footer:i,children:l}=this.props,d=r.Children.map(l,(e=>r.isValidElement(e)?r.cloneElement(e,{innerRef:this.formRef}):e));return(0,a.Y)(n.d,{"data-testid":"mlflow-input-modal",className:this.props.className,title:this.props.title,width:540,visible:o,onOk:this.onSubmit,okText:t,cancelText:s,confirmLoading:e,onCancel:this.handleCancel,footer:i,centered:!0,children:d})}}},77202:function(e,t,s){s.r(t),s.d(t,{ModelListPageWrapper:function(){return Se},default:function(){return _e}});var r=s(20109),n=s(62448),o=s(31014),a=s(76010),i=s(81866),l=s(47664),d=s(32599),c=s(67245),u=s(48012),h=s(41028),g=s(88443),m=s(64912),p=s(50111);const f="modelName";class S extends o.Component{render(){const e=S.getLearnMoreLinkUrl();return(0,p.FD)(u.SQ4,{ref:this.props.innerRef,layout:"vertical","data-testid":"create-model-form-modal",children:[(0,p.Y)(u.SQ4.Item,{name:f,label:this.props.intl.formatMessage({id:"9mAGv0",defaultMessage:"Model name"}),rules:[{required:!0,message:this.props.intl.formatMessage({id:"7nAMnb",defaultMessage:"Please input a name for the new model."})},{validator:this.props.validator}],children:(0,p.Y)(h.I,{componentId:"codegen_mlflow_app_src_model-registry_components_createmodelform.tsx_62",autoFocus:!0})}),(0,p.FD)("p",{className:"create-modal-explanatory-text",children:[(0,p.Y)(g.A,{id:"+wQBvn",defaultMessage:"After creation, you can register logged models as new versions.\xa0"}),(0,p.Y)(g.A,{id:"iZm6YQ",defaultMessage:"<link>Learn more</link>",values:{link:t=>(0,p.Y)("a",{href:e,target:"_blank",children:t})}}),"."]})]})}}S.getLearnMoreLinkUrl=()=>l.qT;const _=(0,m.Ay)(S);var v=s(10811),y=s(69708),M=s(7204),A=s(69869),I=s(9133),Y=s(87877),C=s(25869);class R extends o.Component{constructor(){super(...arguments),this.createRegisteredModelRequestId=(0,M.yk)(),this.handleCreateRegisteredModel=async e=>{const t=await this.props.createRegisteredModelApi(e[f],this.createRegisteredModelRequestId),s=t.value&&t.value.registered_model;s&&setTimeout((()=>this.props.navigate(A.fM.getModelPageRoute(s.name))))},this.debouncedModelNameValidator=(0,I.debounce)(Y.N,400),this.handleOnCancel=()=>{this.props.navigateBackOnCancel&&this.props.navigate(A.fM.modelListPageRoute)}}render(){const{modalVisible:e,hideModal:t}=this.props;return(0,p.Y)(c.B,{title:this.props.intl.formatMessage({id:"mIk1MU",defaultMessage:"Create Model"}),okText:this.props.intl.formatMessage({id:"MatV9u",defaultMessage:"Create"}),cancelText:this.props.intl.formatMessage({id:"SCYMXw",defaultMessage:"Cancel"}),isOpen:e,handleSubmit:this.handleCreateRegisteredModel,onClose:t,onCancel:this.handleOnCancel,children:(0,p.Y)(_,{visible:e,validator:this.debouncedModelNameValidator})})}}const x={createRegisteredModelApi:y.eF},T=(0,C.h)((0,v.Ng)(void 0,x)((0,m.Ay)(R))),N=(0,r.X)(n.A.mlflowServices.MODEL_REGISTRY,T);function b(e){let{buttonType:t="primary",buttonText:s=(0,p.Y)(g.A,{id:"ZwAzmu",defaultMessage:"Create Model"})}=e;const[r,n]=(0,o.useState)(!1);return(0,p.FD)("div",{css:P.wrapper,children:[(0,p.Y)(d.B,{componentId:"codegen_mlflow_app_src_model-registry_components_CreateModelButton.tsx_28",className:"create-model-btn",css:P.getButtonSize(t),type:t,onClick:()=>{n(!0)},"data-testid":"create-model-button",children:s}),(0,p.Y)(N,{modalVisible:r,hideModal:()=>{n(!1)}})]})}const P={getButtonSize:e=>"primary"===e?{height:"40px",width:"fit-content"}:{padding:"0px"},wrapper:{display:"inline"}};var w=s(32614),E=s(79085),k=s(15579),D=s(91144),O=s(88464),L=s(69526);var B={name:"mfyowb",styles:"border:0;background:none;padding:0;line-height:0;cursor:pointer"};const F=e=>{let{exampleEntityName:t="my_model_name"}=e;const{formatMessage:s}=(0,O.A)(),r=(0,L.zR)({id:"wMG8+P",defaultMessage:"To search by tags or by names and tags, use a simplified version{newline}of the SQL {whereBold} clause."}),n=s(r,{newline:" ",whereBold:"WHERE"});return(0,p.FD)(d.av.Root,{componentId:"codegen_mlflow_app_src_model-registry_components_model-list_modellistfilters.tsx_46",children:[(0,p.Y)(d.av.Trigger,{"aria-label":n,css:B,children:(0,p.Y)(k.I,{})}),(0,p.FD)(d.av.Content,{align:"start",children:[(0,p.FD)("div",{children:[(0,p.Y)(g.A,{...r,values:{newline:(0,p.Y)("br",{}),whereBold:(0,p.Y)("b",{children:"WHERE"})}})," ",(0,p.Y)(g.A,{id:"3bagxW",defaultMessage:"<link>Learn more</link>",values:{link:e=>(0,p.Y)(d.T.Link,{componentId:"codegen_mlflow_app_src_model-registry_components_model-list_modellistfilters.tsx_61",href:l.g2+"#syntax",openInNewTab:!0,children:e})}}),(0,p.Y)("br",{}),(0,p.Y)("br",{}),(0,p.Y)(g.A,{id:"U3btBc",defaultMessage:"Examples:"}),(0,p.Y)("br",{}),'\u2022 tags.my_key = "my_value"',(0,p.Y)("br",{}),'\u2022 name ilike "%',t,'%" and tags.my_key = "my_value"']}),(0,p.Y)(d.av.Arrow,{})]})]})},K=e=>{let{searchFilter:t,onSearchFilterChange:s,isFiltered:r}=e;const n=(0,O.A)(),[a,i]=(0,o.useState)(t);(0,o.useEffect)((()=>{i(t)}),[t]);return(0,p.FD)(u.R9P,{children:[(0,p.Y)(u.z2z,{componentId:"codegen_mlflow_app_src_model-registry_components_model-list_modellistfilters.tsx_118",placeholder:n.formatMessage({id:"6Nk5AH",defaultMessage:"Filter registered models by name or tags"}),onSubmit:()=>{s(a)},onClear:()=>{i(""),s("")},onChange:e=>i(e.target.value),"data-testid":"model-search-input",suffix:(0,p.Y)(F,{}),value:a,showSearchButton:!0}),r&&(0,p.Y)(d.B,{componentId:"codegen_mlflow_app_src_model-registry_components_model-list_modellistfilters.tsx_152",type:"tertiary",onClick:()=>{s("")},"data-testid":"models-list-filters-reset",children:(0,p.Y)(g.A,{id:"n/l2ft",defaultMessage:"Reset filters"})})]})};var V=s(70618),G=s(9856),U=s(93215),H=s(89555),W=s(98590);const z=()=>(0,p.Y)(p.FK,{children:"\u2014"});var Q={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"};const j=e=>{let{tags:t}=e;const{theme:s}=(0,d.u)(),[r,n]=(0,o.useState)(!1),a=null===t||void 0===t?void 0:t.filter((e=>!e.key.startsWith(W.nt))),i=null===a||void 0===a?void 0:a.slice(0,r?void 0:3);if(null===a||void 0===a||!a.length)return(0,p.Y)(z,{});const l=(0,p.Y)("em",{children:(0,p.Y)(g.A,{id:"2I27Yy",defaultMessage:"(empty)"})});return(0,p.FD)("div",{children:[i.map((e=>(0,p.Y)(u.paO,{title:(0,p.FD)(p.FK,{children:[e.key,": ",e.value||l]}),placement:"left",children:(0,p.FD)("div",{css:Q,"data-testid":"models-table-tag-entry",children:[(0,p.Y)(d.T.Text,{bold:!0,children:e.key}),": ",e.value||l]},e.key)},e.key))),t.length>3&&(0,p.Y)(d.B,{componentId:"codegen_mlflow_app_src_model-registry_components_model-list_modeltablecellrenderers.tsx_65",css:(0,H.AH)({marginTop:s.spacing.sm},""),size:"small",onClick:()=>n(!r),icon:r?(0,p.Y)(u.H$v,{}):(0,p.Y)(u.mc5,{}),"data-testid":"models-table-show-more-tags",children:r?(0,p.Y)(g.A,{id:"nddV+2",defaultMessage:"Show less"}):(0,p.Y)(g.A,{id:"cJo1zH",defaultMessage:"{value} more",values:{value:a.length-3}})})]})},q=e=>{let{versionNumber:t,name:s}=e;return t?(0,p.Y)(g.A,{id:"U+Jzcv",defaultMessage:"<link>Version {versionNumber}</link>",values:{versionNumber:t,link:e=>(0,p.Y)(U.N_,{to:A.fM.getModelVersionPageRoute(s,t),children:e})}}):(0,p.Y)(z,{})};var J=s(64756);const $=(0,L.zR)({id:"M1dwxx",defaultMessage:"Version {version}"});var X={name:"1u2bo0v",styles:"margin-right:0;cursor:pointer"},Z={name:"1u2bo0v",styles:"margin-right:0;cursor:pointer"};const ee=e=>{let{model:t}=e;const{aliases:s}=t,{theme:r}=(0,d.u)();if(null===s||void 0===s||!s.length)return null;const n=(0,I.sortBy)(s,(e=>{let{version:t}=e;return parseInt(t,10)||0})).reverse(),o=(0,I.first)(n);if(!o)return null;const a=n.filter((e=>e!==o));return(0,p.FD)("div",{children:[(0,p.FD)(U.N_,{to:A.fM.getModelVersionPageRoute(t.name,o.version),children:[(0,p.Y)(J.m,{value:o.alias,css:X}),": ",(0,p.Y)(g.A,{...$,values:{version:o.version}})]}),a.length>0&&(0,p.FD)(u.rId.Root,{modal:!1,children:[(0,p.Y)(u.rId.Trigger,{asChild:!0,children:(0,p.FD)(d.B,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelstablealiasedversionscell.tsx_47",size:"small",css:(0,H.AH)({borderRadius:12,marginLeft:r.spacing.xs},""),children:["+",s.length-1]})}),(0,p.Y)(u.rId.Content,{align:"start",children:a.map((e=>{let{alias:s,version:n}=e;return(0,p.Y)(u.rId.Item,{componentId:"codegen_mlflow_app_src_model-registry_components_aliases_modelstablealiasedversionscell.tsx_57",children:(0,p.FD)(U.N_,{to:A.fM.getModelVersionPageRoute(t.name,n),children:[(0,p.Y)(J.m,{value:s,css:Z}),":"," ",(0,p.Y)("span",{css:(0,H.AH)({color:r.colors.actionTertiaryTextDefault},""),children:(0,p.Y)(g.A,{...$,values:{version:n}})})]})},s)}))})]})]})};var te=s(73414),se=s(52350);const re=(e,t)=>{const s=e&&e.find((e=>e.current_stage===t));return s&&s.version};var ne=function(e){return e.NAME="name",e.LATEST_VERSION="latest_versions",e.LAST_MODIFIED="timestamp",e.CREATED_BY="user_id",e.STAGE_STAGING="stage_staging",e.STAGE_PRODUCTION="stage_production",e.TAGS="tags",e.ALIASED_VERSIONS="aliased_versions",e}(ne||{});const oe=e=>{let{modelsData:t,orderByAsc:s,orderByKey:r,onSortChange:n,isLoading:c,error:m,isFiltered:f,pagination:S}=e;const _=(0,O.A)(),{usingNextModelsUI:v}=(0,te.E)(),y=t.map((e=>e)),M=(0,o.useMemo)((()=>{const e=[{id:ne.NAME,enableSorting:!0,header:_.formatMessage({id:"g17Es2",defaultMessage:"Name"}),accessorKey:"name",cell:e=>{let{getValue:t}=e;return(0,p.Y)(U.N_,{to:A.fM.getModelPageRoute(String(t())),children:(0,p.Y)(u.paO,{title:t(),children:t()})})},meta:{styles:{minWidth:200,flex:1}}},{id:ne.LATEST_VERSION,enableSorting:!1,header:_.formatMessage({id:"CruI7o",defaultMessage:"Latest version"}),accessorKey:"latest_versions",cell:e=>{let{getValue:t,row:{original:s}}=e;const{name:r}=s,n=t(),o=Boolean(null===n||void 0===n?void 0:n.length)&&Math.max(...n.map((e=>parseInt(e.version,10)))).toString()||"";return(0,p.Y)(q,{name:r,versionNumber:o})},meta:{styles:{maxWidth:120}}}];return v?e.push({id:ne.ALIASED_VERSIONS,enableSorting:!1,header:_.formatMessage({id:"WQwCH6",defaultMessage:"Aliased versions"}),cell:e=>{let{row:{original:t}}=e;return(0,p.Y)(ee,{model:t})},meta:{styles:{minWidth:150}}}):e.push({id:ne.STAGE_STAGING,enableSorting:!1,header:_.formatMessage({id:"08WP3h",defaultMessage:"Staging"}),cell:e=>{let{row:{original:t}}=e;const{latest_versions:s,name:r}=t,n=re(s,i.e3.STAGING);return(0,p.Y)(q,{name:r,versionNumber:n})},meta:{styles:{maxWidth:120}}},{id:ne.STAGE_PRODUCTION,enableSorting:!1,header:_.formatMessage({id:"PGbCZD",defaultMessage:"Production"}),cell:e=>{let{row:{original:t}}=e;const{latest_versions:s,name:r}=t,n=re(s,i.e3.PRODUCTION);return(0,p.Y)(q,{name:r,versionNumber:n})},meta:{styles:{maxWidth:120}}}),e.push({id:ne.CREATED_BY,header:_.formatMessage({id:"WP1pyQ",defaultMessage:"Created by"}),accessorKey:"user_id",enableSorting:!1,cell:e=>{let{getValue:t,row:{original:s}}=e;return(0,p.Y)("span",{title:t(),children:t()})},meta:{styles:{flex:1}}},{id:ne.LAST_MODIFIED,enableSorting:!0,header:_.formatMessage({id:"Gs+blV",defaultMessage:"Last modified"}),accessorKey:"last_updated_timestamp",cell:e=>{let{getValue:t}=e;return(0,p.Y)("span",{children:a.A.formatTimestamp(t(),_)})},meta:{styles:{flex:1,maxWidth:150}}},{id:ne.TAGS,header:_.formatMessage({id:"nC54Nf",defaultMessage:"Tags"}),enableSorting:!1,accessorKey:"tags",cell:e=>{let{getValue:t}=e;return(0,p.Y)(j,{tags:t()})}}),e}),[_,v]),I=[{id:r,desc:!s}];let Y=l.gw;const C=(0,p.Y)(g.A,{id:"JNS471",defaultMessage:"No results. Try using a different keyword or adjusting your filters."}),R=m?(0,p.Y)(u.SvL,{image:(0,p.Y)(d.W,{}),description:m instanceof se.s?m.getMessageField():m.message,title:(0,p.Y)(g.A,{id:"5cac8T",defaultMessage:"Error fetching models"})}):f?(0,p.Y)(u.SvL,{description:C,image:(0,p.Y)(h.S,{}),"data-testid":"model-list-no-results"}):(0,p.Y)(u.SvL,{description:(0,p.Y)(g.A,{id:"Oj2ENw",defaultMessage:"No models registered yet. <link>Learn more about registering models</link>.",values:{link:e=>(0,p.Y)("a",{target:"_blank",rel:"noopener noreferrer",href:Y,children:e})}}),image:(0,p.Y)(u.c11,{}),button:(0,p.Y)(b,{buttonType:"primary",buttonText:(0,p.Y)(g.A,{id:"RUw2fH",defaultMessage:"Create a model"})})}),x=(0,V.N4)({data:y,columns:M,state:{sorting:I},getCoreRowModel:(0,G.HT)(),getRowId:e=>{let{id:t}=e;return t},onSortingChange:e=>{const[t]="function"===typeof e?e(I):e;t&&n({orderByKey:t.id,orderByAsc:!t.desc})}});return(0,p.Y)(p.FK,{children:(0,p.FD)(u.XIK,{"data-testid":"model-list-table",pagination:S,scrollable:!0,empty:!c&&0===x.getRowModel().rows.length||m?R:void 0,children:[(0,p.Y)(u.Hjg,{isHeader:!0,children:x.getLeafHeaders().map((e=>{var t;return(0,p.Y)(u.A0N,{componentId:"codegen_mlflow_app_src_model-registry_components_model-list_modellisttable.tsx_412",ellipsis:!0,sortable:e.column.getCanSort(),sortDirection:e.column.getIsSorted()||"none",onToggleSort:()=>{const[t]=I,s=!!(e.column.id===t.id)&&!t.desc;e.column.toggleSorting(s)},css:null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.styles,children:(0,V.Kv)(e.column.columnDef.header,e.getContext())},e.id)}))}),c?(0,p.Y)(u.BAM,{table:x}):x.getRowModel().rows.map((e=>(0,p.Y)(u.Hjg,{children:e.getAllCells().map((e=>{var t;return(0,p.Y)(u.nA6,{ellipsis:!0,css:null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.styles,children:(0,V.Kv)(e.column.columnDef.cell,e.getContext())},e.id)}))},e.id)))]})})};var ae=s(48588),ie=s(84963);var le={name:"18e72da",styles:"width:100%;align-items:center;display:flex"},de={name:"82a6rk",styles:"flex:1"};class ce extends o.Component{constructor(e){super(e),this.handleSearch=(e,t)=>{null===e||void 0===e||e.preventDefault(),this.props.onSearch(t)},this.unifiedTableSortChange=e=>{let{orderByKey:t,orderByAsc:s}=e;const r={timestamp:"last_updated_timestamp"}[t]||t;this.handleTableChange(void 0,void 0,{field:r,order:s?"undefined":"descend"})},this.handleTableChange=(e,t,s)=>{this.props.onClickSortableColumn(ce.getSortFieldName(s.field),s.order)},this.handleClickNext=()=>{this.props.onClickNext()},this.handleClickPrev=()=>{this.props.onClickPrev()},this.handleSetMaxResult=e=>{let{item:t,key:s,keyPath:r,domEvent:n}=e;this.props.onSetMaxResult(s)},this.state={maxResultsSelection:i.CG}}disableOnboardingHelper(){ce.getLocalStore(l.Ge).setItem("showRegistryHelper","false")}static getLocalStore(e){return w.A.getStoreForComponent("ModelListView",e)}componentDidMount(){a.A.updatePageTitle("MLflow Models")}render(){const{models:e,currentPage:t,nextPageToken:s,searchInput:r}=this.props,{loading:n,error:o}=this.props,a=Boolean(r),i=(0,p.Y)(g.A,{id:"qMwRy3",defaultMessage:"Registered Models"});return(0,p.FD)(ae.L,{"data-test-id":"ModelListView-container",usesFullHeight:!0,children:[(0,p.FD)("div",{children:[(0,p.Y)(E.z,{title:i,spacerSize:"xs",children:(0,p.Y)(b,{})}),(0,p.FD)(d.T.Hint,{children:[ce.getLearnMoreDisplayString()," ",(0,p.Y)(g.A,{id:"aYsI8a",defaultMessage:"<link>Learn more</link>",values:{link:e=>(0,p.Y)(d.T.Link,{componentId:"codegen_mlflow_app_src_model-registry_components_modellistview.tsx_244",href:ce.getLearnMoreLinkUrl(),openInNewTab:!0,children:e})}})]}),(0,p.Y)(k.S,{}),(0,p.Y)(K,{searchFilter:this.props.searchInput,onSearchFilterChange:e=>this.handleSearch(null,e),isFiltered:a})]}),(0,p.Y)(oe,{modelsData:e,onSortChange:this.unifiedTableSortChange,orderByKey:this.props.orderByKey,orderByAsc:this.props.orderByAsc,isLoading:n||!1,error:o,pagination:(0,p.FD)("div",{"data-testid":"model-list-view-pagination",css:le,children:[(0,p.Y)("div",{css:de,children:(0,D.WX)()&&(0,p.Y)(ie.C,{})}),(0,p.Y)("div",{children:(0,p.Y)(u.vIA,{componentId:"codegen_mlflow_app_src_model-registry_components_modellistview.tsx_305",hasNextPage:Boolean(s),hasPreviousPage:t>1,onNextPage:this.handleClickNext,onPreviousPage:this.handleClickPrev,pageSizeSelect:{onChange:e=>this.handleSetMaxResult({key:e}),default:this.props.maxResultValue,options:[10,25,50,100]}})})]}),isFiltered:a})]})}}ce.defaultProps={models:[],searchInput:""},ce.getSortFieldName=e=>{switch(e){case"name":return i.Qs;case"last_updated_timestamp":return i.BE;default:return null}},ce.getLearnMoreLinkUrl=()=>l.qT,ce.getLearnMoreDisplayString=()=>l.N0;const ue=(0,te.p)((0,m.Ay)(ce));var he=s(59508),ge=s(84069);class me extends o.Component{constructor(e){var t;super(e),t=this,this.modelListPageStoreKey="ModelListPageStore",this.defaultPersistedPageTokens={1:null},this.initialSearchRegisteredModelsApiId=(0,M.yk)(),this.searchRegisteredModelsApiId=(0,M.yk)(),this.criticalInitialRequestIds=[this.initialSearchRegisteredModelsApiId],this.pollIntervalId=void 0,this.isEmptyPageResponse=e=>!e||!e.registered_models||!e.next_page_token,this.updatePageState=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=t.getNextPageTokenFromResponse(s);t.setState((t=>({currentPage:e,pageTokens:{...t.pageTokens,[e+1]:r}})),(()=>{t.setPersistedPageTokens(t.state.pageTokens)}))},this.handleSearch=e=>{this.resetHistoryState(),this.setState({searchInput:e},(()=>{this.loadPage(1,!1)}))},this.handleSearchInputChange=e=>{this.setState({searchInput:e})},this.updateUrlWithSearchFilter=(e,t,s,r)=>{const n={};e&&(n.searchInput=e),t&&t!==i.Qs&&(n.orderByKey=t),!1===s&&(n.orderByAsc=s),r&&1!==r&&(n.page=r);const o=(0,U.Oz)(`/models?${a.A.getSearchUrlFromState(n)}`);o!==this.props.location.pathname+this.props.location.search&&this.props.navigate(o)},this.handleMaxResultsChange=e=>{this.setState({maxResultsSelection:parseInt(e,10)},(()=>{this.resetHistoryState();const{maxResultsSelection:e}=this.state;this.setMaxResultsInStore(e),this.loadPage(1,!1)}))},this.handleClickNext=()=>{const{currentPage:e}=this.state;this.loadPage(e+1,!1)},this.handleClickPrev=()=>{const{currentPage:e}=this.state;this.loadPage(e-1,!1)},this.handleClickSortableColumn=(e,t)=>{const s=t!==i.uB.DESC;this.setState({orderByKey:e,orderByAsc:s},(()=>{this.resetHistoryState(),this.loadPage(1,!1)}))},this.getMaxResultsSelection=()=>this.state.maxResultsSelection,this.state={orderByKey:i.Qs,orderByAsc:!0,currentPage:1,maxResultsSelection:this.getPersistedMaxResults(),pageTokens:{},loading:!0,error:void 0,searchInput:(0,he._C)(this.getUrlState())}}getUrlState(){return this.props.location?a.A.getSearchParamsFromUrl(this.props.location.search):{}}componentDidMount(){const e=this.getUrlState(),t=this.getPersistedPageTokens(),s=this.getPersistedMaxResults();this.setState({orderByKey:void 0===e.orderByKey?this.state.orderByKey:e.orderByKey,orderByAsc:void 0===e.orderByAsc?this.state.orderByAsc:"true"===e.orderByAsc,currentPage:void 0!==e.page&&e.page in t?parseInt(e.page,10):this.state.currentPage,maxResultsSelection:s,pageTokens:t},(()=>{this.loadModels(!0)}))}getPersistedPageTokens(){const e=me.getLocalStore(this.modelListPageStoreKey);return e&&e.getItem("page_tokens")?JSON.parse(e.getItem("page_tokens")):this.defaultPersistedPageTokens}setPersistedPageTokens(e){const t=me.getLocalStore(this.modelListPageStoreKey);t&&t.setItem("page_tokens",JSON.stringify(e))}getPersistedMaxResults(){const e=me.getLocalStore(this.modelListPageStoreKey);return e&&e.getItem("max_results")?parseInt(e.getItem("max_results"),10):i.CG}setMaxResultsInStore(e){me.getLocalStore(this.modelListPageStoreKey).setItem("max_results",e.toString())}static getLocalStore(e){return w.A.getSessionScopedStoreForComponent("ModelListPage",e)}loadModels(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.loadPage(this.state.currentPage,e)}resetHistoryState(){this.setState((e=>({currentPage:1,pageTokens:this.defaultPersistedPageTokens}))),this.setPersistedPageTokens(this.defaultPersistedPageTokens)}getNextPageTokenFromResponse(e){const{value:t}=e;return this.isEmptyPageResponse(t)?null:t.next_page_token}loadPage(e,t){const{searchInput:s,pageTokens:r,orderByKey:n,orderByAsc:o}=this.state;this.setState({loading:!0,error:void 0}),this.updateUrlWithSearchFilter(s,n,o,e),this.props.searchRegisteredModelsApi((0,he.so)({query:s}),this.state.maxResultsSelection,me.getOrderByExpr(n,o),r[e],t?this.initialSearchRegisteredModelsApiId:this.searchRegisteredModelsApiId).then((t=>{this.updatePageState(e,t)})).catch((e=>{this.setState({currentPage:1,error:e}),this.resetHistoryState()})).finally((()=>{this.setState({loading:!1})}))}render(){const{orderByKey:e,orderByAsc:t,currentPage:s,pageTokens:r,searchInput:n}=this.state,{models:o}=this.props;return(0,p.Y)(ge.m,{children:(0,p.Y)(ue,{models:o,loading:this.state.loading,error:this.state.error,searchInput:n,orderByKey:e,orderByAsc:t,currentPage:s,nextPageToken:r[s+1],onSearch:this.handleSearch,onClickNext:this.handleClickNext,onClickPrev:this.handleClickPrev,onClickSortableColumn:this.handleClickSortableColumn,onSetMaxResult:this.handleMaxResultsChange,maxResultValue:this.getMaxResultsSelection()})})}}me.getOrderByExpr=(e,t)=>e?`${e} ${t?"ASC":"DESC"}`:"";const pe={searchRegisteredModelsApi:y.JK},fe=(0,C.h)((0,v.Ng)((e=>({models:Object.values(e.entities.modelByName)})),pe)(me)),Se=(0,r.X)(n.A.mlflowServices.MODEL_REGISTRY,(()=>(0,p.Y)(fe,{})));var _e=Se},79085:function(e,t,s){s.d(t,{o:function(){return c},z:function(){return h}});var r=s(89555),n=(s(31014),s(48012)),o=s(32599),a=s(15579),i=s(88464),l=s(30214),d=s(50111);function c(e){let{menu:t}=e;const s=(0,d.Y)(n.W1t,{children:t.map((e=>{let{id:t,itemName:s,onClick:r,href:o,...a}=e;return(0,d.Y)(n.W1t.Item,{onClick:r,href:o,"data-test-id":t,...a,children:s},t)}))});return t.length>0?(0,d.Y)(n.msM,{overlay:s,trigger:["click"],placement:"bottomLeft",arrow:!0,children:(0,d.Y)(o.B,{componentId:"codegen_mlflow_app_src_shared_building_blocks_pageheader.tsx_54",icon:(0,d.Y)(n.ssM,{}),"data-test-id":"overflow-menu-trigger","aria-label":"Open header dropdown menu"})}):null}var u={name:"1gz4j9a",styles:"margin-left:0"};function h(e){const{title:t,breadcrumbs:s=[],titleAddOns:c=[],preview:h,children:g,spacerSize:m,hideSpacer:p=!1,dangerouslyAppendEmotionCSS:f}=e,{theme:S}=(0,o.u)();(0,i.A)();return(0,d.FD)(d.FK,{children:[(0,d.Y)(n.Y9Y,{breadcrumbs:s.length>0&&(0,d.Y)(n.QpV,{includeTrailingCaret:!0,children:s.map(((e,t)=>(0,d.Y)(n.QpV.Item,{children:e},t)))}),buttons:g,title:t,titleAddOns:(0,d.FD)(d.FK,{children:[h&&(0,d.Y)(l.W,{css:u}),c]}),dangerouslyAppendEmotionCSS:f}),(0,d.Y)(a.S,{css:(0,r.AH)({flexShrink:0,...p?{display:"none"}:{}},""),size:m})]})}},81866:function(e,t,s){s.d(t,{$0:function(){return c},$p:function(){return u},BE:function(){return y},CG:function(){return S},Gs:function(){return f},IP:function(){return h},QQ:function(){return d},Qs:function(){return v},SF:function(){return _},Tm:function(){return I},UA:function(){return p},e3:function(){return i},gL:function(){return A},jI:function(){return l},uB:function(){return M},zA:function(){return g},zr:function(){return m}});var r=s(48012),n=s(88443),o=s(24478),a=s(50111);const i={NONE:"None",STAGING:"Staging",PRODUCTION:"Production",ARCHIVED:"Archived"},l=[i.STAGING,i.PRODUCTION],d={[i.NONE]:"None",[i.STAGING]:"Staging",[i.PRODUCTION]:"Production",[i.ARCHIVED]:"Archived"},c={[i.NONE]:(0,a.Y)(r.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_37",children:d[i.NONE]}),[i.STAGING]:(0,a.Y)(r.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_38",color:"lemon",children:d[i.STAGING]}),[i.PRODUCTION]:(0,a.Y)(r.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_39",color:"lime",children:d[i.PRODUCTION]}),[i.ARCHIVED]:(0,a.Y)(r.vwO,{componentId:"codegen_mlflow_app_src_model-registry_constants.tsx_40",color:"charcoal",children:d[i.ARCHIVED]})};let u=function(e){return e.APPLIED_TRANSITION="APPLIED_TRANSITION",e.REQUESTED_TRANSITION="REQUESTED_TRANSITION",e.SYSTEM_TRANSITION="SYSTEM_TRANSITION",e.CANCELLED_REQUEST="CANCELLED_REQUEST",e.APPROVED_REQUEST="APPROVED_REQUEST",e.REJECTED_REQUEST="REJECTED_REQUEST",e.NEW_COMMENT="NEW_COMMENT",e}({});(0,a.Y)("div",{style:{marginTop:-12},children:"_"});const h={READY:"READY"},g={[h.READY]:(0,a.Y)(n.A,{id:"f/An1W",defaultMessage:"Ready."})},m={[h.READY]:(0,a.Y)(n.A,{id:"zAvilr",defaultMessage:"Ready"})},p={[h.READY]:(0,a.Y)(o.vV,{})},f=1e4,S=25,_=75,v="name",y="timestamp",M={ASC:"ascend",DESC:"descend"},A=e=>(0,a.Y)(n.A,{id:"Ll6vbT",defaultMessage:"Model versions in the `{currentStage}` stage will be moved to the `Archived` stage.",values:{currentStage:e}}),I="https://mlflow.org/docs/latest/model-registry.html#using-registered-model-aliases"},84069:function(e,t,s){s.d(t,{m:function(){return a}});var r=s(48012),n=s(50111);var o={name:"gmowil",styles:"height:calc(100% - 60px)"};const a=e=>{let{children:t,className:s}=e;return(0,n.Y)(r.ffj,{css:o,className:s,children:t})}},87877:function(e,t,s){s.d(t,{J:function(){return o},N:function(){return a}});var r=s(63528),n=s(46795);const o=e=>(t,s,n)=>{s?e().includes(s)?n(`Experiment "${s}" already exists.`):r.x.getExperimentByName({experiment_name:s}).then((e=>n(`Experiment "${s}" already exists in deleted state.\n                                 You can restore the experiment, or permanently delete the\n                                 experiment from the .trash folder (under tracking server's\n                                 root folder) in order to use this experiment name again.`))).catch((e=>n(void 0))):n(void 0)},a=(e,t,s)=>{t?n.x.getRegisteredModel({name:t}).then((()=>s(`Model "${t}" already exists.`))).catch((e=>s(void 0))):s(void 0)}}}]);
//# sourceMappingURL=7202.2447fcfe.chunk.js.map