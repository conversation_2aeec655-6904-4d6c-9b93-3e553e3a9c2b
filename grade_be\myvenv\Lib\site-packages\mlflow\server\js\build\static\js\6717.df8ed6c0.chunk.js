"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[6717],{39234:function(e,t,r){r.r(t),r.d(t,{RunPage:function(){return Pt},default:function(){return Ot}});var n=r(89555),i=r(48012),a=r(32599),s=r(10811),o=r(4877),l=r.n(o),d=r(31014),u=r(48588),c=r(93215),m=r(76010),g=r(25866),p=r(53677),v=r(5643),h=r(75111),f=r(50111);const Y=e=>{let{runTags:t,experimentId:r,runOutputs:i,artifactUri:s,runUuid:o}=e;const{theme:l}=(0,a.u)(),d=(0,h.U)(`(min-width: ${l.responsive.breakpoints.sm}px)`);return(0,f.Y)("div",{css:(0,n.AH)({flex:1,overflow:"hidden",display:"flex",paddingBottom:l.spacing.md,position:"relative"},""),children:(0,f.Y)(v.Ay,{runUuid:o,runTags:t,runOutputs:i,useAutoHeight:d,artifactRootUri:s,experimentId:r})})};var x=r(88443),_=r(79085),I=r(58481),y=r(91144);const S=()=>{const{"*":e}=(0,c.g)();return"model-metrics"===e?g.N3.MODEL_METRIC_CHARTS:"system-metrics"===e?g.N3.SYSTEM_METRIC_CHARTS:(0,y.oX)()&&"traces"===e?g.N3.TRACES:null!==e&&void 0!==e&&e.match(/^(artifactPath|artifacts)/)?g.N3.ARTIFACTS:g.N3.OVERVIEW},M=[g.N3.ARTIFACTS,g.N3.EVALUATIONS],A=()=>{const{experimentId:e,runUuid:t}=(0,c.g)(),r=(0,c.Zp)(),{theme:n}=(0,a.u)(),s=S(),[o,l]=(0,d.useState)(M.includes(s));return(0,f.FD)(i.Y6f,{activeKey:s,onChange:n=>{e&&t&&s!==n&&(l(M.includes(n)),n!==g.N3.OVERVIEW?r(I.h.getRunPageTabRoute(e,t,n)):r(I.h.getRunPageRoute(e,t)))},tabBarStyle:{margin:o&&"0px"},children:[(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"kA+QJr",defaultMessage:"Overview"})},g.N3.OVERVIEW),(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"j25Ttg",defaultMessage:"Model metrics"})},g.N3.MODEL_METRIC_CHARTS),(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"hFlaPP",defaultMessage:"System metrics"})},g.N3.SYSTEM_METRIC_CHARTS),(0,y.oX)()?(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"s8a93+",defaultMessage:"Traces"})},g.N3.TRACES):null,(0,f.Y)(i.Y6f.TabPane,{tab:(0,f.Y)(x.A,{id:"vPqFJE",defaultMessage:"Artifacts"})},g.N3.ARTIFACTS)]})};var R=r(9133),w=r(85466),D=r(81866),C=(r(69869),r(21317));var T={name:"s5xdrg",styles:"display:flex;align-items:center"},b={name:"2ecpu6",styles:"margin-left:8px;margin-right:4px"};function E(e){let{models:t,onRegisterClick:r,experimentId:s,runUuid:o}=e;const{theme:l}=(0,a.u)(),d=(e,t)=>(0,f.FD)(i.rId.Group,{children:[(0,f.Y)(i.rId.Label,{children:e}),t.map((e=>{const t=(0,R.first)(e.registeredModelVersionSummaries);if(!t)return(0,f.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_50",onClick:()=>r(e),children:[(0,f.Y)("div",{css:(0,n.AH)({marginRight:l.spacing.md},""),children:(0,R.last)(e.path.split("/"))}),(0,f.Y)(i.rId.HintColumn,{children:(0,f.Y)(c.N_,{target:"_blank",to:I.h.getRunPageTabRoute(s,o,"artifacts/"+e.path),children:(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_58",type:"link",size:"small",onClick:e=>{e.stopPropagation()},endIcon:(0,f.Y)(a.at,{}),children:(0,f.Y)(x.A,{id:"NqEELO",defaultMessage:"View model"})})})})]},e.absolutePath);const{status:d,displayedName:u,version:m,link:g}=t;return(0,f.Y)(c.N_,{target:"_blank",to:g,children:(0,f.FD)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_80",children:[(0,f.Y)(i.rId.IconWrapper,{css:T,children:"READY"===d?(0,f.Y)(C.h,{}):d?D.UA[d]:null}),(0,f.FD)("span",{css:(0,n.AH)({marginRight:l.spacing.md},""),children:[u,(0,f.FD)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_90",css:b,children:["v",m]})]}),(0,f.Y)(i.rId.HintColumn,{children:(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_89",type:"link",size:"small",onClick:e=>{e.stopPropagation()},endIcon:(0,f.Y)(a.at,{}),children:(0,f.Y)(x.A,{id:"T1sd79",defaultMessage:"Go to model"})})})]})},e.absolutePath)}))]}),u=t.filter((e=>e.registeredModelVersionSummaries.length>0)),m=t.filter((e=>!e.registeredModelVersionSummaries.length));return(0,f.FD)(f.FK,{children:[m.length?d("Unregistered models",m):null,m.length&&u.length?(0,f.Y)(i.rId.Separator,{}):null,u.length?d("Registered models",u):null]})}const U=e=>{let{runUuid:t,experimentId:r,runTags:s,artifactRootUri:o,registeredModelVersionSummaries:l}=e;const{theme:u}=(0,a.u)(),g=(0,d.useMemo)((()=>s?m.A.getLoggedModelsFromTags(s).map((e=>{let{artifactPath:t}=e;return t})):[]),[s]),p=(0,d.useMemo)((()=>(0,R.orderBy)(g.map((e=>({path:e,absolutePath:`${o}/${e}`,registeredModelVersionSummaries:(null===l||void 0===l?void 0:l.filter((t=>{let{source:r}=t;return r===`${o}/${e}`})))||[]}))),(e=>{var t;return parseInt((null===(t=e.registeredModelVersionSummaries[0])||void 0===t?void 0:t.version)||"0",10)}),"desc")),[g,l,o]),[v,h]=(0,d.useState)(null);if(p.length>1){const e=p.filter((e=>e.registeredModelVersionSummaries.length>0));return(0,f.FD)(f.FK,{children:[v&&(0,f.Y)(w.vR,{runUuid:t,modelPath:v.absolutePath,modelRelativePath:v.path,disabled:!1,showButton:!1,modalVisible:!0,onCloseModal:()=>h(null)}),(0,f.FD)(i.rId.Root,{modal:!1,children:[(0,f.Y)(i.paO,{placement:"bottom",title:(0,f.Y)(x.A,{id:"qE/ZB7",defaultMessage:"{registeredCount}/{loggedCount} logged models are registered",values:{registeredCount:e.length,loggedCount:p.length}}),children:(0,f.Y)(i.rId.Trigger,{asChild:!0,children:(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_195",type:"primary",endIcon:(0,f.Y)(i.D3D,{}),children:(0,f.Y)(x.A,{id:"WGN2f3",defaultMessage:"Register model"})})})}),(0,f.Y)(i.rId.Content,{align:"end",children:(0,f.Y)(E,{models:p,onRegisterClick:h,experimentId:r,runUuid:t})})]})]})}const Y=(0,R.first)(p);if(!Y)return null;const _=(0,R.first)(Y.registeredModelVersionSummaries);return _?(0,f.Y)(c.N_,{to:_.link,target:"_blank",css:(0,n.AH)({marginLeft:u.spacing.sm},""),children:(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewheaderregistermodelbutton.tsx_231",endIcon:(0,f.Y)(a.at,{}),type:"link",children:"Model registered"})}):(0,f.Y)(w.vR,{disabled:!1,runUuid:t,modelPath:Y.absolutePath,modelRelativePath:Y.path,showButton:!0,buttonType:"primary"})};var k={name:"ozd7xs",styles:"flex-shrink:0"};const F=e=>{let{hasComparedExperimentsBefore:t,comparedExperimentIds:r=[],experiment:n,runDisplayName:i,runTags:a,runParams:s,runUuid:o,handleRenameRunClick:l,handleDeleteRunClick:d,artifactRootUri:u,registeredModelVersionSummaries:m,isLoading:g}=e;const p=[function(){var e;return t&&r?(0,f.Y)(c.N_,{to:I.h.getCompareExperimentsPageRoute(r),children:(0,f.Y)(x.A,{id:"LLm5Bo",defaultMessage:"Displaying Runs from {numExperiments} Experiments",values:{numExperiments:r.length}})}):(0,f.Y)(c.N_,{to:I.h.getExperimentPageRoute(null!==(e=null===n||void 0===n?void 0:n.experimentId)&&void 0!==e?e:""),"data-test-id":"experiment-runs-link",children:n.name})}()];return(0,f.FD)("div",{css:k,children:[(0,f.FD)(_.z,{title:(0,f.Y)("span",{"data-test-id":"runs-header",children:i}),breadcrumbs:p,children:[(0,f.Y)(_.o,{menu:[{id:"overflow-rename-button",onClick:l,itemName:(0,f.Y)(x.A,{id:"fv7vQf",defaultMessage:"Rename"})},...d?[{id:"overflow-delete-button",onClick:d,itemName:(0,f.Y)(x.A,{id:"7WkP1e",defaultMessage:"Delete"})}]:[]]}),(()=>{var e;return(0,f.Y)(U,{runUuid:o,experimentId:null!==(e=null===n||void 0===n?void 0:n.experimentId)&&void 0!==e?e:"",runTags:a,artifactRootUri:u,registeredModelVersionSummaries:m})})()]}),(0,f.Y)(A,{})]})};var L=r(88464),N=r(15579),P=r(21616),O=r(76758);const B=e=>{let{status:t}=e;const{theme:r}=(0,a.u)();return(0,f.FD)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewstatusbox.tsx_81",css:(0,n.AH)({backgroundColor:"FINISHED"===t?r.isDarkMode?r.colors.green800:r.colors.green100:"KILLED"===t||"FAILED"===t?r.isDarkMode?r.colors.red800:r.colors.red100:"SCHEDULED"===t||"RUNNING"===t?r.isDarkMode?r.colors.blue800:r.colors.blue100:void 0},""),children:[t&&(0,f.Y)(O.F,{status:t})," ",(0,f.Y)(a.T.Text,{css:(0,n.AH)({marginLeft:r.spacing.sm},""),children:"FINISHED"===t?(0,f.Y)(a.T.Text,{color:"success",children:(0,f.Y)(x.A,{id:"ujm55z",defaultMessage:"Finished"})}):"KILLED"===t?(0,f.Y)(a.T.Text,{color:"error",children:(0,f.Y)(x.A,{id:"AupQl+",defaultMessage:"Killed"})}):"FAILED"===t?(0,f.Y)(a.T.Text,{color:"error",children:(0,f.Y)(x.A,{id:"AM8DuO",defaultMessage:"Failed"})}):"RUNNING"===t?(0,f.Y)(a.T.Text,{color:"info",children:(0,f.Y)(x.A,{id:"On3g1B",defaultMessage:"Running"})}):"SCHEDULED"===t?(0,f.Y)(a.T.Text,{color:"info",children:(0,f.Y)(x.A,{id:"X8OaXU",defaultMessage:"Scheduled"})}):t})]})},H=e=>{var t;let{runInfo:r,tags:n}=e;const i=m.A.getUser(r,n);return(0,f.Y)(c.N_,{to:I.h.searchRunsByUser(null!==(t=null===r||void 0===r?void 0:r.experimentId)&&void 0!==t?t:"",i),children:i})};var V=r(68109),z=r(41028),K=r(69526),j=r(32039),$=r(70618),W=r(9856),G=r(3288);const{systemMetricsLabel:Z,modelMetricsLabel:J}=(0,K.YK)({systemMetricsLabel:{id:"aFMLIO",defaultMessage:"System metrics"},modelMetricsLabel:{id:"NiSPrL",defaultMessage:"Model metrics"}}),X=e=>t=>{let{key:r}=t;return r.toLowerCase().includes(e.toLowerCase())};var q={name:"1ff36h2",styles:"flex-grow:1"};const Q=e=>{let{metricsList:t,runInfo:r,header:s,table:o}=e;const{theme:l}=(0,a.u)(),[{column:d}]=o.getLeafHeaders();return t.length?(0,f.FD)(f.FK,{children:[s&&(0,f.Y)(i.Hjg,{children:(0,f.Y)(i.nA6,{css:(0,n.AH)({flex:1,backgroundColor:l.colors.backgroundSecondary},""),children:(0,f.FD)(a.T.Text,{bold:!0,children:[s," (",t.length,")"]})})}),t.map((e=>{var t,n;let{key:a,value:s}=e;return(0,f.FD)(i.Hjg,{children:[(0,f.Y)(i.nA6,{style:{flexGrow:0,flexBasis:d.getSize()},children:(0,f.Y)(c.N_,{to:I.h.getMetricPageRoute([null!==(t=r.runUuid)&&void 0!==t?t:""],a,[null!==(n=r.experimentId)&&void 0!==n?n:""]),children:a})}),(0,f.Y)(i.nA6,{css:q,children:s.toString()})]},a)}))]}):null};var ee={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"},te={name:"ozd7xs",styles:"flex-shrink:0"};const re=e=>{let{latestMetrics:t,runInfo:r}=e;const{theme:s}=(0,a.u)(),{detailsPageTableStyles:o,detailsPageNoEntriesStyles:l,detailsPageNoResultsWrapperStyles:u}=(0,G.z)(),c=(0,L.A)(),[m,g]=(0,d.useState)(""),p=(0,d.useMemo)((()=>(0,R.values)(t)),[t]),v=(0,d.useMemo)((()=>[{id:"key",accessorKey:"key",header:()=>(0,f.Y)(x.A,{id:"pjlcSc",defaultMessage:"Metric"}),enableResizing:!0,size:240},{id:"value",header:()=>(0,f.Y)(x.A,{id:"t0Qkuc",defaultMessage:"Value"}),accessorKey:"value",enableResizing:!1}]),[]),h=(0,d.useMemo)((()=>{const e=p.filter((e=>{let{key:t}=e;return(0,j.bw)(t)})),t=p.filter((e=>{let{key:t}=e;return!(0,j.bw)(t)}));return e.length>0&&t.length>0?[{header:c.formatMessage(Z),metrics:e.filter(X(m))},{header:c.formatMessage(J),metrics:t.filter(X(m))}]:[{header:void 0,metrics:p.filter(X(m))}]}),[m,p,c]),Y=(0,$.N4)({data:p,getCoreRowModel:(0,W.HT)(),getRowId:e=>e.key,enableColumnResizing:!0,columnResizeMode:"onChange",columns:v});return(0,f.FD)("div",{css:ee,children:[(0,f.Y)(a.T.Title,{level:4,css:te,children:(0,f.Y)(x.A,{id:"m4159e",defaultMessage:"Metrics ({length})",values:{length:p.filter(X(m)).length}})}),(0,f.Y)("div",{css:(0,n.AH)({padding:s.spacing.sm,border:`1px solid ${s.colors.borderDecorative}`,borderRadius:s.general.borderRadiusBase,display:"flex",flexDirection:"column",flex:1,overflow:"hidden"},""),children:(()=>{if(!p.length)return(0,f.Y)("div",{css:l,children:(0,f.Y)(i.SvL,{description:(0,f.Y)(x.A,{id:"9vj5Ap",defaultMessage:"No metrics recorded"})})});const e=(0,R.sum)(h.map((e=>{let{metrics:t}=e;return t.length})))<1;return(0,f.FD)(f.FK,{children:[(0,f.Y)("div",{css:(0,n.AH)({marginBottom:s.spacing.sm},""),children:(0,f.Y)(z.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewmetricstable.tsx_186",prefix:(0,f.Y)(z.S,{}),placeholder:c.formatMessage({id:"w2+rkp",defaultMessage:"Search metrics"}),value:m,onChange:e=>g(e.target.value),allowClear:!0})}),(0,f.FD)(i.XIK,{scrollable:!0,empty:e?(0,f.Y)("div",{css:u,children:(0,f.Y)(i.SvL,{description:(0,f.Y)(x.A,{id:"lT1Joh",defaultMessage:"No metrics match the search filter"})})}):null,css:o,children:[(0,f.Y)(i.Hjg,{isHeader:!0,children:Y.getLeafHeaders().map((e=>(0,f.Y)(i.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewmetricstable.tsx_312",header:e,column:e.column,setColumnSizing:Y.setColumnSizing,isResizing:e.column.getIsResizing(),style:{flexGrow:e.column.getCanResize()?0:1,flexBasis:e.column.getCanResize()?e.column.getSize():void 0},children:(0,$.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),h.map(((e,t)=>(0,f.Y)(Q,{metricsList:e.metrics,runInfo:r,header:e.header,table:Y},e.header||t)))]})]})})()})]})};var ne=r(38243),ie=r(85343);var ae={name:"1flj9lk",styles:"text-align:left"},se={name:"ti75j2",styles:"margin:0"};const oe=e=>{let{dataset:t,onClick:r}=e;return(0,f.Y)(a.T.Link,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewdatasetbox.tsx_16",role:"link",css:ae,onClick:r,children:(0,f.Y)(ne.E,{datasetWithTags:t,displayTextAsLink:!0,css:se})})},le=e=>{let{tags:t,runInfo:r,datasets:s}=e;const[o,l]=(0,d.useState)(null),{theme:u}=(0,a.u)(),[c,m]=(0,d.useState)(!1);if(!s||!s.length)return null;const g=s[0],p=s.slice(1),v=e=>{var n,i,a;l({datasetWithTags:e,runData:{experimentId:null!==(n=r.experimentId)&&void 0!==n?n:void 0,runUuid:null!==(i=r.runUuid)&&void 0!==i?i:"",runName:null!==(a=r.runName)&&void 0!==a?a:void 0,datasets:s,tags:t}}),m(!0)};return(0,f.FD)("div",{css:(0,n.AH)({display:"flex",gap:u.spacing.sm,alignItems:"center"},""),children:[(0,f.Y)(oe,{dataset:g,onClick:()=>v(g)}),p.length?(0,f.FD)(i.rId.Root,{modal:!1,children:[(0,f.Y)(i.rId.Trigger,{asChild:!0,children:(0,f.FD)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewdatasetbox.tsx_70",size:"small",children:["+",p.length]})}),(0,f.Y)(i.rId.Content,{children:p.map((e=>(0,f.Y)(i.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewdatasetbox.tsx_81",children:(0,f.Y)(oe,{dataset:e,onClick:()=>v(e)})},e.dataset.digest)))})]}):null,o&&(0,f.Y)(ie.O,{isOpen:c,setIsOpen:m,selectedDatasetWithRun:o,setSelectedDatasetWithRun:l})]})};var de=r(26809),ue=r(36568);const ce=e=>{let{parentRunUuid:t}=e;const r=(0,s.wA)(),n=(0,s.d4)((e=>{let{entities:r}=e;return r.runInfosByUuid[t]})),a=(0,ue.t)({runUuid:t,disabled:!(0,y.wD)()}),o=(0,d.useMemo)((()=>{var e;return(0,y.wD)()?null===a||void 0===a||null===(e=a.data)||void 0===e?void 0:e.info:n}),[a,n]);return(0,d.useEffect)((()=>{(0,y.wD)()||o||r((0,de.aO)(t))}),[r,t,o]),o?o.experimentId&&o.runUuid?(0,f.Y)(c.N_,{to:I.h.getRunPageRoute(o.experimentId,o.runUuid),children:o.runName}):null:(0,f.Y)(i.I_K,{loading:!0,label:(0,f.Y)(x.A,{id:"nfIS4i",defaultMessage:"Parent run name loading"})})};var me=r(43683),ge=r(98597),pe=r(98590);var ve={name:"1wcfv52",styles:"margin-right:0"};const he=e=>{let{runUuid:t,tags:r,onTagsUpdated:o}=e;const{theme:l}=(0,a.u)(),u=(0,s.wA)(),c=(0,L.A)(),[m,g]=(0,d.useMemo)((()=>[(0,R.keys)(r).filter(pe.oD),(0,R.values)(r).filter((e=>{let{key:t}=e;return(0,pe.oD)(t)}))]),[r]),{EditTagsModal:p,showEditTagsModal:v,isLoading:h}=(0,me.Q)({valueRequired:!0,allAvailableTags:m,saveTagsHandler:async(e,r,n)=>u((0,de.hD)(t,r,n)).then(o)}),Y=()=>{v({tags:g})},_=c.formatMessage({id:"EwAZgg",defaultMessage:"Edit tags"});return(0,f.FD)("div",{css:(0,n.AH)({paddingTop:l.spacing.xs,paddingBottom:l.spacing.xs,display:"flex",flexWrap:"wrap",alignItems:"center","> *":{marginRight:"0 !important"},gap:l.spacing.xs},""),children:[g.length<1?(0,f.Y)(a.B,{componentId:"mlflow.run_details.overview.tags.add_button",size:"small",type:"tertiary",onClick:Y,children:(0,f.Y)(x.A,{id:"dt3hj5",defaultMessage:"Add tags"})}):(0,f.FD)(f.FK,{children:[g.map((e=>(0,f.Y)(ge.t,{tag:e,enableFullViewModal:!0,css:ve},`${e.key}-${e.value}`))),(0,f.Y)(N.T,{componentId:"mlflow.run_details.overview.tags.edit_button.tooltip",content:_,children:(0,f.Y)(a.B,{componentId:"mlflow.run_details.overview.tags.edit_button","aria-label":_,size:"small",icon:(0,f.Y)(i.R2l,{}),onClick:Y})})]}),h&&(0,f.Y)(a.S,{size:"small"}),p]})};var fe=r(96034),Ye=r(56928);const xe=e=>{var t;let{runUuid:r,tags:o,onDescriptionChanged:l}=e;const u=(null===(t=o[Ye.e])||void 0===t?void 0:t.value)||"",[c,m]=(0,d.useState)(!1),g=(0,L.A)(),{theme:p}=(0,a.u)(),v=(0,s.wA)(),h=!u;return(0,f.FD)("div",{css:(0,n.AH)({marginBottom:p.spacing.md},""),children:[(0,f.FD)(a.T.Title,{level:4,css:(0,n.AH)({display:"flex",alignItems:"center",gap:p.spacing.xs},""),children:[(0,f.Y)(x.A,{id:"ugwpKL",defaultMessage:"Description"}),(0,f.Y)(a.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewdescriptionbox.tsx_46",size:"small",type:"tertiary","aria-label":g.formatMessage({id:"Mtj9Ay",defaultMessage:"Edit description"}),onClick:()=>m(!0),icon:(0,f.Y)(i.R2l,{})})]}),h&&!c&&(0,f.Y)(a.T.Hint,{children:(0,f.Y)(x.A,{id:"VcNdOq",defaultMessage:"No description"})}),(!h||c)&&(0,f.Y)(fe.V,{defaultMarkdown:u,onSubmit:e=>v((0,de.Jv)(r,Ye.e,e)).then(l).then((()=>m(!1))),onCancel:()=>m(!1),showEditor:c})]})};var _e=r(4874);var Ie={name:"e0dnmk",styles:"cursor:pointer"};const ye=e=>{let{registeredModelVersionSummaries:t}=e;const{theme:r}=(0,a.u)();return(0,f.Y)(i.nEg,{children:null===t||void 0===t?void 0:t.map((e=>(0,f.FD)(c.N_,{to:e.link,css:(0,n.AH)({display:"flex",alignItems:"center",gap:r.spacing.sm},""),children:[(0,f.Y)(C.h,{})," ",e.displayedName," ",(0,f.FD)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewregisteredmodelsbox.tsx_40",css:Ie,children:["v",e.version]})]},e.displayedName)))})};var Se=r(41261),Me=r(82832);const Ae=async e=>{let{queryKey:t}=e;const[,{runUuid:r}]=t;return Me.M.getPromptVersionsForRun(r)};var Re={name:"1bmnxg7",styles:"white-space:nowrap"};const we=e=>{let{runUuid:t}=e;const{theme:r}=(0,a.u)(),{data:s,error:o,isLoading:l}=function(e){var t;let{runUuid:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=(0,Se.I)(["run_uuid",{runUuid:r}],{queryFn:Ae,retry:!1,...n});return{data:i.data,error:null!==(t=i.error)&&void 0!==t?t:void 0,isLoading:i.isLoading,refetch:i.refetch}}({runUuid:t}),d=null===s||void 0===s?void 0:s.model_versions;return l?(0,f.Y)(i.I_K,{}):o||!d||0===d.length?(0,f.Y)(a.T.Hint,{children:"\u2014"}):(0,f.Y)("div",{css:(0,n.AH)({display:"flex",flexDirection:"row",gap:r.spacing.sm,flexWrap:"wrap",padding:`${r.spacing.sm}px 0px`},""),children:d.map(((e,t)=>{const r=I.h.getPromptDetailsPageRoute(encodeURIComponent(e.name)),n=`${e.name} (v${e.version})`;return(0,f.FD)(a.T.Text,{css:Re,children:[(0,f.Y)(c.N_,{to:r,children:n}),t<d.length-1&&","]},n)}))})},De=e=>{let{loggedModels:t,loggedModelsV3:r,runInfo:s}=e;const{theme:o}=(0,a.u)(),{experimentId:l,runUuid:u}=s,m=e=>(0,R.first)(e)||(0,f.Y)(x.A,{id:"0HbGko",defaultMessage:"Model"}),g=(0,d.useMemo)((()=>{const e=t.map((e=>m(e.flavors)));return new Set(e).size!==e.length}),[t]);return(0,f.FD)(i.nEg,{children:[t.map(((e,t)=>(0,f.FD)(c.N_,{to:I.h.getRunPageRoute(null!==l&&void 0!==l?l:"",null!==u&&void 0!==u?u:"",e.artifactPath),css:(0,n.AH)({display:"flex",alignItems:"center",gap:o.spacing.sm,cursor:"pointer",height:g&&t>0?o.general.heightBase:o.general.heightSm},""),children:[(0,f.Y)(i.oiI,{}),(0,f.FD)("div",{children:[m(e.flavors),g&&t>0&&(0,f.Y)(a.T.Hint,{children:e.artifactPath})]})]},e.artifactPath))),r.map(((e,t)=>{var r,a,s,d,u;return(0,f.FD)(c.N_,{to:I.h.getExperimentLoggedModelDetailsPageRoute(null!==l&&void 0!==l?l:"",null!==(r=null===(a=e.info)||void 0===a?void 0:a.model_id)&&void 0!==r?r:""),css:(0,n.AH)({display:"flex",alignItems:"center",gap:o.spacing.sm,cursor:"pointer",height:g&&t>0?o.general.heightBase:o.general.heightSm},""),children:[(0,f.Y)(i.oiI,{}),(0,f.Y)("div",{children:null===(u=e.info)||void 0===u?void 0:u.name})]},null!==(s=null===(d=e.info)||void 0===d?void 0:d.model_id)&&void 0!==s?s:t)}))]})};var Ce=r(56412),Te=r(1323);var be={name:"1wcfv52",styles:"margin-right:0"},Ee={name:"kauk44",styles:"display:flex;gap:4px;white-space:nowrap"},Ue={name:"1a7v7i3",styles:"margin-right:0;&>div{padding-right:0;}"};const ke=e=>{var t,r,s;let{runUuid:o,tags:l,search:d,className:u}=e;const c=null===l||void 0===l||null===(t=l[g.xd])||void 0===t?void 0:t.value,p=null===l||void 0===l||null===(r=l[m.A.gitCommitTag])||void 0===r?void 0:r.value,v=m.A.renderSource(l,d,o,c),{theme:h}=(0,a.u)();return v?(0,f.FD)("div",{css:(0,n.AH)({display:"flex",alignItems:"center",gap:h.spacing.sm,paddingTop:h.spacing.sm,paddingBottom:h.spacing.sm,flexWrap:"wrap"},""),className:u,children:[(0,f.Y)(Te.m,{sourceType:null===(s=l[m.A.sourceTypeTag])||void 0===s?void 0:s.value,css:(0,n.AH)({color:h.colors.actionPrimaryBackgroundDefault},"")}),v," ",c&&(0,f.Y)(i.paO,{title:c,children:(0,f.Y)(i.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewsourcebox.tsx_48",css:be,children:(0,f.FD)("div",{css:Ee,children:[(0,f.Y)(i.OKA,{})," ",c]})})}),p&&(0,f.FD)(a.av.Root,{componentId:"mlflow.run_details.overview.source.commit_hash_popover",children:[(0,f.Y)(a.av.Trigger,{asChild:!0,children:(0,f.Y)(i.vwO,{componentId:"mlflow.run_details.overview.source.commit_hash",css:Ue,children:(0,f.FD)("div",{css:(0,n.AH)({display:"flex",gap:h.spacing.xs,whiteSpace:"nowrap",alignContent:"center"},""),children:[(0,f.Y)(i.fSU,{}),p.slice(0,7)]})})}),(0,f.FD)(a.av.Content,{align:"start",children:[(0,f.Y)(a.av.Arrow,{}),(0,f.FD)("div",{css:(0,n.AH)({display:"flex",gap:h.spacing.xs,alignItems:"center"},""),children:[p,(0,f.Y)(Ce.i,{showLabel:!1,size:"small",type:"tertiary",copyText:p,icon:(0,f.Y)(i.TdU,{})})]})]})]})]}):(0,f.Y)(a.T.Hint,{children:"\u2014"})};var Fe=r(14830),Le=r(31179),Ne=r(67212),Pe=r(93358);var Oe=r(46398),Be=r(92677),He=r(36506),Ve=r(30706);const ze=[Be.I7.RelationshipType,Be.I7.Step,Be.I7.Name,Be.I7.Status,Be.I7.CreationTime,Be.I7.RegisteredModels,Be.I7.Dataset];var Ke={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"},je={name:"g030ms",styles:"display:flex;flex-direction:row;justify-content:space-between;align-items:center"},$e={name:"ozd7xs",styles:"flex-shrink:0"};const We=e=>{let{inputs:t,outputs:r,runInfo:s}=e;const{theme:o}=(0,a.u)(),{models:l,isLoading:u,errors:c}=((e,t,r)=>{var n,i;const a=(0,R.compact)((0,R.uniq)(null===e||void 0===e||null===(n=e.modelInputs)||void 0===n?void 0:n.map((e=>e.modelId)))),s=(0,R.compact)((0,R.uniq)(null===t||void 0===t||null===(i=t.modelOutputs)||void 0===i?void 0:i.map((e=>e.modelId)))),o=(0,Pe.v)(a),l=(0,Pe.v)(s),u=(0,d.useMemo)((()=>o.map((e=>{var t,r;if(null!==(t=e.data)&&void 0!==t&&t.model)return{...null===(r=e.data)||void 0===r?void 0:r.model,direction:"input"}}))),[o]),c=(0,d.useMemo)((()=>l.map((e=>{var r,n,i,a;if(null===(r=e.data)||void 0===r||!r.model)return;const s=null===t||void 0===t||null===(n=t.modelOutputs)||void 0===n?void 0:n.find((t=>{var r,n,i;let{modelId:a}=t;return a===(null===(r=e.data)||void 0===r||null===(n=r.model)||void 0===n||null===(i=n.info)||void 0===i?void 0:i.model_id)}));return{...null===(i=e.data)||void 0===i?void 0:i.model,direction:"output",step:null!==(a=null===s||void 0===s?void 0:s.step)&&void 0!==a?a:void 0}}))),[l,null===t||void 0===t?void 0:t.modelOutputs]);return{models:(0,d.useMemo)((()=>{var e,t;return null!==(e=(0,R.uniqBy)((0,R.compact)([...u,...c]).map((t=null===r||void 0===r?void 0:r.runUuid,e=>{var r;return null!==(r=e.data)&&void 0!==r&&r.metrics?{...e,data:{...e.data,metrics:e.data.metrics.filter((e=>!t||e.run_id===t))}}:e})),(e=>{var t;return null===(t=e.info)||void 0===t?void 0:t.model_id})))&&void 0!==e?e:[]}),[u,c,r]),errors:[...o,...l].map((e=>e.error)).filter(Boolean),isLoading:[...o,...l].some((e=>e.isLoading))}})(t,r,s),[m,g]=(0,d.useState)({}),{columnDefs:p}=(0,Be.ih)({loggedModels:l,columnVisibility:m,disablePinnedColumns:!0,disableOrderBy:!0,supportedAttributeColumnKeys:ze}),v=(0,d.useMemo)((()=>(0,R.first)(c)),[c]);return(0,f.FD)("div",{css:Ke,children:[(0,f.FD)("div",{css:je,children:[(0,f.Y)(a.T.Title,{level:4,css:$e,children:(0,f.Y)(x.A,{id:"km8vtM",defaultMessage:"Logged models ({length})",values:{length:l.length}})}),(0,f.Y)(Ve.$,{columnDefs:p,onUpdateColumns:g,columnVisibility:m,customTrigger:(0,f.Y)(a.B,{componentId:"mlflow.logged_model.list.columns",icon:(0,f.Y)(i.jng,{})})})]}),(0,f.Y)(N.S,{size:"sm",shrinks:!1}),(0,f.FD)("div",{css:(0,n.AH)({padding:o.spacing.sm,border:`1px solid ${o.colors.border}`,borderRadius:o.general.borderRadiusBase,display:"flex",flexDirection:"column",flex:1,overflow:"hidden"},""),children:[v instanceof Error&&v.message&&(0,f.FD)(f.FK,{children:[(0,f.Y)(i.FcD,{type:"error",message:v.message,closable:!1,componentId:"mlflow.run_page.logged_model.list.error"}),(0,f.Y)(N.S,{size:"sm",shrinks:!1})]}),(0,f.Y)(He.Xs,{children:(0,f.Y)(Oe._,{columnDefs:p,loggedModels:l,columnVisibility:m,isLoading:u,isLoadingMore:!1,moreResultsAvailable:!1,disableLoadMore:!0,css:Ge(o),displayShowExampleButton:!1})})]})]})},Ge=e=>({"&.ag-theme-balham":{"--ag-border-color":e.colors.border,"--ag-row-border-color":e.colors.border,"--ag-foreground-color":e.colors.textPrimary,"--ag-background-color":"transparent","--ag-odd-row-background-color":"transparent","--ag-row-hover-color":e.colors.actionDefaultBackgroundHover,"--ag-selected-row-background-color":e.colors.actionDefaultBackgroundPress,"--ag-header-foreground-color":e.colors.textPrimary,"--ag-header-background-color":e.colors.backgroundPrimary,"--ag-modal-overlay-background-color":e.colors.overlayOverlay,".ag-header-row.ag-header-row-column-group":{"--ag-header-foreground-color":e.colors.textPrimary},borderTop:0,fontSize:e.typography.fontSizeBase,".ag-center-cols-viewport":{...(0,a.E)(e,{orientation:"horizontal"})}}});var Ze=r(65287),Je=r(81313);var Xe={name:"ti75j2",styles:"margin:0"};const qe=e=>{let{tags:t,runInfo:r,datasets:s}=e;const[o,l]=(0,d.useState)(null),[u,c]=(0,d.useState)(!1),{theme:m}=(0,a.u)();if(!s||!s.length)return null;return(0,f.FD)(f.FK,{children:[(0,f.Y)(i.nEg,{children:s.map((e=>(0,f.Y)(a.T.Link,{componentId:"mlflow.run_details.datasets_box.dataset_link",css:(0,n.AH)({textAlign:"left",".anticon":{fontSize:m.general.iconFontSize}},""),onClick:()=>(e=>{var n,i,a;l({datasetWithTags:e,runData:{experimentId:null!==(n=r.experimentId)&&void 0!==n?n:void 0,runUuid:null!==(i=r.runUuid)&&void 0!==i?i:"",runName:null!==(a=r.runName)&&void 0!==a?a:void 0,datasets:s,tags:t}}),c(!0)})(e),children:(0,f.Y)(ne.E,{datasetWithTags:e,displayTextAsLink:!0,css:Xe})})))}),o&&(0,f.Y)(ie.O,{isOpen:u,setIsOpen:c,selectedDatasetWithRun:o,setSelectedDatasetWithRun:l})]})};var Qe=function(e){return e.DETAILS="DETAILS",e.DATASETS="DATASETS",e.TAGS="TAGS",e.REGISTERED_MODELS="REGISTERED_MODELS",e}(Qe||{});const et=()=>(0,f.Y)(a.T.Hint,{children:"\u2014"});var tt={name:"67z2yy",styles:"flex:1;align-self:flex-start"},rt={name:"1ctdo4k",styles:"min-height:360px;max-height:760px;overflow:hidden;display:flex"};const nt=e=>{let{runUuid:t,onRunDataUpdated:r,tags:i,runInfo:s,datasets:o,params:l,latestMetrics:u,runInputs:g,runOutputs:p,registeredModelVersionSummaries:v,loggedModelsV3:h=[],isLoadingLoggedModels:Y=!1}=e;const{theme:_}=(0,a.u)(),{usingUnifiedDetailsLayout:S}=(0,G.z)(),{search:M}=(0,c.zy)(),A=(0,L.A)(),w=(0,d.useMemo)((()=>m.A.getLoggedModelsFromTags(i)),[i]),D=i[P.Ol],C=!(0,R.isEmpty)(null===g||void 0===g?void 0:g.modelInputs)||!(0,R.isEmpty)(null===p||void 0===p?void 0:p.modelOutputs),T=!(0,y.Ok)()||!C,b=(null===w||void 0===w?void 0:w.length)>0||(null===h||void 0===h?void 0:h.length)>0,E=(0,Le.b)({loggedModels:h}),U=(0,R.uniqBy)([...v,...E],(e=>null===e||void 0===e?void 0:e.link)),k=(e=>{var t,r,i;let{runUuid:s,runInfo:o,tags:l,onTagsUpdated:u,datasets:g,shouldRenderLoggedModelsBox:p,loggedModelsV3:v,registeredModelVersionSummaries:h}=e;const Y=(0,L.A)(),{theme:x}=(0,a.u)(),{search:_}=(0,c.zy)(),y=(0,d.useMemo)((()=>m.A.getLoggedModelsFromTags(l)),[l]),S=l[P.Ol],M=o&&(0,f.FD)(f.FK,{children:[(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"PP5HZf",defaultMessage:"Created at"}),value:o.startTime?m.A.formatTimestamp(o.startTime,Y):(0,f.Y)(Je.bw,{})}),(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"7KKvmS",defaultMessage:"Created by"}),value:(0,f.Y)(H,{runInfo:o,tags:l})}),(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"ffema5",defaultMessage:"Experiment ID"}),value:(0,f.Y)(Ne.t,{value:null!==(t=null===o||void 0===o?void 0:o.experimentId)&&void 0!==t?t:"",element:null!==o&&void 0!==o&&o.experimentId?(0,f.Y)(c.N_,{to:I.h.getExperimentPageRoute(o.experimentId),children:null===o||void 0===o?void 0:o.experimentId}):void 0})}),(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"vxY1CI",defaultMessage:"Status"}),value:(0,f.Y)(B,{status:o.status})}),(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"JYVQuV",defaultMessage:"Run ID"}),value:(0,f.Y)(Ne.t,{value:null!==(r=o.runUuid)&&void 0!==r?r:""})}),(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"ZoIjun",defaultMessage:"Duration"}),value:m.A.getDuration(o.startTime,o.endTime)}),S&&(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"zdYXP8",defaultMessage:"Parent run"}),value:(0,f.Y)(ce,{parentRunUuid:S.value})}),(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"WmDiY9",defaultMessage:"Source"}),value:(0,f.Y)(ke,{tags:l,search:_,runUuid:s,css:(0,n.AH)({paddingTop:x.spacing.xs,paddingBottom:x.spacing.xs},"")})}),p&&(0,f.Y)(Je.wB,{keyValue:Y.formatMessage({id:"mjNFJt",defaultMessage:"Logged models"}),value:(0,f.Y)(De,{runInfo:o,loggedModels:y,loggedModelsV3:v})})]});return[{id:Qe.DETAILS,title:Y.formatMessage({id:"QGiy9C",defaultMessage:"About this run"}),content:M},{id:Qe.DATASETS,title:Y.formatMessage({id:"yrsFOP",defaultMessage:"Datasets"}),content:null!==g&&void 0!==g&&g.length?(0,f.Y)(qe,{tags:l,runInfo:o,datasets:g}):(0,f.Y)(Je.bw,{})},{id:Qe.TAGS,title:Y.formatMessage({id:"arFT1W",defaultMessage:"Tags"}),content:(0,f.Y)(he,{runUuid:null!==(i=o.runUuid)&&void 0!==i?i:"",tags:l,onTagsUpdated:u})},{id:Qe.REGISTERED_MODELS,title:Y.formatMessage({id:"HnGOwk",defaultMessage:"Registered models"}),content:(null===h||void 0===h?void 0:h.length)>0?(0,f.Y)(ye,{registeredModelVersionSummaries:h}):(0,f.Y)(Je.bw,{})}]})({runUuid:t,runInfo:s,tags:i,onTagsUpdated:r,datasets:o,loggedModelsV3:h,shouldRenderLoggedModelsBox:T,registeredModelVersionSummaries:U}),F=S;return(0,f.FD)(Ze.a,{css:tt,usingSidebarLayout:F,secondarySections:k,children:[(0,f.Y)(xe,{runUuid:t,tags:i,onDescriptionChanged:r}),!F&&(0,f.FD)(f.FK,{children:[(0,f.Y)(a.T.Title,{level:4,children:(0,f.Y)(x.A,{id:"Isaf66",defaultMessage:"Details"})}),(()=>{var e,n,l;return(0,f.FD)(Fe.N,{children:[(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"PP5HZf",defaultMessage:"Created at"}),value:s.startTime?m.A.formatTimestamp(s.startTime,A):(0,f.Y)(et,{})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"7KKvmS",defaultMessage:"Created by"}),value:(0,f.Y)(H,{runInfo:s,tags:i})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"ffema5",defaultMessage:"Experiment ID"}),value:(0,f.Y)(Ne.t,{value:null!==(e=null===s||void 0===s?void 0:s.experimentId)&&void 0!==e?e:""})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"vxY1CI",defaultMessage:"Status"}),value:(0,f.Y)(B,{status:s.status})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"JYVQuV",defaultMessage:"Run ID"}),value:(0,f.Y)(Ne.t,{value:null!==(n=s.runUuid)&&void 0!==n?n:""})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"ZoIjun",defaultMessage:"Duration"}),value:m.A.getDuration(s.startTime,s.endTime)}),D&&(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"zdYXP8",defaultMessage:"Parent run"}),value:(0,f.Y)(ce,{parentRunUuid:D.value})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"fwp4JP",defaultMessage:"Datasets used"}),value:null!==o&&void 0!==o&&o.length?(0,f.Y)(le,{tags:i,runInfo:s,datasets:o}):(0,f.Y)(et,{})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"rs7Iic",defaultMessage:"Tags"}),value:(0,f.Y)(he,{runUuid:null!==(l=s.runUuid)&&void 0!==l?l:"",tags:i,onTagsUpdated:r})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"WmDiY9",defaultMessage:"Source"}),value:(0,f.Y)(ke,{tags:i,search:M,runUuid:t})}),T&&(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"mjNFJt",defaultMessage:"Logged models"}),value:Y?(0,f.Y)(a.S,{}):b?(0,f.Y)(De,{runInfo:s,loggedModels:w,loggedModelsV3:h}):(0,f.Y)(et,{})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"BCpX6U",defaultMessage:"Registered models"}),value:(null===U||void 0===U?void 0:U.length)>0?(0,f.Y)(ye,{registeredModelVersionSummaries:U}):(0,f.Y)(et,{})}),(0,f.Y)(_e.Z,{title:(0,f.Y)(x.A,{id:"UCQwvo",defaultMessage:"Registered prompts"}),value:(0,f.Y)(we,{runUuid:t})})]})})()]}),(0,f.FD)("div",{css:[F?{flexDirection:"column"}:{minHeight:360,maxHeight:760},{display:"flex",gap:_.spacing.lg,overflow:"hidden"},""],children:[(0,f.Y)(re,{latestMetrics:u,runInfo:s}),(0,f.Y)(V.y,{params:l})]}),(0,y.Ok)()&&C&&(0,f.FD)(f.FK,{children:[(0,f.Y)(N.S,{}),(0,f.Y)("div",{css:rt,children:(0,f.Y)(We,{inputs:g,outputs:p,runInfo:s})})]}),!F&&(0,f.Y)(N.S,{})]})};var it=r(23275),at=r(52350),st=r(82214);function ot(e){let{runId:t}=e;return(0,f.Y)(st.E,{statusCode:404,subMessage:`Run ID ${t} does not exist`,fallbackHomePageReactRoute:I.h.rootRoute})}var lt=r(47664),dt=r(64558),ut=r(33946),ct=r(75627),mt=r(59397);const gt=e=>{var t;let{contextData:{metricsForRun:r},hoverData:n,chartData:{metricKey:i},isHovering:s,mode:o}=e;const l=(0,ct.bL)(n)?n.hoveredDataPoint:n,d=(0,L.A)();if(o===ct.QS.MultipleTracesWithScanline&&(0,ct.bL)(n)&&s)return(0,f.Y)(mt.G,{hoverData:n});if(null===l||void 0===l||!l.metricEntity)return null;const{timestamp:u,step:c,value:g}=l.metricEntity,p=(null===r||void 0===r||null===(t=r[i])||void 0===t?void 0:t.length)>1,v=(0,j.bw)(i),h=p&&v&&!(0,R.isUndefined)(u),Y=p&&!v&&!(0,R.isUndefined)(c);return(0,f.FD)("div",{children:[Y&&(0,f.FD)("div",{css:pt.valueField,children:[(0,f.FD)("strong",{children:[(0,f.Y)(x.A,{id:"a02Pn6",defaultMessage:"Step"}),":"]})," ",c]}),h&&(0,f.FD)("div",{css:pt.valueField,children:[(0,f.FD)("strong",{children:[(0,f.Y)(x.A,{id:"faLIN+",defaultMessage:"Timestamp"}),":"]})," ",m.A.formatTimestamp(u,d)]}),g&&(0,f.FD)("div",{children:[(0,f.Y)(a.T.Text,{bold:!0,children:i}),(0,f.Y)(N.S,{size:"xs"}),(0,f.Y)(a.T.Text,{children:g})]})]})},pt={valueField:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}};var vt=r(30152),ht=r(36118),ft=r(39045),Yt=r(19415),xt=r(62758),_t=r(32614),It=r(63617),yt=r(4972),St=r(55999),Mt=r(688),At=r(71932),Rt=r(40029);var wt={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"},Dt={name:"1tc6xju",styles:"flex:1;overflow:auto"};const Ct=e=>{var t;let{runInfo:r,metricKeys:o,mode:l,chartUIState:u,updateChartsUIState:c,latestMetrics:m={},params:p={},tags:v={}}=e;const{theme:h}=(0,a.u)(),[Y,x]=(0,d.useState)(""),{formatMessage:_}=(0,L.A)(),{compareRunCharts:I,compareRunSections:S,chartsSearchFilter:M}=u,A=(0,d.useMemo)((()=>{var e;return null!==(e=null===I||void 0===I?void 0:I.filter((e=>!e.deleted)))&&void 0!==e?e:[]}),[I]),[w,D]=(0,d.useState)(void 0),C=(0,s.d4)((e=>{var t;let{entities:n}=e;return(0,R.mapValues)(n.sampledMetricsByRunUuid[null!==(t=r.runUuid)&&void 0!==t?t:""],(e=>(0,R.compact)((0,R.values)(e).map((e=>{let{metricsHistory:t}=e;return t})).flat())))})),T=(0,d.useMemo)((()=>({runInfo:r,metricsForRun:C})),[r,C]),{imagesByRunUuid:b}=(0,s.d4)((e=>({imagesByRunUuid:e.entities.imagesByRunUuid}))),[E,U]=(0,d.useState)(null),k=(0,xt.iO)(),F=(0,xt.cA)(),N=(0,xt.KP)(),P=(0,xt.Ez)(),O=(0,d.useMemo)((()=>{var e,t,n;return[{displayName:null!==(e=r.runName)&&void 0!==e?e:"",metrics:m,params:p,tags:v,images:b[null!==(t=r.runUuid)&&void 0!==t?t:""]||{},metricHistory:{},uuid:null!==(n=r.runUuid)&&void 0!==n?n:"",color:h.colors.primary,runInfo:r}]}),[r,m,p,v,b,h]);(0,d.useEffect)((()=>{if((!S||!I)&&O.length>0){const{resultChartSet:e,resultSectionSet:t}=vt.i$.getBaseChartAndSectionConfigs({runsData:O,enabledSectionNames:["model"===l?g.NN:g.rx],filterMetricNames:e=>{const t=e.startsWith(g.qt);return"model"===l?!t:t}});c((r=>({...r,compareRunCharts:e,compareRunSections:t})))}}),[I,S,O,l,c]),(0,d.useEffect)((()=>{c((e=>{if(!e.compareRunCharts||!e.compareRunSections)return e;const{resultChartSet:t,resultSectionSet:r,isResultUpdated:n}=vt.i$.updateChartAndSectionConfigs({compareRunCharts:e.compareRunCharts,compareRunSections:e.compareRunSections,runsData:O,isAccordionReordered:e.isAccordionReordered,filterMetricNames:e=>{const t=e.startsWith(g.qt);return"model"===l?!t:t}});return n?{...e,compareRunCharts:t,compareRunSections:r}:e}))}),[O,c,l]);const B=(0,yt.$)(),H=u.autoRefreshEnabled&&(0,y.vC)()&&B,V=Boolean(v[g.Cr]);return(0,St.L)({runUuids:[null!==(t=r.runUuid)&&void 0!==t?t:""],runUuidsIsActive:["RUNNING"===r.status],autoRefreshEnabled:H,enabled:V}),(0,f.FD)("div",{css:wt,children:[(0,f.FD)("div",{css:(0,n.AH)({paddingBottom:h.spacing.md,display:"flex",gap:h.spacing.sm,flex:"0 0 auto"},""),children:[(0,f.Y)(Rt.I,{chartsSearchFilter:M}),(0,y.vC)()&&(0,f.Y)(i.ffE,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_runviewmetricchartsv2.tsx_244",pressed:u.autoRefreshEnabled,onPressedChange:e=>{c((t=>({...t,autoRefreshEnabled:e})))},children:_({id:"zC/uKl",defaultMessage:"Auto-refresh"})}),(0,f.Y)(Mt.f,{metricKeyList:o,globalLineChartConfig:u.globalLineChartConfig,updateUIState:c})]}),(0,f.Y)("div",{css:Dt,children:(0,f.Y)(ct.W,{contextData:T,component:gt,children:(0,f.Y)(At.c_,{visibleChartCards:A,children:(0,f.Y)(ft.J,{compareRunSections:S,compareRunCharts:A,reorderCharts:k,insertCharts:F,chartData:O,startEditChart:e=>U(e),removeChart:N,addNewChartCard:e=>t=>U(vt.i$.getEmptyChartCardByType(t,!1,void 0,e)),search:null!==M&&void 0!==M?M:"",supportedChartTypes:[vt.zL.LINE,vt.zL.BAR,vt.zL.IMAGE],setFullScreenChart:D,autoRefreshEnabled:H,globalLineChartConfig:u.globalLineChartConfig,groupBy:null})})})}),E&&(0,f.Y)(Yt.z,{chartRunData:O,metricKeyList:o,paramKeyList:[],config:E,onSubmit:e=>{P(e),U(null)},onCancel:()=>U(null),groupBy:null,supportedChartTypes:[vt.zL.LINE,vt.zL.BAR,vt.zL.IMAGE],globalLineChartConfig:u.globalLineChartConfig}),(0,f.Y)(It._,{fullScreenChart:w,onCancel:()=>D(void 0),chartData:O,tooltipContextValue:T,tooltipComponent:gt,autoRefreshEnabled:H,groupBy:null})]})},Tt=e=>{const t=`${e.runInfo.runUuid}-${e.mode}`,r=(0,d.useMemo)((()=>_t.A.getStoreForComponent("RunPage",t)),[t]),[n,i]=(0,d.useState)((()=>{const e={isAccordionReordered:!1,compareRunCharts:void 0,compareRunSections:void 0,autoRefreshEnabled:(0,y.vC)(),globalLineChartConfig:{xAxisKey:ht.fj.STEP,lineSmoothness:0,selectedXAxisMetricKey:""}};try{const t=r.getItem("chartUIState");return t?JSON.parse(t):e}catch{return e}}));return(0,d.useEffect)((()=>{r.setItem("chartUIState",JSON.stringify(n))}),[n,r]),(0,f.Y)(xt.oB,{updateChartsUIState:i,children:(0,f.Y)(Ct,{...e,chartUIState:n,updateChartsUIState:i})})};var bt=r(91089);const Et=[r(80171).se.runName];var Ut={name:"fxp7t8",styles:"flex:1;min-width:0"};const kt=e=>{let{experimentId:t,runUuid:r}=e;const n=(0,d.useMemo)((()=>[t]),[t]);return(0,f.Y)("div",{css:Ut,children:(0,f.Y)(bt.O,{experimentIds:n,runUuid:r,disabledColumns:Et})})};var Ft=r(26626),Lt=r(63609);const Nt=()=>(0,f.FD)(u.L,{children:[(0,f.Y)(i.oud,{loading:!0,label:(0,f.Y)(x.A,{id:"ea5zBl",defaultMessage:"Run page loading"})}),[...Array(3).keys()].map((e=>(0,f.Y)(i.I_K,{seed:`s-${e}`},e)))]}),Pt=()=>{var e,t;const{runUuid:r,experimentId:o}=(0,c.g)(),v=(0,c.Zp)(),{theme:_}=(0,a.u)(),[M,A]=(0,d.useState)(!1),[R,w]=(0,d.useState)(!1);l()(r,"[RunPage] Run UUID route param not provided"),l()(o,"[RunPage] Experiment ID route param not provided");const{experiment:D,error:C,latestMetrics:T,loading:b,params:E,refetchRun:U,runInfo:k,tags:L,experimentFetchError:N,runFetchError:P,apiError:O,datasets:B,runInputs:H,runOutputs:V,registeredModelVersionSummaries:z}=(0,it.g)({experimentId:o,runUuid:r}),[K,$]=(0,d.useMemo)((()=>T?[Object.keys(T).filter((e=>!(0,j.bw)(e))),Object.keys(T).filter((e=>(0,j.bw)(e)))]:[[],[]]),[T]),{comparedExperimentIds:W=[],hasComparedExperimentsBefore:G=!1}=(0,s.d4)((e=>e.comparedExperiments||{})),Z=S(),{models:J,isLoading:X}=((e,t)=>{const{data:r,isLoading:n}=(0,Lt.e)({experimentIds:[e]},{enabled:(0,y.Dz)()});return{models:(0,d.useMemo)((()=>{var e;return null!==(e=null===r||void 0===r?void 0:r.filter((e=>{var r;return(null===(r=e.info)||void 0===r?void 0:r.source_run_id)===t})))&&void 0!==e?e:[]}),[r,t]),isLoading:(0,y.Dz)()&&n}})(o,r),q=(0,h.U)(`(min-width: ${_.responsive.breakpoints.sm}px)`),Q=b&&(!k||!D);return P instanceof at.s&&P.getErrorCode()===lt.tG.RESOURCE_DOES_NOT_EXIST||(null===O||void 0===O?void 0:O.code)===lt.tG.RESOURCE_DOES_NOT_EXIST||C&&(0,Ft.b)(C).match(/not found$/)?(0,f.Y)(ot,{runId:r}):N instanceof at.s&&N.getErrorCode()===lt.tG.RESOURCE_DOES_NOT_EXIST?(0,f.Y)(dt.A,{}):P||N?null:(0,y.wD)()&&(C||O)?(0,f.Y)("div",{css:(0,n.AH)({marginTop:_.spacing.lg},""),children:(0,f.Y)(i.SvL,{title:(0,f.Y)(x.A,{id:"XaC3qF",defaultMessage:"Can't load run details"}),description:(0,Ft.b)(null!==O&&void 0!==O?O:C),image:(0,f.Y)(a.j,{})})}):!Q&&k&&D?(0,f.FD)(f.FK,{children:[(0,f.FD)(u.L,{usesFullHeight:q,children:[(0,f.Y)(F,{comparedExperimentIds:W,experiment:D,handleRenameRunClick:()=>A(!0),handleDeleteRunClick:()=>w(!0),hasComparedExperimentsBefore:G,runDisplayName:m.A.getRunDisplayName(k,r),runTags:L,runParams:E,runUuid:r,artifactRootUri:null!==(e=null===k||void 0===k?void 0:k.artifactUri)&&void 0!==e?e:void 0,registeredModelVersionSummaries:z,isLoading:b||X}),(0,f.Y)("div",{css:(0,n.AH)({flex:1,overflow:"auto",marginBottom:_.spacing.sm,display:"flex"},""),children:(()=>{var e;if(!k)return null;switch(Z){case g.N3.MODEL_METRIC_CHARTS:return(0,f.Y)(Tt,{mode:"model",metricKeys:K,runInfo:k,latestMetrics:T,tags:L,params:E},"model");case g.N3.SYSTEM_METRIC_CHARTS:return(0,f.Y)(Tt,{mode:"system",metricKeys:$,runInfo:k,latestMetrics:T,tags:L,params:E},"system");case g.N3.ARTIFACTS:return(0,f.Y)(Y,{runUuid:r,runTags:L,runOutputs:V,experimentId:o,artifactUri:null!==(e=k.artifactUri)&&void 0!==e?e:void 0});case g.N3.TRACES:if((0,y.oX)())return(0,f.Y)(kt,{runUuid:r,runTags:L,experimentId:o})}return(0,f.Y)(nt,{runInfo:k,tags:L,params:E,latestMetrics:T,runUuid:r,onRunDataUpdated:U,runInputs:H,runOutputs:V,datasets:B,registeredModelVersionSummaries:z,loggedModelsV3:J,isLoadingLoggedModels:X})})()})]}),(0,f.Y)(p.j,{runUuid:r,onClose:()=>A(!1),runName:null!==(t=k.runName)&&void 0!==t?t:"",isOpen:M,onSuccess:U}),(0,f.Y)(ut.A,{selectedRunIds:[r],onClose:()=>w(!1),isOpen:R,onSuccess:()=>{v(I.h.getExperimentPageRoute(o))}})]}):(0,f.Y)(Nt,{})};var Ot=Pt},48588:function(e,t,r){r.d(t,{L:function(){return s}});r(31014);var n=r(48012),i=r(15579),a=r(50111);function s(e){const{usesFullHeight:t,...r}=e;return(0,a.FD)(n.ffj,{css:t?o.useFullHeightLayout:o.wrapper,children:[(0,a.Y)(i.S,{css:o.fixedSpacer}),t?e.children:(0,a.Y)("div",{...r,css:o.container})]})}s.defaultProps={usesFullHeight:!1};const o={useFullHeightLayout:{height:"calc(100% - 60px)",display:"flex",flexDirection:"column","&:last-child":{flexGrow:1}},wrapper:{flex:1},fixedSpacer:{flexShrink:0},container:{width:"100%",flexGrow:1,paddingBottom:24}}},64558:function(e,t,r){var n=r(31014),i=r(50111);class a extends n.Component{render(){return(0,i.Y)("div",{children:"Resource not found."})}}t.A=a},82832:function(e,t,r){r.d(t,{M:function(){return o}});var n=r(39416),i=r(53962),a=r(84565);const s=async e=>{let{reject:t,response:r,err:i}=e;const a=(0,n.a$)(r),s=a instanceof n.Bk?i:a;if(r)try{var o;const e=null===(o=await r.json())||void 0===o?void 0:o.message;e&&(s.message=e)}catch{}t(s)},o={listRegisteredPrompts:(e,t)=>{const r=new URLSearchParams;let n=`tags.\`${a.PS}\` = '${a.pY}'`;e&&(n=`${n} AND name ILIKE '%${e}%'`),t&&r.append("page_token",t),r.append("filter",n);const o=["ajax-api/2.0/mlflow/registered-models/search",r.toString()].join("?");return(0,i.AC)({relativeUrl:o,error:s})},setRegisteredPromptTag:(e,t,r)=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/set-tag",method:"POST",body:JSON.stringify({key:t,value:r,name:e}),error:s}),deleteRegisteredPromptTag:(e,t)=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete-tag",method:"DELETE",body:JSON.stringify({key:t,name:e}),error:s}),createRegisteredPrompt:e=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/create",method:"POST",body:JSON.stringify({name:e,tags:[{key:a.PS,value:a.pY}]}),error:s}),createRegisteredPromptVersion:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;return(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/create",method:"POST",body:JSON.stringify({name:e,description:r,source:"dummy-source",tags:[{key:a.PS,value:a.pY},...t]}),error:s})},setRegisteredPromptVersionTag:(e,t,r,n)=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/set-tag",method:"POST",body:JSON.stringify({key:r,value:n,name:e,version:t}),error:s}),deleteRegisteredPromptVersionTag:(e,t,r)=>{(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete-tag",method:"DELETE",body:JSON.stringify({key:r,name:e,version:t}),error:s})},getPromptDetails:e=>{const t=new URLSearchParams;t.append("name",e);const r=["ajax-api/2.0/mlflow/registered-models/get",t.toString()].join("?");return(0,i.AC)({relativeUrl:r,error:s})},getPromptVersions:e=>{const t=new URLSearchParams;t.append("filter",`name='${e}' AND tags.\`${a.PS}\` = '${a.pY}'`);const r=["ajax-api/2.0/mlflow/model-versions/search",t.toString()].join("?");return(0,i.AC)({relativeUrl:r,error:s})},getPromptVersionsForRun:e=>{const t=new URLSearchParams;t.append("filter",`tags.\`${a.PS}\` = '${a.pY}' AND tags.\`${a.xd}\` ILIKE "%${e}%"`);const r=["ajax-api/2.0/mlflow/model-versions/search",t.toString()].join("?");return(0,i.AC)({relativeUrl:r,error:s})},deleteRegisteredPrompt:e=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete",method:"DELETE",body:JSON.stringify({name:e}),error:s}),deleteRegisteredPromptVersion:(e,t)=>(0,i.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete",method:"DELETE",body:JSON.stringify({name:e,version:t}),error:s})}},84565:function(e,t,r){r.d(t,{Dh:function(){return n},Dp:function(){return o},PS:function(){return a},dv:function(){return l},pY:function(){return s},xd:function(){return i}});const n="mlflow.prompt.text",i="mlflow.prompt.run_ids",a="mlflow.prompt.is_prompt",s="true";let o=function(e){return e.TABLE="table",e.PREVIEW="preview",e.COMPARE="compare",e}({});const l=e=>{var t,r;return null===e||void 0===e||null===(t=e.tags)||void 0===t||null===(r=t.find((e=>e.key===n)))||void 0===r?void 0:r.value}}}]);
//# sourceMappingURL=6717.df8ed6c0.chunk.js.map