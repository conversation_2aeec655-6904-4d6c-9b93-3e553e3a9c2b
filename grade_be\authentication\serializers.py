from rest_framework import serializers
from .models import User, Organization
from django.contrib.auth.password_validation import validate_password
from .models import UserCredit
from .models import Organization


class RoleSerializer(serializers.Serializer):
    student = serializers.BooleanField(default=False)
    evaluator = serializers.BooleanField(default=False)
    qp_uploader = serializers.BooleanField(default=False)
    mentor = serializers.BooleanField(default=False)


class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True,
        required=True,
        validators=[validate_password],
        style={"input_type": "password"},
    )
    password2 = serializers.CharField(
        write_only=True, required=True, style={"input_type": "password"}
    )
    roles = RoleSerializer()

    class Meta:
        model = User
        fields = ("email", "username", "password", "password2", "roles")

    def validate(self, attrs):
        if attrs["password"] != attrs["password2"]:
            raise serializers.ValidationError(
                {"password": "Password fields didn't match."}
            )

        email = attrs.get("email", "").lower()
        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError(
                {"email": "A user with this email already exists."}
            )

        return attrs

    def create(self, validated_data):
        roles_data = validated_data.pop("roles")
        roles = [role for role, selected in roles_data.items() if selected]
        user = User.objects.create(
            email=validated_data["email"],
            username=validated_data["username"],
            is_active=True,
            # Automatically activate users (will require OTP verification)
            roles=roles,
            active_role=(
                roles[0] if roles else None
            ),  # Set first role as active
        )
        user.set_password(validated_data["password"])
        user.generate_otp()
        user.save()
        return user


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True, write_only=True)
    role = serializers.CharField(required=False)  # Optional for role switching


class GoogleLoginSerializer(serializers.Serializer):
    id_token = serializers.CharField(required=True)
    role = serializers.CharField(required=False)  # Add this line


class VerifyOTPSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    otp = serializers.CharField(required=True, max_length=6)


class ResendOTPSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)


class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)


class ResetPasswordSerializer(serializers.Serializer):
    token = serializers.CharField(required=True)
    password = serializers.CharField(
        required=True,
        write_only=True,
        validators=[validate_password],
        style={"input_type": "password"},
    )
    password2 = serializers.CharField(
        required=True, write_only=True, style={"input_type": "password"}
    )

    def validate(self, attrs):
        if attrs["password"] != attrs["password2"]:
            raise serializers.ValidationError(
                {"password": "Password fields didn't match."}
            )
        return attrs


class UserCreditSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserCredit
        fields = ["free_credit", "paid_credit", "total_credit", "last_updated"]


class CreateRazorpayOrderSerializer(serializers.Serializer):
    amount = serializers.DecimalField(
        max_digits=10, decimal_places=7, min_value=1
    )
    currency = serializers.CharField(max_length=3, default="USD")


class VerifyPaymentSerializer(serializers.Serializer):
    razorpay_payment_id = serializers.CharField()
    razorpay_order_id = serializers.CharField()
    razorpay_signature = serializers.CharField()
    amount = serializers.DecimalField(max_digits=10, decimal_places=7)
    currency = serializers.CharField(max_length=3)


class GoogleLoginSerializer(serializers.Serializer):
    """Serializer for Google login (shared between users and organizations)"""

    id_token = serializers.CharField(required=True)


class OrganizationRegistrationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = [
            "name",
            "email",
            "address",
            "phone_number",
            "registration_date",
            "registration_proof",
            "description",
        ]
        extra_kwargs = {"registration_proof": {"required": True}}

    def create(self, validated_data):
        organization = Organization.objects.create(**validated_data)
        return organization


class OrganizationVerificationSerializer(serializers.ModelSerializer):
    verification_notes = serializers.CharField(required=True)

    class Meta:
        model = Organization
        fields = ["is_verified", "verification_notes"]
        read_only_fields = ["is_verified"]


class OrganizationRejectionSerializer(serializers.ModelSerializer):
    verification_notes = serializers.CharField(required=True)

    class Meta:
        model = Organization
        fields = ["verification_notes"]


class OrganizationListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = [
            "id",
            "name",
            "email",
            "status",
            "is_verified",
            "created_at",
            "verified_at",
            "verification_notes",
            "address",
            "phone_number",
            "registration_date",
            "description",
        ]
        read_only_fields = fields
