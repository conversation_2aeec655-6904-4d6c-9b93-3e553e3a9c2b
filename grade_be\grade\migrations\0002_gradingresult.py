# Generated by Django 5.1.9 on 2025-05-28 09:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("grade", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="GradingResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("user_id", models.CharField(max_length=100)),
                ("total_score", models.FloatField(default=0)),
                ("max_possible_score", models.FloatField(default=0)),
                ("percentage", models.FloatField(default=0)),
                (
                    "result_json_path",
                    models.CharField(blank=True, max_length=500, null=True),
                ),
                ("grading_processed", models.BooleanField(default=False)),
                ("grading_error", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("graded_at", models.DateTimeField(blank=True, null=True)),
                ("questions_count", models.IntegerField(default=0)),
                ("diagrams_count", models.IntegerField(default=0)),
                (
                    "answer_upload",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="grading_result",
                        to="grade.answerupload",
                    ),
                ),
            ],
            options={
                "db_table": "grading_results",
                "indexes": [
                    models.Index(
                        fields=["user_id"],
                        name="grading_res_user_id_d0d380_idx",
                    ),
                    models.Index(
                        fields=["answer_upload"],
                        name="grading_res_answer__5dff05_idx",
                    ),
                    models.Index(
                        fields=["created_at"],
                        name="grading_res_created_f30862_idx",
                    ),
                ],
            },
        ),
    ]
