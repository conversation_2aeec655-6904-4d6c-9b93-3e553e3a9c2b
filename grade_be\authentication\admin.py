"""
This module handles Django administration settings and authentication-related imports.
"""

from django.contrib import admin
from django.contrib.auth import admin as auth_admin
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from .models import PaymentTransaction, UserCredit, UsageHistory, Organization
from django.contrib.auth.admin import UserAdmin

User = get_user_model()


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "email",
        "status",
        "is_verified",
        "created_at",
        "verified_at",
    )
    list_filter = ("status", "is_verified", "created_at", "verified_at")
    search_fields = ("name", "email", "address", "phone_number")
    readonly_fields = ("created_at", "updated_at", "verified_at")
    fieldsets = (
        ("Basic Information", {"fields": ("name", "email", "password")}),
        (
            "Contact Details",
            {"fields": ("address", "phone_number", "description")},
        ),
        (
            "Registration Details",
            {"fields": ("registration_date", "registration_proof")},
        ),
        (
            "Verification Status",
            {
                "fields": (
                    "status",
                    "is_verified",
                    "verification_notes",
                    "verified_by",
                    "verified_at",
                )
            },
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Only hash password on creation
            obj.password = (
                obj.password
            )  # Password will be hashed in model's save method
        super().save_model(request, obj, form, change)


class UserCreditInline(admin.StackedInline):
    model = UserCredit
    can_delete = False
    verbose_name_plural = "User Credit"
    fields = ("free_credit", "paid_credit", "total_credit", "last_updated")
    readonly_fields = ("total_credit", "last_updated")


class PaymentTransactionInline(admin.TabularInline):
    model = PaymentTransaction
    extra = 0
    readonly_fields = ("created_at", "updated_at")
    fields = (
        "amount",
        "currency",
        "status",
        "created_at",
        "razorpay_order_id",
    )
    ordering = ("-created_at",)


@admin.register(User)
class UserAdmin(auth_admin.UserAdmin):
    """
    Custom User admin configuration.
    """

    inlines = (UserCreditInline, PaymentTransactionInline)

    fieldsets = (
        (None, {"fields": ("username", "password")}),
        (
            _("Personal info"),
            {"fields": ("email", "full_name", "phone_number")},
        ),
        (
            _("Organization Information"),
            {
                "fields": (
                    "organization",
                    "role_org",
                ),
            },
        ),
        (
            _("Address information"),
            {
                "fields": (
                    "country",
                    "state",
                    "city",
                    "address_line1",
                    "address_line2",
                ),
            },
        ),
        (
            _("Security Information"),
            {
                "fields": (
                    "otp",
                    "otp_created_at",
                    "password_reset_token",
                    "password_reset_expires",
                    "failed_login_attempts",
                    "account_locked_until",
                ),
            },
        ),
        (
            _("Permissions and Status"),
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_superuser",
                    "is_allowed",
                    "is_premium",
                    "is_admin",
                    "is_profile_completed",
                    "is_email_verified",
                    "groups",
                    "user_permissions",
                ),
            },
        ),
        (_("Important dates"), {"fields": ("last_login", "date_joined")}),
    )

    list_display = [
        "id",
        "email",
        "username",
        "full_name",
        "organization",
        "role_org",
        "country",
        "state",
        "city",
        "address_line1",
        "address_line2",
        "roles",
        "active_role",
        "phone_number",
        "is_premium",
        "is_allowed",
        "is_profile_completed",
        "is_email_verified",
        "is_admin",
        "is_staff",
        "last_login",
    ]

    list_filter = [
        "is_premium",
        "is_allowed",
        "is_profile_completed",
        "is_staff",
        "is_superuser",
        "is_email_verified",
        "country",
        "city",
        "is_admin",
        "organization",
        "role_org",
    ]

    search_fields = [
        "username",
        "email",
        "id",
        "full_name",
        "phone_number",
        "organization__name",
    ]

    readonly_fields = [
        "otp_created_at",
        "password_reset_expires",
        "account_locked_until",
        "last_login",
        "date_joined",
    ]

    raw_id_fields = ["organization"]

    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": ("email", "username", "password1", "password2"),
            },
        ),
    )


@admin.register(PaymentTransaction)
class PaymentTransactionAdmin(admin.ModelAdmin):
    list_display = ("user", "amount", "currency", "status", "created_at")
    list_filter = ("status", "created_at", "currency")
    search_fields = ("user__email", "razorpay_payment_id", "razorpay_order_id")
    readonly_fields = ("created_at", "updated_at")
    fieldsets = (
        (
            "Transaction Details",
            {"fields": ("user", "amount", "currency", "status")},
        ),
        (
            "Razorpay Details",
            {
                "fields": (
                    "razorpay_payment_id",
                    "razorpay_order_id",
                    "razorpay_signature",
                    "razorpay_invoice_id",
                )
            },
        ),
        (
            "Timestamps",
            {"fields": ("created_at", "updated_at"), "classes": ("collapse",)},
        ),
    )


@admin.register(UserCredit)
class UserCreditAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "free_credit",
        "paid_credit",
        "total_credit",
        "last_updated",
    )
    list_filter = ("last_updated",)
    search_fields = ("user__email",)
    readonly_fields = ("last_updated",)


@admin.register(UsageHistory)
class UsageHistoryAdmin(admin.ModelAdmin):
    list_display = (
        "user",
        "service_type",
        "input_length",
        "cost",
        "timestamp",
    )
    list_filter = ("service_type", "timestamp")
    search_fields = ("user__email", "service_type")
    readonly_fields = ("timestamp",)
    fieldsets = (
        (
            "Usage Details",
            {"fields": ("user", "service_type", "input_length", "cost")},
        ),
        ("Timestamps", {"fields": ("timestamp",), "classes": ("collapse",)}),
    )
