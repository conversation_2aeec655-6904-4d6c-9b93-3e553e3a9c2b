from rest_framework.response import Response
from rest_framework.request import Request
import json
import logging
import random
import time
from sentence_transformers import SentenceTransformer, util
from typing import Dict, Any, List, Optional

from .models import Question, Company, Topic, TestCase
from .serializers import GenerateQuestionInputSerializer
from .prompts.question_prompts import build_question_generation_prompt
from .llm_utils import clean_generated_content
import asyncio


import os
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from centralised_llm.src.llm_manager import handle_request


logger = logging.getLogger(__name__)
model = SentenceTransformer('all-MiniLM-L6-v2')

def create_question_from_data(data: Dict[str, Any], topic_obj: Topic) -> Question:
    question = Question.objects.create(
        title=data.get("title", "Untitled"),
        description=data.get("description", ""),
        sample_input=data.get("sample_input", ""),
        sample_output=data.get("sample_output", ""),
        explanation=data.get("explanation", ""),
        constraints=data.get("constraints", ""),
        testcase_description=data.get("testcase_description", ""),
        difficulty=data.get("difficulty", "easy"),
        year_asked=data.get("year_asked", None),
    )
    question.topics.add(topic_obj)
    for topic_name in data.get("topics", []):
        t_obj, _ = Topic.objects.get_or_create(name=topic_name)
        question.topics.add(t_obj)
    for company_name in data.get("companies", []):
        c_obj, _ = Company.objects.get_or_create(name=company_name)
        question.companies.add(c_obj)
    return question

def generate_questions_logic(request: Request) -> Response:
    logger.info("Received POST request to generate questions.")
    serializer = GenerateQuestionInputSerializer(data=request.data)

    if not serializer.is_valid():
        logger.error("Invalid serializer input: %s", serializer.errors)
        return Response(serializer.errors, status=400)

    topic_name = serializer.validated_data['topic']
    difficulty = serializer.validated_data['difficulty']
    logger.info(f"Topic: {topic_name}, Difficulty: {difficulty}")

    existing_questions = Question.objects.filter(
        topics__name=topic_name,
        difficulty=difficulty
    ).values_list('title', flat=True)
    existing_titles = list(existing_questions)
    existing_embeddings = model.encode(existing_titles, convert_to_tensor=True) if existing_titles else None

    target_count = 5
    max_attempts = 5
    attempt = 0
    generated_questions = []
    new_titles = []
    topic_obj, _ = Topic.objects.get_or_create(name=topic_name)

    while len(generated_questions) < target_count and attempt < max_attempts:
        attempt += 1
        logger.info(f"Attempt {attempt}: Generating questions...")
        prompt = build_question_generation_prompt(topic_name, difficulty)
        content = None
        req1 = json.dumps({
                "model": "openai",  # e.g., "openai", "mistralai"
                "prompt": prompt,
                "parameters": {
                "temperature": 0.2,"model": "gpt-4.1-nano",
            },
            })
        try:
            content = safe_async_call(handle_request(req1))
        except Exception as e:
            logger.error(f"Exception while calling handle_request: {e}")
            continue

        if not content:
            logger.warning(" Model did not return any content.")
            continue

        try:
            if isinstance(content, dict) and 'response' in content:
                content = content['response']
            content = clean_generated_content(content)
            content = extract_json_from_response(content)  
            content = auto_fix_json(content)

            try:
                questions_json = json.loads(content)
                if not isinstance(questions_json, list):
                    raise ValueError("Expected a list of questions.")
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                logger.debug(f"Raw response content:\n{repr(content)}")
                continue

            if not isinstance(questions_json, list):
                raise ValueError("Expected a list of questions.")
        except Exception as e:
            logger.error(f"Failed to parse JSON: {e}")
            continue

        for q in questions_json:
            try:
                new_title = q.get("title", "").strip()
                if not new_title:
                    continue

                all_titles = existing_titles + new_titles
                if all_titles:
                    all_embeddings = model.encode(all_titles, convert_to_tensor=True)
                    new_embedding = model.encode(new_title, convert_to_tensor=True)
                    similarity_scores = util.cos_sim(new_embedding, all_embeddings)
                    if similarity_scores.max().item() > 0.85:
                        logger.info(f"Skipping duplicate question (semantic match): {new_title}")
                        continue

                sample_io = q.get("sample_io", [])
                sample_input = sample_io[0].get("input", "") if sample_io else ""
                sample_output = sample_io[0].get("output", "") if sample_io else ""

                question_data = {
                    "title": new_title,
                    "description": q.get("description", ""),
                    "sample_input": sample_input,
                    "sample_output": sample_output,
                    "explanation": q.get("explanation", ""),
                    "constraints": q.get("constraints", ""),
                    "testcase_description": q.get("testcase_description", ""),
                    "difficulty": difficulty,
                    "year_asked": q.get("year_asked", None),
                    "topics": q.get("topics", []),
                    "companies": q.get("companies_asked", []),
                }

                question = create_question_from_data(question_data, topic_obj)

                for sample in sample_io:
                    TestCase.objects.create(
                        question=question,
                        input_data=sample.get("input", ""),
                        expected_output=sample.get("output", ""),
                        is_sample=True,
                        test_type="public"
                    )

                hidden_tests = q.get("test_cases", [])
                for test in hidden_tests:
                    TestCase.objects.create(
                        question=question,
                        input_data=test.get("input", ""),
                        expected_output=test.get("output", ""),
                        is_sample=False,
                        test_type="hidden"
                    )

                generated_questions.append({
                    'id': question.id,
                    'title': question.title,
                    'description': question.description,
                    'sample_input': question.sample_input,
                    'sample_output': question.sample_output,
                    'companies_asked': question_data["companies"],
                    'year_asked': question.year_asked,
                    'difficulty': question.difficulty,
                })
                new_titles.append(new_title)

                if len(generated_questions) >= target_count:
                    break
            except Exception as e:
                logger.error(f"Failed to process question: {e}")

    if len(generated_questions) == 0:
        return Response({"error": "No valid questions were generated."}, status=500)

    return Response({
        "message": f"Successfully generated {len(generated_questions)} questions.",
        "questions": generated_questions
    }, status=201)


import re

def extract_json_from_response(content: str) -> str:

    # Remove any markdown formatting or prefix/suffix
    content = content.strip()
    content = re.sub(r"^```(?:json)?", "", content)
    content = re.sub(r"```$", "", content).strip()

    # Extract only the JSON array
    match = re.search(r"\[\s*{.*?}\s*.*?\]", content, re.DOTALL)
    if match:
        return match.group(0)

    # Fallback — return raw (may still be broken)
    return content
def auto_fix_json(content: str) -> str:
    """
    Attempts to auto-correct incomplete JSON arrays from LLM output.
    """
    content = content.strip()

    # Balance braces
    while content.count("{") > content.count("}"):
        content += "}"
    while content.count("[") > content.count("]"):
        content += "]"

    return content

def safe_async_call(coroutine):
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            return asyncio.ensure_future(coroutine)
        return loop.run_until_complete(coroutine)
    except RuntimeError:
        return asyncio.run(coroutine)