"""
Task definitions for the grade app.

Defines background or utility tasks for grading, OCR, or related features.
"""
# tasks.py
from celery import shared_task
from django.conf import settings
import os
import logging
from .models import AnswerUpload
from .ocr_processor import process_answer_ocr

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def process_ocr_task(self, answer_upload_id: int) -> dict:
    """
    Asynchronous OCR processing task.

    Args:
        self: The task instance (provided by Ce<PERSON><PERSON> when bind=True).
        answer_upload_id (int): The ID of the AnswerUpload object to process.

    Returns:
        dict: A dictionary with the result of the OCR processing, including success status, answer_upload_id, and message or error.
    """
    try:
        answer_upload = AnswerUpload.objects.get(id=answer_upload_id)

        # Create directories
        upload_id = str(answer_upload.id)
        output_base_dir = os.path.join(
            settings.MEDIA_ROOT, "output", upload_id
        )
        json_dir = os.path.join(output_base_dir, "json")
        images_dir = os.path.join(output_base_dir, "images")

        os.makedirs(json_dir, exist_ok=True)
        os.makedirs(images_dir, exist_ok=True)

        # Get the file path
        file_path = os.path.join(settings.MEDIA_ROOT, answer_upload.file.name)

        # Process OCR
        ocr_result = process_answer_ocr(
            file_path=file_path,
            output_json_dir=json_dir,
            output_images_dir=images_dir,
            user_id=answer_upload.user_id,
        )

        # Update the answer upload
        if ocr_result["success"]:
            answer_upload.ocr_processed = True
            answer_upload.ocr_json_path = ocr_result["json_path"]
            answer_upload.ocr_images_dir = ocr_result["images_dir"]
            answer_upload.roll_number = ocr_result.get("roll_number")
        else:
            answer_upload.ocr_processed = False
            answer_upload.ocr_error = ocr_result.get(
                "error", "OCR processing failed"
            )

        answer_upload.save()

        return {
            "success": ocr_result["success"],
            "answer_upload_id": answer_upload_id,
            "message": "OCR processing completed",
        }

    except Exception as e:
        logger.error(
            f"OCR task failed for upload {answer_upload_id}: {str(e)}"
        )
        # Update the record to indicate failure
        try:
            answer_upload = AnswerUpload.objects.get(id=answer_upload_id)
            answer_upload.ocr_processed = False
            answer_upload.ocr_error = str(e)
            answer_upload.save()
        except BaseException:
            pass

        return {
            "success": False,
            "answer_upload_id": answer_upload_id,
            "error": str(e),
        }
