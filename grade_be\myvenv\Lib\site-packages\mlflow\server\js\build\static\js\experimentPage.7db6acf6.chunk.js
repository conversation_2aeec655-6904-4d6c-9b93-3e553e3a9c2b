"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[7419],{3679:function(e,t,n){n.d(t,{M:function(){return x}});var a=n(89555),r=n(32599),s=n(48012),i=n(41028),o=n(15579),l=n(88464),d=n(31014),c=n(96070),u=n(6604),p=n(88443),m=n(9133),g=n(28380),h=n(50111);const f="tooltipLastPopup";var v={name:"c61v0h",styles:".du-bois-light-select-item-option-active:not(.du-bois-light-select-item-option-disabled){background-color:#e6f1f5;}"},_={name:"9gxvqt",styles:"display:flex;gap:4px;align-items:center"};const x=e=>{let{baseOptions:t,searchFilter:n,requestError:x=null,onSearchFilterChange:y,onClear:w,tooltipContent:b,placeholder:Y,useQuickFilter:C,defaultActiveFirstOption:I=!0}=e;const{theme:A,getPrefixedClassName:k}=(0,r.u)(),E=(0,d.useRef)(null),S=(0,l.A)(),[M,R]=(0,d.useState)(""),[T,D]=(0,d.useState)(void 0),[F,L]=(0,d.useState)(!1),[B,N]=(0,d.useState)(!1),[P,H]=(0,d.useState)({Metrics:10,Parameters:10,Tags:10}),O=(0,d.useRef)([]),[U,V]=(0,d.useState)(void 0);(0,d.useEffect)((()=>{R(n)}),[n]),(0,d.useEffect)((()=>{const e=O.current,t=(0,c.jP)(M);if(O.current=t,B)return void N(!1);const n=t.map((e=>e.name)),a=e.map((e=>e.name));if(!(0,m.isEqual)(n,a)&&t.length>=e.length){let n=0;for(;n<t.length;){if(n>=e.length||t[n].name.trim()!==e[n].name.trim())return D(!0),void V(t[n]);n++}}D(!1)}),[M]);const K=(0,d.useMemo)((()=>U?(0,c.M8)(t,U,P):[]),[t,U,P]),G=(0,d.useCallback)(((e,t)=>{if(U)if(e.startsWith("...")){D(!0);const e=t.value.split("_")[1];H((t=>({...t,[e]:t[e]+10})))}else{const t=M.substring(0,U.startIndex),n=M.substring(U.endIndex);R(t+e+" "+n),N(!0),D(!1)}}),[M,R,U,D]),z=(0,g.S)(f),[$,j]=(0,d.useState)((()=>{const e=Math.floor(Date.now()/1e3),t=z.getItem(f);return!t||parseInt(t,10)<e-604800})),q=d.useRef(null),W=(0,d.useMemo)((()=>{if(C&&M.length>0&&!(0,u.cK)(M))return(0,u.jA)(M)}),[M,C]);(0,d.useEffect)((()=>{if(x&&$){var e;const t=Math.floor(Date.now()/1e3);z.setItem(f,t),j(!1),null===(e=q.current)||void 0===e||e.click()}}),[x]);const Q=0===K.flatMap((e=>e.options)).length,J=T&&F&&!Q,Z=(0,d.useCallback)((e=>{var t;const n=k("select-item-option-active"),a=Boolean(null===(t=E.current)||void 0===t?void 0:t.querySelector(`.${n}`));"Enter"===e.key&&(J&&D(!1),J&&a||y(M)),"Escape"===e.key&&(e.preventDefault(),J&&D(!1))}),[J,M,y,k]);return(0,h.Y)("div",{css:(0,a.AH)({display:"flex",gap:A.spacing.sm,width:430,[A.responsive.mediaQueries.xs]:{width:"auto"}},""),children:(0,h.Y)(s.j9R,{dropdownMatchSelectWidth:560,css:(0,a.AH)({width:560,[A.responsive.mediaQueries.xs]:{width:"auto"}},""),defaultOpen:!1,defaultActiveFirstOption:I&&!C,open:J,options:K,onSelect:G,value:M,"data-test-id":"runs-search-autocomplete",dropdownRender:e=>(0,h.Y)("div",{css:v,ref:E,children:e}),children:(0,h.Y)(i.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_runssearchautocomplete.tsx_236",value:M,prefix:(0,h.Y)(i.S,{css:(0,a.AH)({svg:{width:A.general.iconFontSize,height:A.general.iconFontSize,color:A.colors.textSecondary}},"")}),onKeyDown:Z,onClick:()=>L(!0),onBlur:()=>L(!1),onChange:e=>R(e.target.value),placeholder:Y,"data-test-id":"search-box",suffix:(0,h.FD)("div",{css:_,children:[M&&(0,h.Y)(r.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_runssearchautocomplete.tsx_212",onClick:()=>{w(),R("")},type:"link","data-test-id":"clear-button",children:(0,h.Y)(r.C,{})}),W?(0,h.Y)(o.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_runssearchautocomplete.tsx_310",content:(0,h.Y)(p.A,{id:"ESmLOR",defaultMessage:"Using regular expression quick filter. The following query will be used: {filterSample}",values:{filterSample:(0,h.Y)("div",{children:(0,h.Y)("code",{children:W})})}}),delayDuration:0,children:(0,h.Y)(s.VAR,{"aria-label":S.formatMessage({id:"ESmLOR",defaultMessage:"Using regular expression quick filter. The following query will be used: {filterSample}"},{filterSample:W}),css:(0,a.AH)({svg:{width:A.general.iconFontSize,height:A.general.iconFontSize,color:A.colors.actionPrimaryBackgroundDefault}},"")})}):(0,h.Y)(s.paO,{title:b,placement:"right",dangerouslySetAntdProps:{overlayInnerStyle:{width:"150%"},trigger:["focus","click"]},children:(0,h.Y)(r.B,{size:"small",ref:q,componentId:"mlflow.experiment_page.search_filter.tooltip",type:"link",css:(0,a.AH)({marginLeft:-A.spacing.xs,marginRight:-A.spacing.xs},""),icon:(0,h.Y)(o.I,{css:(0,a.AH)({svg:{width:A.general.iconFontSize,height:A.general.iconFontSize,color:A.colors.textSecondary}},"")})})})]})})})})}},20789:function(e,t,n){n.r(t),n.d(t,{default:function(){return rs}});var a=n(89555),r=n(31014),s=n(10811),i=n(26809),o=n(93215),l=n(7204),d=n(53140),c=n(48012),u=n(15579),p=n(32599),m=n(41028),g=n(36698),h=(n(59764),n(58481)),f=n(80683),v=n.n(f),_=n(67245),x=n(64912),y=n(50111);const w="experimentName",b="artifactLocation";class Y extends r.Component{render(){return(0,y.FD)(c.SQ4,{ref:this.props.innerRef,layout:"vertical",children:[(0,y.Y)(c.SQ4.Item,{label:this.props.intl.formatMessage({id:"eyGoqW",defaultMessage:"Experiment Name"}),name:w,rules:[{required:!0,message:this.props.intl.formatMessage({id:"okQ1oB",defaultMessage:"Please input a new name for the new experiment."})},{validator:this.props.validator}],children:(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_modals_createexperimentform.tsx_51",placeholder:this.props.intl.formatMessage({id:"FZubuU",defaultMessage:"Input an experiment name"}),autoFocus:!0})}),(0,y.Y)(c.SQ4.Item,{name:b,label:this.props.intl.formatMessage({id:"meoYKZ",defaultMessage:"Artifact Location"}),rules:[{required:!1}],children:(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_modals_createexperimentform.tsx_71",placeholder:this.props.intl.formatMessage({id:"WvHAEg",defaultMessage:"Input an artifact location (optional)"})})})]})}}const C=(0,x.Ay)(Y);var I=n(87877),A=n(72877),k=n(25869);class E extends r.Component{constructor(){super(...arguments),this.handleCreateExperiment=async e=>{const t=e[w],n=e[b],a=await this.props.createExperimentApi(t,n);await this.props.searchExperimentsApi();const{value:{experiment_id:r}}=a;r&&this.props.navigate(h.h.getExperimentPageRoute(r))},this.debouncedExperimentNameValidator=v()((0,I.J)((()=>this.props.experimentNames)),400)}render(){const{isOpen:e}=this.props;return(0,y.Y)(_.B,{title:"Create Experiment",okText:"Create",isOpen:e,handleSubmit:this.handleCreateExperiment,onClose:this.props.onClose,children:(0,y.Y)(C,{validator:this.debouncedExperimentNameValidator})})}}const S={createExperimentApi:i.Gh,searchExperimentsApi:i.vF},M=(0,k.h)((0,s.Ng)((e=>({experimentNames:(0,A.DZ)(e).map((e=>e.name))})),S)(E));var R=n(97026),T=n(76010);class D extends r.Component{constructor(){super(...arguments),this.handleSubmit=()=>{const{experimentId:e,activeExperimentIds:t}=this.props,n=(0,l.yk)();return this.props.deleteExperimentApi(e,n).then((()=>{if(null!==t&&void 0!==t&&t.includes(e))if(1===t.length)this.props.navigate(h.h.rootRoute);else{const n=t.filter((t=>t!==e)),a=1===n.length?h.h.getExperimentPageRoute(n[0]):h.h.getCompareExperimentsPageRoute(n);this.props.navigate(a)}})).then((()=>this.props.searchExperimentsApi(n))).catch((e=>{T.A.logErrorAndNotifyUser(e)}))}}render(){return(0,y.Y)(R.u,{isOpen:this.props.isOpen,onClose:this.props.onClose,handleSubmit:this.handleSubmit,title:`Delete Experiment "${this.props.experimentName}"`,helpText:(0,y.FD)("div",{children:[(0,y.Y)("p",{children:(0,y.FD)("b",{children:['Experiment "',this.props.experimentName,'" (Experiment ID: ',this.props.experimentId,") will be deleted."]})}),""]}),confirmButtonText:"Delete"})}}const F={deleteExperimentApi:i.lJ,searchExperimentsApi:i.vF},L=(0,k.h)((0,s.Ng)(void 0,F)(D));var B=n(58645);class N extends r.Component{constructor(){super(...arguments),this.handleRenameExperiment=e=>{const t=e[B.m];return this.props.updateExperimentApi(this.props.experimentId,t).then((()=>this.props.getExperimentApi(this.props.experimentId))).catch((e=>T.A.logErrorAndNotifyUser(e)))},this.debouncedExperimentNameValidator=v()((0,I.J)((()=>this.props.experimentNames)),400)}render(){const{isOpen:e,experimentName:t}=this.props;return(0,y.Y)(_.B,{title:"Rename Experiment",okText:"Save",isOpen:e,handleSubmit:this.handleRenameExperiment,onClose:this.props.onClose,children:(0,y.Y)(B.P,{type:"experiment",name:t,visible:e,validator:this.debouncedExperimentNameValidator})})}}const P={updateExperimentApi:i.Td,getExperimentApi:i.yc},H=(0,s.Ng)((e=>({experimentNames:(0,A.DZ)(e).map((e=>e.name))})),P)(N);var O={name:"1d4xjjz",styles:"white-space:nowrap;overflow:hidden;text-overflow:ellipsis;flex:1"},U={name:"15c25lm",styles:"svg{transform:rotate(-90deg);}"},V={name:"ddhbat",styles:"box-sizing:border-box;height:100%;margin-left:24px;margin-right:8px;padding-right:16px;width:100%;min-width:max(280px, 20vw);max-width:20vw;display:grid;grid-template-rows:auto auto 1fr"},K={name:"1uoxr6z",styles:"svg{transform:rotate(90deg);}"};class G extends r.Component{constructor(){super(...arguments),this.list=void 0,this.state={checkedKeys:this.props.activeExperimentIds,hidden:!1,searchInput:"",showCreateExperimentModal:!1,showDeleteExperimentModal:!1,showRenameExperimentModal:!1,selectedExperimentId:"0",selectedExperimentName:""},this.bindListRef=e=>{this.list=e},this.componentDidUpdate=()=>{this.list&&this.list.forceUpdateGrid()},this.filterExperiments=e=>{const{experiments:t}=this.props,n=e.toLowerCase();return""===n?this.props.experiments:t.filter((e=>{let{name:t}=e;return t.toLowerCase().includes(n)}))},this.handleSearchInputChange=e=>{this.setState({searchInput:e.target.value})},this.updateSelectedExperiment=(e,t)=>{this.setState({selectedExperimentId:e,selectedExperimentName:t})},this.handleCreateExperiment=()=>{this.setState({showCreateExperimentModal:!0})},this.handleDeleteExperiment=(e,t)=>()=>{this.setState({showDeleteExperimentModal:!0}),this.updateSelectedExperiment(e,t)},this.handleRenameExperiment=(e,t)=>()=>{this.setState({showRenameExperimentModal:!0}),this.updateSelectedExperiment(e,t)},this.handleCloseCreateExperimentModal=()=>{this.setState({showCreateExperimentModal:!1})},this.handleCloseDeleteExperimentModal=()=>{this.setState({showDeleteExperimentModal:!1}),this.updateSelectedExperiment("0","")},this.handleCloseRenameExperimentModal=()=>{this.setState({showRenameExperimentModal:!1}),this.updateSelectedExperiment("0","")},this.handleCheck=(e,t)=>{this.setState(((n,a)=>{let{checkedKeys:r}=n;return!0!==e||a.activeExperimentIds.includes(t)||(r=[t,...a.activeExperimentIds]),!1===e&&1!==a.activeExperimentIds.length&&(r=a.activeExperimentIds.filter((e=>e!==t))),{checkedKeys:r}}),this.pushExperimentRoute)},this.pushExperimentRoute=()=>{if(this.state.checkedKeys.length>0){const e=1===this.state.checkedKeys.length?h.h.getExperimentPageRoute(this.state.checkedKeys[0]):h.h.getCompareExperimentsPageRoute(this.state.checkedKeys);this.props.navigate(e)}},this.renderListItem=e=>{let{index:t,key:n,style:r,parent:s}=e;const i=s.props.data[t],{activeExperimentIds:l}=this.props,d=l.includes(i.experimentId),m=d?"active-experiment-list-item":"experiment-list-item",{theme:g}=this.props.designSystemThemeApi;return(0,y.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",paddingLeft:g.spacing.xs,paddingRight:g.spacing.xs,borderLeft:d?`solid ${g.colors.primary}`:"solid transparent",borderLeftWidth:4,backgroundColor:d?g.colors.actionDefaultBackgroundPress:"transparent",fontSize:g.typography.fontSizeBase,svg:{width:14,height:14}},""),"data-testid":m,style:r,children:[(0,y.Y)(c.Sc0,{componentId:"mlflow.experiment_list_view.check_box",id:i.experimentId,onChange:e=>this.handleCheck(e,i.experimentId),isChecked:d,"data-testid":`${m}-check-box`},i.experimentId),(0,y.Y)(o.N_,{className:"experiment-link",css:O,to:h.h.getExperimentPageRoute(i.experimentId),onClick:()=>this.setState({checkedKeys:[i.experimentId]}),title:i.name,"data-testid":`${m}-link`,children:i.name}),(0,y.Y)(u.T,{componentId:"mlflow.experiment_list_view.rename_experiment_button.tooltip",content:"Rename experiment",children:(0,y.Y)(p.B,{type:"link",componentId:"mlflow.experiment_list_view.rename_experiment_button",icon:(0,y.Y)(c.R2l,{}),onClick:this.handleRenameExperiment(i.experimentId,i.name),"data-testid":"rename-experiment-button",size:"small"})}),(0,y.Y)(u.T,{componentId:"mlflow.experiment_list_view.delete_experiment_button.tooltip",content:"Delete experiment",children:(0,y.Y)(p.B,{type:"link",componentId:"mlflow.experiment_list_view.delete_experiment_button",icon:(0,y.Y)(c.ucK,{}),onClick:this.handleDeleteExperiment(i.experimentId,i.name),"data-testid":"delete-experiment-button",size:"small"})})]},n)},this.unHide=()=>this.setState({hidden:!1}),this.hide=()=>this.setState({hidden:!0})}render(){const{hidden:e}=this.state,{activeExperimentIds:t,designSystemThemeApi:n}=this.props,{theme:r}=n;if(e)return(0,y.Y)(u.T,{content:"Show experiment list",componentId:"mlflow.experiment_list_view.show_experiments.tooltip",children:(0,y.Y)(p.B,{componentId:"mlflow.experiment_list_view.show_experiments",icon:(0,y.Y)(c.iTX,{}),onClick:this.unHide,css:U,title:"Show experiment list"})});const{searchInput:s}=this.state,i=this.filterExperiments(s);return(0,y.FD)("div",{id:"experiment-list-outer-container",css:V,children:[(0,y.Y)(M,{isOpen:this.state.showCreateExperimentModal,onClose:this.handleCloseCreateExperimentModal}),(0,y.Y)(L,{isOpen:this.state.showDeleteExperimentModal,onClose:this.handleCloseDeleteExperimentModal,activeExperimentIds:t,experimentId:this.state.selectedExperimentId,experimentName:this.state.selectedExperimentName}),(0,y.Y)(H,{isOpen:this.state.showRenameExperimentModal,onClose:this.handleCloseRenameExperimentModal,experimentId:this.state.selectedExperimentId,experimentName:this.state.selectedExperimentName}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:r.spacing.sm},""),children:[(0,y.Y)(p.T.Title,{level:2,style:{margin:0},children:"Experiments"}),(0,y.FD)("div",{children:[(0,y.Y)(u.T,{componentId:"mlflow.experiment_list_view.new_experiment_button.tooltip",content:"New experiment",children:(0,y.Y)(p.B,{componentId:"mlflow.experiment_list_view.new_experiment_button",icon:(0,y.Y)(c.GYj,{}),onClick:this.handleCreateExperiment,title:"New Experiment","data-testid":"create-experiment-button"})}),(0,y.Y)(u.T,{componentId:"mlflow.experiment_list_view.hide_button.tooltip",content:"Hide experiment list",children:(0,y.Y)(p.B,{componentId:"mlflow.experiment_list_view.hide_button",icon:(0,y.Y)(c.iTX,{}),onClick:this.hide,css:K,title:"Hide experiment list"})})]})]}),(0,y.Y)(m.I,{componentId:"mlflow.experiment_list_view.search_input",placeholder:"Search experiments","aria-label":"search experiments",value:s,onChange:this.handleSearchInputChange,"data-testid":"search-experiment-input"}),(0,y.Y)("div",{css:(0,a.AH)({marginTop:r.spacing.xs},""),children:(0,y.Y)(g.t$,{children:e=>{let{width:t,height:n}=e;return(0,y.Y)(g.B8,{rowRenderer:this.renderListItem,data:i,ref:this.bindListRef,rowHeight:32,overscanRowCount:10,height:n,width:t,rowCount:i.length})}})})]})}}var z=(0,k.h)((0,p.as)(G)),$=n(24947),j=n(9133),q=n(62448),W=n(91144);const Q=(0,r.createContext)(null),J=e=>{let{children:t,actions:n}=e;const[a,i]=(0,r.useState)([]),[o,l]=(0,r.useState)(!1),[c,u]=(0,r.useState)(null),p=(0,s.wA)(),m=(0,r.useCallback)((e=>{u(null),(()=>{const t=e.map((e=>{const t=n.getExperimentApi(e);return p(t).catch((e=>{(0,W.Ng)()||T.A.logErrorAndNotifyUser(e)})),t.meta.id}));i((e=>(0,j.isEqual)(t,e)?e:t))})()}),[n,p]),g=(0,r.useMemo)((()=>({fetchExperiments:m,isLoadingExperiment:o,requestError:c,actions:n})),[n,m,o,c]);return(0,y.Y)(Q.Provider,{value:g,children:(0,y.Y)(d.Ay,{shouldOptimisticallyRender:!0,requestIds:a,children:(e,n,r)=>(l(r.some((e=>a.includes(e.id)&&e.active))),c||r.forEach((e=>{if(e.error){if((0,W.Ng)()){const t=(0,q.h)(e.error);if(t)return void u(t)}u(e.error)}})),t)})})};var Z=n(47664),X=n(88443),ee=n(79085),te=n(56412);var ne={name:"490tlg",styles:"display:flex;gap:4px"};const ae=e=>{let{copyText:t}=e;return(0,y.FD)("div",{css:ne,children:[(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_shared_building_blocks_copybox.tsx_18",readOnly:!0,value:t,"data-testid":"copy-box"}),(0,y.Y)(te.i,{copyText:t})]})},re=e=>{let{visible:t,onCancel:n,link:a}=e;return(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_modals_getlinkmodal.tsx_21",title:(0,y.Y)(X.A,{id:"ktiuki",defaultMessage:"Get Link"}),visible:t,onCancel:n,children:(0,y.Y)(ae,{copyText:a})})};var se=n(45653),ie=n(25866);const oe="compareRunsMode",le=()=>(0,W.xt)()?"CHART":"TABLE",de={MODELS:e=>h.h.getExperimentPageTabRoute(e,ie.fM.Models)},ce=()=>{const[e,t]=(0,o.ok)(),n=(0,o.Zp)();return[e.get(oe)||le(),(e,a)=>{if(e in de&&a){var r;const t=null===(r=de[e])||void 0===r?void 0:r.call(de,a);if(t)return void n(t)}t((t=>(t.set(oe,e||""),t)),{replace:!1})}]};var ue={name:"11jf4ye",styles:"display:flex;gap:8px"},pe={name:"82a6rk",styles:"flex:1"};const me=e=>{let{onCancel:t,visible:n,experimentIds:a,searchFacetsState:o,uiState:l}=e;const[d,p]=(0,r.useState)(""),[g,f]=(0,r.useState)(!0),[v,_]=(0,r.useState)(null),[x]=ce(),w=(0,s.wA)(),b=(0,r.useMemo)((()=>({...o,...l})),[o,l]),Y=(0,r.useCallback)((async e=>{if(a.length>1)return f(!1),_(e),void p(window.location.href);f(!0);const[t]=a;try{const n=await(async e=>(0,W.YP)()?(0,se.z7)(JSON.stringify(e)):JSON.stringify(e))(e),a=await(0,se.Xb)(n),r=`${ie.o6}${a}`;await w((0,i.EJ)(t,r,n)),f(!1),_(e),p(((e,t,n)=>{const a=h.h.getExperimentPageRoute(e),r=new URLSearchParams;r.set(ie.ex,t),n&&r.set(oe,n);const s=r.toString(),i=`${a}${null!==s&&void 0!==s&&s.startsWith("?")?"":"?"}${s}`;return`${window.location.origin}${window.location.pathname}#${i}`})(t,a,x))}catch(n){throw T.A.logErrorAndNotifyUser("Failed to create shareable link for experiment"),n}}),[w,a,x]);return(0,r.useEffect)((()=>{n&&v!==b&&Y(b)}),[n,Y,v,b]),(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentgetsharelinkmodal.tsx_101",title:(0,y.Y)(X.A,{id:"6KFQMl",defaultMessage:"Get shareable link"}),visible:n,onCancel:t,children:(0,y.FD)("div",{css:ue,children:[g?(0,y.Y)(c.xUE,{css:pe}):(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentgetsharelinkmodal.tsx_115",placeholder:"Click button on the right to create shareable state",value:d,readOnly:!0}),(0,y.Y)(te.i,{loading:g,copyText:d,"data-testid":"share-link-copy-button"})]})})},ge=e=>{let{searchFacetsState:t,uiState:n,experimentIds:a}=e;const[s,i]=(0,r.useState)(!1);return(0,y.FD)(y.FK,{children:[t&&n&&a?(0,y.Y)(me,{searchFacetsState:t,uiState:n,visible:s,onCancel:()=>i(!1),experimentIds:a}):(0,y.Y)(re,{link:window.location.href,visible:s,onCancel:()=>i(!1)}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentviewheadersharebutton.tsx_44",type:"primary",onClick:()=>i(!0),"data-test-id":"share-button",children:(0,y.Y)(X.A,{id:"2oFAO4",defaultMessage:"Share"})})]})},he=r.memo((e=>{let{experiments:t}=e;const n=(0,r.useMemo)((()=>(0,y.Y)(X.A,{id:"Dhn7Mb",defaultMessage:"Displaying Runs from {numExperiments} Experiments",values:{numExperiments:t.length}})),[t.length]);return(0,y.Y)(ee.z,{title:n,breadcrumbs:[],children:(0,y.Y)(ge,{})})}));var fe=n(21616);var ve=n(33946),_e=n(53677);class xe extends r.Component{constructor(e){super(e),this.handleSubmit=this.handleSubmit.bind(this)}handleSubmit(){const e=[];return this.props.selectedRunIds.forEach((t=>{e.push(this.props.restoreRunApi(t))})),Promise.all(e).catch((e=>{let t="While restoring an experiment run, an error occurred.";e.textJson&&"RESOURCE_LIMIT_EXCEEDED"===e.textJson.error_code&&(t=t+" "+e.textJson.message),this.props.openErrorModal(t)})).then((()=>{var e,t;null===(e=(t=this.props).onSuccess)||void 0===e||e.call(t)}))}render(){const e=this.props.selectedRunIds.length;return(0,y.Y)(R.u,{isOpen:this.props.isOpen,onClose:this.props.onClose,handleSubmit:this.handleSubmit,title:`Restore Experiment ${T.A.pluralize("Run",e)}`,helpText:`${e} experiment ${T.A.pluralize("run",e)} will be restored.`,confirmButtonText:"Restore"})}}const ye={restoreRunApi:i.iz,openErrorModal:i.Yi};var we=(0,s.Ng)(null,ye)(xe);const be=e=>{let{showDeleteRunModal:t,showRestoreRunModal:n,showRenameRunModal:a,runsSelected:r,onCloseDeleteRunModal:s,onCloseRestoreRunModal:i,onCloseRenameRunModal:o,renamedRunName:l,refreshRuns:d}=e;const c=Object.entries(r).filter((e=>{let[,t]=e;return t})).map((e=>{let[t]=e;return t}));return(0,y.FD)(y.FK,{children:[(0,y.Y)(ve.A,{isOpen:t,onClose:s,selectedRunIds:c,onSuccess:()=>{d()}}),(0,y.Y)(we,{isOpen:n,onClose:i,selectedRunIds:c,onSuccess:()=>{d()}}),(0,y.Y)(_e.j,{runUuid:c[0],onClose:o,runName:l,isOpen:a,onSuccess:()=>{d()}})]})};var Ye=n(98590);var Ce={name:"82a6rk",styles:"flex:1"},Ie={name:"82a6rk",styles:"flex:1"};const Ae=e=>{let{isOpen:t,setIsOpen:n,selectedRunsExistingTagKeys:s,addNewTag:i}=e;const{theme:o}=(0,p.u)(),[l,d]=(0,r.useState)(""),[g,h]=(0,r.useState)(""),f=""===l||/^[^,.:/=\-\s]+$/.test(l),v=s.includes(l),_=f&&!v,x=l.length>0&&g.length>0&&_;return(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactionsaddnewtagmodal.tsx_34",title:(0,y.Y)(X.A,{id:"VGJhVI",defaultMessage:"Add New Tag"}),visible:t,onCancel:()=>n(!1),onOk:()=>{x&&(i({key:l,value:g}),n(!1),d(""),h(""))},okText:(0,y.Y)(X.A,{id:"oU/SPm",defaultMessage:"Add"}),cancelText:(0,y.Y)(X.A,{id:"urvfNd",defaultMessage:"Cancel"}),okButtonProps:{disabled:!x},children:(0,y.Y)("form",{css:(0,a.AH)({display:"flex",alignItems:"flex-end",gap:o.spacing.md},""),children:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:o.spacing.md,flex:1},""),children:[(0,y.FD)("div",{css:Ce,children:[(0,y.Y)(c.D$Q.Label,{htmlFor:"key",children:(0,y.Y)(X.A,{id:"FYxQgz",defaultMessage:"Key"})}),(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactionsaddnewtagmodal.tsx_51",value:l,onChange:e=>d(e.target.value),validationState:_?void 0:"warning","data-testid":"add-new-tag-key-input"}),!f&&(0,y.Y)(c.D$Q.Hint,{children:(0,y.Y)(X.A,{id:"Sb0Z4Z",defaultMessage:", . : / - = and blank spaces are not allowed"})}),v&&(0,y.Y)(c.D$Q.Hint,{children:(0,y.Y)(X.A,{id:"xBoybr",defaultMessage:"Tag key already exists on one or more of the selected runs. Please choose a different key."})})]}),(0,y.FD)("div",{css:Ie,children:[(0,y.Y)(c.D$Q.Label,{htmlFor:"value",children:(0,y.Y)(X.A,{id:"OQsEJv",defaultMessage:"Value"})}),(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactionsaddnewtagmodal.tsx_78",value:g,onChange:e=>h(e.target.value),"data-testid":"add-new-tag-value-input"})]})]})})})};var ke=n(52350);const Ee=e=>`${e.key}: ${e.value}`,Se=e=>{const[t,...n]=e.split(": ");return{key:t,value:n.join(": ")}},Me=e=>{let{runInfos:t,runsSelected:n,tagsList:o,refreshRuns:l}=e;const{theme:d}=(0,p.u)(),[u,m]=(0,r.useState)({}),[g,h]=(0,r.useState)(!1),[f,v]=(0,r.useState)(!1),[_,x]=(0,r.useState)(!1),{allSelectedTags:w,allNotSelectedTags:b,indeterminateTags:Y,allTags:C}=((e,t,n)=>{const a=e.flatMap(((e,a)=>{if(t[e.runUuid]){const e=n[a];return[Object.keys(e).filter(Ye.oD).map((t=>Ee(e[t])))]}return[]})),r=n.flatMap((e=>Object.keys(e).filter(Ye.oD).map((t=>Ee(e[t]))))),s=r.filter((e=>a.every((t=>t.includes(e))))),i=r.filter((e=>a.every((t=>!t.includes(e))))),o=r.filter((e=>!s.includes(e)&&a.some((t=>t.includes(e)))));return{allSelectedTags:s,allNotSelectedTags:i,indeterminateTags:o,allTags:r}})(t,n,o),I=e=>{m((()=>{const t={...u};return C.forEach((e=>{w.includes(e)?t[e]=!0:b.includes(e)?t[e]=!1:Y.includes(e)&&(t[e]=void 0)})),void 0!==e&&(t[Ee(e)]=!0),t})),v(!0)},A=e=>{m((t=>({...t,[e]:!t[e]})))},k=(0,s.wA)();return(0,y.FD)(y.FK,{children:[(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactionsselecttags.tsx_162",open:f,label:"Add tags",id:"runs-tag-multiselect",multiSelect:!0,children:[(0,y.Y)(c.gGe,{onClick:()=>{f?v(!1):I()},"data-testid":"runs-tag-multiselect-trigger"}),(0,y.FD)(c.dn6,{matchTriggerWidth:!0,children:[(0,y.Y)(c.HI_,{children:Object.keys(u).map((e=>{const t=void 0===u[e];return(0,y.Y)(c.jTC,{value:e,onChange:A,checked:u[e],indeterminate:t},e)}))}),(0,y.Y)(c.BU4,{children:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",justifyContent:"flex-end",gap:d.spacing.sm},""),children:[(0,y.Y)(p.B,{componentId:"mlflow.experiment_page.runs.add_new_tag",onClick:()=>{h(!0),v(!1)},icon:(0,y.Y)(c.c11,{}),"data-testid":"runs-add-new-tag-button",children:(0,y.Y)(X.A,{id:"2zy+D5",defaultMessage:"Add new tag"})}),(0,y.Y)(p.B,{type:"primary",componentId:"mlflow.experiment_page.runs.add_tags",onClick:()=>{x(!0);t.flatMap(((e,t)=>n[e.runUuid]?[t]:[])).forEach((e=>{const n=t[e].runUuid,a=Object.values(o[e]).filter((e=>(0,Ye.oD)(e.key))),r=Object.keys(u).filter((e=>void 0===u[e]?a.map((e=>Ee(e))).includes(e):u[e])).map((e=>Se(e)));k((0,i.hD)(n,a,r)).then((()=>{l()})).catch((e=>{const t=e instanceof ke.s?e.getMessageField():e.message;T.A.displayGlobalErrorNotification(t)})).finally((()=>{x(!1),v(!1)}))}))},disabled:0===Object.keys(u).length,loading:_,children:(0,y.Y)(X.A,{id:"dl0TeT",defaultMessage:"Save"})})]})})]})]}),(0,y.Y)(Ae,{isOpen:g,setIsOpen:h,selectedRunsExistingTagKeys:(0,j.uniq)(w.concat(Y).map((e=>Se(e).key))),addNewTag:e=>{I(e)}})]})},Re=e=>{let{children:t}=e;return(0,y.Y)(y.FK,{children:t})},Te=r.memo((e=>{let{viewState:t,runsData:n,searchFacetsState:a,refreshRuns:s}=e;const{runsSelected:i}=t,{runInfos:l,tagsList:d}=n,{lifecycleFilter:c}=a,u=(0,o.Zp)(),{theme:m}=(0,p.u)(),[g,f]=(0,r.useState)(!1),[v,_]=(0,r.useState)(!1),[x,w]=(0,r.useState)(!1),[b,Y]=(0,r.useState)(""),C=(0,r.useCallback)((()=>{const e=Object.keys(i),t=l.find((t=>t.runUuid===e[0]));t&&(Y(t.runName),w(!0))}),[l,i]),I=(0,r.useCallback)((()=>{const e=Object.keys(i),t=l.filter((t=>{let{runUuid:n}=t;return e.includes(n)})).map((e=>{let{experimentId:t}=e;return t}));u(h.h.getCompareRunPageRoute(e,[...new Set(t)].sort()))}),[u,l,i]),A=(0,r.useCallback)((()=>f(!0)),[]),k=(0,r.useCallback)((()=>_(!0)),[]),E=(0,r.useCallback)((()=>f(!1)),[]),S=(0,r.useCallback)((()=>_(!1)),[]),M=(0,r.useCallback)((()=>w(!1)),[]),R=Object.values(t.runsSelected).filter(Boolean).length,T=R>0,D=1===R,F=R>1;return(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:De.controlBar,children:[(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactions.tsx_110","data-testid":"run-rename-button",onClick:C,disabled:!D,children:(0,y.Y)(X.A,{id:"oWPgX7",defaultMessage:"Rename"})}),c===ie.gy.ACTIVE?(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactions.tsx_117","data-testid":"runs-delete-button",disabled:!T,onClick:A,danger:!0,children:(0,y.Y)(X.A,{id:"3Rb4sG",defaultMessage:"Delete"})}):null,c===ie.gy.DELETED?(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactions.tsx_126","data-testid":"runs-restore-button",disabled:!T,onClick:k,children:(0,y.Y)(X.A,{id:"Potju2",defaultMessage:"Restore"})}):null,(0,y.Y)("div",{css:De.buttonSeparator}),(0,y.Y)(Re,{children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsactions.tsx_136","data-testid":"runs-compare-button",disabled:!F,onClick:I,children:(0,y.Y)(X.A,{id:"RaGnOQ",defaultMessage:"Compare"})})}),(0,y.Y)("div",{css:De.buttonSeparator}),(0,y.Y)(Me,{runsSelected:i,runInfos:l,tagsList:d,refreshRuns:s})]}),(0,y.Y)(be,{runsSelected:i,onCloseRenameRunModal:M,onCloseDeleteRunModal:E,onCloseRestoreRunModal:S,showDeleteRunModal:g,showRestoreRunModal:v,showRenameRunModal:x,renamedRunName:b,refreshRuns:s})]})})),De={buttonSeparator:e=>({borderLeft:`1px solid ${e.colors.border}`,marginLeft:e.spacing.xs,marginRight:e.spacing.xs,height:"100%"}),controlBar:e=>({display:"flex",gap:e.spacing.sm,alignItems:"center"})};var Fe=n(88464),Le=n(14343);var Be={name:"bjn8wh",styles:"position:relative"};const Ne=r.memo((e=>{const{runInfos:t}=e,{theme:n}=(0,p.u)(),{refreshRuns:s}=e,o=(0,$.z)(),[l,d]=(0,r.useState)(0),[u,m]=(0,r.useState)(0);return(0,r.useEffect)((()=>{m(0),d((()=>Date.now()))}),[t]),(0,r.useEffect)((()=>{if(!l)return;const e=setInterval((()=>{const e={experimentIds:o,filter:`attributes.start_time > ${l}`,maxResults:ie.ks};(0,i.bc)(e).then((e=>{var t;const n=(null===(t=e.runs)||void 0===t?void 0:t.length)||0;m(n)}))}),ie.a0);return()=>clearInterval(e)}),[l,o]),(0,y.FD)("div",{css:Be,children:[u>0&&(0,y.Y)("div",{title:ie.ks>u?`${u}`:ie.ks-1+"+",css:(0,a.AH)({position:"absolute",top:0,right:0,transform:"translate(50%, -50%)",display:"flex",justifyContent:"center",alignItems:"center",width:u>9?28:20,height:20,borderRadius:10,border:`1px solid ${n.colors.white}`,backgroundColor:n.colors.lime,color:n.colors.white,fontSize:10,fontWeight:"bold",userSelect:"none",zIndex:1},""),children:ie.ks>u?u:ie.ks-1+"+"}),(0,y.Y)(c.paO,{title:(0,y.Y)(X.A,{id:"ag05Pe",defaultMessage:"Refresh"}),useAsLabel:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrefreshbutton.tsx_123",onClick:s,"data-testid":"runs-refresh-button",icon:(0,y.Y)(c.Fjq,{})})})]})})),Pe=(0,s.Ng)((e=>({runInfos:e.entities.runInfosByUuid})),void 0,void 0,{areStatesEqual:(e,t)=>e.entities.runInfosByUuid===t.entities.runInfosByUuid})(Ne);var He=n(3679);const Oe=()=>(0,y.FD)("div",{className:"search-input-tooltip-content",children:[(0,y.Y)(X.A,{id:"kIlkgf",defaultMessage:"Search runs using a simplified version of the SQL {whereBold} clause.",values:{whereBold:(0,y.Y)("b",{children:"WHERE"})}})," ",(0,y.Y)(X.A,{id:"nYKZy4",defaultMessage:"<link>Learn more</link>",values:{link:e=>(0,y.Y)("a",{href:Z.g2,target:"_blank",rel:"noopener noreferrer",children:e})}}),(0,y.Y)("br",{}),(0,y.Y)(X.A,{id:"U3btBc",defaultMessage:"Examples:"}),(0,y.Y)("br",{}),"\u2022 metrics.rmse >= 0.8",(0,y.Y)("br",{}),"\u2022 metrics.`f1 score` < 1",(0,y.Y)("br",{}),"\u2022 params.model = 'tree'",(0,y.Y)("br",{}),"\u2022 attributes.run_name = 'my run'",(0,y.Y)("br",{}),"\u2022 tags.`mlflow.user` = 'myUser'",(0,y.Y)("br",{}),"\u2022 metric.f1_score > 0.9 AND params.model = 'tree'",(0,y.Y)("br",{}),"\u2022 dataset.name IN ('dataset1', 'dataset2')",(0,y.Y)("br",{}),"\u2022 attributes.run_id IN ('a1b2c3d4', 'e5f6g7h8')",(0,y.Y)("br",{}),"\u2022 tags.model_class LIKE 'sklearn.linear_model%'"]});var Ue=n(96070);const Ve=["run_id","run_name","status","artifact_uri","user_id","start_time","end_time","created"].map((e=>({value:`attributes.${e}`}))),Ke=(e,t)=>[...new Set([...e,...t])],Ge=e=>e.flatMap((e=>Object.keys(e))),ze=e=>{let{runsData:t,...n}=e;const a=(0,r.useRef)({metricNames:[],paramNames:[],tagNames:[]}),s=(0,r.useMemo)((()=>{const e=a.current,n=(r=t,{metricNames:Ke((s=e).metricNames,r.metricKeyList),paramNames:Ke(s.paramNames,r.paramKeyList),tagNames:(0,Ue.tY)(Ke(Ge(s.tagNames),Ge(r.tagsList)))});var r,s;return a.current=n,(0,Ue.$K)(n,Ve)}),[t]);return(0,y.Y)(He.M,{...n,baseOptions:s,tooltipContent:(0,y.Y)(Oe,{}),placeholder:'metrics.rmse < 1 and params.model = "tree"',useQuickFilter:(0,W.ey)()})};var $e=n(18476),je=n(37616);var qe={name:"1voqbl3",styles:"margin-top:0;font-weight:normal"};const We=e=>{let{isOpen:t,closeModal:n,experimentId:r}=e;const{theme:s}=(0,p.u)(),i=s.isDarkMode?"duotoneDark":"light",o=`\nimport mlflow\nfrom sklearn.model_selection import train_test_split\nfrom sklearn.datasets import load_diabetes\nfrom sklearn.ensemble import RandomForestRegressor\n\n# set the experiment id\nmlflow.set_experiment(experiment_id="${r}")\n\nmlflow.autolog()\ndb = load_diabetes()\n\nX_train, X_test, y_train, y_test = train_test_split(db.data, db.target)\n\n# Create and train models.\nrf = RandomForestRegressor(n_estimators=100, max_depth=6, max_features=3)\nrf.fit(X_train, y_train)\n\n# Use the model to make predictions on the test dataset.\npredictions = rf.predict(X_test)\n`.trimStart(),l=`\nimport mlflow\nimport openai\nimport os\nimport pandas as pd\n\n# you must set the OPENAI_API_KEY environment variable\nassert (\n  "OPENAI_API_KEY" in os.environ\n), "Please set the OPENAI_API_KEY environment variable."\n\n# set the experiment id\nmlflow.set_experiment(experiment_id="${r}")\n\nsystem_prompt = (\n  "The following is a conversation with an AI assistant."\n  + "The assistant is helpful and very friendly."\n)\n\n# start a run\nmlflow.start_run()\nmlflow.log_param("system_prompt", system_prompt)\n\n# Create a question answering model using prompt engineering\n# with OpenAI. Log the model to MLflow Tracking\nlogged_model = mlflow.openai.log_model(\n    model="gpt-4o-mini",\n    task=openai.chat.completions,\n    artifact_path="model",\n    messages=[\n        {"role": "system", "content": system_prompt},\n        {"role": "user", "content": "{question}"},\n    ],\n)\n\n# Evaluate the model on some example questions\nquestions = pd.DataFrame(\n    {\n        "question": [\n            "How do you create a run with MLflow?",\n            "How do you log a model with MLflow?",\n            "What is the capital of France?",\n        ]\n    }\n)\nmlflow.evaluate(\n    model=logged_model.model_uri,\n    model_type="question-answering",\n    data=questions,\n)\nmlflow.end_run()\n`.trimStart(),d=18*(Math.min(...[o,l].map((e=>e.split("\n").length)))+1);return(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_createnotebookrunmodal.tsx_111",visible:t,onCancel:n,onOk:n,footer:(0,y.Y)("div",{css:(0,a.AH)({display:"flex",gap:s.spacing.sm,justifyContent:"flex-end"},""),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_createnotebookrunmodal.tsx_117",onClick:n,type:"primary",children:(0,y.Y)(X.A,{id:"ttyLD4",defaultMessage:"Okay"})})}),title:(0,y.FD)("div",{children:[(0,y.Y)(p.T.Title,{level:2,css:(0,a.AH)({marginTop:s.spacing.sm,marginBottom:s.spacing.xs},""),children:(0,y.Y)(X.A,{id:"iF1UfU",defaultMessage:"New run using notebook"})}),(0,y.Y)(p.T.Hint,{css:qe,children:"Run this code snippet in a notebook or locally, to create an experiment run"})]}),children:(0,y.FD)(c.Y6f,{children:[(0,y.Y)(c.jyI,{tab:(0,y.Y)(X.A,{id:"71dHIO",defaultMessage:"Classical ML"}),children:(0,y.Y)(je.z7,{style:{padding:"5px",height:d},language:"python",theme:i,actions:(0,y.Y)("div",{style:{marginTop:s.spacing.sm,marginRight:s.spacing.md},children:(0,y.Y)(te.i,{copyText:o,showLabel:!1,icon:(0,y.Y)(c.TdU,{})})}),children:o})},"classical-ml"),(0,y.Y)(c.jyI,{tab:(0,y.Y)(X.A,{id:"xBVMQz",defaultMessage:"LLM"}),children:(0,y.Y)(je.z7,{style:{padding:"5px",height:d},language:"python",theme:i,actions:(0,y.Y)("div",{style:{marginTop:s.spacing.sm,marginRight:s.spacing.md},children:(0,y.Y)(te.i,{copyText:l,showLabel:!1,icon:(0,y.Y)(c.TdU,{})})}),children:l})},"llm")]})})};var Qe=n(30214),Je=n(4877),Ze=n.n(Je),Xe=n(63528),et=n(50361);class tt extends ke.s{getGatewayErrorMessage(){var e,t,n,a;return(null===(e=this.textJson)||void 0===e||null===(t=e.error)||void 0===t?void 0:t.message)||(null===(n=this.textJson)||void 0===n?void 0:n.message)||(null===(a=this.textJson)||void 0===a?void 0:a.toString())||this.text}}var nt;class at{static createEvaluationTextPayload(e,t){switch(t){case et.W8.LLM_V1_COMPLETIONS:return{prompt:e};case et.W8.LLM_V1_CHAT:return{messages:[{content:e,role:"user"}]};case et.W8.LLM_V1_EMBEDDINGS:throw new Error(`Unsupported served LLM model task "${t}"!`);default:throw new Error(`Unknown served LLM model task "${t}"!`)}}}nt=at,at.queryMLflowDeploymentEndpointRoute=async(e,t)=>{Ze()(e.mlflowDeployment,"Trying to call a MLflow deployment route without a deployment_url");const{inputText:n}=t,a={...nt.createEvaluationTextPayload(n,e.task),...t.parameters};return Xe.x.gatewayProxyPost({gateway_path:e.mlflowDeployment.endpoint_url.substring(1),json_data:a})},at.queryModelGatewayRoute=async(e,t)=>{if("mlflow_deployment_endpoint"===e.type){Ze()(e.mlflowDeployment,"Trying to call a serving endpoint route without an endpoint");return((e,t)=>{if(t===et.W8.LLM_V1_COMPLETIONS){var n,a;const t=e,r=null===(n=t.choices)||void 0===n||null===(a=n[0])||void 0===a?void 0:a.text,{usage:s}=t;if(r&&s)return{text:r,metadata:{total_tokens:s.total_tokens,output_tokens:s.completion_tokens,input_tokens:s.prompt_tokens}}}if(t===et.W8.LLM_V1_CHAT){var r,s,i;const t=e,n=null===(r=t.choices)||void 0===r||null===(s=r[0])||void 0===s||null===(i=s.message)||void 0===i?void 0:i.content,{usage:a}=t;if(n&&a)return{text:n,metadata:{total_tokens:a.total_tokens,output_tokens:a.completion_tokens,input_tokens:a.prompt_tokens}}}throw new Error(`Unrecognizable AI gateway response metadata "${e.usage}"!`)})(await nt.queryMLflowDeploymentEndpointRoute(e,t),e.task)}throw new Error("Unknown route type")};var rt=n(23734),st=n(65795),it=n(69526);const ot=[{type:"slider",name:"temperature",string:(0,it.zR)({id:"Jrri/Y",defaultMessage:"Temperature"}),helpString:(0,it.zR)({id:"5yWkFd",defaultMessage:"Increase or decrease the confidence level of the language model."}),max:1,min:0,step:.01},{type:"input",name:"max_tokens",string:(0,it.zR)({id:"qR3llD",defaultMessage:"Max tokens"}),helpString:(0,it.zR)({id:"cEwSR8",defaultMessage:"Maximum number of language tokens returned from evaluation."}),max:65536,min:1,step:1},{type:"list",name:"stop",string:(0,it.zR)({id:"FAfPOl",defaultMessage:"Stop Sequences"}),helpString:(0,it.zR)({id:"J2XCE/",defaultMessage:"Specify sequences that signal the model to stop generating text."})}],lt={temperature:.01,max_tokens:100},dt=()=>{const[e,t]=(0,r.useState)(lt),n=(0,r.useCallback)(((e,n)=>{t((t=>({...t,[e]:n})))}),[]);return{parameterDefinitions:ot,parameters:e,updateParameter:n}};var ct=n(20193);var ut={name:"1d3w5wq",styles:"width:100%"};const pt=e=>{let{parameterValue:t,updateParameter:n,disabled:s}=e;const[i,o]=(0,r.useState)(""),{theme:l}=(0,p.u)();return(0,j.isArray)(t)?(0,y.FD)(y.FK,{children:[(0,y.Y)("div",{css:(0,a.AH)({marginTop:l.spacing.xs,marginBottom:l.spacing.sm},""),children:t.map(((e,a)=>(0,y.Y)(c.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptparameters.tsx_28",closable:!0,onClose:()=>{n(t.filter((t=>t!==e)))},children:e},a)))}),(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptparameters.tsx_39",allowClear:!0,css:ut,disabled:s,onChange:e=>o(e.target.value),value:i,onKeyDown:e=>{"Enter"===e.key&&i.trim()&&(n((0,j.uniq)([...t,i])),o(""))}})]}):null};var mt={name:"151f7v8",styles:"span{font-weight:normal;}"};const gt=e=>{let{disabled:t=!1,parameters:n,updateParameter:r}=e;const{parameterDefinitions:s}=dt(),{theme:i}=(0,p.u)();return(0,y.FD)("div",{css:(0,a.AH)({marginBottom:i.spacing.lg},""),children:[(0,y.Y)(c.D$Q.Label,{css:(0,a.AH)({marginBottom:i.spacing.md},""),children:(0,y.Y)(X.A,{id:"KADUUT",defaultMessage:"Model parameters"})}),s.map((e=>{var s;return(0,y.Y)("div",{css:(0,a.AH)({marginBottom:i.spacing.md},""),children:(0,y.FD)(y.FK,{children:[(0,y.FD)(c.D$Q.Label,{htmlFor:e.name,css:mt,children:[(0,y.Y)(X.A,{...e.string}),(0,y.Y)(c.paO,{title:(0,y.Y)(X.A,{...e.helpString}),placement:"right",children:(0,y.Y)(u.I,{css:(0,a.AH)({marginLeft:i.spacing.sm,verticalAlign:"text-top",color:i.colors.textSecondary},"")})})]}),(0,y.Y)(c.D$Q.Hint,{}),"temperature"===e.name&&(0,y.Y)(ct.o,{"data-testid":e.name,disabled:t,max:e.max,min:e.min,step:e.step,value:n[e.name]||0,onChange:t=>r(e.name,t)}),"input"===e.type&&(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptparameters.tsx_107","data-testid":e.name,type:"number",disabled:t,max:e.max,min:e.min,step:e.step,value:n[e.name]||0,onChange:t=>r(e.name,parseInt(t.target.value,10))}),"list"===e.type&&(0,y.Y)(pt,{parameterValue:null!==(s=n[e.name])&&void 0!==s?s:[],disabled:t,updateParameter:t=>r(e.name,t)})]})},e.name)}))]})},ht=" }}",ft="new_variable",vt=()=>{const[e,t]=(0,r.useState)(st.KH),n=(0,r.useRef)(),a=(0,r.useCallback)((()=>{t((e=>{const t=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];if(!e.includes(ft))return ft;const t=(0,j.max)(e.map((e=>{var t;return parseInt((null===(t=e.match(/new_variable_(\d+)/))||void 0===t?void 0:t[1])||"1",10)})))||1;return`${ft}_${t+1}`}((0,st.rl)(e)),a=`${e} {{ ${t}${ht}`;return requestAnimationFrame((()=>{const e=n.current;e&&(e.focus(),e.setSelectionRange(a.length-t.length-3,a.length-3))})),a}))}),[t]);return{savePromptTemplateInputRef:(0,r.useCallback)((e=>{var t;n.current=null===e||void 0===e||null===(t=e.resizableTextArea)||void 0===t?void 0:t.textArea}),[]),handleAddVariableToTemplate:a,promptTemplate:e,updatePromptTemplate:t}},_t=(0,it.zR)({id:"lM0z8k",defaultMessage:"The following variable names contain spaces which is disallowed: {invalidNames}"}),xt=e=>{let{violations:t}=e;const{namesWithSpaces:n}=t,{formatMessage:a}=(0,Fe.A)();return(0,y.Y)(y.FK,{children:n.length>0&&(0,y.Y)(p.T.Text,{color:"warning",size:"sm","aria-label":a(_t,{invalidNames:n.join(", ")}),children:(0,y.Y)(X.A,{..._t,values:{invalidNames:(0,y.Y)(y.FK,{children:n.map((e=>(0,y.Y)("code",{children:e},e)))})}})})})},yt=[{prompt:["You are a marketing consultant for a technology company. Develop a marketing strategy report for {{ company_name }} aiming to {{ company_goal }}"],variables:[{name:"company_name",value:"XYZ Company"},{name:"company_goal",value:"Increase top-line revenue"}]},{prompt:['You are a helpful and friendly customer support chatbot. Answer the users question "{{ user_question }}" clearly, based on the following documentation: {{ documentation }}'],variables:[{name:"user_question",value:"Is MLflow open source?"},{name:"documentation",value:"MLflow is an open source platform for managing the end-to-end machine learning lifecycle."}]},{prompt:['Summarize the given text "{{ text }}" into a concise and coherent summary, capturing the main ideas and key points. Make sure that the summary does not exceed {{ word_count }} words.'],variables:[{name:"text",value:"Although C. septempunctata larvae and adults mainly eat aphids, they also feed on Thysanoptera, Aleyrodidae, on the larvae of Psyllidae and Cicadellidae, and on eggs and larvae of some beetles and butterflies. There are one or two generations per year. Adults overwinter in ground litter in parks, gardens and forest edges and under tree bark and rocks. C. septempunctata has a broad ecological range, generally living wherever there are aphids for it to eat. This includes, amongst other biotopes, meadows, fields, Pontic\u2013Caspian steppe, parkland, gardens, Western European broadleaf forests and mixed forests. In the United Kingdom, there are fears that the seven-spot ladybird is being outcompeted for food by the harlequin ladybird. An adult seven-spot ladybird may reach a body length of 7.6\u201312.7 mm (0.3\u20130.5 in). Their distinctive spots and conspicuous colours warn of their toxicity, making them unappealing to predators. The species can secrete a fluid from joints in their legs which gives them a foul taste. A threatened ladybird may both play dead and secrete the unappetising substance to protect itself. The seven-spot ladybird synthesizes the toxic alkaloids, N-oxide coccinelline and its free base precoccinelline; depending on sex and diet, the spot size and coloration can provide some indication of how toxic the individual insect is to potential predators."},{name:"word_count",value:"75"}]},{prompt:["Generate a list of ten titles for my book. The book is about {{ topic }}. Each title should be between {{ word_range }} words long.","### Examples of great titles ###","{{ examples }}"],variables:[{name:"topic",value:"my journey as an adventurer who has lived an unconventional life, meeting many different personalities and finally finding peace in gardening."},{name:"word_range",value:"two to five"},{name:"examples",value:'"Long walk to freedom", "Wishful drinking", "I know why the caged bird sings"'}]},{prompt:["Generate a SQL query from a user\u2019s question, using the information from the table.","Question: {{ user_question }}","Table Information: {{ table_information }}"],variables:[{name:"user_question",value:"Which product generated the most sales this month?"},{name:"table_information",value:"CREATE TABLE Sales (SaleID INT PRIMARY KEY, ProductID INT, SaleDate DATE, CustomerID INT, QuantitySold INT, UnitPrice DECIMAL(10, 2));"}]}],{TextArea:wt}=m.I,bt=e=>{let{isOpen:t,closeExamples:n,closeModal:r,updatePromptTemplate:s,updateInputVariableValue:i}=e;const{theme:o}=(0,p.u)();return(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodalexamples.tsx_42",verticalSizing:"maxed_out",visible:t,onCancel:r,title:(0,y.Y)("div",{children:(0,y.FD)(p.T.Title,{level:2,css:(0,a.AH)({marginTop:o.spacing.sm,marginBottom:o.spacing.xs},""),children:[(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodalexamples.tsx_48",css:(0,a.AH)({marginRight:o.spacing.sm,marginBottom:o.spacing.sm},""),icon:(0,y.Y)(c.A60,{}),onClick:n}),(0,y.Y)(X.A,{id:"KzC+38",defaultMessage:"Prompt template examples"})]})}),dangerouslySetAntdProps:{width:1200},children:yt.map((e=>(0,y.FD)("div",{css:(0,a.AH)({display:"flex",flexDirection:"column",gap:o.spacing.md},""),children:[(0,y.FD)("div",{css:(0,a.AH)({boxSizing:"border-box",border:`1px solid ${o.colors.actionDefaultBorderDefault}`,borderRadius:o.legacyBorders.borderRadiusMd,background:o.colors.backgroundPrimary,padding:o.spacing.md,margin:0,overflow:"hidden",display:"flex",flexDirection:"column",gap:`${o.spacing.xs}px`},""),children:[(0,y.FD)(p.T.Title,{level:4,children:[(0,y.Y)(X.A,{id:"GDzD7I",defaultMessage:"Prompt Template"}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodalexamples.tsx_90",type:"tertiary",size:"small",style:{float:"right"},onClick:()=>(e=>{s(e.prompt.join("\n")),e.variables.forEach((e=>{let{name:t,value:n}=e;i(t,n)})),n()})(e),children:(0,y.Y)(X.A,{id:"Np53PR",defaultMessage:"Try this template"})})]}),e.prompt.map((e=>(0,y.Y)(p.T.Paragraph,{children:e},e))),(0,y.Y)("div",{css:(0,a.AH)({marginTop:o.spacing.xs,marginBottom:o.spacing.xs,borderTop:`1px solid ${o.colors.border}`,opacity:.5},"")}),e.variables.map((e=>{let{name:t,value:n}=e;return(0,y.FD)("div",{children:[(0,y.Y)(p.T.Title,{level:4,children:t}),(0,y.Y)(p.T.Paragraph,{children:n})]},t)}))]}),(0,y.Y)(u.S,{})]},e.prompt.join("\n"))))})};const{TextArea:Yt}=m.I;var Ct={name:"1ll9bqd",styles:"cursor:default"};const It=e=>{let{evaluationMetadata:t,isEvaluating:n,isOutputDirty:s,evaluationOutput:i,evaluationError:o,evaluateButtonTooltip:l,disabled:d,onEvaluateClick:u,onCancelClick:m}=e;const{theme:g}=(0,p.u)(),h=(0,r.useMemo)((()=>t?n?null:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:g.spacing.xs,alignItems:"center"},""),children:[st.Ef in t&&(0,y.FD)(p.T.Hint,{size:"sm",children:[Math.round(Number(t[st.Ef]))," ms","MLFLOW_total_tokens"in t?",":""]}),st.jC in t&&(0,y.Y)(p.T.Hint,{size:"sm",children:(0,y.Y)(X.A,{id:"/nPZbt",defaultMessage:"{totalTokens} total tokens",values:{totalTokens:t[st.jC]}})})]}):null),[t,n,g]);return(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:(0,a.AH)({marginBottom:g.spacing.md},""),children:[(0,y.Y)(c.paO,{title:l,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationcreatepromptrunoutput.tsx_85","data-testid":"button-evaluate",icon:(0,y.Y)(c.udU,{}),onClick:u,disabled:d,loading:n,children:(0,y.Y)(X.A,{id:"72uGpl",defaultMessage:"Evaluate"})})}),n&&(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationcreatepromptrunoutput.tsx_99","data-testid":"button-cancel",icon:(0,y.Y)(c.wFz,{}),onClick:m,css:(0,a.AH)({marginLeft:g.spacing.sm},""),children:(0,y.Y)(X.A,{id:"CTEh+b",defaultMessage:"Cancel"})})]}),(0,y.FD)(c.D$Q.Label,{children:[(0,y.Y)(X.A,{id:"ydoHrJ",defaultMessage:"Output"}),s&&(0,y.Y)(c.paO,{title:(0,y.Y)(X.A,{id:"gc+GrI",defaultMessage:"Model, input data or prompt have changed since last evaluation of the output"}),children:(0,y.Y)(p.W,{css:(0,a.AH)({marginLeft:g.spacing.xs},"")})})]}),(0,y.Y)(c.D$Q.Hint,{children:(0,y.Y)(X.A,{id:"JtQQMn",defaultMessage:"This is the output generated by the LLM using the prompt template and input values defined above."})}),!o&&n&&(0,y.Y)("div",{css:(0,a.AH)({marginTop:g.spacing.sm},""),children:(0,y.Y)(c.QvX,{lines:5})}),!n&&(0,y.Y)(Yt,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationcreatepromptrunoutput.tsx_144",rows:5,css:Ct,"data-testid":"prompt-output",value:i,readOnly:!0}),!n&&o&&(0,y.Y)(c.D$Q.Message,{message:o,type:"error"}),(0,y.Y)("div",{css:(0,a.AH)({marginTop:g.spacing.sm},""),children:h})]})};var At=n(17111);const kt=e=>{let{routeName:t,routeType:n,compiledPrompt:a,inputValues:r,parameters:s,outputColumn:i,rowKey:o,run:l}=e;return(e=>{let{routeName:t,routeType:n,compiledPrompt:a,inputValues:r,parameters:s,outputColumn:i,rowKey:o,run:l}=e;return async(e,i)=>{const{modelGateway:d}=i();d.modelGatewayRoutesLoading.loading||0!==Object.keys(d.modelGatewayRoutes).length||await e(Et());const c=i().modelGateway.modelGatewayRoutes[`${n}:${t}`];if(!c){const e=`MLflow deployment endpoint ${t} does not exist anymore!`;throw T.A.logErrorAndNotifyUser(e),new Error(e)}const u={inputText:a,parameters:s};return e({type:"EVALUATE_PROMPT_TABLE_VALUE",payload:at.queryModelGatewayRoute(c,u),meta:{inputValues:r,run:l,compiledPrompt:a,rowKey:o,startTime:performance.now()}})}})({...{routeName:t,compiledPrompt:a,inputValues:r,parameters:s,outputColumn:i,rowKey:o,run:l},routeType:n})},Et=()=>async e=>e({type:"SEARCH_MLFLOW_DEPLOYMENTS_MODEL_ROUTES",payload:Xe.x.gatewayProxyGet({gateway_path:"api/2.0/endpoints/"}),meta:{id:(0,l.yk)()}}),St=e=>e instanceof tt?e.getGatewayErrorMessage():e instanceof ke.s?e.getMessageField():e.message;const{TextArea:Mt}=m.I;var Rt={name:"1voqbl3",styles:"margin-top:0;font-weight:normal"},Tt={name:"t691yx",styles:"display:grid;grid-template-columns:300px 1fr;gap:48px"},Dt={name:"1d3w5wq",styles:"width:100%"},Ft={name:"1eoy87d",styles:"display:flex;justify-content:space-between"};const Lt=e=>{let{isOpen:t,closeModal:n,runBeingDuplicated:o,visibleRuns:l=[],refreshRuns:d}=e;const[g]=(0,$.z)(),{theme:h}=(0,p.u)(),{parameters:f,updateParameter:v}=dt(),[,_]=ce(),[x,w]=(0,r.useState)(""),[b,Y]=(0,r.useState)(""),[C,I]=(0,r.useState)(!1),[A,k]=(0,r.useState)(!1),[E,S]=(0,r.useState)(null),[M,R]=(0,r.useState)(""),[D,F]=(0,r.useState)({}),[L,B]=(0,r.useState)(!1),[N,P]=(0,r.useState)(!1),H=(0,r.useRef)(null),O=(0,s.wA)();(0,r.useEffect)((()=>{O(Et()).catch((e=>{T.A.logErrorAndNotifyUser((null===e||void 0===e?void 0:e.message)||e)}))}),[O]);const U=(0,Fe.A)(),{updateInputVariables:V,inputVariables:K,inputVariableValues:G,updateInputVariableValue:z,inputVariableNameViolations:q,clearInputVariableValues:W}=(()=>{const[e,t]=(0,r.useState)((0,st.rl)("")),[n,a]=(0,r.useState)({namesWithSpaces:[]}),[s,i]=(0,r.useState)(st.kU),o=(0,r.useCallback)((()=>i({})),[]),l=(0,r.useMemo)((()=>(0,j.debounce)((e=>{t((t=>{const n=(0,st.rl)(e);return(0,j.isEqual)(n,t)?t:n})),a((0,st.Eb)(e))}),250)),[]),d=(0,r.useCallback)(((e,t)=>{i((n=>({...n,[e]:t})))}),[]),c=(0,r.useMemo)((()=>(0,j.fromPairs)(Object.entries(s).filter((t=>{let[n]=t;return e.includes(n)})))),[s,e]);return{updateInputVariables:l,inputVariables:e,inputVariableValues:c,updateInputVariableValue:d,inputVariableNameViolations:n,clearInputVariableValues:o}})(),{handleAddVariableToTemplate:Q,savePromptTemplateInputRef:J,promptTemplate:Z,updatePromptTemplate:ee}=vt();(0,r.useEffect)((()=>{t&&!o&&Y((0,rt.Lr)())}),[t,o]),(0,r.useEffect)((()=>{V(Z)}),[Z,V]),(0,r.useEffect)((()=>{if(o){const{promptTemplate:e,routeName:t,parameters:n}=(0,st.vC)(o);(0,st.xP)(o),e&&ee(e),n.temperature&&v("temperature",n.temperature),n.max_tokens&&v("max_tokens",n.max_tokens),t&&w(t),R(""),B(!1);const a=(0,rt.gK)(o.runName,(0,j.compact)(l.map((e=>{let{runName:t}=e;return t}))));Y(a),W()}}),[o,W,v,ee,l]);const te=(0,s.d4)((e=>{let{modelGateway:t}=e;return t.modelGatewayRoutes})),ne=(0,r.useMemo)((()=>(0,j.sortBy)(Object.values(te),"name")),[te]),ae=(0,s.d4)((e=>{let{modelGateway:t}=e;return t.modelGatewayRoutesLoading.loading}));(0,r.useEffect)((()=>{M&&B(!0)}),[G,Z,f,x]);const re=(0,r.useCallback)((()=>{const e=te[x],t=Math.random().toString(36);if(H.current=t,!e)throw new Error("No model route found!");S(null),k(!0);const n=(0,st.TG)(Z,G),a=performance.now();at.queryModelGatewayRoute(e,{inputText:n,parameters:f}).then((e=>{if(H.current===t){const{text:n,metadata:r}=e,s=performance.now()-a;R(n);const i={...r,latency:s},o=Object.entries(i).reduce(((e,t)=>{let[n,a]=t;return{...e,[`MLFLOW_${n}`]:a}}),{});F(o),B(!1),k(!1),H.current===t&&(H.current=null)}})).catch((e=>{const n=St(e),a=U.formatMessage({id:"nyHcAz",defaultMessage:'MLflow deployment returned the following error: "{errorMessage}"'},{errorMessage:n});T.A.displayGlobalErrorNotification(a),k(!1),S(a),H.current===t&&(H.current=null)}))}),[G,te,f,Z,x,U]),se=(0,r.useCallback)((()=>{H.current&&(k(!1),H.current=null)}),[k]),ie=U.formatMessage({id:"AyceYA",defaultMessage:"Served LLM model"}),oe=U.formatMessage({id:"pTDM+x",defaultMessage:"Select LLM model endpoint"}),le=Z.trim().length>0,de=(0,r.useMemo)((()=>K.every((e=>{var t;return null===(t=G[e])||void 0===t?void 0:t.trim()}))),[K,G]),ue=b.trim().length>0,pe=x&&le&&de,me=Boolean(x&&le&&de&&M&&!L&&K.length>0&&ue&&!E),ge=(0,r.useMemo)((()=>x?le?de?M?L?U.formatMessage({id:"/K6wBr",defaultMessage:"Input data or prompt template have changed since last evaluation of the output"}):0===K.length?U.formatMessage({id:"z+0t/R",defaultMessage:"You need to define at least one input variable"}):ue?null:U.formatMessage({id:"dYbJha",defaultMessage:"Please provide run name"}):U.formatMessage({id:"gUhc5g",defaultMessage:"You need to evaluate the resulting output first"}):U.formatMessage({id:"flJNTA",defaultMessage:"You need to provide values for all defined inputs"}):U.formatMessage({id:"dHAuWp",defaultMessage:"You need to provide a prompt template"}):U.formatMessage({id:"4HbEVz",defaultMessage:"You need to select a served model endpoint using dropdown first"})),[de,K.length,U,L,M,le,x,ue]),he=(0,r.useMemo)((()=>x?le?de?null:U.formatMessage({id:"flJNTA",defaultMessage:"You need to provide values for all defined inputs"}):U.formatMessage({id:"dHAuWp",defaultMessage:"You need to provide a prompt template"}):U.formatMessage({id:"4HbEVz",defaultMessage:"You need to select a served model endpoint using dropdown first"})),[de,U,le,x]);if(t&&N)return(0,y.Y)(bt,{isOpen:t&&N,closeExamples:()=>P(!1),closeModal:n,updatePromptTemplate:ee,updateInputVariableValue:z});return(0,y.FD)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_541",verticalSizing:"maxed_out",visible:t,onCancel:n,onOk:n,footer:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:h.spacing.sm,justifyContent:"flex-end"},""),children:[(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_589",onClick:n,children:(0,y.Y)(X.A,{id:"mqTFL+",defaultMessage:"Cancel"})}),(0,y.Y)(c.paO,{title:ge,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_596",onClick:()=>{var e,t;I(!0);const a=null===(e=te[x])||void 0===e?void 0:e.name,r={...f,route_type:null===(t=te[x])||void 0===t?void 0:t.type},s=(0,st.TG)(Z,G);O((0,i.$v)({experimentId:g,promptTemplate:Z,modelInput:s,modelParameters:r,modelRouteName:a,promptParameters:G,modelOutput:M,runName:b,modelOutputParameters:D})).then((()=>{d(),n(),I(!1),_("ARTIFACT")})).catch((e=>{T.A.logErrorAndNotifyUser((null===e||void 0===e?void 0:e.message)||e),I(!1)}))},"data-testid":"button-create-run",type:"primary",disabled:!me,children:(0,y.Y)(X.A,{id:"8Uwpjw",defaultMessage:"Create run"})})})]}),title:(0,y.FD)("div",{children:[(0,y.Y)(p.T.Title,{level:2,css:(0,a.AH)({marginTop:h.spacing.sm,marginBottom:h.spacing.xs},""),children:(0,y.Y)(X.A,{id:"sLhmy7",defaultMessage:"New run"})}),(0,y.Y)(p.T.Hint,{css:Rt,children:"Create a new run using a large-language model by giving it a prompt template and model parameters"})]}),dangerouslySetAntdProps:{width:1200},children:[(0,y.FD)("div",{css:Tt,children:[(0,y.FD)("div",{children:[(0,y.Y)(c.D$Q.Label,{htmlFor:"selected_model",css:(0,a.AH)({marginBottom:h.spacing.sm},""),children:ie}),(0,y.Y)("div",{css:(0,a.AH)({marginBottom:h.spacing.lg,display:"flex",alignItems:"center"},""),children:(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_597",label:ie,modal:!1,value:x?[(fe=x,fe.includes(":")?fe.split(":")[1]:fe)]:void 0,children:[(0,y.Y)(c.gGe,{id:"selected_model",css:Dt,allowClear:!1,placeholder:oe,withInlineLabel:!1}),(0,y.Y)(c.dn6,{loading:ae,maxHeight:400,matchTriggerWidth:!0,children:!ae&&(0,y.Y)(c.HI_,{children:(0,y.Y)(c.dhl,{autoFocus:!0,children:ne.map((e=>(0,y.FD)(c.crD,{value:e.key,onChange:e=>{w(e)},checked:x===e.key,children:[e.name,e.mlflowDeployment&&(0,y.Y)(c.b5C,{children:e.mlflowDeployment.model.name})]},e.key)))})})})]})}),x&&(0,y.Y)(gt,{parameters:f,updateParameter:v}),(0,y.Y)("div",{css:Bt.formItem,children:(0,y.FD)(y.FK,{children:[(0,y.FD)(c.D$Q.Label,{htmlFor:"new_run_name",children:[(0,y.Y)(X.A,{id:"Zqj4VA",defaultMessage:"New run name"}),!b.trim()&&(0,y.Y)(c.D$Q.Message,{type:"error",message:U.formatMessage({id:"dYbJha",defaultMessage:"Please provide run name"})})]}),(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_638",id:"new_run_name","data-testid":"run-name-input",required:!0,value:b,onChange:e=>Y(e.target.value)})]})})]}),(0,y.FD)("div",{children:[(0,y.FD)("div",{css:Bt.formItem,children:[(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:Ft,children:[(0,y.Y)(c.D$Q.Label,{htmlFor:"prompt_template",children:(0,y.Y)(X.A,{id:"uBTAtY",defaultMessage:"Prompt Template"})}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_695",onClick:()=>P(!0),style:{marginLeft:"auto"},size:"small",children:(0,y.Y)(X.A,{id:"13vJft",defaultMessage:"View Examples"})})]}),(0,y.Y)(c.D$Q.Hint,{children:(0,y.Y)(X.A,{id:"gC2Kxm",defaultMessage:"Give instructions to the model. Use '{{ }}' or the \"Add new variable\" button to add variables to your prompt."})})]}),(0,y.Y)(Mt,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_678",id:"prompt_template",autoSize:{minRows:3},"data-testid":"prompt-template-input",value:Z,onChange:e=>ee(e.target.value),ref:J}),(0,y.Y)(xt,{violations:q})]}),K.map((e=>(0,y.Y)("div",{css:Bt.formItem,children:(0,y.FD)(y.FK,{children:[(0,y.Y)(c.D$Q.Label,{htmlFor:e,children:(0,y.Y)("span",{children:e})}),(0,y.Y)(Mt,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_694",id:e,autoSize:!0,value:G[e]?G[e]:"",onChange:t=>z(e,t.target.value)})]})},e))),(0,y.Y)("div",{css:(0,a.AH)({marginBottom:2*h.spacing.md},""),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationcreatepromptrunmodal.tsx_736",icon:(0,y.Y)(c.c11,{}),onClick:Q,children:(0,y.Y)(X.A,{id:"71hvMu",defaultMessage:"Add new variable"})})}),(0,y.Y)(It,{evaluateButtonTooltip:he,evaluationMetadata:D,evaluationOutput:M,disabled:!pe,isEvaluating:A,isOutputDirty:L,onCancelClick:se,onEvaluateClick:re,evaluationError:E})]})]}),C&&(0,y.Y)("div",{css:(0,a.AH)({inset:0,backgroundColor:h.colors.overlayOverlay,position:"absolute",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1},""),children:(0,y.Y)(p.S,{})})]});var fe},Bt={formItem:{marginBottom:16}},Nt=r.createContext({createNewRun:()=>{}}),Pt=e=>{let{children:t,visibleRuns:n,refreshRuns:a}=e;const[s,i]=(0,r.useState)(!1),[o,l]=(0,r.useState)(null),d=(0,r.useMemo)((()=>({createNewRun:e=>{i(!0),l(e||null)}})),[]);return(0,y.FD)(Nt.Provider,{value:d,children:[t,(0,W.Ii)()&&(0,y.Y)(Lt,{visibleRuns:n,isOpen:s,closeModal:()=>i(!1),runBeingDuplicated:o,refreshRuns:a})]})},Ht=()=>(0,r.useContext)(Nt);var Ot=n(3546),Ut=n(83028),Vt=n(21879);const Kt=r.memo((e=>{let{searchFacetsState:t,experimentId:n,runsData:i,viewState:o,updateViewState:l,onDownloadCsv:d,requestError:m,additionalControls:g,refreshRuns:h,viewMaximized:f,autoRefreshEnabled:v=!1,hideEmptyCharts:_=!1,areRunsGrouped:x=!1}=e;const w=(0,Ot.Px)(),[b,Y]=ce(),C=(0,Vt.e)(),I=(0,$.z)().length>1,{startTime:A,lifecycleFilter:k,datasetsFilter:E,searchFilter:S}=t,M=b,R=(0,Fe.A)(),{createNewRun:T}=Ht(),[D,F]=(0,r.useState)(!1),{theme:L}=(0,p.u)(),B=(0,r.useMemo)((()=>(0,fe.XU)(R)),[R]),N=k===ie.gy.ACTIVE?R.formatMessage({id:"0pdAuV",defaultMessage:"Active"}):R.formatMessage({id:"yJpGwW",defaultMessage:"Deleted"}),P=R.formatMessage({id:"+fb25b",defaultMessage:"Time created"}),H=void 0===M||"ARTIFACT"===M,O=(0,s.d4)((e=>e.entities.datasetsByExperimentId[n])),U=void 0!==O;return(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:L.spacing.sm,justifyContent:"space-between",[L.responsive.mediaQueries.xs]:{flexDirection:"column"}},""),children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:L.spacing.sm,alignItems:"center",flexWrap:"wrap"},""),children:[("ARTIFACT"!==b||(0,W.GV)())&&(0,y.FD)(c.d98,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_184",name:"runs-view-mode",value:b,onChange:e=>{let{target:t}=e;const{value:n}=t;b!==n&&Y(n)},children:[(0,y.Y)(c.EPn,{value:"TABLE",children:(0,y.Y)(u.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_201",content:R.formatMessage({id:"AxyQXa",defaultMessage:"Table view"}),children:(0,y.Y)(u.L,{})})}),(0,y.Y)(c.EPn,{value:"CHART",children:(0,y.Y)(u.T,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_211",content:R.formatMessage({id:"Q6oN2U",defaultMessage:"Chart view"}),children:(0,y.Y)(c.SCH,{})})}),(0,W.GV)()&&(0,y.Y)(c.EPn,{value:"ARTIFACT",disabled:x,children:(0,y.Y)(u.T,{componentId:"mlflow.experiment_page.mode.artifact",content:x?R.formatMessage({id:"YOp3/x",defaultMessage:"Unavailable when runs are grouped"}):R.formatMessage({id:"LTbwXS",defaultMessage:"Artifact evaluation"}),children:(0,y.Y)(c.KbA,{})})})]}),(0,y.Y)(ze,{runsData:i,searchFilter:S,onSearchFilterChange:e=>{w({searchFilter:e})},onClear:()=>{w((0,Ut.G)())},requestError:m}),(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_217",label:P,value:"ALL"!==A?[B[A]]:[],children:[(0,y.Y)(c.gGe,{allowClear:"ALL"!==A,onClear:()=>{w({startTime:"ALL"})},"data-test-id":"start-time-select-dropdown"}),(0,y.Y)(c.dn6,{children:(0,y.Y)(c.HI_,{children:Object.keys(B).map((e=>(0,y.Y)(c.crD,{checked:e===A,title:B[e],"data-test-id":`start-time-select-${e}`,value:e,onChange:()=>{w({startTime:e})},children:B[e]},e)))})})]}),(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_248",label:R.formatMessage({id:"iLFoPb",defaultMessage:"State"}),value:[N],children:[(0,y.Y)(c.gGe,{allowClear:!1,"data-testid":"lifecycle-filter"}),(0,y.Y)(c.dn6,{children:(0,y.FD)(c.HI_,{children:[(0,y.Y)(c.crD,{checked:k===ie.gy.ACTIVE,"data-testid":"active-runs-menu-item",value:ie.gy.ACTIVE,onChange:()=>{w({lifecycleFilter:ie.gy.ACTIVE})},children:(0,y.Y)(X.A,{id:"0pdAuV",defaultMessage:"Active"})},ie.gy.ACTIVE),(0,y.Y)(c.crD,{checked:k===ie.gy.DELETED,"data-testid":"deleted-runs-menu-item",value:ie.gy.DELETED,onChange:()=>{w({lifecycleFilter:ie.gy.DELETED})},children:(0,y.Y)(X.A,{id:"yJpGwW",defaultMessage:"Deleted"})},ie.gy.DELETED)]})})]}),(0,y.Y)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_289",label:R.formatMessage({id:"IZ95jb",defaultMessage:"Datasets"}),value:E.map((e=>e.name)),multiSelect:!0,children:(0,y.FD)(c.paO,{title:!U&&(0,y.Y)(X.A,{id:"v8UFxB",defaultMessage:"No datasets were recorded for this experiment's runs."}),children:[(0,y.Y)(c.gGe,{allowClear:!0,onClear:()=>w({datasetsFilter:[]}),"data-test-id":"datasets-select-dropdown",showTagAfterValueCount:1,disabled:!U}),U&&(0,y.Y)(c.dn6,{maxHeight:600,children:(0,y.Y)(c.HI_,{children:(0,y.Y)(c.dhl,{children:O.map((e=>(0,y.FD)(c.jTC,{checked:E.some((t=>(0,$e.R)(t,e))),title:e.name,"data-test-id":`dataset-dropdown-${e.name}`,value:e.name,onChange:()=>(e=>{const t=E.some((t=>(0,$e.R)(t,e)))?E.filter((t=>!(0,$e.R)(t,e))):[...E,e];w({datasetsFilter:t})})(e),children:[e.name," (",e.digest,")"," ",e.context&&(0,y.Y)(c.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_329",css:(0,a.AH)({textTransform:"capitalize",marginRight:L.spacing.xs},""),children:e.context})]},e.name+e.digest+e.context)))})})})]})}),g]}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:L.spacing.sm,alignItems:"flex-start"},""),children:[(0,y.FD)(c.rId.Root,{modal:!1,children:[(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_338",icon:(0,y.Y)(c.ssM,{}),"aria-label":R.formatMessage({id:"E5gbt5",defaultMessage:"More options"})})}),(0,y.FD)(c.rId.Content,{children:[(0,y.FD)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_362",className:"csv-button",onClick:d,children:[(0,y.Y)(c.rId.IconWrapper,{children:(0,y.Y)(c.s3U,{})}),`Download ${i.runInfos.length} runs`]}),(0,y.Y)(c.rId.Separator,{}),(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_382",checked:_,onClick:()=>C((e=>({...e,hideEmptyCharts:!e.hideEmptyCharts}))),children:[(0,y.Y)(c.rId.ItemIndicator,{}),(0,y.Y)(X.A,{id:"lZPbxb",defaultMessage:"Hide charts with no data"})]}),(0,W.Hn)()&&(0,y.FD)(y.FK,{children:[(0,y.Y)(c.rId.Separator,{}),(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_402",checked:v,onClick:()=>C((e=>({...e,autoRefreshEnabled:!e.autoRefreshEnabled}))),children:[(0,y.Y)(c.rId.ItemIndicator,{}),(0,y.Y)(X.A,{id:"YamyaP",defaultMessage:"Auto-refresh"})]})]})]})]}),(0,y.Y)(We,{isOpen:D,closeModal:()=>F(!1),experimentId:n}),H&&(0,y.Y)(c.paO,{title:R.formatMessage({id:"eZOxx1",defaultMessage:"Toggle the preview sidepane"}),useAsLabel:!0,children:(0,y.Y)(Le.k,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_403",pressed:o.previewPaneVisible,icon:(0,y.Y)(c.CPw,{}),onClick:()=>l({previewPaneVisible:!o.previewPaneVisible})})}),!(0,W.Hn)()&&(0,y.Y)(Pe,{refreshRuns:h}),!I&&(0,y.FD)(c.rId.Root,{children:[(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_415",icon:(0,y.Y)(c.c11,{}),children:(0,y.Y)(X.A,{id:"PEYesH",defaultMessage:"New run"})})}),(0,y.FD)(c.rId.Content,{children:[(0,W.Ii)()&&(0,y.FD)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_461",onSelect:()=>T(),children:[" ",(0,y.Y)(X.A,{id:"UmwZQv",defaultMessage:"using Prompt Engineering"}),(0,y.Y)(Qe.W,{})]}),(0,y.FD)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrolsfilters.tsx_469",onSelect:()=>F(!0),children:[" ",(0,y.Y)(X.A,{id:"vNRmQa",defaultMessage:"using Notebook"})]})]})]})]})]})}));var Gt=n(35286);const zt="GROUP",$t=(0,fe.GF)(zt,ie.RO.ATTRIBUTES),jt=(0,fe.GF)(zt,ie.RO.PARAMS),qt=(0,fe.GF)(zt,ie.RO.METRICS),Wt=(0,fe.GF)(zt,ie.RO.TAGS),Qt=(e,t)=>e.filter((e=>e.toLowerCase().includes(t.toLowerCase()))),Jt=(e,t)=>{if(!t)return e;const n=e.toLowerCase().indexOf(t.toLowerCase()),a=e.substring(0,n),r=e.substring(n,n+t.length),s=e.substring(n+t.length);return n>-1?(0,y.FD)("span",{children:[a,(0,y.Y)("strong",{children:r}),s]}):e},Zt=r.memo((e=>{let{runsData:t,columnSelectorVisible:n,onChangeColumnSelectorVisible:s,selectedColumns:i}=e;const o=(0,Vt.e)(),l=(0,$.z)(),[d,u]=(0,r.useState)(""),{theme:g}=(0,p.u)(),h=(0,r.useRef)(null),f=(0,r.useRef)(null),v=(0,r.useRef)(null),_=(0,r.useMemo)((()=>(e=>{const t=[ie.qo.USER,ie.qo.SOURCE,ie.qo.VERSION,ie.qo.MODELS,ie.qo.DESCRIPTION];return e&&t.unshift(ie.qo.EXPERIMENT_NAME),t.unshift(ie.qo.DATASET),t})(l.length>1)),[l.length]),x=(0,r.useCallback)((e=>o((t=>{const n=e(t.selectedColumns),a=Array.from(new Set(n));return{...t,selectedColumns:a}}))),[o]),w=(0,r.useMemo)((()=>T.A.getVisibleTagKeyList(t.tagsList)),[t]),b=(0,r.useMemo)((()=>({[ie.RO.ATTRIBUTES]:_.map((e=>(0,fe.GF)(ie.RO.ATTRIBUTES,e))),[ie.RO.PARAMS]:t.paramKeyList.map((e=>(0,fe.GF)(ie.RO.PARAMS,e))),[ie.RO.METRICS]:t.metricKeyList.map((e=>(0,fe.GF)(ie.RO.METRICS,e))),[ie.RO.TAGS]:w.map((e=>(0,fe.GF)(ie.RO.TAGS,e)))})),[t,_,w]),Y=(0,r.useMemo)((()=>{const e=[],n=Qt(_,d),a=Qt(t.paramKeyList,d),r=Qt(t.metricKeyList,d),s=Qt(w,d);return n.length&&e.push({key:$t,title:"Attributes",children:n.map((e=>({key:(0,fe.GF)(ie.RO.ATTRIBUTES,e),title:Jt(e,d)})))}),r.length&&e.push({key:qt,title:`Metrics (${r.length})`,children:r.map((e=>{var t;const n=Gt.g[e];return{key:(0,fe.GF)(ie.RO.METRICS,e),title:Jt(null!==(t=null===n||void 0===n?void 0:n.displayName)&&void 0!==t?t:e,d)}}))}),a.length&&e.push({key:jt,title:`Parameters (${a.length})`,children:a.map((e=>({key:(0,fe.GF)(ie.RO.PARAMS,e),title:Jt(e,d)})))}),s.length&&e.push({key:Wt,title:`Tags (${s.length})`,children:s.map((e=>({key:(0,fe.GF)(ie.RO.TAGS,e),title:e})))}),e}),[_,d,t,w]),C=(0,r.useCallback)(((e,t)=>{x(e?e=>e.filter((e=>!t.includes(e))):e=>[...e,...t])}),[x]),I=(0,r.useCallback)(((e,t)=>{x(t?t=>t.filter((t=>t!==e)):t=>[...t,e])}),[x]);(0,r.useEffect)((()=>{n&&(u(""),requestAnimationFrame((()=>{var e,t;null===f||void 0===f||null===(e=f.current)||void 0===e||e.scrollTo(0,0),null===(t=h.current)||void 0===t||t.focus({preventScroll:!0}),v.current&&v.current.scrollIntoView({block:"nearest",behavior:"smooth"})})))}),[n]);const A=(0,r.useCallback)(((e,t)=>{let{node:{key:n,checked:a}}=t;if((0,fe.tG)(n.toString(),zt)){const e=(0,fe.dz)(n.toString(),zt),t=b[e];t&&C(a,Qt(t,d))}else I(n.toString(),a)}),[b,C,I,d]),k=(0,r.useCallback)((e=>{if("ArrowDown"===e.key){const e=(null===(t=f.current)||void 0===t?void 0:t.querySelector('[role="tree"] input'))||null;e&&e.focus()}var t}),[]),E=(0,y.FD)("div",{css:(0,a.AH)({backgroundColor:g.colors.backgroundPrimary,width:400,border:"1px solid",borderColor:g.colors.border,[g.responsive.mediaQueries.xs]:{width:"100vw"}},""),onKeyDown:e=>{var t;"Escape"===e.key&&(s(!1),null===(t=v.current)||void 0===t||t.focus())},children:[(0,y.Y)("div",{css:e=>({padding:e.spacing.md}),children:(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscolumnselector.tsx_300",value:d,prefix:(0,y.Y)(m.S,{}),placeholder:"Search columns",allowClear:!0,ref:h,onChange:e=>{u(e.target.value)},onKeyDown:k})}),(0,y.Y)("div",{ref:f,css:(0,a.AH)({maxHeight:480,overflowY:"scroll",overflowX:"hidden",paddingBottom:g.spacing.md,"span[title]":{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden"},[g.responsive.mediaQueries.xs]:{maxHeight:"calc(100vh - 100px)"}},""),children:(0,y.Y)(c.PH6,{"data-testid":"column-selector-tree",mode:"checkable",dangerouslySetAntdProps:{checkedKeys:i,onCheck:A},defaultExpandedKeys:[$t,jt,qt,Wt],treeData:Y})})]});return(0,y.Y)(c.msM,{overlay:E,placement:"bottomLeft",trigger:["click"],visible:n,onVisibleChange:s,children:(0,y.FD)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscolumnselector.tsx_315",ref:v,style:{display:"flex",alignItems:"center"},"data-testid":"column-selection-dropdown",icon:(0,y.Y)(c.jng,{}),children:[(0,y.Y)(X.A,{id:"ny+fBZ",defaultMessage:"Columns"})," ",(0,y.Y)(c.D3D,{})]})})})),Xt=e=>{let{viewState:t,runsAreGrouped:n,hideBorder:a=!0}=e;const[,r]=(0,Ot.sR)(),{theme:s}=(0,p.u)(),[i,o]=ce(),{classNamePrefix:l}=(0,p.u)(),d=i||le(),u=((0,W.GV)()?["TABLE","CHART","ARTIFACT"]:["TABLE","CHART"]).includes(d)?"RUNS":d,m=1===r.length?r[0]:void 0;return(0,y.FD)(c.Y6f,{dangerouslyAppendEmotionCSS:{[`.${l}-tabs-nav`]:{marginBottom:0,"::before":{display:a?"none":"block"}}},activeKey:u,onChange:e=>{const t=e;if(u!==t)return"RUNS"===t?o("TABLE"):void o(t,m)},children:[(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.Y)("span",{"data-testid":"experiment-runs-mode-switch-combined",children:(0,y.Y)(X.A,{id:"qpFaad",defaultMessage:"Runs"})})},"RUNS"),m&&(0,W.Dz)()&&(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.FD)("span",{"data-testid":"experiment-runs-mode-switch-models",children:[(0,y.Y)(X.A,{id:"b8rdDM",defaultMessage:"Models"}),(0,y.Y)(Qe.W,{})]})},"MODELS"),(0,y.Y)(c.Y6f.TabPane,{disabled:(0,W.GV)()||n,tab:(0,y.Y)(c.paO,{title:!(0,W.GV)()&&n?(0,y.Y)(X.A,{id:"YOp3/x",defaultMessage:"Unavailable when runs are grouped"}):void 0,children:(0,y.FD)("span",{"data-testid":"experiment-runs-mode-switch-evaluation",css:(0,W.GV)()&&{display:"inline-flex",alignItems:"center",gap:s.spacing.xs},children:[(0,y.Y)(X.A,{id:"SCqKp8",defaultMessage:"Evaluation"}),(0,W.GV)()?(0,y.Y)(c.UyZ,{popoverProps:{maxWidth:350},iconProps:{style:{marginRight:0}},children:(0,y.Y)(X.A,{id:"sFPlmE",defaultMessage:'Accessing artifact evaluation by "Evaluation" tab is being discontinued. In order to use this feature, use <link>"Artifacts evaluation" mode in Runs tab</link> instead.',values:{link:e=>"ARTIFACT"===i?e:(0,y.Y)(p.T.Link,{componentId:"mlflow.experiment_page.evaluation_tab_migration_info_link",onClick:()=>o("ARTIFACT",m),children:e})}})}):(0,y.Y)(Qe.W,{})]})})},"ARTIFACT"),(0,W.XK)()&&(0,y.Y)(c.Y6f.TabPane,{tab:(0,y.Y)("span",{"data-testid":"experiment-runs-mode-switch-traces",children:(0,y.Y)(X.A,{id:"AGQOPV",defaultMessage:"Traces"})})},"TRACES")]})};var en=n(62862),tn=n(12772);const nn=(0,it.YK)({minimum:{id:"iT8ODo",defaultMessage:"Minimum"},maximum:{id:"au61Yy",defaultMessage:"Maximum"},average:{id:"/LfvdB",defaultMessage:"Average"},attributes:{id:"cy9EfG",defaultMessage:"Attributes"},tags:{id:"LEiN8m",defaultMessage:"Tags"},params:{id:"i9OADf",defaultMessage:"Params"},dataset:{id:"RtKhwd",defaultMessage:"Dataset"},noParams:{id:"Q73eXs",defaultMessage:"No params"},noTags:{id:"jHWRLw",defaultMessage:"No tags"},aggregationTooltip:{id:"fKx4kG",defaultMessage:"Aggregation: {value}"},noResults:{id:"aaVp/T",defaultMessage:"No results"}});var an={name:"9q39xd",styles:"min-width:32px"},rn={name:"o5v4ro",styles:"max-height:400px;overflow-y:scroll"};const sn=e=>{let{runsData:t,onChange:n,groupBy:s,useGroupedValuesInCharts:i,onUseGroupedValuesInChartsChange:o}=e;const l=(0,Fe.A)(),d=(0,r.useRef)(null),u=(0,r.useRef)(null),g=(0,r.useRef)(null),h=(0,r.useRef)(null),f=l.formatMessage(nn.minimum),v=l.formatMessage(nn.maximum),_=l.formatMessage(nn.average),x=l.formatMessage(nn.dataset),w=(0,r.useMemo)((()=>(0,j.uniq)((0,j.values)(t.tagsList).flatMap((e=>(0,j.keys)(e).filter((e=>!e.startsWith(Ye.nt))))))),[t.tagsList]),{aggregateFunction:b=tn.it.Average,groupByKeys:Y=[]}=s||{},C={min:f,max:v,average:_}[b],{theme:I}=(0,p.u)(),[A,k]=(0,r.useState)("");(0,r.useEffect)((()=>{requestAnimationFrame((()=>{h.current.focus()}))}),[]);const E=w.filter((e=>e.toLowerCase().includes(A.toLowerCase()))),S=t.paramKeyList.filter((e=>e.toLowerCase().includes(A.toLowerCase()))),M=(0,r.useMemo)((()=>!(0,j.isEmpty)((0,j.compact)(t.datasetsList))),[t.datasetsList])&&x.toLowerCase().includes(A.toLowerCase()),R=E.length>0||S.length>0||M,T=(0,r.useCallback)(((e,t,a)=>{if(a){const a=[...Y];a.some((n=>n.mode===e&&n.groupByData===t))||a.push({mode:e,groupByData:t}),n({aggregateFunction:b,groupByKeys:a})}else{const a=Y.filter((n=>!(n.mode===e&&n.groupByData===t)));if(!a.length)return void n(null);n({aggregateFunction:b,groupByKeys:a})}}),[b,Y,n]),D=(e,t)=>Y.some((n=>n.mode===e&&n.groupByData===t));return(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:I.spacing.xs,padding:I.spacing.sm},""),children:[(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_191",value:A,onChange:e=>k(e.target.value),prefix:(0,y.Y)(m.S,{}),placeholder:"Search",autoFocus:!0,ref:h,onKeyDown:e=>{if("ArrowDown"!==e.key&&"Tab"!==e.key)"Escape"!==e.key&&e.stopPropagation();else{const e=d.current||u.current||g.current;null===e||void 0===e||e.focus()}}}),(0,y.FD)(c.rId.Root,{children:[(0,y.Y)(c.paO,{placement:"right",title:(0,y.Y)(X.A,{...nn.aggregationTooltip,values:{value:C||b}}),children:(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_168",icon:(0,y.Y)(c.L64,{}),css:an,"aria-label":"Change aggregation function"})})}),(0,y.FD)(c.rId.Content,{align:"start",side:"right",children:[(0,W.Bh)()&&(0,y.FD)(y.FK,{children:[(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_233",disabled:!Y.length,checked:i,onCheckedChange:o,children:[(0,y.Y)(c.rId.ItemIndicator,{}),"Use grouping from the runs table in charts"]}),(0,y.Y)(c.rId.Separator,{})]}),(0,y.FD)(c.rId.RadioGroup,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_244",value:b,onValueChange:e=>{if((0,j.values)(tn.it).includes(e)){const t=e,a={...s,aggregateFunction:t};n(a)}},children:[(0,y.FD)(c.rId.RadioItem,{disabled:!Y.length,value:tn.it.Min,children:[(0,y.Y)(c.rId.ItemIndicator,{}),f]},tn.it.Min),(0,y.FD)(c.rId.RadioItem,{disabled:!Y.length,value:tn.it.Max,children:[(0,y.Y)(c.rId.ItemIndicator,{}),v]},tn.it.Max),(0,y.FD)(c.rId.RadioItem,{disabled:!Y.length,value:tn.it.Average,children:[(0,y.Y)(c.rId.ItemIndicator,{}),_]},tn.it.Average)]})]})]})]}),(0,y.FD)(c.rId.Group,{css:rn,children:[M&&(0,y.FD)(y.FK,{children:[(0,y.Y)(c.rId.Label,{children:(0,y.Y)(X.A,{...nn.attributes})}),x.toLowerCase().includes(A.toLowerCase())&&(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_280",checked:D(tn.uq.Dataset,"dataset"),ref:d,onCheckedChange:e=>T(tn.uq.Dataset,"dataset",e),children:[(0,y.Y)(c.rId.ItemIndicator,{}),x]},(0,en._p)(tn.uq.Dataset,"dataset",b)),(0,y.Y)(c.rId.Separator,{})]}),E.length>0&&(0,y.FD)(y.FK,{children:[(0,y.Y)(c.rId.Label,{children:(0,y.Y)(X.A,{...nn.tags})}),E.map(((e,t)=>{const n=(0,en._p)(tn.uq.Tag,e,b);return(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_302",checked:D(tn.uq.Tag,e),ref:0===t?u:void 0,onCheckedChange:t=>T(tn.uq.Tag,e,t),children:[(0,y.Y)(c.rId.ItemIndicator,{}),e]},n)})),!w.length&&(0,y.FD)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_314",disabled:!0,children:[(0,y.Y)(c.rId.ItemIndicator,{})," ",(0,y.Y)(X.A,{...nn.noTags})]}),(0,y.Y)(c.rId.Separator,{})]}),S.length>0&&(0,y.FD)(y.FK,{children:[(0,y.Y)(c.rId.Label,{children:(0,y.Y)(X.A,{...nn.params})}),S.map(((e,t)=>{const n=(0,en._p)(tn.uq.Param,e,b);return(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_330",checked:D(tn.uq.Param,e),ref:0===t?g:void 0,onCheckedChange:t=>T(tn.uq.Param,e,t),children:[(0,y.Y)(c.rId.ItemIndicator,{}),e]},n)})),!t.paramKeyList.length&&(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_342",disabled:!0,children:(0,y.Y)(X.A,{...nn.noParams})})]}),!R&&(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_349",disabled:!0,children:(0,y.Y)(X.A,{...nn.noResults})})]})]})};var on={name:"taqmwb",styles:"margin-left:4px;margin-right:0"};const ln=r.memo((e=>{let{runsData:t,groupBy:n,isLoading:r,onChange:s,useGroupedValuesInCharts:i,onUseGroupedValuesInChartsChange:o}=e;const{theme:l}=(0,p.u)(),d=(0,en.Zp)(n)||{aggregateFunction:tn.it.Average,groupByKeys:[]},u=d&&!(0,j.isEmpty)(d.groupByKeys);return(0,y.FD)(c.rId.Root,{modal:!1,children:[(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.FD)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_306",icon:(0,y.Y)(c.aTS,{}),style:{display:"flex",alignItems:"center"},"data-testid":"column-selection-dropdown",endIcon:(0,y.Y)(c.D3D,{}),children:[u?(0,y.Y)(X.A,{id:"AFI74W",defaultMessage:"Group by: {value}",values:{value:d.groupByKeys[0].groupByData}}):(0,y.Y)(X.A,{id:"fjM/KK",defaultMessage:"Group by"}),d.groupByKeys.length>1&&(0,y.FD)(c.vwO,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_426",css:on,children:["+",d.groupByKeys.length-1]}),n&&(0,y.Y)(c.htq,{"aria-hidden":"false",css:(0,a.AH)({color:l.colors.textPlaceholder,fontSize:l.typography.fontSizeSm,marginLeft:l.spacing.sm,":hover":{color:l.colors.actionTertiaryTextHover}},""),role:"button",onClick:()=>{s(null)},onPointerDownCapture:e=>{e.stopPropagation()}})]})}),(0,y.Y)(c.rId.Content,{children:r?(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunsgroupbyselector.tsx_436",children:(0,y.Y)(p.S,{})}):(0,y.Y)(sn,{groupBy:d,onChange:s,runsData:t,onUseGroupedValuesInChartsChange:o,useGroupedValuesInCharts:i})})]})}));var dn={name:"1kxd8xu",styles:"max-height:400px;overflow-y:auto"},cn={name:"1j5vobt",styles:"display:flex;align-items:center;gap:4px"};const un=e=>{let{sortOptions:t,orderByKey:n,orderByAsc:s,onOptionSelected:i}=e;const{theme:o}=(0,p.u)(),l=(0,Ot.Px)(),d=(0,Vt.e)(),u=(0,r.useRef)(null),[g,h]=(0,r.useState)(""),f=(0,r.useRef)(null),v=(0,r.useMemo)((()=>t.filter((e=>e.label.toLowerCase().includes(g.toLowerCase())))),[t,g]),_=e=>{l({orderByAsc:e}),i()};return(0,r.useEffect)((()=>{requestAnimationFrame((()=>{var e;null===(e=u.current)||void 0===e||e.focus()}))}),[]),(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:(0,a.AH)({padding:`${o.spacing.sm}px ${o.spacing.lg/2}px ${o.spacing.sm}px`,width:"100%",display:"flex",gap:o.spacing.xs},""),children:[(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunssortselectorv2.tsx_97",prefix:(0,y.Y)(m.S,{}),value:g,type:"search",onChange:e=>h(e.target.value),placeholder:"Search",autoFocus:!0,ref:u,onKeyDown:e=>{var t;"ArrowDown"!==e.key&&"Tab"!==e.key?e.stopPropagation():null===(t=f.current)||void 0===t||t.focus()}}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:o.spacing.xs},""),children:[(0,y.Y)(Le.k,{pressed:!s,icon:(0,y.Y)(c.ZLN,{}),componentId:"mlflow.experiment_page.sort_select_v2.sort_desc",onClick:()=>_(!1),"aria-label":"Sort descending","data-testid":"sort-select-desc"}),(0,y.Y)(Le.k,{pressed:s,icon:(0,y.Y)(c.Kpk,{}),componentId:"mlflow.experiment_page.sort_select_v2.sort_asc",onClick:()=>_(!0),"aria-label":"Sort ascending","data-testid":"sort-select-asc"})]})]}),(0,y.FD)(c.rId.Group,{css:dn,children:[v.map(((e,t)=>(0,y.FD)(c.rId.CheckboxItem,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunssortselectorv2.tsx_137",onClick:()=>(e=>{l({orderByKey:e}),d((t=>t.selectedColumns.includes(e)?t:{...t,selectedColumns:[...t.selectedColumns,e]})),i()})(e.value),checked:e.value===n,"data-test-id":`sort-select-${e.label}`,ref:0===t?f:void 0,children:[(0,y.Y)(c.rId.ItemIndicator,{}),(0,y.Y)("span",{css:cn,children:(0,se.fx)(e.label,50)})]},e.value))),!v.length&&(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunssortselectorv2.tsx_151",disabled:!0,children:(0,y.Y)(X.A,{id:"EkUD0b",defaultMessage:"No results"})})]})]})};var pn={name:"9q39xd",styles:"min-width:32px"};const mn=r.memo((e=>{let{metricKeys:t,paramKeys:n,orderByAsc:a,orderByKey:s}=e;const i=(0,Fe.A)(),[o,l]=(0,r.useState)(!1),{theme:d}=(0,p.u)(),u=(0,r.useMemo)((()=>Object.keys(ie.Eg).map((e=>({label:ie.Eg[e],value:ie.T8[e]})))),[]),m=(0,r.useMemo)((()=>t.map((e=>{var t,n;const a=(0,fe.GF)(ie.RO.METRICS,e);return{label:null!==(t=null===(n=Gt.g[e])||void 0===n?void 0:n.displayName)&&void 0!==t?t:e,value:a}}))),[t]),g=(0,r.useMemo)((()=>n.map((e=>({label:e,value:`${(0,fe.GF)(ie.RO.PARAMS,e)}`})))),[n]),h=(0,r.useMemo)((()=>[...u,...m,...g]),[u,m,g]),f=(0,r.useMemo)((()=>{const e=h.find((e=>e.value===s));let t=null===e||void 0===e?void 0:e.label;if(!t){const e=s.match(/^.+\.`(.+)`$/);e&&(t=e[1])}return`${i.formatMessage({id:"sKaamx",defaultMessage:"Sort"})}: ${t}`}),[h,i,s]);return(0,y.FD)(c.rId.Root,{open:o,onOpenChange:l,modal:!1,children:[(0,y.Y)(c.rId.Trigger,{"data-test-id":"sort-select-dropdown",asChild:!0,children:(0,y.Y)(p.B,{componentId:"mlflow.experiment_page.sort_select_v2.toggle",icon:a?(0,y.Y)(c.GCP,{}):(0,y.Y)(c.MMv,{}),css:pn,"aria-label":f,endIcon:(0,y.Y)(c.D3D,{}),children:f})}),(0,y.Y)(c.rId.Content,{minWidth:250,children:(0,y.Y)(un,{sortOptions:h,orderByKey:s,orderByAsc:a,onOptionSelected:()=>l(!1)})})]})})),gn=r.memo((e=>{var t;let{runsData:n,viewState:s,updateViewState:i,searchFacetsState:o,experimentId:l,requestError:d,expandRows:u,updateExpandRows:m,refreshRuns:g,uiState:h,isLoading:f}=e;const[v,_]=ce(),{paramKeyList:x,metricKeyList:w,tagsList:b}=n,{orderByAsc:Y,orderByKey:C}=o,I=(0,Vt.e)(),A="TABLE"!==v,k="ARTIFACT"===v,{theme:E}=(0,p.u)(),S=x,M=w,R=T.A.getVisibleTagKeyList(b),D=(0,r.useCallback)((()=>(0,fe.D1)(n,R,S,M)),[M,S,R,n]),F=(((e,t)=>{(0,r.useMemo)((()=>{let n=[];const a=[ie.mh,ie.KU];return n=[...Object.keys(ie.Eg).reduce(((e,t)=>{const n=ie.Eg[t];return a.forEach((a=>{e.push({label:n,value:ie.T8[t]+ie.KE+a,order:a})})),e}),[]),...e.reduce(((e,t)=>(a.forEach((n=>{e.push({label:t,value:`${(0,fe.GF)(ie.RO.METRICS,t)}${ie.KE}${n}`,order:n})})),e)),[]),...t.reduce(((e,t)=>(a.forEach((n=>{e.push({label:t,value:`${(0,fe.GF)(ie.RO.PARAMS,t)}${ie.KE}${n}`,order:n})})),e)),[])],n}),[e,t])})(M,S),Object.values(s.runsSelected).filter(Boolean).length),L=F>1||1===F||F>0,B=!k,N=(0,r.useCallback)((e=>i({columnSelectorVisible:e})),[i]),P=(0,r.useCallback)((()=>m(!u)),[u,m]),H=(0,r.useMemo)((()=>n.datasetsList.some((e=>(null===e||void 0===e?void 0:e.length)>1))),[n]);return(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:E.spacing.sm,flexDirection:"column",marginTop:h.viewMaximized?void 0:E.spacing.sm,marginBottom:E.spacing.sm},""),children:[(0,y.Y)(Xt,{hideBorder:!1,viewState:s,runsAreGrouped:Boolean(h.groupBy)}),L&&(0,y.Y)(Te,{runsData:n,searchFacetsState:o,viewState:s,refreshRuns:g}),!L&&(0,y.Y)(Kt,{onDownloadCsv:D,searchFacetsState:o,experimentId:l,viewState:s,updateViewState:i,runsData:n,requestError:d,refreshRuns:g,viewMaximized:h.viewMaximized,autoRefreshEnabled:h.autoRefreshEnabled,hideEmptyCharts:h.hideEmptyCharts,areRunsGrouped:Boolean(h.groupBy),additionalControls:(0,y.FD)(y.FK,{children:[(0,y.Y)(mn,{orderByAsc:Y,orderByKey:C,metricKeys:M,paramKeys:S}),!A&&(0,y.Y)(Zt,{columnSelectorVisible:s.columnSelectorVisible,onChangeColumnSelectorVisible:N,runsData:n,selectedColumns:h.selectedColumns}),!A&&H&&(0,y.Y)(c.ffE,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewrunscontrols.tsx_175",onClick:P,children:(0,y.Y)(X.A,{id:"mRc7MY",defaultMessage:"Expand rows"})}),B&&(0,y.Y)(ln,{groupBy:h.groupBy,onChange:e=>{I((t=>({...t,groupBy:e})))},runsData:n,isLoading:f,useGroupedValuesInCharts:null===(t=h.useGroupedValuesInCharts)||void 0===t||t,onUseGroupedValuesInChartsChange:e=>{I((t=>({...t,useGroupedValuesInCharts:e})))}})]})})]})}));var hn=n(28940);class fn{constructor(){this.runsSelected={},this.hiddenChildRunsSelected={},this.columnSelectorVisible=!1,this.previewPaneVisible=!1,this.artifactViewState={selectedTables:[],groupByCols:[],outputColumn:"",intersectingOnly:!1}}}var vn=n(57368);const _n="FETCHED_RUN_NOTIFICATION_KEY",xn=e=>{const{formatMessage:t}=(0,Fe.A)(),n=(0,r.useCallback)(((e,n)=>e===n?t({id:"KJbYrw",defaultMessage:"Loaded {childRuns} child {childRuns, plural, =1 {run} other {runs}}"},{childRuns:n}):t({id:"ziIhFQ",defaultMessage:"Loaded {allRuns} {allRuns, plural, =1 {run} other {runs}}, including {childRuns} child {childRuns, plural, =1 {run} other {runs}}"},{allRuns:e,childRuns:n})),[t]);return(0,r.useCallback)(((t,a)=>{if(Array.isArray(t)){const{allRuns:r,childRuns:s}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];const n=e.filter((e=>!t.some((t=>t.runUuid===e.info.runUuid)))),a=n.filter((e=>{var t;const n=null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.tags;return Array.isArray(n)&&n.some((e=>e.key===fe.Ol&&Boolean(e.value)))}));return{allRuns:n.length,childRuns:a.length}}(t,a);if(s<1)return;e.close(_n),e.info({message:n(r,s),duration:3,placement:"bottomRight",key:_n})}}),[e,n])};var yn=n(85343),wn=n(28380),bn=n(51293);const Yn=r.createContext({getMissingParams:()=>[],pendingDataLoading:{},getEvaluableRowCount:()=>0,evaluateCell:()=>{},evaluateAllClick:()=>{},runColumnsBeingEvaluated:[],canEvaluateInRunColumn:()=>!1,toggleExpandedHeader:()=>{},isHeaderExpanded:!1}),Cn=e=>{let{tableData:t,outputColumn:n,children:a}=e;const i=(0,Fe.A)(),[o,l]=(0,r.useState)(!1),d=(0,r.useCallback)((()=>l((e=>!e))),[]),c=(0,r.useCallback)(((e,n)=>{if(!(0,st.o1)(e))return null;const a=t.find((e=>e.key===n));if(!a)return null;const{promptTemplate:r}=(0,st.vC)(e);if(!r)return null;return(0,st.rl)(r).filter((e=>!a.groupByCellValues[e]))}),[t]),u=(0,s.wA)(),{startEvaluatingRunColumn:p,stopEvaluatingRunColumn:m,runColumnsBeingEvaluated:g}=((e,t)=>{const n=(0,r.useRef)(e),a=(0,r.useRef)([]),i=(0,Fe.A)();(0,r.useEffect)((()=>{n.current=e}),[e]);const[o,l]=(0,r.useState)([]);(0,r.useEffect)((()=>{a.current=o}),[o]);const d=(0,s.wA)(),c=(0,r.useCallback)((e=>{const r=n.current,{parameters:s,promptTemplate:o,routeName:u,routeType:p}=(0,st.vC)(e);if(!o)return;const m=(0,st.rl)(o),g=r.find((t=>!t.cellValues[e.runUuid]&&((e,t)=>0===t.filter((t=>!e.groupByCellValues[t])).length)(t,m)));if(!g)return void l((t=>t.filter((t=>t!==e.runUuid))));const h=g.key,f=g.groupByCellValues;if(!o)return;const v=(0,st.TG)(o,f);u&&d(kt({routeName:u,routeType:p,compiledPrompt:v,inputValues:f,outputColumn:t,rowKey:h,parameters:s,run:e})).then((()=>{a.current.includes(e.runUuid)&&c(e)})).catch((t=>{const n=St(t),a=i.formatMessage({id:"jI22Qu",defaultMessage:'Gateway returned the following error: "{errorMessage}"'},{errorMessage:n});T.A.logErrorAndNotifyUser(a),l((t=>t.filter((t=>t!==e.runUuid))))}))}),[d,t,i]),u=(0,r.useCallback)((e=>{l((t=>[...t,e.runUuid])),c(e)}),[c]),p=(0,r.useCallback)((e=>{l((t=>t.filter((t=>t!==e.runUuid))))}),[]);return{runColumnsBeingEvaluated:o,startEvaluatingRunColumn:u,stopEvaluatingRunColumn:p}})(t,n),h=(0,s.d4)((e=>{let{evaluationData:t}=e;return t.evaluationPendingDataLoadingByRunUuid})),f=(0,r.useCallback)((e=>n===st.hR&&(0,st.o1)(e)),[n]),v=(0,r.useCallback)((e=>t.filter((t=>{if(t.cellValues[e.runUuid])return!1;const n=c(e,t.key);return 0===(null===n||void 0===n?void 0:n.length)})).length),[t,c]),_=(0,r.useCallback)((e=>{g.includes(e.runUuid)?m(e):p(e)}),[g,p,m]),x=(0,r.useCallback)(((e,a)=>{const r=t.find((e=>{let{key:t}=e;return t===a}));if(!r)return;const s=r.groupByCellValues,{parameters:o,promptTemplate:l,routeName:d,routeType:c}=(0,st.vC)(e);if(!l)return;const p=(0,st.TG)(l,s);if(d){u((()=>kt({routeName:d,routeType:c,compiledPrompt:p,inputValues:s,outputColumn:n,rowKey:a,parameters:o,run:e}))()).catch((e=>{const t=St(e),n=i.formatMessage({id:"nyHcAz",defaultMessage:'MLflow deployment returned the following error: "{errorMessage}"'},{errorMessage:t});T.A.logErrorAndNotifyUser(n)}))}}),[t,u,n,i]),w=(0,r.useMemo)((()=>({getMissingParams:c,getEvaluableRowCount:v,evaluateCell:x,evaluateAllClick:_,pendingDataLoading:h,canEvaluateInRunColumn:f,runColumnsBeingEvaluated:g,isHeaderExpanded:o,toggleExpandedHeader:d})),[c,v,_,x,h,f,g,o,d]);return(0,y.Y)(Yn.Provider,{value:w,children:a})},In=()=>(0,r.useContext)(Yn);var An={name:"1h52dri",styles:"overflow:hidden;text-overflow:ellipsis;white-space:nowrap"},kn={name:"lyel5l",styles:"font-size:0"},En={name:"1hetg88",styles:"max-width:300px"};const Sn=e=>{let{run:t}=e;const{theme:n}=(0,p.u)(),{isHeaderExpanded:r}=In(),i=(0,st.vC)(t),o=(0,s.d4)((e=>{let{modelGateway:t}=e;const n=`${i.routeType}:${i.routeName}`;return i.routeName?t.modelGatewayRoutes[n]:null}));if(!(0,st.o1)(t)||!i)return null;const{parameters:l,promptTemplate:d,routeName:c}=i,{stop:u=[]}=l;return(0,y.FD)("div",{css:(0,a.AH)({marginTop:n.spacing.xs,flex:1,display:"flex",flexDirection:"column",gap:n.spacing.sm,overflowX:"hidden",width:"100%"},""),children:[o&&"mlflowDeployment"in o&&o.mlflowDeployment&&(0,y.Y)(p.T.Hint,{children:o.mlflowDeployment.name}),r&&(0,y.FD)(y.FK,{children:[(0,y.Y)(p.T.Hint,{children:(0,y.Y)(X.A,{id:"HGBit9",defaultMessage:"Temperature: {temperature}",values:l})}),(0,y.Y)(p.T.Hint,{children:(0,y.Y)(X.A,{id:"6K04VZ",defaultMessage:"Max. tokens: {max_tokens}",values:l})}),u.length?(0,y.Y)(p.T.Hint,{css:An,children:(0,y.Y)(X.A,{id:"iC2Owx",defaultMessage:"Stop sequences: {stopSequences}",values:{stopSequences:null===u||void 0===u?void 0:u.join(", ")}})}):null,(0,y.Y)("div",{css:kn,children:(0,y.FD)(p.av.Root,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadermodelindicator.tsx_107",children:[(0,y.Y)(p.av.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadermodelindicator.tsx_115",type:"link",size:"small",css:(0,a.AH)({fontSize:n.typography.fontSizeSm},""),children:(0,y.Y)(X.A,{id:"XX8+x1",defaultMessage:"View prompt template"})})}),(0,y.FD)(p.av.Content,{css:En,children:[(0,y.Y)(p.av.Arrow,{}),d]})]})})]})]})};var Mn=n(38243);var Rn={name:"1iwgvlm",styles:"flex-shrink:1;flex-grow:1;overflow:hidden"},Tn={name:"1kd7iwj",styles:"flex-shrink:0;flex-grow:1;display:flex;align-items:flex-end"};const Dn=e=>{var t;let{run:n,onDatasetSelected:s}=e;const{theme:i}=(0,p.u)(),o=(0,r.useCallback)((e=>s(e,n)),[s,n]);return(null===(t=n.datasets)||void 0===t?void 0:t.length)<1?null:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",gap:i.spacing.xs,overflow:"hidden"},""),children:[(0,y.Y)("div",{css:Rn,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheaderdatasetindicator.tsx_37",type:"link",onClick:()=>o(n.datasets[0]),children:(0,y.Y)(Mn.E,{datasetWithTags:n.datasets[0],displayTextAsLink:!0,css:(0,a.AH)({marginTop:i.spacing.xs/2,marginBottom:i.spacing.xs/2},"")})})}),n.datasets.length>1&&(0,y.Y)("div",{css:Tn,children:(0,y.FD)(p.av.Root,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheaderdatasetindicator.tsx_51",modal:!1,children:[(0,y.Y)(p.av.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheaderdatasetindicator.tsx_49",size:"small",style:{borderRadius:"8px",width:"40px"},children:(0,y.FD)(p.T.Text,{color:"secondary",children:["+",n.datasets.length-1]})})}),(0,y.Y)(p.av.Content,{align:"start",children:n.datasets.slice(1).filter(Boolean).map((e=>(0,y.Y)("div",{css:(0,a.AH)({height:i.general.heightSm,display:"flex",alignItems:"center"},""),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheaderdatasetindicator.tsx_66",type:"link",onClick:()=>o(e),children:(0,y.Y)(Mn.E,{datasetWithTags:e,displayTextAsLink:!0})})},`${e.dataset.name}-${e.dataset.digest}`)))})]})})]})};var Fn={name:"1on1b16",styles:"width:100%;height:100%;display:flex;flex-direction:column"};const Ln=e=>{let{children:t,className:n,groupHeaderContent:r=null}=e;const{theme:s}=(0,p.u)();return(0,y.FD)("div",{css:Fn,children:[(0,y.Y)("div",{css:(0,a.AH)({width:"100%",flexBasis:40,display:"flex",alignItems:"center",padding:s.spacing.sm,borderBottom:`1px solid ${s.colors.borderDecorative}`},""),className:"header-group-cell",children:r}),(0,y.Y)("div",{css:(0,a.AH)({width:"100%",flex:1,display:"flex",justifyContent:"flex-start",alignItems:"flex-start",padding:s.spacing.xs,borderRight:`1px solid ${s.colors.borderDecorative}`},""),className:n,children:t})]})};var Bn=n(70403),Nn=n(79432);var Pn={name:"8xhv84",styles:"width:100%;display:flex"},Hn={name:"ozd7xs",styles:"flex-shrink:0"},On={name:"82a6rk",styles:"flex:1"};const Un=e=>{let{run:t,onHideRun:n,onDuplicateRun:s,onDatasetSelected:i,groupHeaderContent:l=null}=e;const{theme:d}=(0,p.u)(),{getEvaluableRowCount:u,evaluateAllClick:m,runColumnsBeingEvaluated:g,canEvaluateInRunColumn:f}=In(),v=(0,Fe.A)(),_=u(t),x=(0,Bn.LE)(),w=_>0,b=g.includes(t.runUuid),Y=(0,r.useMemo)((()=>w?w&&!b?v.formatMessage({id:"2gmOVq",defaultMessage:"Process {evaluableRowCount} rows without evaluation output"},{evaluableRowCount:_}):null:v.formatMessage({id:"7m2B5x",defaultMessage:"There are no evaluable rows within this column"})),[_,w,b,v]);return(0,y.FD)(Ln,{css:(0,a.AH)({justifyContent:"flex-start",padding:d.spacing.sm,paddingBottom:0,paddingTop:d.spacing.sm,flexDirection:"column",gap:d.spacing.xs/2,overflow:"hidden"},""),groupHeaderContent:l,children:[(0,y.FD)("div",{css:Pn,children:[(0,y.FD)("span",{css:(0,a.AH)({display:"flex",gap:d.spacing.sm,alignItems:"center"},""),children:[(0,y.Y)(Nn.E,{color:x(t.runUuid)}),(0,y.Y)(o.N_,{to:h.h.getRunPageRoute(t.experimentId||"",t.runUuid),target:"_blank",children:t.runName})]}),(0,y.Y)("div",{css:(0,a.AH)({flexBasis:d.spacing.sm,flexShrink:0},"")}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadercellrenderer.tsx_112",onClick:()=>n(t.runUuid),size:"small",icon:(0,y.Y)(c.kFX,{}),css:Hn}),(0,y.Y)("div",{css:On}),(0,W.Ii)()&&f(t)&&(0,y.FD)(y.FK,{children:[(0,y.Y)("div",{css:(0,a.AH)({flexBasis:d.spacing.sm},"")}),(0,y.Y)(c.paO,{title:Y,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadercellrenderer.tsx_118",disabled:!w,size:"small",onClick:()=>m(t),icon:b?(0,y.Y)(c.wFz,{}):(0,y.Y)(c.udU,{}),children:b?(0,y.Y)(X.A,{id:"plw4Kp",defaultMessage:"Stop evaluating"}):(0,y.Y)(X.A,{id:"JnIbS3",defaultMessage:"Evaluate all"})})})]}),(0,y.Y)("div",{css:(0,a.AH)({flexBasis:d.spacing.sm},"")}),(0,W.Ii)()&&(0,st.o1)(t)&&(0,y.FD)(c.rId.Root,{modal:!1,children:[(0,y.Y)(c.rId.Trigger,{asChild:!0,children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadercellrenderer.tsx_143",size:"small",icon:(0,y.Y)(c.ssM,{})})}),(0,y.Y)(c.rId.Content,{children:(0,y.Y)(c.rId.Item,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationrunheadercellrenderer.tsx_150",onClick:()=>s(t),children:(0,y.Y)(X.A,{id:"1YGQOY",defaultMessage:"Duplicate run"})})})]})]}),(0,W.Ii)()&&(0,st.o1)(t)?(0,y.Y)(Sn,{run:t}):(0,y.Y)(Dn,{run:t,onDatasetSelected:i})]})},Vn=e=>{let{disabled:t,isLoading:n,run:a,rowKey:r}=e;const s=(0,st.o1)(a),{evaluateCell:i,getMissingParams:o}=In(),l=a&&o(a,r)||null;return l&&l.length>0?(0,y.Y)(c.paO,{title:(0,y.Y)(X.A,{id:"yAuDRt",defaultMessage:'Evaluation is not possible because values for the following inputs cannot be determined: {missingParamList}. Add input columns to the "group by" settings or use "Add row" button to define new parameter set.',values:{missingParamList:(0,y.Y)("code",{children:l.join(", ")})}}),children:(0,y.Y)(u.I,{})}):s?(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationcellevaluatebutton.tsx_59",loading:n,disabled:t,size:"small",onMouseDownCapture:e=>e.stopPropagation(),onClickCapture:e=>{e.stopPropagation(),i(a,r)},icon:(0,y.Y)(c.udU,{}),children:(0,y.Y)(y.FK,{children:"Evaluate"})}):(0,y.Y)(c.paO,{title:(0,y.Y)(X.A,{id:"MhtxHm",defaultMessage:"You cannot evaluate this cell, this run was not created using served LLM model route"}),children:(0,y.Y)(u.I,{})})};var Kn=n(40555);const Gn=r.memo((e=>{let{text:t,highlight:n}=e;const{theme:s}=(0,p.u)();if(!n)return(0,y.Y)(y.FK,{children:t});const i=t.split(new RegExp(`(${n})`,"gi"));return(0,y.Y)(y.FK,{children:i.map(((e,t)=>(0,y.Y)(r.Fragment,{children:e.toLowerCase()===n.toLowerCase()?(0,y.Y)("span",{css:(0,a.AH)({backgroundColor:s.colors.yellow200},""),children:e}):e},t)))})}));var zn={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"},$n={name:"52b6nu",styles:"display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;-webkit-line-clamp:7"},jn={name:"1jwcxx3",styles:"font-style:italic"};const qn=e=>{var t,n;let{value:i,context:o,isGroupByColumn:l,run:d,data:u}=e;const{theme:m}=(0,p.u)(),{pendingDataLoading:g,canEvaluateInRunColumn:h}=In(),f=(0,s.d4)((e=>{let{modelGateway:{modelGatewayRoutesLoading:t,modelGatewayRoutesLoadingLegacy:n}}=e;return t.loading})),v=d&&(null===(t=g[d.runUuid])||void 0===t?void 0:t[null===u||void 0===u?void 0:u.key]),_=d&&(null===(n=u.outputMetadataByRunUuid)||void 0===n?void 0:n[d.runUuid])||null,x=null!==_&&void 0!==_&&_.isPending||u.isPendingInputRow?m.colors.backgroundSecondary:m.colors.backgroundPrimary,w=r.useMemo((()=>{try{return JSON.parse(i)}catch(e){return null}}),[i]);return(0,y.FD)("div",{css:(0,a.AH)({height:"100%",whiteSpace:"normal",padding:m.spacing.sm,overflow:"hidden",position:"relative",cursor:"pointer",backgroundColor:x,"&:hover":{backgroundColor:m.colors.actionDefaultBackgroundHover}},""),children:[v?(0,y.Y)(c.QvX,{lines:3}):(0,y.Y)(y.FK,{children:i?w?(0,y.Y)(Kn.y,{json:JSON.stringify(w,null,2)}):(0,y.Y)("span",{css:$n,children:l&&o.highlightedText?(0,y.Y)(Gn,{text:i,highlight:o.highlightedText}):"string"===typeof i?i.substring(0,512):"object"!==typeof i&&i}):(0,y.Y)(p.T.Text,{color:"info",css:zn,children:(0,y.Y)(X.A,{id:"aQxQIF",defaultMessage:"(empty)"})})}),(0,W.Ii)()&&d&&h(d)&&(0,y.FD)("div",{css:(0,a.AH)({position:"absolute",left:8,bottom:8,right:8,display:"flex",gap:m.spacing.sm,alignItems:"center",justifyContent:"space-between"},""),children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",gap:m.spacing.xs},""),children:[!i&&(0,y.Y)(Vn,{disabled:v,isLoading:f,run:d,rowKey:u.key}),((null===_||void 0===_?void 0:_.isPending)||u.isPendingInputRow)&&(0,y.Y)(p.T.Hint,{size:"sm",css:jn,children:(0,y.Y)(X.A,{id:"2fvQaF",defaultMessage:"Unsaved"})})]}),_&&!v&&(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:m.spacing.xs,alignItems:"center"},""),children:[_.evaluationTime&&(0,y.FD)(p.T.Hint,{size:"sm",children:[Math.round(_.evaluationTime)," ms",_.totalTokens?",":""]}),_.totalTokens&&(0,y.Y)(p.T.Hint,{size:"sm",children:(0,y.Y)(X.A,{id:"/nPZbt",defaultMessage:"{totalTokens} total tokens",values:{totalTokens:_.totalTokens}})})]})]})]})},Wn={initialWidthGroupBy:200,initialWidthOutput:360,maxWidth:500,minWidth:140};var Qn={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"};const Jn=e=>{let{displayName:t}=e;const{theme:n}=(0,p.u)();return(0,y.Y)(Ln,{css:(0,a.AH)({justifyContent:"flex-start",padding:n.spacing.sm},""),children:(0,y.Y)(c.paO,{title:(0,j.truncate)(t,{length:250}),children:(0,y.Y)(p.T.Text,{bold:!0,css:Qn,children:t})})})};var Zn={name:"l8l8b8",styles:"white-space:nowrap;overflow:hidden;text-overflow:ellipsis"};const Xn={svg:{width:20,height:20}},ea=()=>{const{toggleExpandedHeader:e,isHeaderExpanded:t}=In();return(0,y.Y)(Ln,{children:(0,y.Y)(c.paO,{placement:"right",title:(0,y.Y)(X.A,{id:"lDGQGa",defaultMessage:"Toggle detailed view"}),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationtableactionscolumnrenderer.tsx_22",icon:t?(0,y.Y)(c.D3D,{css:Xn}):(0,y.Y)(p.q,{css:Xn}),onClick:e})})})},ta=e=>{let{onAddNewInputs:t,displayAddNewInputsButton:n}=e;const{theme:r}=(0,p.u)();return n?(0,y.Y)("div",{css:(0,a.AH)({width:"100%",height:"100%",display:"flex",flexDirection:"column",padding:r.spacing.xs},""),children:(0,y.Y)(c.paO,{placement:"right",title:(0,y.Y)(X.A,{id:"PNfcez",defaultMessage:"Add row"}),children:(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_components_evaluationtableactionscellrenderer.tsx_37",icon:(0,y.Y)(c.c11,{}),onClick:t})})}):null};var na=n(54421);var aa={name:"1gz2b5f",styles:"overflow:hidden;text-overflow:ellipsis"},ra={name:"pdi3j6",styles:"display:-webkit-box;overflow:hidden;text-overflow:ellipsis;-webkit-box-orient:vertical;-webkit-line-clamp:7;width:100%;height:100%"};const sa=e=>{let{value:t}=e;const{theme:n}=(0,p.u)(),r=n.colors.backgroundPrimary;return(0,y.Y)("div",{css:(0,a.AH)({height:"100%",whiteSpace:"normal",padding:n.spacing.sm,overflow:"hidden",position:"relative",cursor:"pointer",backgroundColor:r,"&:hover":{backgroundColor:n.colors.actionDefaultBackgroundHover}},""),children:t&&t.url&&t.compressed_url?(0,y.Y)("span",{css:ra,children:(0,y.Y)(na.TV,{imageUrl:t.url,compressedImageUrl:t.compressed_url})}):(0,y.Y)(p.T.Text,{color:"info",css:aa,children:(0,y.Y)(X.A,{id:"aQxQIF",defaultMessage:"(empty)"})})})};var ia={name:"3ytxc3",styles:"height:100%;overflow:hidden"};const oa=e=>{let{resultList:t,visibleRuns:n,groupByColumns:i,onCellClick:o,onHideRun:l,onDatasetSelected:d,highlightedText:c="",isPreviewPaneVisible:g,outputColumnName:h,isImageColumn:f}=e;const[v,_]=(0,r.useState)([]),[x,w]=(0,r.useState)(null),b=(0,s.d4)((e=>{let{evaluationData:t}=e;return t.evaluationPendingDataByRunUuid})),Y=(0,r.useRef)(null),{isHeaderExpanded:C}=In(),{createNewRun:I}=Ht(),A=(0,r.useCallback)((e=>{null===x||void 0===x||x.refreshHeader(),l(e)}),[x,l]),k=(0,r.useCallback)((e=>{null===x||void 0===x||x.refreshHeader(),I(e)}),[I,x]);(0,r.useEffect)((()=>{x&&!g&&x.clearFocusedCell()}),[x,g]),(0,r.useEffect)((()=>{if(!x)return;const e=x.getRenderedNodes();x.refreshCells({force:!0,rowNodes:e})}),[x,b,c]);const{showAddNewInputsModal:E,AddNewInputsModal:S}=(()=>{const[e,t]=(0,r.useState)(!1),[n,s]=(0,r.useState)([]),[i,o]=(0,r.useState)({}),l=(0,r.useMemo)((()=>n.every((e=>{let{inputName:t}=e;return i[t]}))),[i,n]),[d,c]=(0,r.useState)((async()=>{})),g=(0,r.useCallback)(((e,t)=>{o((n=>({...n,[e]:t})))}),[]),h=(0,r.useCallback)(((e,n)=>{const a=e.filter(st.o1).map((e=>({runName:e.runName,params:(0,st.xP)(e)}))),r=(0,j.uniq)(a.map((e=>{let{params:t}=e;return t})).flat()).map((e=>({inputName:e,runNames:(0,j.compact)(a.filter((t=>t.params.includes(e))).map((e=>{let{runName:t}=e;return t})))})));t(!0),s(r),o({}),c((()=>n))}),[]),{theme:f}=(0,p.u)(),v=(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_hooks_useevaluationaddnewinputsmodal.tsx_57",title:(0,y.Y)(X.A,{id:"JnidmZ",defaultMessage:"Add row"}),okButtonProps:{disabled:!l},okText:(0,y.Y)(X.A,{id:"eeLqSn",defaultMessage:"Submit"}),cancelText:(0,y.Y)(X.A,{id:"jwALGI",defaultMessage:"Cancel"}),onOk:()=>{d(i),t(!1)},visible:e,onCancel:()=>t(!1),children:n.map((e=>{let{inputName:t,runNames:n}=e;return(0,y.FD)("div",{css:(0,a.AH)({marginBottom:f.spacing.md},""),children:[(0,y.Y)(p.T.Text,{bold:!0,children:t}),(0,y.Y)(p.T.Hint,{css:Zn,children:(0,y.Y)(X.A,{id:"s35HDG",defaultMessage:"Used by {runNames} {hasMore, select, true {and other runs} other {}}",values:{runNames:n.slice(0,5).join(", "),hasMore:n.length>5}})}),(0,y.Y)("div",{css:(0,a.AH)({marginTop:f.spacing.sm},""),children:(0,y.Y)(m.I.TextArea,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_hooks_useevaluationaddnewinputsmodal.tsx_99",value:i[t],onChange:e=>g(t,e.target.value)})})]},t)}))});return{showAddNewInputsModal:h,AddNewInputsModal:v}})(),M=(0,s.wA)(),R=(0,r.useCallback)((()=>{var e;const t=null===(e=Y.current)||void 0===e?void 0:e.querySelector(".ag-body-viewport");t?t.scrollTo({top:0,behavior:"smooth"}):null===x||void 0===x||x.ensureIndexVisible(0,"top")}),[x]),T=(0,r.useMemo)((()=>n.map(st.xP).flat().length>0),[n]),D=(0,r.useCallback)((()=>{E(n,(e=>{M({type:"EVALUATE_ADD_INPUT_VALUES",payload:e,meta:{}}),R()}))}),[R,E,M,n]),{theme:F}=(0,p.u)(),L=(0,Fe.A)(),B=(0,r.useCallback)((e=>{let{value:t,colDef:n,column:a}=e;const r=L.formatMessage({id:"aQxQIF",defaultMessage:"(empty)"});return null===o||void 0===o?void 0:o(t||r,n.headerName||a.getId())}),[L,o]),N=(0,r.useMemo)((()=>(0,y.Y)(p.T.Text,{bold:!0,children:h})),[h]);return(0,r.useEffect)((()=>{const e=[],{initialWidthGroupBy:t,initialWidthOutput:a,maxWidth:r,minWidth:s}=Wn;(0,W.Ii)()&&n.some((e=>(0,st.o1)(e)))&&e.push({resizable:!1,pinned:!0,width:40,headerComponent:"ActionsColumnRenderer",cellRendererSelector:e=>{let{rowIndex:t}=e;return 0===t?{component:"ActionsCellRenderer",params:{displayAddNewInputsButton:T,onAddNewInputs:D}}:void 0},cellClass:"leading-column-cell"}),i.forEach(((n,a)=>{const o=a===i.length-1;e.push({resizable:!0,initialWidth:t,minWidth:s,maxWidth:r,headerName:n,valueGetter:e=>{let{data:t}=e;return t.groupByCellValues[n]},suppressMovable:!0,cellRenderer:"TextRendererCellRenderer",headerClass:o?"last-group-by-header-cell":void 0,cellRendererParams:{isGroupByColumn:!0},headerComponent:"GroupHeaderCellRenderer",headerComponentParams:{displayAddNewInputsButton:T,onAddNewInputs:D},colId:n,onCellClicked:B})})),n.forEach(((t,n)=>{const i=0===n;e.push({resizable:!0,initialWidth:a,minWidth:s,maxWidth:r,headerName:t.runName,colId:t.runUuid,valueGetter:e=>{let{data:n}=e;return n.cellValues[t.runUuid]},suppressMovable:!0,cellRenderer:f?"ImageRendererCellRenderer":"TextRendererCellRenderer",cellRendererParams:{run:t},headerComponent:"RunHeaderCellRenderer",headerComponentParams:{run:t,onDuplicateRun:k,onHideRun:A,onDatasetSelected:d,groupHeaderContent:i?N:null},onCellClicked:B})})),_(e)}),[n,i,A,k,d,D,T,B,N,f]),(0,r.useEffect)((()=>{if(!x)return;const e=n.some((e=>{var t;return(0,st.o1)(e)||(null===(t=e.datasets)||void 0===t?void 0:t.length)>0}));x.setHeaderHeight(function(){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?40+(arguments.length>0&&void 0!==arguments[0]&&arguments[0]?175:62):80}(C,e))}),[x,C,n]),(0,y.FD)("div",{css:ia,ref:Y,children:[(0,y.Y)(bn.p,{css:la(F),context:{highlightedText:c},rowHeight:190,onGridReady:e=>{let{api:t}=e;return w(t)},getRowId:e=>{let{data:t}=e;return t.key},suppressHorizontalScroll:!1,columnDefs:v,rowData:t,components:{TextRendererCellRenderer:qn,GroupHeaderCellRenderer:Jn,RunHeaderCellRenderer:Un,ActionsColumnRenderer:ea,ActionsCellRenderer:ta,ImageRendererCellRenderer:sa}}),S]})},la=e=>({".ag-row:not(.ag-row-first), .ag-body-viewport":{borderTop:`1px solid ${e.colors.borderDecorative}`},".ag-row-last":{borderBottom:`1px solid ${e.colors.borderDecorative}`},".ag-cell, .last-group-by-header-cell .header-group-cell":{borderRight:`1px solid ${e.colors.borderDecorative}`},".ag-cell-focus:not(.leading-column-cell)::after":{content:'""',position:"absolute",inset:0,boxShadow:`inset 0 0 0px 2px ${e.colors.blue300}`,pointerEvents:"none"}});var da=n(77484);const ca=(e,t)=>{const n=t.map((t=>{const n=e[t];return[t,(0,j.isString)(n)?n:JSON.stringify(n)]})),a=n.map((e=>{let[,t]=e;return String(t)})).join(".");return{key:a,groupByValues:(0,j.fromPairs)(n)}};var ua=n(38566);var pa=n(37752);const ma=()=>{var e;return/mac/i.test(null===(e=window.navigator.userAgentData)||void 0===e?void 0:e.platform)||/mac/i.test(window.navigator.platform)},ga=ma()?"metaKey":"ctrlKey",ha=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;const{altOrOptKey:a=!1,ctrlOrCmdKey:s=!1,shiftKey:i=!1}=t;return(0,r.useEffect)((()=>{const t=t=>{if((!s||t[ga])&&(!a||t.altKey)&&(!i||t.shiftKey)&&t.key===e){n()&&t.preventDefault()}};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)}),[e,n,s,a,i]),{isMacKeyboard:ma}},fa=()=>{const{evaluationPendingDataByRunUuid:e,evaluationArtifactsBeingUploaded:t,evaluationDraftInputValues:n}=(0,s.d4)((e=>{let{evaluationData:t}=e;return t})),[o,l]=(0,r.useState)(!1),d=(0,s.wA)(),c=(0,r.useCallback)((()=>{d({type:"DISCARD_PENDING_EVALUATION_DATA"})}),[d]),u=Object.values(e).flat().length,m=n.length,g=Object.values(t).filter((e=>Object.values(e).some((e=>e)))).length;(0,r.useEffect)((()=>{0===u&&l(!1)}),[u]);const h=(0,r.useCallback)((()=>(0===u||o||(l(!0),d((async(e,t)=>{const{evaluationPendingDataByRunUuid:n,evaluationArtifactsByRunUuid:a}=t().evaluationData,r=Object.keys(n),s=(0,j.fromPairs)(Object.entries(a).filter((e=>{let[t,n]=e;return r.includes(t)&&n[ie.AH]})).map((e=>{let[t,n]=e;return[t,n[ie.AH]]}))),o=r.map((e=>{const t=s[e];if(!t)throw new Error(`Cannot find existing prompt engineering artifact for run ${e}`);const a=n[e].map((e=>{let{entryData:n,evaluationTime:a,totalTokens:r}=e;return t.columns.map((e=>e===st.Ef?a.toString():e===st.jC&&r?r.toString():n[e]||""))})),r=(0,j.cloneDeep)(s[e].rawArtifactFile);return null===r||void 0===r||r.data.unshift(...a),{runUuid:e,updatedArtifactFile:r}})),l=o.map((t=>{let{runUuid:n,updatedArtifactFile:a}=t;return e((0,i.Of)(n,ie.AH,a)).then((()=>{const e=(0,At.G4)(ie.AH,a);return{runUuid:n,newEvaluationTable:e}}))}));return e({type:"WRITE_BACK_EVALUATION_ARTIFACTS",payload:Promise.all(l),meta:{runUuidsToUpdate:r,artifactPath:ie.AH}})})).catch((e=>{T.A.logErrorAndNotifyUser(e)}))),!0)),[d,u,o]),{isMacKeyboard:f}=ha("s",{ctrlOrCmdKey:!0},h),{theme:v}=(0,p.u)(),_=m>0&&0===u,x=u>0||_?(0,y.FD)("div",{css:(0,a.AH)({backgroundColor:v.colors.backgroundPrimary,border:`1px solid ${v.colors.border}`,padding:v.spacing.md,marginBottom:v.spacing.sm,display:"flex",justifyContent:"space-between",alignItems:"center"},""),children:[_?(0,y.Y)(X.A,{id:"EPcEi9",defaultMessage:"You have added rows with new input values, but you still need to evaluate the new data in order to save it."}):o?(0,y.Y)(p.T.Text,{children:(0,y.Y)(X.A,{id:"NGsTf/",defaultMessage:"Synchronizing artifacts for {runsBeingSynchronizedCount} runs...",values:{runsBeingSynchronizedCount:(0,y.Y)("strong",{children:g})}})}):(0,y.Y)(p.T.Text,{children:(0,y.Y)(X.A,{id:"BWpQZ7",defaultMessage:'You have <strong>{unsyncedDataEntriesCount}</strong> unsaved evaluated {unsyncedDataEntriesCount, plural, =1 {value} other {values}}. Click "Save" button or press {keyCombination} keys to synchronize the artifact data.',values:{strong:e=>(0,y.Y)("strong",{children:e}),unsyncedDataEntriesCount:u,keyCombination:f()?"\u2318CMD+S":"CTRL+S"}})}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:v.spacing.sm},""),children:[(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_hooks_useevaluationartifactwriteback.tsx_102",disabled:o,onClick:c,children:(0,y.Y)(X.A,{id:"kNTkr+",defaultMessage:"Discard"})})," ",u>0&&(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_hooks_useevaluationartifactwriteback.tsx_110",loading:o,type:"primary",onClick:h,children:(0,y.Y)(X.A,{id:"nhqO2Z",defaultMessage:"Save"})})]})]}):null;return{isSyncingArtifacts:o,EvaluationSyncStatusElement:x}},va=e=>{let{noEvalTablesLogged:t,userDeselectedAllColumns:n,areRunsSelected:a,areTablesSelected:r}=e;return!r||!a||n||t},_a=e=>{let{noEvalTablesLogged:t,userDeselectedAllColumns:n,areRunsSelected:a}=e;const[r,s]=a?t?[(0,y.Y)(X.A,{id:"sguNEF",defaultMessage:"No evaluation tables logged"}),(0,y.Y)(X.A,{id:"j7cj5r",defaultMessage:"Please log at least one table artifact containing evaluation data. <link>Learn more</link>.",values:{link:e=>(0,y.Y)(p.T.Link,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactviewemptystate.tsx_48",openInNewTab:!0,href:"https://mlflow.org/docs/latest/python_api/mlflow.html?highlight=log_table#mlflow.log_table",target:"_blank",rel:"noopener noreferrer",children:e})}})]:n?[(0,y.Y)(X.A,{id:"owr9l2",defaultMessage:"No group by columns selected"}),(0,y.Y)(X.A,{id:"lJQEW4",defaultMessage:'Using controls above, select at least one "group by" column.'})]:[(0,y.Y)(X.A,{id:"ZvJTXB",defaultMessage:"No tables selected"}),(0,y.Y)(X.A,{id:"18/UVG",defaultMessage:"Using controls above, select at least one artifact containing table."})]:[(0,y.Y)(X.A,{id:"BFzsMn",defaultMessage:"No runs selected"}),(0,y.Y)(X.A,{id:"nPWZsh",defaultMessage:"Make sure that at least one experiment run is visible and available to compare"})];return(0,y.Y)(c.SvL,{title:r,description:s})};var xa=n(22853),ya=n(58898);var wa={name:"1hetg88",styles:"max-width:300px"},ba={name:"111arwn",styles:"width:300px;min-width:300px"},Ya={name:"1hetg88",styles:"max-width:300px"},Ca={name:"1hetg88",styles:"max-width:300px"},Ia={name:"1ichkjj",styles:"height:100%;display:flex;justify-content:center;align-items:center"};const Aa=e=>{let{comparedRuns:t,onDatasetSelected:n,viewState:o,updateViewState:l}=e;const d=(0,Fe.A)(),{theme:g}=(0,p.u)(),h=((0,Vt.e)(),(0,r.useMemo)((()=>t.filter((e=>{let{hidden:t}=e;return!t})).slice(0,10)),[t])),{selectedTables:f,groupByCols:v,outputColumn:_,setSelectedTables:x,setGroupByCols:w,setOutputColumn:b}=((e,t)=>{const{artifactViewState:n={}}=e,[a,s]=(0,r.useState)(n.selectedTables||[]),[i,o]=(0,r.useState)(n.groupByCols||[]),[l,d]=(0,r.useState)(n.outputColumn||"");return(0,r.useEffect)((()=>t({artifactViewState:{selectedTables:a,groupByCols:i,outputColumn:l}})),[t,a,i,l]),{selectedTables:a,groupByCols:i,outputColumn:l,setSelectedTables:s,setGroupByCols:o,setOutputColumn:d}})(o,l),[Y,C]=(0,r.useState)(!1),[I,A]=(0,r.useState)(""),[k,E]=(0,r.useState)(""),[S,M]=(0,r.useState)(!1),{isSyncingArtifacts:R,EvaluationSyncStatusElement:D}=fa(),F=(0,s.wA)();(0,r.useEffect)((()=>{(0,W.Ii)()&&F(Et()).catch((e=>{T.A.logErrorAndNotifyUser((null===e||void 0===e?void 0:e.message)||e)}))}),[F]);const L=(0,r.useCallback)((e=>x((t=>t.includes(e)?t.filter((t=>t!==e)):[...t,e]))),[x]),B=(0,r.useCallback)((e=>w((t=>{const n=t.includes(e)?t.filter((t=>t!==e)):[...t,e];return M(0===n.length),n}))),[w]),N=(0,r.useMemo)((()=>h.map((e=>{let{runUuid:t}=e;return t}))),[h]),{evaluationArtifactsByRunUuid:P,evaluationPendingDataByRunUuid:H,evaluationDraftInputValues:O}=(0,s.d4)((e=>{let{evaluationData:t}=e;return t})),{tables:U,tablesByRun:V,noEvalTablesLogged:K}=(G=h,(0,r.useMemo)((()=>{const e=(0,j.fromPairs)(G.map((e=>{const t=e.tags?(0,ua.T)(e.tags):[];return[e.runUuid,t]})).filter((e=>{let[,t]=e;return t.length>0}))),t=Array.from(new Set(Object.values(e).flat())),n=t.filter((t=>G.every((n=>{var a;let{runUuid:r}=n;return null===(a=e[r])||void 0===a?void 0:a.includes(t)})))),a=0===t.length;return{tables:t,tablesByRun:e,tablesIntersection:n,noEvalTablesLogged:a}}),[G]));var G;(0,r.useEffect)((()=>{U.length>0&&0===f.length&&x([U[0]])}),[U,x,f.length]);const z=(0,s.d4)((e=>{let{evaluationData:t,modelGateway:n}=e;return n.modelGatewayRoutesLoading.loading||N.some((e=>f.some((n=>{var a;return null===(a=t.evaluationArtifactsLoadingByRunUuid[e])||void 0===a?void 0:a[n]}))))})),{columns:$,imageColumns:q}=(Q=P,J=N,Z=f,(0,r.useMemo)((()=>{if(0===Z.length||0===J.length)return{columns:[],columnsIntersection:[],imageColumns:[]};const e=J.map((e=>Object.values(Q[e]||{}).filter((e=>{let{path:t}=e;return Z.includes(t)})))).flat(),t=e.filter((e=>{let{path:t}=e;return Z.includes(t)})).map((e=>{let{columns:t,entries:n}=e;return t.map((e=>{const t=String(e);if(n.length>0){const a=n[0][e];return"object"===typeof a&&"image"===(null===a||void 0===a?void 0:a.type)?{name:t,type:"image"}:{name:t,type:"text"}}return{name:t,type:"text"}}))})).flat(),n=Array.from(new Set(t.filter((e=>"text"===e.type)).map((e=>e.name)))),a=Array.from(new Set(t.filter((e=>"image"===e.type)).map((e=>e.name)))),r=n.filter((t=>e.every((e=>{let{columns:n}=e;return n.includes(t)}))));return{columns:n,columnsIntersection:r,imageColumns:a}}),[J,Q,Z]));var Q,J,Z;const ee=q.includes(_),te=((e,t,n,a,s,i,o)=>(0,r.useMemo)((()=>{const r=[],l={},d={},c=[];for(const e of n){const t=i.map((t=>[t,e[t]])),n=t.map((e=>{let[,t]=e;return t})).join(".");d[n]=(0,j.fromPairs)(t),c.push(n)}const u={},p=a.map((t=>{const n=Object.values(e[t]||{}).filter((e=>{let{path:t}=e;return s.includes(t)})).map((e=>{let{entries:t}=e;return t})).flat();return[t,n]}));for(const[e,t]of p)for(const n of t){const{key:t,groupByValues:a}=ca(n,i);if(Object.values(a).every((e=>!e)))continue;d[t]||(d[t]=a),(n[st.Ef]||n[st.jC])&&(u[t]||(u[t]={}),u[t][e]||(u[t][e]={isPending:!1,evaluationTime:parseFloat(n[st.Ef]),totalTokens:n[st.jC]?parseInt(n[st.jC],10):void 0})),l[t]||(l[t]={});const r=l[t];r[e]=r[e]||n[o]}for(const[e,n]of Object.entries(t))for(const t of n){const{entryData:n,...a}=t,{key:r,groupByValues:s}=ca(n,i);if(Object.values(s).every((e=>!e)))continue;d[r]||(d[r]=s,c.push(r)),u[r]||(u[r]={}),u[r][e]=a,l[r]||(l[r]={});const p=l[r];p[e]=n[o]||p[e]}const m=(0,j.sortBy)(Object.entries(d),(e=>{let[t]=e;return!c.includes(t)}));for(const[e,t]of m){const n=r.find((t=>{let{key:n}=t;return e===n}));if(n&&l[e])n.cellValues=l[e],n.outputMetadataByRunUuid=u[e];else{const n=l[e];Object.keys(n||{}).forEach((e=>{if(null!==n[e]&&"object"===typeof n[e])try{const{type:t,filepath:a,compressed_filepath:r}=n[e];t===ie.Oe?n[e]={url:(0,da.To)(a,e),compressed_url:(0,da.To)(r,e)}:n[e]=JSON.stringify(n[e])}catch{n[e]=""}else(0,j.isNil)(n[e])||(0,j.isString)(n[e])||(n[e]=JSON.stringify(n[e]))})),r.push({key:e,groupByCellValues:t,cellValues:l[e]||{},outputMetadataByRunUuid:u[e],isPendingInputRow:c.includes(e)})}}return r}),[a,e,i,n,s,o,t]))(P,H,O,N,f,v,_),ne=(0,r.useMemo)((()=>{const e=h.filter(st.o1).map(st.xP).flat();if(!e.length)return null;return Array.from(new Set(e)).filter((e=>$.includes(e)))}),[h,$]);(0,r.useEffect)((()=>{h.every(st.o1)&&w([])}),[w,h]),(0,r.useEffect)((()=>{if(f.length)for(const e of h){if(!e)continue;const t=(V[e.runUuid]||[]).filter((e=>f.includes(e)));for(const n of t)F((0,i.sT)(e.runUuid,n,!1)).catch((e=>{e instanceof At.ap?T.A.displayGlobalErrorNotification(e.message):T.A.logErrorAndNotifyUser(e.message||e)}))}}),[h,F,f,V]);const ae=f.length>0,re=h.length>0,se=!z&&ae&&re,oe=(0,r.useMemo)((()=>{if(!k.trim())return te;const e=new RegExp(k,"i");return te.filter((t=>{let{groupByCellValues:n}=t;return Object.values(n).some((t=>null===t||void 0===t?void 0:t.match(e)))}))}),[te,k]),le=(0,xa.v)(t),de=(0,r.useCallback)((e=>{le(ya.oy.CUSTOM,e)}),[le]);(0,r.useEffect)((()=>{if(z||S)return;const e=v.length<1,t=v.some((e=>!$.includes(e))),n=$[0],a=ne||(n?[n]:null);(e||t)&&a&&w(a)}),[z,S,v,_,$,w,ne]);const ce=(0,r.useMemo)((()=>$.filter((e=>!e.startsWith("MLFLOW_")))),[$]),ue=(0,r.useMemo)((()=>[...$,...q].filter((e=>!v.includes(e)&&!e.startsWith("MLFLOW_")))),[$,q,v]);(0,r.useEffect)((()=>{if(v.includes(_)||!_){const e=ue.includes(st.hR)?st.hR:ue[0];b(e||"")}}),[v,_,ue,b]),(0,r.useEffect)((()=>{C(!0);const e=setTimeout((()=>E(I)),250);return()=>clearTimeout(e)}),[I]),(0,r.useEffect)((()=>{C(!1)}),[k]),(0,r.useEffect)((()=>{if(!ue.includes(_)){const e=ue.includes(st.hR)?st.hR:ue[0];b(e||"")}}),[_,ue,b]),(0,r.useEffect)((()=>{f.some((e=>!U.includes(e)))&&x([])}),[f,U,x]);const[pe,me]=(0,r.useState)(null),ge=(0,r.useCallback)(((e,t)=>{me({value:e,header:t}),l({previewPaneVisible:!0})}),[l]);return(0,y.FD)("div",{css:(0,a.AH)({flex:1,borderTop:`1px solid ${g.colors.border}`,borderLeft:`1px solid ${g.colors.border}`,marginLeft:-1,zIndex:1,height:"100%",display:"grid",gridTemplateColumns:o.previewPaneVisible?"1fr auto":"1fr",overflow:"hidden"},""),children:[(0,y.FD)("div",{css:(0,a.AH)({paddingLeft:g.spacing.sm,paddingTop:g.spacing.sm,height:"100%",display:"grid",gridTemplateRows:"auto auto 1fr",overflow:"hidden",rowGap:g.spacing.sm,backgroundColor:g.colors.backgroundSecondary},""),children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",alignItems:"center",gap:g.spacing.sm,overflow:"hidden",height:g.general.heightSm},""),children:[(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactcompareview.tsx_358",label:(0,y.Y)(X.A,{id:"syyEiR",defaultMessage:"Table"}),multiSelect:!0,value:f,children:[(0,y.Y)(c.gGe,{css:(0,a.AH)({maxWidth:300,backgroundColor:g.colors.backgroundPrimary},""),"data-testid":"dropdown-tables",onClear:()=>x([]),disabled:R||!re||K}),(0,y.Y)(c.dn6,{css:wa,children:(0,y.Y)(c.HI_,{children:U.map((e=>(0,y.Y)(c.jTC,{value:e,onChange:L,checked:f.includes(e),"data-testid":"dropdown-tables-option",children:e},e)))})})]}),(0,y.Y)(c.paO,{title:(0,y.Y)(X.A,{id:"VZRc73",defaultMessage:"Using the list of logged table artifacts, select at least one to start comparing results."}),children:(0,y.Y)(u.I,{})})]}),z?(0,y.Y)(c.PLz,{}):(0,y.FD)(y.FK,{children:[(0,y.FD)("div",{css:(0,a.AH)({display:"flex",columnGap:g.spacing.sm,alignItems:"center",overflow:"hidden",height:g.general.heightSm},""),children:[(0,y.Y)(m.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactcompareview.tsx_414",prefix:(0,y.Y)(m.S,{}),suffix:Y&&(0,y.Y)(p.S,{size:"small"}),css:ba,onChange:e=>A(e.target.value),value:I,placeholder:d.formatMessage({id:"3D2Znf",defaultMessage:"Filter by {columnNames}"},{columnNames:v.join(", ")}),allowClear:!0,disabled:!se||R}),(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactcompareview.tsx_433",value:v,multiSelect:!0,label:(0,y.Y)(X.A,{id:"fWm1UC",defaultMessage:"Group by"}),children:[(0,y.Y)(c.gGe,{disabled:!se||R,allowClear:!1,showTagAfterValueCount:1,css:(0,a.AH)({maxWidth:300,backgroundColor:g.colors.backgroundPrimary},""),"aria-label":'Select "group by" columns'}),(0,y.Y)(c.dn6,{css:Ya,children:(0,y.Y)(c.HI_,{children:ce.map((e=>(0,y.Y)(c.jTC,{value:e,onChange:B,checked:v.includes(e),children:e},e)))})})]}),(0,y.FD)(c.AYc,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_evaluation-artifacts-compare_evaluationartifactcompareview.tsx_465",value:[_],label:(0,y.Y)(X.A,{id:"ZqGzZd",defaultMessage:"Compare"}),children:[(0,y.Y)(c.gGe,{disabled:!se||R,allowClear:!1,css:(0,a.AH)({maxWidth:300,backgroundColor:g.colors.backgroundPrimary},"")}),(0,y.Y)(c.dn6,{css:Ca,children:(0,y.Y)(c.HI_,{children:ue.map((e=>(0,y.Y)(c.crD,{value:e,onChange:()=>b(e),checked:_===e,children:e},e)))})})]})]}),va({areRunsSelected:re,areTablesSelected:ae,noEvalTablesLogged:K,userDeselectedAllColumns:S})?(0,y.Y)("div",{css:Ia,children:(0,y.Y)(_a,{areRunsSelected:re,areTablesSelected:ae,noEvalTablesLogged:K,userDeselectedAllColumns:S})}):(0,y.Y)("div",{css:(0,a.AH)({position:"relative",zIndex:1,overflowY:"hidden",height:"100%",backgroundColor:g.colors.backgroundPrimary},""),children:(0,y.Y)(Cn,{tableData:te,outputColumn:_,children:(0,y.Y)(oa,{visibleRuns:h,groupByColumns:v,resultList:oe,onCellClick:ee?void 0:ge,onHideRun:de,onDatasetSelected:n,highlightedText:k.trim(),isPreviewPaneVisible:o.previewPaneVisible,outputColumnName:_,isImageColumn:ee})})}),D]})]}),o.previewPaneVisible&&(0,y.Y)(pa.V,{content:null!==pe&&void 0!==pe&&pe.value?(0,y.Y)(Kn.f,{json:pe.value}):null,copyText:(null===pe||void 0===pe?void 0:pe.value)||"",headerText:null===pe||void 0===pe?void 0:pe.header,onClose:()=>l({previewPaneVisible:!1}),empty:(0,y.Y)(c.SvL,{description:(0,y.Y)(X.A,{id:"lTngfr",defaultMessage:"Select a cell to display preview"})})})]})},ka=e=>{const{theme:t}=(0,p.u)();return e.disabled?(0,y.Y)("div",{css:(0,a.AH)({flex:1,backgroundColor:t.colors.backgroundSecondary,height:"100%",borderTop:`1px solid ${t.colors.border}`,borderLeft:`1px solid ${t.colors.border}`,paddingTop:t.spacing.lg,marginLeft:-1,zIndex:1,display:"flex",justifyContent:"center",alignItems:"center"},""),children:(0,y.Y)(c.SvL,{title:(0,y.Y)(X.A,{id:"2gxV/O",defaultMessage:"Evaluation not available when grouping is enabled"}),description:(0,y.Y)(X.A,{id:"kdTxC2",defaultMessage:"Disable run grouping in order to access the evaluation view"}),image:(0,y.Y)("div",{})})}):(0,y.Y)(Aa,{...e})};var Ea=n(30152),Sa=n(19415),Ma=n(36118),Ra=n(73150),Ta=n(75627),Da=n(39045),Fa=n(62758),La=n(63617),Ba=n(55999),Na=n(688),Pa=n(71932),Ha=n(40029),Oa=n(21039);const Ua=(e,t,n,a,r,s)=>{var i,o;return{uuid:e.runUuid,displayName:(null===(i=e.runInfo)||void 0===i?void 0:i.runName)||e.runUuid,runInfo:e.runInfo,metrics:t[e.runUuid]||{},params:n[e.runUuid]||{},tags:a[e.runUuid]||{},images:r[e.runUuid]||{},color:s,pinned:e.pinned,pinnable:e.pinnable,metricsHistory:{},belongsToGroup:null===(o=e.runDateAndNestInfo)||void 0===o?void 0:o.belongsToGroup,hidden:e.hidden}},Va=e=>{let{isLoading:t,comparedRuns:n,metricKeyList:i,paramKeyList:o,experimentTags:l,compareRunCharts:d,compareRunSections:c,groupBy:u,autoRefreshEnabled:m,hideEmptyCharts:g,globalLineChartConfig:h,chartsSearchFilter:f,minWidth:v}=e;const _=(0,Vt.e)(),x=(0,Bn.LE)(),w=(0,Fa.g_)(),{paramsByRunUuid:b,latestMetricsByRunUuid:Y,tagsByRunUuid:C,imagesByRunUuid:I}=(0,s.d4)((e=>({paramsByRunUuid:e.entities.paramsByRunUuid,latestMetricsByRunUuid:e.entities.latestMetricsByRunUuid,tagsByRunUuid:e.entities.tagsByRunUuid,imagesByRunUuid:e.entities.imagesByRunUuid}))),{theme:A}=(0,p.u)(),[k,E]=(0,r.useState)(!1),[S,M]=(0,r.useState)(null),[R,T]=(0,r.useState)(""),{formatMessage:D}=(0,Fe.A)(),F=(0,r.useMemo)((()=>(0,en.Zp)(u)),[u]),[L,B]=(0,r.useState)(void 0),N=(0,r.useCallback)((e=>{M(e)}),[]);(0,r.useEffect)((()=>{k||t||E(!0)}),[k,t]);const P=(0,r.useMemo)((()=>{const e=l[ie.S0],t=l[ie.Q4];return(null===e||void 0===e?void 0:e.value)||(null===t||void 0===t?void 0:t.value)||i[0]||""}),[l,i]),H=(0,r.useMemo)((()=>{if(!u)return n.filter((e=>e.runInfo)).map((e=>Ua(e,Y,b,C,I,x(e.runUuid))));const e=n.filter((e=>e.groupParentInfo&&!(0,en.mC)(e.groupParentInfo))).map((e=>{var t;return((e,t)=>{var n,a,r;const s=null!==(n=e.groupParentInfo)&&void 0!==n&&n.aggregatedMetricData?(0,j.keyBy)((0,j.values)(null===(a=e.groupParentInfo)||void 0===a?void 0:a.aggregatedMetricData).map((e=>{let{key:t,value:n,maxStep:a}=e;return{key:t,value:n,step:a,timestamp:0}})),"key"):{};return{uuid:e.rowUuid,displayName:(0,en.QD)(e.groupParentInfo),groupParentInfo:e.groupParentInfo,metrics:s,params:(null===(r=e.groupParentInfo)||void 0===r?void 0:r.aggregatedParamData)||{},tags:{},images:{},color:t,pinned:e.pinned,pinnable:e.pinnable,metricsHistory:{},hidden:e.hidden}})(e,x(null===(t=e.groupParentInfo)||void 0===t?void 0:t.groupId))}));return[...e,...n.filter((e=>{var t;return!e.groupParentInfo&&!(null!==(t=e.runDateAndNestInfo)&&void 0!==t&&t.belongsToGroup)})).map((e=>Ua(e,Y,b,C,I,x(e.runUuid))))]}),[u,n,Y,b,C,I,x]),O=H.filter((e=>!e.hidden&&e.tags[ie.Cr]));(0,Ba.L)({runUuids:O.map((e=>e.uuid)),runUuidsIsActive:O.map((e=>{var t;return"RUNNING"===(null===(t=e.runInfo)||void 0===t?void 0:t.status)})),enabled:!0,autoRefreshEnabled:m}),(0,r.useEffect)((()=>{if((!c||!d)&&H.length>0){const{resultChartSet:e,resultSectionSet:t}=Ea.i$.getBaseChartAndSectionConfigs({primaryMetricKey:P,runsData:H,useParallelCoordinatesChart:!0});w((n=>({...n,compareRunCharts:e,compareRunSections:t})),!0)}}),[d,c,P,H,w]),(0,r.useEffect)((()=>{w((e=>{if(!e.compareRunCharts||!e.compareRunSections)return e;const{resultChartSet:t,resultSectionSet:n,isResultUpdated:a}=Ea.i$.updateChartAndSectionConfigs({compareRunCharts:e.compareRunCharts,compareRunSections:e.compareRunSections,runsData:H,isAccordionReordered:e.isAccordionReordered});return a?{...e,compareRunCharts:t,compareRunSections:n}:e}),!0)}),[H,w]);const U=(0,r.useCallback)((e=>{_((t=>({...t,runsPinned:t.runsPinned.includes(e)?t.runsPinned.filter((t=>t!==e)):[...t.runsPinned,e]})))}),[_]),V=(0,xa.v)(n),K=(0,r.useCallback)((e=>V(ya.oy.CUSTOM,e)),[V]),G=(0,Fa.Ez)(),z=(0,Fa.KP)(),$=(0,Fa.iO)(),q=(0,Fa.cA)(),W=(0,r.useMemo)((()=>({runs:H,onTogglePin:U,onHideRun:K})),[H,K,U]),Q=(0,r.useMemo)((()=>g?null===d||void 0===d?void 0:d.filter((e=>!e.deleted&&!(0,Ma.Cs)(H,e))):null===d||void 0===d?void 0:d.filter((e=>!e.deleted))),[H,d,g]);return k?(0,y.FD)("div",{css:(0,a.AH)({flex:1,borderTop:`1px solid ${A.colors.border}`,borderLeft:`1px solid ${A.colors.border}`,marginLeft:-1,position:"relative",backgroundColor:A.colors.backgroundSecondary,paddingLeft:A.spacing.md,paddingRight:A.spacing.md,paddingBottom:A.spacing.md,zIndex:1,overflowY:"auto",minWidth:v},""),"data-testid":"experiment-view-compare-runs-chart-area",children:[(0,y.FD)("div",{css:[{paddingTop:A.spacing.sm,paddingBottom:A.spacing.sm,display:"flex",gap:A.spacing.xs,position:"sticky",top:0,zIndex:Oa.K.SEARCH_BAR,backgroundColor:A.colors.backgroundSecondary,marginLeft:-A.spacing.md,marginRight:-A.spacing.md,paddingLeft:A.spacing.md,paddingRight:A.spacing.md},""],children:[(0,y.Y)(Ha.I,{chartsSearchFilter:f}),(0,y.Y)(Na.f,{updateUIState:w,metricKeyList:i,globalLineChartConfig:h})]}),(0,y.Y)(Ta.W,{contextData:W,component:Ra.X,children:(0,y.Y)(Pa.c_,{visibleChartCards:Q,children:(0,y.Y)(Da.J,{compareRunSections:c,compareRunCharts:Q,reorderCharts:$,insertCharts:q,chartData:H,startEditChart:N,removeChart:z,addNewChartCard:e=>t=>{M(Ea.i$.getEmptyChartCardByType(t,!1,void 0,e))},search:null!==f&&void 0!==f?f:"",groupBy:F,setFullScreenChart:B,autoRefreshEnabled:m,hideEmptyCharts:g,globalLineChartConfig:h})})}),S&&(0,y.Y)(Sa.z,{chartRunData:H,metricKeyList:i,paramKeyList:o,config:S,onSubmit:e=>{G(e),M(null)},onCancel:()=>M(null),groupBy:F,globalLineChartConfig:h}),(0,y.Y)(La._,{fullScreenChart:L,onCancel:()=>B(void 0),chartData:H,groupBy:F,tooltipContextValue:W,tooltipComponent:Ra.X,autoRefreshEnabled:m,globalLineChartConfig:h})]}):(0,y.Y)(Ga,{})},Ka=e=>{const t=(0,Vt.e)(),n=(0,r.useCallback)((e=>{t((t=>({...t,...e(t)})))}),[t]);return(0,y.Y)(Fa.oB,{updateChartsUIState:n,children:(0,y.Y)(Va,{...e})})},Ga=()=>{const{theme:e}=(0,p.u)();return(0,y.Y)("div",{css:(0,a.AH)({flex:1,display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gridTemplateRows:"200px",gap:e.spacing.md,borderTop:`1px solid ${e.colors.border}`,borderLeft:`1px solid ${e.colors.border}`,marginLeft:-1,backgroundColor:e.colors.backgroundSecondary,padding:e.spacing.md,zIndex:1},""),children:new Array(6).fill(null).map(((e,t)=>(0,y.Y)(c.QvX,{lines:5,seed:t.toString()},t)))})};var za=n(4972),$a=n(42550),ja=n(25790),qa=n(63609);var Wa={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"};const Qa=e=>{let{error:t}=e;const n=null===t||void 0===t?void 0:t.message;return(0,y.Y)("div",{css:Wa,children:(0,y.Y)(c.SvL,{description:null!==n&&void 0!==n?n:(0,y.Y)(X.A,{id:"i+RyPC",defaultMessage:"Your request could not be fulfilled. Please try again."}),image:(0,y.Y)(p.W,{}),title:(0,y.Y)(X.A,{id:"2mRfvl",defaultMessage:"Request error"})})})};var Ja=n(75111);const Za=()=>{const e=new Date;return e.setMilliseconds(0),e};var Xa={name:"vm4aot",styles:"min-height:225px;height:100%;position:relative;display:flex"};const er=r.memo((e=>{var t;const[n]=ce(),{experiments:a,runsData:i,uiState:o,searchFacetsState:l,isLoadingRuns:d,loadMoreRuns:u,moreRunsAvailable:p,requestError:m,refreshRuns:g}=e,[h,f]=(0,r.useState)(new fn),{experimentId:v}=a[0],_=(0,wn.S)(v),[x,w]=(0,r.useState)("true"===_.getItem("expandRows"));(0,r.useEffect)((()=>{_.setItem("expandRows",x)}),[x,_]);const{paramKeyList:b,metricKeyList:Y,tagsList:C,paramsList:I,metricsList:A,runInfos:k,runUuidsMatchingFilter:E,datasetsList:S}=i,M=(0,s.d4)((e=>{let{entities:t}=e;return t.modelVersionsByRunUuid})),R=(0,r.useMemo)((()=>k.map(((e,t)=>({runInfo:e,params:I[t],metrics:A[t],tags:C[t],datasets:S[t]})))),[S,A,I,k,C]),{orderByKey:D,searchFilter:F}=l,{runsPinned:L,runsExpanded:B,runsHidden:N,runListHidden:P}=o,H="TABLE"!==n,O=(0,r.useCallback)((e=>f((t=>({...t,...e})))),[]),U=(0,r.useCallback)((()=>{O({columnSelectorVisible:!0})}),[O]),V=(0,r.useMemo)((()=>!D&&!F||D===ie.T8.DATE),[D,F]),[K,G]=(0,r.useState)(Za);(0,r.useEffect)((()=>{G(Za)}),[k]);const z=(0,r.useMemo)((()=>T.A.getVisibleTagKeyList(C)),[C]),[$,j]=(0,r.useState)(!1),[q,Q]=(0,r.useState)(),J=(0,r.useMemo)((()=>a.map((e=>{let{experimentId:t}=e;return t}))),[a]),Z=(e=>{const{data:t}=(0,qa.e)({experimentIds:e},{enabled:(0,W.Dz)()});return(0,r.useMemo)((()=>null===t||void 0===t?void 0:t.reduce(((e,t)=>{var n;const{source_run_id:a}=null!==(n=t.info)&&void 0!==n?n:{};return a?(e[a]||(e[a]=[]),e[a].push(t),e):e}),{})),[t])})(J),X=(0,vn.ZU)({experiments:a,paramKeyList:b,metricKeyList:Y,modelVersionsByRunUuid:M,runsExpanded:B,tagKeyList:z,nestChildren:V,referenceTime:K,runData:R,runUuidsMatchingFilter:E,runsPinned:L,runsHidden:N,groupBy:o.groupBy,groupsExpanded:o.groupsExpanded,runsHiddenMode:o.runsHiddenMode,runsVisibilityMap:o.runsVisibilityMap,useGroupedValuesInCharts:o.useGroupedValuesInCharts,searchFacetsState:l,loggedModelsV3ByRunUuid:Z}),[ee,te]=(0,c.oL1)(),ne=xn(ee),[ae,re]=(0,r.useState)(295),se=(0,r.useCallback)((()=>{p&&!d&&u().then((e=>{ne(e,k)}))}),[p,d,u,k,ne]),oe=(0,r.useCallback)(((e,t)=>{Q({datasetWithTags:e,runData:t}),j(!0)}),[]),le=(0,za.$)(),de=o.autoRefreshEnabled&&(0,W.Hn)()&&le,ue=null===(t=o.useGroupedValuesInCharts)||void 0===t||t,pe=m instanceof Error&&!d?(0,y.Y)(Qa,{error:m}):(0,y.Y)(hn.P,{experiments:a,runsData:i,searchFacetsState:l,viewState:h,isLoading:d,updateViewState:O,onAddColumnClicked:U,rowsData:X,loadMoreRunsFunc:se,moreRunsAvailable:p,onDatasetSelected:oe,expandRows:x,uiState:o,compareRunsMode:n}),me=(0,r.useMemo)((()=>a.map((e=>e.experimentId)).sort().join(",")),[a]),{resizableMaxWidth:ge,ref:he}=(0,Ja.b)(350);return(0,y.Y)(Pt,{visibleRuns:X,refreshRuns:g,children:(0,y.FD)(ja.Co,{children:[(0,y.Y)(gn,{viewState:h,updateViewState:O,runsData:i,searchFacetsState:l,experimentId:v,requestError:m,expandRows:x,updateExpandRows:w,refreshRuns:g,uiState:o,isLoading:d}),(0,y.FD)("div",{ref:he,css:Xa,children:[H?(0,y.Y)($a.t,{onResize:re,runListHidden:P,width:ae,maxWidth:ge,children:pe}):pe,"CHART"===n&&(0,y.Y)(Ka,{isLoading:d,comparedRuns:X,metricKeyList:i.metricKeyList,paramKeyList:i.paramKeyList,experimentTags:i.experimentTags,compareRunCharts:o.compareRunCharts,compareRunSections:o.compareRunSections,groupBy:ue?o.groupBy:null,autoRefreshEnabled:de,hideEmptyCharts:o.hideEmptyCharts,globalLineChartConfig:o.globalLineChartConfig,chartsSearchFilter:o.chartsSearchFilter,storageKey:me,minWidth:350}),"ARTIFACT"===n&&(0,y.Y)(ka,{comparedRuns:X,viewState:h,updateViewState:O,onDatasetSelected:oe,disabled:Boolean(o.groupBy)}),te,q&&(0,y.Y)(yn.O,{isOpen:$,setIsOpen:j,selectedDatasetWithRun:q,setSelectedDatasetWithRun:Q})]})]})})}));var tr=n(32614);function nr(e){try{return tr.A.getStoreForComponent("ExperimentPage",e).loadComponentState()}catch{return T.A.logErrorAndNotifyUser(`Error: malformed persisted search state for experiment(s) ${e}`),{...(0,ya.uY)(),...(0,Ut.G)()}}}const ar=function(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const s=(0,Ot.Px)(),i=(0,r.useMemo)((()=>n?JSON.stringify(n.sort()):null),[n]);(0,r.useEffect)((()=>{if(!a&&!t){const e=i?nr(i):null,t=(0,j.pick)({...(0,Ut.G)(),...e},Ot.sO);s(t,{replace:!0})}}),[t,i,a]),(0,r.useEffect)((()=>{var n,r;t&&i&&!a&&(n={...t,...e},r=i,tr.A.getStoreForComponent("ExperimentPage",r).saveComponentState(n))}),[t,e,i,a])};var rr=n(6604),sr=n(32378),ir=n(85017);const or=(e,t)=>{const{experiments:n}=t,a=t.experimentIds||n.map((e=>e.experimentId)),r=a.length>1,s=(e.entities.runInfoOrderByUuid||[]).map((t=>e.entities.runInfosByUuid[t])).filter((e=>{let{experimentId:t}=e;return a.includes(t)})).map((e=>{let{runUuid:t}=e;return t})),{modelVersionsByRunUuid:i,runUuidsMatchingFilter:o}=e.entities,l=((e,t,n)=>{let{lifecycleFilter:a=sr.gy.ACTIVE,modelVersionFilter:r=sr.EL.ALL_RUNS,datasetsFilter:s=[]}=n;const{modelVersionsByRunUuid:i}=t.entities;return e.map((e=>[(0,A.K4)(e,t),(0,A.jF)(e,t)])).filter((e=>{let[t,n]=e;return a===sr.gy.ACTIVE?"active"===t.lifecycleStage:"deleted"===t.lifecycleStage})).filter((e=>{let[t,n]=e;return r===sr.EL.ALL_RUNS||(r===sr.EL.WITH_MODEL_VERSIONS?t.runUuid in i:r===sr.EL.WTIHOUT_MODEL_VERSIONS?!(t.runUuid in i):(console.warn("Invalid input to model version filter - defaulting to showing all runs."),!0))})).filter((e=>{let[t,n]=e;return!s||0===s.length||!!n&&n.some((e=>{const t=e.dataset.name,n=e.dataset.digest;return s.some((e=>{let{name:a,digest:r}=e;return a===t&&r===n}))}))})).map((e=>{let[t,n]=e;return t}))})(s,e,t),d=new Set,c=new Set,u=l.map((t=>e.entities.runDatasetsByUuid[t.runUuid])),p=l.map((t=>{const n=(0,ir.d0)(t.runUuid,e),a=Object.values(n||{}).filter((e=>e.key.trim().length>0));return a.forEach((e=>{d.add(e.key)})),a})),m=l.map((t=>{const n=Object.values((0,A.tI)(t.runUuid,e)).filter((e=>e.key.trim().length>0));return n.forEach((e=>{c.add(e.key)})),n})),g=l.map((t=>(0,j.pickBy)((0,A.X3)(t.runUuid,e),(e=>e.key.trim().length>0)))),h=a[0];return{modelVersionsByRunUuid:i,experimentTags:r?{}:(0,A.xy)(h,e),runInfos:l,paramsList:m,tagsList:g,metricsList:p,runUuidsMatchingFilter:o,datasetsList:u,metricKeyList:Array.from(d.values()).sort(),paramKeyList:Array.from(c.values()).sort()}};var lr=n(69708),dr=n(39416);const cr=(e,t,n)=>{if(!e||!t.length)return null;return{...(0,rr.TB)(t,{...e,runsPinned:n},Date.now()),requestedFacets:e}},ur=function(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const o=(0,s.wA)(),[l,d]=(0,r.useState)((()=>pr())),c=(0,r.useMemo)((()=>n?JSON.stringify(n.sort()):null),[n]),[u,p]=(0,r.useState)(!0),[m,g]=(0,r.useState)(!0),[h,f]=(0,r.useState)(null),[v,_]=(0,r.useState)(null),x=(0,r.useRef)([]),y=(0,r.useRef)(null),w=(0,r.useRef)(null),b=(0,r.useRef)(null);(0,r.useEffect)((()=>{a||(g(!0),d(pr()))}),[c,a]);const Y=(0,r.useCallback)(((e,t,n)=>{d(or(e,{datasetsFilter:n.datasetsFilter,lifecycleFilter:n.lifecycleFilter,modelVersionFilter:n.modelVersionFilter,experiments:[],experimentIds:t}))}),[]);(0,r.useEffect)((()=>{x.current=e.runsPinned}),[e.runsPinned]);const C=(0,r.useCallback)((e=>{(0,rr.iH)(e||[],lr.hY,o).catch((e=>{var t;const n=null!==(t=e instanceof ke.s?(0,q.h)(e):e)&&void 0!==t?t:e,a=n instanceof ke.s?n.getMessageField():n.message;T.A.displayGlobalErrorNotification(`Failed to load model versions for runs: ${a}`)}))}),[o]),I=(0,r.useCallback)((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o(((n,a)=>(t.isAutoRefreshing||(p(!0),w.current=e),n((e.pageToken?i.e$:i.bn)(e)).then((async n=>{var r;let{value:s}=n;return y.current=Date.now(),p(!1),g(!1),_(null),w.current&&null!==(r=t.discardResultsFn)&&void 0!==r&&r.call(t,w.current,s)||(b.current=e,f(s.next_page_token||null),Y(a(),e.experimentIds,e.requestedFacets),C(s.runs||[])),s})).catch((e=>{if(p(!1),g(!1),(0,W.Ng)()){if(e instanceof dr.ZR)return void _(e);const t=(0,q.h)(e);if(t)return void _(t)}_(e),(0,W.Ng)()||T.A.logErrorAndNotifyUser(e)})))))}),[o,Y,C]);(0,r.useEffect)((()=>{if(a)return;const e=cr(t,n,x.current);e&&I(e)}),[I,o,a,t,n]);const A=(0,r.useCallback)((()=>{b.current&&I({...b.current,pageToken:void 0})}),[I]);return(e=>{let{experimentIds:t,lastFetchedTime:n,fetchRuns:a,searchFacets:s,enabled:o,cachedPinnedRuns:l,runsData:d,isLoadingRuns:c}=e;const u=(0,r.useRef)(void 0),p=(0,r.useRef)(c),m=(0,r.useRef)(o),g=(0,r.useRef)(d.runInfos);g.current=d.runInfos,p.current=c,m.current=o,(0,r.useEffect)((()=>{if(window.clearTimeout(u.current),!o||c)return;const e=async()=>{const r=Boolean(n.current),o=n.current?Date.now()-n.current:0;if(s&&r&&o>=rr.t4){const e=g.current.length,n=Math.max(1,Math.ceil(e/i.Aj))*i.Aj,r={...(0,rr.TB)(t,{...s,runsPinned:l.current},Date.now()),requestedFacets:s,maxResults:n};let o,d=0,c=0;const u=(t,a)=>{var s,i;return!!(c+(null!==(s=null===a||void 0===a||null===(i=a.runs)||void 0===i?void 0:i.length)&&void 0!==s?s:0)<n&&null!==a&&void 0!==a&&a.next_page_token)||!m.current||e>n||!(0,j.isEqual)(t.requestedFacets,r.requestedFacets)};for(;(0===d||o)&&!(c>=n);){d++;const e=await a({...r,pageToken:o},{isAutoRefreshing:!0,discardResultsFn:u});c+=(0,j.isArray)(null===e||void 0===e?void 0:e.runs)?e.runs.length:0,o=null===e||void 0===e?void 0:e.next_page_token}}window.clearTimeout(u.current),m.current&&(u.current=window.setTimeout(e,rr.t4))};return e(),()=>{clearTimeout(u.current)}}),[t,a,s,o,l,n,c])})({experimentIds:n,fetchRuns:I,searchFacets:t,enabled:e.autoRefreshEnabled&&(0,W.Hn)(),cachedPinnedRuns:x,runsData:l,isLoadingRuns:u,lastFetchedTime:y}),{isLoadingRuns:u,moreRunsAvailable:Boolean(h),refreshRuns:A,loadMoreRuns:async()=>{const e=cr(t,n,x.current);return h&&e?I({...e,pageToken:h}):[]},isInitialLoadingRuns:m,runsData:l,requestError:v}},pr=()=>({datasetsList:[],experimentTags:{},metricKeyList:[],metricsList:[],modelVersionsByRunUuid:{},paramKeyList:[],paramsList:[],runInfos:[],runUuidsMatchingFilter:[],tagsList:[]}),mr=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const[a]=(0,o.ok)(),s=(0,Fe.A)(),i=a.get(ie.ex),l=Boolean(i),d=(0,Ot.Px)(),[c,u]=(0,r.useState)(null),[p,m]=(0,r.useState)(null),[g,f]=(0,r.useState)(null),[v,_]=(0,r.useState)(null);(0,r.useEffect)((()=>{if(!i||!t)return;const e=t.tags.find((e=>{let{key:t}=e;return t===`${ie.o6}${i}`}));if(!e)return u(null),m(null),f(`Error loading shared view state: share key ${i} does not exist`),void _(s.formatMessage({id:"s2L+xL",defaultMessage:'Error loading shared view state: share key "{viewStateShareKey}" does not exist'},{viewStateShareKey:i}));(async e=>{try{const t=await(async e=>(0,se.ib)(e)?JSON.parse(await(0,se.U1)(e)):JSON.parse(e))(e.value),n=(0,j.pick)(t,Ot.sO),a=(0,j.pick)(t,ya.HC);u(n),m(a),f(null),_(null)}catch(t){u(null),m(null),f("Error loading shared view state: share key is invalid"),_(s.formatMessage({id:"Isig6r",defaultMessage:"Error loading shared view state: share key is invalid"}))}})(e)}),[t,i,s]),(0,r.useEffect)((()=>{c&&!n&&d(c,{replace:!0})}),[c,n]),(0,r.useEffect)((()=>{p&&!n&&e(p)}),[e,p,n]);const x=(0,o.Zp)();return(0,r.useEffect)((()=>{n||g&&t&&(T.A.logErrorAndNotifyUser(new Error(g)),T.A.displayGlobalErrorNotification(v,3),x(h.h.getExperimentPageRoute(t.experimentId),{replace:!0}))}),[g,v,t,x,n]),{isViewStateShared:l,sharedStateError:g}},gr=[(e,t,n,a)=>{if(a)return t;const r=n.runInfos.filter(((e,t)=>{var a,r;return(null===(a=n.tagsList[t])||void 0===a||null===(r=a[ie.yU])||void 0===r?void 0:r.value)===ie.m4})).map((e=>{let{runUuid:t}=e;return t})),s=(0,j.compact)(n.runInfos.map(((e,t)=>{var a;let{runUuid:s}=e;return r.includes(s)&&(null===(a=n.tagsList[t])||void 0===a?void 0:a[fe.Ol].value)})));return s.length?{...t,runsExpanded:s.reduce(((e,t)=>({...e,[t]:!0})),t.runsExpanded)}:t}],hr=(0,ya.uY)(),fr=(e,t)=>{if(0===e.runInfos.length||0===t.length)return null;const n=t.map((e=>e.experimentId)).sort(),a=e.runInfos.map((e=>e.runUuid)).sort();return`${n.join(":")}:${a.join(":")}`};var vr=n(56928),_r=n(75703),xr=n(11473);const yr=[["header","bold","italic","strikethrough"],["link","code","image"],["unordered-list","ordered-list"]],wr=(0,xr.OT)(),br=e=>{if(e){const t=(0,xr.NW)(wr.makeHtml(e));return(0,xr.Yc)(t)}return null},Yr=e=>{let{experiment:t,editing:n,setEditing:o,setShowAddDescriptionButton:l,onNoteUpdated:d,defaultValue:m}=e;const g=(0,s.d4)((e=>{const n=(0,A.xy)(t.experimentId,e);return n?(e=>{var t;return(null===(t=Object.values(e).find((e=>e.key===vr.e)))||void 0===t?void 0:t.value)||void 0})(n):""}));l(!g);const[h,f]=(0,r.useState)(g),[v,_]=(0,r.useState)("write"),[x,w]=(0,r.useState)(!1),{theme:b}=(0,p.u)(),Y=16+2*b.spacing.sm,C=(0,s.wA)(),I=(0,r.useCallback)((e=>{o(!1),l(!e);const n=(0,i.EJ)(t.experimentId,vr.e,e);C(n).then(d)}),[t.experimentId,C,o,l,d]);return(0,y.FD)("div",{children:[(null!==h&&void 0!==h?h:m)&&(0,y.FD)("div",{style:{whiteSpace:x?"normal":"pre-wrap",lineHeight:b.typography.lineHeightLg,background:b.colors.backgroundSecondary,display:"flex",alignItems:"flex-start",padding:b.spacing.xs},children:[(0,y.Y)("div",{style:{flexGrow:1,marginRight:12,overflow:"hidden",overflowWrap:x?"break-word":void 0,padding:`${b.spacing.sm}px 12px`,maxHeight:x?"none":Y+"px",wordBreak:"break-word"},children:(0,y.Y)("div",{dangerouslySetInnerHTML:{__html:br(null!==h&&void 0!==h?h:m)}})}),(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_experimentviewdescriptionnotes.tsx_114",icon:(0,y.Y)(c.R2l,{}),onClick:()=>o(!0),style:{padding:`0px ${b.spacing.sm}px`}}),x?(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_experimentviewdescriptionnotes.tsx_120",icon:(0,y.Y)(c.Mtm,{}),onClick:()=>w(!1),style:{padding:`0px ${b.spacing.sm}px`}}):(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_experimentviewdescriptionnotes.tsx_126",icon:(0,y.Y)(c.D3D,{}),onClick:()=>w(!0),style:{padding:`0px ${b.spacing.sm}px`}})]}),(0,y.Y)(u.d,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_experimentviewdescriptionnotes.tsx_141",title:(0,y.Y)(X.A,{id:"Vkr4Bs",defaultMessage:"Add description"}),visible:n,okText:(0,y.Y)(X.A,{id:"xUnYdk",defaultMessage:"Save"}),cancelText:(0,y.Y)(X.A,{id:"757GVc",defaultMessage:"Cancel"}),onOk:()=>{I(h),o(!1)},onCancel:()=>{f(g),o(!1)},children:(0,y.Y)(r.Fragment,{children:(0,y.Y)(_r.default,{value:h,minEditorHeight:200,maxEditorHeight:500,minPreviewHeight:20,toolbarCommands:yr,onChange:e=>f(e),selectedTab:v,onTabChange:e=>_(e),generateMarkdownPreview:()=>Promise.resolve(br(h)),getIcon:e=>(0,y.Y)(c.paO,{title:e,children:(0,y.Y)("span",{css:(0,a.AH)({color:b.colors.textPrimary},""),children:(0,y.Y)(_r.SvgIcon,{icon:e})})})})})})]})},Cr=e=>{let{experiment:t,size:n}=e;const a=(0,Fe.A)();return(0,y.Y)(p.T.Text,{size:n,dangerouslySetAntdProps:{copyable:{text:t.name,icon:(0,y.Y)(c.TdU,{}),tooltips:[a.formatMessage({id:"A7R0ii",defaultMessage:"Copy path"}),a.formatMessage({id:"JCboZ7",defaultMessage:"Path copied"})]}}})},Ir=e=>{let{artifactLocation:t}=e;return(0,y.Y)(y.FK,{children:t})},Ar=e=>{let{experiment:t}=e;const n=(0,Fe.A)();return(0,y.Y)(p.T.Text,{size:"md",dangerouslySetAntdProps:{copyable:{text:t.experimentId,icon:(0,y.Y)(c.TdU,{}),tooltips:[n.formatMessage({id:"rytnce",defaultMessage:"Copy experiment id"}),n.formatMessage({id:"qH2cN+",defaultMessage:"Experiment id copied"})]}}})},kr=e=>{let{experiment:t}=e;const n=(0,Fe.A)();return(0,y.Y)(p.T.Text,{size:"md",dangerouslySetAntdProps:{copyable:{text:t.artifactLocation,icon:(0,y.Y)(c.TdU,{}),tooltips:[n.formatMessage({id:"cSSMIs",defaultMessage:"Copy artifact location"}),n.formatMessage({id:"5aZ7nE",defaultMessage:"Artifact location copied"})]}}})};const Er=r.memo((e=>{let{experiment:t,searchFacetsState:n,uiState:s,showAddDescriptionButton:i,setEditing:o}=e;const l=(0,r.useMemo)((()=>t?[null===t||void 0===t?void 0:t.experimentId]:[]),[t]),{theme:d}=(0,p.u)(),u=(0,r.useMemo)((()=>t.name.split("/").pop()),[t.name]),m=ie.Cm;return(0,y.Y)(ee.z,{title:(0,y.Y)("div",{css:(0,a.AH)({[d.responsive.mediaQueries.xs]:{display:"inline",wordBreak:"break-all"},[d.responsive.mediaQueries.sm]:{display:"inline-block",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"middle"}},""),title:u,children:u}),titleAddOns:[(0,y.Y)("div",{style:{display:"flex"},children:(0,y.Y)(c.UyZ,{iconTitle:"Info",children:(0,y.FD)("div",{css:(0,a.AH)({display:"flex",flexDirection:"column",gap:d.spacing.xs,flexWrap:"nowrap"},""),"data-testid":"experiment-view-header-info-tooltip-content",children:[(0,y.FD)("div",{style:{whiteSpace:"nowrap"},children:[(0,y.Y)(X.A,{id:"F8MqzZ",defaultMessage:"Path"}),": ",t.name+" ",(0,y.Y)(Cr,{experiment:t,size:"md"})]}),(0,y.FD)("div",{style:{whiteSpace:"nowrap"},children:[(0,y.Y)(X.A,{id:"PZGZHV",defaultMessage:"Experiment ID"}),": ",t.experimentId+" ",(0,y.Y)(Ar,{experiment:t})]}),(0,y.FD)("div",{style:{whiteSpace:"nowrap"},children:[(0,y.Y)(X.A,{id:"8/7V3S",defaultMessage:"Artifact Location"}),": ",(0,y.Y)(Ir,{artifactLocation:t.artifactLocation})," ",(0,y.Y)(kr,{experiment:t})]})]})})}),(0,y.Y)(p.B,{href:m,target:"_blank",rel:"noreferrer",componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentviewheaderv2.tsx_100",css:(0,a.AH)({marginLeft:d.spacing.sm},""),type:"link",size:"small",endIcon:(0,y.Y)(p.at,{}),children:(0,y.Y)(X.A,{id:"d78wwA",defaultMessage:"Provide Feedback"})}),i&&(0,y.Y)(p.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_header_experimentviewheaderv2.tsx_271",size:"small",onClick:()=>{o(!0)},css:(0,a.AH)({marginLeft:d.spacing.sm,background:`${d.colors.backgroundSecondary} !important`,border:"none"},""),children:(0,y.Y)(p.T.Text,{size:"md",children:"Add Description"})})].filter(Boolean),breadcrumbs:[],spacerSize:"sm",dangerouslyAppendEmotionCSS:{[d.responsive.mediaQueries.sm]:{"& > div":{flexWrap:"nowrap"},h2:{display:"flex",overflow:"hidden"}}},children:(0,y.Y)("div",{css:(0,a.AH)({display:"flex",gap:d.spacing.sm},""),children:(0,y.Y)(ge,{experimentIds:l,searchFacetsState:n,uiState:s})})})}));var Sr={name:"1eoy87d",styles:"display:flex;justify-content:space-between"};function Mr(){const{theme:e}=(0,p.u)();return(0,y.FD)("div",{css:(0,a.AH)({height:2*e.general.heightSm},""),children:[(0,y.Y)("div",{css:(0,a.AH)({height:e.spacing.lg},""),children:(0,y.Y)(c.xUE,{css:(0,a.AH)({width:100,height:e.spacing.md},""),loading:!0})}),(0,y.FD)("div",{css:Sr,children:[(0,y.Y)("div",{children:(0,y.Y)(c.xUE,{css:(0,a.AH)({width:160,height:e.general.heightSm},""),loading:!0})}),(0,y.FD)("div",{css:(0,a.AH)({display:"flex",gap:e.spacing.sm},""),children:[(0,y.Y)(c.xUE,{css:(0,a.AH)({width:100,height:e.general.heightSm},""),loading:!0}),(0,y.Y)(c.xUE,{css:(0,a.AH)({width:60,height:e.general.heightSm},""),loading:!0})]})]})]})}var Rr=n(91089);const Tr=e=>{let{experimentIds:t}=e;const{theme:n}=(0,p.u)();return(0,y.FD)("div",{css:(0,a.AH)({minHeight:225,marginTop:n.spacing.sm,display:"flex",flexDirection:"column",gap:n.spacing.sm,flex:1,overflow:"hidden"},""),children:[(0,y.Y)(Xt,{hideBorder:!1}),(0,y.Y)(Dr,{experimentIds:t})]})},Dr=e=>{let{experimentIds:t}=e;return(0,y.Y)(Rr.O,{experimentIds:t})};var Fr={name:"1g7ixto",styles:"height:100%;display:flex;align-items:center;justify-content:center"};const Lr=()=>(0,y.Y)("div",{css:Fr,children:(0,y.Y)(c.SvL,{description:(0,y.Y)(X.A,{id:"VMVNTR",defaultMessage:"Requested experiment was not found."}),image:(0,y.Y)(c.xfv,{}),title:(0,y.Y)(X.A,{id:"/sk75d",defaultMessage:"Experiment not found"})})});var Br={name:"1g7ixto",styles:"height:100%;display:flex;align-items:center;justify-content:center"};const Nr=()=>(0,y.Y)("div",{css:Br,children:(0,y.Y)(c.SvL,{description:(0,y.Y)(X.A,{id:"PSFpHG",defaultMessage:"You don't have permissions to open requested experiment."}),image:(0,y.Y)(c.xfv,{}),title:(0,y.Y)(X.A,{id:"cIV9pX",defaultMessage:"Permission denied"})})});var Pr={name:"1wnowod",styles:"display:flex;align-items:center;justify-content:center"};const Hr=e=>{let{error:t,image:n,title:a,button:r,className:s}=e;return(0,y.Y)("div",{css:Pr,className:s,children:(0,y.Y)(c.SvL,{description:t.message,image:null!==n&&void 0!==n?n:(0,y.Y)(p.W,{}),title:null!==a&&void 0!==a?a:(0,y.Y)(X.A,{id:"G/zTTY",defaultMessage:"Error"}),button:r})})};var Or={name:"13udsys",styles:"height:100%"},Ur={name:"1h8i7is",styles:"overflow-y:hidden;flex-shrink:0;transition:max-height .12s"};const Vr=()=>{const e=(0,s.wA)(),{theme:t}=(0,p.u)(),[n,a,o]=(0,Ot.sR)(),[l]=ce(),d=(u=a,(0,s.d4)((e=>u.map((t=>e.entities.experimentsById[t])).filter(Boolean)),((e,t)=>(0,j.isEqual)(e,t))));var u;const[m]=d,{fetchExperiments:g,isLoadingExperiment:h,requestError:f}=(()=>{const e=(0,r.useContext)(Q);if(!e)throw new Error("Trying to use GetExperimentsContext actions outside of the context!");return e})(),{elementHeight:v,observeHeight:_}=(e=>{const[t,n]=(0,r.useState)(null),[a,s]=(0,r.useState)(void 0);return(0,r.useEffect)((()=>{if(!t||!window.ResizeObserver)return;const n=new ResizeObserver((t=>{let[n]=t;null===e||void 0===e||e(n),n.target.scrollHeight&&s(n.target.scrollHeight)}));return n.observe(t),()=>n.disconnect()}),[t,e]),{elementHeight:a,observeHeight:n}})(),[x,w]=(0,r.useState)(!1),[b,Y]=(0,r.useState)(!0),[C,I,A]=(e=>{const t=(0,r.useMemo)((()=>JSON.stringify(e.sort())),[e]),[n,a]=(0,r.useState)(null),[{uiState:s,isSeeded:i,isFirstVisit:o},l]=((0,Ot.Px)(),(0,r.useReducer)(((e,t)=>{if("UPDATE_UI_STATE"===t.type){const n="function"===typeof t.payload?t.payload(e.uiState):t.payload;return{...e,uiState:n}}return"INITIAL_UI_STATE_SEEDED"===t.type?e.isSeeded?e:{...e,isSeeded:!0}:"LOAD_NEW_EXPERIMENT"===t.type?{uiState:t.payload.uiState,isFirstVisit:t.payload.isFirstVisit,currentPersistKey:t.payload.newPersistKey,isSeeded:t.payload.isSeeded}:e}),void 0,(()=>{const e=nr(t),n=Boolean((0,j.keys)(e||{}).length),a=n?(0,j.pick)(e,ya.HC):{};return{uiState:{...hr,...a},isSeeded:n,isFirstVisit:!n,currentPersistKey:t}}))),d=(0,r.useCallback)((e=>{l({type:"UPDATE_UI_STATE",payload:e})}),[]),c=(0,r.useCallback)(((e,t)=>{if(!o||0===e.length||0===t.runInfos.length)return;const r=fr(t,e);n===r&&i||i&&!(0,W.kj)()||(d((n=>gr.reduce(((n,a)=>a(e,n,t,i)),{...n}))),a(r),i||l({type:"INITIAL_UI_STATE_SEEDED"}))}),[i,o,d,n]);return(0,r.useEffect)((()=>{const e=nr(t),n=(0,j.pick)(e,ya.HC),a=Boolean((0,j.keys)(e||{}).length),r=!a;l({type:"LOAD_NEW_EXPERIMENT",payload:{uiState:{...hr,...n},isSeeded:a,isFirstVisit:r,newPersistKey:t}})}),[t]),[s,d,c]})(a),{isViewStateShared:k}=mr(I,(0,j.first)(d)),E=C.viewMaximized,{isLoadingRuns:S,loadMoreRuns:M,runsData:R,moreRunsAvailable:D,requestError:F,refreshRuns:L}=ur(C,n,a);(0,r.useEffect)((()=>{((0,W.Dz)()||(0,W.$Y)())&&a.every((e=>d.find((t=>t.experimentId===e))))||g(a)}),[g,a,d]),(0,r.useEffect)((()=>{A(d,R)}),[A,d,R]),(0,r.useEffect)((()=>{const t=(0,i.wT)(a);e(t).catch((e=>{(0,W.Ng)()||T.A.logErrorAndNotifyUser(e)}))}),[e,a]);const B=a.length>1;ar(C,n,a,k||o);const N=Boolean(!h&&d[0]&&R&&n);if(f instanceof dr.m_||f instanceof ke.s&&f.getErrorCode()===Z.tG.RESOURCE_DOES_NOT_EXIST)return(0,y.Y)(Lr,{});if(f instanceof dr.i_||f instanceof ke.s&&f.getErrorCode()===Z.tG.PERMISSION_DENIED)return(0,y.Y)(Nr,{});if(f instanceof Error)return(0,y.Y)(Hr,{css:Or,error:f});if(!N)return(0,y.Y)(c.PLz,{});Ze()(n,"searchFacets should be initialized at this point");const P=h||!d[0];return(0,y.Y)(Vt.i,{setUIState:I,children:(0,y.FD)("div",{css:Kr.experimentViewWrapper,children:[P?(0,y.Y)(c.PLz,{title:!0,paragraph:!1,active:!0}):(0,y.Y)(y.FK,{children:B?(0,y.Y)(he,{experiments:d}):(0,y.FD)(y.FK,{children:[(0,y.Y)(Er,{experiment:m,searchFacetsState:n||void 0,uiState:C,showAddDescriptionButton:b,setEditing:w}),(0,y.Y)("div",{style:{maxHeight:E?0:v},css:Ur,children:(0,y.Y)("div",{ref:_,children:(0,y.Y)(Yr,{experiment:m,setShowAddDescriptionButton:Y,editing:x,setEditing:w})})})]})}),(0,W.XK)()&&"TRACES"===l?(0,y.Y)(Tr,{experimentIds:a}):(0,y.Y)(er,{isLoading:!1,experiments:d,isLoadingRuns:S,runsData:R,searchFacetsState:n,loadMoreRuns:M,moreRunsAvailable:D,requestError:F,refreshRuns:L,uiState:C})]})})},Kr={experimentViewWrapper:{height:"100%",display:"flex",flexDirection:"column"}};var Gr=n.p+"static/media/no-experiments.0e4f4a114ef73e7d81c09474aba64b6c.svg";const zr=function(){const{theme:e}=(0,p.u)();return(0,y.FD)("div",{className:"center",children:[(0,y.Y)("img",{alt:"No experiments found.",style:{height:"200px",marginTop:"80px"},src:Gr}),(0,y.Y)("h1",{style:{paddingTop:"10px"},children:"No Experiments Exist"}),(0,y.FD)("h2",{css:(0,a.AH)({color:e.colors.textSecondary},""),children:["To create an experiment use the ",(0,y.Y)("a",{href:Z.d1,children:"mlflow experiments"})," CLI."]})]})},$r=e=>{let{experimentId:t="",activeTab:n}=e;const{theme:r}=(0,p.u)(),s=e=>[h.h.getExperimentPageRoute(t),[oe,e].join("=")].join("?"),i=(0,y.Y)(X.A,{id:"SCqKp8",defaultMessage:"Evaluation"}),l=(0,y.FD)("span",{css:(0,a.AH)({display:"inline-flex",gap:r.spacing.xs,alignItems:"center"},""),children:[(0,y.Y)(p.T.Text,{disabled:!0,bold:!0,children:i}),(0,y.Y)(c.UyZ,{popoverProps:{maxWidth:350},children:(0,y.Y)(X.A,{id:"sFPlmE",defaultMessage:'Accessing artifact evaluation by "Evaluation" tab is being discontinued. In order to use this feature, use <link>"Artifacts evaluation" mode in Runs tab</link> instead.',values:{link:e=>(0,y.Y)(o.N_,{to:s("ARTIFACT"),children:e})}})})]}),d=(0,W.GV)()?(0,y.Y)(y.FK,{children:l}):(0,y.FD)(o.N_,{to:s("ARTIFACT"),children:[i,(0,y.Y)(Qe.W,{})]});return(0,y.Y)(c.KSe.Root,{children:(0,y.FD)(c.KSe.List,{css:(0,a.AH)({marginBottom:0,li:{lineHeight:r.typography.lineHeightBase,marginRight:r.spacing.lg,paddingTop:1.5*r.spacing.xs,paddingBottom:1.5*r.spacing.xs,"&>a":{padding:0},alignItems:"center"},"li+li":{marginLeft:.5*r.spacing.xs}},""),children:[(0,y.Y)(c.KSe.Item,{children:(0,y.Y)(o.N_,{to:s("TABLE"),children:(0,y.Y)(X.A,{id:"qpFaad",defaultMessage:"Runs"})})},"RUNS"),(0,W.Dz)()&&(0,y.Y)(c.KSe.Item,{active:n===ie.fM.Models,children:(0,y.FD)(o.N_,{to:h.h.getExperimentPageTabRoute(t,ie.fM.Models),children:[(0,y.Y)(X.A,{id:"b8rdDM",defaultMessage:"Models"}),(0,y.Y)(Qe.W,{})]})},"MODELS"),(0,y.Y)(c.KSe.Item,{children:d},"ARTIFACT"),(0,W.XK)()&&(0,y.Y)(c.KSe.Item,{children:(0,y.Y)(o.N_,{to:s("TRACES"),children:(0,y.Y)(X.A,{id:"AGQOPV",defaultMessage:"Traces"})})},"TRACES")]})})};var jr=n(72314);var qr=n(26626);const Wr=e=>{var t,n;let{experiment:s,loading:i,onNoteUpdated:o,error:l}=e;const{theme:d}=(0,p.u)(),[u,m]=(0,r.useState)(!0),[g,h]=(0,r.useState)(!1),f=(0,r.useMemo)((()=>{const e=s;return e?{...e,creationTime:Number(null===e||void 0===e?void 0:e.creationTime),lastUpdateTime:Number(null===e||void 0===e?void 0:e.lastUpdateTime)}:null}),[s]),v=null===f||void 0===f||null===(t=f.tags)||void 0===t||null===(n=t.find((e=>e.key===vr.e)))||void 0===n?void 0:n.value,_=(0,qr.b)(l);return i?(0,y.Y)(Mr,{}):_?(0,y.Y)("div",{css:(0,a.AH)({height:d.general.heightBase,marginTop:d.spacing.sm,marginBottom:d.spacing.md},""),children:(0,y.Y)(c.FcD,{componentId:"mlflow.logged_model.list.header.error",type:"error",message:(0,y.Y)(X.A,{id:"E4Te7L",defaultMessage:"Experiment load error: {errorMessage}",values:{errorMessage:_}}),closable:!1})}):f?(0,y.FD)(y.FK,{children:[(0,y.Y)(Er,{experiment:f,showAddDescriptionButton:u,setEditing:h}),(0,y.Y)(Yr,{experiment:f,setShowAddDescriptionButton:m,editing:g,setEditing:h,onNoteUpdated:o,defaultValue:v})]}):null};var Qr=n(81313);const Jr=r.lazy((()=>Promise.all([n.e(1570),n.e(7581),n.e(7951),n.e(7367)]).then(n.bind(n,63250)))),Zr=()=>{const{experimentId:e,tabName:t}=(0,o.g)(),n=(0,Qr.SK)(ie.fM,t,ie.fM.Models);Ze()(e,"Experiment ID must be defined"),Ze()(t,"Tab name must be defined");const{data:a,loading:d,refetch:p,apiError:m,apolloError:g}=(0,jr.L)({experimentId:e}),h=null!==m&&void 0!==m?m:g;if((e=>{const t=(0,s.wA)();(0,r.useEffect)((()=>{const n=(0,j.get)(e,"experimentId");e&&n&&t(((t,a)=>{var r,s;Boolean(null===(r=a().entities)||void 0===r||null===(s=r.experimentsById)||void 0===s?void 0:s[n])||t({type:(0,l.ec)(i.gT),payload:{experiment:e}})}))}),[e,t])})(a),h instanceof dr.ZR)throw h;return(0,y.FD)(y.FK,{children:[(0,y.Y)(Wr,{experiment:a,loading:d,onNoteUpdated:p,error:h}),(0,y.Y)(u.S,{size:"sm",shrinks:!1}),(0,y.Y)($r,{experimentId:e,activeTab:n}),(0,y.Y)(u.S,{size:"sm",shrinks:!1}),(0,y.Y)(r.Suspense,{fallback:(0,y.Y)(c.QvX,{lines:8}),children:n===ie.fM.Models&&(0,y.Y)(Jr,{})})]})};var Xr=()=>{const{theme:e}=(0,p.u)();return(0,y.Y)("div",{css:(0,a.AH)({flex:1,overflow:"hidden",display:"flex",flexDirection:"column",padding:e.spacing.md,paddingTop:e.spacing.lg},""),children:(0,y.Y)(Zr,{})})};const es={setExperimentTagApi:i.EJ,getExperimentApi:i.yc,setCompareExperiments:i.I_};var ts={name:"1ichkjj",styles:"height:100%;display:flex;justify-content:center;align-items:center"},ns={name:"1s36c6h",styles:"display:flex;height:calc(100% - 60px)"},as={name:"tby1uk",styles:"height:100%;padding-top:24px;display:flex"};var rs=()=>{const e=(0,s.wA)(),{theme:t}=(0,p.u)(),n=(0,r.useRef)((0,l.yk)()),{tabName:c}=(0,o.g)(),u=(0,W.Dz)()&&Boolean(c),m=(0,$.z)(),g=(0,s.d4)((e=>(0,j.values)(e.entities.experimentsById))),f=g.length>0;if((0,r.useEffect)((()=>{e((0,i.vF)(n.current))}),[e]),!m.length){const e=(e=>[...e].sort(T.A.compareExperiments).find((e=>{let{lifecycleStage:t}=e;return"active"===t})))(g);if(e)return(0,y.Y)(o.C5,{to:h.h.getExperimentPageRoute(e.experimentId),replace:!0})}const v=(0,y.Y)("div",{css:ts,children:(0,y.Y)(p.S,{size:"large"})});return(0,y.Y)(d.Ay,{requestIds:[n.current],customSpinner:v,children:(0,y.FD)("div",{css:ns,children:[(0,y.Y)("div",{css:as,children:(0,y.Y)(z,{activeExperimentIds:m||[],experiments:g})}),u&&(0,y.Y)(Xr,{}),!u&&(0,y.Y)("div",{css:(0,a.AH)({height:"100%",flex:1,padding:t.spacing.md,paddingTop:t.spacing.lg},""),children:(0,y.Y)(J,{actions:es,children:f?(0,y.Y)(Vr,{}):(0,y.Y)(zr,{})})})]})})}},28380:function(e,t,n){n.d(t,{S:function(){return s}});var a=n(31014),r=n(32614);const s=e=>(0,a.useMemo)((()=>r.A.getStoreForComponent("ExperimentView",e)),[e])},42550:function(e,t,n){n.d(t,{t:function(){return u}});var a=n(89555),r=n(32599),s=n(31014),i=n(15230),o=n(21879),l=n(50111);var d={name:"1h0bf8r",styles:"body, :host{user-select:none;}"},c={name:"5ob2ly",styles:"display:flex;position:relative"};const u=e=>{let{runListHidden:t,width:n,onResize:r,children:u,onHiddenChange:m,maxWidth:g}=e;const h=(0,o.e)(),[f,v]=(0,s.useState)(!1);return(0,l.FD)(l.FK,{children:[(0,l.Y)(i.ResizableBox,{css:c,style:{flex:`0 0 ${t?0:n}px`},width:n,axis:"x",resizeHandles:["e"],minConstraints:[250,0],maxConstraints:void 0===g?void 0:[g,0],handle:(0,l.Y)(p,{runListHidden:t,updateRunListHidden:e=>{m?m(e):h((t=>({...t,runListHidden:e})))}}),onResize:(e,n)=>{let{size:a}=n;t||r(a.width)},onResizeStart:()=>!t&&v(!0),onResizeStop:()=>v(!1),children:u}),f&&(0,l.Y)(a.mL,{styles:d})]})},p=s.forwardRef(((e,t)=>{let{updateRunListHidden:n,runListHidden:s,...i}=e;const{theme:o}=(0,r.u)();return(0,l.FD)("div",{ref:t,...i,css:(0,a.AH)({transition:"opacity 0.2s",width:0,overflow:"visible",height:"100%",position:"relative",zIndex:10,display:"flex",opacity:s?1:0,"&:hover":{opacity:1,".bar":{opacity:1},".button":{border:`2px solid ${o.colors.actionDefaultBorderHover}`}}},""),children:[(0,l.Y)("div",{css:(0,a.AH)({position:"absolute",left:-o.general.iconSize/2,width:o.general.iconSize,cursor:s?void 0:"ew-resize",height:"100%",top:0,bottom:0},""),children:(0,l.Y)("div",{className:"button",css:(0,a.AH)({top:"50%",transition:"border-color 0.2s",position:"absolute",width:o.general.iconSize,height:o.general.iconSize,backgroundColor:o.colors.backgroundPrimary,borderRadius:o.general.iconSize,overflow:"hidden",border:`1px solid ${o.colors.border}`,display:"flex",alignItems:"center",justifyContent:"center",zIndex:11},""),children:(0,l.Y)(r.B,{componentId:"mlflow.experiment_page.table_resizer.collapse",onClick:()=>n(!s),icon:s?(0,l.Y)(r.q,{}):(0,l.Y)(r.o,{}),size:"small"})})}),(0,l.Y)("div",{className:"bar",css:(0,a.AH)({position:"absolute",opacity:0,left:-1.5,width:3,height:"100%",top:0,bottom:0,backgroundColor:o.colors.actionPrimaryBackgroundDefault},"")})]})}))},62448:function(e,t,n){n.d(t,{h:function(){return o}});var a=n(39416),r=n(52350),s=n(47664);class i{}i.mlflowServices={MODEL_REGISTRY:"Model Registry",EXPERIMENTS:"Experiments",MODEL_SERVING:"Model Serving",RUN_TRACKING:"Run Tracking"};const o=(e,t)=>{if(!(e instanceof r.s))return;const{status:n}=e;let i;const o={status:n};e.getErrorCode()===s.tG.RESOURCE_DOES_NOT_EXIST&&(i=new a.m_(o)),e.getErrorCode()===s.tG.PERMISSION_DENIED&&(i=new a.i_(o)),e.getErrorCode()===s.tG.INTERNAL_ERROR&&(i=new a.PO(o)),e.getErrorCode()===s.tG.INVALID_PARAMETER_VALUE&&(i=new a.v7(o));const l=e.getMessageField();return i&&l&&(i.message=l),i};t.A=i},72314:function(e,t,n){n.d(t,{L:function(){return i}});var a=n(56675),r=n(95947);const s=a.J1`
  query MlflowGetExperimentQuery($input: MlflowGetExperimentInput!) @component(name: "MLflow.ExperimentRunTracking") {
    mlflowGetExperiment(input: $input) {
      apiError {
        code
        message
      }
      experiment {
        artifactLocation
        creationTime
        experimentId
        lastUpdateTime
        lifecycleStage
        name
        tags {
          key
          value
        }
      }
    }
  }
`,i=e=>{var t;let{experimentId:n,options:a={}}=e;const{data:i,loading:o,error:l,refetch:d}=(0,r.I)(s,{variables:{input:{experimentId:n}},skip:!n,...a});return{loading:o,data:null===i||void 0===i||null===(t=i.mlflowGetExperiment)||void 0===t?void 0:t.experiment,refetch:d,apolloError:l,apiError:(()=>{var e;return null===i||void 0===i||null===(e=i.mlflowGetExperiment)||void 0===e?void 0:e.apiError})()}}},87877:function(e,t,n){n.d(t,{J:function(){return s},N:function(){return i}});var a=n(63528),r=n(46795);const s=e=>(t,n,r)=>{n?e().includes(n)?r(`Experiment "${n}" already exists.`):a.x.getExperimentByName({experiment_name:n}).then((e=>r(`Experiment "${n}" already exists in deleted state.\n                                 You can restore the experiment, or permanently delete the\n                                 experiment from the .trash folder (under tracking server's\n                                 root folder) in order to use this experiment name again.`))).catch((e=>r(void 0))):r(void 0)},i=(e,t,n)=>{t?r.x.getRegisteredModel({name:t}).then((()=>n(`Model "${t}" already exists.`))).catch((e=>n(void 0))):n(void 0)}},96070:function(e,t,n){n.d(t,{$K:function(){return o},M8:function(){return u},jP:function(){return c},tY:function(){return i}});var a=n(31014),r=n(98590),s=n(50111);const i=e=>e.filter((e=>!e.startsWith(r.nt))).map((e=>e.includes('"')||e.includes(" ")||e.includes(".")?`\`${e}\``:e.includes("`")?`"${e}"`:e)),o=(e,t)=>[{label:"Metrics",options:e.metricNames.map((e=>({value:`metrics.${e}`})))},{label:"Parameters",options:e.paramNames.map((e=>({value:`params.${e}`})))},{label:"Tags",options:e.tagNames.map((e=>({value:`tags.${e}`})))},{label:"Attributes",options:t}];var l={name:"lugakg",styles:"font-weight:normal"};const d=(e,t)=>{const n=e.split(RegExp(t.replace(".","\\."),"ig")),r=e.match(RegExp(t.replace(".","\\."),"ig"));return(0,s.Y)("span",{css:l,"data-test-id":e,children:n.map(((e,t)=>(0,s.FD)(a.Fragment,{children:[e,t!==n.length-1&&r&&(0,s.Y)("b",{children:r[t]})]},t)))})},c=e=>{const t=/>|<|>=|<=|=|!=|like|ilike/gi,n=(e=>{const t=/and[\s]+/gi,n=[];let a,r;for(;r=t.lastIndex,a=t.exec(e);)n.push({clause:e.substring(r,a.index),startIndex:r});return n.push({clause:e.substring(r),startIndex:r}),n})(e),a=[];return n.forEach((e=>{const n=e.clause.split(t)[0],{startIndex:r}=e;a.push({name:n,startIndex:0+r,endIndex:n.length+r})})),a},u=(e,t,n)=>e.map((e=>{const a=e.options.filter((e=>e.value.toLowerCase().includes(t.name.toLowerCase().trim()))).map((e=>({value:e.value,label:d(e.value,t.name.trim())}))),r=n[e.label],s=[...a.slice(0,r),...a.length>r?[{label:"...",value:`..._${e.label}`}]:[]];return{label:e.label,options:s}})).filter((e=>e.options.length>0))}}]);
//# sourceMappingURL=experimentPage.7db6acf6.chunk.js.map