/*! For license information please see 701.bdee1a81.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[701],{3288:function(e,t,i){i.d(t,{z:function(){return r}});var s=i(32599),o=i(31014);const r=()=>{const{theme:e}=(0,s.u)(),t=!1,i=(0,o.useMemo)((()=>({})),[e,t]),r=(0,o.useMemo)((()=>({marginTop:4*e.spacing.md})),[e,t]),n=(0,o.useMemo)((()=>[{flex:"1",display:"flex",alignItems:"center",justifyContent:"center"},t]),[e,t]);return{usingUnifiedDetailsLayout:t,detailsPageTableStyles:i,detailsPageNoEntriesStyles:n,detailsPageNoResultsWrapperStyles:r}}},4874:function(e,t,i){i.d(t,{Z:function(){return n}});var s=i(89555),o=i(32599),r=i(50111);const n=e=>{let{title:t,value:i}=e;const{theme:n}=(0,o.u)();return(0,r.FD)("tr",{css:(0,s.AH)({display:"flex",borderBottom:`1px solid ${n.colors.borderDecorative}`,minHeight:n.general.heightSm},""),children:[(0,r.Y)("th",{css:(0,s.AH)({flex:"0 0 240px",backgroundColor:n.colors.backgroundSecondary,color:n.colors.textSecondary,padding:n.spacing.sm,display:"flex",alignItems:"flex-start"},""),children:t}),(0,r.Y)("td",{css:(0,s.AH)({flex:1,padding:n.spacing.sm,paddingTop:0,paddingBottom:0,display:"flex",alignItems:"center"},""),children:i})]})}},5643:function(e,t,i){i.d(t,{Ay:function(){return te}});var s=i(89555),o=i(9133),r=i.n(o),n=i(31014),a=i(88443),d=i(25869),l=i(10811),c=i(64912),h=i(93215),p=i(64408),u=i(38566),g=i(61902),m=i.n(g),f=i(85466),v=i(62680),M=i(81866),w=i(76010),A=i(69869),b=i(32599),y=i(48012),x=i(93358),I=i(58481),N=i(50111);const Y=e=>{var t;let{loggedModelId:i}=e;const{data:o}=(0,x.b)({loggedModelId:i}),r=null===o||void 0===o||null===(t=o.info)||void 0===t?void 0:t.experiment_id,{theme:n}=(0,b.u)();return(0,N.Y)(y.FcD,{type:"info",componentId:"mlflow.artifacts.logged_model_fallback_info",message:(0,N.Y)(a.A,{id:"pcNJvo",defaultMessage:"You're viewing artifacts assigned to a <link>logged model</link> associated with this run.",values:{link:e=>r?(0,N.Y)(h.N_,{to:I.h.getExperimentLoggedModelDetailsPage(r,i),children:e}):(0,N.Y)(N.FK,{children:e})}}),closable:!1,css:(0,s.AH)({margin:n.spacing.xs},"")})};var S=i(72877),R=i(65871),_=i(26809),C=i(25866),k=i(77484),T=i(17275),F=i(98590),D=i(56412),L=i(91144);const{Text:E}=b.T;var H={name:"ou8xsw",styles:"flex:0 0 auto"},z={name:"1l0i7xs",styles:"flex:1 1;overflow:hidden"},P={name:"i3oi5p",styles:"flex:0 1"};class V extends n.Component{constructor(){super(...arguments),this.state={activeNodeId:void 0,toggledNodeIds:{},requestedNodeIds:new Set,viewAsTable:!0},this.onToggleTreebeard=(e,t)=>{const{id:i,loading:s}=e,o=(0,L.Dz)()&&this.props.isLoggedModelsMode,r=new Set(this.state.requestedNodeIds);s&&!this.state.requestedNodeIds.has(i)&&(o&&this.props.loggedModelId?this.props.listArtifactsLoggedModelApi(this.props.loggedModelId,i):this.props.listArtifactsApi(this.props.runUuid,i)),this.setState({activeNodeId:i,toggledNodeIds:{...this.state.toggledNodeIds,[i]:t},requestedNodeIds:r})},this.getTreebeardData=e=>{const{isRoot:t}=e;if(t){if(e.children)return Object.values(e.children).map((e=>this.getTreebeardData(e)));throw Error("unreachable code.")}let i,s,o,r,n;if(e.fileInfo){const{path:t}=e.fileInfo;i=t,s=(0,p.hA)(t)}const a=this.state.toggledNodeIds[i];a&&(o=a),e.children&&(r=Object.values(e.children).map((e=>this.getTreebeardData(e)))),this.state.activeNodeId===i&&(n=!0);return{id:i,name:s,toggled:o,children:r,active:n,loading:void 0!==e.children&&!e.isLoaded}}}getExistingModelVersions(){const{modelVersionsBySource:e}=this.props;return e[w.A.normalize(this.getActiveNodeRealPath())]}renderRegisterModelButton(){const{runUuid:e}=this.props,{activeNodeId:t}=this.state,i=this.getActiveNodeRealPath();return(0,N.Y)(f.vR,{runUuid:e,modelPath:i,modelRelativePath:String(t),disabled:void 0===t,showButton:!0,buttonType:void 0})}renderModelVersionInfoSection(e,t){return(0,N.Y)(q,{modelVersion:r().last(e),intl:this.props.intl})}renderPathAndSizeInfo(){const e=u.x.findChild(this.props.artifactNode,this.state.activeNodeId),t=this.getActiveNodeRealPath();return(0,N.FD)("div",{className:"artifact-info-left",children:[(0,N.FD)("div",{className:"artifact-info-path",children:[(0,N.Y)("label",{children:(0,N.Y)(a.A,{id:"axF2gD",defaultMessage:"Full Path:"})})," ",(0,N.Y)(E,{className:"artifact-info-text",ellipsis:!0,copyable:!0,children:t})]}),!1===e.fileInfo.is_dir?(0,N.FD)("div",{className:"artifact-info-size",children:[(0,N.Y)("label",{children:(0,N.Y)(a.A,{id:"mpHsCg",defaultMessage:"Size:"})})," ",m()(this.getActiveNodeSize())]}):null]})}renderSizeInfo(){const e=u.x.findChild(this.props.artifactNode,this.state.activeNodeId),{theme:t}=this.props.designSystemThemeApi;return(0,N.FD)("div",{style:{display:"flex",alignItems:"center",gap:t.spacing.sm,overflow:"hidden",textOverflow:"ellipsis"},children:[(0,N.Y)(b.T.Text,{bold:!0,size:"lg",ellipsis:!0,title:this.state.activeNodeId,children:this.state.activeNodeId}),!1===e.fileInfo.is_dir&&(0,N.Y)(b.T.Text,{color:"secondary",children:m()(this.getActiveNodeSize())})]})}renderPathInfo(){const e=this.getActiveNodeRealPath(),{theme:t}=this.props.designSystemThemeApi;return(0,N.FD)("div",{css:(0,s.AH)({display:"flex",overflow:"hidden",alignItems:"center",gap:t.spacing.sm},""),children:[(0,N.FD)("div",{css:(0,s.AH)({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",flex:"0 auto",color:t.colors.textSecondary},""),title:e,children:[(0,N.Y)(a.A,{id:"YZDyOo",defaultMessage:"Path:"})," ",e]}),(0,N.Y)(D.i,{css:H,showLabel:!1,size:"small",type:"tertiary",copyText:e,icon:(0,N.Y)(y.TdU,{})})]})}onDownloadClick(e,t,i,s){e&&!s?window.location.href=(0,k.To)(t,e):i&&(window.location.href=(0,k.qk)(t,i))}renderControls(){const{runUuid:e,loggedModelId:t,isFallbackToLoggedModelArtifacts:i}=this.props,{activeNodeId:s}=this.state;return(0,N.Y)("div",{style:{display:"flex",alignItems:"flex-start"},children:(0,N.FD)("div",{style:{display:"inline-flex",alignItems:"center"},children:[this.shouldShowViewAsTableCheckbox&&(0,N.Y)(y.Sc0,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_artifactview.tsx_288",isChecked:this.state.viewAsTable,onChange:()=>this.setState({viewAsTable:!this.state.viewAsTable}),children:(0,N.Y)(a.A,{id:"zv4Ycc",defaultMessage:"View as table"})}),(0,N.Y)(y.paO,{arrowPointAtCenter:!0,placement:"topLeft",title:this.props.intl.formatMessage({id:"Y9ZFyN",defaultMessage:"Download artifact"}),children:(0,N.Y)(b.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_artifactview.tsx_337",icon:(0,N.Y)(y.s3U,{}),onClick:()=>this.onDownloadClick(e,s,t,i)})})]})})}renderArtifactInfo(){const e=this.getExistingModelVersions();let t;t=e&&w.A.isModelRegistryEnabled()?this.renderModelVersionInfoSection(e,this.props.intl):this.activeNodeCanBeRegistered()&&w.A.isModelRegistryEnabled()?this.renderRegisterModelButton():this.activeNodeIsDirectory()?null:this.renderControls();const{theme:i}=this.props.designSystemThemeApi;return(0,N.FD)("div",{css:(0,s.AH)({padding:`${i.spacing.xs}px ${i.spacing.sm}px ${i.spacing.sm}px ${i.spacing.md}px`,display:"flex",flexDirection:"column",gap:i.spacing.xs},""),children:[(0,N.FD)("div",{css:(0,s.AH)({whiteSpace:"nowrap",display:"flex",justifyContent:"space-between",alignItems:"center",gap:i.spacing.md},""),children:[(0,N.Y)("div",{css:z,children:this.renderSizeInfo()}),(0,N.Y)("div",{css:P,children:t})]}),this.renderPathInfo()]})}getActiveNodeRealPath(){return this.state.activeNodeId?`${this.props.artifactRootUri}/${this.state.activeNodeId}`:this.props.artifactRootUri}getActiveNodeSize(){if(this.state.activeNodeId){const e=u.x.findChild(this.props.artifactNode,this.state.activeNodeId).fileInfo.file_size||"0";return parseInt(e,10)}return 0}activeNodeIsDirectory(){if(this.state.activeNodeId){return u.x.findChild(this.props.artifactNode,this.state.activeNodeId).fileInfo.is_dir}return!0}activeNodeCanBeRegistered(){if(this.state.activeNodeId){const e=u.x.findChild(this.props.artifactNode,this.state.activeNodeId);if(e&&e.children&&C.eh in e.children)return!0}return!1}componentDidUpdate(e,t){const{activeNodeId:i}=this.state;t.activeNodeId!==i&&this.props.handleActiveNodeChange(this.activeNodeIsDirectory())}componentDidMount(){if(this.props.initialSelectedArtifactPath){const t=this.props.initialSelectedArtifactPath.split("/");if(t)try{u.x.findChild(this.props.artifactNode,this.props.initialSelectedArtifactPath)}catch(e){return void console.error(e)}let i="";const s={activeNodeId:this.props.initialSelectedArtifactPath,toggledNodeIds:{}};t.forEach((e=>{i+=e,s.toggledNodeIds[i]=!0,i+="/"})),this.setArtifactState(s)}}setArtifactState(e){this.setState(e)}get shouldShowViewAsTableCheckbox(){return this.state.activeNodeId&&this.props.runTags&&(0,F.L9)(this.props.runTags).includes(this.state.activeNodeId)}render(){if(!this.props.artifactNode||u.x.isEmpty(this.props.artifactNode))return(0,N.Y)(O,{useAutoHeight:this.props.useAutoHeight});const{theme:e}=this.props.designSystemThemeApi,{loggedModelId:t,isLoggedModelsMode:i}=this.props;return(0,N.FD)("div",{className:"artifact-view",css:(0,s.AH)({flex:this.props.useAutoHeight?1:"unset",height:this.props.useAutoHeight?"auto":void 0,[e.responsive.mediaQueries.xs]:{overflowX:"auto"}},""),children:[(0,N.Y)("div",{style:{minWidth:"200px",maxWidth:"400px",flex:1,whiteSpace:"nowrap",borderRight:`1px solid ${e.colors.borderDecorative}`},children:(0,N.Y)(T.K,{data:this.getTreebeardData(this.props.artifactNode),onToggleTreebeard:this.onToggleTreebeard})}),(0,N.FD)("div",{className:"artifact-right",children:[this.props.isFallbackToLoggedModelArtifacts&&this.props.loggedModelId&&(0,N.Y)(Y,{loggedModelId:this.props.loggedModelId}),this.state.activeNodeId?this.renderArtifactInfo():null,(0,N.Y)(v.A,{experimentId:this.props.experimentId,runUuid:this.props.runUuid,path:this.state.activeNodeId,isDirectory:this.activeNodeIsDirectory(),size:this.getActiveNodeSize(),runTags:this.props.runTags,artifactRootUri:this.props.artifactRootUri,modelVersions:this.props.modelVersions,showArtifactLoggedTableView:this.state.viewAsTable&&this.shouldShowViewAsTableCheckbox,loggedModelId:t,isLoggedModelsMode:i})]})]})}}const B={listArtifactsApi:_.cN,listArtifactsLoggedModelApi:_.bb},U=(0,l.Ng)(((e,t)=>{var i;const{runUuid:s,loggedModelId:o,isLoggedModelsMode:n}=t,{apis:a}=e,d=n&&o?(0,S.ej)(o,e):(0,S.ej)(s,e),l=null!==(i=null===t||void 0===t?void 0:t.artifactRootUri)&&void 0!==i?i:(0,S.vb)(s,e),c=(0,R.U_)(e),h=r().flatMap(c,(e=>({...e,source:w.A.normalize(e.source)})));return{artifactNode:d,artifactRootUri:l,modelVersions:c,modelVersionsBySource:r().groupBy(h,"source"),apis:a}}),B)((0,b.as)((0,c.Ay)(V)));function q(e){const{modelVersion:t,intl:i}=e,{name:s,version:o,status:r,status_message:d}=t;let l=A.fM.getModelVersionPageRoute(s,o);const c=(0,N.Y)(y.paO,{title:`${s} version ${o}`,children:(0,N.FD)(h.N_,{to:l,className:"model-version-link",target:"_blank",rel:"noreferrer",children:[(0,N.Y)("span",{className:"model-name",children:s}),(0,N.FD)("span",{children:[",\xa0v",o,"\xa0"]}),(0,N.Y)("i",{className:"fas fa-external-link-o"})]})});return(0,N.FD)("div",{className:"model-version-info",children:[(0,N.FD)("div",{className:"model-version-link-section",children:[(0,N.Y)(y.paO,{title:d||M.zr[r],children:(0,N.Y)("div",{children:M.UA[r]})}),c]}),(0,N.Y)("div",{className:"model-version-status-text",children:r===M.IP.READY?(0,N.Y)(n.Fragment,{children:(0,N.Y)(a.A,{id:"nVndit",defaultMessage:"Registered on {registeredDate}",values:{registeredDate:w.A.formatTimestamp(t.creation_timestamp,i)}})}):d||M.zA[r]})]})}function O(e){let{useAutoHeight:t}=e;const{theme:i}=(0,b.u)();return(0,N.Y)("div",{css:(0,s.AH)({flex:t?1:"unset",height:t?"auto":void 0,paddingTop:i.spacing.md,display:"flex",justifyContent:"center",alignItems:"center"},""),children:(0,N.Y)(y.SvL,{image:(0,N.Y)(y.BfH,{}),title:(0,N.Y)(a.A,{id:"X3F7x3",defaultMessage:"No Artifacts Recorded"}),description:(0,N.Y)(a.A,{id:"3geT+I",defaultMessage:"Use the log artifact APIs to store file outputs from MLflow runs."})})})}var $=i(69708),K=i(53140),j=i(7204),W=i(8220),G=i(57596),Q=i(52350);class X extends n.Component{constructor(){super(...arguments),this.pollIntervalId=void 0,this.getFailedtoListArtifactsMsg=()=>(0,N.Y)("span",{children:(0,N.Y)(a.A,{id:"V4CsL/",defaultMessage:"Unable to list artifacts stored under {artifactUri} for the current run. Please contact your tracking server administrator to notify them of this error, which can happen when the tracking server lacks permission to list artifacts under the current run's root artifact directory.",values:{artifactUri:this.props.artifactRootUri}})}),this.state={activeNodeIsDirectory:!1,errorThrown:!1},this.searchRequestId=(0,j.yk)(),this.listArtifactRequestIds=[(0,j.yk)()].concat(this.props.initialSelectedArtifactPath?this.props.initialSelectedArtifactPath.split("/").map((e=>(0,j.yk)())):[]),this.pollModelVersionsForCurrentRun=async()=>{const{apis:e,runUuid:t,isLoggedModelsMode:i}=this.props,{activeNodeIsDirectory:s}=this.state,o=e[this.searchRequestId];if((!i||t)&&s&&(!o||!o.active))try{await this.props.searchModelVersionsApi({run_id:t},this.searchRequestId)}catch(r){if(!this.state.errorThrown){const e=`Error while fetching model version for run: ${r instanceof Error?r.toString():JSON.stringify(r)}`;w.A.logErrorAndNotifyUser(e),this.setState({errorThrown:!0})}}},this.handleActiveNodeChange=e=>{this.setState({activeNodeIsDirectory:e})},this.pollArtifactsForCurrentRun=async()=>{const{runUuid:e,loggedModelId:t}=this.props,i=(0,L.Dz)()&&this.props.isLoggedModelsMode;if(i&&t?await this.props.listArtifactsLoggedModelApi(this.props.loggedModelId,void 0,this.listArtifactRequestIds[0]):await this.props.listArtifactsApi(e,void 0,this.listArtifactRequestIds[0]),this.props.initialSelectedArtifactPath){const s=this.props.initialSelectedArtifactPath.split("/");let o="";for(let r=0;r<s.length;r++)o+=s[r],i&&t?await this.props.listArtifactsLoggedModelApi(this.props.loggedModelId,o,this.listArtifactRequestIds[r+1]):await this.props.listArtifactsApi(e,o,this.listArtifactRequestIds[r+1]),o+="/"}},this.renderErrorCondition=e=>e,this.renderArtifactView=(e,t,i)=>{if(e&&!t)return(0,N.Y)(W.d,{});if(this.renderErrorCondition(t)){const e=i[0];e&&e.error&&console.error(e.error);const t=(()=>{const t=null===e||void 0===e?void 0:e.error;return t instanceof Q.s?t.getMessageField():this.getFailedtoListArtifactsMsg()})();return(0,N.Y)(G.F,{css:(0,s.AH)({flex:this.props.useAutoHeight?1:"unset",height:this.props.useAutoHeight?"auto":void 0},""),"data-testid":"artifact-view-error",description:t})}return(0,N.Y)(U,{...this.props,handleActiveNodeChange:this.handleActiveNodeChange,useAutoHeight:this.props.useAutoHeight})}}componentDidMount(){w.A.isModelRegistryEnabled()&&(this.pollModelVersionsForCurrentRun(),this.pollIntervalId=setInterval(this.pollModelVersionsForCurrentRun,M.Gs)),this.pollArtifactsForCurrentRun()}componentDidUpdate(e){e.runUuid!==this.props.runUuid&&this.setState({errorThrown:!1}),!e.isFallbackToLoggedModelArtifacts&&this.props.isFallbackToLoggedModelArtifacts&&this.pollArtifactsForCurrentRun()}componentWillUnmount(){w.A.isModelRegistryEnabled()&&clearInterval(this.pollIntervalId)}render(){return(0,N.Y)(K.Ay,{requestIds:this.listArtifactRequestIds,children:this.renderArtifactView})}}const J=["/Volumes/","dbfs:/Volumes/"],Z={listArtifactsApi:_.cN,listArtifactsLoggedModelApi:_.bb,searchModelVersionsApi:$.hY},ee=(0,l.Ng)(((e,t)=>{var i;const{runUuid:s,location:n,runOutputs:a}=t,d=((null===n||void 0===n?void 0:n.pathname)||"").match(/\/(?:artifactPath|artifacts)\/(.+)/),{isFallbackToLoggedModelArtifacts:l,fallbackLoggedModelId:c}=((e,t)=>{const i=J.some((e=>{var i;return null===(i=t.artifactRootUri)||void 0===i?void 0:i.startsWith(e)}));if((0,L.Dz)()&&!t.isLoggedModelsMode&&!i){var s,r;const i=(0,S.ej)(t.runUuid,e),n=i&&!i.fileInfo&&(0,o.isEmpty)(i.children),a=null===(s=(0,o.first)(null===(r=t.runOutputs)||void 0===r?void 0:r.modelOutputs))||void 0===s?void 0:s.modelId;if(n&&a)return{isFallbackToLoggedModelArtifacts:!0,fallbackLoggedModelId:a}}return{isFallbackToLoggedModelArtifacts:!1}})(e,t),h=(null===d||void 0===d?void 0:d[1])||void 0,{apis:p}=e,u=null!==(i=t.artifactRootUri)&&void 0!==i?i:(0,S.vb)(s,e);let g=h;if(!g){var m;const e=(0,F.x1)(null!==(m=t.runTags)&&void 0!==m?m:{});e.length>0&&(g=r().first(e))}return{artifactRootUri:u,apis:p,initialSelectedArtifactPath:g,isLoggedModelsMode:!!l||t.isLoggedModelsMode,loggedModelId:l?c:t.loggedModelId,isFallbackToLoggedModelArtifacts:l}}),Z)(X);var te=(0,d.h)(ee)},14830:function(e,t,i){i.d(t,{N:function(){return a}});var s=i(89555),o=i(32599),r=i(50111);var n={name:"4zleql",styles:"display:block"};const a=e=>{let{children:t}=e;const{theme:i}=(0,o.u)();return(0,r.Y)("table",{css:(0,s.AH)({display:"block",border:`1px solid ${i.colors.borderDecorative}`,borderBottom:"none",borderRadius:i.general.borderRadiusBase,width:"50%",minWidth:640,marginBottom:i.spacing.lg,overflow:"hidden"},""),children:(0,r.Y)("tbody",{css:n,children:t})})}},41261:function(e,t,i){i.d(t,{I:function(){return n}});var s=i(28999),o=i(45586),r=i(44200);function n(e,t,i){const n=(0,s.vh)(e,t,i);return(0,r.t)(n,o.$)}},59508:function(e,t,i){i.d(t,{_C:function(){return a},pc:function(){return r},so:function(){return n}});var s=i(81866),o=i(69708);function r(e){return e?`${s.Qs} ilike ${(0,o.GP)(e,!0)}`:""}function n(){let{query:e=""}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=[],i=e.includes("tags.")?e:r(e);return i&&t.push(i),t.join(" AND ")}function a(e){return"searchInput"in e?e.searchInput:"nameSearchInput"in e&&"tagSearchInput"in e?r(e.nameSearchInput)+" AND "+e.tagSearchInput:"tagSearchInput"in e?e.tagSearchInput:"nameSearchInput"in e?e.nameSearchInput:""}},61902:function(e){e.exports=function(e,t){if("string"===typeof e)return n(e);if("number"===typeof e)return r(e,t);return null},e.exports.format=r,e.exports.parse=n;var t=/\B(?=(\d{3})+(?!\d))/g,i=/(?:\.0*|(\.[^0]+)0+)$/,s={b:1,kb:1024,mb:1<<20,gb:1<<30,tb:1024*(1<<30)},o=/^((-|\+)?(\d+(?:\.\d+)?)) *(kb|mb|gb|tb)$/i;function r(e,o){if(!Number.isFinite(e))return null;var r=Math.abs(e),n=o&&o.thousandsSeparator||"",a=o&&o.unitSeparator||"",d=o&&void 0!==o.decimalPlaces?o.decimalPlaces:2,l=Boolean(o&&o.fixedDecimals),c=o&&o.unit||"";c&&s[c.toLowerCase()]||(c=r>=s.tb?"TB":r>=s.gb?"GB":r>=s.mb?"MB":r>=s.kb?"KB":"B");var h=(e/s[c.toLowerCase()]).toFixed(d);return l||(h=h.replace(i,"$1")),n&&(h=h.replace(t,n)),h+a+c}function n(e){if("number"===typeof e&&!isNaN(e))return e;if("string"!==typeof e)return null;var t,i=o.exec(e),r="b";return i?(t=parseFloat(i[1]),r=i[4].toLowerCase()):(t=parseInt(e,10),r="b"),Math.floor(s[r]*t)}},65287:function(e,t,i){i.d(t,{a:function(){return r}});var s=i(81313),o=i(50111);const r=e=>{let{children:t,className:i,secondarySections:r=[],usingSidebarLayout:n}=e;return n?(0,o.Y)("div",{className:i,children:(0,o.Y)(s.u8,{secondarySections:r,isTabLayout:!0,sidebarSize:"lg",verticalStackOrder:"secondary-first",children:t})}):(0,o.Y)("div",{className:i,children:t})}},67212:function(e,t,i){i.d(t,{t:function(){return c}});var s=i(89555),o=i(32599),r=i(48012),n=i(56412),a=i(50111);var d={name:"l8l8b8",styles:"white-space:nowrap;overflow:hidden;text-overflow:ellipsis"},l={name:"ozd7xs",styles:"flex-shrink:0"};const c=e=>{let{value:t,className:i,element:c}=e;const{theme:h}=(0,o.u)();return(0,a.FD)("div",{css:(0,s.AH)({display:"flex",gap:h.spacing.xs,alignItems:"center"},""),className:i,children:[(0,a.Y)("span",{css:d,children:null!==c&&void 0!==c?c:t}),(0,a.Y)(n.i,{showLabel:!1,copyText:t,icon:(0,a.Y)(r.TdU,{}),size:"small",css:l})]})}},68109:function(e,t,i){i.d(t,{y:function(){return b}});var s=i(89555),o=i(32599),r=i(48012),n=i(41028),a=i(9133),d=i(31014),l=i(88443),c=i(88464),h=i(70618),p=i(9856),u=i(50111);const g=e=>{let{value:t}=e;const i=(0,d.useMemo)((()=>{try{const e=JSON.parse(t);return JSON.stringify(e,null,2)}catch(e){return null}}),[t]);return(0,u.Y)("div",{css:(0,s.AH)({whiteSpace:"pre-wrap",wordBreak:"break-word",fontFamily:i?"monospace":void 0},""),children:i||t})};var m=i(91144),f=i(3288);var v={name:"ozd7xs",styles:"flex-shrink:0"};const M=e=>{let{name:t,value:i,toggleExpanded:n,isExpanded:l,autoExpandedRowsList:c}=e;const{theme:h}=(0,o.u)(),p=(0,d.useRef)(null),[m,f]=(0,d.useState)(!1);return(0,d.useEffect)((()=>{c[t]||m&&(n(),c[t]=!0)}),[c,m,t,n]),(0,d.useEffect)((()=>{if(!p.current)return;const e=(0,a.throttle)((e=>{let[t]=e;const i=t.target.scrollHeight>t.target.clientHeight;f(i)}),500,{trailing:!0}),t=new ResizeObserver(e);return t.observe(p.current),()=>t.disconnect()}),[p,n]),(0,d.useEffect)((()=>{if(p.current&&!l){p.current.scrollHeight>p.current.clientHeight&&f(!0)}}),[l]),(0,u.FD)("div",{css:(0,s.AH)({display:"flex",gap:h.spacing.xs},""),children:[(m||l)&&(0,u.Y)(o.B,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewparamstable.tsx_74",size:"small",icon:l?(0,u.Y)(r.D3D,{}):(0,u.Y)(o.q,{}),onClick:()=>n(),css:v}),(0,u.Y)("div",{title:i,css:(0,s.AH)({overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitBoxOrient:"vertical",WebkitLineClamp:l?void 0:"3"},""),ref:p,children:l?(0,u.Y)(g,{value:i}):i})]})},w=[{id:"key",accessorKey:"key",header:()=>(0,u.Y)(l.A,{id:"r+KCRg",defaultMessage:"Parameter"}),enableResizing:!0,size:240},{id:"value",header:()=>(0,u.Y)(l.A,{id:"qIsNL0",defaultMessage:"Value"}),accessorKey:"value",enableResizing:!1,meta:{styles:{paddingLeft:0}},cell:e=>{let{row:{original:t,getIsExpanded:i,toggleExpanded:s},table:{options:{meta:o}}}=e;const{autoExpandedRowsList:r}=o;return(0,u.Y)(M,{name:t.key,value:t.value,isExpanded:i(),toggleExpanded:s,autoExpandedRowsList:r.current})}}];var A={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"};const b=e=>{let{params:t}=e;const{theme:i}=(0,o.u)(),g=(0,c.A)(),[v,b]=(0,d.useState)(""),y=(0,d.useRef)({}),{detailsPageTableStyles:x,detailsPageNoEntriesStyles:I,detailsPageNoResultsWrapperStyles:N}=(0,f.z)(),Y=(0,d.useMemo)((()=>(0,a.values)(t)),[t]),S=(0,d.useMemo)((()=>Y.filter((e=>{let{key:t,value:i}=e;const s=v.toLowerCase();return t.toLowerCase().includes(s)||i.toLowerCase().includes(s)}))),[v,Y]),R=(0,d.useMemo)((()=>(0,m.rR)()?w:[{id:"key",accessorKey:"key",header:()=>(0,u.Y)(l.A,{id:"r+KCRg",defaultMessage:"Parameter"}),enableResizing:!0,size:240},{id:"value",header:()=>(0,u.Y)(l.A,{id:"qIsNL0",defaultMessage:"Value"}),accessorKey:"value",enableResizing:!1,meta:{styles:{paddingLeft:0}},cell:e=>{let{row:{original:t,getIsExpanded:i,toggleExpanded:s}}=e;return(0,u.Y)(M,{name:t.key,value:t.value,isExpanded:i(),toggleExpanded:s,autoExpandedRowsList:y.current})}}]),[]),_=(0,h.N4)({data:S,getCoreRowModel:(0,p.HT)(),getExpandedRowModel:(0,p.D0)(),getRowId:e=>e.key,enableColumnResizing:!0,columnResizeMode:"onChange",columns:R,meta:{autoExpandedRowsList:y}});return(0,u.FD)("div",{css:A,children:[(0,u.Y)(o.T.Title,{level:4,children:(0,u.Y)(l.A,{id:"Q/evEc",defaultMessage:"Parameters ({length})",values:{length:S.length}})}),(0,u.Y)("div",{css:(0,s.AH)({padding:i.spacing.sm,border:`1px solid ${i.colors.borderDecorative}`,borderRadius:i.general.borderRadiusBase,flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},""),children:(()=>{if(!Y.length)return(0,u.Y)("div",{css:I,children:(0,u.Y)(r.SvL,{description:(0,u.Y)(l.A,{id:"6UqPlE",defaultMessage:"No parameters recorded"})})});const e=S.length<1;return(0,u.FD)(u.FK,{children:[(0,u.Y)("div",{css:(0,s.AH)({marginBottom:i.spacing.sm},""),children:(0,u.Y)(n.I,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewparamstable.tsx_213",prefix:(0,u.Y)(n.S,{}),placeholder:g.formatMessage({id:"SLHSXV",defaultMessage:"Search parameters"}),value:v,onChange:e=>b(e.target.value),allowClear:!0})}),(0,u.FD)(r.XIK,{scrollable:!0,empty:e?(0,u.Y)("div",{css:N,children:(0,u.Y)(r.SvL,{description:(0,u.Y)(l.A,{id:"QD6iZA",defaultMessage:"No parameters match the search filter"})})}):null,css:x,children:[(0,u.Y)(r.Hjg,{isHeader:!0,children:_.getLeafHeaders().map(((e,t)=>(0,u.Y)(r.A0N,{componentId:"codegen_mlflow_app_src_experiment-tracking_components_run-page_overview_runviewparamstable.tsx_244",header:e,column:e.column,setColumnSizing:_.setColumnSizing,isResizing:e.column.getIsResizing(),css:(0,s.AH)({flexGrow:e.column.getCanResize()?0:1},""),style:{flexBasis:e.column.getCanResize()?e.column.getSize():void 0},children:(0,h.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),_.getRowModel().rows.map((e=>(0,u.Y)(r.Hjg,{children:e.getAllCells().map((e=>{var t;return(0,u.Y)(r.nA6,{css:null===(t=e.column.columnDef.meta)||void 0===t?void 0:t.styles,style:{flexGrow:e.column.getCanResize()?0:1,flexBasis:e.column.getCanResize()?e.column.getSize():void 0},multiline:!0,children:(0,h.Kv)(e.column.columnDef.cell,e.getContext())},e.id)}))},e.id)))]})]})})()})]})}},85466:function(e,t,i){i.d(t,{vR:function(){return b}});var s=i(31014),o=i(9133),r=i.n(o),n=i(32599),a=i(48012),d=i(15579),l=i(88443),c=i(64912),h=i(87665),p=i(69708),u=i(10811),g=i(76010),m=i(7204),f=i(59508),v=i(50111);class M extends s.Component{constructor(){super(),this.form=void 0,this.state={visible:!1,confirmLoading:!1,modelByName:{}},this.createRegisteredModelRequestId=(0,m.yk)(),this.createModelVersionRequestId=(0,m.yk)(),this.searchModelVersionRequestId=(0,m.yk)(),this.showRegisterModal=()=>{this.setState({visible:!0})},this.hideRegisterModal=()=>{var e,t;this.setState({visible:!1}),null===(e=(t=this.props).onCloseModal)||void 0===e||e.call(t)},this.resetAndClearModalForm=()=>{var e,t,i;this.setState({visible:!1,confirmLoading:!1}),null===(e=this.form.current)||void 0===e||e.resetFields(),null===(t=(i=this.props).onCloseModal)||void 0===t||t.call(i)},this.handleRegistrationFailure=e=>{this.setState({confirmLoading:!1}),g.A.logErrorAndNotifyUser(e)},this.handleSearchRegisteredModels=e=>{this.props.searchRegisteredModelsApi((0,f.pc)(e),5)},this.reloadModelVersionsForCurrentRun=()=>{const{runUuid:e}=this.props;return this.props.searchModelVersionsApi({run_id:e},this.searchModelVersionRequestId)},this.handleRegisterModel=()=>this.form.current.validateFields().then((e=>{this.setState({confirmLoading:!0});const{runUuid:t,modelPath:i}=this.props,s=e[h.BE];var r,n,a,d;return s===h.QN?this.props.createRegisteredModelApi(e[h.F3],this.createRegisteredModelRequestId).then((()=>this.props.createModelVersionApi(e[h.F3],i,t,[],this.createModelVersionRequestId,this.props.loggedModelId))).then(null!==(r=this.props.onRegisterSuccess)&&void 0!==r?r:o.identity).then(this.resetAndClearModalForm).catch(null!==(n=this.props.onRegisterFailure)&&void 0!==n?n:this.handleRegistrationFailure).then(this.reloadModelVersionsForCurrentRun).catch(g.A.logErrorAndNotifyUser):this.props.createModelVersionApi(s,i,t,[],this.createModelVersionRequestId,this.props.loggedModelId).then(null!==(a=this.props.onRegisterSuccess)&&void 0!==a?a:o.identity).then(this.resetAndClearModalForm).catch(null!==(d=this.props.onRegisterFailure)&&void 0!==d?d:this.handleRegistrationFailure).then(this.reloadModelVersionsForCurrentRun).catch(g.A.logErrorAndNotifyUser)})),this.form=s.createRef()}componentDidMount(){this.props.searchRegisteredModelsApi()}componentDidUpdate(e,t){!1===t.visible&&!0===this.state.visible&&this.props.searchRegisteredModelsApi()}renderRegisterModelForm(){const{modelByName:e}=this.props;return(0,v.Y)(h.xH,{modelByName:e,innerRef:this.form,onSearchRegisteredModels:r().debounce(this.handleSearchRegisteredModels,300)})}renderFooter(){return[(0,v.Y)(n.B,{componentId:"codegen_mlflow_app_src_model-registry_components_registermodel.tsx_242",onClick:this.hideRegisterModal,children:(0,v.Y)(l.A,{id:"iwD/Dv",defaultMessage:"Cancel"})},"back"),(0,v.Y)(n.B,{componentId:"codegen_mlflow_app_src_model-registry_components_registermodel.tsx_248",type:"primary",onClick:()=>this.handleRegisterModel(),"data-test-id":"confirm-register-model",children:(0,v.Y)(l.A,{id:"wBFfWW",defaultMessage:"Register"})},"submit")]}renderHelper(e,t,i){const{visible:s,confirmLoading:o}=this.state,{showButton:r=!0,buttonType:c}=this.props;return(0,v.FD)("div",{className:"register-model-btn-wrapper",children:[r&&(0,v.Y)(a.paO,{title:this.props.tooltip||null,placement:"left",children:(0,v.Y)(n.B,{componentId:"codegen_mlflow_app_src_model-registry_components_registermodel.tsx_261",className:"register-model-btn",type:c,onClick:this.showRegisterModal,disabled:e,htmlType:"button",children:(0,v.Y)(l.A,{id:"YQ+Eon",defaultMessage:"Register model"})})}),(0,v.Y)(d.d,{title:this.props.intl.formatMessage({id:"qSpgg/",defaultMessage:"Register model"}),width:540,visible:this.props.modalVisible||s,onOk:()=>this.handleRegisterModel(),okText:this.props.intl.formatMessage({id:"9W3kev",defaultMessage:"Register"}),confirmLoading:o,onCancel:this.hideRegisterModal,centered:!0,footer:i,children:t})]})}render(){const{disabled:e}=this.props;return this.renderHelper(e,this.renderRegisterModelForm(),this.renderFooter())}}const w={createRegisteredModelApi:p.eF,createModelVersionApi:p.Ey,searchModelVersionsApi:p.hY,searchRegisteredModelsApi:p.JK},A=(0,c.Ay)(M),b=(0,u.Ng)((e=>({modelByName:e.entities.modelByName})),w)(A)},87665:function(e,t,i){i.d(t,{QN:function(){return h},F3:function(){return u},xH:function(){return g},BE:function(){return p}});var s=i(31014),o=i(48012),r=i(41028),n=i(88443),a=i(50111);const{Option:d,OptGroup:l}=o._vn,c="Create New Model",h=`$$$__${c}__$$$`,p="selectedModel",u="modelName";class g extends s.Component{constructor(){super(...arguments),this.state={selectedModel:null},this.handleModelSelectChange=e=>{this.setState({selectedModel:e})},this.modelNameValidator=(e,t,i)=>{const{modelByName:s}=this.props;i(s[t]?`Model "${t}" already exists.`:void 0)},this.handleFilterOption=(e,t)=>-1!==(t&&t.value||"").toLowerCase().indexOf(e.toLowerCase())}renderExplanatoryText(){const{isCopy:e}=this.props,{selectedModel:t}=this.state;if(!t||t===h)return null;const i=e?(0,a.Y)(n.A,{id:"hxMEqi",defaultMessage:"The model version will be copied to {selectedModel} as a new version.",values:{selectedModel:t}}):(0,a.Y)(n.A,{id:"zjXq2X",defaultMessage:"The model will be registered as a new version of {selectedModel}.",values:{selectedModel:t}});return(0,a.Y)("p",{className:"modal-explanatory-text",children:i})}renderModel(e){return(0,a.Y)(d,{value:e.name,children:e.name},e.name)}render(){const{modelByName:e,innerRef:t,isCopy:i}=this.props,{selectedModel:s}=this.state,n=s===h;return(0,a.FD)(o.SQ4,{ref:t,layout:"vertical",className:"register-model-form",children:[(0,a.Y)(o.SQ4.Item,{label:i?(0,a.Y)("b",{children:"Copy to model"}):"Model",name:p,rules:[{required:!0,message:"Please select a model or create a new one."}],children:(0,a.FD)(o._vn,{dropdownClassName:"model-select-dropdown",onChange:this.handleModelSelectChange,placeholder:"Select a model",filterOption:this.handleFilterOption,onSearch:this.props.onSearchRegisteredModels,showSearch:!0,children:[(0,a.FD)(d,{value:h,className:"create-new-model-option",children:[(0,a.Y)("i",{className:"fa fa-plus fa-fw",style:{fontSize:13}})," ",c]}),(0,a.Y)(l,{label:"Models",children:Object.values(e).map((e=>this.renderModel(e)))})]})}),n?(0,a.Y)(o.SQ4.Item,{label:"Model Name",name:u,rules:[{required:!0,message:"Please input a name for the new model."},{validator:this.modelNameValidator}],children:(0,a.Y)(r.I,{componentId:"codegen_mlflow_app_src_model-registry_components_registermodelform.tsx_132",placeholder:"Input a model name"})}):null,this.renderExplanatoryText()]})}}},93358:function(e,t,i){i.d(t,{b:function(){return l},v:function(){return c}});var s=i(41261),o=i(77735),r=i(8986),n=i(84174);const a=e=>["GET_LOGGED_MODEL",e],d=async e=>{let{queryKey:[,t]}=e;return(0,r.G)(`ajax-api/2.0/mlflow/logged-models/${t}`,"GET")},l=e=>{let{loggedModelId:t}=e;const{data:i,isLoading:o,isFetching:r,refetch:n,error:l}=(0,s.I)({queryKey:a(null!==t&&void 0!==t?t:""),queryFn:d,cacheTime:0,refetchOnWindowFocus:!1,retry:!1});return{isLoading:o,isFetching:r,data:null===i||void 0===i?void 0:i.model,refetch:n,error:l}},c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];const t=(0,o.E)({queries:e.map((e=>({queryKey:a(e),queryFn:d,cacheTime:0,refetchOnWindowFocus:!1,retry:!1})))});return(0,n.z)(t)}},96034:function(e,t,i){i.d(t,{V:function(){return M}});var s=i(89555),o=i(31014),r=i(32599),n=i(48012),a=i(93215);const d=e=>{let{when:t,message:i}=e;const s=o.useContext(a.jb).navigator.block;return o.useEffect((()=>{if(!t)return;return null===s||void 0===s?void 0:s((()=>window.confirm(i)))}),[i,s,t]),null};var l=i(75703),c=i(11473),h=i(88443),p=i(64912),u=i(50111);const g=e=>(0,u.Y)(f,{name:e});class m extends o.Component{constructor(){super(...arguments),this.state={markdown:this.props.defaultMarkdown,selectedTab:this.props.defaultSelectedTab,error:null},this.converter=(0,c.OT)(),this.handleMdeValueChange=e=>{this.setState({markdown:e})},this.handleTabChange=e=>{this.setState({selectedTab:e})},this.handleSubmitClick=()=>{const{onSubmit:e}=this.props,{markdown:t}=this.state;return this.setState({confirmLoading:!0}),e?Promise.resolve(e(t)).then((()=>{this.setState({confirmLoading:!1,error:null})})).catch((e=>{this.setState({confirmLoading:!1,error:e&&e.getMessageField?e.getMessageField():this.props.intl.formatMessage({id:"SzvvXl",defaultMessage:"Failed to submit"})})})):null},this.handleCancelClick=()=>{this.setState({markdown:this.props.defaultMarkdown,selectedTab:this.props.defaultSelectedTab});const{onCancel:e}=this.props;e&&e()}}contentHasChanged(){return this.state.markdown!==this.props.defaultMarkdown}renderActions(){const{confirmLoading:e}=this.state;return(0,u.Y)("div",{className:"editable-note-actions","data-testid":"editable-note-actions",children:(0,u.FD)("div",{children:[(0,u.Y)(r.B,{componentId:"codegen_mlflow_app_src_common_components_editablenote.tsx_114",type:"primary",className:"editable-note-save-button",onClick:this.handleSubmitClick,disabled:!this.contentHasChanged()||e,loading:e,"data-testid":"editable-note-save-button",children:this.props.saveText}),(0,u.Y)(r.B,{componentId:"codegen_mlflow_app_src_common_components_editablenote.tsx_124",htmlType:"button",className:"editable-note-cancel-button",onClick:this.handleCancelClick,disabled:e,children:(0,u.Y)(h.A,{id:"17k196",defaultMessage:"Cancel"})})]})})}getSanitizedHtmlContent(){const{markdown:e}=this.state;if(e){const t=(0,c.NW)(this.converter.makeHtml(e));return(0,c.Yc)(t)}return null}render(){const{showEditor:e}=this.props,{markdown:t,selectedTab:i,error:s}=this.state,r=this.getSanitizedHtmlContent();return(0,u.Y)("div",{className:"note-view-outer-container","data-testid":"note-view-outer-container",children:e?(0,u.FD)(o.Fragment,{children:[(0,u.Y)("div",{className:"note-view-text-area",children:(0,u.Y)(l.default,{value:t,minEditorHeight:this.props.minEditorHeight,maxEditorHeight:this.props.maxEditorHeight,minPreviewHeight:50,childProps:this.props.childProps,toolbarCommands:this.props.toolbarCommands,onChange:this.handleMdeValueChange,selectedTab:i,onTabChange:this.handleTabChange,generateMarkdownPreview:e=>Promise.resolve(this.getSanitizedHtmlContent(e)),getIcon:g})}),s&&(0,u.Y)(n.FcD,{componentId:"codegen_mlflow_app_src_common_components_editablenote.tsx_178",type:"error",message:this.props.intl.formatMessage({id:"79dD5F",defaultMessage:"There was an error submitting your note."}),description:s,closable:!0}),this.renderActions(),(0,u.Y)(d,{when:this.contentHasChanged(),message:this.props.intl.formatMessage({id:"dwTTNK",defaultMessage:"Are you sure you want to navigate away? Your pending text changes will be lost."})})]}):(0,u.Y)(v,{content:r})})}}function f(e){const{theme:t}=(0,r.u)(),{name:i}=e;return(0,u.Y)(n.paO,{position:"top",title:i,children:(0,u.Y)("span",{css:(0,s.AH)({color:t.colors.textPrimary},""),children:(0,u.Y)(l.SvgIcon,{icon:i})})})}function v(e){const{content:t}=e;return t?(0,u.Y)("div",{className:"note-view-outer-container","data-testid":"note-view-outer-container",children:(0,u.Y)("div",{className:"note-view-text-area",children:(0,u.Y)("div",{className:"note-view-preview note-editor-preview",children:(0,u.Y)("div",{className:"note-editor-preview-content","data-testid":"note-editor-preview-content",dangerouslySetInnerHTML:{__html:e.content}})})})}):(0,u.Y)("div",{children:(0,u.Y)(h.A,{id:"PRe/8y",defaultMessage:"None"})})}m.defaultProps={defaultMarkdown:"",defaultSelectedTab:"write",showEditor:!1,saveText:(0,u.Y)(h.A,{id:"Shv28w",defaultMessage:"Save"}),confirmLoading:!1,toolbarCommands:[["header","bold","italic","strikethrough"],["link","quote","code","image"],["unordered-list","ordered-list","checked-list"]],maxEditorHeight:500,minEditorHeight:200,childProps:{}};const M=(0,p.Ay)(m)}}]);
//# sourceMappingURL=701.bdee1a81.chunk.js.map