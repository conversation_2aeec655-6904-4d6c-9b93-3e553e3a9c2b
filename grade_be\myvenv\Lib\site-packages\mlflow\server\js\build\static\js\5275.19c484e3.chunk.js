"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[5275],{47300:function(t,e,r){r.r(e),r.d(e,{default:function(){return p}});var n=r(89555),i=r(31014),o=r(32599),l=r(44520),u=(r(2340),r(70807)),s=r(36118),a=r(32039),c=r(25790),h=r(6922),d=r(50111);const f=(t,e,r)=>{var n;const i="http://www.w3.org/2000/svg",o=document.createElementNS(i,"g"),l=document.createElementNS(i,"rect"),u=document.createElementNS(i,"text");u.innerHTML=e,u.setAttribute("fill","black"),o.classList.add(t),o.appendChild(l),o.appendChild(u),null===(n=r.parentNode)||void 0===n||n.insertBefore(o,r.nextSibling);const s=u.getBBox();l.setAttribute("x",(s.x-4).toString()),l.setAttribute("y",(s.y-4).toString()),l.setAttribute("width",(s.width+8).toString()),l.setAttribute("height",(s.height+8).toString()),l.setAttribute("fill","white")},g=t=>{const{onHover:e,onUnhover:r,selectedRunUuid:n,data:o,axesRotateThreshold:s,selectedMetrics:h,selectedParams:g,width:p,height:m,closeContextMenu:v}=t,b=(0,i.useRef)(null),y=(0,i.useRef)(),[w,M]=(0,i.useState)("");(0,i.useEffect)((()=>{w?null===e||void 0===e||e(w):null===r||void 0===r||r()}),[w,e,r]);const x=(0,i.useCallback)((()=>!1!==y.current.brushed()?y.current.brushed():y.current.data()),[]),{onHighlightChange:A}=(0,c.ZY)(),S=(0,i.useCallback)((t=>{if(!t)return void y.current.unhighlight();const e=x().filter((e=>{let{uuid:r}=e;return t===r}));e.length?y.current.highlight(e):y.current.unhighlight()}),[x]);(0,i.useEffect)((()=>A(S)),[A,S]),(0,i.useEffect)((()=>{if(!y.current)return;const t=x().filter((t=>[w,n].includes(t.uuid)));t.length?y.current.highlight(t):y.current.unhighlight()}),[w,n,x]);const E=(0,i.useCallback)((t=>{const e=[],r=x();if(0===r.length)return!1;const n=k();if(0===n.length)return!1;const i=N(t,n[0]);if(!i)return!1;const o=i;return n.forEach(((n,i)=>{H(n[o-1],n[o],t,2)&&e.push(r[i])})),[e]}),[x]),C=(0,i.useCallback)((t=>{const e=[];let r=0;const n=b.current;if(!n)return;n.querySelectorAll(".dimension").forEach((function(t){const n=t.getAttribute("transform");if(n){const i=parseFloat(n.split("(")[1].split(")")[0]);e.push(i);const{width:o}=t.getBBox();0===r&&(r=o)}}));const i=[];for(let s=0;s<e.length;s++)i.push(e[s]+r/2);let o=[];const l=E(t);let u="";if(l&&0!==l[0].length)if([o]=l,o.length>1&&(o=[o[1]]),i.some((e=>Math.abs(e-t.offsetX)<10)));else{u=o[0].uuid}M(u)}),[b,E]),k=()=>{const t=y.current.margin();return(y.current.brushed().length?y.current.brushed():y.current.data()).map((e=>y.current.compute_real_centroids(e).map((e=>[e[0]+t.left,e[1]+t.top]))))},N=(t,e)=>{const r=t.offsetX;if(e[0][0]>r)return!1;if(e[e.length-1][0]<r)return!1;for(let n=0;n<e.length;n++)if(e[n][0]>r)return n;return!1},H=(t,e,r,n)=>{const i=r.offsetX,o=r.offsetY,[l,u]=t,[s,a]=e,c=s-l,h=a-u;return Math.abs(h*i-c*o-l*a+s*u)/Math.sqrt(Math.pow(c,2)+Math.pow(h,2))<=n};return(0,i.useEffect)((()=>{if(null!==b){var t,e,r,n,i;const d=g.length+h.length,w=15,x=9,A=p/d*.8,S=p/d*.4,E=h[h.length-1],k=o.map((t=>t[E])),N=Math.min(...k.filter((t=>!isNaN(t)))),H=Math.max(...k.filter((t=>!isNaN(t)))),T=(0,u.ex)().domain([N,H]).interpolator((t=>{const e=Math.max(0,Math.min(1,t));return`rgb(\n            ${Math.max(0,Math.min(255,Math.round(34.61+e*(1172.33-e*(10793.56-e*(33300.12-e*(38394.49-14825.05*e)))))))},\n            ${Math.max(0,Math.min(255,Math.round(23.31+e*(557.33+e*(1225.33-e*(3574.96-e*(1073.77+707.56*e)))))))},\n            ${Math.max(0,Math.min(255,Math.round(27.2+e*(3211.1-e*(15327.97-e*(27814-e*(22569.18-6838.66*e)))))))}\n          )`})),q=b.current;var c;if(q)null===(c=q.querySelector("#wrapper svg"))||void 0===c||c.remove();q&&q.querySelectorAll("canvas").forEach((t=>t.remove()));const B=()=>{const t=Object.keys(o[0]),e=t.map((t=>o.map((e=>e[t])).filter((t=>null!==t)))).map((t=>t.every((t=>!isNaN(t)&&null!==t))?"number":"string"));return Object.fromEntries(t.map(((r,n)=>[t[n],{type:e[n]}])))};if(y.current=(0,l.A)()(b.current).width(p).height(m).data(o).dimensions(B()).alpha(.8).alphaOnBrushed(.1).hideAxis(["uuid"]).lineWidth(1).color((t=>t&&E in t&&"null"!==t[E]?T(t[E]):"#f33")).createAxes().render().reorderable().brushMode("1D-axes"),!q)return;y.current.on("brushend",(()=>{y.current.unhighlight(),v()})),null===(t=q.querySelector("#wrapper svg"))||void 0===t||t.addEventListener("mousemove",(function(t){const{offsetX:e,offsetY:r}=t;C({offsetX:e,offsetY:r})})),null===(e=q.querySelector("#wrapper svg"))||void 0===e||e.addEventListener("mouseout",(()=>{M("")})),q.querySelectorAll(".parcoords .label").forEach((t=>{const e=t.innerHTML;d>s&&t.setAttribute("transform","rotate(-30)"),t.setAttribute("y","-20"),t.setAttribute("x","20");t.getBoundingClientRect().width>A&&(t.innerHTML=(0,a.y4)(e,w),e!==t.innerHTML&&f("axis-label-tooltip",e,t))})),q.querySelectorAll(".parcoords .tick text").forEach((t=>{const e=t.innerHTML;t.getBoundingClientRect().width>S&&(t.innerHTML=(0,a.y4)(e,x),e!==t.innerHTML&&f("tick-label-tooltip",e,t))}));const L=Array.from({length:10},((t,e)=>e/9)),R=y.current.svg.append("defs").append("linearGradient").attr("id","mygrad").attr("x2","0%").attr("y1","100%").attr("y2","0%");L.forEach((t=>{R.append("stop").attr("offset",100*t+"%").style("stop-color",T(N+t*(H-N)))}));const Y=null===(r=b.current)||void 0===r||null===(n=r.querySelector("svg"))||void 0===n?void 0:n.getBoundingClientRect(),_=null===(i=b.current)||void 0===i?void 0:i.querySelector(".dimension:last-of-type");if(!_)return;const X=null===_||void 0===_?void 0:_.getBoundingClientRect(),O=null===_||void 0===_?void 0:_.getAttribute("transform");if(!O)return;const P=parseFloat(O.split("(")[1].split(")")[0]);if(Y){y.current.svg.append("rect").attr("x",P+20).attr("y",0).attr("width",20).attr("height",X.height-40).style("fill","url(#mygrad)")}}}),[o,p,m,g,h,e,s,C,b,v]),(0,d.Y)("div",{ref:b,id:"wrapper",style:{width:t.width,height:t.height},className:"parcoords"})};var p=t=>{const e=(0,i.useRef)(null),{theme:r}=(0,o.u)(),{layoutHeight:l,layoutWidth:u,setContainerDiv:a}=(0,s.xN)(),[c,f]=(0,i.useState)(!0),p=(0,i.useRef)();return(0,i.useEffect)((()=>{a(e.current)}),[a]),(0,i.useEffect)((()=>{f(!0),p.current&&clearTimeout(p.current),p.current=setTimeout((()=>{f(!1)}),300)}),[l,u]),(0,d.Y)("div",{ref:e,css:(0,n.AH)({overflow:"hidden",flex:"1",paddingTop:"20px",fontSize:0,".parcoords":{backgroundColor:r.colors.backgroundPrimary},".parcoords text.label":{fill:r.colors.textPrimary}},""),children:c?(0,d.Y)(h.Us,{}):(0,d.Y)(g,{...t,width:u,height:l})})}}}]);
//# sourceMappingURL=5275.19c484e3.chunk.js.map