"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[7581],{23275:function(e,t,r){r.d(t,{u:function(){return p},g:function(){return g}});var n=r(9133),s=r(31014),o=r(10811),i=r(26809),a=r(69708),l=r(76010);var u=r(36568),d=r(91144),c=r(69869);const v=e=>{let{queryResult:t,runUuid:r}=e;const{registeredModels:n}=(0,o.d4)((e=>{let{entities:t}=e;return{registeredModels:t.modelVersionsByRunUuid[r]}}));if((0,d._O)()){const e=[];var s,i;if(null!==t&&void 0!==t&&t.data&&"modelVersions"in t.data)null===(s=t.data)||void 0===s||null===(i=s.modelVersions)||void 0===i||i.forEach((t=>{e.push({displayedName:t.name,version:t.version,link:t.name&&t.version?c.fM.getModelVersionPageRoute(t.name,t.version):"",status:t.status,source:t.source})}));return e}return n?n.map((e=>{const t=e.name,r=c.fM.getModelVersionPageRoute(t,e.version);return{displayedName:e.name,version:e.version,link:r,status:e.status,source:e.source}})):[]},m=e=>(0,n.keyBy)(e,"key"),p=e=>null===e||void 0===e?void 0:e.map((e=>{var t,r,s,o,i,a,l,u,d,c,v,m,p,g;return{dataset:{digest:null!==(t=null===(r=e.dataset)||void 0===r?void 0:r.digest)&&void 0!==t?t:"",name:null!==(s=null===(o=e.dataset)||void 0===o?void 0:o.name)&&void 0!==s?s:"",profile:null!==(i=null===(a=e.dataset)||void 0===a?void 0:a.profile)&&void 0!==i?i:"",schema:null!==(l=null===(u=e.dataset)||void 0===u?void 0:u.schema)&&void 0!==l?l:"",source:null!==(d=null===(c=e.dataset)||void 0===c?void 0:c.source)&&void 0!==d?d:"",sourceType:null!==(v=null===(m=e.dataset)||void 0===m?void 0:m.sourceType)&&void 0!==v?v:""},tags:null!==(p=null===(g=e.tags)||void 0===g?void 0:g.map((e=>{var t,r;return{key:null!==(t=e.key)&&void 0!==t?t:"",value:null!==(r=e.value)&&void 0!==r?r:""}})).filter((e=>!(0,n.isEmpty)(e.key))))&&void 0!==p?p:[]}})),g=e=>{var t,r,c,g,f,h;let{runUuid:y,experimentId:E}=e;const M=(0,d.wD)(),b=(0,o.wA)();if(M){var O,D,k,w,_,R;const e=(()=>(0,u.t)({runUuid:y}))();(0,s.useEffect)((()=>{(0,d._O)()||b((0,a.hY)({run_id:y}))}),[b,y]);const{latestMetrics:t,tags:r,params:o,datasets:i}=(0,s.useMemo)((()=>{var t,r,s,o,i,a,l,u,d,c,v,g;return{latestMetrics:(0,n.pickBy)(m((g=null!==(t=null===(r=e.data)||void 0===r||null===(s=r.data)||void 0===s?void 0:s.metrics)&&void 0!==t?t:[],g.filter((e=>{let{key:t,value:r,step:n,timestamp:s}=e;return null!==t&&null!==r&&null!==n&&null!==s})).map((e=>{let{key:t,value:r,step:n,timestamp:s}=e;return{key:t,value:r,step:Number(n),timestamp:Number(s)}})))),(e=>e.key.trim().length>0)),tags:(0,n.pickBy)(m(null!==(o=null===(i=e.data)||void 0===i||null===(a=i.data)||void 0===a?void 0:a.tags)&&void 0!==o?o:[]),(e=>e.key.trim().length>0)),params:(0,n.pickBy)(m(null!==(l=null===(u=e.data)||void 0===u||null===(d=u.data)||void 0===d?void 0:d.params)&&void 0!==l?l:[]),(e=>e.key.trim().length>0)),datasets:p(null===(c=e.data)||void 0===c||null===(v=c.inputs)||void 0===v?void 0:v.datasetInputs)}}),[e.data]),l=v({runUuid:y,queryResult:e});return{runInfo:null!==(O=null===(D=e.data)||void 0===D?void 0:D.info)&&void 0!==O?O:void 0,experiment:null!==(k=null===(w=e.data)||void 0===w?void 0:w.experiment)&&void 0!==k?k:void 0,loading:e.loading,error:e.apolloError,apiError:e.apiError,refetchRun:e.refetchRun,runInputs:null===(_=e.data)||void 0===_?void 0:_.inputs,runOutputs:null===(R=e.data)||void 0===R?void 0:R.outputs,registeredModelVersionSummaries:l,datasets:i,latestMetrics:t,tags:r,params:o}}const A=((e,t)=>{const[r,u]=(0,s.useState)(""),[d,c]=(0,s.useState)(""),v=(0,o.wA)(),{runInfo:m,tags:p,latestMetrics:g,experiment:f,params:h,datasets:y}=(0,o.d4)((r=>({runInfo:r.entities.runInfosByUuid[e],tags:(0,n.pickBy)(r.entities.tagsByRunUuid[e],(e=>e.key.trim().length>0)),latestMetrics:(0,n.pickBy)(r.entities.latestMetricsByRunUuid[e],(e=>e.key.trim().length>0)),params:(0,n.pickBy)(r.entities.paramsByRunUuid[e],(e=>e.key.trim().length>0)),experiment:r.entities.experimentsById[t],datasets:r.entities.runDatasetsByUuid[e]}))),E=(0,s.useCallback)((()=>{const t=(0,i.aO)(e);return u(t.meta.id),v(t)}),[v,e]),M=(0,s.useCallback)((()=>{const e=(0,i.yc)(t);return c(e.meta.id),v(e)}),[v,t]),b=(0,s.useCallback)((()=>{v((0,a.hY)({run_id:e}))}),[v,e]);(0,s.useEffect)((()=>{m||E().catch((e=>l.A.logErrorAndNotifyUser(e))),b()}),[m,E,b]),(0,s.useEffect)((()=>{f||M().catch((e=>l.A.logErrorAndNotifyUser(e)))}),[f,M]);const{loading:O,error:D}=(0,o.d4)((e=>{var t,n,s,o;return{loading:!r||Boolean(null===(t=e.apis)||void 0===t||null===(n=t[r])||void 0===n?void 0:n.active),error:null===(s=e.apis)||void 0===s||null===(o=s[r])||void 0===o?void 0:o.error}})),{loading:k,error:w}=(0,o.d4)((e=>{var t,n,s,o;return{loading:!r||Boolean(null===(t=e.apis)||void 0===t||null===(n=t[d])||void 0===n?void 0:n.active),error:null===(s=e.apis)||void 0===s||null===(o=s[d])||void 0===o?void 0:o.error}}));return{loading:O||k,data:{runInfo:m,tags:p,params:h,latestMetrics:g,experiment:f,datasets:y},refetchRun:E,errors:{runFetchError:D,experimentFetchError:w}}})(y,E),x=A.errors.runFetchError||A.errors.experimentFetchError,U=v({runUuid:y});return{runInfo:null===(t=A.data)||void 0===t?void 0:t.runInfo,latestMetrics:null===(r=A.data)||void 0===r?void 0:r.latestMetrics,tags:null===(c=A.data)||void 0===c?void 0:c.tags,experiment:null===(g=A.data)||void 0===g?void 0:g.experiment,params:null===(f=A.data)||void 0===f?void 0:f.params,datasets:null===(h=A.data)||void 0===h?void 0:h.datasets,loading:A.loading,error:x,runFetchError:A.errors.runFetchError,experimentFetchError:A.errors.experimentFetchError,refetchRun:A.refetchRun,registeredModelVersionSummaries:U}}},28684:function(e,t,r){r.d(t,{f:function(){return d}});var n=r(89555),s=r(32599),o=r(48012),i=r(31014),a=r(36506),l=r(37368),u=r(50111);const d=e=>{let{datasetName:t,datasetDigest:r,runId:d}=e;const{theme:c}=(0,s.u)(),[v,m]=(0,i.useState)(!1),{onDatasetClicked:p}=(0,a.s7)(),{handleError:g}=(0,l.tF)();return(0,u.FD)(s.B,{type:"link",icon:v?(0,u.Y)(s.S,{size:"small",css:(0,n.AH)({marginRight:c.spacing.sm},"")}):(0,u.Y)(o.KbA,{}),componentId:"mlflow.logged_model.dataset",onClick:()=>((e,t,r)=>{r&&(m(!0),null===p||void 0===p||p({datasetName:e,datasetDigest:t,runId:r}).catch((e=>{g(e)})).finally((()=>m(!1))))})(t,r,d),children:[t," (#",r,")"]},[t,r].join("."))}},31179:function(e,t,r){r.d(t,{b:function(){return u}});var n=r(31014),s=r(9133),o=r(93215);const i=(e,t)=>`/explore/data/models/${e.replace(/\./g,"/")}/version/${t}`,a=(e,t)=>(0,o.Oz)(`/models/${e}/versions/${t}`),l=e=>{try{var t,r,n;const s=null===(t=e.info)||void 0===t||null===(r=t.tags)||void 0===r||null===(n=r.find((e=>"mlflow.modelVersions"===e.key)))||void 0===n?void 0:n.value;if(s)return JSON.parse(s)}catch(s){return null}return null},u=e=>{let{loggedModels:t}=e;const r=(0,n.useMemo)((()=>(0,s.compact)(t.map(l)).flat()),[t]);return(0,n.useMemo)((()=>{var e;return null!==(e=r.map((e=>{var t;const r=(t=e.name,Boolean(t.match(/^[^. /]+\.[^. /]+\.[^. /]+$/)))?i:a;return{displayedName:e.name,version:e.version,link:r(e.name,e.version),source:null,status:null}})))&&void 0!==e?e:[]}),[r])}},36506:function(e,t,r){r.d(t,{Xs:function(){return p},s7:function(){return g}});var n=r(31014),s=r(85343),o=r(36568),i=r(23275),a=r(9133),l=r(39416),u=r(47664),d=r(88443),c=r(50111);class v extends l.ZR{constructor(){super(...arguments),this.errorLogType=l.ZQ.UnexpectedSystemStateError,this.errorName=l.UW.DatasetRunNotFoundError,this.isUserError=!0,this.displayMessage=(0,c.Y)(d.A,{id:"vwDBPr",defaultMessage:"The run containing the dataset could not be found."})}}const m=(0,n.createContext)({onDatasetClicked:()=>Promise.resolve()}),p=e=>{let{children:t}=e;const[r,l]=(0,n.useState)(!1),[d,p]=(0,n.useState)(),[g]=(0,o.T)(),f=(0,n.useRef)(null),h=(0,n.useCallback)((async e=>new Promise(((t,r)=>{var n;return null===(n=f.current)||void 0===n||n.call(f),g({onError:r,onCompleted(n){var s,o,d,c,m,g,h,y,E,M,b;if(null!==(s=n.mlflowGetRun)&&void 0!==s&&s.apiError){const e=n.mlflowGetRun.apiError.code===u.tG.RESOURCE_DOES_NOT_EXIST?new v:n.mlflowGetRun.apiError;return void r(e)}const O=(0,i.u)(null===(o=n.mlflowGetRun)||void 0===o||null===(d=o.run)||void 0===d||null===(c=d.inputs)||void 0===c?void 0:c.datasetInputs);if(!O||null===(m=n.mlflowGetRun)||void 0===m||null===(g=m.run)||void 0===g||!g.info)return void t();const D=null===O||void 0===O?void 0:O.find((t=>{var r;return(null===(r=t.dataset)||void 0===r?void 0:r.digest)===e.datasetDigest&&t.dataset.name===e.datasetName}));if(!D)return void t();const{info:k,data:w}=n.mlflowGetRun.run,_=(0,a.keyBy)(null!==(h=null===w||void 0===w||null===(y=w.tags)||void 0===y?void 0:y.filter((e=>e.key&&e.value)))&&void 0!==h?h:[],"key");l(!0),p({datasetWithTags:{dataset:D.dataset,tags:D.tags},runData:{datasets:O,runUuid:null!==(E=k.runUuid)&&void 0!==E?E:"",experimentId:null!==(M=k.experimentId)&&void 0!==M?M:"",runName:null!==(b=k.runName)&&void 0!==b?b:"",tags:_}}),t(),f.current=null},variables:{data:{runId:e.runId}}})}))),[g]),y=(0,n.useMemo)((()=>({onDatasetClicked:h})),[h]);return(0,c.FD)(m.Provider,{value:y,children:[t,d&&(0,c.Y)(s.O,{isOpen:r,selectedDatasetWithRun:d,setIsOpen:l,setSelectedDatasetWithRun:p})]})},g=()=>(0,n.useContext)(m)},36568:function(e,t,r){r.d(t,{t:function(){return c},T:function(){return v}});var n=r(56675),s=r(95947),o=r(97779),i=r(31014),a=r(86896),l=r(18815),u=["refetch","reobserve","fetchMore","updateQuery","startPolling","subscribeToMore"];const d=n.J1`
  query GetRun($data: MlflowGetRunInput!) @component(name: "MLflow.ExperimentRunTracking") {
    mlflowGetRun(input: $data) {
      apiError {
        helpUrl
        code
        message
      }
      run {
        info {
          runName
          status
          runUuid
          experimentId
          artifactUri
          endTime
          lifecycleStage
          startTime
          userId
        }
        experiment {
          experimentId
          name
          tags {
            key
            value
          }
          artifactLocation
          lifecycleStage
          lastUpdateTime
        }
        modelVersions {
          status
          version
          name
          source
        }
        data {
          metrics {
            key
            value
            step
            timestamp
          }
          params {
            key
            value
          }
          tags {
            key
            value
          }
        }
        inputs {
          datasetInputs {
            dataset {
              digest
              name
              profile
              schema
              source
              sourceType
            }
            tags {
              key
              value
            }
          }
          modelInputs {
            modelId
          }
        }
        outputs {
          modelOutputs {
            modelId
            step
          }
        }
      }
    }
  }
`,c=e=>{var t,r;let{runUuid:n,disabled:o=!1}=e;const{data:i,loading:a,error:l,refetch:u}=(0,s.I)(d,{variables:{data:{runId:n}},skip:o});return{loading:a,data:null===i||void 0===i||null===(t=i.mlflowGetRun)||void 0===t?void 0:t.run,refetchRun:u,apolloError:l,apiError:null===i||void 0===i||null===(r=i.mlflowGetRun)||void 0===r?void 0:r.apiError}},v=()=>function(e,t){var r=(0,s.k)((0,l.m)(t&&t.client),e),n=(0,i.useRef)(),d=n.current?(0,a.l)(t,n.current):t,c=r.useQuery((0,o.__assign)((0,o.__assign)({},d),{skip:!n.current})),v=c.observable.options.initialFetchPolicy||r.getDefaultFetchPolicy(),m=Object.assign(c,{called:!!n.current}),p=(0,i.useMemo)((function(){for(var e={},t=function(t){var s=m[t];e[t]=function(){return n.current||(n.current=Object.create(null),r.forceUpdate()),s.apply(this,arguments)}},s=0,o=u;s<o.length;s++)t(o[s]);return e}),[]);return Object.assign(m,p),[(0,i.useCallback)((function(e){n.current=e?(0,o.__assign)((0,o.__assign)({},e),{fetchPolicy:e.fetchPolicy||v}):{fetchPolicy:v};var t=r.asyncUpdate().then((function(e){return Object.assign(e,p)}));return t.catch((function(){})),t}),[]),m]}(d)},37368:function(e,t,r){r.d(t,{Au:function(){return a},tF:function(){return l}});var n=r(31014),s=r(39416),o=r(50111);const i=(0,n.createContext)({currentUserActionError:null,handleError:()=>{},handlePromise:()=>{},clearUserActionError:()=>{}}),a=e=>{let{children:t,errorFilter:r}=e;const[a,l]=(0,n.useState)(null),u=(0,n.useCallback)(((e,t)=>{if(null===r||void 0===r||!r(e)){const r=(0,s.a$)(e);l(r),t&&t(r)}}),[l,r]),d=(0,n.useCallback)((e=>{e.catch((e=>{u(e)}))}),[u]),c=(0,n.useCallback)((()=>{l(null)}),[l]);return(0,o.Y)(i.Provider,{value:(0,n.useMemo)((()=>({currentUserActionError:a,handleError:u,handlePromise:d,clearUserActionError:c})),[c,a,u,d]),children:t})},l=()=>{const{currentUserActionError:e,handleError:t,handlePromise:r,clearUserActionError:s}=(0,n.useContext)(i),o=(0,n.useCallback)(((e,r,n)=>{t(r,n)}),[t]);return(0,n.useMemo)((()=>({currentUserActionError:e,handleError:t,handleErrorWithEvent:o,handlePromise:r,clearUserActionError:s})),[s,t,r,e,o])}},43102:function(e,t,r){r.d(t,{J:function(){return f}});var n=r(89555),s=r(32599),o=r(15579),i=r(48012),a=r(76010),l=r(25866),u=r(56412),d=r(1323),c=r(31014),v=r(93215),m=r(50111);var p={name:"1wcfv52",styles:"margin-right:0"},g={name:"1a7v7i3",styles:"margin-right:0;&>div{padding-right:0;}"};const f=e=>{var t,r,f,h;let{loggedModel:y,displayDetails:E,className:M}=e;const[b]=(0,v.ok)(),O=(0,c.useMemo)((()=>{var e,t,r;return null!==(e=null===y||void 0===y||null===(t=y.info)||void 0===t||null===(r=t.tags)||void 0===r?void 0:r.reduce(((e,t)=>t.key?(e[t.key]=t,e):e),{}))&&void 0!==e?e:{}}),[null===y||void 0===y||null===(t=y.info)||void 0===t?void 0:t.tags]),D=null===O||void 0===O||null===(r=O[l.xd])||void 0===r?void 0:r.value,k=null===O||void 0===O||null===(f=O[a.A.gitCommitTag])||void 0===f?void 0:f.value,w=(0,c.useMemo)((()=>{try{return a.A.renderSource(O,b.toString(),void 0,D)}catch(e){return}}),[O,b,D]),_=null===(h=O[a.A.sourceTypeTag])||void 0===h?void 0:h.value,{theme:R}=(0,s.u)();return w?(0,m.FD)("div",{css:(0,n.AH)({display:"flex",alignItems:"center",gap:R.spacing.sm,paddingTop:R.spacing.sm,paddingBottom:R.spacing.sm,flexWrap:E?"wrap":void 0},""),className:M,children:[_&&(0,m.Y)(d.m,{sourceType:_,css:(0,n.AH)({color:R.colors.actionPrimaryBackgroundDefault},"")}),w," ",E&&D&&(0,m.Y)(o.T,{componentId:"mlflow.logged_model.details.source.branch_tooltip",content:D,children:(0,m.Y)(i.vwO,{componentId:"mlflow.logged_model.details.source.branch",css:p,children:(0,m.FD)("div",{css:(0,n.AH)({display:"flex",gap:R.spacing.xs,whiteSpace:"nowrap"},""),children:[(0,m.Y)(i.OKA,{})," ",D]})})}),E&&k&&(0,m.FD)(s.av.Root,{componentId:"mlflow.logged_model.details.source.commit_hash_popover",children:[(0,m.Y)(s.av.Trigger,{asChild:!0,children:(0,m.Y)(i.vwO,{componentId:"mlflow.logged_model.details.source.commit_hash",css:g,children:(0,m.FD)("div",{css:(0,n.AH)({display:"flex",gap:R.spacing.xs,whiteSpace:"nowrap",alignContent:"center"},""),children:[(0,m.Y)(i.fSU,{}),k.slice(0,7)]})})}),(0,m.FD)(s.av.Content,{align:"start",children:[(0,m.Y)(s.av.Arrow,{}),(0,m.FD)("div",{css:(0,n.AH)({display:"flex",gap:R.spacing.xs,alignItems:"center"},""),children:[k,(0,m.Y)(u.i,{showLabel:!1,size:"small",type:"tertiary",copyText:k,icon:(0,m.Y)(i.TdU,{})})]})]})]})]}):(0,m.Y)(s.T.Hint,{children:"\u2014"})}},58710:function(e,t,r){r.d(t,{P:function(){return d}});r(31014);var n=r(15579),s=r(88464),o=r(50111);const i=86400,a={timeZoneName:"short",year:"numeric",month:"numeric",day:"numeric",hour:"2-digit",minute:"2-digit"},l=e=>{let{date:t,intl:r,tooltipFormatOptions:n=a}=e;const s=new Date,o=Math.round((s.getTime()-t.getTime())/1e3),l=navigator.language||"en-US";let u="";try{u=Intl.DateTimeFormat(l,n).format(t)}catch(d){}for(const a of(e=>[{seconds:31536e3,timeAgoMessage:t=>e.formatMessage({id:"NF6ePS",defaultMessage:"{count, plural, =1 {1 year} other {# years}} ago"},{count:t})},{seconds:2592e3,timeAgoMessage:t=>e.formatMessage({id:"N79Rdb",defaultMessage:"{count, plural, =1 {1 month} other {# months}} ago"},{count:t})},{seconds:i,timeAgoMessage:t=>e.formatMessage({id:"XwD3hV",defaultMessage:"{count, plural, =1 {1 day} other {# days}} ago"},{count:t})},{seconds:3600,timeAgoMessage:t=>e.formatMessage({id:"i1Hj20",defaultMessage:"{count, plural, =1 {1 hour} other {# hours}} ago"},{count:t})},{seconds:60,timeAgoMessage:t=>e.formatMessage({id:"ZO8PZt",defaultMessage:"{count, plural, =1 {1 minute} other {# minutes}} ago"},{count:t})},{seconds:1,timeAgoMessage:t=>e.formatMessage({id:"gQB+Vs",defaultMessage:"{count, plural, =1 {1 second} other {# seconds}} ago"},{count:t})}])(r)){const e=Math.floor(o/a.seconds);if(e>=1)return{displayText:a.timeAgoMessage(e),tooltipTitle:u}}return{displayText:r.formatMessage({id:"ZOsiNc",defaultMessage:"just now"}),tooltipTitle:u}},u=e=>{let{date:t,tooltipFormatOptions:r=a}=e;const i=(0,s.A)(),{displayText:u,tooltipTitle:d}=l({date:t,intl:i,tooltipFormatOptions:r});return(0,o.Y)(n.T,{componentId:"web-shared.time-ago",content:d,children:(0,o.Y)("span",{children:u})})},d=e=>{let{value:t}=e;const r=new Date(Number(t));return isNaN(r)?null:(0,o.Y)(u,{date:r})}},77735:function(e,t,r){r.d(t,{E:function(){return g}});var n=r(31014),s=r(61226),o=r(28999),i=r(95904),a=r(45586),l=r(21363);class u extends l.Q{constructor(e,t){super(),this.client=e,this.queries=[],this.result=[],this.observers=[],this.observersMap={},t&&this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.observers.forEach((e=>{e.subscribe((t=>{this.onUpdate(e,t)}))}))}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.observers.forEach((e=>{e.destroy()}))}setQueries(e,t){this.queries=e,i.j.batch((()=>{const e=this.observers,r=this.findMatchingObservers(this.queries);r.forEach((e=>e.observer.setOptions(e.defaultedQueryOptions,t)));const n=r.map((e=>e.observer)),s=Object.fromEntries(n.map((e=>[e.options.queryHash,e]))),i=n.map((e=>e.getCurrentResult())),a=n.some(((t,r)=>t!==e[r]));(e.length!==n.length||a)&&(this.observers=n,this.observersMap=s,this.result=i,this.hasListeners()&&((0,o.iv)(e,n).forEach((e=>{e.destroy()})),(0,o.iv)(n,e).forEach((e=>{e.subscribe((t=>{this.onUpdate(e,t)}))})),this.notify()))}))}getCurrentResult(){return this.result}getQueries(){return this.observers.map((e=>e.getCurrentQuery()))}getObservers(){return this.observers}getOptimisticResult(e){return this.findMatchingObservers(e).map((e=>e.observer.getOptimisticResult(e.defaultedQueryOptions)))}findMatchingObservers(e){const t=this.observers,r=new Map(t.map((e=>[e.options.queryHash,e]))),n=e.map((e=>this.client.defaultQueryOptions(e))),s=n.flatMap((e=>{const t=r.get(e.queryHash);return null!=t?[{defaultedQueryOptions:e,observer:t}]:[]})),o=new Set(s.map((e=>e.defaultedQueryOptions.queryHash))),i=n.filter((e=>!o.has(e.queryHash))),l=new Set(s.map((e=>e.observer))),u=t.filter((e=>!l.has(e))),d=e=>{const t=this.client.defaultQueryOptions(e),r=this.observersMap[t.queryHash];return null!=r?r:new a.$(this.client,t)},c=i.map(((e,t)=>{if(e.keepPreviousData){const r=u[t];if(void 0!==r)return{defaultedQueryOptions:e,observer:r}}return{defaultedQueryOptions:e,observer:d(e)}}));return s.concat(c).sort(((e,t)=>n.indexOf(e.defaultedQueryOptions)-n.indexOf(t.defaultedQueryOptions)))}onUpdate(e,t){const r=this.observers.indexOf(e);-1!==r&&(this.result=(0,o._D)(this.result,r,t),this.notify())}notify(){i.j.batch((()=>{this.listeners.forEach((e=>{let{listener:t}=e;t(this.result)}))}))}}var d=r(27288),c=r(26737),v=r(35067),m=r(52165),p=r(41698);function g(e){let{queries:t,context:r}=e;const o=(0,d.jE)({context:r}),a=(0,c.w)(),l=(0,v.h)(),g=n.useMemo((()=>t.map((e=>{const t=o.defaultQueryOptions(e);return t._optimisticResults=a?"isRestoring":"optimistic",t}))),[t,o,a]);g.forEach((e=>{(0,p.tu)(e),(0,m.LJ)(e,l)})),(0,m.wZ)(l);const[f]=n.useState((()=>new u(o,g))),h=f.getOptimisticResult(g);(0,s.r)(n.useCallback((e=>a?()=>{}:f.subscribe(i.j.batchCalls(e))),[f,a]),(()=>f.getCurrentResult()),(()=>f.getCurrentResult())),n.useEffect((()=>{f.setQueries(g,{listeners:!1})}),[g,f]);const y=h.some(((e,t)=>(0,p.EU)(g[t],e,a)))?h.flatMap(((e,t)=>{const r=g[t],n=f.getObservers()[t];if(r&&n){if((0,p.EU)(r,e,a))return(0,p.iL)(r,n,l);(0,p.nE)(e,a)&&(0,p.iL)(r,n,l)}return[]})):[];if(y.length>0)throw Promise.all(y);const E=f.getQueries(),M=h.find(((e,t)=>{var r,n;return(0,m.$1)({result:e,errorResetBoundary:l,useErrorBoundary:null!=(r=null==(n=g[t])?void 0:n.useErrorBoundary)&&r,query:E[t]})}));if(null!=M&&M.error)throw M.error;return h}},83858:function(e,t,r){r.d(t,{a:function(){return c}});var n=r(89555),s=r(48012),o=r(41028),i=r(32599),a=r(88443),l=r(32378),u=r(50111);const d=e=>{let{status:t}=e;return t===l.Fq.LOGGED_MODEL_READY?(0,u.Y)(s.C1y,{color:"success"}):t===l.Fq.LOGGED_MODEL_UPLOAD_FAILED?(0,u.Y)(s.qhh,{color:"danger"}):t===l.Fq.LOGGED_MODEL_PENDING?(0,u.Y)(o.C,{color:"warning"}):null},c=e=>{var t,r;let{data:o}=e;const{theme:c}=(0,i.u)(),v=null!==(t=null===(r=o.info)||void 0===r?void 0:r.status)&&void 0!==t?t:l.Fq.LOGGED_MODEL_STATUS_UNSPECIFIED;return(0,u.FD)(s.vwO,{componentId:"mlflow.logged_model.status",css:(0,n.AH)({backgroundColor:v===l.Fq.LOGGED_MODEL_READY?c.isDarkMode?c.colors.green800:c.colors.green100:v===l.Fq.LOGGED_MODEL_UPLOAD_FAILED?c.isDarkMode?c.colors.red800:c.colors.red100:v===l.Fq.LOGGED_MODEL_PENDING?c.isDarkMode?c.colors.yellow800:c.colors.yellow100:void 0},""),children:[v&&(0,u.Y)(d,{status:v})," ",(0,u.Y)(i.T.Text,{css:(0,n.AH)({marginLeft:c.spacing.sm},""),children:v===l.Fq.LOGGED_MODEL_READY?(0,u.Y)(i.T.Text,{color:"success",children:(0,u.Y)(a.A,{id:"Rs+SVS",defaultMessage:"Ready"})}):v===l.Fq.LOGGED_MODEL_UPLOAD_FAILED?(0,u.Y)(i.T.Text,{color:"error",children:(0,u.Y)(a.A,{id:"z0e/Kg",defaultMessage:"Upload failed"})}):v===l.Fq.LOGGED_MODEL_PENDING?(0,u.Y)(i.T.Text,{color:"warning",children:(0,u.Y)(a.A,{id:"jo4LfR",defaultMessage:"Pending"})}):v})]})}},84174:function(e,t,r){r.d(t,{z:function(){return s}});var n=r(31014);function s(e){const t=(0,n.useRef)(),r=!(!t.current||e.length!==t.current.length)&&e.every(((e,r)=>{var n;return e===(null===(n=t.current)||void 0===n?void 0:n[r])}));return r||(t.current=e),r&&t.current?t.current:e}}}]);
//# sourceMappingURL=7581.94973fc7.chunk.js.map