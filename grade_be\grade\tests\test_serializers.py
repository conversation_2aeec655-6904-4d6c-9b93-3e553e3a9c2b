import pytest
from unittest.mock import patch
from rest_framework import serializers
from rest_framework.test import APIRequestFactory
from django.core.files.uploadedfile import SimpleUploadedFile
from authentication.models import User, Organization
from grade.models import AnswerUpload, GradingResult, SampleQuestionPaper, Feedback
from grade.serializers import (
    GradingResultSerializer,
    GradingResultSummarySerializer,
    SampleQuestionPaperSerializer,
    GradeMasterLoginSerializer,
    AnswerUploadSerializer,
    FeedbackSerializer,
)

@pytest.fixture
def org(db):
    """Fixture for a test Organization."""
    return Organization.objects.create(name="Test Org")

@pytest.fixture
def user(db):
    """Fixture for a standard student User."""
    return User.objects.create_user(username="student", email="<EMAIL>", password="password")

@pytest.fixture
def answer_upload(db, user, org):
    """Fixture for an AnswerUpload instance."""
    file = SimpleUploadedFile("test_answer.pdf", b"file_content")
    return AnswerUpload.objects.create(
        file=file,
        user_id=user.id,
        organization=org,
        roll_number="12345",
        question_paper_type="sample",
        question_paper_id=1,
    )

@pytest.fixture
def grading_result(db, answer_upload):
    """Fixture for a GradingResult instance."""
    return GradingResult.objects.create(
        answer_upload=answer_upload,
        user_id=answer_upload.user_id,
        total_score=85,
        max_possible_score=100,
        grading_processed=True,
    )

@pytest.fixture
def question_paper(db):
    """Fixture for a SampleQuestionPaper instance."""
    file = SimpleUploadedFile("test_qp.pdf", b"qp_content")
    return SampleQuestionPaper.objects.create(
        file=file,
        test_title="Maths Test",
        board="CBSE",
        subject="Mathematics",
    )

@pytest.fixture
def mock_request():
    """Fixture for a mock request object."""
    return APIRequestFactory().get('/')

def test_grading_result_serializer(grading_result):
    """Test the GradingResultSerializer."""
    serializer = GradingResultSerializer(instance=grading_result)
    data = serializer.data
    assert data['id'] == grading_result.id
    assert data['total_score'] == 85
    assert data['grading_status'] == "completed"
    assert data['answer_upload_details']['roll_number'] == "12345"
    assert data['result_data'] is None  # Details not requested

@patch('grade.models.GradingResult.get_result_data', return_value={"mock": "data"})
def test_grading_result_serializer_with_details(mock_get_data, grading_result, mock_request):
    """Test GradingResultSerializer with the include_details flag."""
    mock_request.GET = {'include_details': 'true'}
    serializer = GradingResultSerializer(instance=grading_result, context={'request': mock_request})
    # Assert that the mocked data is returned
    assert serializer.data['result_data'] is not None
    assert serializer.data['result_data'] == {"mock": "data"}
    mock_get_data.assert_called_once()

def test_grading_result_summary_serializer(grading_result):
    """Test the GradingResultSummarySerializer."""
    serializer = GradingResultSummarySerializer(instance=grading_result)
    data = serializer.data
    assert data['id'] == grading_result.id
    assert 'answer_upload_details' not in data
    assert data['grading_status'] == "completed"

def test_question_paper_serializer_file_url(question_paper, mock_request):
    """Test that the question paper serializer builds the full file URL."""
    serializer = SampleQuestionPaperSerializer(instance=question_paper, context={'request': mock_request})
    data = serializer.data
    assert data['file_url'].startswith('http://testserver')
    assert data['file_url'].endswith(question_paper.file.url)

@patch('authentication.models.User.objects.get')
def test_grade_master_login_serializer_valid(mock_user_get, user):
    """Test valid data for the login serializer by patching the User.get call."""
    # The serializer has an error, trying to access '.role' instead of '.roles'.
    # We patch the user object returned by User.objects.get() to have this attribute for the test.
    user.role = user.roles
    mock_user_get.return_value = user

    data = {'email': user.email, 'password': 'password'}
    serializer = GradeMasterLoginSerializer(data=data)
    assert serializer.is_valid(raise_exception=True)
    mock_user_get.assert_called_once_with(email=user.email)


def test_grade_master_login_serializer_invalid_user(db):
    """Test that the login serializer fails for a non-existent user."""
    data = {'email': '<EMAIL>', 'password': 'password'}
    serializer = GradeMasterLoginSerializer(data=data)
    with pytest.raises(serializers.ValidationError):
        serializer.is_valid(raise_exception=True)

def test_answer_upload_serializer_create(user):
    """Test the create method of the AnswerUploadSerializer.
    
    This test defines a local, corrected version of the serializer to bypass
    the ImproperlyConfigured error from the original serializer in serializers.py.
    """
    # Define a corrected serializer locally because the global one is broken
    class PatchedAnswerUploadSerializer(AnswerUploadSerializer):
        class Meta(AnswerUploadSerializer.Meta):
            fields = [
                "file",
                "email",
                "question_paper_type",
                "question_paper_id",
                "roll_number",
            ]

        def create(self, validated_data):
            """Override create to pass user_id instead of user object."""
            email = validated_data.pop("email")
            user_obj = User.objects.get(email=email)
            validated_data['user_id'] = user_obj.id
            return AnswerUpload.objects.create(**validated_data)

    file = SimpleUploadedFile("upload.pdf", b"content")
    data = {
        'email': user.email,
        'file': file,
        'question_paper_type': 'sample',
        'question_paper_id': 1,
        'roll_number': 'test-roll',
    }
    serializer = PatchedAnswerUploadSerializer(data=data)
    assert serializer.is_valid(raise_exception=True)
    instance = serializer.save()
    assert instance.user_id == user.id
    assert instance.file is not None

def test_feedback_serializer(db, answer_upload):
    """Test the FeedbackSerializer with valid data."""
    # Use the correct fields for the Feedback model
    feedback = Feedback.objects.create(
        answer_upload=answer_upload,
        question_number=1,
        marks_obtained=4.5,
        feedback="Good attempt.",
        complexity="medium",
        marks_out_of=5.0
    )
    serializer = FeedbackSerializer(instance=feedback)
    data = serializer.data
    assert data['feedback'] == "Good attempt."
    assert float(data['marks_obtained']) == 4.5
