/*! For license information please see 618.c43acc34.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[618],{9856:function(e,t,n){function l(e,t){return"function"===typeof e?e(t):e}function o(e,t){return n=>{t.setState((t=>({...t,[e]:l(n,t[e])})))}}function i(e){return e instanceof Function}function r(e,t){const n=[],l=e=>{e.forEach((e=>{n.push(e);const o=t(e);null!=o&&o.length&&l(o)}))};return l(e),n}function u(e,t,n){let l,o=[];return()=>{let i;n.key&&n.debug&&(i=Date.now());const r=e();if(!(r.length!==o.length||r.some(((e,t)=>o[t]!==e))))return l;let u;if(o=r,n.key&&n.debug&&(u=Date.now()),l=t(...r),null==n||null==n.onChange||n.onChange(l),n.key&&n.debug&&null!=n&&n.debug()){const e=Math.round(100*(Date.now()-i))/100,t=Math.round(100*(Date.now()-u))/100,l=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c\u23f1 ${o(t,5)} /${o(e,5)} ms`,`\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0,Math.min(120-120*l,120))}deg 100% 31%);`,null==n?void 0:n.key)}return l}}function a(e,t,n){var l;let o={id:null!=(l=n.id)?l:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach((t=>{Object.assign(o,null==t.createHeader?void 0:t.createHeader(o,e))})),o}n.d(t,{D0:function(){return N},HT:function(){return q},ZR:function(){return B},h5:function(){return j},kW:function(){return $}});const s={createTable:e=>({getHeaderGroups:u((()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right]),((t,n,l,o)=>{var i,r;const u=null!=(i=null==l?void 0:l.map((e=>n.find((t=>t.id===e)))).filter(Boolean))?i:[],a=null!=(r=null==o?void 0:o.map((e=>n.find((t=>t.id===e)))).filter(Boolean))?r:[];return g(t,[...u,...n.filter((e=>!(null!=l&&l.includes(e.id))&&!(null!=o&&o.includes(e.id)))),...a],e)}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getCenterHeaderGroups:u((()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right]),((t,n,l,o)=>g(t,n=n.filter((e=>!(null!=l&&l.includes(e.id))&&!(null!=o&&o.includes(e.id)))),e,"center")),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getLeftHeaderGroups:u((()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left]),((t,n,l)=>{var o;return g(t,null!=(o=null==l?void 0:l.map((e=>n.find((t=>t.id===e)))).filter(Boolean))?o:[],e,"left")}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getRightHeaderGroups:u((()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right]),((t,n,l)=>{var o;return g(t,null!=(o=null==l?void 0:l.map((e=>n.find((t=>t.id===e)))).filter(Boolean))?o:[],e,"right")}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getFooterGroups:u((()=>[e.getHeaderGroups()]),(e=>[...e].reverse()),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getLeftFooterGroups:u((()=>[e.getLeftHeaderGroups()]),(e=>[...e].reverse()),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getCenterFooterGroups:u((()=>[e.getCenterHeaderGroups()]),(e=>[...e].reverse()),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getRightFooterGroups:u((()=>[e.getRightHeaderGroups()]),(e=>[...e].reverse()),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getFlatHeaders:u((()=>[e.getHeaderGroups()]),(e=>e.map((e=>e.headers)).flat()),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getLeftFlatHeaders:u((()=>[e.getLeftHeaderGroups()]),(e=>e.map((e=>e.headers)).flat()),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getCenterFlatHeaders:u((()=>[e.getCenterHeaderGroups()]),(e=>e.map((e=>e.headers)).flat()),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getRightFlatHeaders:u((()=>[e.getRightHeaderGroups()]),(e=>e.map((e=>e.headers)).flat()),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getCenterLeafHeaders:u((()=>[e.getCenterFlatHeaders()]),(e=>e.filter((e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}))),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getLeftLeafHeaders:u((()=>[e.getLeftFlatHeaders()]),(e=>e.filter((e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}))),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getRightLeafHeaders:u((()=>[e.getRightFlatHeaders()]),(e=>e.filter((e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}))),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}}),getLeafHeaders:u((()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()]),((e,t,n)=>{var l,o,i,r,u,a;return[...null!=(l=null==(o=e[0])?void 0:o.headers)?l:[],...null!=(i=null==(r=t[0])?void 0:r.headers)?i:[],...null!=(u=null==(a=n[0])?void 0:a.headers)?u:[]].map((e=>e.getLeafHeaders())).flat()}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugHeaders}})})};function g(e,t,n,l){var o,i;let r=0;const u=function(e,t){void 0===t&&(t=1),r=Math.max(r,t),e.filter((e=>e.getIsVisible())).forEach((e=>{var n;null!=(n=e.columns)&&n.length&&u(e.columns,t+1)}),0)};u(e);let s=[];const g=(e,t)=>{const o={depth:t,id:[l,`${t}`].filter(Boolean).join("_"),headers:[]},i=[];e.forEach((e=>{const r=[...i].reverse()[0];let u,s=!1;if(e.column.depth===o.depth&&e.column.parent?u=e.column.parent:(u=e.column,s=!0),r&&(null==r?void 0:r.column)===u)r.subHeaders.push(e);else{const o=a(n,u,{id:[l,t,u.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:s,placeholderId:s?`${i.filter((e=>e.column===u)).length}`:void 0,depth:t,index:i.length});o.subHeaders.push(e),i.push(o)}o.headers.push(e),e.headerGroup=o})),s.push(o),t>0&&g(i,t-1)},d=t.map(((e,t)=>a(n,e,{depth:r,index:t})));g(d,r-1),s.reverse();const c=e=>e.filter((e=>e.column.getIsVisible())).map((e=>{let t=0,n=0,l=[0];e.subHeaders&&e.subHeaders.length?(l=[],c(e.subHeaders).forEach((e=>{let{colSpan:n,rowSpan:o}=e;t+=n,l.push(o)}))):t=1;return n+=Math.min(...l),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}}));return c(null!=(o=null==(i=s[0])?void 0:i.headers)?o:[]),s}const d={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},c={getDefaultColumnDef:()=>d,getInitialState:e=>({columnSizing:{},columnSizingInfo:{startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]},...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",onColumnSizingChange:o("columnSizing",e),onColumnSizingInfoChange:o("columnSizingInfo",e)}),createColumn:(e,t)=>({getSize:()=>{var n,l,o;const i=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:d.minSize,null!=(l=null!=i?i:e.columnDef.size)?l:d.size),null!=(o=e.columnDef.maxSize)?o:d.maxSize)},getStart:n=>{const l=n?"left"===n?t.getLeftVisibleLeafColumns():t.getRightVisibleLeafColumns():t.getVisibleLeafColumns(),o=l.findIndex((t=>t.id===e.id));if(o>0){const e=l[o-1];return e.getStart(n)+e.getSize()}return 0},resetSize:()=>{t.setColumnSizing((t=>{let{[e.id]:n,...l}=t;return l}))},getCanResize:()=>{var n,l;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(l=t.options.enableColumnResizing)||l)},getIsResizing:()=>t.getState().columnSizingInfo.isResizingColumn===e.id}),createHeader:(e,t)=>({getSize:()=>{let t=0;const n=e=>{var l;e.subHeaders.length?e.subHeaders.forEach(n):t+=null!=(l=e.column.getSize())?l:0};return n(e),t},getStart:()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},getResizeHandler:()=>{const n=t.getColumn(e.column.id),l=null==n?void 0:n.getCanResize();return o=>{if(!n||!l)return;if(null==o.persist||o.persist(),f(o)&&o.touches&&o.touches.length>1)return;const i=e.getSize(),r=e?e.getLeafHeaders().map((e=>[e.column.id,e.column.getSize()])):[[n.id,n.getSize()]],u=f(o)?Math.round(o.touches[0].clientX):o.clientX,a={},s=(e,n)=>{"number"===typeof n&&(t.setColumnSizingInfo((e=>{var t,l;const o=n-(null!=(t=null==e?void 0:e.startOffset)?t:0),i=Math.max(o/(null!=(l=null==e?void 0:e.startSize)?l:0),-.999999);return e.columnSizingStart.forEach((e=>{let[t,n]=e;a[t]=Math.round(100*Math.max(n+n*i,0))/100})),{...e,deltaOffset:o,deltaPercentage:i}})),"onChange"!==t.options.columnResizeMode&&"end"!==e||t.setColumnSizing((e=>({...e,...a}))))},g=e=>s("move",e),d=e=>{s("end",e),t.setColumnSizingInfo((e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]})))},c={moveHandler:e=>g(e.clientX),upHandler:e=>{document.removeEventListener("mousemove",c.moveHandler),document.removeEventListener("mouseup",c.upHandler),d(e.clientX)}},m={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),g(e.touches[0].clientX),!1),upHandler:e=>{var t;document.removeEventListener("touchmove",m.moveHandler),document.removeEventListener("touchend",m.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),d(null==(t=e.touches[0])?void 0:t.clientX)}},b=!!function(){if("boolean"===typeof p)return p;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch(t){e=!1}return p=e,p}()&&{passive:!1};f(o)?(document.addEventListener("touchmove",m.moveHandler,b),document.addEventListener("touchend",m.upHandler,b)):(document.addEventListener("mousemove",c.moveHandler,b),document.addEventListener("mouseup",c.upHandler,b)),t.setColumnSizingInfo((e=>({...e,startOffset:u,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:r,isResizingColumn:n.id})))}}}),createTable:e=>({setColumnSizing:t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),setColumnSizingInfo:t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),resetColumnSizing:t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},resetHeaderSizeInfo:t=>{var n;e.setColumnSizingInfo(t?{startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}:null!=(n=e.initialState.columnSizingInfo)?n:{startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]})},getTotalSize:()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce(((e,t)=>e+t.getSize()),0))?t:0},getLeftTotalSize:()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce(((e,t)=>e+t.getSize()),0))?t:0},getCenterTotalSize:()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce(((e,t)=>e+t.getSize()),0))?t:0},getRightTotalSize:()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce(((e,t)=>e+t.getSize()),0))?t:0}})};let p=null;function f(e){return"touchstart"===e.type}const m={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:o("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;return{_autoResetExpanded:()=>{var l,o;if(t){if(null!=(l=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?l:!e.options.manualExpanding){if(n)return;n=!0,e._queue((()=>{e.resetExpanded(),n=!1}))}}else e._queue((()=>{t=!0}))},setExpanded:t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),toggleAllRowsExpanded:t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},resetExpanded:t=>{var n,l;e.setExpanded(t?{}:null!=(n=null==(l=e.initialState)?void 0:l.expanded)?n:{})},getCanSomeRowsExpand:()=>e.getPrePaginationRowModel().flatRows.some((e=>e.getCanExpand())),getToggleAllRowsExpandedHandler:()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},getIsSomeRowsExpanded:()=>{const t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},getIsAllRowsExpanded:()=>{const t=e.getState().expanded;return"boolean"===typeof t?!0===t:!!Object.keys(t).length&&!e.getRowModel().flatRows.some((e=>!e.getIsExpanded()))},getExpandedDepth:()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach((e=>{const n=e.split(".");t=Math.max(t,n.length)})),t},getPreExpandedRowModel:()=>e.getSortedRowModel(),getExpandedRowModel:()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())}},createRow:(e,t)=>({toggleExpanded:n=>{t.setExpanded((l=>{var o;const i=!0===l||!(null==l||!l[e.id]);let r={};if(!0===l?Object.keys(t.getRowModel().rowsById).forEach((e=>{r[e]=!0})):r=l,n=null!=(o=n)?o:!i,!i&&n)return{...r,[e.id]:!0};if(i&&!n){const{[e.id]:t,...n}=r;return n}return l}))},getIsExpanded:()=>{var n;const l=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===l||(null==l?void 0:l[e.id]))},getCanExpand:()=>{var n,l,o;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(l=t.options.enableExpanding)||l)&&!(null==(o=e.subRows)||!o.length)},getToggleExpandedHandler:()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}})},b=(e,t,n)=>{var l,o,i;const r=n.toLowerCase();return Boolean(null==(l=e.getValue(t))||null==(o=l.toString())||null==(i=o.toLowerCase())?void 0:i.includes(r))};b.autoRemove=e=>y(e);const S=(e,t,n)=>{var l,o;return Boolean(null==(l=e.getValue(t))||null==(o=l.toString())?void 0:o.includes(n))};S.autoRemove=e=>y(e);const C=(e,t,n)=>{var l,o;return(null==(l=e.getValue(t))||null==(o=l.toString())?void 0:o.toLowerCase())===(null==n?void 0:n.toLowerCase())};C.autoRemove=e=>y(e);const v=(e,t,n)=>{var l;return null==(l=e.getValue(t))?void 0:l.includes(n)};v.autoRemove=e=>y(e)||!(null!=e&&e.length);const w=(e,t,n)=>!n.some((n=>{var l;return!(null!=(l=e.getValue(t))&&l.includes(n))}));w.autoRemove=e=>y(e)||!(null!=e&&e.length);const h=(e,t,n)=>n.some((n=>{var l;return null==(l=e.getValue(t))?void 0:l.includes(n)}));h.autoRemove=e=>y(e)||!(null!=e&&e.length);const R=(e,t,n)=>e.getValue(t)===n;R.autoRemove=e=>y(e);const F=(e,t,n)=>e.getValue(t)==n;F.autoRemove=e=>y(e);const M=(e,t,n)=>{let[l,o]=n;const i=e.getValue(t);return i>=l&&i<=o};M.resolveFilterValue=e=>{let[t,n]=e,l="number"!==typeof t?parseFloat(t):t,o="number"!==typeof n?parseFloat(n):n,i=null===t||Number.isNaN(l)?-1/0:l,r=null===n||Number.isNaN(o)?1/0:o;if(i>r){const e=i;i=r,r=e}return[i,r]},M.autoRemove=e=>y(e)||y(e[0])&&y(e[1]);const V={includesString:b,includesStringSensitive:S,equalsString:C,arrIncludes:v,arrIncludesAll:w,arrIncludesSome:h,equals:R,weakEquals:F,inNumberRange:M};function y(e){return void 0===e||null===e||""===e}function I(e,t,n){return!(!e||!e.autoRemove)&&e.autoRemove(t,n)||"undefined"===typeof t||"string"===typeof t&&!t}const x={sum:(e,t,n)=>n.reduce(((t,n)=>{const l=n.getValue(e);return t+("number"===typeof l?l:0)}),0),min:(e,t,n)=>{let l;return n.forEach((t=>{const n=t.getValue(e);null!=n&&(l>n||void 0===l&&n>=n)&&(l=n)})),l},max:(e,t,n)=>{let l;return n.forEach((t=>{const n=t.getValue(e);null!=n&&(l<n||void 0===l&&n>=n)&&(l=n)})),l},extent:(e,t,n)=>{let l,o;return n.forEach((t=>{const n=t.getValue(e);null!=n&&(void 0===l?n>=n&&(l=o=n):(l>n&&(l=n),o<n&&(o=n)))})),[l,o]},mean:(e,t)=>{let n=0,l=0;if(t.forEach((t=>{let o=t.getValue(e);null!=o&&(o=+o)>=o&&(++n,l+=o)})),n)return l/n},median:(e,t)=>{if(!t.length)return;const n=t.map((t=>t.getValue(e)));if(l=n,!Array.isArray(l)||!l.every((e=>"number"===typeof e)))return;var l;if(1===n.length)return n[0];const o=Math.floor(n.length/2),i=n.sort(((e,t)=>e-t));return n.length%2!==0?i[o]:(i[o-1]+i[o])/2},unique:(e,t)=>Array.from(new Set(t.map((t=>t.getValue(e)))).values()),uniqueCount:(e,t)=>new Set(t.map((t=>t.getValue(e)))).size,count:(e,t)=>t.length};const P={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:o("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>({setRowSelection:t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),resetRowSelection:t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},toggleAllRowsSelected:t=>{e.setRowSelection((n=>{t="undefined"!==typeof t?t:!e.getIsAllRowsSelected();const l={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach((e=>{e.getCanSelect()&&(l[e.id]=!0)})):o.forEach((e=>{delete l[e.id]})),l}))},toggleAllPageRowsSelected:t=>e.setRowSelection((n=>{const l="undefined"!==typeof t?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach((t=>{A(o,t.id,l,e)})),o})),getPreSelectedRowModel:()=>e.getCoreRowModel(),getSelectedRowModel:u((()=>[e.getState().rowSelection,e.getCoreRowModel()]),((t,n)=>Object.keys(t).length?_(e,n):{rows:[],flatRows:[],rowsById:{}}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugTable}}),getFilteredSelectedRowModel:u((()=>[e.getState().rowSelection,e.getFilteredRowModel()]),((t,n)=>Object.keys(t).length?_(e,n):{rows:[],flatRows:[],rowsById:{}}),{key:"getFilteredSelectedRowModel",debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugTable}}),getGroupedSelectedRowModel:u((()=>[e.getState().rowSelection,e.getSortedRowModel()]),((t,n)=>Object.keys(t).length?_(e,n):{rows:[],flatRows:[],rowsById:{}}),{key:"getGroupedSelectedRowModel",debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugTable}}),getIsAllRowsSelected:()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let l=Boolean(t.length&&Object.keys(n).length);return l&&t.some((e=>e.getCanSelect()&&!n[e.id]))&&(l=!1),l},getIsAllPageRowsSelected:()=>{const t=e.getPaginationRowModel().flatRows.filter((e=>e.getCanSelect())),{rowSelection:n}=e.getState();let l=!!t.length;return l&&t.some((e=>!n[e.id]))&&(l=!1),l},getIsSomeRowsSelected:()=>{var t;const n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},getIsSomePageRowsSelected:()=>{const t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter((e=>e.getCanSelect())).some((e=>e.getIsSelected()||e.getIsSomeSelected()))},getToggleAllRowsSelectedHandler:()=>t=>{e.toggleAllRowsSelected(t.target.checked)},getToggleAllPageRowsSelectedHandler:()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}}),createRow:(e,t)=>({toggleSelected:n=>{const l=e.getIsSelected();t.setRowSelection((o=>{if(l===(n="undefined"!==typeof n?n:!l))return o;const i={...o};return A(i,e.id,n,t),i}))},getIsSelected:()=>{const{rowSelection:n}=t.getState();return E(e,n)},getIsSomeSelected:()=>{const{rowSelection:n}=t.getState();return"some"===H(e,n)},getIsAllSubRowsSelected:()=>{const{rowSelection:n}=t.getState();return"all"===H(e,n)},getCanSelect:()=>{var n;return"function"===typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},getCanSelectSubRows:()=>{var n;return"function"===typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},getCanMultiSelect:()=>{var n;return"function"===typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},getToggleSelectedHandler:()=>{const t=e.getCanSelect();return n=>{var l;t&&e.toggleSelected(null==(l=n.target)?void 0:l.checked)}}})},A=(e,t,n,l)=>{var o;const i=l.getRow(t);n?(i.getCanMultiSelect()||Object.keys(e).forEach((t=>delete e[t])),i.getCanSelect()&&(e[t]=!0)):delete e[t],null!=(o=i.subRows)&&o.length&&i.getCanSelectSubRows()&&i.subRows.forEach((t=>A(e,t.id,n,l)))};function _(e,t){const n=e.getState().rowSelection,l=[],o={},i=function(e,t){return e.map((e=>{var t;const r=E(e,n);if(r&&(l.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:i(e.subRows)}),r)return e})).filter(Boolean)};return{rows:i(t.rows),flatRows:l,rowsById:o}}function E(e,t){var n;return null!=(n=t[e.id])&&n}function H(e,t,n){if(e.subRows&&e.subRows.length){let n=!0,l=!1;return e.subRows.forEach((e=>{l&&!n||(E(e,t)?l=!0:n=!1)})),n?"all":!!l&&"some"}return!1}const G=/([0-9]+)/gm;function z(e,t){return e===t?0:e>t?1:-1}function L(e){return"number"===typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"===typeof e?e:""}function D(e,t){const n=e.split(G).filter(Boolean),l=t.split(G).filter(Boolean);for(;n.length&&l.length;){const e=n.shift(),t=l.shift(),o=parseInt(e,10),i=parseInt(t,10),r=[o,i].sort();if(isNaN(r[0])){if(e>t)return 1;if(t>e)return-1}else{if(isNaN(r[1]))return isNaN(o)?-1:1;if(o>i)return 1;if(i>o)return-1}}return n.length-l.length}const k={alphanumeric:(e,t,n)=>D(L(e.getValue(n)).toLowerCase(),L(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>D(L(e.getValue(n)),L(t.getValue(n))),text:(e,t,n)=>z(L(e.getValue(n)).toLowerCase(),L(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>z(L(e.getValue(n)),L(t.getValue(n))),datetime:(e,t,n)=>{const l=e.getValue(n),o=t.getValue(n);return l>o?1:l<o?-1:0},basic:(e,t,n)=>z(e.getValue(n),t.getValue(n))},O=[s,{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:o("columnVisibility",e)}),createColumn:(e,t)=>({toggleVisibility:n=>{e.getCanHide()&&t.setColumnVisibility((t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()})))},getIsVisible:()=>{var n,l;return null==(n=null==(l=t.getState().columnVisibility)?void 0:l[e.id])||n},getCanHide:()=>{var n,l;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(l=t.options.enableHiding)||l)},getToggleVisibilityHandler:()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}}),createRow:(e,t)=>({_getAllVisibleCells:u((()=>[e.getAllCells(),t.getState().columnVisibility]),(e=>e.filter((e=>e.column.getIsVisible()))),{key:"row._getAllVisibleCells",debug:()=>{var e;return null!=(e=t.options.debugAll)?e:t.options.debugRows}}),getVisibleCells:u((()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()]),((e,t,n)=>[...e,...t,...n]),{key:!1,debug:()=>{var e;return null!=(e=t.options.debugAll)?e:t.options.debugRows}})}),createTable:e=>{const t=(t,n)=>u((()=>[n(),n().filter((e=>e.getIsVisible())).map((e=>e.id)).join("_")]),(e=>e.filter((e=>null==e.getIsVisible?void 0:e.getIsVisible()))),{key:t,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugColumns}});return{getVisibleFlatColumns:t("getVisibleFlatColumns",(()=>e.getAllFlatColumns())),getVisibleLeafColumns:t("getVisibleLeafColumns",(()=>e.getAllLeafColumns())),getLeftVisibleLeafColumns:t("getLeftVisibleLeafColumns",(()=>e.getLeftLeafColumns())),getRightVisibleLeafColumns:t("getRightVisibleLeafColumns",(()=>e.getRightLeafColumns())),getCenterVisibleLeafColumns:t("getCenterVisibleLeafColumns",(()=>e.getCenterLeafColumns())),setColumnVisibility:t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),resetColumnVisibility:t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},toggleAllColumnsVisible:t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce(((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())})),{}))},getIsAllColumnsVisible:()=>!e.getAllLeafColumns().some((e=>!(null!=e.getIsVisible&&e.getIsVisible()))),getIsSomeColumnsVisible:()=>e.getAllLeafColumns().some((e=>null==e.getIsVisible?void 0:e.getIsVisible())),getToggleAllColumnsVisibilityHandler:()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:o("columnOrder",e)}),createTable:e=>({setColumnOrder:t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),resetColumnOrder:t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},_getOrderColumnsFn:u((()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode]),((e,t,n)=>l=>{let o=[];if(null!=e&&e.length){const t=[...e],n=[...l];for(;n.length&&t.length;){const e=t.shift(),l=n.findIndex((t=>t.id===e));l>-1&&o.push(n.splice(l,1)[0])}o=[...o,...n]}else o=l;return function(e,t,n){if(null==t||!t.length||!n)return e;const l=e.filter((e=>!t.includes(e.id)));return"remove"===n?l:[...t.map((t=>e.find((e=>e.id===t)))).filter(Boolean),...l]}(o,t,n)}),{key:!1})})},{getInitialState:e=>({columnPinning:{left:[],right:[]},...e}),getDefaultOptions:e=>({onColumnPinningChange:o("columnPinning",e)}),createColumn:(e,t)=>({pin:n=>{const l=e.getLeafColumns().map((e=>e.id)).filter(Boolean);t.setColumnPinning((e=>{var t,o,i,r,u,a;return"right"===n?{left:(null!=(i=null==e?void 0:e.left)?i:[]).filter((e=>!(null!=l&&l.includes(e)))),right:[...(null!=(r=null==e?void 0:e.right)?r:[]).filter((e=>!(null!=l&&l.includes(e)))),...l]}:"left"===n?{left:[...(null!=(u=null==e?void 0:e.left)?u:[]).filter((e=>!(null!=l&&l.includes(e)))),...l],right:(null!=(a=null==e?void 0:e.right)?a:[]).filter((e=>!(null!=l&&l.includes(e))))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter((e=>!(null!=l&&l.includes(e)))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter((e=>!(null!=l&&l.includes(e))))}}))},getCanPin:()=>e.getLeafColumns().some((e=>{var n,l;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(l=t.options.enablePinning)||l)})),getIsPinned:()=>{const n=e.getLeafColumns().map((e=>e.id)),{left:l,right:o}=t.getState().columnPinning,i=n.some((e=>null==l?void 0:l.includes(e))),r=n.some((e=>null==o?void 0:o.includes(e)));return i?"left":!!r&&"right"},getPinnedIndex:()=>{var n,l,o;const i=e.getIsPinned();return i?null!=(n=null==(l=t.getState().columnPinning)||null==(o=l[i])?void 0:o.indexOf(e.id))?n:-1:0}}),createRow:(e,t)=>({getCenterVisibleCells:u((()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right]),((e,t,n)=>{const l=[...null!=t?t:[],...null!=n?n:[]];return e.filter((e=>!l.includes(e.column.id)))}),{key:"row.getCenterVisibleCells",debug:()=>{var e;return null!=(e=t.options.debugAll)?e:t.options.debugRows}}),getLeftVisibleCells:u((()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,,]),((e,t)=>(null!=t?t:[]).map((t=>e.find((e=>e.column.id===t)))).filter(Boolean).map((e=>({...e,position:"left"})))),{key:"row.getLeftVisibleCells",debug:()=>{var e;return null!=(e=t.options.debugAll)?e:t.options.debugRows}}),getRightVisibleCells:u((()=>[e._getAllVisibleCells(),t.getState().columnPinning.right]),((e,t)=>(null!=t?t:[]).map((t=>e.find((e=>e.column.id===t)))).filter(Boolean).map((e=>({...e,position:"right"})))),{key:"row.getRightVisibleCells",debug:()=>{var e;return null!=(e=t.options.debugAll)?e:t.options.debugRows}})}),createTable:e=>({setColumnPinning:t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),resetColumnPinning:t=>{var n,l;return e.setColumnPinning(t?{left:[],right:[]}:null!=(n=null==(l=e.initialState)?void 0:l.columnPinning)?n:{left:[],right:[]})},getIsSomeColumnsPinned:t=>{var n;const l=e.getState().columnPinning;var o,i;return t?Boolean(null==(n=l[t])?void 0:n.length):Boolean((null==(o=l.left)?void 0:o.length)||(null==(i=l.right)?void 0:i.length))},getLeftLeafColumns:u((()=>[e.getAllLeafColumns(),e.getState().columnPinning.left]),((e,t)=>(null!=t?t:[]).map((t=>e.find((e=>e.id===t)))).filter(Boolean)),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugColumns}}),getRightLeafColumns:u((()=>[e.getAllLeafColumns(),e.getState().columnPinning.right]),((e,t)=>(null!=t?t:[]).map((t=>e.find((e=>e.id===t)))).filter(Boolean)),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugColumns}}),getCenterLeafColumns:u((()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right]),((e,t,n)=>{const l=[...null!=t?t:[],...null!=n?n:[]];return e.filter((e=>!l.includes(e.id)))}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugColumns}})})},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],globalFilter:void 0,...e}),getDefaultOptions:e=>({onColumnFiltersChange:o("columnFilters",e),onGlobalFilterChange:o("globalFilter",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100,globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n,l;const o=null==(n=e.getCoreRowModel().flatRows[0])||null==(l=n._getAllCellsByColumnId()[t.id])?void 0:l.getValue();return"string"===typeof o||"number"===typeof o}}),createColumn:(e,t)=>({getAutoFilterFn:()=>{const n=t.getCoreRowModel().flatRows[0],l=null==n?void 0:n.getValue(e.id);return"string"===typeof l?V.includesString:"number"===typeof l?V.inNumberRange:"boolean"===typeof l||null!==l&&"object"===typeof l?V.equals:Array.isArray(l)?V.arrIncludes:V.weakEquals},getFilterFn:()=>{var n,l;return i(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(l=t.options.filterFns)?void 0:l[e.columnDef.filterFn])?n:V[e.columnDef.filterFn]},getCanFilter:()=>{var n,l,o;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(l=t.options.enableColumnFilters)||l)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},getCanGlobalFilter:()=>{var n,l,o,i;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(l=t.options.enableGlobalFilter)||l)&&(null==(o=t.options.enableFilters)||o)&&(null==(i=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||i)&&!!e.accessorFn},getIsFiltered:()=>e.getFilterIndex()>-1,getFilterValue:()=>{var n,l;return null==(n=t.getState().columnFilters)||null==(l=n.find((t=>t.id===e.id)))?void 0:l.value},getFilterIndex:()=>{var n,l;return null!=(n=null==(l=t.getState().columnFilters)?void 0:l.findIndex((t=>t.id===e.id)))?n:-1},setFilterValue:n=>{t.setColumnFilters((t=>{const o=e.getFilterFn(),i=null==t?void 0:t.find((t=>t.id===e.id)),r=l(n,i?i.value:void 0);var u;if(I(o,r,e))return null!=(u=null==t?void 0:t.filter((t=>t.id!==e.id)))?u:[];const a={id:e.id,value:r};var s;return i?null!=(s=null==t?void 0:t.map((t=>t.id===e.id?a:t)))?s:[]:null!=t&&t.length?[...t,a]:[a]}))},_getFacetedRowModel:t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),getFacetedRowModel:()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),_getFacetedUniqueValues:t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),getFacetedUniqueValues:()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,_getFacetedMinMaxValues:t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),getFacetedMinMaxValues:()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}),createRow:(e,t)=>({columnFilters:{},columnFiltersMeta:{}}),createTable:e=>({getGlobalAutoFilterFn:()=>V.includesString,getGlobalFilterFn:()=>{var t,n;const{globalFilterFn:l}=e.options;return i(l)?l:"auto"===l?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[l])?t:V[l]},setColumnFilters:t=>{const n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange((e=>{var o;return null==(o=l(t,e))?void 0:o.filter((e=>{const t=n.find((t=>t.id===e.id));if(t){if(I(t.getFilterFn(),e.value,t))return!1}return!0}))}))},setGlobalFilter:t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},resetGlobalFilter:t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)},resetColumnFilters:t=>{var n,l;e.setColumnFilters(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.columnFilters)?n:[])},getPreFilteredRowModel:()=>e.getCoreRowModel(),getFilteredRowModel:()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel()),_getGlobalFacetedRowModel:e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),getGlobalFacetedRowModel:()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),_getGlobalFacetedUniqueValues:e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),getGlobalFacetedUniqueValues:()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,_getGlobalFacetedMinMaxValues:e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),getGlobalFacetedMinMaxValues:()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}})},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto"}),getDefaultOptions:e=>({onSortingChange:o("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>({getAutoSortingFn:()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let l=!1;for(const t of n){const n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return k.datetime;if("string"===typeof n&&(l=!0,n.split(G).length>1))return k.alphanumeric}return l?k.text:k.basic},getAutoSortDir:()=>{const n=t.getFilteredRowModel().flatRows[0];return"string"===typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},getSortingFn:()=>{var n,l;if(!e)throw new Error;return i(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(l=t.options.sortingFns)?void 0:l[e.columnDef.sortingFn])?n:k[e.columnDef.sortingFn]},toggleSorting:(n,l)=>{const o=e.getNextSortingOrder(),i="undefined"!==typeof n&&null!==n;t.setSorting((r=>{const u=null==r?void 0:r.find((t=>t.id===e.id)),a=null==r?void 0:r.findIndex((t=>t.id===e.id));let s,g=[],d=i?n:"desc"===o;var c;(s=null!=r&&r.length&&e.getCanMultiSort()&&l?u?"toggle":"add":null!=r&&r.length&&a!==r.length-1?"replace":u?"toggle":"replace","toggle"===s&&(i||o||(s="remove")),"add"===s)?(g=[...r,{id:e.id,desc:d}],g.splice(0,g.length-(null!=(c=t.options.maxMultiSortColCount)?c:Number.MAX_SAFE_INTEGER))):g="toggle"===s?r.map((t=>t.id===e.id?{...t,desc:d}:t)):"remove"===s?r.filter((t=>t.id!==e.id)):[{id:e.id,desc:d}];return g}))},getFirstSortDir:()=>{var n,l;return(null!=(n=null!=(l=e.columnDef.sortDescFirst)?l:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},getNextSortingOrder:n=>{var l,o;const i=e.getFirstSortDir(),r=e.getIsSorted();return r?!!(r===i||null!=(l=t.options.enableSortingRemoval)&&!l||n&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===r?"asc":"desc"):i},getCanSort:()=>{var n,l;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(l=t.options.enableSorting)||l)&&!!e.accessorFn},getCanMultiSort:()=>{var n,l;return null!=(n=null!=(l=e.columnDef.enableMultiSort)?l:t.options.enableMultiSort)?n:!!e.accessorFn},getIsSorted:()=>{var n;const l=null==(n=t.getState().sorting)?void 0:n.find((t=>t.id===e.id));return!!l&&(l.desc?"desc":"asc")},getSortIndex:()=>{var n,l;return null!=(n=null==(l=t.getState().sorting)?void 0:l.findIndex((t=>t.id===e.id)))?n:-1},clearSorting:()=>{t.setSorting((t=>null!=t&&t.length?t.filter((t=>t.id!==e.id)):[]))},getToggleSortingHandler:()=>{const n=e.getCanSort();return l=>{n&&(null==l.persist||l.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(l))))}}}),createTable:e=>({setSorting:t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),resetSorting:t=>{var n,l;e.setSorting(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.sorting)?n:[])},getPreSortedRowModel:()=>e.getGroupedRowModel(),getSortedRowModel:()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())})},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:o("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>({toggleGrouping:()=>{t.setGrouping((t=>null!=t&&t.includes(e.id)?t.filter((t=>t!==e.id)):[...null!=t?t:[],e.id]))},getCanGroup:()=>{var n,l,o,i;return null!=(n=null==(l=null!=(o=null==(i=e.columnDef.enableGrouping)||i)?o:t.options.enableGrouping)||l)?n:!!e.accessorFn},getIsGrouped:()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},getGroupedIndex:()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},getToggleGroupingHandler:()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},getAutoAggregationFn:()=>{const n=t.getCoreRowModel().flatRows[0],l=null==n?void 0:n.getValue(e.id);return"number"===typeof l?x.sum:"[object Date]"===Object.prototype.toString.call(l)?x.extent:void 0},getAggregationFn:()=>{var n,l;if(!e)throw new Error;return i(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(l=t.options.aggregationFns)?void 0:l[e.columnDef.aggregationFn])?n:x[e.columnDef.aggregationFn]}}),createTable:e=>({setGrouping:t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),resetGrouping:t=>{var n,l;e.setGrouping(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.grouping)?n:[])},getPreGroupedRowModel:()=>e.getFilteredRowModel(),getGroupedRowModel:()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())}),createRow:(e,t)=>({getIsGrouped:()=>!!e.groupingColumnId,getGroupingValue:n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];const l=t.getColumn(n);return null!=l&&l.columnDef.getGroupingValue?(e._groupingValuesCache[n]=l.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},_groupingValuesCache:{}}),createCell:(e,t,n,l)=>({getIsGrouped:()=>t.getIsGrouped()&&t.id===n.groupingColumnId,getIsPlaceholder:()=>!e.getIsGrouped()&&t.getIsGrouped(),getIsAggregated:()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!(null==(t=n.subRows)||!t.length)}})},m,{getInitialState:e=>({...e,pagination:{pageIndex:0,pageSize:10,...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:o("pagination",e)}),createTable:e=>{let t=!1,n=!1;return{_autoResetPageIndex:()=>{var l,o;if(t){if(null!=(l=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?l:!e.options.manualPagination){if(n)return;n=!0,e._queue((()=>{e.resetPageIndex(),n=!1}))}}else e._queue((()=>{t=!0}))},setPagination:t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange((e=>l(t,e))),resetPagination:t=>{var n;e.setPagination(t?{pageIndex:0,pageSize:10}:null!=(n=e.initialState.pagination)?n:{pageIndex:0,pageSize:10})},setPageIndex:t=>{e.setPagination((n=>{let o=l(t,n.pageIndex);const i="undefined"===typeof e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return o=Math.max(0,Math.min(o,i)),{...n,pageIndex:o}}))},resetPageIndex:t=>{var n,l,o;e.setPageIndex(t?0:null!=(n=null==(l=e.initialState)||null==(o=l.pagination)?void 0:o.pageIndex)?n:0)},resetPageSize:t=>{var n,l,o;e.setPageSize(t?10:null!=(n=null==(l=e.initialState)||null==(o=l.pagination)?void 0:o.pageSize)?n:10)},setPageSize:t=>{e.setPagination((e=>{const n=Math.max(1,l(t,e.pageSize)),o=e.pageSize*e.pageIndex,i=Math.floor(o/n);return{...e,pageIndex:i,pageSize:n}}))},setPageCount:t=>e.setPagination((n=>{var o;let i=l(t,null!=(o=e.options.pageCount)?o:-1);return"number"===typeof i&&(i=Math.max(-1,i)),{...n,pageCount:i}})),getPageOptions:u((()=>[e.getPageCount()]),(e=>{let t=[];return e&&e>0&&(t=[...new Array(e)].fill(null).map(((e,t)=>t))),t}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugTable}}),getCanPreviousPage:()=>e.getState().pagination.pageIndex>0,getCanNextPage:()=>{const{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return-1===n||0!==n&&t<n-1},previousPage:()=>e.setPageIndex((e=>e-1)),nextPage:()=>e.setPageIndex((e=>e+1)),getPrePaginationRowModel:()=>e.getExpandedRowModel(),getPaginationRowModel:()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),getPageCount:()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getPrePaginationRowModel().rows.length/e.getState().pagination.pageSize)}}}},P,c];function B(e){var t;(e.debugAll||e.debugTable)&&console.info("Creating Table Instance...");let n={_features:O};const o=n._features.reduce(((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(n))),{});let i={...null!=(t=e.initialState)?t:{}};n._features.forEach((e=>{var t;i=null!=(t=null==e.getInitialState?void 0:e.getInitialState(i))?t:i}));const r=[];let a=!1;const s={_features:O,options:{...o,...e},initialState:i,_queue:e=>{r.push(e),a||(a=!0,Promise.resolve().then((()=>{for(;r.length;)r.shift()();a=!1})).catch((e=>setTimeout((()=>{throw e})))))},reset:()=>{n.setState(n.initialState)},setOptions:e=>{const t=l(e,n.options);n.options=(e=>n.options.mergeOptions?n.options.mergeOptions(o,e):{...o,...e})(t)},getState:()=>n.options.state,setState:e=>{null==n.options.onStateChange||n.options.onStateChange(e)},_getRowId:(e,t,l)=>{var o;return null!=(o=null==n.options.getRowId?void 0:n.options.getRowId(e,t,l))?o:`${l?[l.id,t].join("."):t}`},getCoreRowModel:()=>(n._getCoreRowModel||(n._getCoreRowModel=n.options.getCoreRowModel(n)),n._getCoreRowModel()),getRowModel:()=>n.getPaginationRowModel(),getRow:e=>{const t=n.getRowModel().rowsById[e];if(!t)throw new Error;return t},_getDefaultColumnDef:u((()=>[n.options.defaultColumn]),(e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{const t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...n._features.reduce(((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef())),{}),...e}}),{debug:()=>{var e;return null!=(e=n.options.debugAll)?e:n.options.debugColumns},key:!1}),_getColumnDefs:()=>n.options.columns,getAllColumns:u((()=>[n._getColumnDefs()]),(e=>{const t=function(e,l,o){return void 0===o&&(o=0),e.map((e=>{const i=function(e,t,n,l){var o,i;const r={...e._getDefaultColumnDef(),...t},a=r.accessorKey;let s,g=null!=(o=null!=(i=r.id)?i:a?a.replace(".","_"):void 0)?o:"string"===typeof r.header?r.header:void 0;if(r.accessorFn?s=r.accessorFn:a&&(s=a.includes(".")?e=>{let t=e;for(const l of a.split(".")){var n;t=null==(n=t)?void 0:n[l]}return t}:e=>e[r.accessorKey]),!g)throw new Error;let d={id:`${String(g)}`,accessorFn:s,parent:l,depth:n,columnDef:r,columns:[],getFlatColumns:u((()=>[!0]),(()=>{var e;return[d,...null==(e=d.columns)?void 0:e.flatMap((e=>e.getFlatColumns()))]}),{key:"column.getFlatColumns",debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugColumns}}),getLeafColumns:u((()=>[e._getOrderColumnsFn()]),(e=>{var t;if(null!=(t=d.columns)&&t.length){let t=d.columns.flatMap((e=>e.getLeafColumns()));return e(t)}return[d]}),{key:"column.getLeafColumns",debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugColumns}})};return d=e._features.reduce(((t,n)=>Object.assign(t,null==n.createColumn?void 0:n.createColumn(d,e))),d),d}(n,e,o,l),r=e;return i.columns=r.columns?t(r.columns,i,o+1):[],i}))};return t(e)}),{key:!1,debug:()=>{var e;return null!=(e=n.options.debugAll)?e:n.options.debugColumns}}),getAllFlatColumns:u((()=>[n.getAllColumns()]),(e=>e.flatMap((e=>e.getFlatColumns()))),{key:!1,debug:()=>{var e;return null!=(e=n.options.debugAll)?e:n.options.debugColumns}}),_getAllFlatColumnsById:u((()=>[n.getAllFlatColumns()]),(e=>e.reduce(((e,t)=>(e[t.id]=t,e)),{})),{key:!1,debug:()=>{var e;return null!=(e=n.options.debugAll)?e:n.options.debugColumns}}),getAllLeafColumns:u((()=>[n.getAllColumns(),n._getOrderColumnsFn()]),((e,t)=>t(e.flatMap((e=>e.getLeafColumns())))),{key:!1,debug:()=>{var e;return null!=(e=n.options.debugAll)?e:n.options.debugColumns}}),getColumn:e=>n._getAllFlatColumnsById()[e]};return Object.assign(n,s),n._features.forEach((e=>Object.assign(n,null==e.createTable?void 0:e.createTable(n)))),n}const T=(e,t,n,l,o,i,a)=>{let s={id:t,index:l,original:n,depth:o,parentId:a,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(s._valuesCache.hasOwnProperty(t))return s._valuesCache[t];const n=e.getColumn(t);return null!=n&&n.accessorFn?(s._valuesCache[t]=n.accessorFn(s.original,l),s._valuesCache[t]):void 0},getUniqueValues:t=>{if(s._uniqueValuesCache.hasOwnProperty(t))return s._uniqueValuesCache[t];const n=e.getColumn(t);return null!=n&&n.accessorFn?n.columnDef.getUniqueValues?(s._uniqueValuesCache[t]=n.columnDef.getUniqueValues(s.original,l),s._uniqueValuesCache[t]):(s._uniqueValuesCache[t]=[s.getValue(t)],s._uniqueValuesCache[t]):void 0},renderValue:t=>{var n;return null!=(n=s.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=i?i:[],getLeafRows:()=>r(s.subRows,(e=>e.subRows)),getParentRow:()=>s.parentId?e.getRow(s.parentId):void 0,getParentRows:()=>{let e=[],t=s;for(;;){const n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:u((()=>[e.getAllLeafColumns()]),(t=>t.map((t=>function(e,t,n,l){const o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(l),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:u((()=>[e,n,t,o]),((e,t,n,l)=>({table:e,column:t,row:n,cell:l,getValue:l.getValue,renderValue:l.renderValue})),{key:!1,debug:()=>e.options.debugAll})};return e._features.forEach((l=>{Object.assign(o,null==l.createCell?void 0:l.createCell(o,n,t,e))}),{}),o}(e,s,t,t.id)))),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugRows}}),_getAllCellsByColumnId:u((()=>[s.getAllCells()]),(e=>e.reduce(((e,t)=>(e[t.column.id]=t,e)),{})),{key:"row.getAllCellsByColumnId",debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugRows}})};for(let r=0;r<e._features.length;r++){const t=e._features[r];Object.assign(s,null==t||null==t.createRow?void 0:t.createRow(s,e))}return s};function q(){return e=>u((()=>[e.options.data]),(t=>{const n={rows:[],flatRows:[],rowsById:{}},l=function(t,o,i){void 0===o&&(o=0);const r=[];for(let a=0;a<t.length;a++){const s=T(e,e._getRowId(t[a],a,i),t[a],a,o,void 0,null==i?void 0:i.id);var u;if(n.flatRows.push(s),n.rowsById[s.id]=s,r.push(s),e.options.getSubRows)s.originalSubRows=e.options.getSubRows(t[a],a),null!=(u=s.originalSubRows)&&u.length&&(s.subRows=l(s.originalSubRows,o+1,s))}return r};return n.rows=l(t),n}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugTable},onChange:()=>{e._autoResetPageIndex()}})}function j(){return e=>u((()=>[e.getState().sorting,e.getPreSortedRowModel()]),((t,n)=>{if(!n.rows.length||null==t||!t.length)return n;const l=e.getState().sorting,o=[],i=l.filter((t=>{var n;return null==(n=e.getColumn(t.id))?void 0:n.getCanSort()})),r={};i.forEach((t=>{const n=e.getColumn(t.id);n&&(r[t.id]={sortUndefined:n.columnDef.sortUndefined,invertSorting:n.columnDef.invertSorting,sortingFn:n.getSortingFn()})}));const u=e=>{const t=[...e];return t.sort(((e,t)=>{for(let l=0;l<i.length;l+=1){var n;const o=i[l],u=r[o.id],a=null!=(n=null==o?void 0:o.desc)&&n;if(u.sortUndefined){const n="undefined"===typeof e.getValue(o.id),l="undefined"===typeof t.getValue(o.id);if(n||l){let e=n&&l?0:n?u.sortUndefined:-u.sortUndefined;return a&&0!==e&&(e*=-1),e}}let s=u.sortingFn(e,t,o.id);if(0!==s)return a&&(s*=-1),u.invertSorting&&(s*=-1),s}return e.index-t.index})),t.forEach((e=>{var t;o.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=u(e.subRows))})),t};return{rows:u(n.rows),flatRows:o,rowsById:n.rowsById}}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugTable},onChange:()=>{e._autoResetPageIndex()}})}function N(){return e=>u((()=>[e.getState().expanded,e.getPreExpandedRowModel(),e.options.paginateExpandedRows]),((e,t,n)=>!t.rows.length||!0!==e&&!Object.keys(null!=e?e:{}).length?t:n?U(t):t),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugTable}})}function U(e){const t=[],n=e=>{var l;t.push(e),null!=(l=e.subRows)&&l.length&&e.getIsExpanded()&&e.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}function $(e){return e=>u((()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded]),((t,n)=>{if(!n.rows.length)return n;const{pageSize:l,pageIndex:o}=t;let{rows:i,flatRows:r,rowsById:u}=n;const a=l*o,s=a+l;let g;i=i.slice(a,s),g=e.options.paginateExpandedRows?{rows:i,flatRows:r,rowsById:u}:U({rows:i,flatRows:r,rowsById:u}),g.flatRows=[];const d=e=>{g.flatRows.push(e),e.subRows.length&&e.subRows.forEach(d)};return g.rows.forEach(d),g}),{key:!1,debug:()=>{var t;return null!=(t=e.options.debugAll)?t:e.options.debugTable}})}},70618:function(e,t,n){n.d(t,{Kv:function(){return i},N4:function(){return r}});var l=n(31014),o=n(9856);function i(e,t){return e?function(e){return"function"===typeof e&&(()=>{const t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()}(n=e)||"function"===typeof n||function(e){return"object"===typeof e&&"symbol"===typeof e.$$typeof&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}(n)?l.createElement(e,t):e:null;var n}function r(e){const t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=l.useState((()=>({current:(0,o.ZR)(t)}))),[i,r]=l.useState((()=>n.current.initialState));return n.current.setOptions((t=>({...t,...e,state:{...i,...e.state},onStateChange:t=>{r(t),null==e.onStateChange||e.onStateChange(t)}}))),n.current}}}]);
//# sourceMappingURL=618.c43acc34.chunk.js.map