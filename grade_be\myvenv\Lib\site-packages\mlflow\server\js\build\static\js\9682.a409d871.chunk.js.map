{"version": 3, "file": "static/js/9682.a409d871.chunk.js", "mappings": "2LAMO,MAAMA,EAA2BC,IAMjC,IANkC,WACvCC,EAAU,UACVC,GAIDF,EACC,OAAIC,IAAeE,EAAAA,GAAWC,UACrBC,EAAAA,EAAAA,GAACC,EAAAA,IAAY,CAACJ,UAAWA,IACvBD,IAAeE,EAAAA,GAAWI,OAC5BF,EAAAA,EAAAA,GAACG,EAAAA,IAAY,CAACN,UAAWA,IACvBD,IAAeE,EAAAA,GAAWM,SAC5BJ,EAAAA,EAAAA,GAACK,EAAAA,IAAgB,CAACR,UAAWA,IAC3BD,IAAeE,EAAAA,GAAWQ,KAC5BN,EAAAA,EAAAA,GAACO,EAAAA,IAAa,CAACV,UAAWA,IAE5B,IAAI,C,sECpBb,SAASW,EAAqBC,GAC5B,QAAgBC,IAAZD,EAGJ,MAA0B,kBAAZA,GAAwBA,aAAmBE,UAAYF,aAAmBG,KACpFH,EACAI,KAAKC,UAAUL,EACrB,CAGO,MAAMM,EAA0BC,eACrCC,GAGI,IAFJC,EAA2CC,UAAAC,OAAA,QAAAV,IAAAS,UAAA,GAAAA,UAAA,GAAG,MAC9CE,EAAUF,UAAAC,OAAA,EAAAD,UAAA,QAAAT,EAEV,MAAMY,QAAiBC,MAAMN,EAAK,CAChCC,SACAG,KAAMb,EAAqBa,GAC3BG,QAASH,EAAO,CAAE,eAAgB,oBAAuB,CAAC,IAE5D,IAAKC,EAASG,GAAI,CAChB,MAAMC,GAAkBC,EAAAA,EAAAA,IAAqBL,GAC7C,GAAII,EAAiB,CACnB,IAEE,MAAME,SAAiBN,EAASO,QAAQD,QACxCF,EAAgBE,QAAiB,OAAPA,QAAO,IAAPA,EAAAA,EAAWF,EAAgBE,OACvD,CAAE,MACA,CAEF,MAAMF,CACR,CACF,CACA,OAAOJ,EAASO,MAClB,C,wGCdA,MAAMC,EAAmBC,EAAAA,YACvB,CAACC,EAA8BC,KAC7B,MAAM,QACJC,EAAO,QACPC,EAAO,KACPC,EAAI,OACJC,EAAM,QACNC,EAAO,aACPC,EAAY,aACZC,EAAY,YACZC,EAAW,gBACXC,EAAe,KACfC,KACGC,GACDZ,GACE,MAAEa,IAAUC,EAAAA,EAAAA,KAEZC,GAAeC,EAAAA,EAAAA,GAAuC,CAC1DC,cAAeC,EAAAA,EAAwCC,OACvDV,cACAC,gBAAgC,OAAfA,QAAe,IAAfA,EAAAA,EAAmB,CAACU,EAAAA,EAA6CC,WAGpF,OACErD,EAAAA,EAAAA,GAAA,UACEmC,QAAUmB,IACRP,EAAaZ,QAAQmB,GACd,OAAPnB,QAAO,IAAPA,GAAAA,EAAUmB,EAAM,EAElBC,KAAGC,EAAAA,EAAAA,IAAE,CACHC,OAAQ,UACRC,MAAOb,EAAMc,QAAQC,SACrBC,OAAQhB,EAAMc,QAAQC,SACtBE,aAAcjB,EAAMkB,cAAcC,eAClCC,WAAYpB,EAAMqB,WAAWC,eAC7BC,QAAS,EACTC,OAAQ,EACRC,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBC,WAAYvC,EAAUW,EAAM6B,OAAOC,6BAA+B,cAClEC,MAAO1C,EAAUW,EAAM6B,OAAOG,uBAAyBhC,EAAM6B,OAAOI,cACpE,UAAW,CACTL,WAAY5B,EAAM6B,OAAOK,6BACzBH,MAAO/B,EAAM6B,OAAOM,yBAEvB,IACD/C,IAAKA,EACLI,OAAQA,EACRC,QAASA,EACTC,aAAcA,EACdC,aAAcA,KACVI,EAAcqC,SAEjB7C,GACM,G,6HCzEiE,IAAA8C,EAAA,CAAAC,KAAA,UAAAC,OAAA,0CAEzE,MAAMC,EAAqBtD,EAAAA,MAAWpC,IAAqD,IAAD2F,EAAA,IAAjDC,MAAOC,GAAqC7F,EAC1F,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,KAClB,IAAK0C,EACH,OAAOxF,EAAAA,EAAAA,GAAAyF,EAAAA,GAAA,CAAAR,SAAE,MAEX,MAAMrF,GAAsC,QAAzB0F,EAAAE,EAAKE,EAAAA,EAAMC,sBAAc,IAAAL,OAAA,EAAzBA,EAA2BC,QAAS,GAEjDK,EAAaF,EAAAA,EAAMG,aAAaL,GAAQ,CAAC,OAAG9E,OAAWA,GAC7D,OAAOkF,GACLE,EAAAA,EAAAA,IAAA,OAAKvC,KAAGC,EAAAA,EAAAA,IAAE,CAAEc,QAAS,OAAQyB,IAAKlD,EAAMmD,QAAQC,GAAI1B,WAAY,UAAU,IAACU,SAAA,EACzEjF,EAAAA,EAAAA,GAACN,EAAAA,EAAwB,CAACE,WAAYA,EAAY2D,KAAGC,EAAAA,EAAAA,IAAE,CAAEoB,MAAO/B,EAAM6B,OAAOI,eAAe,OAC5F9E,EAAAA,EAAAA,GAAA,QAAMuD,IAAG2B,EAAmDD,SAAEW,QAGhE5F,EAAAA,EAAAA,GAAAyF,EAAAA,GAAA,CAAAR,SAAE,KACH,G,8FCnBI,MAAMiB,EAAwBA,CAACC,EAA0BC,IAC9DD,EAASE,SAAWD,EAASC,QAAUF,EAAShB,OAASiB,EAASjB,MAAQgB,EAASG,UAAYF,EAASE,QAE7FC,EAAuBC,IAClC,MAAM,QAAEC,GAAYD,EACd5G,EAAa6G,EAAQ7G,WAC3B,IACE,GAAIA,IAAe8G,EAAAA,GAAmBC,KAAM,CAC1C,MAAM,IAAE1F,GAAQJ,KAAK+F,MAAMH,EAAQI,QACnC,OAAO5F,CACT,CACA,GAAIrB,IAAe8G,EAAAA,GAAmBI,GAAI,CACxC,MAAM,IAAEC,GAAQlG,KAAK+F,MAAMH,EAAQI,QACnC,OAAOE,CACT,CACA,GAAInH,IAAe8G,EAAAA,GAAmBM,aAAc,CAClD,MAAM,KAAEC,GAASpG,KAAK+F,MAAMH,EAAQI,QACpC,MAAO,mCAAmCI,GAC5C,CACF,CAAE,MACA,OAAO,IACT,CACA,OAAO,IAAI,C,4DCxBTC,EAAOC,EAAIC,EAAQC,EAAQC,E,WAC/B,SAASC,IAAa,OAAOA,EAAWC,OAAOC,OAASD,OAAOC,OAAOC,OAAS,SAAUC,GAAK,IAAK,IAAIC,EAAI,EAAGA,EAAIzG,UAAUC,OAAQwG,IAAK,CAAE,IAAIC,EAAI1G,UAAUyG,GAAI,IAAK,IAAIE,KAAKD,GAAG,CAAG,GAAEE,eAAeC,KAAKH,EAAGC,KAAOH,EAAEG,GAAKD,EAAEC,GAAK,CAAE,OAAOH,CAAG,EAAGJ,EAASU,MAAM,KAAM9G,UAAY,CAEnR,SAAS+G,EAAyBvI,EAAMwI,GACtC,IAAI,MACFC,EAAK,QACLC,KACGrG,GACDrC,EACJ,OAAoB,gBAAoB,MAAO4H,EAAS,CACtD7D,MAAO,GACPG,OAAQ,GACRyE,QAAS,YACTC,KAAM,OACNC,MAAO,6BACPvG,IAAKkG,EACL,kBAAmBE,GAClBrG,GAAQoG,EAAqB,gBAAoB,QAAS,CAC3DK,GAAIJ,GACHD,GAAS,KAAmB,gBAAoB,IAAK,CACtDM,SAAU,mBACI,gBAAoB,OAAQ,CAC1CD,GAAI,YACJE,MAAO,CACLC,SAAU,aAEZC,UAAW,iBACXC,EAAG,EACHC,EAAG,EACHrF,MAAO,GACPG,OAAQ,IACPqD,IAAUA,EAAqB,gBAAoB,OAAQ,CAC5D8B,EAAG,mBACHT,KAAM,YACFpB,IAAOA,EAAkB,gBAAoB,IAAK,CACtD8B,KAAM,mBACQ,gBAAoB,OAAQ,CAC1CC,SAAU,UACVC,SAAU,UACVH,EAAG,uuDACHT,KAAM,mBACFnB,IAAWA,EAAsB,gBAAoB,OAAQ,CACjE4B,EAAG,uHACHT,KAAM,WACHlB,IAAWA,EAAsB,gBAAoB,OAAQ,CAChE6B,SAAU,UACVC,SAAU,UACVH,EAAG,0dACHT,KAAM,cACFjB,IAAUA,EAAqB,gBAAoB,OAAQ,KAAmB,gBAAoB,WAAY,CAClHmB,GAAI,aACU,gBAAoB,OAAQ,CAC1C/E,MAAO,GACPG,OAAQ,GACR0E,KAAM,aAEV,CACA,MAAMa,EAA0B,aAAiBlB,GAClC,G,wDChDA,MAAMmB,EAanB,2BAAOC,CAAqBC,EAAoBd,GAC9C,OAAO,IAAIe,EAAkB,CAACD,EAAed,GAAIgB,KAAK,KAAM,eAC9D,CAEA,wCAAOC,CAAkCH,EAAoBd,GAC3D,OAAO,IAAIe,EAAkB,CAACD,EAAed,GAAIgB,KAAK,KAAM,iBAC9D,EAnBmBJ,EAMZM,QAAU,MAoBnB,MAAMH,EACJI,WAAAA,CAAYC,EAAYlH,GAAY,KAUpCkH,WAAK,OACLC,gBAAU,EAVRC,KAAKF,MAAQA,EAEXE,KAAKD,WADM,iBAATnH,EACgBqH,OAAOC,aAEPD,OAAOE,cAE7B,CASAC,kBAAAA,GACE,MAAMC,EAAYL,KAAKM,QAAQb,EAAkBc,wBACjD,OAAIF,EACKvJ,KAAK+F,MAAMwD,GAEb,CAAC,CACV,CAMAG,kBAAAA,CAAmBC,GACjB,MAAMC,EAA4C,oBAAvBD,EAAYE,OAAwBF,EAAYE,SAAWF,EACtFT,KAAKY,QAAQnB,EAAkBc,uBAAwBzJ,KAAKC,UAAU2J,GACxE,CAMAG,eAAAA,CAAgBC,GACd,MAAO,CAAC,qBAAsBxB,EAAkBM,QAASI,KAAKF,MAAOgB,GAAKpB,KAAK,IACjF,CAGAkB,OAAAA,CAAQE,EAAUtF,GAChBwE,KAAKD,WAAWa,QAAQZ,KAAKa,gBAAgBC,GAAMtF,EACrD,CAGA8E,OAAAA,CAAQQ,GACN,OAAOd,KAAKD,WAAWO,QAAQN,KAAKa,gBAAgBC,GACtD,EAlDIrB,EASGc,uBAAyB,qB,8HC1CqC,IAAAQ,EAAA,CAAA3F,KAAA,UAAAC,OAAA,mBAQhE,MAAM2F,EAAmCpL,IAIhB,IAADqL,EAAA,IAJkB,gBAC/CxE,EAAe,kBACfyE,EAAiB,UACjBpL,GACwBF,EACxB,MAAM,QAAE8G,EAAO,KAAEjB,GAASgB,GACpB,MAAE3D,IAAUC,EAAAA,EAAAA,KAEZoI,EAAiB,OAAJ1F,QAAI,IAAJA,GAA+D,QAA3DwF,EAAJxF,EAAM2F,MAAKjG,IAAA,IAAC,IAAE2F,GAAK3F,EAAA,OAAK2F,IAAQO,EAAAA,EAA8B,WAAC,IAAAJ,OAA3D,EAAJA,EAAiEzF,MAEpF,OACEO,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT+G,cAAe,MACf9G,WAAY,SACZ+G,UAAWzI,EAAMmD,QAAQC,GACzBsF,aAAc1I,EAAMmD,QAAQC,IAC7B,IACDpG,UAAWA,EAAUoF,SAAA,EAErBjF,EAAAA,EAAAA,GAACwL,EAAAA,IAAS,CAACjI,KAAGC,EAAAA,EAAAA,IAAE,CAAEiI,YAAa5I,EAAMmD,QAAQC,GAAIrB,MAAO/B,EAAM6B,OAAOI,eAAe,MACnFmG,GACCnF,EAAAA,EAAAA,IAAA,OAAAb,SAAA,CACGwB,EAAQtB,KAAK,KAAGsB,EAAQJ,OAAO,QAGlCP,EAAAA,EAAAA,IAAC4F,EAAAA,EAAWC,KAAI,CAACC,KAAK,KAAKrI,IAAGuH,EAAsB7F,SAAA,CACjDwB,EAAQtB,KAAK,KAAGsB,EAAQJ,OAAO,OAGnC6E,IACClL,EAAAA,EAAAA,GAAC6L,EAAAA,IAAG,CACFpJ,YAAY,gIACZc,KAAGC,EAAAA,EAAAA,IAAE,CACHsI,cAAe,aACfC,WAAYlJ,EAAMmD,QAAQC,GAC1BwF,YAAa5I,EAAMmD,QAAQC,IAC5B,IAAChB,SAEDiG,MAGD,C,yIC3CH,MAAMc,EAA0CrM,IAA6D,IAA5D,KAAEkC,EAAI,aAAEoK,EAAY,aAAEC,EAAY,iBAAEC,GAAkBxM,EAC5G,MAAM,cAAEyM,EAAa,cAAEC,GAAkBC,EAAiBzK,GAsB1D,OACE7B,EAAAA,EAAAA,GAAA,OAAK2I,MAAO,CApBZ4D,SAAU,WACVC,UAAW,kBACXC,SAAU,YAkB+BR,GAAehH,SACrDoH,GACCvG,EAAAA,EAAAA,IAAAL,EAAAA,GAAA,CAAAR,SAAA,EACEjF,EAAAA,EAAAA,GAAC0M,EAAAA,GAAW,CAACC,SAAS,OAAOhE,MAAO,CAR1CvE,QAAS,MACTwI,UAAW,YAOgET,GAAmBlH,SACrFmH,KAEHpM,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CApBhB+I,SAAU,WACVM,OAAQ,EACRC,MAAO,EACPC,KAAM,EACNlJ,OAAQ,MACRY,WAAY,yCAeiCyH,GAAc,UAGvDlM,EAAAA,EAAAA,GAAAyF,EAAAA,GAAA,CAAAR,SAAGpD,KAED,EAIV,SAASyK,EAAiBzK,GACxB,OAAOE,EAAAA,SAAc,KACnB,IACE,MAAMiL,EAASnM,KAAK+F,MAAM/E,GACpBoL,GAASC,EAAAA,EAAAA,UAASF,IAA6B,oBAAXA,KAA2BA,aAAkBG,MACvF,MAAO,CACLf,cAAea,EAASpM,KAAKC,UAAUkM,EAAQ,KAAM,GAAKnL,EAC1DwK,cAAeY,EAEnB,CAAE,MAAOrF,GACP,MAAO,CACLwE,cAAevK,EACfwK,eAAe,EAEnB,IACC,CAACxK,GACN,CAAC,IAAAiJ,EAAA,CAAA3F,KAAA,UAAAC,OAAA,wBAEM,MAAMgI,EAAmDlI,IAAe,IAAd,KAAErD,GAAMqD,EACvE,MAAM,cAAEkH,EAAa,cAAEC,GAAkBC,EAAiBzK,GAE1D,OACE7B,EAAAA,EAAAA,GAAA,OAAKuD,IAAGuH,EAA6B7F,SAClCoH,GACCrM,EAAAA,EAAAA,GAAC0M,EAAAA,GAAW,CAACC,SAAS,OAAOU,eAAa,EAAApI,SACvCmH,KAGHpM,EAAAA,EAAAA,GAAA,QAAAiF,SAAOpD,KAEL,C,4IClEV,MAAMyL,EAAQ,CACZC,YAAYvN,EAAAA,EAAAA,GAACwN,EAAAA,IAAQ,IACrBC,aAAazN,EAAAA,EAAAA,GAAC0N,EAAAA,IAAQ,IACtBC,QAAQ3N,EAAAA,EAAAA,GAAC4N,EAAAA,IAAU,IACnBC,SAAS7N,EAAAA,EAAAA,GAAC8N,EAAAA,IAAW,IACrBC,OAAO/N,EAAAA,EAAAA,GAACgO,EAAAA,EAAS,IACjBjB,MAAM/M,EAAAA,EAAAA,GAACiO,EAAAA,IAAa,IACpBnB,OAAO9M,EAAAA,EAAAA,GAACkO,EAAAA,IAAc,KAGXC,EAAoBxO,IAQ1B,IAR2B,SAChCsF,EAAQ,QACRmJ,EAAO,gBACPC,GAKD1O,EACC,MAAM,kBAAE2O,IAAsBC,EAAAA,EAAAA,YAAWC,EAAAA,IAEzC,OACExO,EAAAA,EAAAA,GAACyO,EAAAA,EAAQC,aAAY,CACnBpB,MAAOA,EACPqB,QAAS,CACPP,QAASA,EACTQ,aAAcN,EACdD,gBAAkBQ,GAAMR,EAAgBQ,IACxC5J,SAEDA,GACoB,C,yGC3C3B,MAAM6J,GAA2CC,EAAAA,EAAAA,eAG9C,CAAC,GAKSC,EAAmDrP,IAQzD,IAR0D,SAC/DsF,EAAQ,iBACRgK,EAAgB,uBAChBC,GAKDvP,EACC,OACEK,EAAAA,EAAAA,GAAC8O,EAAyCK,SAAQ,CAAC5J,MAAO,CAAE0J,mBAAkBC,0BAAyBjK,SACpGA,GACiD,EAI3CmK,EAA8CA,KAAMb,EAAAA,EAAAA,YAAWO,E,6NCdrE,MAAMO,EAAsB,IAAI,IAAAnK,EAAA,CAAAC,KAAA,SAAAC,OAAA,oBAShC,MAAMkK,EAAY3P,IAAgF,IAA/E,SAAE4P,EAAQ,mBAAEC,EAAkB,UAAEC,EAAS,aAAEC,GAA8B/P,EACjG,MAAOgQ,EAAgBC,IAAqBC,EAAAA,EAAAA,WAAS,IAC/C,MAAEhN,IAAUC,EAAAA,EAAAA,MAEXgN,EAAcC,IAAmBF,EAAAA,EAAAA,WAAS,GAcjD,OAZAG,EAAAA,EAAAA,YAAU,KAERD,GAAgB,GAChB,MAAME,EAAM,IAAIjG,OAAOkG,MAIvB,OAHAD,EAAIE,OAAS,IAAMJ,GAAgB,GACnCE,EAAIG,QAAU,IAAML,GAAgB,GACpCE,EAAII,IAAMb,EACH,KACLS,EAAII,IAAM,EAAE,CACb,GACA,CAACb,KAGFxP,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CAAEE,MAAO+L,GAAa,OAAQ5L,OAAQ4L,GAAa,QAAQ,IAACxK,UACpEjF,EAAAA,EAAAA,GAAA,OAAKuD,IAAG2B,EAA0BD,cACRvE,IAAvB8O,GAAoCM,GACnC9P,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACHE,MAAO,OACP4M,gBAAiBzN,EAAM6B,OAAO6L,oBAC9BjM,QAAS,OACTkM,YAAa,IACbhM,eAAgB,SAChBD,WAAY,UACb,IAACU,UAEFjF,EAAAA,EAAAA,GAACyQ,EAAAA,EAAO,OAGVzQ,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACTC,WAAY,SACZC,eAAgB,SAChBd,MAAO+L,GAAa,OACpBe,YAAa,IACbE,SAAUhB,EACVlD,UAAWkD,EACXY,gBAAiBzN,EAAM6B,OAAO6L,oBAC9B,YAAa,CACX9M,OAAQ,YAEX,IAACwB,UAEFjF,EAAAA,EAAAA,GAACmO,EAAAA,EAAiB,CAACC,QAASuB,EAAgBtB,gBAAiBuB,EAAkB3K,UAC7EjF,EAAAA,EAAAA,GAACkQ,EAAAA,EAAK,CACJG,IAAKb,EACLb,QAAS,CAAE0B,IAAKd,GAChB5G,MAAO,CAAE+H,SAAUhB,GAAgB,OAAQlD,UAAWkD,GAAgB,iBAM5E,EAIGiB,EAAuB7F,IAU7B,IAV8B,eACnC8F,EAAc,UACdnB,EAAS,KACToB,EAAI,QACJC,GAMDhG,EACC,MAAM,MAAEjI,IAAUC,EAAAA,EAAAA,KAElB,YAA6BpC,IAAzBkQ,EAAeC,IAEf/K,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT+G,cAAe,SACf9G,WAAY,SACZC,eAAgB,SAChBuM,UAAW,SACXrN,MAAO+L,EACPa,gBAAiBzN,EAAM6B,OAAO6L,oBAC9BnM,QAASvB,EAAMmD,QAAQgL,GACvBR,YAAa,KACd,IAACvL,SAAA,EAEFjF,EAAAA,EAAAA,GAACiR,EAAAA,IAAS,KACVjR,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,qCAOrBnR,EAAAA,EAAAA,GAACsP,EAAS,CACRC,UAAU6B,EAAAA,EAAAA,IAAuBR,EAAeC,GAAMQ,SAAUP,GAChEtB,oBAAoB4B,EAAAA,EAAAA,IAAuBR,EAAeC,GAAMS,oBAAqBR,GACrFrB,UAAWA,GACX,EAEJ,IAAA8B,EAAA,CAAApM,KAAA,UAAAC,OAAA,sHAAAoM,EAAA,CAAArM,KAAA,SAAAC,OAAA,mBAAAqM,EAAA,CAAAtM,KAAA,UAAAC,OAAA,sBAEK,MAAMsM,EAAqBA,KAE9B5L,EAAAA,EAAAA,IAAA,OACEvC,IAAGgO,EAQDtM,SAAA,EAEFjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWiG,MAAK,CAACpO,IAAGiO,EAAqB5M,MAAM,YAAYgN,MAAO,EAAE3M,SAAC,2BAGtEjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWC,KAAI,CAACpI,IAAGkO,EAAwB7M,MAAM,YAAWK,SAAC,qE,6HC1IpE,MAAM4M,EAA8B,uEAqBpC,IAAAN,EAAA,CAAApM,KAAA,UAAAC,OAAA,oGAGO,MAAM0M,EAAenS,IASrB,IAADuF,EAAA,IATuB,MAC3BN,EAAK,OACLmN,EAAM,cACNC,KACGhQ,GAKJrC,EACC,MAAOsS,EAAYC,IAAiBrC,EAAAA,EAAAA,eAA6BnP,GAE3DyR,GAAyBC,EAAAA,EAAAA,UAAQ,IAIjCJ,GACKK,EAAAA,EAAAA,UAASL,EAAe,KAE1B,QACN,CAACA,IAEJ,OACElM,EAAAA,EAAAA,IAAA,SACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHE,MAAO,GACPG,OAAQ,GACRC,aAAc,EACdwO,WAAY,EAEZjO,OAAQ,cAAa0N,EAAS,cAAgB,mBAE9C,CAACF,GAA8B,CAC7BtG,aAAc,GAEhB9G,WAAYsN,EAzDiB,kSAyDuBrR,EACpD+C,OAAQuO,EAAgB,UAAY,UACpCzF,SAAU,WACV,UAAW,CACTgG,QAASP,EAAgB,GAAM,IAElC,IACDrJ,MAAO,CAAE2H,gBAAoC,QAArBpL,EAAY,OAAV+M,QAAU,IAAVA,EAAAA,EAAcrN,SAAK,IAAAM,EAAAA,EAAI,kBAC7ClD,EAAKiD,SAAA,EAETjF,EAAAA,EAAAA,GAAA,QACEuD,IAAG,CACDiP,EAAAA,EAAe,qBAIfvN,SAEDL,IAEFoN,IACChS,EAAAA,EAAAA,GAAA,SACEyS,SAAUV,EACVpP,KAAK,QACL4C,MAAiB,OAAV0M,QAAU,IAAVA,EAAAA,EAAcrN,EACrB8N,SAAU5H,IAAiB,IAAhB,OAAE6H,GAAQ7H,EACnBoH,EAAcS,EAAOpN,OACrB4M,EAAuBQ,EAAOpN,MAAM,EAEtCqN,KAAMC,EAAAA,EACNtP,IAAGgO,MAYD,C,gXCzGZ,MAIauB,EAA4B,mBAMnCC,EAAwBA,CAACC,EAA2BC,KAAmB,IAADC,EAAAC,EAC1E,OAAiC,QAAjCD,EAAOF,EAAUI,wBAAgB,IAAAF,GAAkC,QAAlCC,EAA1BD,EAA4B/H,MAAKxL,IAAA,IAAC,IAAEkL,GAAKlL,EAAA,OAAKkL,IAAQoI,CAAK,WAAC,IAAAE,OAAlC,EAA1BA,EAA8D5N,KAAK,EAG/D8N,EAAoCC,GACxCA,EAAclS,QAP2B,IAUrCmS,EAAqBP,GAChCD,EAAsBC,EAnBY,oBAqBvBQ,EAA2BR,GACtCD,EAAsBC,EArBkB,gBAuB7BS,EAAsBT,IACjC,MAAMU,EAASX,EAAsBC,EAvBH,sBAwBlC,KAAIW,EAAAA,EAAAA,OAAMD,GAGV,IACE,OAAO7S,KAAKC,UAAUD,KAAK+F,MAAM8M,GACnC,CAAE,MAAO9L,GACP,OAAO8L,CACT,GAGWE,EAAuBZ,IAClC,MAAMa,EAAUd,EAAsBC,EAlCH,uBAmCnC,KAAIW,EAAAA,EAAAA,OAAME,GAGV,IACE,OAAOhT,KAAKC,UAAUD,KAAK+F,MAAMiN,GACnC,CAAE,MAAOjM,GACP,OAAOiM,CACT,GAGWC,EAAmBA,CAACd,EAA2Be,KAAqB,IAADC,EAC3CC,EAAAC,EAAnC,OAAIC,MAAMC,QAAQpB,EAAUxN,MACL,QAArByO,EAAOjB,EAAUxN,YAAI,IAAAyO,GAAoC,QAApCC,EAAdD,EAAgB9I,MAAKjG,IAAA,IAAC,IAAE2F,GAAK3F,EAAA,OAAK2F,IAAQkJ,CAAO,WAAC,IAAAG,OAApC,EAAdA,EAAoD3O,MAGxC,QAArByO,EAAOhB,EAAUxN,YAAI,IAAAwO,OAAA,EAAdA,EAAiBD,EAAQ,EAGrBM,EAAuBrB,GAC3Bc,EAAiBd,EAAWF,IAA8BE,EAAUsB,WAGhEC,EAAqC,CAAC,gBAItCC,EAAiC,SAEvC,IAAKC,EAAgC,SAAhCA,GAAgC,OAAhCA,EAAgC,uBAAhCA,EAAgC,sBAAhCA,EAAgC,2BAAhCA,EAAgC,gBAAhCA,EAAgC,kBAAhCA,EAAgC,kBAAhCA,EAAgC,2BAAhCA,EAAgC,gBAAhCA,EAAgC,kBAAhCA,EAAgC,YAAhCA,EAAgC,gBAAhCA,CAAgC,MAcrC,MAAMC,EAAqG,CAChH,CAACD,EAAiCE,YAAYC,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SAC1D0I,eAAe,eAGjB,CAACsD,EAAiCI,YAAYD,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SAC1D0I,eAAe,eAGjB,CAACsD,EAAiCK,cAAcF,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SAC5D0I,eAAe,iBAGjB,CAACsD,EAAiCM,SAASH,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACvD0I,eAAe,WAGjB,CAACsD,EAAiCf,SAASkB,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACvD0I,eAAe,YAGjB,CAACsD,EAAiCZ,UAAUe,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACxD0I,eAAe,aAGjB,CAACsD,EAAiCO,UAAUJ,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACxD0I,eAAe,aAGjB,CAACsD,EAAiCQ,cAAcL,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SAC5D0I,eAAe,WAGjB,CAACsD,EAAiC5N,SAAS+N,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACvD0I,eAAe,WAGjB,CAACsD,EAAiCS,UAAUN,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACxD0I,eAAe,mBAGjB,CAACsD,EAAiCjP,OAAOoP,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACrD0I,eAAe,UAKNgE,EAAmC,CAC9CC,MAAO,KACPC,aAAaT,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACzB0I,eAAe,gBAGjBmE,IAAIV,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SAChB0I,eAAe,OAGjBoE,OAAOX,EAAAA,EAAAA,IAAc,CAAAnM,GAAA,SACnB0I,eAAe,U,wBC9IZ,SAASqE,EACdC,EACAlQ,EACAmQ,GAEA,QAAchV,IAAV6E,GAAiC,OAAVA,GAAmC,kBAAVA,EAClD,OAAOmQ,EAET,IAAK,MAAM7G,KAAK4G,EACd,GAAIA,EAAQ5G,KAAOtJ,EAAO,OAAOkQ,EAAQ5G,GAE3C,OAAO6G,CACT,C,oKCyBA,MA9BA,SAAgCzT,EAA6B0T,GAC3D,MAAOC,EAAaC,IAAkBhG,EAAAA,EAAAA,UAAwB,MA0B9D,OAxBAG,EAAAA,EAAAA,YAAU,KACR,GAAI/N,EAAI6T,SAAWH,EAAS,CAC1B,MAAMI,EAAeA,KACnB,IAAK9T,EAAI6T,QACP,OAEF,MAAME,EAAe/T,EAAI6T,QAAQG,YAC3BC,EAAa1O,OAAO2O,KAAKR,GAC5BS,QAAQvL,GAAQ8K,EAAQ9K,IAAQmL,IAChCK,MAAK,CAACC,EAAGC,IAAMZ,EAAQW,GAAKX,EAAQY,KAAI,GAE3CV,EAAeK,EAAW,EAG5BH,IAEA,MAAMS,EAAiB,IAAIC,eAAeV,GAG1C,OAFAS,EAAeE,QAAQzU,EAAI6T,SAEpB,IAAMU,EAAeG,YAC9B,CACgB,GACf,CAAC1U,EAAK0T,IAEFC,CACT,E,WCnBA,MAAMgB,EAAiB,CACrBC,GAAI,IACJC,GAAI,KAKOC,EAAiBpX,IAcvB,IAdwB,UAC7BqX,EAAS,kBACTC,EAAiB,SACjBhS,EAAQ,YACRiS,GAAc,EAAI,YAClBC,EAAc,KAAI,mBAClBC,GAQDzX,EACC,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,KACZuU,GAAeC,EAAAA,EAAAA,QAAuB,MAEtCC,EAAuG,UAArFC,EAAuBH,EAAc,CAAEI,MAAO5U,EAAM6U,WAAWC,YAAYb,KAG7Fc,EAA4D,kBAAvBR,EAErCS,EAAoBjB,EAAeO,GACnCW,EAAoBD,EA3BD,GA6BnBE,EAAyBR,EAC3BK,EACE,CAAElU,MAAO,QACT,CAAEsU,aAAc,aAAanV,EAAM6B,OAAOL,SAAUX,MAAO,QAC7DkU,EACA,CACElU,MAAOoU,GAET,CACEG,cAAepV,EAAMmD,QAAQ6Q,GAC7BnT,MAAOoU,GAGb,OACEhS,EAAAA,EAAAA,IAAA,OACE,cAAY,4BACZ7D,IAAKoV,EACL9T,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT+G,cAAekM,EAAmBK,EAAqC,SAAW,iBAAoB,MACtG7R,IAAKlD,EAAMmD,QAAQ8Q,IACpB,IAAC7R,SAAA,EAEFjF,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT4T,SAAU,EACV7M,cAAe,SACftF,IAAKlD,EAAMmD,QAAQgL,GACnBtN,MAAO6T,EAAkB,OAAS,eAAeM,QAClD,IAAC5S,SAED+R,GAAYhX,EAAAA,EAAAA,GAACmY,EAAAA,IAAe,IAAMlT,KAErCjF,EAAAA,EAAAA,GAAA,OACE2I,MAAO,CACLrE,QAAS,UACL4S,GAAe,CAAE5L,WAAYzI,EAAMmD,QAAQgL,KAC/C/L,UAEFa,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT+G,cAAe,SACftF,IAAKlD,EAAMmD,QAAQ8Q,MAChBiB,GACJ,IAAC9S,SAAA,CAED+R,IAAahX,EAAAA,EAAAA,GAACmY,EAAAA,IAAe,KAC5BnB,IAAahX,EAAAA,EAAAA,GAACoY,EAAc,CAACnB,kBAAmBA,WAGlD,EAIJmB,EAAiBlT,IAAsE,IAArE,kBAAE+R,GAA6D/R,EACrF,OACElF,EAAAA,EAAAA,GAAA,OAAAiF,SACGgS,EACEb,QAAQiC,GAAwB,OAAZA,IACpBjC,QAAQiC,GAAiC,QAAd,OAAPA,QAAO,IAAPA,OAAO,EAAPA,EAASC,WAC7BC,KAAI,CAAAzN,EAAyC0N,KAAK,IAA7C,MAAEpQ,EAAK,eAAEqQ,EAAc,QAAEH,EAAO,GAAE7P,GAAIqC,EAAA,OAC1C9K,EAAAA,EAAAA,GAAC0Y,EAAgB,CAACtQ,MAAOA,EAAOqQ,eAAgBA,EAAgBH,QAASA,EAAkBE,MAAOA,GAAX/P,EAAoB,KAE3G,EAIGkQ,EAAwBpH,IAA4C,IAA3C,SAAEtM,GAAmCsM,EACzE,MAAM,MAAE1O,IAAUC,EAAAA,EAAAA,KAClB,OACE9C,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWiG,MAAK,CACfC,MAAO,EACPjJ,MAAO,CACLiQ,WAAY,SACZnN,YAAa5I,EAAMmD,QAAQ8Q,GAC3BxL,UAAW,GACXrG,SAEDA,GACgB,EAIjByT,EAAmBlH,IAOlB,IAPmB,MACxBpJ,EAAK,QACLkQ,EAAO,MACPE,EAAK,eACLC,GAAiB,GAGlBjH,EACC,MAAM,MAAE3O,IAAUC,EAAAA,EAAAA,KAEZ+V,EAAiBJ,GACrBzY,EAAAA,EAAAA,GAAC8Y,EAAAA,IAAiB,CAChBC,OACE/Y,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,4BAKnB/I,GACFpI,EAAAA,EAAAA,GAAC2Y,EAAqB,CAAA1T,SAAEmD,IACtB,KAEE4Q,EAAgB,CAAE5U,QAAS,GAAGvB,EAAMmD,QAAQgL,UAAUnO,EAAMmD,QAAQgL,UAE1E,OACElL,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,IACAwV,KACW,IAAVR,EAAc,CAAC,EAAI,CAAES,UAAW,aAAapW,EAAM6B,OAAOL,WAC/D,IAACY,SAAA,CAED4T,EACAP,IACG,EAER,IAAAY,EAAA,CAAA/T,KAAA,UAAAC,OAAA,2CAEK,MAAM+T,EAAmB1H,IAQzB,IAR0B,SAC/B2H,EAAQ,MACR7T,EAAK,SACLmL,GAKDe,EACC,MAAM,MAAE5O,IAAUC,EAAAA,EAAAA,KAClB,OACEgD,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACTC,WAAY,SACZ,eAAgB,CACdgH,aAAc1I,EAAMmD,QAAQC,IAE9ByK,SAAkB,OAARA,QAAQ,IAARA,EAAAA,EAzKQ,IA0KlB2I,UAAW,aACXpV,WAAYpB,EAAMqB,WAAWoV,cAC9B,IAACrU,SAAA,EAEFjF,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACHoB,MAAO/B,EAAM6B,OAAOI,cACpByU,KAAM,GACNC,UAAW,SACZ,IAACvU,SAEDmU,KAEHpZ,EAAAA,EAAAA,GAAA,OACEuD,IAAG2V,EAIDjU,SAEDM,MAEC,EAIGkU,EAAWA,KAEpBzZ,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWC,KAAI,CAAC/G,MAAM,YAAWK,UAChCjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SAAC0I,eAAe,U,oJC3NO,IAAAjM,EAAA,CAAAC,KAAA,UAAAC,OAAA,cAOvC,MAAMsU,EAAmC/Z,IAGW,IAHV,OAC/Cga,EAAM,OACNvD,GACsCzW,EACtC,MAQMia,EAAiBD,EAAOvD,QAAO,CAACyD,EAAqCC,KACzEC,OATiB5U,EASP0U,EAAI1U,KATkBxC,EASZkX,EAAIlX,KAPX,KAAXyT,IACI,OAAJjR,QAAI,IAAJA,OAAI,EAAJA,EAAM6U,cAAcC,SAAS7D,EAAO4D,kBAChC,OAAJrX,QAAI,IAAJA,OAAI,EAAJA,EAAMqX,cAAcC,SAAS7D,EAAO4D,gBAJtBD,IAAC5U,EAAexC,CAUlC,IAeA,OACEmD,EAAAA,EAAAA,IAACoU,EAAAA,IAAK,CAACC,YAAU,EAAC5W,IAAG2B,EAAoBD,SAAA,EACvCa,EAAAA,EAAAA,IAACsU,EAAAA,IAAQ,CAACC,UAAQ,EAAApV,SAAA,EAChBjF,EAAAA,EAAAA,GAACsa,EAAAA,IAAW,CAAC7X,YAAY,gIAA+HwC,UAd1JjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,YAgBfnR,EAAAA,EAAAA,GAACsa,EAAAA,IAAW,CAAC7X,YAAY,gIAA+HwC,UATrJjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SAAC0I,eAAe,eAatCnR,EAAAA,EAAAA,GAAA,OAAKua,QAAU3S,GAAMA,EAAE4S,kBAAkBvV,SACZ,IAA1B2U,EAAexY,QACdpB,EAAAA,EAAAA,GAACoa,EAAAA,IAAQ,CAAAnV,UACPjF,EAAAA,EAAAA,GAACya,EAAAA,IAAS,CAAAxV,UACRjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,sCAMrByI,EAAerB,KAAI,CAACsB,EAAqCa,KACvD5U,EAAAA,EAAAA,IAACsU,EAAAA,IAAQ,CAAAnV,SAAA,EACPjF,EAAAA,EAAAA,GAACya,EAAAA,IAAS,CAAAxV,SAAE4U,EAAI1U,QAChBnF,EAAAA,EAAAA,GAACya,EAAAA,IAAS,CAAAxV,SAAE4U,EAAIlX,SAFH,kBAAkB+X,WAOjC,EClDkC,IAAAxV,EAAA,CAAAC,KAAA,SAAAC,OAAA,mDAAA0F,EAAA,CAAA3F,KAAA,UAAAC,OAAA,kGAAAmM,EAAA,CAAApM,KAAA,SAAAC,OAAA,mEAAAoM,EAAA,CAAArM,KAAA,UAAAC,OAAA,cAAAqM,EAAA,CAAAtM,KAAA,SAAAC,OAAA,gBAAA8T,EAAA,CAAA/T,KAAA,SAAAC,OAAA,4FAAAuV,EAAA,CAAAxV,KAAA,SAAAC,OAAA,qBAAAwV,EAAA,CAAAzV,KAAA,UAAAC,OAAA,iEAAAyV,EAAA,CAAA1V,KAAA,UAAAC,OAAA,iEAMvC,MAAM0V,EAA8Bnb,IAAkE,IAAjE,gBAAE6G,GAA4C7G,EACxF,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,MACZ,QAAE2D,GAAYD,GACb4P,EAAQ2E,IAAalL,EAAAA,EAAAA,UAAS,IAErC,GAAuB,OAAnBpJ,EAAQkT,QAAsC,KAAnBlT,EAAQkT,OACrC,OACE3Z,EAAAA,EAAAA,GAAA,OACEuD,IAAG2B,EAIDD,UAEFjF,EAAAA,EAAAA,GAAA,OACEuD,IAAGuH,EAMD7F,UAEFjF,EAAAA,EAAAA,GAACgb,EAAAA,IAAM,CAAC5S,OAAOpI,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CAAEoB,MAAO/B,EAAM6B,OAAOuW,SAAS,IAAChW,SAAC,8BAKlE,IACE,MAAM0U,EAAS9Y,KAAK+F,MAAMH,EAAQkT,QAClC,MAAI,mBAAoBA,GAGpB7T,EAAAA,EAAAA,IAAA,OACEvC,IAAGgO,EAKDtM,SAAA,EAEFjF,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACH8H,UAAWzI,EAAMmD,QAAQ6Q,GACzBqE,KAAM,CAAExX,MAAO,SAChB,IAACuB,UAEFjF,EAAAA,EAAAA,GAACmb,EAAAA,IAAgB,CACf1Y,YAAY,2HACZ8C,MAAO6Q,EACPgF,YAAY,gBACZ1I,SAAW9K,GAAMmT,EAAUnT,EAAE+K,OAAOpN,OACpC8V,QAASA,KACPN,EAAU,GAAG,EAEfxX,IAAGiO,EACH8J,eAAgB,CAAE3S,MAAO,CAAEjF,MAAO,cAGtC1D,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACH8H,UAAWzI,EAAMmD,QAAQ6Q,GACzBpK,SAAU,UACX,IAACxH,UAEFjF,EAAAA,EAAAA,GAAC0Z,EAAgC,CAACC,OAAQA,EAAO4B,eAAgBnF,OAAQA,SAItE,sBAAuBuD,GAG9B3Z,EAAAA,EAAAA,GAAA,OAAKuD,IAAGkO,EAAsBxM,UAC5Ba,EAAAA,EAAAA,IAAA,OACEvC,IAAG2V,EAMDjU,SAAA,EAEFjF,EAAAA,EAAAA,GAACwL,EAAAA,IAAS,CAACjI,KAAGC,EAAAA,EAAAA,IAAE,CAAEgY,SAAU,OAAQ5W,MAAO/B,EAAM6B,OAAOuW,SAAS,OACjEjb,EAAAA,EAAAA,GAACgb,EAAAA,IAAM,CAAC5S,OAAOpI,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CAAEoB,MAAO/B,EAAM6B,OAAOuW,SAAS,IAAChW,SAAC,wBAE1DjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWC,KAAI,CAAC/G,MAAO/B,EAAM6B,OAAOuW,QAAS1X,IAAGoX,EAA0B1V,UACzEjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,iHAUvBnR,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CAAEuI,WAAYlJ,EAAMmD,QAAQ8Q,GAAIxL,UAAWzI,EAAMmD,QAAQgL,GAAItN,MAAO,QAAQ,IAACuB,UACrFa,EAAAA,EAAAA,IAAA,OAAKvC,IAAGqX,EAA6E3V,SAAA,EACnFjF,EAAAA,EAAAA,GAACgb,EAAAA,IAAM,CAAC5S,OAAOpI,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CAAEoB,MAAO/B,EAAM6B,OAAOuW,SAAS,IAAChW,SAAC,kCAE1Da,EAAAA,EAAAA,IAAC4F,EAAAA,EAAWC,KAAI,CAAC/G,MAAO/B,EAAM6B,OAAOuW,QAAQhW,SAAA,EAC3CjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,sBAGhBtQ,KAAKC,UAAU6Y,UAM5B,CAAE,MACA,OACE3Z,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CAAEuI,WAAYlJ,EAAMmD,QAAQ8Q,GAAIxL,UAAWzI,EAAMmD,QAAQgL,GAAItN,MAAO,QAAQ,IAACuB,UACrFjF,EAAAA,EAAAA,GAAA,OAAKuD,IAAGsX,EAA6E5V,UACnFjF,EAAAA,EAAAA,GAACgb,EAAAA,IAAM,CAAC5S,OAAOpI,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CAAEoB,MAAO/B,EAAM6B,OAAOuW,SAAS,IAAChW,SAAC,6BAIlE,G,qCClIK,MAAMwW,EAA4B9b,IAAqD,IAApD,gBAAE6G,EAAe,QAAEkV,GAA2B/b,EACtF,MAAM,QAAE8G,GAAYD,EACpB,GAAIC,EAAQ7G,aAAe8G,EAAAA,GAAmBC,MAAQF,EAAQ7G,aAAe8G,EAAAA,GAAmBM,aAAc,CAC5G,MAAM/F,GAAMsF,EAAAA,EAAAA,GAAoBC,GAChC,GAAIvF,EACF,OACEjB,EAAAA,EAAAA,GAACmD,EAAAA,EAAM,CACLR,KAAK,UACLF,YAAY,2HACZL,MAAMpC,EAAAA,EAAAA,GAAC2b,EAAAA,GAAa,IACpBC,KAAM3a,EACN0R,OAAO,SAAQ1N,UAEfjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,kBAMzB,CACA,GAAI1K,EAAQ7G,aAAe8G,EAAAA,GAAmBI,GAAI,CAChD,MAAM7F,GAAMsF,EAAAA,EAAAA,GAAoBC,GAChC,GAAIvF,EACF,OACEjB,EAAAA,EAAAA,GAAC6b,EAAAA,EAAU,CACTpZ,YAAY,2HACZL,MAAMpC,EAAAA,EAAAA,GAAC8b,EAAAA,IAAQ,IACfC,SAAU9a,EAAIgE,UAEdjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,8BAMzB,CACA,OAAI1K,EAAQ7G,aAAe8G,EAAAA,GAAmBsV,UAE1Chc,EAAAA,EAAAA,GAACmD,EAAAA,EAAM,CACLV,YAAY,2HACZL,MAAMpC,EAAAA,EAAAA,GAAC2b,EAAAA,GAAa,IAAI1W,UAExBjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,8BAMhB,IAAI,E,gDCtDN,MAAM8K,EAAkCtc,IAAgE,IAA/D,gBAAE6G,GAAuD7G,EACvG,MAAM,QAAE8G,GAAYD,EAEd5G,EAAa6G,EAAQ7G,WA8BrBsc,EA3BAtc,IAAe8G,EAAAA,GAAmBC,MAAQ/G,IAAe8G,EAAAA,GAAmBsV,UAE5Ehc,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,SAKjBvR,IAAe8G,EAAAA,GAAmBI,IAElC9G,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,OAKjBvR,IAAe8G,EAAAA,GAAmBM,cAElChH,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,iBAKd,KAKT,OAAI+K,GAEAlc,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWyQ,KAAI,CAAAlX,UACdjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,2BAEfiL,OAAQ,CAAEF,iBAMX,IAAI,ECtDwD,IAAAhX,EAAA,CAAAC,KAAA,SAAAC,OAAA,gCAAA0F,EAAA,CAAA3F,KAAA,UAAAC,OAAA,0CAAAmM,EAAA,CAAApM,KAAA,SAAAC,OAAA,6DAM9D,MAAMiX,EAAiC1c,IAA4D,IAA3D,gBAAE6G,GAAmD7G,EAClG,MAAM,QAAE8G,GAAYD,GACd,MAAE3D,IAAUC,EAAAA,EAAAA,KAEZlD,EAAa6G,EAAQ7G,WAE3B,GACEA,IAAe8G,EAAAA,GAAmBC,MAClC/G,IAAe8G,EAAAA,GAAmBsV,UAClCpc,IAAe8G,EAAAA,GAAmBM,aAClC,CACA,MAAM/F,GAAMsF,EAAAA,EAAAA,GAAoBC,GAChC,GAAIvF,EACF,OACE6E,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHoV,WAAY,SACZtU,QAAS,OACTkX,SAAU3Y,EAAMqB,WAAWoY,WAC3B1X,MAAO/B,EAAM6B,OAAOI,cACpByX,UAAW1Z,EAAMmD,QAAQC,IAC1B,IACDmC,MAAOnH,EAAIgE,SAAA,CACZ,OACM,KACLjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACd/Z,YAAY,8HACZga,cAAY,EACZb,KAAM3a,EACNsC,IAAG2B,EAA0CD,UAE7CjF,EAAAA,EAAAA,GAAA,QAAMuD,IAAGuH,EAAmD7F,SAAEhE,QAKxE,CACA,GAAIrB,IAAe8G,EAAAA,GAAmBI,GACpC,IACE,MAAM,IAAEC,GAAQlG,KAAK+F,MAAMH,EAAQI,QACnC,GAAIE,EACF,OACEjB,EAAAA,EAAAA,IAAC4F,EAAAA,EAAWyQ,KAAI,CACd/T,MAAOrB,EACPxD,IAAGgO,EAIDtM,SAAA,CACH,WACU8B,IAIjB,CAAE,MACA,OAAO,IACT,CAEF,OAAO,IAAI,EC9DA2V,EAA8B/c,IAAmE,IAAlE,gBAAE6G,GAA0D7G,EACtG,MAAM,QAAE8G,GAAYD,EACpB,OACExG,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWyQ,KAAI,CAAAlX,UACdjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,mBAEfiL,OAAQ,CAAE/V,QAAQrG,EAAAA,EAAAA,GAAA,QAAAiF,SAAOwB,EAAQJ,aAEnB,E,eCuCpB,IAAAnB,EAAA,CAAAC,KAAA,UAAAC,OAAA,+CAAA0F,EAAA,CAAA3F,KAAA,UAAAC,OAAA,0CAAAmM,EAAA,CAAApM,KAAA,UAAAC,OAAA,8DAAAoM,EAAA,CAAArM,KAAA,SAAAC,OAAA,UAAAqM,EAAA,CAAAtM,KAAA,SAAAC,OAAA,sDAAA8T,EAAA,CAAA/T,KAAA,UAAAC,OAAA,mCAEF,MAAMuX,EAAkChd,IAKO,IAADid,EAAA,IALL,OACvCC,EAAM,UACNC,EAAS,uBACTC,EAAsB,0BACtBC,GAC0Brd,EAC1B,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,MACZ,gBAAE0D,EAAe,QAAEyW,GAAYF,EAC/B7R,EAAa6R,EACA,OAAfvW,QAAe,IAAfA,GAAqB,QAANoW,EAAfpW,EAAiBhB,YAAI,IAAAoX,OAAN,EAAfA,EAAuBzR,MAAM+R,GAAQA,EAAIrS,MAAQO,EAAAA,UACjD1K,EACEyc,EACJ3W,EAAgBC,QAAQ2W,SAA+C,SAApC5W,EAAgBC,QAAQ2W,QACvD5W,EAAgBC,QAAQ2W,aACxB1c,EAEA2c,GAAcC,EAAAA,EAAAA,OACd,aAAEC,EAAe,GAAE,KAAE/X,EAAO,CAAC,GAAMyX,EAEzC,OACEjd,EAAAA,EAAAA,GAACwd,EAAAA,GAAOC,KAAI,CACVC,KAAMb,EACNc,aAAeD,IACRA,GACHZ,GAAU,EACZ,EACA7X,UAEFjF,EAAAA,EAAAA,GAACwd,EAAAA,GAAOI,QAAO,CACbnb,YAAY,2HACZ2F,OACEtC,EAAAA,EAAAA,IAAA,OAAKvC,IAAG2B,EAA4DD,SAAA,EAClEjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWiG,MAAK,CAACC,MAAO,EAAGrO,KAAGC,EAAAA,EAAAA,IAAE,CAAEiI,YAAa5I,EAAMmD,QAAQ6Q,GAAItL,aAAc,GAAG,IAACtG,UAClFjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,yBAInBrL,EAAAA,EAAAA,IAAC0W,EAAAA,GAAI,CAACqB,GAAIC,EAAAA,EAAOC,gBAAgBR,EAAcN,EAAQnM,SAAUvN,IAAK6B,EAAO4Y,QAAQ/Y,SAAA,EACnFjF,EAAAA,EAAAA,GAAC8R,EAAAA,EAAY,CAAClN,MAAOyY,EAAYJ,EAAQnM,YACzC9Q,EAAAA,EAAAA,GAAA,QAAMuD,IAAK6B,EAAO4P,QAAQ/P,SAAEgY,EAAQjI,gBAI1CtR,MAnDa,QAoDbua,QAAQje,EAAAA,EAAAA,GAACke,EAAAA,EAAM,CAACtS,KAAK,OAAQ3G,UAE7Ba,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT2U,UAAW,aAAapW,EAAM6B,OAAOL,SACrCR,OAAQ,OACRkI,YAAalJ,EAAMmD,QAAQ6Q,IAC5B,IAAC5R,SAAA,EAGFa,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT+G,cAAe,SACf3H,MAAO,QACPya,YAAa,aAAatb,EAAM6B,OAAOL,SACvCR,OAAQ,QACT,IAACoB,SAAA,EAEFa,EAAAA,EAAAA,IAAC4F,EAAAA,EAAWC,KAAI,CACd/G,MAAM,YACNrB,KAAGC,EAAAA,EAAAA,IAAE,CACH+H,aAAc1I,EAAMmD,QAAQ6Q,GAC5BvL,UAAWzI,EAAMmD,QAAQ6Q,GACzBuH,YAAavb,EAAMmD,QAAQ6Q,IAC5B,IAAC5R,SAAA,CAEDgY,EAAQoB,SAASjd,OAAQ,KAC1BpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,sBAInBnR,EAAAA,EAAAA,GAAA,OACEuD,IAAGuH,EAKHyP,QAAU3S,GAAMA,EAAE4S,kBAAkBvV,UAEpCjF,EAAAA,EAAAA,GAAA,OACEuD,IAAGgO,EAKDtM,SAEDgY,EAAQoB,SAAS9F,KAAK9R,IACrBzG,SAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACd/Z,YAAY,qCACZ,aAAY,GAAGgE,EAAQA,QAAQtB,SAASsB,EAAQA,QAAQJ,UAExD9C,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACTsU,WAAY,SACZ0F,eAAgB,OAChB7a,OAAQ,UACR4H,cAAe,SACf7G,eAAgB,SAChBD,WAAY,aACZ+L,iBAhHIiO,EAgH8B9X,EAhHA+X,EAgHShY,EA/GxD+X,EAAS9X,QAAQJ,SAAWmY,EAAS/X,QAAQJ,QAAUkY,EAAS9X,QAAQtB,OAASqZ,EAAS/X,QAAQtB,KAgHjFtC,EAAM6B,OAAO+Z,8BACb,eACJxG,cAAepV,EAAMmD,QAAQ6Q,GAC7B6H,WAAY7b,EAAMmD,QAAQ6Q,GAC1BuH,YAAavb,EAAMmD,QAAQ6Q,GAC3BxS,OAAQ,EACR4U,UAAW,aAAapW,EAAM6B,OAAOL,SACrC,UAAW,CACTiM,gBAAiBzN,EAAM6B,OAAOia,gCAEjC,IACDxc,QAASA,KACP6a,EAA0B,CAAExW,gBAAiBC,EAASwW,QAASA,IAC/DH,GAAU,EAAK,EACf7X,UAEFjF,EAAAA,EAAAA,GAAC+K,EAAAA,EAAgC,CAACvE,gBAAiBC,EAASwE,mBAAmB,KA1B1E,GAAGxE,EAAQA,QAAQtB,QAAQsB,EAAQA,QAAQJ,UAvG3CuY,IAACL,EAA8BC,CAmIvC,YAKP1Y,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHiJ,SAAU,SACV2R,YAAavb,EAAMmD,QAAQgL,GAC3B0N,WAAY7b,EAAMmD,QAAQgL,GAC1B1M,QAAS,OACT+G,cAAe,SACf3H,MAAO,QACR,IAACuB,SAAA,EAGFa,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACTyB,IAAKlD,EAAMmD,QAAQ6Q,IACpB,IAAC5R,SAAA,EAEFa,EAAAA,EAAAA,IAAA,OAAKvC,IAAGiO,EAAgBvM,SAAA,EACtBjF,EAAAA,EAAAA,GAACgb,EAAAA,IAAM,CACL5S,OACEtC,EAAAA,EAAAA,IAAA,OAAKvC,IAAGkO,EAAkExM,SAAA,EACxEjF,EAAAA,EAAAA,GAACwL,EAAAA,IAAS,CAACjI,KAAGC,EAAAA,EAAAA,IAAE,CAAEiI,YAAa5I,EAAMmD,QAAQC,IAAI,OACjDjG,EAAAA,EAAAA,GAAC6e,EAAAA,IAAa,CAACzW,MAAO5B,EAAgBC,QAAQtB,KAAKF,UACjDjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWiG,MAAK,CAACmN,UAAQ,EAAClN,MAAO,EAAGrO,IAAG2V,EAAqCjU,SAC1EuB,EAAgBC,QAAQtB,SAG5B+F,IACClL,EAAAA,EAAAA,GAAC6L,EAAAA,IAAG,CACFpJ,YAAY,4HACZc,KAAGC,EAAAA,EAAAA,IAAE,CACHsI,cAAe,aACfC,WAAYlJ,EAAMmD,QAAQC,GAC1BwF,YAAa5I,EAAMmD,QAAQC,IAC5B,IAAChB,SAEDiG,EAAW3F,cAMtBvF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWiG,MAAK,CACfC,MAAO,EACPhN,MAAM,YACNrB,KAAGC,EAAAA,EAAAA,IAAE,CAAE+H,aAAc1I,EAAMmD,QAAQC,GAAIqF,UAAWzI,EAAMmD,QAAQC,IAAI,IACpEmC,MAAO+U,EAAYlY,SAElBuB,EAAgBC,QAAQ2W,SAA+C,SAApC5W,EAAgBC,QAAQ2W,QAC1D5W,EAAgBC,QAAQ2W,QAAQhc,OA3LzB,GA4LL,GAAGoF,EAAgBC,QAAQ2W,QAAQ2B,UAAU,EA5LxC,UA8LLvY,EAAgBC,QAAQ2W,SAG1Bpd,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,+BAMvBnR,EAAAA,EAAAA,GAACyb,EAAyB,CAACjV,gBAAiBA,EAAiBkV,QAASlW,QAExEM,EAAAA,EAAAA,IAAA,OAAKvC,KAAGC,EAAAA,EAAAA,IAAE,CAAE8O,WAAY,EAAGhO,QAAS,OAAQ+G,cAAe,SAAUtF,IAAKlD,EAAMmD,QAAQC,IAAI,IAAChB,SAAA,EAC3FjF,EAAAA,EAAAA,GAAC0c,EAA2B,CAAClW,gBAAiBA,KAC9CxG,EAAAA,EAAAA,GAACic,EAA+B,CAACzV,gBAAiBA,KAClDxG,EAAAA,EAAAA,GAACqc,EAA8B,CAAC7V,gBAAiBA,QAGnDxG,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACH8H,UAAWzI,EAAMmD,QAAQ6Q,GACzBtL,aAAc1I,EAAMmD,QAAQC,GAC5BgT,UAAW,aAAapW,EAAM6B,OAAOL,SACrCkO,QAAS,IACV,OAEHvS,EAAAA,EAAAA,GAAC8a,EAA2B,CAACtU,gBAAiBA,aAIxC,EAMLwY,EAA8Bjd,EAAAA,KAAW4a,GAEhDvX,EAAS,CACb4Y,QAAS,CACPvR,SAAU,SACVnI,QAAS,OACTyB,IAAK,EACLxB,WAAY,UAEdyQ,QAAS,CACPvI,SAAU,SACVwS,aAAc,WACdzD,SAAU,Q,oJCpRd,MAAM0D,EAA2B,sCAC3BC,EAAoC,oCAEpCC,EAAgCC,GAE7B,cADgBA,EAAS9G,KAAK+G,GAAe,IAAIA,OACpB7V,KAAK,QAiD9B8V,EAAsB5f,IAe5B,IAf6B,cAClC6f,EAAa,QACbC,EAAO,OACPrJ,EAAS,GAAE,QACXtF,EAAO,cACP4O,GAUD/f,EACC,MAAOggB,EAAQC,IAAa/P,EAAAA,EAAAA,UAAsC,KAC3DgQ,EAASC,IAAcjQ,EAAAA,EAAAA,WAAkB,IACzCkQ,EAAOC,IAAYnQ,EAAAA,EAAAA,eAA4BnP,GAGhDuf,GAAgB7N,EAAAA,EAAAA,UAAQ,KAC5B,MAAM8N,GAAqBC,EAAAA,EAAAA,OAAMV,GACjC,OAAIS,GAAsB3L,EAAAA,GAAmC0F,SAASiG,EAAmBzX,IAChF,GAAGyX,EAAmBzX,MAAMyX,EAAmBE,KAAO,OAAS,QAEjE,mBAAmB,GACzB,CAACX,IAEEY,GAAejO,EAAAA,EAAAA,UAAQ,IACtBtB,GAAY4O,GAIbY,EAAAA,EAAAA,OAAuCZ,EACrCtJ,EACK,GAAGA,SAAc+I,MAAsCO,KAEzD,GAAGP,MAAsCO,KAG9CtJ,EACK,GAAGA,SAAc8I,MAA6BpO,KAGhD,GAAGoO,MAA6BpO,KAd9BsF,GAeR,CAACA,EAAQtF,EAAS4O,KAEda,EAAYC,IAAiB3Q,EAAAA,EAAAA,UAA6C,CAAE,OAAGnP,KAC/E+f,EAAaC,IAAkB7Q,EAAAA,EAAAA,UAAS,GACzC8Q,EAAmBJ,EAAWE,GAE9BG,GAAcC,EAAAA,EAAAA,cAClB7f,UAcO,IAdA,cACLwe,EAAa,YACbiB,EAAc,EAAC,UACfK,EAAS,OACTC,EAAM,cACNd,EAAgB,GAAE,aAClBI,EAAe,IAQhBnb,EACM6b,GACHjB,GAAW,GAEbE,OAAStf,GAET,IACE,MAAMY,QAAiB0f,EAAAA,EAAcC,oBAAoBzB,EAAeS,EAAea,EAAWT,GAElG,IAAK/e,EAASqe,OAEZ,YADAC,EAAU,IAIZ,MAAMsB,OA5HiBlgB,OAAOwe,EAAyBG,KAC7D,MAAMwB,EAAoBxB,EAAOyB,QAA+B,CAACC,EAAKC,KACpE,MAAMC,EAAUD,EAAMhN,WAChBgL,GAAQ/L,EAAAA,EAAAA,GAAkB+N,GAChC,OAAKC,GAAYjC,EAGV,IAAK+B,EAAK,CAACE,GAAUjC,GAFnB+B,CAE0B,GAClC,CAAC,GAEEhC,GAAWmC,EAAAA,EAAAA,OAAKpF,EAAAA,EAAAA,QAAO+E,IAC7B,GAAI9B,EAASje,OAAS,EACpB,MAAO,CAAC,EAEV,MAQMqgB,UARqBT,EAAAA,EAAcU,WAAW,CAClDC,eAAgBnC,EAChBpJ,OAAQgJ,EAA6BC,GACrCuC,cAAeC,EAAAA,GAASC,OAGDC,MAES,IAAIX,QAA+B,CAACC,EAAKW,KAClE,IAAKX,EAAK,CAACW,EAAIC,KAAKnR,SAAUkR,EAAIC,KAAKjN,WAC7C,CAAC,GAYJ,OAV2B2K,EAAOyB,QAA+B,CAACC,EAAKC,KACrE,MAAMC,EAAUD,EAAMhN,WACtB,IAAKiN,EACH,OAAOF,EAET,MAAM/B,EAAQ6B,EAAkBI,GAEhC,MAAO,IAAKF,EAAK,CAACE,GAAUE,EAAiBnC,IAAUA,EAAO,GAC7D,CAAC,EAEqB,EAwFa4C,CAAuB1C,EAAele,EAASqe,QACzEwC,EAAqB7gB,EAASqe,OAAOpH,KAAK+I,IAC9C,MAAMC,EAAUD,EAAMhN,WACtB,IAAKiN,EACH,MAAO,IAAKD,GAEd,MAAMtM,EAAUkM,EAAkBK,GAClC,MAAO,IAAKD,EAAOtM,UAAS,IAG9B4K,EAAUuC,GACV3B,GAAe4B,IACN,IAAKA,EAAW,CAAC3B,EAAc,GAAInf,EAAS+gB,mBAEvD,CAAE,MAAOza,GACPoY,EAASpY,EACX,CAAC,QACCkY,GAAW,EACb,IAEF,IAGIwC,GAAezC,QAA2Cnf,IAAhC6f,EAAWE,EAAc,GACnD8B,GAAmB1C,IAA4B,IAAhBY,QAAqD/f,IAAhC6f,EAAWE,EAAc,KAEnFzQ,EAAAA,EAAAA,YAAU,KACR4Q,EAAY,CAAEpB,gBAAea,eAAcJ,iBAAgB,GAC1D,CAACW,EAAaP,EAAcb,EAAeS,IAE9C,MAAMuC,GAAQ3B,EAAAA,EAAAA,cAAY,KACxBjB,EAAU,IACVY,EAAc,CAAE,OAAG9f,IACnBggB,EAAe,GACfE,EAAY,CAAEpB,iBAAgB,GAC7B,CAACoB,EAAapB,IAEXiD,GAAgB5B,EAAAA,EAAAA,cAAY,KAChCH,GAAgBgC,GAAaA,EAAW,IACxC9B,EAAY,CACVpB,gBACAiB,YAAaA,EAAc,EAC3BK,UAAWP,EAAWE,EAAc,GACpCJ,eACAJ,iBACA,GACD,CAACT,EAAeiB,EAAaG,EAAaL,EAAYF,EAAcJ,IAEjE0C,GAAgB9B,EAAAA,EAAAA,cAAY,KAChCH,GAAgBgC,GAAaA,EAAW,IACxC9B,EAAY,CACVpB,gBACAiB,YAAaA,EAAc,EAC3BK,UAAWP,EAAWE,EAAc,GACpCJ,eACAJ,iBACA,GACD,CAACT,EAAeiB,EAAaG,EAAaL,EAAYF,EAAcJ,IAEjE2C,GAAqB/B,EAAAA,EAAAA,cACzB,WACE,OAAOD,EAAY,CACjBpB,gBACAiB,cACAK,UAAWH,EACXI,OALG5f,UAAAC,OAAA,QAAAV,IAAAS,UAAA,IAAAA,UAAA,GAMHkf,eACAJ,iBAEJ,GACA,CAACT,EAAeiB,EAAaG,EAAaD,EAAkBN,EAAcJ,IAG5E,MAAO,CACLN,SACAE,UACAE,QACAuC,cACAC,kBACAE,gBACAE,gBACAC,qBACAJ,QACD,E,uICjO2C,IAAA1X,EAAA,CAAA3F,KAAA,UAAAC,OAAA,kBAAAmM,EAAA,CAAApM,KAAA,UAAAC,OAAA,+FAEvC,MAAMyd,EAAyBljB,IAQ/B,IARgC,cACrCmjB,EAAa,KACbtd,EAAI,gBACJud,GAKDpjB,EACC,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,KACZkgB,GAAqB,OAAJxd,QAAI,IAAJA,OAAI,EAAJA,EAAM4Q,QAAOlR,IAAA,IAAC,IAAE2F,GAAK3F,EAAA,OAAK2F,IAAQA,EAAIoY,WAAWC,EAAAA,GAAuB,MAAK,GAC9FC,EAAeH,EAAe5hB,OAAS,EAC7C,OACE0E,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACTC,WAAY,SACZ6e,SAAU,OACV7G,UAAW1Z,EAAMmD,QAAQC,GACzBod,OAAQxgB,EAAMmD,QAAQC,IACvB,IAAChB,SAAA,CAED+d,EAAezK,KAAK2E,IACnBld,EAAAA,EAAAA,GAACsjB,EAAAA,EAAW,CAEVpG,IAAKA,EACL3Z,IAAGuH,EACHyY,UAAW,GACX7S,SAAU,IACV8S,qBAAmB,GALdtG,EAAIrS,OAOT,KACJ7K,EAAAA,EAAAA,GAACmD,EAAAA,EAAM,CACLV,YAAa,GAAGsgB,0BAChBnX,KAAK,QACLxJ,KAAO+gB,GAA2BnjB,EAAAA,EAAAA,GAACyjB,EAAAA,IAAU,SAAvB/iB,EACtByB,QAAS2gB,EACT7d,SACGke,OAKGziB,GAJFV,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,aAKrB5N,IAAGgO,EAUH5O,KAAK,eAEH,E,eCvDV,MAAM+gB,EAAUA,CAAC3O,EAA6ClS,IAC7C,gBAAXkS,GACK/U,EAAAA,EAAAA,GAAC2jB,EAAAA,EAAS,CAACpgB,KAAGC,EAAAA,EAAAA,IAAE,CAAEoB,MAAO/B,EAAM6B,OAAOkf,uBAAuB,MAGvD,OAAX7O,GACK/U,EAAAA,EAAAA,GAAC6jB,EAAAA,IAAe,CAACtgB,KAAGC,EAAAA,EAAAA,IAAE,CAAEoB,MAAO/B,EAAM6B,OAAOof,uBAAuB,MAG7D,UAAX/O,GACK/U,EAAAA,EAAAA,GAAC+jB,EAAAA,IAAW,CAACxgB,KAAGC,EAAAA,EAAAA,IAAE,CAAEoB,MAAO/B,EAAM6B,OAAOsf,sBAAsB,MAGhE,KAGIC,EAAgGtkB,IAEtG,IADLka,KAAK,SAAEqK,IACRvkB,EACC,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,KACZqhB,GAAOC,EAAAA,EAAAA,KAEPC,EAAkBlP,EAAAA,GAAiC+O,EAASnP,QAAU,SAE5E,OACEjP,EAAAA,EAAAA,IAAA,OAAKvC,KAAGC,EAAAA,EAAAA,IAAE,CAAEc,QAAS,OAAQyB,IAAKlD,EAAMmD,QAAQC,GAAI1B,WAAY,UAAU,IAACU,SAAA,CACxEye,EAAQQ,EAASnP,OAAQlS,GACzBwhB,EAAkBF,EAAKG,cAAcD,GAAmB,KACrD,E,eCvBV,MAAME,EAAe,CAAApf,KAAA,SAAAC,OAAA,wEAInB,IAAAF,EAAA,CAAAC,KAAA,SAAAC,OAAA,iBAEF,MAAMof,EAA6B7kB,IAQ5B,IAR6B,MAClC4F,EAAK,QACLgc,EAAO,iBACPkD,GAKD9kB,EACC,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,MACX4hB,EAAYC,IAAiB9U,EAAAA,EAAAA,WAAS,IACtC+U,EAAUC,IAAehV,EAAAA,EAAAA,UAAwB,OAEjDgQ,EAASC,IAAcjQ,EAAAA,EAAAA,WAAkB,GAE1CiV,GAAgBjE,EAAAA,EAAAA,cAAY7f,UAChC8e,GAAW,GACX,IACE,MAAMxe,QAAiB0f,EAAAA,EAAc+D,uBAGlCxD,GAEH,GAAIkD,KAAoBnjB,EAAU,CAChC,MAAM0jB,EAAe1jB,EAASmjB,GACxBQ,GAAcC,EAAAA,EAAAA,UAASF,GAAgBA,EAAenkB,KAAKC,UAAUkkB,GAC3EH,EAAYI,EACd,CACF,CAAE,MAAOrd,GACP,MAAMud,EAAevd,aAAawd,EAAAA,EAAexd,EAAEyd,sBAAwBzd,EAAEhG,QAC7E8D,EAAAA,EAAM4f,sBAAsB,4BAA4BH,IAC1D,CACArF,GAAW,EAAM,GAChB,CAAC2E,EAAkBlD,IAEhBgE,GAAyBlS,EAAAA,EAAAA,IAAiC9N,GAE1DigB,GAAS3E,EAAAA,EAAAA,cAAY7f,WACpB4jB,GAAYW,SACTT,IAERH,GAAc,EAAK,GAClB,CAACC,EAAUE,EAAeS,IAEvBE,GAAW5E,EAAAA,EAAAA,cAAY,KAC3B8D,GAAc,EAAM,GACnB,IAEH,OACE7e,EAAAA,EAAAA,IAAA,OAAKvC,KAAGC,EAAAA,EAAAA,IAAE,CAAEc,QAAS,OAAQyB,IAAKlD,EAAMmD,QAAQC,IAAI,IAAChB,SAAA,EACnDjF,EAAAA,EAAAA,GAACmD,EAAAA,EAGC,CACAV,YAAY,iDACZmJ,KAAK,QACLxJ,KAAMsiB,GAAa1kB,EAAAA,EAAAA,GAAC0lB,EAAAA,IAAe,KAAM1lB,EAAAA,EAAAA,GAAC2lB,EAAAA,EAAgB,IAC1DxjB,QAASuiB,EAAae,EAAWD,EACjCjiB,IAAG2B,EACH2a,QAASA,EACTld,KAAK,aAEP3C,EAAAA,EAAAA,GAAA,OACEoI,MAAO7C,EACPhC,IAAG,4CAKAmhB,GAAcH,EAAe,IAC9Btf,SAEDyf,GAAa1kB,EAAAA,EAAAA,GAAC4lB,EAAiB,CAACrgB,MAAe,OAARqf,QAAQ,IAARA,EAAAA,EAAYrf,IAAYA,MAE9D,EAIJqgB,EAAoB9a,IAAmC,IAAlC,MAAEvF,GAA0BuF,EACrD,MAAM,MAAEjI,IAAUC,EAAAA,EAAAA,KAEZ+iB,GAAsBzT,EAAAA,EAAAA,UAAQ,KAGlC,IACE,MAAM0T,EAAajlB,KAAK+F,MAAMrB,GAC9B,OAAO1E,KAAKC,UAAUglB,EAAY,KAAM,EAC1C,CAAE,MAAOle,GACP,OAAO,IACT,IACC,CAACrC,IACJ,OACEvF,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACHoV,WAAY,WACZS,UAAW,aACX0M,WAAYF,EAAsB,iBAAcnlB,GACjD,IAACuE,UAEFjF,EAAAA,EAAAA,GAAC0M,EAAAA,GAAW,CACVC,SAAS,OACTU,eAAa,EACb1E,MAAO,CACLvE,QAASvB,EAAMmD,QAAQ6Q,IAEzBhU,MAAOA,EAAMmjB,WAAa,cAAgB,QAAQ/gB,SAEjD4gB,GAAuBtgB,KAEtB,EAIG0gB,EAAwG1U,IAAA,IACnHsI,KAAK,SAAEqK,IACR3S,EAAA,OACCvR,EAAAA,EAAAA,GAACwkB,EAA0B,CACzBC,iBAAiB,UACjBlD,QAAS2C,EAAS5P,YAAc,GAChC/O,OAAOkO,EAAAA,EAAAA,GAAmByQ,IAAa,IACvC,EAGSgC,EAAyG1U,IAAA,IACpHqI,KAAK,SAAEqK,IACR1S,EAAA,OACCxR,EAAAA,EAAAA,GAACwkB,EAA0B,CACzBC,iBAAiB,WACjBlD,QAAS2C,EAAS5P,YAAc,GAChC/O,OAAOqO,EAAAA,EAAAA,IAAoBsQ,IAAa,IACxC,E,eC/IG,MAAMiC,EAAgGxmB,IAAA,IAC3Gka,KAAK,SAAEqK,IACRvkB,EAAA,OAAKK,EAAAA,EAAAA,GAACqF,EAAAA,EAAkB,CAACE,OAAO6gB,EAAAA,EAAAA,OAAMlC,EAAS1e,KAAM,QAAU,ECInD6gB,EAA0B5d,GAAe,YAAYA,SACrD6d,EAA0B7d,GAAe,SAASA,SCGlD8d,EAAqBxkB,EAAAA,MAChCpC,IAAuC,IAAtC,IAAEka,GAA8Bla,EAC/B,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,KAElB,OACE9C,EAAAA,EAAAA,GAAA,OACEwmB,KAAK,MAEL,cAAY,4BACZjjB,KAAGC,EAAAA,EAAAA,IAAE,CACHijB,UAAW5jB,EAAMc,QAAQ+iB,aACzBpiB,QAAS,OACT+G,cAAe,MACf,SAAU,CACRiF,gBAAiB,0BAEnBqW,aAAc,OACd3O,aAAc,0CACf,IAAC/S,SAED4U,EAAI+M,cAAcrO,KAAKsO,IAAU,IAADC,EAAAC,EAC/B,MAAMC,EAA2D,QAAlDF,EAAID,EAAKI,OAAOC,UAA8BC,YAAI,IAAAL,OAAA,EAA/CA,EAAiDE,UAE7D5iB,EADWyiB,EAAKI,OAAOxe,KAAO+L,EAAAA,GACT3R,EAAMmD,QAAQ6Q,GAAK,GAAGhU,EAAMmD,QAAQ6Q,QAAQhU,EAAMmD,QAAQC,OAErF,OACEjG,EAAAA,EAAAA,GAAA,OACEwmB,KAAK,OACLjjB,IAAG,CACD,CACE,+BAAgC,GAAGV,EAAMmD,QAAQ6Q,OACjD0C,KAAM,YAAY+M,EAAuBO,EAAKI,OAAOxe,cACrDgE,SAAU,SACVmM,WAAYoO,EAAY,WAAa,SACrC/H,aAAc+H,EAAY,gBAAatmB,EACvC0D,WAE6C,QAD9C2iB,EACAF,EAAKI,OAAOC,UAA8BC,YAAI,IAAAJ,OAAA,EAA/CA,EAAiD3hB,OAAM,IACvDH,UAGDmiB,EAAAA,EAAAA,IAAWP,EAAKI,OAAOC,UAAUL,KAAMA,EAAKQ,eAFxCR,EAAKpe,GAGN,KAnCLoR,EAAIpR,GAsCL,IAGV,CAAC6e,EAAMC,IAEHD,EAAKE,UAAYD,EAAKC,SACtBF,EAAKG,WAAaF,EAAKE,WACvBC,EAAAA,EAAAA,SAAQJ,EAAKzN,IAAIqK,SAAS1e,KAAM+hB,EAAK1N,IAAIqK,SAAS1e,QC7D3CmiB,EACX5lB,EAAAA,MACEpC,IAA4B,IAAzBka,KAAK,SAAEqK,IAAYvkB,EACpB,OAAKukB,EAAS0D,cAIZ5nB,EAAAA,EAAAA,GAAC6e,EAAAA,IAAa,CACZzW,MAAO,IAAI+E,KAAK+W,EAAS0D,cAAcC,eAAeC,UAAUnb,SAAU,CACxEob,aAAc,UAEhBC,UAAU,QAAO/iB,UAEjBjF,EAAAA,EAAAA,GAAA,QAAAiF,SAAOS,EAAAA,EAAMuiB,aAAa/D,EAAS0D,kBAT9B,IAUS,IAGpB,KAAM,ICdGM,EAAgCvoB,IAA8C,IAA7C,MAAEwoB,GAAqCxoB,EACnF,MAAMyoB,EAAYD,EAAME,0BAA2BF,EAAMG,yBAA0B,KAEnF,OACEtoB,EAAAA,EAAAA,GAACuoB,EAAAA,IAAQ,CACP9lB,YAAY,oGACZ,cAAY,8BACZ2lB,UAAWA,EACXnc,aAAc,CAAE7H,QAAS,EAAGokB,OAAQ,GACpC9V,SAAUyV,EAAMM,uBAChB,ECVOC,EAA8B/oB,IAA2C,IAA1C,IAAEka,GAAkCla,EAC9E,OACEK,EAAAA,EAAAA,GAACuoB,EAAAA,IAAQ,CACP9lB,YAAY,kGACZ,cAAa,6BAA6BoX,EAAIpR,KAC9CgK,UAAWoH,EAAI8O,eACfP,UAAWvO,EAAI+O,gBACf3c,aAAc,CAAE7H,QAAS,EAAGokB,OAAQ,GACpC9V,SAAUA,IAAMmH,EAAIgP,kBACpB,E,eCDC,MAAMC,EAOT,CACFC,OAAQ,CACNC,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,6FAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,+BAIlBkkB,cAAeA,IACb,wZAeJC,UAAW,CAITJ,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,+GAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,kCAIlBkkB,cAAeA,IACb,6YAaJE,YAAa,CACXL,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,+FAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,oCAIlBkkB,cAAeA,IACb,wWAWJG,KAAM,CACJN,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,4FAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,6BAIlBkkB,cAAeA,IACb,oeAgBJI,OAAQ,CACNP,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,8FAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,+BAIlBkkB,cAAeA,IAAM,6rCAyCvBK,QAAS,CACPR,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,kGAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,gCAIlBkkB,cAAeA,IACb,shBAaJM,UAAW,CACTT,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,gGAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,kCAIlBkkB,cAAeA,IAAM,igBAkBvBO,QAAS,CACPV,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,kGAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,gCAIlBkkB,cAAeA,IAAM,0dAgBvBQ,QAAS,CACPX,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,8FAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,gCAIlBkkB,cAAeA,IAAM,qSAUvBS,OAAQ,CACNZ,WAAY,SACZC,WAAYA,KACVjpB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,iGAEfiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,+BAIlBkkB,cAAeA,IAAM,8SAUvBU,OAAQ,CACNb,WAAY,SACZC,WAAalG,IACXjd,EAAAA,EAAAA,IAAAL,EAAAA,GAAA,CAAAR,SAAA,EACEjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWoe,UAAS,CAAA7kB,UACnBjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eACE,kMAIFiL,OAAQ,CACN8M,MAAMlpB,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,wBAIlBjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWoe,UAAS,CAAA7kB,UACnBjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eACE,kOAQFiL,OAAQ,CACN9F,EAAIyT,IACF/pB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACdpU,MAAM,yBACN3F,YAAa,GAAGsgB,0CAChBnH,KAAK,6EACLa,cAAY,EAAAxX,SAEX8kB,YAQfZ,cAAeA,IACb,uU,eCxVmG,IAAAjkB,EAAA,CAAAC,KAAA,UAAAC,OAAA,uCAElG,MAAM4kB,EAA8BrqB,IAMpC,IANqC,WAC1CsqB,EAAU,gBACVlH,GAIDpjB,EACC,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,MACZ,WAAEmmB,EAAU,cAAEE,EAAa,WAAEH,GAAeF,EAAmBmB,IAC/D,uBAAE/a,GAAyB,IAASE,EAAAA,EAAAA,KACpCkJ,EAAU2Q,EAAWlG,GAYrBmG,EAXe,0GAG0BF,mDACPA,4IAOZG,IAEtBe,GACJlqB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eACE,0MAKFiL,OAAQ,CACN4M,aACAmB,gBAAgBnqB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWC,KAAI,CAACud,MAAI,EAAAjkB,SAAC,8BAK5C,OACEa,EAAAA,EAAAA,IAAA,OAAAb,SAAA,CACGiK,IACClP,EAAAA,EAAAA,GAACoqB,EAAAA,IAAK,CACJ3nB,YAAa,GAAGsgB,kBAAgCkH,qBAChD1mB,KAAGC,EAAAA,EAAAA,IAAE,CAAE+H,aAAc1I,EAAMmD,QAAQgL,IAAI,IACvCqZ,UAAU,EACVzoB,SACE5B,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,kCAEfiL,OAAQ,CAAE4M,gBAGdsB,YAAaJ,EACbvnB,KAAK,UAGT3C,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWC,KAAI,CAAA1G,SAAEqT,KAClBxS,EAAAA,EAAAA,IAAA,OAAKvC,IAAG2B,EAAiDD,SAAA,EACvDjF,EAAAA,EAAAA,GAAC6b,EAAAA,EAAU,CACTpZ,YAAa,GAAGsgB,kBAAgCkH,4BAChD1mB,KAAGC,EAAAA,EAAAA,IAAE,CAAE+mB,OAAQ,EAAGhe,SAAU,WAAYie,IAAK3nB,EAAMmD,QAAQC,GAAI6G,MAAOjK,EAAMmD,QAAQC,IAAI,IACxFwkB,WAAW,EACX1O,SAAUmN,EACV9mB,MAAMpC,EAAAA,EAAAA,GAAC8b,EAAAA,IAAQ,OAEjB9b,EAAAA,EAAAA,GAAC0M,EAAAA,GAAW,CACVge,iBAAe,EACf7nB,MAAOA,EAAMmjB,WAAa,cAAgB,QAC1Crd,MAAO,CACLvE,QAAS,GAAGvB,EAAMmD,QAAQ6Q,QAAQhU,EAAMmD,QAAQgL,OAChD1F,UAAWzI,EAAMmD,QAAQgL,IAE3BrE,SAAS,SAAQ1H,SAEhBikB,SAGD,EC/EGyB,EAAoChrB,IAQ1C,IAR2C,gBAChDojB,EAAe,cACfvD,EAAa,QACb1O,GAKDnR,EACC,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,MACZ,iBAAEmM,IAAqBG,EAAAA,EAAAA,KAE7B,OACEtJ,EAAAA,EAAAA,IAAA,OAAKvC,KAAGC,EAAAA,EAAAA,IAAE,CAAEuI,YAAalJ,EAAMmD,QAAQgL,IAAI,IAAC/L,SAAA,EAC1CjF,EAAAA,EAAAA,GAACgb,EAAAA,IAAM,CACL5S,OACEpI,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,uBAInByZ,kBAAmB,KAErB5qB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWC,KAAI,CACdpI,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,QACTgH,UAAWzI,EAAMmD,QAAQgL,GACzBzF,aAAc1I,EAAMmD,QAAQgL,IAC7B,IAAC/L,SAEe,OAAhBgK,QAAgB,IAAhBA,EAAAA,GACCjP,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eACE,iTAQFiL,OAAQ,CACNyO,QAAQlX,EAAAA,EAAAA,OAAM7C,GACdwF,EAAIyT,IACF/pB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACd/Z,YAAa,GAAGsgB,sCAChBnH,KAAK,yDACLa,cAAY,EAAAxX,SAEX8kB,UAObjkB,EAAAA,EAAAA,IAACglB,EAAAA,IAAKrN,KAAI,CAAChb,YAAa,GAAGsgB,4BAA2CgI,aAAa,SAAQ9lB,SAAA,EACzFa,EAAAA,EAAAA,IAACglB,EAAAA,IAAKE,KAAI,CAAA/lB,SAAA,EACRjF,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,SAAQN,UAC1BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,cAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,YAAWN,UAC7BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,6BAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,cAAaN,UAC/BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,kBAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,OAAMN,UACxBjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,YAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,SAAQN,UAC1BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,cAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,UAASN,UAC3BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,eAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,YAAWN,UAC7BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,iBAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,UAASN,UAC3BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,eAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,UAASN,UAC3BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,eAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,SAAQN,UAC1BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,cAInBnR,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKG,QAAO,CAAC1lB,MAAM,SAAQN,UAC1BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,iBAKpBgF,EAAAA,EAAAA,MAAK2S,GAAoBvQ,KAAK0R,IAC7BjqB,EAAAA,EAAAA,GAAC8qB,EAAAA,IAAKlN,QAAO,CAACrY,MAAO0kB,EAAgChlB,UACnDjF,EAAAA,EAAAA,GAACgqB,EAA2B,CAC1BC,WAAYA,EACZlH,gBAAiBA,KAHsCkH,EAAa,mBAQxE,ECjGwF,IAAA/kB,GAAA,CAAAC,KAAA,SAAAC,OAAA,yCAkClG,MAAM8lB,GAAyCvrB,IAKxC,IAJLka,KAAK,SAAEqK,GACPiE,OACEgD,SAAS,KAAEhE,KAEdxnB,EACC,MAAM,gBAAEojB,EAAe,eAAEqI,GAAmBjE,EAC5C,OACEnnB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACd/Z,YAAa,GAAGsgB,iCAChBjE,UAAQ,EACRvb,IAAG2B,GACH/C,QAASA,KACO,OAAdipB,QAAc,IAAdA,GAAAA,EAAiBlH,EAAS,EAC1Bjf,SAEDif,EAAS5P,YACM,EAEpB,IAAA/C,GAAA,CAAApM,KAAA,SAAAC,OAAA,yCAEF,MAAMimB,GAAyCvgB,IAKxC,IAJL+O,KAAK,SAAEqK,GACPiE,OACEgD,SAAS,KAAEhE,KAEdrc,EACC,MAAM,gBAAEiY,EAAe,eAAEqI,GAAmBjE,EAC5C,OACEnnB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACd/Z,YAAa,GAAGsgB,iCAChBjE,UAAQ,EACRvb,IAAGgO,GACHpP,QAASA,KACO,OAAdipB,QAAc,IAAdA,GAAAA,EAAiBlH,EAAS,EAC1Bjf,UAED6O,EAAAA,EAAAA,IAAiBoQ,EAAUpR,EAAAA,KACZ,EAEpB,IAAArB,GAAA,CAAAtM,KAAA,SAAAC,OAAA,8EAEF,MAAMkmB,GAAuC9Z,IAA4B,IAAzBqI,KAAK,SAAEqK,IAAY1S,EACjE,MAAM8N,GAAQ/L,EAAAA,EAAAA,GAAkB2Q,GAChC,IAAK5E,IAAU4E,EAASqH,cACtB,OAAO,KAET,MAAMxS,EAAQmL,EAASlP,SAAWsK,EAClC,OACEtf,EAAAA,EAAAA,GAACwc,EAAAA,GAAI,CACHjZ,IAAGkO,GAMHoM,GAAIC,EAAAA,EAAOC,gBAAgBmG,EAASqH,cAAejM,GAAOra,SAEzD8T,GACI,EAILyS,GAAyCtS,IAKxC,IAJLW,KAAK,SAAEqK,GACPiE,OACEgD,SAAS,KAAEhE,KAEdjO,EACC,MAAM,gBAAEuS,EAAe,gBAAE1I,GAAoBoE,EAC7C,OACEnnB,EAAAA,EAAAA,GAAC6iB,EAAsB,CACrBrd,KAAM0e,EAAS1e,MAAQ,GACvBsd,cAAeA,IAAqB,OAAf2I,QAAe,IAAfA,OAAe,EAAfA,EAAkBvH,GACvCnB,gBAAiBA,GACjB,EAEJ,IAAA2I,GAAA,CAAAvmB,KAAA,SAAAC,OAAA,yCAAAumB,GAAA,CAAAxmB,KAAA,SAAAC,OAAA,yCAAAwmB,GAAA,CAAAzmB,KAAA,SAAAC,OAAA,8EAOK,MAAMymB,GAAkB9pB,EAAAA,MAC7B4Y,IAuB6B,IAvB5B,cACC6E,EAAa,QACb1O,EAAO,OACP6O,EAAM,QACNE,EAAO,MACPE,EAAK,eACLqL,EAAc,gBACdK,EAAe,YACfnJ,EAAW,gBACXC,EAAe,WACfuJ,EAAU,eACVC,EAAc,aACdC,EAAY,eACZC,EAAc,QACdxM,EAAO,WACPyM,EAAU,aACVC,EAAY,gBACZC,EAAe,cACfC,EAAgB,GAAE,mBAClBC,EAAkB,gBAClBvJ,EAAe,mBACfwJ,EAAkB,gBAClBC,EAAkB,IACG7R,EACrB,MAAMwJ,GAAOC,EAAAA,EAAAA,MACP,MAAEvhB,IAAUC,EAAAA,EAAAA,KAEZ2pB,GAAwBC,EAAAA,EAAAA,MAExBC,GAAiBva,EAAAA,EAAAA,UAA0B,KACxCwa,EAAAA,EAAAA,SAAQlY,EAAAA,IACZ6D,KAAIqC,IAAA,IAAE/P,EAAKkO,GAAM6B,EAAA,MAAM,CACtB/P,MACAkO,MAAOoL,EAAKG,cAAcvL,GAC3B,IACA3C,QAAOyE,IAAA,IAAC,IAAEhQ,GAAKgQ,EAAA,OAAM2R,EAAgBvS,SAASpP,EAAI,KACpD,CAACsZ,EAAMqI,IAEJhF,IAAUpV,EAAAA,EAAAA,UAA2B,KACzC,MAAMoV,EAA6B,CACjC,CACE/e,GAAI+L,EAAAA,GACJqY,OAAQ3E,EACR4E,gBAAgB,EAChBC,eAAe,EACflG,KAAM6B,EACNvB,KAAM,CAAE/hB,OAAQ,CAAE4nB,SAAU,GAAItc,SAAU,MAE5C,CACEmc,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiCE,YAClGoY,eAAe,EACfD,gBAAgB,EAChBrkB,GAAIgM,EAAAA,GAAiCE,UACrCkS,KAAM4F,EACFvB,GACA+B,IAA4B,IAAzBpT,KAAK,SAAEqK,IAAY+I,EACpB,OACEjtB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACd/Z,YAAa,GAAGsgB,iCAChBjE,UAAQ,EACRvb,IAAGmoB,GACHvpB,QAASA,KACO,OAAdipB,QAAc,IAAdA,GAAAA,EAAiBlH,EAAS,EAC1Bjf,SAEDif,EAAS5P,YACM,EAG1B6S,KAAM,CAAE/hB,OAAQ,CAAE4nB,SAAU,OAE9B,CACEH,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiCI,YAClGkY,eAAe,EACfD,gBAAgB,EAChBrkB,GAAIgM,EAAAA,GAAiCI,UACrCgS,KAAM4F,EACFpB,GACA6B,IAA4B,IAAzBrT,KAAK,SAAEqK,IAAYgJ,EACpB,OACEltB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACd/Z,YAAa,GAAGsgB,iCAChBjE,UAAQ,EACRvb,IAAGooB,GACHxpB,QAASA,KACO,OAAdipB,QAAc,IAAdA,GAAAA,EAAiBlH,EAAS,EAC1Bjf,UAED6O,EAAAA,EAAAA,IAAiBoQ,EAAUpR,EAAAA,KACZ,EAG1BqU,KAAM,CAAE/hB,OAAQ,CAAE4nB,SAAU,OAE9B,CACEH,OAAQ1I,EAAKG,cACX5P,EAAAA,GAAsCD,EAAAA,GAAiCK,cAEzErM,GAAIgM,EAAAA,GAAiCK,YACrCqY,WAAaC,GAASA,EAAKxF,aAC3BmF,eAAe,EACfD,gBAAgB,EAChBjG,KAAMc,EACNR,KAAM,CAAE/hB,OAAQ,CAAE4nB,SAAU,OAE9B,CACEH,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiCM,SAClGtM,GAAIgM,EAAAA,GAAiCM,OACrCgY,eAAe,EACfD,gBAAgB,EAChBjG,KAAM5C,EACNkD,KAAM,CAAE/hB,OAAQ,CAAE4nB,SAAU,OAE9B,CACEH,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiCf,SAClGjL,GAAIgM,EAAAA,GAAiCf,OACrCqZ,eAAe,EACfD,gBAAgB,EAChBjG,KAAMZ,EACNkB,KAAM,CAAEH,WAAW,IAErB,CACE6F,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiCZ,UAClGkZ,eAAe,EACfD,gBAAgB,EAChBrkB,GAAIgM,EAAAA,GAAiCZ,QACrCgT,KAAMX,EACNiB,KAAM,CAAEH,WAAW,IAErB,CACE6F,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiCO,UAClG+X,eAAe,EACfD,gBAAgB,EAChBrkB,GAAIgM,EAAAA,GAAiCO,QACrC6R,KAAM4F,EACFnB,GACA+B,IAA4B,IAAzBxT,KAAK,SAAEqK,IAAYmJ,EACpB,MAAM/N,GAAQ/L,EAAAA,EAAAA,GAAkB2Q,GAChC,IAAK5E,IAAU4E,EAASqH,cACtB,OAAO,KAET,MAAMxS,EAAQmL,EAASlP,SAAWsK,EAClC,OACEtf,EAAAA,EAAAA,GAACwc,EAAAA,GAAI,CACHjZ,IAAGqoB,GAMH/N,GAAIC,EAAAA,EAAOC,gBAAgBmG,EAASqH,cAAejM,GAAOra,SAEzD8T,GACI,GAIjB,CACE8T,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiC5N,SAClGkmB,eAAe,EACfD,gBAAgB,EAChBrkB,GAAIgM,EAAAA,GAAiC5N,OACrCggB,KAAMV,EACNgB,KAAM,CAAE/hB,OAAQ,CAAE4nB,SAAU,QAiDhC,OA7CKV,GACH9E,EAAQ8F,KAAK,CACXT,OAAQ1I,EAAKG,cACX5P,EAAAA,GAAsCD,EAAAA,GAAiCQ,cAEzE8X,eAAe,EACfD,gBAAgB,EAChBrkB,GAAIgM,EAAAA,GAAiCQ,YACrCkY,WAAaC,IAAS5Z,EAAAA,EAAAA,IAAwB4Z,GAC9CjG,KAAM,CAAE/hB,OAAQ,CAAE4nB,SAAU,GAAItc,SAAU,OAG9C8W,EAAQ8F,KACN,CACET,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiCS,UAClG6X,eAAe,EACfD,gBAAgB,EAChBrkB,GAAIgM,EAAAA,GAAiCS,QACrCiY,WAAaC,IACPzZ,EAAAA,EAAAA,OAAMyZ,EAAKG,qBAAuBC,SAASJ,EAAKG,mBAC3C,KAEF7nB,EAAAA,EAAM+nB,eAAeL,EAAKG,mBAEnCpG,KAAM,CAAE/hB,OAAQ,CAAE4nB,SAAU,OAE9B,CACEH,OAAQ1I,EAAKG,cAAc5P,EAAAA,GAAsCD,EAAAA,GAAiCjP,OAClGunB,eAAe,EACfD,gBAAgB,EAChBrkB,GAAIgM,EAAAA,GAAiCjP,KACrCqhB,KAAM4F,EACFjB,GACAkC,IAA4B,IAAzB7T,KAAK,SAAEqK,IAAYwJ,EACpB,OACE1tB,EAAAA,EAAAA,GAAC6iB,EAAsB,CACrBrd,KAAM0e,EAAS1e,MAAQ,GACvBsd,cAAeA,IAAqB,OAAf2I,QAAe,IAAfA,OAAe,EAAfA,EAAkBvH,GACvCnB,gBAAiBA,GACjB,IAMPyE,EAAQpR,QAAQ6Q,GAAWA,EAAOxe,KAAO4jB,EAAcpS,SAASgN,EAAOxe,KAAI,GACjF,CACD0b,EACAiH,EACAK,EACAa,EACAD,EACAtJ,EACA0J,IAGItE,IAAQwF,EAAAA,EAAAA,IAAyC,CACrDnG,WACA4F,KAAMzN,EACNiO,MAAO,CAAEnO,UAAS0M,gBAClB0B,iBAAiBA,EAAAA,EAAAA,MACjBC,SAAUA,CAACjU,EAAKrB,IAAUqB,EAAIvF,YAAckE,EAAMuV,WAClDC,mBAAmBA,EAAAA,EAAAA,MACnBC,gBAAiB/B,EACjBgC,qBAAsB9B,EACtB+B,sBAAsB,EACtBC,oBAAoB,EACpBC,iBAAkB,WAClBlH,KAAM,CAAEpE,kBAAiBqI,iBAAgBK,qBA4DrC6C,GAAiBnG,GAAMoG,WAAWC,iBAClCC,GAAiB1sB,EAAAA,SAAc,KACnC,MAAMP,EAAU2mB,GAAMuG,iBAChBC,EAAsC,CAAC,EAK7C,OAJAntB,EAAQotB,SAAS/B,IACf8B,EAAStI,EAAuBwG,EAAOpkB,KAAOokB,EAAOgC,UACrDF,EAASrI,EAAuBuG,EAAO5F,OAAOxe,KAAOokB,EAAO5F,OAAO4H,SAAS,IAEvEF,CAAQ,GAGd,CAACL,GAAgB9G,GAASW,KAE7B,OACEriB,EAAAA,EAAAA,IAACoU,EAAAA,IAAK,CACJC,YAAU,EACV2U,MAzEkBC,MACpB,GAAIhP,EAAO,CACT,MAAMoF,EAAepF,aAAiBqF,EAAAA,EAAerF,EAAMiP,kBAAoBjP,EAAMne,QACrF,OACE5B,EAAAA,EAAAA,GAACivB,EAAAA,IAAK,CACJC,OAAOlvB,EAAAA,EAAAA,GAACmvB,EAAAA,EAAU,IAClB7E,YAAanF,EACb/c,OACEpI,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,WAMzB,CACA,OAAK0O,GAA6B,IAAlBF,EAAOve,QAAgB4qB,GAEnChsB,EAAAA,EAAAA,GAACivB,EAAAA,IAAK,CACJ3E,aACEtqB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,mGAEfiL,OAAQ,CACNgT,OAASC,IACPrvB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAW8Q,KAAI,CACd/Z,YAAY,uFACZN,QAAS8pB,EAAehnB,SAEvBoqB,OAMXjnB,OACEpI,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,sBAOpB0O,GAA6B,IAAlBF,EAAOve,OAShB,MAPHpB,EAAAA,EAAAA,GAAC2qB,EAAiC,CAChC5H,gBAAiBA,EACjBvD,cAAeA,EACf1O,QAASA,GAIJ,EAoBFie,GACPpmB,MAAO8lB,GACPa,YACEtvB,EAAAA,EAAAA,GAACuvB,EAAAA,IAAgB,CACf9sB,YAAa,GAAGsgB,4BAChBT,YAAaA,EACbC,gBAAiBA,EACjBuJ,WAAYA,EACZC,eAAgBA,IAEnB9mB,SAAA,EAEDa,EAAAA,EAAAA,IAACsU,EAAAA,IAAQ,CAACC,UAAQ,EAAApV,SAAA,CACfkjB,GAAMqH,iBAAiBjX,KAAKsU,IAAY,IAAD/F,EACtC,OACE9mB,EAAAA,EAAAA,GAACsa,EAAAA,IAAW,CACV7X,YAAY,uFAEZc,IAAsD,QAAnDujB,EAAG+F,EAAO5F,OAAOC,UAA8BC,YAAI,IAAAL,OAAA,EAAjDA,EAAmD1hB,OACxDqqB,SAAU5C,EAAO5F,OAAOyI,aACxBC,cAAe9C,EAAO5F,OAAO2I,eAAiB,OAC9CC,aAAchD,EAAO5F,OAAO6I,0BAC5BjD,OAAQA,EACR5F,OAAQ4F,EAAO5F,OACf8I,gBAAiB5H,GAAM4H,gBACvBC,WAAYnD,EAAO5F,OAAOgJ,gBAC1BtnB,MAAO,CACL4Q,KAAM,YAAY8M,EAAuBwG,EAAOpkB,eAChDxD,UAEDmiB,EAAAA,EAAAA,IAAWyF,EAAO5F,OAAOC,UAAU2F,OAAQA,EAAOxF,eAb9CwF,EAAOpkB,GAcA,KAGlBzI,EAAAA,EAAAA,GAACkwB,EAAAA,IAAc,CAAAjrB,UACba,EAAAA,EAAAA,IAACqqB,EAAAA,IAAa1S,KAAI,CAAAxY,SAAA,EAChBjF,EAAAA,EAAAA,GAACmwB,EAAAA,IAAalF,QAAO,CAACmF,SAAO,EAAAnrB,UAC3BjF,EAAAA,EAAAA,GAACmD,EAAAA,EAAM,CACLV,YAAa,GAAGsgB,0CAChB3gB,MAAMpC,EAAAA,EAAAA,GAACqwB,EAAAA,IAAW,IAClBzkB,KAAK,QACL,aAAYuY,EAAKG,cAAc,CAAA7b,GAAA,SAC7B0I,eAAe,wBAKrBnR,EAAAA,EAAAA,GAACmwB,EAAAA,IAAavS,QAAO,CAAC0S,MAAM,MAAKrrB,SAC9B0nB,EAAepU,KAAIgY,IAAA,IAAC,IAAE1lB,EAAG,MAAEkO,GAAOwX,EAAA,OACjCzqB,EAAAA,EAAAA,IAACqqB,EAAAA,IAAaK,aAAY,CAExB/tB,YAAa,GAAGsgB,sCAChB0N,SAAUpE,EAAcpS,SAASpP,GACjC1I,QAASA,IAAMoqB,EAAmB1hB,GAAK5F,SAAA,EAEvCjF,EAAAA,EAAAA,GAACmwB,EAAAA,IAAaO,cAAa,IAC1B3X,IANIlO,EAOqB,cAMrCgV,IAAW7f,EAAAA,EAAAA,GAAC2wB,EAAAA,IAAiB,CAACxI,MAAOA,MACpCtI,IACCE,GACDoI,GACGyI,cACAC,KAAKtY,KAAKsB,IACT7Z,EAAAA,EAAAA,GAACumB,EAAkB,CAAc1M,IAAKA,EAAK2N,QAASA,GAASC,SAAU0E,EAAatS,EAAIpR,KAA/DoR,EAAIpR,QAE7B,I,gCCvhBP,MAAMqoB,GAAkCnxB,IAQxC,IARyC,WAC9CoxB,EAAU,OACVltB,EAAS,IAAG,iBACZmtB,GAAmB,GAKpBrxB,EACC,MAAMsxB,GAAY3Z,EAAAA,EAAAA,QAA0B,OACrCN,EAAWka,GAAgBnvB,EAAAA,UAAe,GAsCjD,OApCAiO,EAAAA,EAAAA,YAAU,KACR,MAAMmhB,EAAiB7tB,IAAkE,IAAD8tB,EAEtF,MAAMC,EAAgC,QAApBD,EAAGH,EAAUnb,eAAO,IAAAsb,OAAA,EAAjBA,EAAmBE,cACxC,GAAKD,GAAgB/tB,EAAMuD,SAAWwqB,GAI9B/tB,EAAM8pB,KAAKzqB,OACZ4uB,GAAoCC,MACvCN,GAAa,EAKjB,EAIF,OADAlnB,OAAOynB,iBAAiB,UAAWN,GAC5B,KACLnnB,OAAO0nB,oBAAoB,UAAWP,EAAc,CACrD,GACA,CAACJ,KAEJ/gB,EAAAA,EAAAA,YAAU,KAAO,IAAD2hB,EACd,MAAMN,EAAgC,QAApBM,EAAGV,EAAUnb,eAAO,IAAA6b,OAAA,EAAjBA,EAAmBL,cACnCD,IAAgBra,GAIrBqa,EAAaO,YAAY,CACvBjvB,KAAMkvB,GAAoCC,YAC1CC,UAAWhB,GACX,GACD,CAACA,EAAY/Z,KAGdlR,EAAAA,EAAAA,IAAA,OAAKvC,KAAGC,EAAAA,EAAAA,IAAE,CAAEK,UAAQ,IAACoB,SAAA,CAClB+R,IACClR,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACH+I,SAAU,WACV7I,MAAO,OACPG,UACD,IAACoB,SAAA,EAEFjF,EAAAA,EAAAA,GAACgyB,EAAAA,IAAa,KACdhyB,EAAAA,EAAAA,GAACiyB,EAAAA,IAAa,CAACC,MAAO,QAG1BlyB,EAAAA,EAAAA,GAAA,UACEoI,MAAM,uBAGNiI,IAAK,+DAA+D8hB,GAAAA,KACpElwB,IAAKgvB,EACL1tB,KAAGC,EAAAA,EAAAA,IAAE,CACHa,OAAQ,OACRX,MAAO,OACPG,UACD,QAEC,EClFH,IA2HK0tB,GAAmC,SAAnCA,GAAmC,OAAnCA,EAAmC,cAAnCA,CAAmC,MAQnCM,GAAmC,SAAnCA,GAAmC,OAAnCA,EAAmC,2BAAnCA,CAAmC,MC9HxC,MCeMO,GAAkBzyB,IAcxB,IAdyB,UAC9BgV,EAAS,UACT3B,EAAS,iBACTqf,EAAgB,QAChBC,EAAO,eACPC,EAAc,aACdC,GAQD7yB,EACC,MAAM,UACJoyB,EACAlS,QAAS4S,EAAgB,MACzB1S,GCnCkC,SAACwB,GAAoC,IAAlBmR,EAAIvxB,UAAAC,OAAA,QAAAV,IAAAS,UAAA,IAAAA,UAAA,GAC3D,MAAO4wB,EAAWY,IAAgB9iB,EAAAA,EAAAA,eAAqCnP,IAChEmf,EAASC,IAAcjQ,EAAAA,EAAAA,WAAkB,IACzCkQ,EAAOC,IAAYnQ,EAAAA,EAAAA,eAA4BnP,GAEhDkyB,GAAiB/R,EAAAA,EAAAA,cAAY7f,UACjC8e,GAAW,GACX,IACE,MAAMxe,QAAiB0f,EAAAA,EAAc+D,uBAAuBxD,GAExDpN,MAAMC,QAAQ9S,EAASuxB,OACzBF,EAAarxB,GAGboE,EAAAA,EAAM4f,sBAAsB,gCAAkCzkB,KAAKC,UAAkB,OAARQ,QAAQ,IAARA,OAAQ,EAARA,EAAUysB,YAE3F,CAAE,MAAOnmB,GACPoY,EAASpY,EACX,CACAkY,GAAW,EAAM,GAChB,IAQH,OANA9P,EAAAA,EAAAA,YAAU,KACJuR,IAAYmR,GACdE,EAAerR,EACjB,GACC,CAACqR,EAAgBrR,EAASmR,IAEtB,CAAEX,YAAWlS,UAASE,QAC/B,CDOM+S,CACFne,EAEsB,iBAAb,OAAT3B,QAAS,IAATA,OAAS,EAATA,EAAW+B,UAEP,MAAElS,IAAUC,EAAAA,EAAAA,KAKZiwB,GAAwBV,IAAqBrf,GAE3CA,UAAWggB,EAAmBnT,QAASoT,GD9CX,SAACte,GAAuC,IAApBue,IAAO/xB,UAAAC,OAAA,QAAAV,IAAAS,UAAA,KAAAA,UAAA,GAC/D,MAAO6R,EAAWmgB,IAAoBtjB,EAAAA,EAAAA,eAAqCnP,IACpEmf,EAASC,IAAcjQ,EAAAA,EAAAA,UAAkBqjB,IACzCnT,EAAOC,IAAYnQ,EAAAA,EAAAA,eAA4BnP,GAEhD0yB,GAAiBvS,EAAAA,EAAAA,cAAY7f,UACjC,GAAKkyB,EAAL,CAGAlT,OAAStf,GAET,IACE,MAAMY,QAAiB0f,EAAAA,EAAcqS,uBAAuB1e,GAE5D,IAAKrT,EAASgyB,WAEZ,YADAH,OAAiBzyB,GAInByyB,EAAiB7xB,EAASgyB,WAC5B,CAAE,MAAO1rB,GACPoY,EAASpY,EACX,CAAC,QACCkY,GAAW,EACb,CAhBA,CAgBA,GACC,CAACoT,EAASve,IAMb,OAJA3E,EAAAA,EAAAA,YAAU,KACRojB,GAAgB,GACf,CAACA,IAEG,CACLpgB,YACA6M,UACAE,QAEJ,CCUgFwT,CAC5E5e,EACAoe,GAGIS,EAAiBxgB,GAAaggB,EAE9B5qB,GAAQgK,EAAAA,EAAAA,UAAQ,IAChBigB,GAAoBY,GACfjzB,EAAAA,EAAAA,GAACgyB,EAAAA,IAAa,IAEnBwB,GAEAxzB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWiG,MAAK,CAACC,MAAO,EAAG6hB,gBAAc,EAAAxuB,UACvCoP,EAAAA,EAAAA,IAAoBmf,KAIpB7e,GACN,CAED0d,EACAY,EACAO,EACA7e,IAII+e,GAAqBthB,EAAAA,EAAAA,UACzB,IACE2f,EACI,CACE9P,KAAMuR,GAAkB,CAAC,EACzBpG,KAAM2E,QAERrxB,GACN,CAACqxB,EAAWyB,IAGRG,IAA0B,OAAT5B,QAAS,IAATA,OAAS,EAATA,EAAWc,QAAS,IAAIzxB,OAAS,EA0FxD,OACEpB,EAAAA,EAAAA,GAACwd,EAAAA,GAAOC,KAAI,CACVmW,OAAK,EACLlW,MAAI,EACJC,aAAeD,IACRA,GACH4U,GACF,EACArtB,UAEFjF,EAAAA,EAAAA,GAACwd,EAAAA,GAAOI,QAAO,CACbnb,YAAY,uFACZiB,MAAM,OACN0E,MAAOA,EACPyrB,2BAAyB,EAAA5uB,SArGzBwtB,GAAoBJ,GAAoBY,GAExCntB,EAAAA,EAAAA,IAAAL,EAAAA,GAAA,CAAAR,SAAA,EACEjF,EAAAA,EAAAA,GAACgyB,EAAAA,IAAa,KACdhyB,EAAAA,EAAAA,GAACiyB,EAAAA,IAAa,CAACC,MAAO,OAIF,iBAAb,OAATlf,QAAS,IAATA,OAAS,EAATA,EAAW+B,SAEXjP,EAAAA,EAAAA,IAAAL,EAAAA,GAAA,CAAAR,SAAA,EACEjF,EAAAA,EAAAA,GAACke,GAAAA,EAAM,CAACtS,KAAK,QACb5L,EAAAA,EAAAA,GAACivB,EAAAA,IAAK,CACJC,OAAOlvB,EAAAA,EAAAA,GAAC8zB,EAAAA,EAAW,IACnBxJ,aACEtqB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,+FAInB/I,OACEpI,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,kCAQvB4O,GAEAja,EAAAA,EAAAA,IAAAL,EAAAA,GAAA,CAAAR,SAAA,EACEjF,EAAAA,EAAAA,GAACke,GAAAA,EAAM,CAACtS,KAAK,QACb5L,EAAAA,EAAAA,GAACivB,EAAAA,IAAK,CACJC,OAAOlvB,EAAAA,EAAAA,GAACmvB,EAAAA,EAAU,IAClB7E,aACEtqB,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,oGAInB/I,OACEpI,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,eAQtBwiB,EAgBDD,GAGA1zB,EAAAA,EAAAA,GAAA,OACEuD,KAAGC,EAAAA,EAAAA,IAAE,CACHK,OAAQ,eAAehB,EAAMmD,QAAQ6Q,QACrC9K,YAAalJ,EAAMmD,QAAQ8Q,GAC3BrL,aAAc5I,EAAMmD,QAAQ8Q,GAC5BvL,cAAe1I,EAAMmD,QAAQ8Q,IAC9B,IACDyD,QAAU3S,GAAMA,EAAE4S,kBAAkBvV,UAEpCjF,EAAAA,EAAAA,GAAC8wB,GAA+B,CAACC,WAAY2C,EAAkC7vB,OAAO,WAIrF,MA9BHiC,EAAAA,EAAAA,IAAAL,EAAAA,GAAA,CAAAR,SAAA,EACEjF,EAAAA,EAAAA,GAACke,GAAAA,EAAM,CAACtS,KAAK,QACb5L,EAAAA,EAAAA,GAACivB,EAAAA,IAAK,CACJ3E,YAAa,KACbliB,OACEpI,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,mCA6Cb,E,gBE5LX,MCLM4iB,GAA6Bp0B,IAcnC,IAdoC,cACzC6f,EAAa,QACbpR,EAAO,aACP+d,EAAY,gBACZC,EAAe,YACf4H,EAAW,cACXC,GAQDt0B,EACC,MAAMwkB,GAAOC,EAAAA,EAAAA,MACNe,EAAc+O,IAAmBrkB,EAAAA,EAAAA,UAAiB,KAClDmH,EAAWka,IAAgBrhB,EAAAA,EAAAA,WAAS,GACrCskB,GAAiBhe,EAAAA,EAAAA,OAAKie,EAAAA,EAAAA,QAAOjI,GAAe5mB,GAAUA,KA6B5D,OACEO,EAAAA,EAAAA,IAACuuB,GAAAA,EAAK,CACJ5xB,YAAY,iGACZ2F,OACEpI,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,4DAEfiL,OAAQ,CAAEkY,MAAOH,EAAe/yB,UAGpCgN,QAASA,EACTmmB,SAAUP,EACVQ,QACEx0B,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,6DAEfiL,OAAQ,CAAEkY,MAAOH,EAAe/yB,UAGpCqzB,KAxBaC,KAtBU1zB,WACzB,IAAK,IAAD2zB,QAII3T,EAAAA,EAAc4T,aAA6B,QAAjBD,EAACnV,EAAc,UAAE,IAAAmV,EAAAA,EAAI,GAAIR,GAGzD/H,EAAgB,CAAC,GACjB6H,IACAD,GACF,CAAE,MAAOpsB,GACPssB,EACE/P,EAAKG,cAAc,CAAA7b,GAAA,SACjB0I,eAAe,+FAIrB,CACA+f,GAAa,EAAM,EAInB2D,GACA3D,GAAa,EAAK,EAuBhB4D,cAAe,CAAEjV,QAAS7I,EAAW+d,QAAQ,GAAO9vB,SAAA,CAEnDkgB,IAAgBnlB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWoe,UAAS,CAACllB,MAAM,QAAOK,SAAEkgB,KACtDnlB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWoe,UAAS,CAAA7kB,UACnBjF,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWC,KAAI,CAACqpB,MAAI,EAAA/vB,UACnBjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,uEAEfiL,OAAQ,CACNkY,MAAOH,EAAe/yB,eAK9BpB,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWoe,UAAS,CAAA7kB,UACnBjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,6EAIb,ECzFC8jB,GAA4Bt1B,IAYlC,IAZmC,cACxC6f,EAAa,aACb2M,EAAY,gBACZC,EAAe,cACf6H,EAAa,gBACblR,GAODpjB,EACC,MAAOu1B,EAAaC,IAAkBtlB,EAAAA,EAAAA,WAAS,IACzC,MAAEhN,IAAUC,EAAAA,EAAAA,KAEZsyB,GAAYvU,EAAAA,EAAAA,cAAY,KAC5BsU,GAAe,EAAK,GACnB,CAACA,IAEEE,GAAaxU,EAAAA,EAAAA,cAAY,KAC7BsU,GAAe,EAAM,GACpB,CAACA,IAEJ,OACErvB,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT+G,cAAe,MACf9G,WAAY,SACZwB,IAAKlD,EAAMmD,QAAQ6Q,IACpB,IAAC5R,SAAA,EAEFjF,EAAAA,EAAAA,GAACmD,EAAAA,EAAM,CAACV,YAAa,GAAGsgB,+BAA8C5gB,QAASizB,EAAWL,QAAM,EAAA9vB,UAC9FjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,cAInBnR,EAAAA,EAAAA,GAAC+zB,GAA0B,CACzBvU,cAAeA,EACfpR,QAAS8mB,EACT/I,aAAcA,EACd6H,YAAaqB,EACbpB,cAAeA,EACf7H,gBAAiBA,MAEf,ECrCV,MAAMkJ,GAAe31B,IAAuD,IAAtD,gBAAEojB,GAA8CpjB,EACpE,MAAM,MAAEkD,IAAUC,EAAAA,EAAAA,KAElB,OACEgD,EAAAA,EAAAA,IAACyvB,EAAAA,GAAQ9X,KAAI,CACXhb,YAAY,yFACZmxB,OAAO,EAAM3uB,SAAA,EAEbjF,EAAAA,EAAAA,GAACu1B,EAAAA,GAAQtK,QAAO,CAACmF,SAAO,EAAAnrB,UACtBjF,EAAAA,EAAAA,GAACmD,EAAAA,EAAM,CACLyI,KAAK,QACLjJ,KAAK,OACLP,MACEpC,EAAAA,EAAAA,GAACw1B,GAAAA,EAAQ,CACPjyB,KAAGC,EAAAA,EAAAA,IAAE,CACHiyB,IAAK,CAAE/xB,MAAO,GAAIG,OAAQ,GAAIe,MAAO/B,EAAM6B,OAAOI,gBACnD,MAGLrC,YAAa,GAAGsgB,qCAGpBjd,EAAAA,EAAAA,IAACyvB,EAAAA,GAAQ3X,QAAO,CAAA3Y,SAAA,EACdjF,EAAAA,EAAAA,GAACu1B,EAAAA,GAAQG,MAAK,KACd11B,EAAAA,EAAAA,GAAC0L,EAAAA,EAAWoe,UAAS,CAAA7kB,UACnBjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SACf0I,eAAe,0EAEfiL,OAAQ,CAAEuZ,WAAW31B,EAAAA,EAAAA,GAAA,KAAAiF,SAAG,gBAG5BjF,EAAAA,EAAAA,GAACkR,EAAAA,EAAgB,CAAAzI,GAAA,SAAC0I,eAAe,eACjCnR,EAAAA,EAAAA,GAAA,MAAAiF,UACEjF,EAAAA,EAAAA,GAAA,MAAAiF,UACEjF,EAAAA,EAAAA,GAAA,QAAAiF,SAAM,mCAIC,EAEjB,IAAAuM,GAAA,CAAArM,KAAA,UAAAC,OAAA,mBAAAqM,GAAA,CAAAtM,KAAA,SAAAC,OAAA,eAEK,MAAMwwB,GAAqB1wB,IAoB3B,IApB4B,cACjCsa,EAAa,OACbpJ,EAAM,eACNyf,EAAc,aACd1J,EAAY,gBACZC,EAAe,cACf6H,EAAa,gBACblR,EAAe,QACfjS,EAAO,OACP6O,GAWDza,EACC,MAAMif,GAAOC,EAAAA,EAAAA,MACP,MAAEvhB,IAAUC,EAAAA,EAAAA,MAGXgzB,EAAaC,IAAkBlmB,EAAAA,EAAAA,UAA6BuG,QAAU1V,IACtEs1B,EAA2BC,IAA8BpmB,EAAAA,EAAAA,WAAS,GAEnEqmB,EAAkC,OAAXJ,QAAW,IAAXA,EAAAA,EAAe1f,EAEtC+f,EAAqB3uB,OAAOolB,QAAQT,GACvC/V,QAAOtL,IAAA,IAAE,CAAEsrB,GAAWtrB,EAAA,OAAKsrB,CAAU,IACrC7d,KAAIhH,IAAA,IAAE9I,GAAG8I,EAAA,OAAK9I,CAAE,IAGb4tB,EAFoBF,EAAmB/0B,OAAS,GAGpDpB,EAAAA,EAAAA,GAACi1B,GAAyB,CACxBzV,cAAeA,EACf2M,aAAcA,EACdC,gBAAiBA,EACjB6H,cAAeA,EACflR,gBAAiBA,KAGnB/iB,EAAAA,EAAAA,GAACs2B,EAAAA,IAAiB,CAAC/yB,IAAGiO,GAAsBvM,UAC1CjF,EAAAA,EAAAA,GAACu2B,EAAAA,EAAK,CACJ9zB,YAAa,GAAGsgB,+BAChB3H,YAAa+I,EAAKG,cAAc,CAAA7b,GAAA,SAC9B0I,eAAe,kBAGjB5L,MAAO2wB,EAEP3yB,IAAGkO,GACHiB,SAAW9K,GAAMmuB,EAAenuB,EAAE+K,OAAOpN,OACzCixB,QAAQx2B,EAAAA,EAAAA,GAACy2B,EAAAA,EAAU,IACnBC,QAAQ12B,EAAAA,EAAAA,GAACs1B,GAAY,CAACvS,gBAAiBA,IACvC4T,YAAU,EACVtb,QAASA,KACPwa,EAAe,IACfE,OAAer1B,EAAU,EAE3Bk2B,UAAYhvB,IACI,UAAVA,EAAEiD,MACJgrB,EAAeK,GACfH,OAAer1B,GACjB,MAMR,OACEV,EAAAA,EAAAA,GAAA,OAAKuD,KAAGC,EAAAA,EAAAA,IAAE,CAAEc,QAAS,OAAQyB,IAAKlD,EAAMmD,QAAQC,IAAI,IAAChB,SAElDoxB,GACG,E,gBC1HV,MAAMQ,GAAkE,CACtExK,cAAe,CAAC5X,EAAAA,GAAiCI,UAAWJ,EAAAA,GAAiC5N,SAgBlFiwB,GAAkCtX,IAC7C,MAAMuX,GAAa3kB,EAAAA,EAAAA,UAAQ,KACzB,MAAM4kB,EAAwBn2B,KAAKC,UAAU0e,EAAcyX,QAAQ5gB,QACnE,OAAOhN,GAAAA,EAAkBC,qBAAqB,uBAAwB0tB,EAAsB,GAC3F,CAACxX,KAEG0X,EAASC,IAActnB,EAAAA,EAAAA,WAAsC,IAnB7BknB,KACvC,IACE,MAAMK,EAAaL,EAAW1sB,QAAQ,WAChC6sB,EAAUr2B,KAAK+F,MAAMwwB,GAC3B,OAAKlqB,EAAAA,EAAAA,UAASgqB,GAGPA,EAFEL,EAGX,CAAE,MAAOjvB,GACP,OAAOivB,EACT,GAUEQ,CAAgCN,KAG5BxK,GAAqB1L,EAAAA,EAAAA,cAAayW,IACtCH,GAAYI,IACV,MAAMlL,EAAgBkL,EAAYlL,eAAiB,GACnD,MAAO,CACLA,cAAeA,EAAcpS,SAASqd,GAClCjL,EAAcjW,QAAQ3N,GAAOA,IAAO6uB,IACpC,IAAIjL,EAAeiL,GACxB,GACD,GACD,IAMH,OAJAtnB,EAAAA,EAAAA,YAAU,KACR+mB,EAAWpsB,QAAQ,UAAW9J,KAAKC,UAAUo2B,GAAS,GACrD,CAACH,EAAYG,IAET,CAAEA,UAAS3K,qBAAoB,EClDlCiL,GAAkB,kBCAlBA,GAAkB,iBCelBC,GAA+B,CAAC,CAAEhvB,GAAIgM,EAAAA,GAAiCK,YAAasL,MAAM,IAEnFsX,GAAa/3B,IAyBnB,IAzBoB,cACzB6f,EAAa,QACb1O,EAAO,cACP4O,EAAa,gBACb8M,EAAe,gBACfzJ,GAAkBjS,EAAU,oBAAsB,kCAoBnDnR,EACC,MAAMg4B,GAAargB,EAAAA,EAAAA,aAA2B5W,IACvC0V,EAAQ2E,IAAalL,EAAAA,EAAAA,UAAiB,KACtC4P,EAASyM,IAAcrc,EAAAA,EAAAA,UAAuB4nB,KAC9CtL,EAAcC,IAAmBvc,EAAAA,EAAAA,UAAoC,CAAC,IAEtE+nB,EAAiBC,GF1CcC,MAAO,IAADC,EAE5C,MAAOC,EAAcC,IAAmBC,EAAAA,EAAAA,MAElCN,EAAmD,QAApCG,EAAGC,EAAaG,IAAIX,WAAgB,IAAAO,EAAAA,OAAIr3B,EAEvDm3B,GAAqBhX,EAAAA,EAAAA,cACxB+W,IACCK,GAAiBG,QACS13B,IAApBk3B,GACFQ,EAAOC,OAAOb,IACPY,IAETA,EAAOE,IAAId,GAAiBI,GACrBQ,IACP,GAEJ,CAACH,IAGH,MAAO,CAACL,EAAiBC,EAAmB,EEsBEC,IACvCvF,EAAgBgG,GD3CcC,MAAO,IAADT,EAC3C,MAAOC,EAAcC,IAAmBC,EAAAA,EAAAA,MAElC3F,EAAkD,QAApCwF,EAAGC,EAAaG,IAAIX,WAAgB,IAAAO,EAAAA,OAAIr3B,EAEtD63B,GAAoB1X,EAAAA,EAAAA,cACvB0R,IACC0F,GACGG,QACwB13B,IAAnB6xB,GACF6F,EAAOC,OAAOb,IACPY,IAETA,EAAOE,IAAId,GAAiBjF,GACrB6F,IAET,CAAEK,SAAS,GACZ,GAEH,CAACR,IAGH,MAAO,CAAC1F,EAAgBgG,EAAkB,ECqBEC,IAEtC,OAAE7Y,EAAM,QAAEE,EAAO,MAAEE,EAAK,YAAEuC,EAAW,gBAAEC,EAAe,cAAEE,EAAa,cAAEE,EAAa,mBAAEC,GAC1FrD,EAAoB,CAClBC,gBACAC,UACArJ,SACAtF,UACA4O,kBAGE0L,GAAiBvK,EAAAA,EAAAA,cACrB3b,IAAA,IAAC,WAAEoP,GAA4BpP,EAAA,OAAK2yB,EAAmBvjB,EAAW,GAClE,CAACujB,IAOG/L,GAAajL,EAAAA,EAAAA,cAAY,KAC7B4B,IACA2J,EAAgB,CAAC,EAAE,GAClB,CAAC3J,IAEEsJ,GAAiBlL,EAAAA,EAAAA,cAAY,KACjC8B,IACAyJ,EAAgB,CAAC,EAAE,GAClB,CAACzJ,KAGJ3S,EAAAA,EAAAA,YAAU,KAERhG,OAAO0uB,aAAaf,EAAW7hB,SAE/B,MAAM6iB,EAAkB33B,UAGlB6e,GAAW0C,UAETK,GAAmB,GAEzB5Y,OAAO0uB,aAAaf,EAAW7hB,SAC/B6hB,EAAW7hB,QAAU9L,OAAO4uB,WAAWD,EA/EF,KA+E+C,EAItF,OADAhB,EAAW7hB,QAAU9L,OAAO4uB,WAAWD,EAlFA,KAmFhC,IAAM3uB,OAAO0uB,aAAaf,EAAW7hB,QAAQ,GACnD,CAAC8M,EAAoB/C,EAAS0C,IAEjC,MAAM,MAAE1f,IAAUC,EAAAA,EAAAA,KAGZ+1B,GAAoBzmB,EAAAA,EAAAA,UAAQ,KAChC,GAAKwlB,EACL,OAAOjY,EAAOxU,MAAMmW,GAAUA,EAAMhN,aAAesjB,GAAgB,GAClE,CAACA,EAAiBjY,KAEf,QAEJuX,EAAO,mBACP3K,GACEuK,GAA+BtX,GAE7BsZ,GAAkB1mB,EAAAA,EAAAA,UACtB,KAAMoP,EAAAA,EAAAA,OAAKuX,EAAAA,EAAAA,SAAQpZ,EAAOqZ,SAAS1X,IAAK,IAAA2X,EAAA,OAAe,QAAfA,EAAK3X,EAAM9b,YAAI,IAAAyzB,OAAA,EAAVA,EAAY1gB,KAAK2E,GAAQA,EAAIrS,KAAI,OAC9E,CAAC8U,KAGG,0BAAEuZ,EAAyB,cAAEC,GP7GKx5B,KAQnC,IARoC,UACzCy5B,EAAS,gBACTN,EAAkB,GAAE,UACpBO,GAKD15B,EACC,MAAM,kBAAE25B,EAAiB,cAAEH,IAAkBI,EAAAA,GAAAA,GAA2C,CACtFC,gBAAiBx4B,MAAOy4B,EAAcC,EAAcC,KAClD,IAAKF,EAAaG,eAChB,OAEF,MAAMjlB,EAAY8kB,EAAaG,eAEzBC,EAAsBF,EAAQvjB,QAClClR,IAAA,IAAG2F,IAAKivB,EAAWv0B,MAAOw0B,GAAa70B,EAAA,OACpCw0B,EAAaM,MACZlvB,IAAA,IAAGD,IAAKovB,EAAgB10B,MAAO20B,GAAkBpvB,EAAA,OAC/CmvB,IAAmBH,GAAaC,IAAgBG,CAAgB,GACnE,IAICC,EAAcT,EAAatjB,QAC/B7E,IAAA,IAAG1G,IAAKovB,GAAgB1oB,EAAA,OAAMooB,EAAQK,MAAKxoB,IAAA,IAAG3G,IAAKivB,GAAWtoB,EAAA,OAAKyoB,IAAmBH,CAAS,GAAC,IAI5FM,EAAiBC,QAAQC,IAAI,IAC9BT,EAAoBthB,KAAI9G,IAAA,IAAC,IAAE5G,EAAG,MAAEtF,GAAOkM,EAAA,OACxC4nB,EACIrY,EAAAA,EAAcuZ,wBAAwB5lB,EAAW9J,EAAKtF,GACtDyb,EAAAA,EAAcwZ,sBAAsB7lB,EAAW9J,EAAKtF,EAAM,OAE7D40B,EAAY5hB,KAAIW,IAAA,IAAC,IAAErO,GAAKqO,EAAA,OACzBmgB,EACIrY,EAAAA,EAAcyZ,2BAA2B9lB,EAAW9J,GACpDmW,EAAAA,EAAc0Z,yBAAyB/lB,EAAW9J,EAAI,MAI9D,OAAOuvB,CAAc,EAEvBO,eAAe,EACfC,iBAAkB9B,EAAgB1iB,QAAQykB,GAAWA,IAAWA,EAAO5X,WAAWC,EAAAA,MAClFkW,UAAWA,IAiBb,MAAO,CACLF,2BAfgCrY,EAAAA,EAAAA,cAC/BS,IAA2B,IAAD2X,EACzB,IAAK3X,EAAMhN,WACT,OAEF,MAAMwmB,GAAwB,QAAV7B,EAAA3X,EAAM9b,YAAI,IAAAyzB,OAAA,EAAVA,EAAY7iB,QAAOuE,IAAA,IAAC,IAAE9P,GAAK8P,EAAA,OAAK9P,IAAQA,EAAIoY,WAAWC,EAAAA,GAAuB,MAAK,GACvGoW,EAAkB,CAChBM,eAAgBtY,EAAMhN,WACtB9O,KAAMs1B,GAAe,IACrB,GAEJ,CAACxB,IAKDH,gBACD,EO0CoD4B,CAA2B,CAC9E3B,UAAWA,IAAMxW,GAAmB,GACpCkW,oBAGI9M,EAA0B,KAAX5V,EAEf4kB,EAA6Brb,EAAOqa,MAAM1Y,KAAW3N,EAAAA,EAAAA,QAAMH,EAAAA,EAAAA,IAAwB8N,MAGnF2Z,GAAsB7oB,EAAAA,EAAAA,UAC1B,IAAQ4oB,EAA8E,GAAjD,CAACvmB,EAAAA,GAAiCQ,cACvE,CAAC+lB,IAIGE,GAAqB9oB,EAAAA,EAAAA,UACzB,IAAM,IAAoB,OAAfoa,QAAe,IAAfA,EAAAA,EAAmB,MAAQyO,IACtC,CAACzO,EAAiByO,IAGdE,GAAmB/oB,EAAAA,EAAAA,UACvB,SAAAgpB,EAAA,MAAM,IAA0B,QAAzBA,EAAIlE,EAAQ7K,qBAAa,IAAA+O,EAAAA,EAAI,MAAQF,EAAmB,GAC/D,CAAChE,EAASgE,IAGZ,OACEp1B,EAAAA,EAAAA,IAAA,OACEvC,KAAGC,EAAAA,EAAAA,IAAE,CACHc,QAAS,OACT+G,cAAe,SACftF,IAAKlD,EAAMmD,QAAQ6Q,GACnBhT,OAAQ,OACR4I,SAAU,UACX,IAACxH,SAAA,EAEFjF,EAAAA,EAAAA,GAAC41B,GAAkB,CACjBpW,cAAeA,EACfpJ,OAAQA,EACRyf,eAAgB9a,EAChBoR,aAAcA,EACdC,gBAAiBA,EACjB6H,cAAerR,EACfG,gBAAiBA,EACjBjS,QAASA,EACT6O,OAAQA,KAEV3f,EAAAA,EAAAA,GAAC6rB,GAAe,CACdrM,cAAeA,EACf1O,QAASA,EACT6O,OAAQA,EACRE,QAASA,EACTE,MAAOA,EACPqL,eAAgBA,EAChBK,gBAAiByN,EACjB5W,YAAaA,EACbC,gBAAiBA,EACjBwJ,eAAgBA,EAChBD,WAAYA,EACZuP,cAAezY,EACfoJ,aAAcA,EACdC,eAAgBA,IAAMlR,EAAU,IAChCsR,cAAe8O,EACf7O,oBAAqB0O,EACrBxO,gBAAiB0O,EACjBhP,WAAaoP,IAGX,IAAIC,EAAAA,EAAAA,YAAWD,GACb,OAAOpP,GAAYsP,IACjB,MAAMC,EAAWH,EAAcE,GACzBE,EAAgBF,EAAa,GACnC,OAAMC,GAAgC,IAApBA,EAASr6B,SAAiBs6B,EAGrCD,EAFE,CAAC,CAAEhzB,GAAIizB,EAAcjzB,GAAI2X,MAAOsb,EAActb,MAExC,GAEnB,EAEFX,QAASA,EACT0M,aAAcA,EACdC,gBAAiBA,EACjBrJ,gBAAiBA,EACjBwJ,mBAAoBA,IAErBqL,IACC53B,EAAAA,EAAAA,GAACoyB,GAAe,CACdpf,UAAW6lB,EACXxG,iBAAkBxS,EAClBlL,UAAWijB,EACXtF,QAASA,IAAMuF,OAAmBn3B,GAClC6xB,eAAgBA,EAChBC,aAAc+F,IAGjBY,IACG,C", "sources": ["experiment-tracking/components/ExperimentSourceTypeIcon.tsx", "experiment-tracking/hooks/logged-models/request.utils.ts", "common/components/ToggleIconButton.tsx", "experiment-tracking/components/experiment-page/components/runs/cells/SourceCellRenderer.tsx", "experiment-tracking/utils/DatasetUtils.ts", "common/static/registered-model-grey-ok.svg", "common/utils/LocalStorageUtils.ts", "experiment-tracking/components/experiment-page/components/runs/ExperimentViewDatasetWithContext.tsx", "common/components/JsonFormatting.tsx", "shared/building_blocks/Image.tsx", "experiment-tracking/components/traces/quickstart/TracesViewTableNoTracesQuickstartContext.tsx", "experiment-tracking/components/runs-charts/components/charts/ImageGridPlot.common.tsx", "experiment-tracking/components/experiment-page/components/RunColorPill.tsx", "experiment-tracking/components/traces/TracesView.utils.ts", "shared/web-shared/utils/coerceToEnum.ts", "shared/web-shared/utils/unified-details/useResponsiveContainer.ts", "shared/web-shared/utils/unified-details/index.tsx", "experiment-tracking/components/experiment-page/components/runs/ExperimentViewDatasetSchemaTable.tsx", "experiment-tracking/components/experiment-page/components/runs/ExperimentViewDatasetSchema.tsx", "experiment-tracking/components/experiment-page/components/runs/ExperimentViewDatasetLink.tsx", "experiment-tracking/components/experiment-page/components/runs/ExperimentViewDatasetSourceType.tsx", "experiment-tracking/components/experiment-page/components/runs/ExperimentViewDatasetSourceURL.tsx", "experiment-tracking/components/experiment-page/components/runs/ExperimentViewDatasetDigest.tsx", "experiment-tracking/components/experiment-page/components/runs/ExperimentViewDatasetDrawer.tsx", "experiment-tracking/components/traces/hooks/useExperimentTraces.tsx", "experiment-tracking/components/traces/TracesViewTableTagCell.tsx", "experiment-tracking/components/traces/TracesViewTableStatusCell.tsx", "experiment-tracking/components/traces/TracesViewTablePreviewCell.tsx", "experiment-tracking/components/traces/TracesViewTableSourceCell.tsx", "experiment-tracking/components/traces/TracesViewTable.utils.ts", "experiment-tracking/components/traces/TracesViewTableRow.tsx", "experiment-tracking/components/traces/TracesViewTableTimestampCell.tsx", "experiment-tracking/components/traces/TracesViewTableHeaderCheckbox.tsx", "experiment-tracking/components/traces/TracesViewTableCellCheckbox.tsx", "experiment-tracking/components/traces/quickstart/TraceTableQuickstart.utils.tsx", "experiment-tracking/components/traces/quickstart/TraceTableGenericQuickstart.tsx", "experiment-tracking/components/traces/quickstart/TracesViewTableNoTracesQuickstart.tsx", "experiment-tracking/components/traces/TracesViewTable.tsx", "shared/web-shared/model-trace-explorer/ModelTraceExplorerFrameRenderer.tsx", "shared/web-shared/model-trace-explorer/index.ts", "experiment-tracking/components/traces/hooks/useExperimentTraceInfo.tsx", "experiment-tracking/components/traces/TraceDataDrawer.tsx", "experiment-tracking/components/traces/hooks/useExperimentTraceData.tsx", "experiment-tracking/components/traces/hooks/useEditExperimentTraceTags.tsx", "experiment-tracking/components/traces/TracesViewDeleteTraceModal.tsx", "experiment-tracking/components/traces/TracesViewControlsActions.tsx", "experiment-tracking/components/traces/TracesViewControls.tsx", "experiment-tracking/components/traces/hooks/useExperimentViewTracesUIState.tsx", "experiment-tracking/components/traces/hooks/useActiveExperimentTrace.tsx", "experiment-tracking/components/traces/hooks/useActiveExperimentSpan.tsx", "experiment-tracking/components/traces/TracesView.tsx"], "sourcesContent": ["import { FileCodeIcon, FolderBranchIcon, NotebookIcon, WorkflowsIcon } from '@databricks/design-system';\nimport { SourceType } from '../sdk/MlflowEnums';\n\n/**\n * Displays an icon corresponding to the source type of an experiment run.\n */\nexport const ExperimentSourceTypeIcon = ({\n  sourceType,\n  className,\n}: {\n  sourceType: SourceType | string;\n  className?: string;\n}) => {\n  if (sourceType === SourceType.NOTEBOOK) {\n    return <NotebookIcon className={className} />;\n  } else if (sourceType === SourceType.LOCAL) {\n    return <FileCodeIcon className={className} />;\n  } else if (sourceType === SourceType.PROJECT) {\n    return <FolderBranchIcon className={className} />;\n  } else if (sourceType === SourceType.JOB) {\n    return <WorkflowsIcon className={className} />;\n  }\n  return null;\n};\n", "import { matchPredefinedError } from '@databricks/web-shared/errors';\n\nfunction serializeRequestBody(payload: any | FormData | Blob) {\n  if (payload === undefined) {\n    return undefined;\n  }\n  return typeof payload === 'string' || payload instanceof FormData || payload instanceof Blob\n    ? payload\n    : JSON.stringify(payload);\n}\n\n// Helper method to make a request to the backend.\nexport const loggedModelsDataRequest = async (\n  url: string,\n  method: 'POST' | 'GET' | 'PATCH' | 'DELETE' = 'GET',\n  body?: any,\n) => {\n  const response = await fetch(url, {\n    method,\n    body: serializeRequestBody(body),\n    headers: body ? { 'Content-Type': 'application/json' } : {},\n  });\n  if (!response.ok) {\n    const predefinedError = matchPredefinedError(response);\n    if (predefinedError) {\n      try {\n        // Attempt to use message from the response\n        const message = (await response.json()).message;\n        predefinedError.message = message ?? predefinedError.message;\n      } catch {\n        // If the message can't be parsed, use default one\n      }\n      throw predefinedError;\n    }\n  }\n  return response.json();\n};\n", "import React from 'react';\n\nimport type { ButtonProps } from '@databricks/design-system';\nimport {\n  DesignSystemEventProviderAnalyticsEventTypes,\n  DesignSystemEventProviderComponentTypes,\n  useDesignSystemEventComponentCallbacks,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\n\nexport interface ToggleIconButtonProps extends ButtonProps {\n  pressed?: boolean;\n}\n\n/**\n * WARNING: Temporary component!\n *\n * This component recreates \"Toggle button with icon\" pattern which is not\n * available in the design system yet.\n *\n * TODO: replace this component with the one from DuBois design system when available.\n */\nconst ToggleIconButton = React.forwardRef<HTMLButtonElement, ToggleIconButtonProps>(\n  (props: ToggleIconButtonProps, ref) => {\n    const {\n      pressed,\n      onClick,\n      icon,\n      onBlur,\n      onFocus,\n      onMouseEnter,\n      onMouseLeave,\n      componentId,\n      analyticsEvents,\n      type,\n      ...remainingProps\n    } = props;\n    const { theme } = useDesignSystemTheme();\n\n    const eventContext = useDesignSystemEventComponentCallbacks({\n      componentType: DesignSystemEventProviderComponentTypes.Button,\n      componentId,\n      analyticsEvents: analyticsEvents ?? [DesignSystemEventProviderAnalyticsEventTypes.OnClick],\n    });\n\n    return (\n      <button\n        onClick={(event) => {\n          eventContext.onClick(event);\n          onClick?.(event);\n        }}\n        css={{\n          cursor: 'pointer',\n          width: theme.general.heightSm,\n          height: theme.general.heightSm,\n          borderRadius: theme.legacyBorders.borderRadiusMd,\n          lineHeight: theme.typography.lineHeightBase,\n          padding: 0,\n          border: 0,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: pressed ? theme.colors.actionDefaultBackgroundPress : 'transparent',\n          color: pressed ? theme.colors.actionDefaultTextPress : theme.colors.textSecondary,\n          '&:hover': {\n            background: theme.colors.actionDefaultBackgroundHover,\n            color: theme.colors.actionDefaultTextHover,\n          },\n        }}\n        ref={ref}\n        onBlur={onBlur}\n        onFocus={onFocus}\n        onMouseEnter={onMouseEnter}\n        onMouseLeave={onMouseLeave}\n        {...remainingProps}\n      >\n        {icon}\n      </button>\n    );\n  },\n);\n\nexport { ToggleIconButton };\n", "import React from 'react';\nimport Utils from '../../../../../../common/utils/Utils';\nimport { RunRowType } from '../../../utils/experimentPage.row-types';\nimport { useDesignSystemTheme } from '@databricks/design-system';\nimport { ExperimentSourceTypeIcon } from '../../../../ExperimentSourceTypeIcon';\n\nexport const SourceCellRenderer = React.memo(({ value: tags }: { value: RunRowType['tags'] }) => {\n  const { theme } = useDesignSystemTheme();\n  if (!tags) {\n    return <>-</>;\n  }\n  const sourceType = tags[Utils.sourceTypeTag]?.value || '';\n\n  const sourceLink = Utils.renderSource(tags || {}, undefined, undefined);\n  return sourceLink ? (\n    <div css={{ display: 'flex', gap: theme.spacing.xs, alignItems: 'center' }}>\n      <ExperimentSourceTypeIcon sourceType={sourceType} css={{ color: theme.colors.textSecondary }} />\n      <span css={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>{sourceLink}</span>\n    </div>\n  ) : (\n    <>-</>\n  );\n});\n", "import { DatasetSourceTypes, type DatasetSummary, type RunDatasetWithTags } from '../types';\n\nexport const datasetSummariesEqual = (summary1: DatasetSummary, summary2: DatasetSummary) =>\n  summary1.digest === summary2.digest && summary1.name === summary2.name && summary1.context === summary2.context;\n\nexport const getDatasetSourceUrl = (datasetWithTags: RunDatasetWithTags) => {\n  const { dataset } = datasetWithTags;\n  const sourceType = dataset.sourceType;\n  try {\n    if (sourceType === DatasetSourceTypes.HTTP) {\n      const { url } = JSON.parse(dataset.source);\n      return url;\n    }\n    if (sourceType === DatasetSourceTypes.S3) {\n      const { uri } = JSON.parse(dataset.source);\n      return uri;\n    }\n    if (sourceType === DatasetSourceTypes.HUGGING_FACE) {\n      const { path } = JSON.parse(dataset.source);\n      return `https://huggingface.co/datasets/${path}`;\n    }\n  } catch {\n    return null;\n  }\n  return null;\n};\n", "var _path, _g, _path2, _path3, _defs;\nfunction _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }\nimport * as React from \"react\";\nfunction SvgRegisteredModelGreyOk(_ref, svgRef) {\n  let {\n    title,\n    titleId,\n    ...props\n  } = _ref;\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    width: 19,\n    height: 16,\n    viewBox: \"0 0 19 16\",\n    fill: \"none\",\n    xmlns: \"http://www.w3.org/2000/svg\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"g\", {\n    clipPath: \"url(#clip0_0_3)\"\n  }, /*#__PURE__*/React.createElement(\"mask\", {\n    id: \"mask0_0_3\",\n    style: {\n      maskType: \"luminance\"\n    },\n    maskUnits: \"userSpaceOnUse\",\n    x: 0,\n    y: 0,\n    width: 16,\n    height: 16\n  }, _path || (_path = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M16 0H0V16H16V0Z\",\n    fill: \"white\"\n  }))), _g || (_g = /*#__PURE__*/React.createElement(\"g\", {\n    mask: \"url(#mask0_0_3)\"\n  }, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    clipRule: \"evenodd\",\n    d: \"M2.75 3.49999C2.05964 3.49999 1.5 4.05963 1.5 4.74999C1.5 5.44034 2.05964 5.99999 2.75 5.99999C3.44036 5.99999 4 5.44034 4 4.74999C4 4.05963 3.44036 3.49999 2.75 3.49999ZM0 4.74999C0 3.2312 1.23122 1.99999 2.75 1.99999C3.77682 1.99999 4.6722 2.56276 5.14452 3.39669L9.51655 2.44626C9.66772 1.0704 10.8338 0 12.25 0C13.7688 0 15 1.23122 15 2.75C15 3.87686 14.3222 4.84541 13.3521 5.27025L13.6341 7.52661C14.9711 7.71349 16 8.86158 16 10.25C16 11.7688 14.7687 13 13.25 13C12.3895 13 11.6214 12.6048 11.1172 11.9861L8.49749 13.1322C8.49913 13.1713 8.49997 13.2105 8.49997 13.25C8.49996 14.7688 7.26875 16 5.74997 16C4.23118 16 2.99997 14.7688 2.99996 13.25C2.99997 12.3569 3.42568 11.5633 4.08524 11.0609L3.01322 7.48755C2.92659 7.49578 2.83878 7.49999 2.75 7.49999C1.23122 7.49999 0 6.26877 0 4.74999ZM5.46534 5.18782C5.48277 5.07884 5.4938 4.96773 5.49804 4.85488L9.76409 3.92748C10.1528 4.74671 10.9346 5.34321 11.8658 5.47338L12.1478 7.72974C11.7709 7.89483 11.438 8.14204 11.1719 8.44873L5.46534 5.18782ZM4.82802 6.55126C4.70399 6.69422 4.56546 6.82424 4.41471 6.93906L5.48674 10.5124C5.57337 10.5042 5.66118 10.5 5.74997 10.5C6.69483 10.5 7.52839 10.9765 8.02345 11.7023L10.5231 10.6087C10.5079 10.4913 10.5 10.3716 10.5 10.25C10.5 10.101 10.5118 9.95475 10.5346 9.81218L4.82802 6.55126ZM12 10.25C12 9.55963 12.5596 8.99999 13.25 8.99999C13.9403 8.99999 14.5 9.55963 14.5 10.25C14.5 10.9403 13.9403 11.5 13.25 11.5C12.5596 11.5 12 10.9403 12 10.25ZM5.74997 12C5.05961 12 4.49997 12.5596 4.49997 13.25C4.49997 13.9403 5.05961 14.5 5.74997 14.5C6.44032 14.5 6.99997 13.9403 6.99997 13.25C6.99997 12.5596 6.44032 12 5.74997 12ZM11 2.75C11 2.05964 11.5596 1.5 12.25 1.5C12.9403 1.5 13.5 2.05964 13.5 2.75C13.5 3.44036 12.9403 4 12.25 4C11.5596 4 11 3.44036 11 2.75Z\",\n    fill: \"currentColor\"\n  }))), _path2 || (_path2 = /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M13.5 14C15.433 14 17 12.433 17 10.5C17 8.567 15.433 7 13.5 7C11.567 7 10 8.567 10 10.5C10 12.433 11.567 14 13.5 14Z\",\n    fill: \"white\"\n  })), _path3 || (_path3 = /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    clipRule: \"evenodd\",\n    d: \"M11.125 11C11.125 9.41218 12.4122 8.125 14 8.125C15.5878 8.125 16.875 9.41218 16.875 11C16.875 12.5878 15.5878 13.875 14 13.875C12.4122 13.875 11.125 12.5878 11.125 11ZM14 6.625C11.5838 6.625 9.625 8.58375 9.625 11C9.625 13.4162 11.5838 15.375 14 15.375C16.4162 15.375 18.375 13.4162 18.375 11C18.375 8.58375 16.4162 6.625 14 6.625ZM14.0303 12.5303L16.0303 10.5303L14.9697 9.46967L13.5 10.9393L13.0303 10.4697L11.9697 11.5303L12.9697 12.5303L13.5 13.0607L14.0303 12.5303Z\",\n    fill: \"#3CAA60\"\n  }))), _defs || (_defs = /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"clipPath\", {\n    id: \"clip0_0_3\"\n  }, /*#__PURE__*/React.createElement(\"rect\", {\n    width: 19,\n    height: 16,\n    fill: \"white\"\n  })))));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(SvgRegisteredModelGreyOk);\nexport default __webpack_public_path__ + \"static/media/registered-model-grey-ok.8274b58d39504c8d1b8c358aa1c9aa35.svg\";\nexport { ForwardRef as ReactComponent };", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\n/**\n * Utils for working with local storage.\n */\nexport default class LocalStorageUtils {\n  /**\n   * Protocol version of MLflow's local storage. Should be incremented on any breaking change in how\n   * data persisted in local storage is used, to prevent old (invalid) cached data from being loaded\n   * and breaking the application.\n   */\n  static version = '1.1';\n\n  /**\n   * Return a LocalStorageStore corresponding to the specified component and ID, where the ID\n   * can be used to disambiguate between multiple instances of cached data for the same component\n   * (e.g. cached data for multiple experiments).\n   */\n  static getStoreForComponent(componentName: any, id: any) {\n    return new LocalStorageStore([componentName, id].join('-'), 'localStorage');\n  }\n\n  static getSessionScopedStoreForComponent(componentName: any, id: any) {\n    return new LocalStorageStore([componentName, id].join('-'), 'sessionStorage');\n  }\n}\n\n/**\n * Interface to browser local storage that allows for setting key-value pairs under the specified\n * \"scope\".\n */\nclass LocalStorageStore {\n  constructor(scope: any, type: any) {\n    this.scope = scope;\n    if (type === 'localStorage') {\n      this.storageObj = window.localStorage;\n    } else {\n      this.storageObj = window.sessionStorage;\n    }\n  }\n  static reactComponentStateKey = 'ReactComponentState';\n\n  scope: any;\n  storageObj: any;\n\n  /**\n   * Loads React component state cached in local storage into a vanilla JS object.\n   */\n  loadComponentState() {\n    const storedVal = this.getItem(LocalStorageStore.reactComponentStateKey);\n    if (storedVal) {\n      return JSON.parse(storedVal);\n    }\n    return {};\n  }\n\n  /**\n   * Save React component state in local storage.\n   * @param stateRecord: Immutable.Record instance or plain object containing component state.\n   */\n  saveComponentState(stateRecord: any) {\n    const targetValue = typeof stateRecord.toJSON === 'function' ? stateRecord.toJSON() : stateRecord;\n    this.setItem(LocalStorageStore.reactComponentStateKey, JSON.stringify(targetValue));\n  }\n\n  /**\n   * Helper method for constructing a scoped key to use for setting/getting values in\n   * local storage.\n   */\n  withScopePrefix(key: any) {\n    return ['MLflowLocalStorage', LocalStorageUtils.version, this.scope, key].join('-');\n  }\n\n  /** Save the specified key-value pair in local storage. */\n  setItem(key: any, value: any) {\n    this.storageObj.setItem(this.withScopePrefix(key), value);\n  }\n\n  /** Fetch the value corresponding to the passed-in key from local storage. */\n  getItem(key: any) {\n    return this.storageObj.getItem(this.withScopePrefix(key));\n  }\n}\n", "import { TableIcon, Tag, Typography, useDesignSystemTheme } from '@databricks/design-system';\nimport type { RunDatasetWithTags } from '../../../../types';\nimport React from 'react';\nimport { MLFLOW_RUN_DATASET_CONTEXT_TAG } from '../../../../constants';\n\nexport interface DatasetWithContextProps {\n  datasetWithTags: RunDatasetWithTags;\n  displayTextAsLink: boolean;\n  className?: string;\n}\n\nexport const ExperimentViewDatasetWithContext = ({\n  datasetWithTags,\n  displayTextAsLink,\n  className,\n}: DatasetWithContextProps) => {\n  const { dataset, tags } = datasetWithTags;\n  const { theme } = useDesignSystemTheme();\n\n  const contextTag = tags?.find(({ key }) => key === MLFLOW_RUN_DATASET_CONTEXT_TAG)?.value;\n\n  return (\n    <div\n      css={{\n        display: 'flex',\n        flexDirection: 'row',\n        alignItems: 'center',\n        marginTop: theme.spacing.xs,\n        marginBottom: theme.spacing.xs,\n      }}\n      className={className}\n    >\n      <TableIcon css={{ marginRight: theme.spacing.xs, color: theme.colors.textSecondary }} />\n      {displayTextAsLink ? (\n        <div>\n          {dataset.name} ({dataset.digest})\n        </div>\n      ) : (\n        <Typography.Text size=\"md\" css={{ marginBottom: 0 }}>\n          {dataset.name} ({dataset.digest})\n        </Typography.Text>\n      )}\n      {contextTag && (\n        <Tag\n          componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetwithcontext.tsx_41\"\n          css={{\n            textTransform: 'capitalize',\n            marginLeft: theme.spacing.xs,\n            marginRight: theme.spacing.xs,\n          }}\n        >\n          {contextTag}\n        </Tag>\n      )}\n    </div>\n  );\n};\n", "import React from 'react';\nimport { CodeSnippet } from '@databricks/web-shared/snippet';\nimport { isObject } from 'lodash';\n\ninterface JsonPreviewProps {\n  json: string;\n  wrapperStyle?: React.CSSProperties;\n  overlayStyle?: React.CSSProperties;\n  codeSnippetStyle?: React.CSSProperties;\n}\n\nexport const JsonPreview: React.FC<JsonPreviewProps> = ({ json, wrapperStyle, overlayStyle, codeSnippetStyle }) => {\n  const { formattedJson, isJsonContent } = useFormattedJson(json);\n\n  const defaultWrapperStyle: React.CSSProperties = {\n    position: 'relative',\n    maxHeight: 'calc(1.5em * 9)',\n    overflow: 'hidden',\n  };\n\n  const defaultOverlayStyle: React.CSSProperties = {\n    position: 'absolute',\n    bottom: 0,\n    right: 0,\n    left: 6,\n    height: '2em',\n    background: 'linear-gradient(transparent, white)',\n  };\n\n  const defaultCodeSnippetStyle: React.CSSProperties = {\n    padding: '5px',\n    overflowX: 'hidden',\n  };\n\n  return (\n    <div style={{ ...defaultWrapperStyle, ...wrapperStyle }}>\n      {isJsonContent ? (\n        <>\n          <CodeSnippet language=\"json\" style={{ ...defaultCodeSnippetStyle, ...codeSnippetStyle }}>\n            {formattedJson}\n          </CodeSnippet>\n          <div css={{ ...defaultOverlayStyle, ...overlayStyle }}></div>\n        </>\n      ) : (\n        <>{json}</>\n      )}\n    </div>\n  );\n};\n\nfunction useFormattedJson(json: string) {\n  return React.useMemo(() => {\n    try {\n      const parsed = JSON.parse(json);\n      const isJson = isObject(parsed) && typeof parsed !== 'function' && !(parsed instanceof Date);\n      return {\n        formattedJson: isJson ? JSON.stringify(parsed, null, 2) : json,\n        isJsonContent: isJson,\n      };\n    } catch (e) {\n      return {\n        formattedJson: json,\n        isJsonContent: false,\n      };\n    }\n  }, [json]);\n}\n\nexport const FormattedJsonDisplay: React.FC<{ json: string }> = ({ json }) => {\n  const { formattedJson, isJsonContent } = useFormattedJson(json);\n\n  return (\n    <div css={{ whiteSpace: 'pre-wrap' }}>\n      {isJsonContent ? (\n        <CodeSnippet language=\"json\" wrapLongLines>\n          {formattedJson}\n        </CodeSnippet>\n      ) : (\n        <span>{json}</span>\n      )}\n    </div>\n  );\n};\n", "import {\n  ArrowLeftIcon,\n  ArrowRightIcon,\n  CloseIcon,\n  DesignSystemContext,\n  RedoIcon,\n  UndoIcon,\n  ZoomInIcon,\n  ZoomOutIcon,\n} from '@databricks/design-system';\nimport { useContext } from 'react';\nimport RcImage from 'rc-image';\nimport './Image.css';\n\nconst icons = {\n  rotateLeft: <UndoIcon />,\n  rotateRight: <RedoIcon />,\n  zoomIn: <ZoomInIcon />,\n  zoomOut: <ZoomOutIcon />,\n  close: <CloseIcon />,\n  left: <ArrowLeftIcon />,\n  right: <ArrowRightIcon />,\n};\n\nexport const ImagePreviewGroup = ({\n  children,\n  visible,\n  onVisibleChange,\n}: {\n  children: React.ReactNode;\n  visible: boolean;\n  onVisibleChange: (v: boolean) => void;\n}) => {\n  const { getPopupContainer } = useContext(DesignSystemContext);\n\n  return (\n    <RcImage.PreviewGroup\n      icons={icons}\n      preview={{\n        visible: visible,\n        getContainer: getPopupContainer,\n        onVisibleChange: (v) => onVisibleChange(v),\n      }}\n    >\n      {children}\n    </RcImage.PreviewGroup>\n  );\n};\n\nexport { RcImage as Image };\n", "import React, { createContext, type ReactNode, useContext } from 'react';\n\nconst TracesViewTableNoTracesQuickstartContext = createContext<{\n  introductionText?: ReactNode;\n  displayVersionWarnings?: boolean;\n}>({});\n\n/**\n * Allows to alter default behavior of a quickstart tutorial for logging traces\n */\nexport const TracesViewTableNoTracesQuickstartContextProvider = ({\n  children,\n  introductionText,\n  displayVersionWarnings,\n}: {\n  children: ReactNode;\n  introductionText?: ReactNode;\n  displayVersionWarnings?: boolean;\n}) => {\n  return (\n    <TracesViewTableNoTracesQuickstartContext.Provider value={{ introductionText, displayVersionWarnings }}>\n      {children}\n    </TracesViewTableNoTracesQuickstartContext.Provider>\n  );\n};\n\nexport const useTracesViewTableNoTracesQuickstartContext = () => useContext(TracesViewTableNoTracesQuickstartContext);\n", "import { <PERSON><PERSON><PERSON>, Spinner } from '@databricks/design-system';\nimport { useDesignSystemTheme } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport { getArtifactLocationUrl } from '@mlflow/mlflow/src/common/utils/ArtifactUtils';\nimport { ImageEntity } from '@mlflow/mlflow/src/experiment-tracking/types';\nimport { useState, useEffect } from 'react';\nimport { Typography } from '@databricks/design-system';\nimport { ImagePreviewGroup, Image } from '../../../../../shared/building_blocks/Image';\n\n/**\n * Despite image size being dynamic, we want to set a minimum size for the grid images.\n */\nexport const MIN_GRID_IMAGE_SIZE = 200;\n\ntype ImagePlotProps = {\n  imageUrl: string;\n  compressedImageUrl: string;\n  imageSize?: number;\n  maxImageSize?: number;\n};\n\nexport const ImagePlot = ({ imageUrl, compressedImageUrl, imageSize, maxImageSize }: ImagePlotProps) => {\n  const [previewVisible, setPreviewVisible] = useState(false);\n  const { theme } = useDesignSystemTheme();\n\n  const [imageLoading, setImageLoading] = useState(true);\n\n  useEffect(() => {\n    // Load the image in the memory (should reuse the same request) in order to get the loading state\n    setImageLoading(true);\n    const img = new window.Image();\n    img.onload = () => setImageLoading(false);\n    img.onerror = () => setImageLoading(false);\n    img.src = compressedImageUrl;\n    return () => {\n      img.src = '';\n    };\n  }, [compressedImageUrl]);\n\n  return (\n    <div css={{ width: imageSize || '100%', height: imageSize || '100%' }}>\n      <div css={{ display: 'contents' }}>\n        {compressedImageUrl === undefined || imageLoading ? (\n          <div\n            css={{\n              width: '100%',\n              backgroundColor: theme.colors.backgroundSecondary,\n              display: 'flex',\n              aspectRatio: '1',\n              justifyContent: 'center',\n              alignItems: 'center',\n            }}\n          >\n            <Spinner />\n          </div>\n        ) : (\n          <div\n            css={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              width: imageSize || '100%',\n              aspectRatio: '1',\n              maxWidth: maxImageSize,\n              maxHeight: maxImageSize,\n              backgroundColor: theme.colors.backgroundSecondary,\n              '.rc-image': {\n                cursor: 'pointer',\n              },\n            }}\n          >\n            <ImagePreviewGroup visible={previewVisible} onVisibleChange={setPreviewVisible}>\n              <Image\n                src={compressedImageUrl}\n                preview={{ src: imageUrl }}\n                style={{ maxWidth: maxImageSize || '100%', maxHeight: maxImageSize || '100%' }}\n              />\n            </ImagePreviewGroup>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport const ImagePlotWithHistory = ({\n  metadataByStep,\n  imageSize,\n  step,\n  runUuid,\n}: {\n  metadataByStep: Record<number, ImageEntity>;\n  imageSize?: number;\n  step: number;\n  runUuid: string;\n}) => {\n  const { theme } = useDesignSystemTheme();\n\n  if (metadataByStep[step] === undefined) {\n    return (\n      <div\n        css={{\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          textAlign: 'center',\n          width: imageSize,\n          backgroundColor: theme.colors.backgroundSecondary,\n          padding: theme.spacing.md,\n          aspectRatio: '1',\n        }}\n      >\n        <ImageIcon />\n        <FormattedMessage\n          defaultMessage=\"No image logged at this step\"\n          description=\"Experiment tracking > runs charts > charts > image plot with history > no image text\"\n        />\n      </div>\n    );\n  }\n  return (\n    <ImagePlot\n      imageUrl={getArtifactLocationUrl(metadataByStep[step].filepath, runUuid)}\n      compressedImageUrl={getArtifactLocationUrl(metadataByStep[step].compressed_filepath, runUuid)}\n      imageSize={imageSize}\n    />\n  );\n};\n\nexport const EmptyImageGridPlot = () => {\n  return (\n    <div\n      css={{\n        display: 'flex',\n        flexDirection: 'column',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100%',\n        width: '100%',\n        fontSize: 16,\n      }}\n    >\n      <Typography.Title css={{ marginTop: 16 }} color=\"secondary\" level={3}>\n        Compare logged images\n      </Typography.Title>\n      <Typography.Text css={{ marginBottom: 16 }} color=\"secondary\">\n        Use the image grid chart to compare logged images across runs.\n      </Typography.Text>\n    </div>\n  );\n};\n", "// This media query applies to screens with a pixel density of 2 or higher\n\nimport { debounce } from 'lodash';\nimport { useMemo, useState } from 'react';\nimport { COLORS_PALETTE_DATALIST_ID } from '../../../../common/components/ColorsPaletteDatalist';\nimport { visuallyHidden } from '@databricks/design-system';\n\n// and higher resolution values (e.g. Retina displays). 192 dpi is double the \"default\" historical 96 dpi.\nconst HIGH_RESOLUTION_MEDIA_QUERY = '@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)';\n\nconst stripedHiddenBackgroundStyle = `repeating-linear-gradient(\n  135deg,\n  #959595 0,\n  #e7e7e7 1px,\n  #e7e7e7 2px,\n  #959595 3px,\n  #e7e7e7 4px,\n  #e7e7e7 5px,\n  #959595 6px,\n  #e7e7e7 7px,\n  #e7e7e7 8px,\n  #959595 9px,\n  #e7e7e7 10px,\n  #e7e7e7 11px,\n  #959595 12px,\n  #e7e7e7 13px,\n  #e7e7e7 14px\n)`;\n\n/**\n * Renders a colored rounded pill for a run.\n */\nexport const RunColorPill = ({\n  color,\n  hidden,\n  onChangeColor,\n  ...props\n}: {\n  color?: string;\n  hidden?: boolean;\n  onChangeColor?: (colorValue: string) => void;\n}) => {\n  const [colorValue, setColorValue] = useState<string | undefined>(undefined);\n\n  const onChangeColorDebounced = useMemo(() => {\n    // Implementations of <input type=\"color\"> vary from browser to browser, some browser\n    // fire an event on every color change so we debounce the event to avoid multiple\n    // calls to the onChangeColor handler.\n    if (onChangeColor) {\n      return debounce(onChangeColor, 300);\n    }\n    return () => {};\n  }, [onChangeColor]);\n\n  return (\n    <label\n      css={{\n        width: 12,\n        height: 12,\n        borderRadius: 6,\n        flexShrink: 0,\n        // Add a border to make the pill visible when using very light color\n        border: `1px solid ${hidden ? 'transparent' : 'rgba(0,0,0,0.1)'}`,\n        // Straighten it up on high-res screens\n        [HIGH_RESOLUTION_MEDIA_QUERY]: {\n          marginBottom: 1,\n        },\n        background: hidden ? stripedHiddenBackgroundStyle : undefined,\n        cursor: onChangeColor ? 'pointer' : 'default',\n        position: 'relative',\n        '&:hover': {\n          opacity: onChangeColor ? 0.8 : 1,\n        },\n      }}\n      style={{ backgroundColor: colorValue ?? color ?? 'transparent' }}\n      {...props}\n    >\n      <span\n        css={[\n          visuallyHidden,\n          {\n            userSelect: 'none',\n          },\n        ]}\n      >\n        {color}\n      </span>\n      {onChangeColor && (\n        <input\n          disabled={hidden}\n          type=\"color\"\n          value={colorValue ?? color}\n          onChange={({ target }) => {\n            setColorValue(target.value);\n            onChangeColorDebounced(target.value);\n          }}\n          list={COLORS_PALETTE_DATALIST_ID}\n          css={{\n            appearance: 'none',\n            width: 0,\n            height: 0,\n            border: 0,\n            padding: 0,\n            position: 'absolute',\n            bottom: 0,\n            visibility: 'hidden',\n          }}\n        />\n      )}\n    </label>\n  );\n};\n", "import { type ModelTraceInfo } from '@databricks/web-shared/model-trace-explorer';\nimport { type MessageDescriptor, defineMessage } from 'react-intl';\nimport { isNil } from 'lodash';\n\nconst TRACE_METADATA_FIELD_RUN_ID = 'mlflow.sourceRun';\nconst TRACE_METADATA_FIELD_TOTAL_TOKENS = 'total_tokens';\nconst TRACE_METADATA_FIELD_INPUTS = 'mlflow.traceInputs';\nconst TRACE_METADATA_FIELD_OUTPUTS = 'mlflow.traceOutputs';\nexport const TRACE_TAG_NAME_TRACE_NAME = 'mlflow.traceName';\n\n// Truncation limit for tracing metadata, taken from:\n// https://github.com/mlflow/mlflow/blob/2b457f2b46fc135a3fba77aefafe2319a899fc08/mlflow/tracing/constant.py#L23\nconst MAX_CHARS_IN_TRACE_INFO_METADATA_AND_TAGS = 250;\n\nconst getTraceMetadataField = (traceInfo: ModelTraceInfo, field: string) => {\n  return traceInfo.request_metadata?.find(({ key }) => key === field)?.value;\n};\n\nexport const isTraceMetadataPossiblyTruncated = (traceMetadata: string) => {\n  return traceMetadata.length >= MAX_CHARS_IN_TRACE_INFO_METADATA_AND_TAGS;\n};\n\nexport const getTraceInfoRunId = (traceInfo: ModelTraceInfo) =>\n  getTraceMetadataField(traceInfo, TRACE_METADATA_FIELD_RUN_ID);\n\nexport const getTraceInfoTotalTokens = (traceInfo: ModelTraceInfo) =>\n  getTraceMetadataField(traceInfo, TRACE_METADATA_FIELD_TOTAL_TOKENS);\n\nexport const getTraceInfoInputs = (traceInfo: ModelTraceInfo) => {\n  const inputs = getTraceMetadataField(traceInfo, TRACE_METADATA_FIELD_INPUTS);\n  if (isNil(inputs)) {\n    return undefined;\n  }\n  try {\n    return JSON.stringify(JSON.parse(inputs)); // unescape non-ascii characters\n  } catch (e) {\n    return inputs;\n  }\n};\n\nexport const getTraceInfoOutputs = (traceInfo: ModelTraceInfo) => {\n  const outputs = getTraceMetadataField(traceInfo, TRACE_METADATA_FIELD_OUTPUTS);\n  if (isNil(outputs)) {\n    return undefined;\n  }\n  try {\n    return JSON.stringify(JSON.parse(outputs)); // unescape non-ascii characters\n  } catch (e) {\n    return outputs;\n  }\n};\n\nexport const getTraceTagValue = (traceInfo: ModelTraceInfo, tagName: string) => {\n  if (Array.isArray(traceInfo.tags)) {\n    return traceInfo.tags?.find(({ key }) => key === tagName)?.value;\n  }\n\n  return traceInfo.tags?.[tagName];\n};\n\nexport const getTraceDisplayName = (traceInfo: ModelTraceInfo) => {\n  return getTraceTagValue(traceInfo, TRACE_TAG_NAME_TRACE_NAME) || traceInfo.request_id;\n};\n\nexport const EXPERIMENT_TRACES_SORTABLE_COLUMNS = ['timestamp_ms'];\n\n// defining a separate const for this column as\n// we don't users to be able to control its visibility\nexport const TRACE_TABLE_CHECKBOX_COLUMN_ID = 'select';\n\nexport enum ExperimentViewTracesTableColumns {\n  requestId = 'request_id',\n  traceName = 'traceName',\n  timestampMs = 'timestamp_ms',\n  inputs = 'inputs',\n  outputs = 'outputs',\n  runName = 'runName',\n  totalTokens = 'total_tokens',\n  source = 'source',\n  latency = 'latency',\n  tags = 'tags',\n  status = 'status',\n}\n\nexport const ExperimentViewTracesTableColumnLabels: Record<ExperimentViewTracesTableColumns, MessageDescriptor> = {\n  [ExperimentViewTracesTableColumns.requestId]: defineMessage({\n    defaultMessage: 'Request ID',\n    description: 'Experiment page > traces table > request id column header',\n  }),\n  [ExperimentViewTracesTableColumns.traceName]: defineMessage({\n    defaultMessage: 'Trace name',\n    description: 'Experiment page > traces table > trace name column header',\n  }),\n  [ExperimentViewTracesTableColumns.timestampMs]: defineMessage({\n    defaultMessage: 'Time created',\n    description: 'Experiment page > traces table > time created column header',\n  }),\n  [ExperimentViewTracesTableColumns.status]: defineMessage({\n    defaultMessage: 'Status',\n    description: 'Experiment page > traces table > status column header',\n  }),\n  [ExperimentViewTracesTableColumns.inputs]: defineMessage({\n    defaultMessage: 'Request',\n    description: 'Experiment page > traces table > input column header',\n  }),\n  [ExperimentViewTracesTableColumns.outputs]: defineMessage({\n    defaultMessage: 'Response',\n    description: 'Experiment page > traces table > output column header',\n  }),\n  [ExperimentViewTracesTableColumns.runName]: defineMessage({\n    defaultMessage: 'Run name',\n    description: 'Experiment page > traces table > run name column header',\n  }),\n  [ExperimentViewTracesTableColumns.totalTokens]: defineMessage({\n    defaultMessage: 'Tokens',\n    description: 'Experiment page > traces table > tokens column header',\n  }),\n  [ExperimentViewTracesTableColumns.source]: defineMessage({\n    defaultMessage: 'Source',\n    description: 'Experiment page > traces table > source column header',\n  }),\n  [ExperimentViewTracesTableColumns.latency]: defineMessage({\n    defaultMessage: 'Execution time',\n    description: 'Experiment page > traces table > latency column header',\n  }),\n  [ExperimentViewTracesTableColumns.tags]: defineMessage({\n    defaultMessage: 'Tags',\n    description: 'Experiment page > traces table > tags column header',\n  }),\n};\n\nexport const ExperimentViewTracesStatusLabels = {\n  UNSET: null,\n  IN_PROGRESS: defineMessage({\n    defaultMessage: 'In progress',\n    description: 'Experiment page > traces table > status label > in progress',\n  }),\n  OK: defineMessage({\n    defaultMessage: 'OK',\n    description: 'Experiment page > traces table > status label > ok',\n  }),\n  ERROR: defineMessage({\n    defaultMessage: 'Error',\n    description: 'Experiment page > traces table > status label > error',\n  }),\n};\n", "export function coerceToEnum<T extends Record<string, string>, K extends keyof T, V extends T[K] | undefined>(\n  enumObj: T,\n  value: any,\n  fallback: V,\n): V | T[keyof T] {\n  if (value === undefined || value === null || typeof value !== 'string') {\n    return fallback;\n  }\n  for (const v in enumObj) {\n    if (enumObj[v] === value) return enumObj[v];\n  }\n  return fallback;\n}\n", "import type { RefObject } from 'react';\nimport { useState, useEffect } from 'react';\n\ninterface SizeMap {\n  [key: string]: number;\n}\n\nfunction useResponsiveContainer(ref: RefObject<HTMLElement>, sizeMap: SizeMap): string | null {\n  const [matchedSize, setMatchedSize] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (ref.current && sizeMap) {\n      const handleResize = () => {\n        if (!ref.current) {\n          return;\n        }\n        const elementWidth = ref.current.offsetWidth;\n        const matchedKey = Object.keys(sizeMap)\n          .filter((key) => sizeMap[key] >= elementWidth)\n          .sort((a, b) => sizeMap[a] - sizeMap[b])[0];\n\n        setMatchedSize(matchedKey);\n      };\n\n      handleResize();\n\n      const resizeObserver = new ResizeObserver(handleResize);\n      resizeObserver.observe(ref.current);\n\n      return () => resizeObserver.disconnect();\n    }\n    return undefined;\n  }, [ref, sizeMap]);\n\n  return matchedSize;\n}\n\nexport default useResponsiveContainer;\n", "import { GenericSkeleton, ParagraphSkeleton, Typography, useDesignSystemTheme } from '@databricks/design-system';\nimport type { ReactNode } from 'react';\nimport { useRef } from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport useResponsiveContainer from './useResponsiveContainer';\n\nexport interface SecondarySectionProps {\n  id: string;\n  title?: ReactNode;\n  content: ReactNode;\n  isTitleLoading?: boolean;\n}\n\nexport type MaybeSecondarySection = SecondarySectionProps | null;\nexport type SecondarySections = Array<MaybeSecondarySection>;\n\nconst SIDEBAR_WIDTHS = {\n  sm: 316,\n  lg: 480,\n} as const;\nconst VERTICAL_MARGIN_PX = 16;\nconst DEFAULT_MAX_WIDTH = 450;\n\nexport const OverviewLayout = ({\n  isLoading,\n  secondarySections,\n  children,\n  isTabLayout = true,\n  sidebarSize = 'sm',\n  verticalStackOrder,\n}: {\n  isLoading?: boolean;\n  secondarySections: SecondarySections;\n  children: ReactNode;\n  isTabLayout?: boolean;\n  sidebarSize?: 'sm' | 'lg';\n  verticalStackOrder?: 'primary-first' | 'secondary-first';\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  const stackVertically = useResponsiveContainer(containerRef, { small: theme.responsive.breakpoints.lg }) === 'small';\n\n  // Determine vertical stack order, i.e. should the main content be on top or bottom\n  const verticalDisplayPrimaryContentOnTop = verticalStackOrder === 'primary-first';\n\n  const totalSidebarWidth = SIDEBAR_WIDTHS[sidebarSize];\n  const innerSidebarWidth = totalSidebarWidth - VERTICAL_MARGIN_PX;\n\n  const secondaryStackedStyles = stackVertically\n    ? verticalDisplayPrimaryContentOnTop\n      ? { width: '100%' }\n      : { borderBottom: `1px solid ${theme.colors.border}`, width: '100%' }\n    : verticalDisplayPrimaryContentOnTop\n    ? {\n        width: innerSidebarWidth,\n      }\n    : {\n        paddingBottom: theme.spacing.sm,\n        width: innerSidebarWidth,\n      };\n\n  return (\n    <div\n      data-testid=\"entity-overview-container\"\n      ref={containerRef}\n      css={{\n        display: 'flex',\n        flexDirection: stackVertically ? (verticalDisplayPrimaryContentOnTop ? 'column' : 'column-reverse') : 'row',\n        gap: theme.spacing.lg,\n      }}\n    >\n      <div\n        css={{\n          display: 'flex',\n          flexGrow: 1,\n          flexDirection: 'column',\n          gap: theme.spacing.md,\n          width: stackVertically ? '100%' : `calc(100% - ${totalSidebarWidth}px)`,\n        }}\n      >\n        {isLoading ? <GenericSkeleton /> : children}\n      </div>\n      <div\n        style={{\n          display: 'flex',\n          ...(isTabLayout && { marginTop: -theme.spacing.md }), // remove the gap between tab list and sidebar content\n        }}\n      >\n        <div\n          css={{\n            display: 'flex',\n            flexDirection: 'column',\n            gap: theme.spacing.lg,\n            ...secondaryStackedStyles,\n          }}\n        >\n          {isLoading && <GenericSkeleton />}\n          {!isLoading && <SidebarWrapper secondarySections={secondarySections} />}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nconst SidebarWrapper = ({ secondarySections }: { secondarySections: SecondarySections }) => {\n  return (\n    <div>\n      {secondarySections\n        .filter((section) => section !== null)\n        .filter((section) => section?.content !== null)\n        .map(({ title, isTitleLoading, content, id }, index) => (\n          <SecondarySection title={title} isTitleLoading={isTitleLoading} content={content} key={id} index={index} />\n        ))}\n    </div>\n  );\n};\n\nexport const SecondarySectionTitle = ({ children }: { children: ReactNode }) => {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <Typography.Title\n      level={4}\n      style={{\n        whiteSpace: 'nowrap',\n        marginRight: theme.spacing.lg,\n        marginTop: 0,\n      }}\n    >\n      {children}\n    </Typography.Title>\n  );\n};\n\nconst SecondarySection = ({\n  title,\n  content,\n  index,\n  isTitleLoading = false,\n}: Omit<SecondarySectionProps, 'id'> & {\n  index: number;\n}) => {\n  const { theme } = useDesignSystemTheme();\n\n  const titleComponent = isTitleLoading ? (\n    <ParagraphSkeleton\n      label={\n        <FormattedMessage\n          defaultMessage=\"Section title loading\"\n          description=\"Loading skeleton label for overview page section title in Catalog Explorer\"\n        />\n      }\n    />\n  ) : title ? (\n    <SecondarySectionTitle>{title}</SecondarySectionTitle>\n  ) : null;\n\n  const compactStyles = { padding: `${theme.spacing.md}px 0 ${theme.spacing.md}px 0` };\n\n  return (\n    <div\n      css={{\n        ...compactStyles,\n        ...(index === 0 ? {} : { borderTop: `1px solid ${theme.colors.border}` }),\n      }}\n    >\n      {titleComponent}\n      {content}\n    </div>\n  );\n};\n\nexport const KeyValueProperty = ({\n  keyValue,\n  value,\n  maxWidth,\n}: {\n  keyValue: string;\n  value: React.ReactNode;\n  maxWidth?: number | string;\n}) => {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <div\n      css={{\n        display: 'flex',\n        alignItems: 'center',\n        '&:has(+ div)': {\n          marginBottom: theme.spacing.xs,\n        },\n        maxWidth: maxWidth ?? DEFAULT_MAX_WIDTH,\n        wordBreak: 'break-word',\n        lineHeight: theme.typography.lineHeightLg,\n      }}\n    >\n      <div\n        css={{\n          color: theme.colors.textSecondary,\n          flex: 0.5,\n          alignSelf: 'start',\n        }}\n      >\n        {keyValue}\n      </div>\n      <div\n        css={{\n          flex: 1,\n          alignSelf: 'start',\n          overflow: 'hidden',\n        }}\n      >\n        {value}\n      </div>\n    </div>\n  );\n};\n\nexport const NoneCell = () => {\n  return (\n    <Typography.Text color=\"secondary\">\n      <FormattedMessage defaultMessage=\"None\" description=\"Cell value when there's no content\" />\n    </Typography.Text>\n  );\n};\n", "import { Table, TableCell, TableHeader, TableRow } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\nexport interface ExperimentViewDatasetSchemaTableProps {\n  schema: any[];\n  filter: string;\n}\n\nexport const ExperimentViewDatasetSchemaTable = ({\n  schema,\n  filter,\n}: ExperimentViewDatasetSchemaTableProps): JSX.Element => {\n  const hasFilter = (name?: string, type?: string) => {\n    return (\n      filter === '' ||\n      name?.toLowerCase().includes(filter.toLowerCase()) ||\n      type?.toLowerCase().includes(filter.toLowerCase())\n    );\n  };\n\n  const filteredSchema = schema.filter((row: { name: string; type: string }, _: number) =>\n    hasFilter(row.name, row.type),\n  );\n\n  const getNameHeader = () => {\n    return (\n      <FormattedMessage\n        defaultMessage=\"Name\"\n        description={'Header for \"name\" column in the experiment run dataset schema'}\n      />\n    );\n  };\n\n  const getTypeHeader = () => {\n    return <FormattedMessage defaultMessage=\"Type\" description={'Header for \"type\" column in the UC table schema'} />;\n  };\n\n  return (\n    <Table scrollable css={{ width: '100%' }}>\n      <TableRow isHeader>\n        <TableHeader componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetschematable.tsx_57\">\n          {getNameHeader()}\n        </TableHeader>\n        <TableHeader componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetschematable.tsx_58\">\n          {getTypeHeader()}\n        </TableHeader>\n      </TableRow>\n      <div onWheel={(e) => e.stopPropagation()}>\n        {filteredSchema.length === 0 ? (\n          <TableRow>\n            <TableCell>\n              <FormattedMessage\n                defaultMessage=\"No results match this search.\"\n                description=\"No results message in datasets drawer table\"\n              />\n            </TableCell>\n          </TableRow>\n        ) : (\n          filteredSchema.map((row: { name: string; type: string }, idx: number) => (\n            <TableRow key={`table-body-row-${idx}`}>\n              <TableCell>{row.name}</TableCell>\n              <TableCell>{row.type}</TableCell>\n            </TableRow>\n          ))\n        )}\n      </div>\n    </Table>\n  );\n};\n", "import {\n  Header,\n  TableIcon,\n  useDesignSystemTheme,\n  Table,\n  TableCell,\n  TableHeader,\n  TableRow,\n  TableFilterInput,\n  Spacer,\n  Typography,\n} from '@databricks/design-system';\nimport { ExperimentViewDatasetSchemaTable } from './ExperimentViewDatasetSchemaTable';\nimport { DatasetSourceTypes, RunDatasetWithTags } from '../../../../types';\nimport { useEffect, useMemo, useState } from 'react';\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nexport interface DatasetsCellRendererProps {\n  datasetWithTags: RunDatasetWithTags;\n}\n\nexport const ExperimentViewDatasetSchema = ({ datasetWithTags }: DatasetsCellRendererProps): JSX.Element => {\n  const { theme } = useDesignSystemTheme();\n  const { dataset } = datasetWithTags;\n  const [filter, setFilter] = useState('');\n\n  if (dataset.schema === null || dataset.schema === '') {\n    return (\n      <div\n        css={{\n          display: 'flex',\n          flexDirection: 'column',\n          height: '100vh',\n        }}\n      >\n        <div\n          css={{\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'flex-start',\n            alignContent: 'center',\n          }}\n        >\n          <Header title={<div css={{ color: theme.colors.grey600 }}>No schema available</div>} />\n        </div>\n      </div>\n    );\n  }\n  try {\n    const schema = JSON.parse(dataset.schema);\n    if ('mlflow_colspec' in schema) {\n      // if the dataset schema is colspec\n      return (\n        <div\n          css={{\n            display: 'flex',\n            flexDirection: 'column',\n            overflow: 'hidden',\n            height: '100vh',\n          }}\n        >\n          <div\n            css={{\n              marginTop: theme.spacing.sm,\n              form: { width: '100%' },\n            }}\n          >\n            <TableFilterInput\n              componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetschema.tsx_92\"\n              value={filter}\n              placeholder=\"Search fields\"\n              onChange={(e) => setFilter(e.target.value)}\n              onClear={() => {\n                setFilter('');\n              }}\n              css={{ width: '100%' }}\n              containerProps={{ style: { width: 'auto' } }}\n            />\n          </div>\n          <div\n            css={{\n              marginTop: theme.spacing.sm,\n              overflow: 'hidden',\n            }}\n          >\n            <ExperimentViewDatasetSchemaTable schema={schema.mlflow_colspec} filter={filter} />\n          </div>\n        </div>\n      );\n    } else if ('mlflow_tensorspec' in schema) {\n      // if the dataset schema is tensorspec\n      return (\n        <div css={{ height: '100vh' }}>\n          <div\n            css={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              height: '100%',\n            }}\n          >\n            <TableIcon css={{ fontSize: '56px', color: theme.colors.grey600 }} />\n            <Header title={<div css={{ color: theme.colors.grey600 }}>Array Datasource</div>} />\n            {/* @ts-expect-error Type 'string' is not assignable to type '\"primary\" | \"secondary\" | \"info\" | \"error\" | \"success\" | \"warning\" | undefined' */}\n            <Typography.Text color={theme.colors.grey600} css={{ textAlign: 'center' }}>\n              <FormattedMessage\n                defaultMessage=\"The dataset is an array. To see a preview of the dataset, view the dataset in the training notebook.\"\n                description=\"Notification when the dataset is an array data source in the experiment run dataset schema\"\n              />\n            </Typography.Text>\n          </div>\n        </div>\n      );\n    } else {\n      // if the dataset schema is not colspec or tensorspec\n      return (\n        <div css={{ marginLeft: theme.spacing.lg, marginTop: theme.spacing.md, width: '100%' }}>\n          <div css={{ display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}>\n            <Header title={<div css={{ color: theme.colors.grey600 }}>Unrecognized Schema Format</div>} />\n            {/* @ts-expect-error Type 'string' is not assignable to type '\"primary\" | \"secondary\" | \"info\" | \"error\" | \"success\" | \"warning\" | undefined' */}\n            <Typography.Text color={theme.colors.grey600}>\n              <FormattedMessage\n                defaultMessage=\"Raw Schema JSON: \"\n                description=\"Label for the raw schema JSON in the experiment run dataset schema\"\n              />\n              {JSON.stringify(schema)}\n            </Typography.Text>\n          </div>\n        </div>\n      );\n    }\n  } catch {\n    return (\n      <div css={{ marginLeft: theme.spacing.lg, marginTop: theme.spacing.md, width: '100%' }}>\n        <div css={{ display: 'flex', flexDirection: 'column', justifyContent: 'flex-start' }}>\n          <Header title={<div css={{ color: theme.colors.grey600 }}>No schema available</div>} />\n        </div>\n      </div>\n    );\n  }\n};\n", "import { Button, CopyIcon, NewWindowIcon, Typography } from '@databricks/design-system';\nimport { DatasetSourceTypes, RunDatasetWithTags } from '../../../../types';\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { getDatasetSourceUrl } from '../../../../utils/DatasetUtils';\nimport { CopyButton } from '../../../../../shared/building_blocks/CopyButton';\n\nexport interface DatasetLinkProps {\n  datasetWithTags: RunDatasetWithTags;\n  runTags: Record<string, { key: string; value: string }>;\n}\n\nexport const ExperimentViewDatasetLink = ({ datasetWithTags, runTags }: DatasetLinkProps) => {\n  const { dataset } = datasetWithTags;\n  if (dataset.sourceType === DatasetSourceTypes.HTTP || dataset.sourceType === DatasetSourceTypes.HUGGING_FACE) {\n    const url = getDatasetSourceUrl(datasetWithTags);\n    if (url) {\n      return (\n        <Button\n          type=\"primary\"\n          componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetlink.tsx_19_1\"\n          icon={<NewWindowIcon />}\n          href={url}\n          target=\"_blank\"\n        >\n          <FormattedMessage\n            defaultMessage=\"Open dataset\"\n            description=\"Text for the HTTP/HF location link in the experiment run dataset drawer\"\n          />\n        </Button>\n      );\n    }\n  }\n  if (dataset.sourceType === DatasetSourceTypes.S3) {\n    const url = getDatasetSourceUrl(datasetWithTags);\n    if (url) {\n      return (\n        <CopyButton\n          componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetlink.tsx_19_2\"\n          icon={<CopyIcon />}\n          copyText={url}\n        >\n          <FormattedMessage\n            defaultMessage=\"Copy S3 URI to clipboard\"\n            description=\"Text for the HTTP/HF location link in the experiment run dataset drawer\"\n          />\n        </CopyButton>\n      );\n    }\n  }\n  if (dataset.sourceType === DatasetSourceTypes.EXTERNAL) {\n    return (\n      <Button\n        componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetlink.tsx_19_3\"\n        icon={<NewWindowIcon />}\n      >\n        <FormattedMessage\n          defaultMessage=\"Go to external location\"\n          description=\"Text for the external location link in the experiment run dataset drawer\"\n        />\n      </Button>\n    );\n  }\n  return null;\n};\n", "import { Typography } from '@databricks/design-system';\nimport { DatasetSourceTypes, RunDatasetWithTags } from '../../../../types';\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nexport interface ExperimentViewDatasetSourceTypeProps {\n  datasetWithTags: RunDatasetWithTags;\n}\n\nexport const ExperimentViewDatasetSourceType = ({ datasetWithTags }: ExperimentViewDatasetSourceTypeProps) => {\n  const { dataset } = datasetWithTags;\n\n  const sourceType = dataset.sourceType;\n\n  const getSourceTypeLabel = () => {\n    if (sourceType === DatasetSourceTypes.HTTP || sourceType === DatasetSourceTypes.EXTERNAL) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"HTTP\"\n          description=\"Experiment dataset drawer > source type > HTTP source type label\"\n        />\n      );\n    }\n    if (sourceType === DatasetSourceTypes.S3) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"S3\"\n          description=\"Experiment dataset drawer > source type > S3 source type label\"\n        />\n      );\n    }\n    if (sourceType === DatasetSourceTypes.HUGGING_FACE) {\n      return (\n        <FormattedMessage\n          defaultMessage=\"Hugging Face\"\n          description=\"Experiment dataset drawer > source type > Hugging Face source type label\"\n        />\n      );\n    }\n    return null;\n  };\n\n  const typeLabel = getSourceTypeLabel();\n\n  if (typeLabel) {\n    return (\n      <Typography.Hint>\n        <FormattedMessage\n          defaultMessage=\"Source type: {typeLabel}\"\n          description=\"Experiment dataset drawer > source type > label\"\n          values={{ typeLabel }}\n        />\n      </Typography.Hint>\n    );\n  }\n\n  return null;\n};\n", "import { Typography, useDesignSystemTheme } from '@databricks/design-system';\nimport { DatasetSourceTypes, RunDatasetWithTags } from '../../../../types';\nimport { getDatasetSourceUrl } from '../../../../utils/DatasetUtils';\n\nexport interface ExperimentViewDatasetSourceProps {\n  datasetWithTags: RunDatasetWithTags;\n}\n\nexport const ExperimentViewDatasetSourceURL = ({ datasetWithTags }: ExperimentViewDatasetSourceProps) => {\n  const { dataset } = datasetWithTags;\n  const { theme } = useDesignSystemTheme();\n\n  const sourceType = dataset.sourceType;\n\n  if (\n    sourceType === DatasetSourceTypes.HTTP ||\n    sourceType === DatasetSourceTypes.EXTERNAL ||\n    sourceType === DatasetSourceTypes.HUGGING_FACE\n  ) {\n    const url = getDatasetSourceUrl(datasetWithTags);\n    if (url) {\n      return (\n        <div\n          css={{\n            whiteSpace: 'nowrap',\n            display: 'flex',\n            fontSize: theme.typography.fontSizeSm,\n            color: theme.colors.textSecondary,\n            columnGap: theme.spacing.xs,\n          }}\n          title={url}\n        >\n          URL:{' '}\n          <Typography.Link\n            componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetsourceurl.tsx_34\"\n            openInNewTab\n            href={url}\n            css={{ display: 'flex', overflow: 'hidden' }}\n          >\n            <span css={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>{url}</span>\n          </Typography.Link>\n        </div>\n      );\n    }\n  }\n  if (sourceType === DatasetSourceTypes.S3) {\n    try {\n      const { uri } = JSON.parse(dataset.source);\n      if (uri) {\n        return (\n          <Typography.Hint\n            title={uri}\n            css={{\n              whiteSpace: 'nowrap',\n              overflow: 'hidden',\n              textOverflow: 'ellipsis',\n            }}\n          >\n            S3 URI: {uri}\n          </Typography.Hint>\n        );\n      }\n    } catch {\n      return null;\n    }\n  }\n  return null;\n};\n", "import { Typography } from '@databricks/design-system';\nimport { RunDatasetWithTags } from '../../../../types';\nimport { FormattedMessage } from 'react-intl';\n\nexport const ExperimentViewDatasetDigest = ({ datasetWithTags }: { datasetWithTags: RunDatasetWithTags }) => {\n  const { dataset } = datasetWithTags;\n  return (\n    <Typography.Hint>\n      <FormattedMessage\n        defaultMessage=\"Digest: {digest}\"\n        description=\"Experiment dataset drawer > digest > label and value\"\n        values={{ digest: <code>{dataset.digest}</code> }}\n      />\n    </Typography.Hint>\n  );\n};\n", "import React from 'react';\nimport { useState } from 'react';\nimport {\n  <PERSON><PERSON>,\n  <PERSON>er,\n  Header,\n  Spacer,\n  TableIcon,\n  Tag,\n  LegacyTooltip,\n  Typography,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport type { RunDatasetWithTags } from '../../../../types';\nimport { MLFLOW_RUN_DATASET_CONTEXT_TAG } from '../../../../constants';\nimport { ExperimentViewDatasetSchema } from './ExperimentViewDatasetSchema';\nimport { ExperimentViewDatasetLink } from './ExperimentViewDatasetLink';\nimport { Link } from '../../../../../common/utils/RoutingUtils';\nimport Routes from '../../../../routes';\nimport { FormattedMessage } from 'react-intl';\nimport { ExperimentViewDatasetWithContext } from './ExperimentViewDatasetWithContext';\nimport { RunColorPill } from '../RunColorPill';\nimport { ExperimentViewDatasetSourceType } from './ExperimentViewDatasetSourceType';\nimport { ExperimentViewDatasetSourceURL } from './ExperimentViewDatasetSourceURL';\nimport { ExperimentViewDatasetDigest } from './ExperimentViewDatasetDigest';\nimport { useSelector } from 'react-redux';\nimport { ReduxState } from '../../../../../redux-types';\nimport { useGetExperimentRunColor } from '../../hooks/useExperimentRunColor';\n\nexport type DatasetWithRunType = {\n  datasetWithTags: RunDatasetWithTags;\n  runData: {\n    experimentId?: string;\n    tags?: Record<string, { key: string; value: string }>;\n    runUuid: string;\n    runName?: string;\n    datasets: RunDatasetWithTags[];\n  };\n};\n\nexport interface DatasetsCellRendererProps {\n  isOpen: boolean;\n  setIsOpen: (open: boolean) => void;\n  selectedDatasetWithRun: DatasetWithRunType;\n  setSelectedDatasetWithRun: (datasetWithRun: DatasetWithRunType) => void;\n}\n\nconst DRAWER_WITDH = '800px';\nconst MAX_PROFILE_LENGTH = 80;\n\nconst areDatasetsEqual = (datasetA: RunDatasetWithTags, datasetB: RunDatasetWithTags) => {\n  return datasetA.dataset.digest === datasetB.dataset.digest && datasetA.dataset.name === datasetB.dataset.name;\n};\n\nconst ExperimentViewDatasetDrawerImpl = ({\n  isOpen,\n  setIsOpen,\n  selectedDatasetWithRun,\n  setSelectedDatasetWithRun,\n}: DatasetsCellRendererProps): JSX.Element => {\n  const { theme } = useDesignSystemTheme();\n  const { datasetWithTags, runData } = selectedDatasetWithRun;\n  const contextTag = selectedDatasetWithRun\n    ? datasetWithTags?.tags?.find((tag) => tag.key === MLFLOW_RUN_DATASET_CONTEXT_TAG)\n    : undefined;\n  const fullProfile =\n    datasetWithTags.dataset.profile && datasetWithTags.dataset.profile !== 'null'\n      ? datasetWithTags.dataset.profile\n      : undefined;\n\n  const getRunColor = useGetExperimentRunColor();\n  const { experimentId = '', tags = {} } = runData;\n\n  return (\n    <Drawer.Root\n      open={isOpen}\n      onOpenChange={(open) => {\n        if (!open) {\n          setIsOpen(false);\n        }\n      }}\n    >\n      <Drawer.Content\n        componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetdrawer.tsx_81\"\n        title={\n          <div css={{ display: 'flex', alignItems: 'center', height: '100%' }}>\n            <Typography.Title level={4} css={{ marginRight: theme.spacing.sm, marginBottom: 0 }}>\n              <FormattedMessage\n                defaultMessage=\"Data details for \"\n                description=\"Text for data details for the experiment run in the dataset drawer\"\n              />\n            </Typography.Title>\n            <Link to={Routes.getRunPageRoute(experimentId, runData.runUuid)} css={styles.runLink}>\n              <RunColorPill color={getRunColor(runData.runUuid)} />\n              <span css={styles.runName}>{runData.runName}</span>\n            </Link>\n          </div>\n        }\n        width={DRAWER_WITDH}\n        footer={<Spacer size=\"xs\" />}\n      >\n        <div\n          css={{\n            display: 'flex',\n            borderTop: `1px solid ${theme.colors.border}`,\n            height: '100%',\n            marginLeft: -theme.spacing.sm,\n          }}\n        >\n          {/* column for dataset selection */}\n          <div\n            css={{\n              display: 'flex',\n              flexDirection: 'column',\n              width: '300px',\n              borderRight: `1px solid ${theme.colors.border}`,\n              height: '100%',\n            }}\n          >\n            <Typography.Text\n              color=\"secondary\"\n              css={{\n                marginBottom: theme.spacing.sm,\n                marginTop: theme.spacing.sm,\n                paddingLeft: theme.spacing.sm,\n              }}\n            >\n              {runData.datasets.length}{' '}\n              <FormattedMessage\n                defaultMessage=\"datasets used\"\n                description=\"Text for dataset count in the experiment run dataset drawer\"\n              />\n            </Typography.Text>\n            <div\n              css={{\n                height: '100%',\n                display: 'flex',\n                overflow: 'auto',\n              }}\n              onWheel={(e) => e.stopPropagation()}\n            >\n              <div\n                css={{\n                  display: 'flex',\n                  flexDirection: 'column',\n                  overflow: 'visible',\n                  flex: 1,\n                }}\n              >\n                {runData.datasets.map((dataset) => (\n                  <Typography.Link\n                    componentId=\"mlflow.dataset_drawer.dataset_link\"\n                    aria-label={`${dataset.dataset.name} (${dataset.dataset.digest})`}\n                    key={`${dataset.dataset.name}-${dataset.dataset.digest}`}\n                    css={{\n                      display: 'flex',\n                      whiteSpace: 'nowrap',\n                      textDecoration: 'none',\n                      cursor: 'pointer',\n                      flexDirection: 'column',\n                      justifyContent: 'center',\n                      alignItems: 'flex-start',\n                      backgroundColor: areDatasetsEqual(dataset, datasetWithTags)\n                        ? theme.colors.actionTertiaryBackgroundPress\n                        : 'transparent',\n                      paddingBottom: theme.spacing.sm,\n                      paddingTop: theme.spacing.sm,\n                      paddingLeft: theme.spacing.sm,\n                      border: 0,\n                      borderTop: `1px solid ${theme.colors.border}`,\n                      '&:hover': {\n                        backgroundColor: theme.colors.actionTertiaryBackgroundHover,\n                      },\n                    }}\n                    onClick={() => {\n                      setSelectedDatasetWithRun({ datasetWithTags: dataset, runData: runData });\n                      setIsOpen(true);\n                    }}\n                  >\n                    <ExperimentViewDatasetWithContext datasetWithTags={dataset} displayTextAsLink={false} />\n                  </Typography.Link>\n                ))}\n              </div>\n            </div>\n          </div>\n          {/* column for dataset details */}\n          <div\n            css={{\n              overflow: 'hidden',\n              paddingLeft: theme.spacing.md,\n              paddingTop: theme.spacing.md,\n              display: 'flex',\n              flexDirection: 'column',\n              width: '100%',\n            }}\n          >\n            {/* dataset metadata */}\n            <div\n              css={{\n                display: 'flex',\n                gap: theme.spacing.sm,\n              }}\n            >\n              <div css={{ flex: '1' }}>\n                <Header\n                  title={\n                    <div css={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>\n                      <TableIcon css={{ marginRight: theme.spacing.xs }} />\n                      <LegacyTooltip title={datasetWithTags.dataset.name}>\n                        <Typography.Title ellipsis level={3} css={{ marginBottom: 0, maxWidth: 200 }}>\n                          {datasetWithTags.dataset.name}\n                        </Typography.Title>\n                      </LegacyTooltip>\n                      {contextTag && (\n                        <Tag\n                          componentId=\"codegen_mlflow_app_src_experiment-tracking_components_experiment-page_components_runs_experimentviewdatasetdrawer.tsx_206\"\n                          css={{\n                            textTransform: 'capitalize',\n                            marginLeft: theme.spacing.xs,\n                            marginRight: theme.spacing.xs,\n                          }}\n                        >\n                          {contextTag.value}\n                        </Tag>\n                      )}\n                    </div>\n                  }\n                />\n                <Typography.Title\n                  level={4}\n                  color=\"secondary\"\n                  css={{ marginBottom: theme.spacing.xs, marginTop: theme.spacing.xs }}\n                  title={fullProfile}\n                >\n                  {datasetWithTags.dataset.profile && datasetWithTags.dataset.profile !== 'null' ? (\n                    datasetWithTags.dataset.profile.length > MAX_PROFILE_LENGTH ? (\n                      `${datasetWithTags.dataset.profile.substring(0, MAX_PROFILE_LENGTH)} ...`\n                    ) : (\n                      datasetWithTags.dataset.profile\n                    )\n                  ) : (\n                    <FormattedMessage\n                      defaultMessage=\"No profile available\"\n                      description=\"Text for no profile available in the experiment run dataset drawer\"\n                    />\n                  )}\n                </Typography.Title>\n              </div>\n              <ExperimentViewDatasetLink datasetWithTags={datasetWithTags} runTags={tags} />\n            </div>\n            <div css={{ flexShrink: 0, display: 'flex', flexDirection: 'column', gap: theme.spacing.xs }}>\n              <ExperimentViewDatasetDigest datasetWithTags={datasetWithTags} />\n              <ExperimentViewDatasetSourceType datasetWithTags={datasetWithTags} />\n              <ExperimentViewDatasetSourceURL datasetWithTags={datasetWithTags} />\n            </div>\n            {/* dataset schema */}\n            <div\n              css={{\n                marginTop: theme.spacing.sm,\n                marginBottom: theme.spacing.xs,\n                borderTop: `1px solid ${theme.colors.border}`,\n                opacity: 0.5,\n              }}\n            />\n            <ExperimentViewDatasetSchema datasetWithTags={datasetWithTags} />\n          </div>\n        </div>\n      </Drawer.Content>\n    </Drawer.Root>\n  );\n};\n\n// Memoize the component so it rerenders only when props change directly, preventing\n// rerenders caused e.g. by the overarching context provider.\nexport const ExperimentViewDatasetDrawer = React.memo(ExperimentViewDatasetDrawerImpl);\n\nconst styles = {\n  runLink: {\n    overflow: 'hidden',\n    display: 'flex',\n    gap: 8,\n    alignItems: 'center',\n  },\n  runName: {\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    fontSize: '13px',\n  },\n};\n", "import { type ModelTraceInfo } from '@databricks/web-shared/model-trace-explorer';\nimport { useCallback, useEffect, useMemo, useState } from 'react';\nimport { MlflowService } from '../../../sdk/MlflowService';\nimport { EXPERIMENT_TRACES_SORTABLE_COLUMNS, getTraceInfoRunId } from '../TracesView.utils';\nimport { ViewType } from '../../../sdk/MlflowEnums';\nimport { first, uniq, values } from 'lodash';\nimport { RunEntity } from '../../../types';\nimport { isExperimentLoggedModelsUIEnabled } from '../../../../common/utils/FeatureUtils';\n\n// A filter expression used to filter traces by run ID\nconst RUN_ID_FILTER_EXPRESSION = 'request_metadata.`mlflow.sourceRun`';\nconst LOGGED_MODEL_ID_FILTER_EXPRESSION = 'request_metadata.`mlflow.modelId`';\n\nconst createRunIdsFilterExpression = (runUuids: string[]) => {\n  const runIdsInQuotes = runUuids.map((runId: any) => `'${runId}'`);\n  return `run_id IN (${runIdsInQuotes.join(',')})`;\n};\n\n/**\n * Utility function that fetches run names for traces.\n */\nconst fetchRunNamesForTraces = async (experimentIds: string[], traces: ModelTraceInfo[]) => {\n  const traceIdToRunIdMap = traces.reduce<Record<string, string>>((acc, trace) => {\n    const traceId = trace.request_id;\n    const runId = getTraceInfoRunId(trace);\n    if (!traceId || !runId) {\n      return acc;\n    }\n    return { ...acc, [traceId]: runId };\n  }, {});\n\n  const runUuids = uniq(values(traceIdToRunIdMap));\n  if (runUuids.length < 1) {\n    return {};\n  }\n  const runResponse = (await MlflowService.searchRuns({\n    experiment_ids: experimentIds,\n    filter: createRunIdsFilterExpression(runUuids),\n    run_view_type: ViewType.ALL,\n  })) as { runs?: RunEntity[] };\n\n  const runs = runResponse.runs;\n\n  const runIdsToRunNames = (runs || []).reduce<Record<string, string>>((acc, run) => {\n    return { ...acc, [run.info.runUuid]: run.info.runName };\n  }, {});\n\n  const traceIdsToRunNames = traces.reduce<Record<string, string>>((acc, trace) => {\n    const traceId = trace.request_id;\n    if (!traceId) {\n      return acc;\n    }\n    const runId = traceIdToRunIdMap[traceId];\n\n    return { ...acc, [traceId]: runIdsToRunNames[runId] || runId };\n  }, {});\n\n  return traceIdsToRunNames;\n};\n\nexport interface ModelTraceInfoWithRunName extends ModelTraceInfo {\n  runName?: string;\n}\n\nexport const useExperimentTraces = ({\n  experimentIds,\n  sorting,\n  filter = '',\n  runUuid,\n  loggedModelId,\n}: {\n  experimentIds: string[];\n  sorting: {\n    id: string;\n    desc: boolean;\n  }[];\n  filter?: string;\n  runUuid?: string;\n  loggedModelId?: string;\n}) => {\n  const [traces, setTraces] = useState<ModelTraceInfoWithRunName[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<Error | undefined>(undefined);\n\n  // Backend currently only supports ordering by timestamp\n  const orderByString = useMemo(() => {\n    const firstOrderByColumn = first(sorting);\n    if (firstOrderByColumn && EXPERIMENT_TRACES_SORTABLE_COLUMNS.includes(firstOrderByColumn.id)) {\n      return `${firstOrderByColumn.id} ${firstOrderByColumn.desc ? 'DESC' : 'ASC'}`;\n    }\n    return 'timestamp_ms DESC';\n  }, [sorting]);\n\n  const filterString = useMemo(() => {\n    if (!runUuid && !loggedModelId) {\n      return filter;\n    }\n\n    if (isExperimentLoggedModelsUIEnabled() && loggedModelId) {\n      if (filter) {\n        return `${filter} AND ${LOGGED_MODEL_ID_FILTER_EXPRESSION}='${loggedModelId}'`;\n      }\n      return `${LOGGED_MODEL_ID_FILTER_EXPRESSION}='${loggedModelId}'`;\n    }\n\n    if (filter) {\n      return `${filter} AND ${RUN_ID_FILTER_EXPRESSION}='${runUuid}'`;\n    }\n\n    return `${RUN_ID_FILTER_EXPRESSION}='${runUuid}'`;\n  }, [filter, runUuid, loggedModelId]);\n\n  const [pageTokens, setPageTokens] = useState<Record<string, string | undefined>>({ 0: undefined });\n  const [currentPage, setCurrentPage] = useState(0);\n  const currentPageToken = pageTokens[currentPage];\n\n  const fetchTraces = useCallback(\n    async ({\n      experimentIds,\n      currentPage = 0,\n      pageToken,\n      silent,\n      orderByString = '',\n      filterString = '',\n    }: {\n      experimentIds: string[];\n      currentPage?: number;\n      pageToken?: string;\n      filterString?: string;\n      orderByString?: string;\n      silent?: boolean;\n    }) => {\n      if (!silent) {\n        setLoading(true);\n      }\n      setError(undefined);\n\n      try {\n        const response = await MlflowService.getExperimentTraces(experimentIds, orderByString, pageToken, filterString);\n\n        if (!response.traces) {\n          setTraces([]);\n          return;\n        }\n\n        const runNamesForTraces = await fetchRunNamesForTraces(experimentIds, response.traces);\n        const tracesWithRunNames = response.traces.map((trace) => {\n          const traceId = trace.request_id;\n          if (!traceId) {\n            return { ...trace };\n          }\n          const runName = runNamesForTraces[traceId];\n          return { ...trace, runName };\n        });\n\n        setTraces(tracesWithRunNames);\n        setPageTokens((prevPages) => {\n          return { ...prevPages, [currentPage + 1]: response.next_page_token };\n        });\n      } catch (e: any) {\n        setError(e);\n      } finally {\n        setLoading(false);\n      }\n    },\n    [],\n  );\n\n  const hasNextPage = !loading && pageTokens[currentPage + 1] !== undefined;\n  const hasPreviousPage = !loading && (currentPage === 1 || pageTokens[currentPage - 1] !== undefined);\n\n  useEffect(() => {\n    fetchTraces({ experimentIds, filterString, orderByString });\n  }, [fetchTraces, filterString, experimentIds, orderByString]);\n\n  const reset = useCallback(() => {\n    setTraces([]);\n    setPageTokens({ 0: undefined });\n    setCurrentPage(0);\n    fetchTraces({ experimentIds });\n  }, [fetchTraces, experimentIds]);\n\n  const fetchNextPage = useCallback(() => {\n    setCurrentPage((prevPage) => prevPage + 1);\n    fetchTraces({\n      experimentIds,\n      currentPage: currentPage + 1,\n      pageToken: pageTokens[currentPage + 1],\n      filterString,\n      orderByString,\n    });\n  }, [experimentIds, currentPage, fetchTraces, pageTokens, filterString, orderByString]);\n\n  const fetchPrevPage = useCallback(() => {\n    setCurrentPage((prevPage) => prevPage - 1);\n    fetchTraces({\n      experimentIds,\n      currentPage: currentPage - 1,\n      pageToken: pageTokens[currentPage - 1],\n      filterString,\n      orderByString,\n    });\n  }, [experimentIds, currentPage, fetchTraces, pageTokens, filterString, orderByString]);\n\n  const refreshCurrentPage = useCallback(\n    (silent = false) => {\n      return fetchTraces({\n        experimentIds,\n        currentPage,\n        pageToken: currentPageToken,\n        silent,\n        filterString,\n        orderByString,\n      });\n    },\n    [experimentIds, currentPage, fetchTraces, currentPageToken, filterString, orderByString],\n  );\n\n  return {\n    traces,\n    loading,\n    error,\n    hasNextPage,\n    hasPreviousPage,\n    fetchNextPage,\n    fetchPrevPage,\n    refreshCurrentPage,\n    reset,\n  };\n};\n", "import { Button, PencilIcon, SpeechBubblePlusIcon, useDesignSystemTheme } from '@databricks/design-system';\nimport { MLFLOW_INTERNAL_PREFIX } from '../../../common/utils/TagUtils';\nimport { KeyValueTag } from '../../../common/components/KeyValueTag';\nimport { FormattedMessage } from 'react-intl';\n\nexport const TracesViewTableTagCell = ({\n  onAddEditTags,\n  tags,\n  baseComponentId,\n}: {\n  tags: { key: string; value: string }[];\n  onAddEditTags: () => void;\n  baseComponentId: string;\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const visibleTagList = tags?.filter(({ key }) => key && !key.startsWith(MLFLOW_INTERNAL_PREFIX)) || [];\n  const containsTags = visibleTagList.length > 0;\n  return (\n    <div\n      css={{\n        display: 'flex',\n        alignItems: 'center',\n        flexWrap: 'wrap',\n        columnGap: theme.spacing.xs,\n        rowGap: theme.spacing.xs,\n      }}\n    >\n      {visibleTagList.map((tag) => (\n        <KeyValueTag\n          key={tag.key}\n          tag={tag}\n          css={{ marginRight: 0 }}\n          charLimit={20}\n          maxWidth={150}\n          enableFullViewModal\n        />\n      ))}{' '}\n      <Button\n        componentId={`${baseComponentId}.traces_table.edit_tag`}\n        size=\"small\"\n        icon={!containsTags ? undefined : <PencilIcon />}\n        onClick={onAddEditTags}\n        children={\n          !containsTags ? (\n            <FormattedMessage\n              defaultMessage=\"Add tags\"\n              description=\"Button text to add tags to a trace in the experiment traces table\"\n            />\n          ) : undefined\n        }\n        css={{\n          flexShrink: 0,\n          opacity: 0,\n          '[role=row]:hover &': {\n            opacity: 1,\n          },\n          '[role=row]:focus-within &': {\n            opacity: 1,\n          },\n        }}\n        type=\"tertiary\"\n      />\n    </div>\n  );\n};\n", "import type { CellContext, ColumnDefTemplate } from '@tanstack/react-table';\nimport { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { CheckCircleIcon, ClockIcon, XCircleIcon, useDesignSystemTheme } from '@databricks/design-system';\nimport { ExperimentViewTracesStatusLabels } from './TracesView.utils';\nimport { useIntl } from 'react-intl';\nimport type { Theme } from '@emotion/react';\n\nconst getIcon = (status: ModelTraceInfoWithRunName['status'], theme: Theme) => {\n  if (status === 'IN_PROGRESS') {\n    return <ClockIcon css={{ color: theme.colors.textValidationWarning }} />;\n  }\n\n  if (status === 'OK') {\n    return <CheckCircleIcon css={{ color: theme.colors.textValidationSuccess }} />;\n  }\n\n  if (status === 'ERROR') {\n    return <XCircleIcon css={{ color: theme.colors.textValidationDanger }} />;\n  }\n\n  return null;\n};\n\nexport const TracesViewTableStatusCell: ColumnDefTemplate<CellContext<ModelTraceInfoWithRunName, unknown>> = ({\n  row: { original },\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const intl = useIntl();\n\n  const labelDescriptor = ExperimentViewTracesStatusLabels[original.status || 'UNSET'];\n\n  return (\n    <div css={{ display: 'flex', gap: theme.spacing.xs, alignItems: 'center' }}>\n      {getIcon(original.status, theme)}\n      {labelDescriptor ? intl.formatMessage(labelDescriptor) : ''}\n    </div>\n  );\n};\n", "import { Button, ChevronDownIcon, ChevronRightIcon, useDesignSystemTheme } from '@databricks/design-system';\nimport { isString } from 'lodash';\nimport { useCallback, useMemo, useState } from 'react';\nimport { MlflowService } from '../../sdk/MlflowService';\nimport Utils from '../../../common/utils/Utils';\nimport { ErrorWrapper } from '../../../common/utils/ErrorWrapper';\nimport type { CellContext, ColumnDefTemplate } from '@tanstack/react-table';\nimport type { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { getTraceInfoInputs, getTraceInfoOutputs, isTraceMetadataPossiblyTruncated } from './TracesView.utils';\nimport { CodeSnippet } from '@databricks/web-shared/snippet';\nimport { css } from '@emotion/react';\n\nconst clampedLinesCss = css`\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n`;\n\nconst TracesViewTablePreviewCell = ({\n  value,\n  traceId,\n  previewFieldName,\n}: {\n  value: string;\n  traceId: string;\n  previewFieldName: 'request' | 'response';\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [fullData, setFullData] = useState<string | null>(null);\n\n  const [loading, setLoading] = useState<boolean>(false);\n\n  const fetchFullData = useCallback(async () => {\n    setLoading(true);\n    try {\n      const response = await MlflowService.getExperimentTraceData<{\n        request?: any;\n        response?: any;\n      }>(traceId);\n\n      if (previewFieldName in response) {\n        const previewValue = response[previewFieldName];\n        const requestData = isString(previewValue) ? previewValue : JSON.stringify(previewValue);\n        setFullData(requestData);\n      }\n    } catch (e: any) {\n      const errorMessage = e instanceof ErrorWrapper ? e.getUserVisibleError() : e.message;\n      Utils.logErrorAndNotifyUser(`Error fetching response: ${errorMessage}`);\n    }\n    setLoading(false);\n  }, [previewFieldName, traceId]);\n\n  const valuePossiblyTruncated = isTraceMetadataPossiblyTruncated(value);\n\n  const expand = useCallback(async () => {\n    if (!fullData && valuePossiblyTruncated) {\n      await fetchFullData();\n    }\n    setIsExpanded(true);\n  }, [fullData, fetchFullData, valuePossiblyTruncated]);\n\n  const collapse = useCallback(() => {\n    setIsExpanded(false);\n  }, []);\n\n  return (\n    <div css={{ display: 'flex', gap: theme.spacing.xs }}>\n      <Button\n        // it's difficult to distinguish between run and experiment page\n        // in this component due to how the data is passed to the table,\n        // so the base component ID here is simply `mlflow.traces`\n        componentId=\"mlflow.traces.traces_table.expand_cell_preview\"\n        size=\"small\"\n        icon={isExpanded ? <ChevronDownIcon /> : <ChevronRightIcon />}\n        onClick={isExpanded ? collapse : expand}\n        css={{ flexShrink: 0 }}\n        loading={loading}\n        type=\"primary\"\n      />\n      <div\n        title={value}\n        css={[\n          {\n            overflow: 'hidden',\n            textOverflow: 'ellipsis',\n          },\n          !isExpanded && clampedLinesCss,\n        ]}\n      >\n        {isExpanded ? <ExpandedParamCell value={fullData ?? value} /> : value}\n      </div>\n    </div>\n  );\n};\n\nconst ExpandedParamCell = ({ value }: { value: string }) => {\n  const { theme } = useDesignSystemTheme();\n\n  const structuredJSONValue = useMemo(() => {\n    // Attempts to parse the value as JSON and returns a pretty printed version if successful.\n    // If JSON structure is not found, returns null.\n    try {\n      const objectData = JSON.parse(value);\n      return JSON.stringify(objectData, null, 2);\n    } catch (e) {\n      return null;\n    }\n  }, [value]);\n  return (\n    <div\n      css={{\n        whiteSpace: 'pre-wrap',\n        wordBreak: 'break-word',\n        fontFamily: structuredJSONValue ? 'monospace' : undefined,\n      }}\n    >\n      <CodeSnippet\n        language=\"json\"\n        wrapLongLines\n        style={{\n          padding: theme.spacing.sm,\n        }}\n        theme={theme.isDarkMode ? 'duotoneDark' : 'light'}\n      >\n        {structuredJSONValue || value}\n      </CodeSnippet>\n    </div>\n  );\n};\n\nexport const TracesViewTableRequestPreviewCell: ColumnDefTemplate<CellContext<ModelTraceInfoWithRunName, unknown>> = ({\n  row: { original },\n}) => (\n  <TracesViewTablePreviewCell\n    previewFieldName=\"request\"\n    traceId={original.request_id || ''}\n    value={getTraceInfoInputs(original) || ''}\n  />\n);\n\nexport const TracesViewTableResponsePreviewCell: ColumnDefTemplate<CellContext<ModelTraceInfoWithRunName, unknown>> = ({\n  row: { original },\n}) => (\n  <TracesViewTablePreviewCell\n    previewFieldName=\"response\"\n    traceId={original.request_id || ''}\n    value={getTraceInfoOutputs(original) || ''}\n  />\n);\n", "import type { CellContext, ColumnDefTemplate } from '@tanstack/react-table';\nimport { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { keyBy } from 'lodash';\nimport { SourceCellRenderer } from '../experiment-page/components/runs/cells/SourceCellRenderer';\n\nexport const TracesViewTableSourceCell: ColumnDefTemplate<CellContext<ModelTraceInfoWithRunName, unknown>> = ({\n  row: { original },\n}) => <SourceCellRenderer value={keyBy(original.tags, 'key')} />;\n", "import { ColumnDef } from '@tanstack/react-table';\nimport { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { Interpolation, Theme } from '@emotion/react';\n\nexport type TracesColumnDef = ColumnDef<ModelTraceInfoWithRunName> & {\n  meta?: {\n    styles?: Interpolation<Theme>;\n    multiline?: boolean;\n  };\n};\n\nexport const getHeaderSizeClassName = (id: string) => `--header-${id}-size`;\nexport const getColumnSizeClassName = (id: string) => `--col-${id}-size`;\n", "import React from 'react';\nimport { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { Row, flexRender } from '@tanstack/react-table';\nimport { useDesignSystemTheme } from '@databricks/design-system';\nimport { TracesColumnDef, getColumnSizeClassName } from './TracesViewTable.utils';\nimport { TRACE_TABLE_CHECKBOX_COLUMN_ID } from './TracesView.utils';\nimport { isEqual } from 'lodash';\n\ntype TracesViewTableRowProps = {\n  row: Row<ModelTraceInfoWithRunName>;\n  // used only for memoization updates\n  selected: boolean;\n  columns: TracesColumnDef[];\n};\n\nexport const TracesViewTableRow = React.memo(\n  ({ row }: TracesViewTableRowProps) => {\n    const { theme } = useDesignSystemTheme();\n\n    return (\n      <div\n        role=\"row\"\n        key={row.id}\n        data-testid=\"endpoints-list-table-rows\"\n        css={{\n          minHeight: theme.general.buttonHeight,\n          display: 'flex',\n          flexDirection: 'row',\n          ':hover': {\n            backgroundColor: 'var(--table-row-hover)',\n          },\n          paddingRight: '32px', // width of the column selector defined in TableRowActionStyles\n          borderBottom: `1px solid var(--table-separator-color)`,\n        }}\n      >\n        {row.getAllCells().map((cell) => {\n          const multiline = (cell.column.columnDef as TracesColumnDef).meta?.multiline;\n          const isSelect = cell.column.id === TRACE_TABLE_CHECKBOX_COLUMN_ID;\n          const padding = isSelect ? theme.spacing.sm : `${theme.spacing.sm}px ${theme.spacing.xs}px`;\n\n          return (\n            <div\n              role=\"cell\"\n              css={[\n                {\n                  '--table-row-vertical-padding': `${theme.spacing.sm}px`,\n                  flex: `calc(var(${getColumnSizeClassName(cell.column.id)}) / 100)`,\n                  overflow: 'hidden',\n                  whiteSpace: multiline ? 'pre-wrap' : 'nowrap',\n                  textOverflow: multiline ? 'ellipsis' : undefined,\n                  padding,\n                },\n                (cell.column.columnDef as TracesColumnDef).meta?.styles,\n              ]}\n              key={cell.id}\n            >\n              {flexRender(cell.column.columnDef.cell, cell.getContext())}\n            </div>\n          );\n        })}\n      </div>\n    );\n  },\n  (prev, next) => {\n    return (\n      prev.columns === next.columns &&\n      prev.selected === next.selected &&\n      isEqual(prev.row.original.tags, next.row.original.tags)\n    );\n  },\n);\n", "import { CellContext, ColumnDefTemplate } from '@tanstack/react-table';\nimport React from 'react';\nimport { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { LegacyTooltip } from '@databricks/design-system';\nimport Utils from '@mlflow/mlflow/src/common/utils/Utils';\n\nexport const TracesViewTableTimestampCell: ColumnDefTemplate<CellContext<ModelTraceInfoWithRunName, unknown>> =\n  React.memo(\n    ({ row: { original } }) => {\n      if (!original.timestamp_ms) {\n        return null;\n      }\n      return (\n        <LegacyTooltip\n          title={new Date(original.timestamp_ms).toLocaleString(navigator.language, {\n            timeZoneName: 'short',\n          })}\n          placement=\"right\"\n        >\n          <span>{Utils.timeSinceStr(original.timestamp_ms)}</span>\n        </LegacyTooltip>\n      );\n    },\n    () => true,\n  );\n", "import React from 'react';\nimport { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { Table } from '@tanstack/react-table';\nimport { Checkbox } from '@databricks/design-system';\n\ntype TracesViewTableCheckboxProps = {\n  table: Table<ModelTraceInfoWithRunName>;\n};\n\nexport const TracesViewTableHeaderCheckbox = ({ table }: TracesViewTableCheckboxProps) => {\n  const isChecked = table.getIsAllRowsSelected() || (table.getIsSomeRowsSelected() ? null : false);\n\n  return (\n    <Checkbox\n      componentId=\"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewtableheadercheckbox.tsx_14\"\n      data-testid=\"trace-table-header-checkbox\"\n      isChecked={isChecked}\n      wrapperStyle={{ padding: 0, margin: 0 }}\n      onChange={table.toggleAllRowsSelected}\n    />\n  );\n};\n", "import React from 'react';\nimport { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { Row } from '@tanstack/react-table';\nimport { Checkbox } from '@databricks/design-system';\n\ntype TracesViewCellCheckboxProps = {\n  row: Row<ModelTraceInfoWithRunName>;\n};\n\nexport const TracesViewTableCellCheckbox = ({ row }: TracesViewCellCheckboxProps) => {\n  return (\n    <Checkbox\n      componentId=\"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewtablecellcheckbox.tsx_12\"\n      data-testid={`trace-table-cell-checkbox-${row.id}`}\n      disabled={!row.getCanSelect()}\n      isChecked={row.getIsSelected()}\n      wrapperStyle={{ padding: 0, margin: 0 }}\n      onChange={() => row.toggleSelected()}\n    />\n  );\n};\n", "import { Typography } from '@databricks/design-system';\nimport React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nexport type QUICKSTART_FLAVOR =\n  | 'openai'\n  | 'langchain'\n  | 'llama_index'\n  | 'dspy'\n  | 'crewai'\n  | 'autogen'\n  | 'anthropic'\n  | 'bedrock'\n  | 'litellm'\n  | 'gemini'\n  | 'custom';\n\nexport const QUICKSTART_CONTENT: Record<\n  QUICKSTART_FLAVOR,\n  {\n    minVersion: string;\n    getContent: (baseComponentId?: string) => React.ReactNode;\n    getCodeSource: () => string;\n  }\n> = {\n  openai: {\n    minVersion: '2.15.1',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for OpenAI API calls by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the OpenAI package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.openai.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () =>\n      `from openai import OpenAI\n\nmlflow.openai.autolog()\n\n# Ensure that the \"OPENAI_API_KEY\" environment variable is set\nclient = OpenAI()\n\nmessages = [\n  {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n  {\"role\": \"user\", \"content\": \"Hello!\"}\n]\n\n# Inputs and outputs of the API request will be logged in a trace\nclient.chat.completions.create(model=\"gpt-4o-mini\", messages=messages)`,\n  },\n  langchain: {\n    // the autologging integration was really introduced in\n    // 2.14.0, but it does not support newer versions of langchain\n    // so effectively that version will not work with the code snippet\n    minVersion: '2.17.2',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for LangChain or LangGraph invocations by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the LangChain/LangGraph package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.langchain.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () =>\n      `from langchain_openai import OpenAI\nfrom langchain_core.prompts import PromptTemplate\n\nmlflow.langchain.autolog()\n\n# Ensure that the \"OPENAI_API_KEY\" environment variable is set\nllm = OpenAI()\nprompt = PromptTemplate.from_template(\"Answer the following question: {question}\")\nchain = prompt | llm\n\n# Invoking the chain will cause a trace to be logged\nchain.invoke(\"What is MLflow?\")`,\n  },\n  llama_index: {\n    minVersion: '2.15.1',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for LlamaIndex queries by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the LlamaIndex package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.llama_index.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () =>\n      `from llama_index.core import Document, VectorStoreIndex\n\nmlflow.llama_index.autolog()\n\n# Ensure that the \"OPENAI_API_KEY\" environment variable is set\nindex = VectorStoreIndex.from_documents([Document.example()])\nquery_engine = index.as_query_engine()\n\n# Querying the engine will cause a trace to be logged\nquery_engine.query(\"What is LlamaIndex?\")`,\n  },\n  dspy: {\n    minVersion: '2.18.0',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for DSPy executions by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the DSPy package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.dspy.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () =>\n      `import dspy\n\nmlflow.dspy.autolog()\n\n# Configure the LLM to use. Please ensure that\n# the OPENAI_API_KEY environment variable is set\nlm = dspy.LM(\"openai/gpt-4o-mini\")\ndspy.configure(lm=lm)\n\n# Define a simple chain-of-thought model and run it\nmath = dspy.ChainOfThought(\"question -> answer: float\")\nquestion = \"Two dice are tossed. What is the probability that the sum equals two?\"\n\n# All intermediate outputs from the execution will be logged\nmath(question=question)`,\n  },\n  crewai: {\n    minVersion: '2.19.0',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for CrewAI executions by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the CrewAI package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.crewai.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () => `from crewai import Agent, Crew, Process, Task\n\nmlflow.crewai.autolog()\n\ncity_selection_agent = Agent(\n    role=\"City selection expert\",\n    goal=\"Select the best city based on weather, season, and prices\",\n    backstory=\"An expert in analyzing travel data to pick ideal destinations\",\n    allow_delegation=True,\n    verbose=True,\n)\n\nlocal_expert = Agent(\n    role=\"Local expert\",\n    goal=\"Provide the best insights about the selected city\",\n    backstory=\"A local guide with extensive information about the city\",\n    verbose=True,\n)\n  \nplan_trip = Task(\n    name=\"Plan a trip\",\n    description=\"\"\"Plan a trip to a city based on weather, prices, and best local attractions. \n    Please consult with a local expert when researching things to do.\"\"\",\n    expected_output=\"A short summary of the trip destination and key things to do\",\n    agent=city_selection_agent,\n)\n\ncrew = Crew(\n  agents=[\n    city_selection_agent,\n    local_expert,\n  ],\n  tasks=[plan_trip],\n  process=Process.sequential\n)\n\n# Ensure the \"OPENAI_API_KEY\" environment variable is set\n# before kicking off the crew. All intermediate agent outputs\n# will be logged in the resulting trace.\ncrew.kickoff()`,\n  },\n  autogen: {\n    minVersion: '2.16.2',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for AutoGen conversations by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the AutoGen package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.autogen.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () =>\n      `import os\nfrom autogen import AssistantAgent, UserProxyAgent\n\nmlflow.autogen.autolog()\n\n# Ensure that the \"OPENAI_API_KEY\" environment variable is set\nllm_config = { \"model\": \"gpt-4o-mini\", \"api_key\": os.environ[\"OPENAI_API_KEY\"] }\nassistant = AssistantAgent(\"assistant\", llm_config = llm_config)\nuser_proxy = UserProxyAgent(\"user_proxy\", code_execution_config = False)\n\n# All intermediate executions within the chat session will be logged\nuser_proxy.initiate_chat(assistant, message = \"What is MLflow?\", max_turns = 1)`,\n  },\n  anthropic: {\n    minVersion: '2.19.0',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for Anthropic API calls by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the Anthropic package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.anthropic.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () => `import os\nimport anthropic\n\n# Enable auto-tracing for Anthropic\nmlflow.anthropic.autolog()\n\n# Configure your API key (please ensure that the \"ANTHROPIC_API_KEY\" environment variable is set)\nclient = anthropic.Anthropic(api_key=os.environ[\"ANTHROPIC_API_KEY\"])\n\n# Inputs and outputs of API calls will be logged as a trace\nmessage = client.messages.create(\n    model=\"claude-3-5-sonnet-20241022\",\n    max_tokens=1024,\n    messages=[\n        {\"role\": \"user\", \"content\": \"Hello, Claude\"},\n    ],\n)`,\n  },\n  bedrock: {\n    minVersion: '2.20.0',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for Bedrock conversations by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the Bedrock package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.bedrock.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () => `import boto3\n\nmlflow.bedrock.autolog()\n\n# Ensure that your boto3 client has the necessary auth information\nbedrock = boto3.client(\n    service_name=\"bedrock-runtime\",\n    region_name=\"<REPLACE_WITH_YOUR_AWS_REGION>\",\n)\n\nmodel = \"anthropic.claude-3-5-sonnet-20241022-v2:0\"\nmessages = [{ \"role\": \"user\", \"content\": [{\"text\": \"Hello!\"}]}]\n\n# All intermediate executions within the chat session will be logged\nbedrock.converse(modelId=model, messages=messages)`,\n  },\n  litellm: {\n    minVersion: '2.18.0',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for LiteLLM API calls by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for the LiteLLM package using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.litellm.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () => `import litellm\n\nmlflow.litellm.autolog()\n\n# Ensure that the \"OPENAI_API_KEY\" environment variable is set\nmessages = [{\"role\": \"user\", \"content\": \"Hello!\"}]\n\n# Inputs and outputs of the API request will be logged in a trace\nlitellm.completion(model=\"gpt-4o-mini\", messages=messages)`,\n  },\n  gemini: {\n    minVersion: '2.18.0',\n    getContent: () => (\n      <FormattedMessage\n        defaultMessage=\"Automatically log traces for Gemini conversations by calling the {code} function. For example:\"\n        description=\"Description of how to log traces for API calls to Google's Gemini API using MLflow autologging. This message is followed by a code example.\"\n        values={{\n          code: <code>mlflow.gemini.autolog()</code>,\n        }}\n      />\n    ),\n    getCodeSource: () => `import google.genai as genai\n\nmlflow.gemini.autolog()\n\n# Replace \"GEMINI_API_KEY\" with your API key\nclient = genai.Client(api_key=\"GEMINI_API_KEY\")\n\n# Inputs and outputs of the API request will be logged in a trace\nclient.models.generate_content(model=\"gemini-1.5-flash\", contents=\"Hello!\")`,\n  },\n  custom: {\n    minVersion: '2.14.3',\n    getContent: (baseComponentId) => (\n      <>\n        <Typography.Paragraph>\n          <FormattedMessage\n            defaultMessage={\n              'To manually instrument your own traces, the most convenient method is to use the {code} function decorator. ' +\n              'This will cause the inputs and outputs of the function to be captured in the trace.'\n            }\n            description=\"Description of how to log custom code traces using MLflow. This message is followed by a code example.\"\n            values={{\n              code: <code>@mlflow.trace</code>,\n            }}\n          />\n        </Typography.Paragraph>\n        <Typography.Paragraph>\n          <FormattedMessage\n            defaultMessage={\n              'For more complex use cases, MLflow also provides granular APIs that can be used to ' +\n              'control tracing behavior. For more information, please visit the <a>official documentation</a> on ' +\n              'fluent and client APIs for MLflow Tracing.'\n            }\n            description={\n              'Explanation of alternative APIs for custom tracing in MLflow. ' +\n              'The link leads to the MLflow documentation for the user to learn more.'\n            }\n            values={{\n              a: (text: string) => (\n                <Typography.Link\n                  title=\"official documentation\"\n                  componentId={`${baseComponentId}.traces_table.custom_tracing_docs_link`}\n                  href=\"https://mlflow.org/docs/latest/llms/tracing/index.html#tracing-fluent-apis\"\n                  openInNewTab\n                >\n                  {text}\n                </Typography.Link>\n              ),\n            }}\n          />\n        </Typography.Paragraph>\n      </>\n    ),\n    getCodeSource: () =>\n      `@mlflow.trace\ndef foo(a):\nreturn a + bar(a)\n\n# Various attributes can be passed to the decorator\n# to modify the information contained in the span\**************(name = \"custom_name\", attributes = { \"key\": \"value\" })\ndef bar(b):\nreturn b + 1\n\n# Invoking the traced function will cause a trace to be logged\nfoo(1)`,\n  },\n};\n", "import { CopyIcon, Typography, useDesignSystemTheme, Alert } from '@databricks/design-system';\nimport { CodeSnippet } from '@databricks/web-shared/snippet';\nimport { CopyButton } from '@mlflow/mlflow/src/shared/building_blocks/CopyButton';\n\nimport { QUICKSTART_CONTENT } from './TraceTableQuickstart.utils';\nimport { FormattedMessage } from 'react-intl';\nimport { useTracesViewTableNoTracesQuickstartContext } from './TracesViewTableNoTracesQuickstartContext';\n\nexport const TraceTableGenericQuickstart = ({\n  flavorName,\n  baseComponentId,\n}: {\n  flavorName: keyof typeof QUICKSTART_CONTENT;\n  baseComponentId: string;\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const { getContent, getCodeSource, minVersion } = QUICKSTART_CONTENT[flavorName];\n  const { displayVersionWarnings = true } = useTracesViewTableNoTracesQuickstartContext();\n  const content = getContent(baseComponentId);\n  const versionCheck = `import mlflow\nfrom packaging.version import Version\n\nassert Version(mlflow.__version__) >= Version(\"${minVersion}\"), (\n  \"This feature requires MLflow version ${minVersion} or newer. \"\n  \"Please run '%pip install -U mlflow' in a notebook cell, \"\n  \"and restart the kernel when the command finishes.\"\n)\n\n`;\n\n  const code = versionCheck + getCodeSource();\n\n  const alertContent = (\n    <FormattedMessage\n      defaultMessage={\n        'This example requires MLflow version {minVersion} or newer. ' +\n        'Please run {installCommand} in a notebook cell if your MLflow version is older than this, ' +\n        'and restart the kernel when the command finishes.'\n      }\n      description=\"Alert description informing the user of how to upgrade MLflow to the minimum required version\"\n      values={{\n        minVersion,\n        installCommand: <Typography.Text code>%pip install -U mlflow</Typography.Text>,\n      }}\n    />\n  );\n\n  return (\n    <div>\n      {displayVersionWarnings && (\n        <Alert\n          componentId={`${baseComponentId}.traces_table.${flavorName}_quickstart_alert`}\n          css={{ marginBottom: theme.spacing.md }}\n          closable={false}\n          message={\n            <FormattedMessage\n              defaultMessage=\"Requires MLflow >= {minVersion}\"\n              description=\"Alert title informing the user of the minimum required MLflow version to run the code example\"\n              values={{ minVersion }}\n            />\n          }\n          description={alertContent}\n          type=\"info\"\n        />\n      )}\n      <Typography.Text>{content}</Typography.Text>\n      <div css={{ position: 'relative', width: 'min-content' }}>\n        <CopyButton\n          componentId={`${baseComponentId}.traces_table.${flavorName}_quickstart_snippet_copy`}\n          css={{ zIndex: 1, position: 'absolute', top: theme.spacing.xs, right: theme.spacing.xs }}\n          showLabel={false}\n          copyText={code}\n          icon={<CopyIcon />}\n        />\n        <CodeSnippet\n          showLineNumbers\n          theme={theme.isDarkMode ? 'duotoneDark' : 'light'}\n          style={{\n            padding: `${theme.spacing.sm}px ${theme.spacing.md}px`,\n            marginTop: theme.spacing.md,\n          }}\n          language=\"python\"\n        >\n          {code}\n        </CodeSnippet>\n      </div>\n    </div>\n  );\n};\n", "import { Header, Tabs, Typography, useDesignSystemTheme } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\nimport { isNil, keys } from 'lodash';\nimport { TraceTableGenericQuickstart } from './TraceTableGenericQuickstart';\nimport { QUICKSTART_CONTENT, QUICKSTART_FLAVOR } from './TraceTableQuickstart.utils';\nimport { useTracesViewTableNoTracesQuickstartContext } from './TracesViewTableNoTracesQuickstartContext';\n\nexport const TracesViewTableNoTracesQuickstart = ({\n  baseComponentId,\n  experimentIds,\n  runUuid,\n}: {\n  baseComponentId: string;\n  experimentIds: string[];\n  runUuid?: string;\n}) => {\n  const { theme } = useDesignSystemTheme();\n  const { introductionText } = useTracesViewTableNoTracesQuickstartContext();\n\n  return (\n    <div css={{ marginLeft: -theme.spacing.md }}>\n      <Header\n        title={\n          <FormattedMessage\n            defaultMessage=\"No traces recorded\"\n            description=\"Message displayed when there are no traces logged to the experiment\"\n          />\n        }\n        titleElementLevel={3}\n      />\n      <Typography.Text\n        css={{\n          display: 'block',\n          marginTop: theme.spacing.md,\n          marginBottom: theme.spacing.md,\n        }}\n      >\n        {introductionText ?? (\n          <FormattedMessage\n            defaultMessage={\n              'This tab displays all the traces logged to this {isRun, select, true {run} other {experiment}}. ' +\n              'MLflow supports automatic tracing for many popular generative AI frameworks. Follow the steps below to log ' +\n              'your first trace. For more information about MLflow Tracing, visit the <a>MLflow documentation</a>.'\n            }\n            description={\n              \"Message that explains the function of the 'Traces' tab in the MLflow UI.\" +\n              'This message is followed by a tutorial explaining how to get started with MLflow Tracing.'\n            }\n            values={{\n              isRun: !isNil(runUuid),\n              a: (text: string) => (\n                <Typography.Link\n                  componentId={`${baseComponentId}.traces_table.quickstart_docs_link`}\n                  href=\"https://mlflow.org/docs/latest/llms/tracing/index.html\"\n                  openInNewTab\n                >\n                  {text}\n                </Typography.Link>\n              ),\n            }}\n          />\n        )}\n      </Typography.Text>\n      <Tabs.Root componentId={`${baseComponentId}.traces_table.quickstart`} defaultValue=\"openai\">\n        <Tabs.List>\n          <Tabs.Trigger value=\"openai\">\n            <FormattedMessage\n              defaultMessage=\"OpenAI\"\n              description=\"Header for OpenAI tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"langchain\">\n            <FormattedMessage\n              defaultMessage=\"LangChain / LangGraph\"\n              description=\"Header for LangChain / LangGraph tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"llama_index\">\n            <FormattedMessage\n              defaultMessage=\"LlamaIndex\"\n              description=\"Header for LlamaIndex tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"dspy\">\n            <FormattedMessage\n              defaultMessage=\"DSPy\"\n              description=\"Header for DSPy tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"crewai\">\n            <FormattedMessage\n              defaultMessage=\"CrewAI\"\n              description=\"Header for CrewAI tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"autogen\">\n            <FormattedMessage\n              defaultMessage=\"AutoGen\"\n              description=\"Header for AutoGen tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"anthropic\">\n            <FormattedMessage\n              defaultMessage=\"Anthropic\"\n              description=\"Header for Anthropic tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"bedrock\">\n            <FormattedMessage\n              defaultMessage=\"Bedrock\"\n              description=\"Header for Bedrock tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"litellm\">\n            <FormattedMessage\n              defaultMessage=\"LiteLLM\"\n              description=\"Header for LiteLLM tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"gemini\">\n            <FormattedMessage\n              defaultMessage=\"Gemini\"\n              description=\"Header for Gemini tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"custom\">\n            <FormattedMessage\n              defaultMessage=\"Custom\"\n              description=\"Header for custom tracing tab in the MLflow Tracing quickstart guide\"\n            />\n          </Tabs.Trigger>\n        </Tabs.List>\n        {keys(QUICKSTART_CONTENT).map((flavorName) => (\n          <Tabs.Content value={flavorName as QUICKSTART_FLAVOR} key={flavorName + '_content'}>\n            <TraceTableGenericQuickstart\n              flavorName={flavorName as QUICKSTART_FLAVOR}\n              baseComponentId={baseComponentId}\n            />\n          </Tabs.Content>\n        ))}\n      </Tabs.Root>\n    </div>\n  );\n};\n", "import {\n  <PERSON>ursorPagination,\n  DangerIcon,\n  Empty,\n  Table,\n  TableHeader,\n  TableRow,\n  TableSkeletonRows,\n  Typography,\n  useDesignSystemTheme,\n  Button,\n  DropdownMenu,\n  TableRowAction,\n  ColumnsIcon,\n} from '@databricks/design-system';\nimport { SortingState, flexRender, getCoreRowModel, getSortedRowModel, useReactTable } from '@tanstack/react-table';\nimport React, { useMemo } from 'react';\nimport { isNil, entries } from 'lodash';\nimport Utils from '../../../common/utils/Utils';\nimport { Link } from '../../../common/utils/RoutingUtils';\nimport { ErrorWrapper } from '../../../common/utils/ErrorWrapper';\nimport Routes from '../../routes';\nimport {\n  ExperimentViewTracesTableColumnLabels,\n  ExperimentViewTracesTableColumns,\n  TRACE_TABLE_CHECKBOX_COLUMN_ID,\n  TRACE_TAG_NAME_TRACE_NAME,\n  getTraceInfoRunId,\n  getTraceInfoTotalTokens,\n  getTraceTagValue,\n} from './TracesView.utils';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { type ModelTraceInfo } from '@databricks/web-shared/model-trace-explorer';\nimport { TracesViewTableTagCell } from './TracesViewTableTagCell';\nimport type { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\nimport { TracesViewTableStatusCell } from './TracesViewTableStatusCell';\nimport { TracesViewTableRequestPreviewCell, TracesViewTableResponsePreviewCell } from './TracesViewTablePreviewCell';\nimport { TracesViewTableSourceCell } from './TracesViewTableSourceCell';\nimport { TracesColumnDef, getColumnSizeClassName, getHeaderSizeClassName } from './TracesViewTable.utils';\nimport { TracesViewTableRow } from './TracesViewTableRow';\nimport { TracesViewTableTimestampCell } from './TracesViewTableTimestampCell';\nimport { TracesViewTableHeaderCheckbox } from './TracesViewTableHeaderCheckbox';\nimport { TracesViewTableCellCheckbox } from './TracesViewTableCellCheckbox';\nimport { TracesViewTableNoTracesQuickstart } from './quickstart/TracesViewTableNoTracesQuickstart';\nimport { isUnstableNestedComponentsMigrated } from '@mlflow/mlflow/src/common/utils/FeatureUtils';\n\nexport interface TracesViewTableProps {\n  experimentIds: string[];\n  runUuid?: string;\n  traces: ModelTraceInfoWithRunName[];\n  onTraceClicked?: (trace: ModelTraceInfo) => void;\n  onTraceTagsEdit?: (trace: ModelTraceInfo) => void;\n  onTagsUpdated?: () => void;\n  loading: boolean;\n  error?: Error;\n  usingFilters?: boolean;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n  onNextPage: () => void;\n  onPreviousPage: () => void;\n  onResetFilters: () => void;\n  sorting: SortingState;\n  setSorting: React.Dispatch<React.SetStateAction<SortingState>>;\n  rowSelection: { [id: string]: boolean };\n  setRowSelection: React.Dispatch<React.SetStateAction<{ [id: string]: boolean }>>;\n  hiddenColumns?: string[];\n  disableTokenColumn?: boolean;\n  baseComponentId: string;\n  toggleHiddenColumn: (columnId: string) => void;\n  disabledColumns?: string[];\n}\n\ntype TracesViewTableMeta = {\n  baseComponentId: string;\n  onTraceClicked?: TracesViewTableProps['onTraceClicked'];\n  onTraceTagsEdit?: TracesViewTableProps['onTraceTagsEdit'];\n};\n\nconst RequestIdCell: TracesColumnDef['cell'] = ({\n  row: { original },\n  table: {\n    options: { meta },\n  },\n}) => {\n  const { baseComponentId, onTraceClicked } = meta as TracesViewTableMeta;\n  return (\n    <Typography.Link\n      componentId={`${baseComponentId}.traces_table.request_id_link`}\n      ellipsis\n      css={{ maxWidth: '100%', textOverflow: 'ellipsis' }}\n      onClick={() => {\n        onTraceClicked?.(original);\n      }}\n    >\n      {original.request_id}\n    </Typography.Link>\n  );\n};\n\nconst TraceNameCell: TracesColumnDef['cell'] = ({\n  row: { original },\n  table: {\n    options: { meta },\n  },\n}) => {\n  const { baseComponentId, onTraceClicked } = meta as TracesViewTableMeta;\n  return (\n    <Typography.Link\n      componentId={`${baseComponentId}.traces_table.trace_name_link`}\n      ellipsis\n      css={{ maxWidth: '100%', textOverflow: 'ellipsis' }}\n      onClick={() => {\n        onTraceClicked?.(original);\n      }}\n    >\n      {getTraceTagValue(original, TRACE_TAG_NAME_TRACE_NAME)}\n    </Typography.Link>\n  );\n};\n\nconst RunNameCell: TracesColumnDef['cell'] = ({ row: { original } }) => {\n  const runId = getTraceInfoRunId(original);\n  if (!runId || !original.experiment_id) {\n    return null;\n  }\n  const label = original.runName || runId;\n  return (\n    <Link\n      css={{\n        maxWidth: '100%',\n        textOverflow: 'ellipsis',\n        display: 'inline-block',\n        overflow: 'hidden',\n      }}\n      to={Routes.getRunPageRoute(original.experiment_id, runId)}\n    >\n      {label}\n    </Link>\n  );\n};\n\nconst TraceTagsCell: TracesColumnDef['cell'] = ({\n  row: { original },\n  table: {\n    options: { meta },\n  },\n}) => {\n  const { onTraceTagsEdit, baseComponentId } = meta as TracesViewTableMeta;\n  return (\n    <TracesViewTableTagCell\n      tags={original.tags || []}\n      onAddEditTags={() => onTraceTagsEdit?.(original)}\n      baseComponentId={baseComponentId}\n    />\n  );\n};\n\ntype ColumnListItem = {\n  key: string;\n  label: string;\n};\n\nexport const TracesViewTable = React.memo(\n  ({\n    experimentIds,\n    runUuid,\n    traces,\n    loading,\n    error,\n    onTraceClicked,\n    onTraceTagsEdit,\n    hasNextPage,\n    hasPreviousPage,\n    onNextPage,\n    onPreviousPage,\n    usingFilters,\n    onResetFilters,\n    sorting,\n    setSorting,\n    rowSelection,\n    setRowSelection,\n    hiddenColumns = [],\n    disableTokenColumn,\n    baseComponentId,\n    toggleHiddenColumn,\n    disabledColumns = [],\n  }: TracesViewTableProps) => {\n    const intl = useIntl();\n    const { theme } = useDesignSystemTheme();\n\n    const useStaticColumnsCells = isUnstableNestedComponentsMigrated();\n\n    const allColumnsList = useMemo<ColumnListItem[]>(() => {\n      return entries(ExperimentViewTracesTableColumnLabels)\n        .map(([key, label]) => ({\n          key,\n          label: intl.formatMessage(label),\n        }))\n        .filter(({ key }) => !disabledColumns.includes(key));\n    }, [intl, disabledColumns]);\n\n    const columns = useMemo<TracesColumnDef[]>(() => {\n      const columns: TracesColumnDef[] = [\n        {\n          id: TRACE_TABLE_CHECKBOX_COLUMN_ID,\n          header: TracesViewTableHeaderCheckbox,\n          enableResizing: false,\n          enableSorting: false,\n          cell: TracesViewTableCellCheckbox,\n          meta: { styles: { minWidth: 32, maxWidth: 32 } },\n        },\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.requestId]),\n          enableSorting: false,\n          enableResizing: true,\n          id: ExperimentViewTracesTableColumns.requestId,\n          cell: useStaticColumnsCells\n            ? RequestIdCell\n            : ({ row: { original } }) => {\n                return (\n                  <Typography.Link\n                    componentId={`${baseComponentId}.traces_table.request_id_link`}\n                    ellipsis\n                    css={{ maxWidth: '100%', textOverflow: 'ellipsis' }}\n                    onClick={() => {\n                      onTraceClicked?.(original);\n                    }}\n                  >\n                    {original.request_id}\n                  </Typography.Link>\n                );\n              },\n          meta: { styles: { minWidth: 200 } },\n        },\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.traceName]),\n          enableSorting: false,\n          enableResizing: true,\n          id: ExperimentViewTracesTableColumns.traceName,\n          cell: useStaticColumnsCells\n            ? TraceNameCell\n            : ({ row: { original } }) => {\n                return (\n                  <Typography.Link\n                    componentId={`${baseComponentId}.traces_table.trace_name_link`}\n                    ellipsis\n                    css={{ maxWidth: '100%', textOverflow: 'ellipsis' }}\n                    onClick={() => {\n                      onTraceClicked?.(original);\n                    }}\n                  >\n                    {getTraceTagValue(original, TRACE_TAG_NAME_TRACE_NAME)}\n                  </Typography.Link>\n                );\n              },\n          meta: { styles: { minWidth: 150 } },\n        },\n        {\n          header: intl.formatMessage(\n            ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.timestampMs],\n          ),\n          id: ExperimentViewTracesTableColumns.timestampMs,\n          accessorFn: (data) => data.timestamp_ms,\n          enableSorting: true,\n          enableResizing: true,\n          cell: TracesViewTableTimestampCell,\n          meta: { styles: { minWidth: 100 } },\n        },\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.status]),\n          id: ExperimentViewTracesTableColumns.status,\n          enableSorting: false,\n          enableResizing: true,\n          cell: TracesViewTableStatusCell,\n          meta: { styles: { minWidth: 100 } },\n        },\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.inputs]),\n          id: ExperimentViewTracesTableColumns.inputs,\n          enableSorting: false,\n          enableResizing: true,\n          cell: TracesViewTableRequestPreviewCell,\n          meta: { multiline: true },\n        },\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.outputs]),\n          enableSorting: false,\n          enableResizing: true,\n          id: ExperimentViewTracesTableColumns.outputs,\n          cell: TracesViewTableResponsePreviewCell,\n          meta: { multiline: true },\n        },\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.runName]),\n          enableSorting: false,\n          enableResizing: true,\n          id: ExperimentViewTracesTableColumns.runName,\n          cell: useStaticColumnsCells\n            ? RunNameCell\n            : ({ row: { original } }) => {\n                const runId = getTraceInfoRunId(original);\n                if (!runId || !original.experiment_id) {\n                  return null;\n                }\n                const label = original.runName || runId;\n                return (\n                  <Link\n                    css={{\n                      maxWidth: '100%',\n                      textOverflow: 'ellipsis',\n                      display: 'inline-block',\n                      overflow: 'hidden',\n                    }}\n                    to={Routes.getRunPageRoute(original.experiment_id, runId)}\n                  >\n                    {label}\n                  </Link>\n                );\n              },\n        },\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.source]),\n          enableSorting: true,\n          enableResizing: true,\n          id: ExperimentViewTracesTableColumns.source,\n          cell: TracesViewTableSourceCell,\n          meta: { styles: { minWidth: 100 } },\n        },\n      ];\n\n      if (!disableTokenColumn) {\n        columns.push({\n          header: intl.formatMessage(\n            ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.totalTokens],\n          ),\n          enableSorting: false,\n          enableResizing: true,\n          id: ExperimentViewTracesTableColumns.totalTokens,\n          accessorFn: (data) => getTraceInfoTotalTokens(data),\n          meta: { styles: { minWidth: 80, maxWidth: 80 } },\n        });\n      }\n      columns.push(\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.latency]),\n          enableSorting: false,\n          enableResizing: true,\n          id: ExperimentViewTracesTableColumns.latency,\n          accessorFn: (data) => {\n            if (isNil(data.execution_time_ms) || !isFinite(data.execution_time_ms)) {\n              return null;\n            }\n            return Utils.formatDuration(data.execution_time_ms);\n          },\n          meta: { styles: { minWidth: 100 } },\n        },\n        {\n          header: intl.formatMessage(ExperimentViewTracesTableColumnLabels[ExperimentViewTracesTableColumns.tags]),\n          enableSorting: false,\n          enableResizing: true,\n          id: ExperimentViewTracesTableColumns.tags,\n          cell: useStaticColumnsCells\n            ? TraceTagsCell\n            : ({ row: { original } }) => {\n                return (\n                  <TracesViewTableTagCell\n                    tags={original.tags || []}\n                    onAddEditTags={() => onTraceTagsEdit?.(original)}\n                    baseComponentId={baseComponentId}\n                  />\n                );\n              },\n        },\n      );\n\n      return columns.filter((column) => column.id && !hiddenColumns.includes(column.id));\n    }, [\n      intl,\n      onTraceClicked,\n      onTraceTagsEdit,\n      disableTokenColumn,\n      hiddenColumns,\n      baseComponentId,\n      useStaticColumnsCells,\n    ]);\n\n    const table = useReactTable<ModelTraceInfoWithRunName>({\n      columns,\n      data: traces,\n      state: { sorting, rowSelection },\n      getCoreRowModel: getCoreRowModel(),\n      getRowId: (row, index) => row.request_id || index.toString(),\n      getSortedRowModel: getSortedRowModel(),\n      onSortingChange: setSorting,\n      onRowSelectionChange: setRowSelection,\n      enableColumnResizing: true,\n      enableRowSelection: true,\n      columnResizeMode: 'onChange',\n      meta: { baseComponentId, onTraceClicked, onTraceTagsEdit } satisfies TracesViewTableMeta,\n    });\n\n    const getEmptyState = () => {\n      if (error) {\n        const errorMessage = error instanceof ErrorWrapper ? error.getMessageField() : error.message;\n        return (\n          <Empty\n            image={<DangerIcon />}\n            description={errorMessage}\n            title={\n              <FormattedMessage\n                defaultMessage=\"Error\"\n                description=\"Experiment page > traces table > error state title\"\n              />\n            }\n          />\n        );\n      }\n      if (!loading && traces.length === 0 && usingFilters) {\n        return (\n          <Empty\n            description={\n              <FormattedMessage\n                defaultMessage=\"No traces found with the current filter query. <button>Reset filters</button> to see all traces.\"\n                description=\"Experiment page > traces table > no traces recorded\"\n                values={{\n                  button: (chunks: any) => (\n                    <Typography.Link\n                      componentId=\"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewtable.tsx_289\"\n                      onClick={onResetFilters}\n                    >\n                      {chunks}\n                    </Typography.Link>\n                  ),\n                }}\n              />\n            }\n            title={\n              <FormattedMessage\n                defaultMessage=\"No traces found\"\n                description=\"Experiment page > traces table > no traces recorded\"\n              />\n            }\n          />\n        );\n      }\n      if (!loading && traces.length === 0) {\n        return (\n          <TracesViewTableNoTracesQuickstart\n            baseComponentId={baseComponentId}\n            experimentIds={experimentIds}\n            runUuid={runUuid}\n          />\n        );\n      }\n      return null;\n    };\n\n    // to improve performance, we pass the column sizes as inline styles to the table\n    const columnSizeInfo = table.getState().columnSizingInfo;\n    const columnSizeVars = React.useMemo(() => {\n      const headers = table.getFlatHeaders();\n      const colSizes: { [key: string]: number } = {};\n      headers.forEach((header) => {\n        colSizes[getHeaderSizeClassName(header.id)] = header.getSize();\n        colSizes[getColumnSizeClassName(header.column.id)] = header.column.getSize();\n      });\n      return colSizes;\n      // we need to recompute this whenever columns get resized or changed\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [columnSizeInfo, columns, table]);\n\n    return (\n      <Table\n        scrollable\n        empty={getEmptyState()}\n        style={columnSizeVars}\n        pagination={\n          <CursorPagination\n            componentId={`${baseComponentId}.traces_table.pagination`}\n            hasNextPage={hasNextPage}\n            hasPreviousPage={hasPreviousPage}\n            onNextPage={onNextPage}\n            onPreviousPage={onPreviousPage}\n          />\n        }\n      >\n        <TableRow isHeader>\n          {table.getLeafHeaders().map((header) => {\n            return (\n              <TableHeader\n                componentId=\"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewtable.tsx_365\"\n                key={header.id}\n                css={(header.column.columnDef as TracesColumnDef).meta?.styles}\n                sortable={header.column.getCanSort()}\n                sortDirection={header.column.getIsSorted() || 'none'}\n                onToggleSort={header.column.getToggleSortingHandler()}\n                header={header}\n                column={header.column}\n                setColumnSizing={table.setColumnSizing}\n                isResizing={header.column.getIsResizing()}\n                style={{\n                  flex: `calc(var(${getHeaderSizeClassName(header.id)}) / 100)`,\n                }}\n              >\n                {flexRender(header.column.columnDef.header, header.getContext())}\n              </TableHeader>\n            );\n          })}\n          <TableRowAction>\n            <DropdownMenu.Root>\n              <DropdownMenu.Trigger asChild>\n                <Button\n                  componentId={`${baseComponentId}.traces_table.column_selector_dropdown`}\n                  icon={<ColumnsIcon />}\n                  size=\"small\"\n                  aria-label={intl.formatMessage({\n                    defaultMessage: 'Select columns',\n                    description: 'Experiment page > traces table > column selector dropdown aria label',\n                  })}\n                />\n              </DropdownMenu.Trigger>\n              <DropdownMenu.Content align=\"end\">\n                {allColumnsList.map(({ key, label }) => (\n                  <DropdownMenu.CheckboxItem\n                    key={key}\n                    componentId={`${baseComponentId}.traces_table.column_toggle_button`}\n                    checked={!hiddenColumns.includes(key)}\n                    onClick={() => toggleHiddenColumn(key)}\n                  >\n                    <DropdownMenu.ItemIndicator />\n                    {label}\n                  </DropdownMenu.CheckboxItem>\n                ))}\n              </DropdownMenu.Content>\n            </DropdownMenu.Root>\n          </TableRowAction>\n        </TableRow>\n        {loading && <TableSkeletonRows table={table} />}\n        {!loading &&\n          !error &&\n          table\n            .getRowModel()\n            .rows.map((row) => (\n              <TracesViewTableRow key={row.id} row={row} columns={columns} selected={rowSelection[row.id]} />\n            ))}\n      </Table>\n    );\n  },\n);\n", "import React, { useEffect, useRef } from 'react';\n\nimport {\n  ModelTraceChildToParentFrameMessage,\n  ModelTraceParentToChildFrameMessage,\n} from '@databricks/web-shared/model-trace-explorer';\nimport type { ModelTrace, ModelTraceChildToParentFrameMessageType } from '@databricks/web-shared/model-trace-explorer';\nimport { TableSkeleton, TitleSkeleton } from '@databricks/design-system';\nimport { Version } from '../../../common/constants';\n\nexport const ModelTraceExplorerFrameRenderer = ({\n  modelTrace,\n  height = 700,\n  useLatestVersion = false,\n}: {\n  modelTrace: ModelTrace;\n  height?: number | string;\n  useLatestVersion?: boolean;\n}) => {\n  const iframeRef = useRef<HTMLIFrameElement>(null);\n  const [isLoading, setIsLoading] = React.useState(true);\n\n  useEffect(() => {\n    const handleMessage = (event: MessageEvent<ModelTraceChildToParentFrameMessageType>) => {\n      // only handle messages from the child iframe\n      const iframeWindow = iframeRef.current?.contentWindow;\n      if (!iframeWindow || event.source !== iframeWindow) {\n        return;\n      }\n\n      switch (event.data.type) {\n        case ModelTraceChildToParentFrameMessage.Ready: {\n          setIsLoading(false);\n          break;\n        }\n        default:\n          break;\n      }\n    };\n\n    window.addEventListener('message', handleMessage);\n    return () => {\n      window.removeEventListener('message', handleMessage);\n    };\n  }, [modelTrace]);\n\n  useEffect(() => {\n    const iframeWindow = iframeRef.current?.contentWindow;\n    if (!iframeWindow || isLoading) {\n      return;\n    }\n\n    iframeWindow.postMessage({\n      type: ModelTraceParentToChildFrameMessage.UpdateTrace,\n      traceData: modelTrace,\n    });\n  }, [modelTrace, isLoading]);\n\n  return (\n    <div css={{ height }}>\n      {isLoading && (\n        <div\n          css={{\n            position: 'absolute',\n            width: '100%',\n            height,\n          }}\n        >\n          <TitleSkeleton />\n          <TableSkeleton lines={5} />\n        </div>\n      )}\n      <iframe\n        title=\"Model Trace Explorer\"\n        // Include the current mlflow version as a query parameter to bust the browser cache\n        // generated and prevent https://github.com/mlflow/mlflow/issues/13829.\n        src={`static-files/lib/ml-model-trace-renderer/index.html?version=${Version}`}\n        ref={iframeRef}\n        css={{\n          border: 'none',\n          width: '100%',\n          height,\n        }}\n      />\n    </div>\n  );\n};\n", "export { ModelTraceExplorer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './ModelTraceExplorerFrameRenderer';\n\nexport enum ModelSpanType {\n  LLM = 'LLM',\n  CHAIN = 'CHAIN',\n  AGENT = 'AGENT',\n  TOOL = 'TOOL',\n  FUNCTION = 'FUNCTION',\n  CHAT_MODEL = 'CHAT_MODEL',\n  RETRIEVER = 'RETRIEVER',\n  PARSER = 'PARSER',\n  EMBEDDING = 'EMBEDDING',\n  RERANKER = 'RERANKER',\n  UNKNOWN = 'UNKNOWN',\n}\n\nexport enum ModelIconType {\n  MODELS = 'models',\n  DOCUMENT = 'document',\n  CONNECT = 'connect',\n}\n\n/**\n * Represents a single model trace span.\n * Based on https://github.com/mlflow/mlflow/blob/tracing/mlflow/entities/span.py\n */\nexport type ModelTraceSpan = {\n  context: {\n    span_id: string;\n    trace_id: string;\n  };\n  name: string;\n  /* deprecated, renamed to `parent_id` */\n  parent_span_id?: string | null;\n  parent_id?: string | null;\n  /* deprecated, contained in attributes['mlflow.spanType'] */\n  span_type?: ModelSpanType | string;\n  /* deprecated, migrated to `status_code` and `status_message` */\n  status?: ModelTraceStatus;\n  status_code?: string;\n  status_message?: string | null;\n  start_time: number;\n  end_time: number;\n  /* deprecated, contained in attributes['mlflow.spanInputs'] */\n  inputs?: any;\n  /* deprecated, contained in attributes['mlflow.spanOutputs'] */\n  outputs?: any;\n  attributes?: Record<string, any>;\n  /* metadata for ui usage logging */\n  type: ModelSpanType;\n};\n\nexport type ModelTraceEvent = {\n  name: string;\n  timestamp?: number;\n  attributes?: Record<string, any>;\n};\n\nexport type ModelTraceData = {\n  spans: ModelTraceSpan[];\n};\n\n/**\n * Represents a single model trace object.\n * Based on https://github.com/mlflow/mlflow/blob/8e44d102e9568d09d9dc376136d13a5a5d1ab46f/mlflow/tracing/types/model.py#L11\n */\nexport type ModelTrace = {\n  /* deprecated, renamed to `data` */\n  trace_data?: ModelTraceData;\n  /* deprecated, renamed to `info` */\n  trace_info?: ModelTraceInfo;\n  data: ModelTraceData;\n  info: ModelTraceInfo;\n};\n\nexport type ModelTraceInfo = {\n  request_id?: string;\n  experiment_id?: string;\n  timestamp_ms?: number;\n  execution_time_ms?: number;\n  status?: ModelTraceStatus['description'];\n  attributes?: Record<string, any>;\n  request_metadata?: { key: string; value: string }[];\n  tags?: { key: string; value: string }[];\n};\n\nexport type ModelTraceStatusUnset = {\n  description: 'UNSET';\n  status_code: 0;\n};\n\nexport type ModelTraceStatusOk = {\n  description: 'OK';\n  status_code: 1;\n};\n\nexport type ModelTraceStatusError = {\n  description: 'ERROR';\n  status_code: 2;\n};\n\nexport type ModelTraceStatusInProgress = {\n  description: 'IN_PROGRESS';\n  status_code: 3;\n};\n\nexport enum ModelTraceSpanType {\n  LLM = 'LLM',\n  CHAIN = 'CHAIN',\n  AGENT = 'AGENT',\n  TOOL = 'TOOL',\n  CHAT_MODEL = 'CHAT_MODEL',\n  RETRIEVER = 'RETRIEVER',\n  PARSER = 'PARSER',\n  EMBEDDING = 'EMBEDDING',\n  RERANKER = 'RERANKER',\n  UNKNOWN = 'UNKNOWN',\n}\n\nexport type ModelTraceStatus =\n  | ModelTraceStatusUnset\n  | ModelTraceStatusOk\n  | ModelTraceStatusError\n  | ModelTraceStatusInProgress;\n\nexport enum ModelTraceChildToParentFrameMessage {\n  Ready = 'READY',\n}\n\ntype ModelTraceFrameReadyMessage = {\n  type: ModelTraceChildToParentFrameMessage.Ready;\n};\n\nexport enum ModelTraceParentToChildFrameMessage {\n  UpdateTrace = 'UPDATE_TRACE',\n}\n\ntype ModelTraceFrameUpdateTraceMessage = {\n  type: ModelTraceParentToChildFrameMessage.UpdateTrace;\n  traceData: ModelTrace;\n};\n\nexport type ModelTraceChildToParentFrameMessageType = ModelTraceFrameReadyMessage;\nexport type ModelTraceParentToChildFrameMessageType = ModelTraceFrameUpdateTraceMessage;\n", "import { type ModelTraceInfo } from '@databricks/web-shared/model-trace-explorer';\nimport { useCallback, useEffect, useState } from 'react';\nimport { MlflowService } from '../../../sdk/MlflowService';\n\n/**\n * Fetches single trace info object for a given trace request ID.\n */\nexport const useExperimentTraceInfo = (requestId: string, enabled = true) => {\n  const [traceInfo, setTraceInfoData] = useState<ModelTraceInfo | undefined>(undefined);\n  const [loading, setLoading] = useState<boolean>(enabled);\n  const [error, setError] = useState<Error | undefined>(undefined);\n\n  const fetchTraceInfo = useCallback(async () => {\n    if (!enabled) {\n      return;\n    }\n    setError(undefined);\n\n    try {\n      const response = await MlflowService.getExperimentTraceInfo(requestId);\n\n      if (!response.trace_info) {\n        setTraceInfoData(undefined);\n        return;\n      }\n\n      setTraceInfoData(response.trace_info);\n    } catch (e: any) {\n      setError(e);\n    } finally {\n      setLoading(false);\n    }\n  }, [enabled, requestId]);\n\n  useEffect(() => {\n    fetchTraceInfo();\n  }, [fetchTraceInfo]);\n\n  return {\n    traceInfo,\n    loading,\n    error,\n  };\n};\n", "import {\n  DangerIcon,\n  Drawer,\n  Empty,\n  Spacer,\n  TableSkeleton,\n  TitleSkeleton,\n  Typography,\n  WarningIcon,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport { getTraceDisplayName } from './TracesView.utils';\nimport { useExperimentTraceData } from './hooks/useExperimentTraceData';\nimport {\n  type ModelTrace,\n  ModelTraceInfo,\n  ModelTraceExplorerFrameRenderer,\n} from '@databricks/web-shared/model-trace-explorer';\nimport { useMemo } from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { useExperimentTraceInfo } from './hooks/useExperimentTraceInfo';\n\nexport const TraceDataDrawer = ({\n  requestId,\n  traceInfo,\n  loadingTraceInfo,\n  onClose,\n  selectedSpanId,\n  onSelectSpan,\n}: {\n  requestId: string;\n  traceInfo?: ModelTraceInfo;\n  loadingTraceInfo?: boolean;\n  onClose: () => void;\n  selectedSpanId?: string;\n  onSelectSpan?: (selectedSpanId?: string) => void;\n}) => {\n  const {\n    traceData,\n    loading: loadingTraceData,\n    error,\n  } = useExperimentTraceData(\n    requestId,\n    // skip fetching trace data if trace is in progress\n    traceInfo?.status === 'IN_PROGRESS',\n  );\n  const { theme } = useDesignSystemTheme();\n\n  // Usually, we rely on the parent component to provide trace info object (when clicked in a table row).\n  // But in some cases it's not available (e.g. when deep linking to a trace when the entity is not on the same page)\n  // and then we fetch it independently here.\n  const shouldFetchTraceInfo = !loadingTraceInfo && !traceInfo;\n\n  const { traceInfo: internalTraceInfo, loading: loadingInternalTracingInfo } = useExperimentTraceInfo(\n    requestId,\n    shouldFetchTraceInfo,\n  );\n\n  const traceInfoToUse = traceInfo || internalTraceInfo;\n\n  const title = useMemo(() => {\n    if (loadingTraceInfo || loadingInternalTracingInfo) {\n      return <TitleSkeleton />;\n    }\n    if (traceInfoToUse) {\n      return (\n        <Typography.Title level={2} withoutMargins>\n          {getTraceDisplayName(traceInfoToUse as ModelTraceInfo)}\n        </Typography.Title>\n      );\n    }\n    return requestId;\n  }, [\n    // Memo dependency list\n    loadingTraceInfo,\n    loadingInternalTracingInfo,\n    traceInfoToUse,\n    requestId,\n  ]);\n\n  // Construct the model trace object with the trace info and trace data\n  const combinedModelTrace = useMemo(\n    () =>\n      traceData\n        ? {\n            info: traceInfoToUse || {},\n            data: traceData,\n          }\n        : undefined,\n    [traceData, traceInfoToUse],\n  );\n\n  const containsSpans = (traceData?.spans || []).length > 0;\n\n  const renderContent = () => {\n    if (loadingTraceData || loadingTraceInfo || loadingInternalTracingInfo) {\n      return (\n        <>\n          <TitleSkeleton />\n          <TableSkeleton lines={5} />\n        </>\n      );\n    }\n    if (traceInfo?.status === 'IN_PROGRESS') {\n      return (\n        <>\n          <Spacer size=\"lg\" />\n          <Empty\n            image={<WarningIcon />}\n            description={\n              <FormattedMessage\n                defaultMessage=\"Trace data is not available for in-progress traces. Please wait for the trace to complete.\"\n                description=\"Experiment page > traces data drawer > in-progress description\"\n              />\n            }\n            title={\n              <FormattedMessage\n                defaultMessage=\"Trace data not available\"\n                description=\"Experiment page > traces data drawer > in-progress title\"\n              />\n            }\n          />\n        </>\n      );\n    }\n    if (error) {\n      return (\n        <>\n          <Spacer size=\"lg\" />\n          <Empty\n            image={<DangerIcon />}\n            description={\n              <FormattedMessage\n                defaultMessage=\"An error occurred while attempting to fetch the trace data. Please wait a moment and try again.\"\n                description=\"Experiment page > traces data drawer > error state description\"\n              />\n            }\n            title={\n              <FormattedMessage\n                defaultMessage=\"Error\"\n                description=\"Experiment page > traces data drawer > error state title\"\n              />\n            }\n          />\n        </>\n      );\n    }\n    if (!containsSpans) {\n      return (\n        <>\n          <Spacer size=\"lg\" />\n          <Empty\n            description={null}\n            title={\n              <FormattedMessage\n                defaultMessage=\"No trace data recorded\"\n                description=\"Experiment page > traces data drawer > no trace data recorded empty state\"\n              />\n            }\n          />\n        </>\n      );\n    }\n    if (combinedModelTrace) {\n      // TODO: pass onSelectSpan or stop using iframe for OSS tracing page to enable spanId query params in the OSS tracing UI\n      return (\n        <div\n          css={{\n            height: `calc(100% - ${theme.spacing.sm}px)`,\n            marginLeft: -theme.spacing.lg,\n            marginRight: -theme.spacing.lg,\n            marginBottom: -theme.spacing.lg,\n          }}\n          onWheel={(e) => e.stopPropagation()}\n        >\n          <ModelTraceExplorerFrameRenderer modelTrace={combinedModelTrace as ModelTrace} height=\"100%\" />\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <Drawer.Root\n      modal\n      open\n      onOpenChange={(open) => {\n        if (!open) {\n          onClose();\n        }\n      }}\n    >\n      <Drawer.Content\n        componentId=\"codegen_mlflow_app_src_experiment-tracking_components_traces_tracedatadrawer.tsx_222\"\n        width=\"90vw\"\n        title={title}\n        expandContentToFullHeight\n      >\n        {renderContent()}\n      </Drawer.Content>\n    </Drawer.Root>\n  );\n};\n", "import { useCallback, useEffect, useState } from 'react';\nimport { MlflowService } from '../../../sdk/MlflowService';\nimport { ModelTraceStatus, type ModelTraceData } from '@databricks/web-shared/model-trace-explorer';\nimport Utils from '../../../../common/utils/Utils';\n\nexport const useExperimentTraceData = (traceId?: string, skip = false) => {\n  const [traceData, setTraceData] = useState<ModelTraceData | undefined>(undefined);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<Error | undefined>(undefined);\n\n  const fetchTraceData = useCallback(async (traceId: string) => {\n    setLoading(true);\n    try {\n      const response = await MlflowService.getExperimentTraceData(traceId);\n\n      if (Array.isArray(response.spans)) {\n        setTraceData(response);\n      } else {\n        // Not a showstopper, but we should log this error and notify the user.\n        Utils.logErrorAndNotifyUser('Invalid trace data response: ' + JSON.stringify(response?.toString()));\n      }\n    } catch (e: any) {\n      setError(e);\n    }\n    setLoading(false);\n  }, []);\n\n  useEffect(() => {\n    if (traceId && !skip) {\n      fetchTraceData(traceId);\n    }\n  }, [fetchTraceData, traceId, skip]);\n\n  return { traceData, loading, error };\n};\n", "import { type ModelTraceInfo } from '@databricks/web-shared/model-trace-explorer';\nimport { useEditKeyValueTagsModal } from '../../../../common/hooks/useEditKeyValueTagsModal';\nimport { MlflowService } from '../../../sdk/MlflowService';\nimport { KeyValueEntity } from '../../../types';\nimport { useCallback } from 'react';\nimport { MLFLOW_INTERNAL_PREFIX } from '../../../../common/utils/TagUtils';\n\ntype EditedModelTrace = {\n  traceRequestId: string;\n  tags: KeyValueEntity[];\n};\n\nexport const useEditExperimentTraceTags = ({\n  onSuccess,\n  existingTagKeys = [],\n  useV3Apis,\n}: {\n  onSuccess?: () => void;\n  existingTagKeys?: string[];\n  useV3Apis?: boolean;\n}) => {\n  const { showEditTagsModal, EditTagsModal } = useEditKeyValueTagsModal<EditedModelTrace>({\n    saveTagsHandler: async (editedEntity, existingTags, newTags) => {\n      if (!editedEntity.traceRequestId) {\n        return;\n      }\n      const requestId = editedEntity.traceRequestId;\n      // First, determine new tags to be added\n      const addedOrModifiedTags = newTags.filter(\n        ({ key: newTagKey, value: newTagValue }) =>\n          !existingTags.some(\n            ({ key: existingTagKey, value: existingTagValue }) =>\n              existingTagKey === newTagKey && newTagValue === existingTagValue,\n          ),\n      );\n\n      // Next, determine those to be deleted\n      const deletedTags = existingTags.filter(\n        ({ key: existingTagKey }) => !newTags.some(({ key: newTagKey }) => existingTagKey === newTagKey),\n      );\n\n      // Fire all requests at once\n      const updateRequests = Promise.all([\n        ...addedOrModifiedTags.map(({ key, value }) =>\n          useV3Apis\n            ? MlflowService.setExperimentTraceTagV3(requestId, key, value)\n            : MlflowService.setExperimentTraceTag(requestId, key, value),\n        ),\n        ...deletedTags.map(({ key }) =>\n          useV3Apis\n            ? MlflowService.deleteExperimentTraceTagV3(requestId, key)\n            : MlflowService.deleteExperimentTraceTag(requestId, key),\n        ),\n      ]);\n\n      return updateRequests;\n    },\n    valueRequired: true,\n    allAvailableTags: existingTagKeys.filter((tagKey) => tagKey && !tagKey.startsWith(MLFLOW_INTERNAL_PREFIX)),\n    onSuccess: onSuccess,\n  });\n\n  const showEditTagsModalForTrace = useCallback(\n    (trace: ModelTraceInfo) => {\n      if (!trace.request_id) {\n        return;\n      }\n      const visibleTags = trace.tags?.filter(({ key }) => key && !key.startsWith(MLFLOW_INTERNAL_PREFIX)) || [];\n      showEditTagsModal({\n        traceRequestId: trace.request_id,\n        tags: visibleTags || [],\n      });\n    },\n    [showEditTagsModal],\n  );\n\n  return {\n    showEditTagsModalForTrace,\n    EditTagsModal,\n  };\n};\n", "import Utils from '@mlflow/mlflow/src/common/utils/Utils';\nimport { keys, pickBy } from 'lodash';\nimport React, { useState } from 'react';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { MlflowService } from '../../sdk/MlflowService';\nimport { Modal, Typography } from '@databricks/design-system';\n\nexport const TracesViewDeleteTraceModal = ({\n  experimentIds,\n  visible,\n  rowSelection,\n  setRowSelection,\n  handleClose,\n  refreshTraces,\n}: {\n  experimentIds: string[];\n  visible: boolean;\n  rowSelection: { [id: string]: boolean };\n  setRowSelection: (rowSelection: { [id: string]: boolean }) => void;\n  handleClose: () => void;\n  refreshTraces: () => void;\n}) => {\n  const intl = useIntl();\n  const [errorMessage, setErrorMessage] = useState<string>('');\n  const [isLoading, setIsLoading] = useState(false);\n  const tracesToDelete = keys(pickBy(rowSelection, (value) => value));\n\n  const submitDeleteTraces = async () => {\n    try {\n      // TODO: Add support for deleting traces from multiple experiments\n      // The trace data contains the experiment ID, so we simply need to\n      // pass the trace data instead of just the trace IDs.\n      await MlflowService.deleteTraces(experimentIds[0] ?? '', tracesToDelete);\n\n      // reset row selection and refresh traces\n      setRowSelection({});\n      refreshTraces();\n      handleClose();\n    } catch (e: any) {\n      setErrorMessage(\n        intl.formatMessage({\n          defaultMessage: 'An error occured while attempting to delete traces. Please refresh the page and try again.',\n          description: 'Experiment page > traces view controls > Delete traces modal > Error message',\n        }),\n      );\n    }\n    setIsLoading(false);\n  };\n\n  const handleOk = () => {\n    submitDeleteTraces();\n    setIsLoading(true);\n  };\n\n  return (\n    <Modal\n      componentId=\"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewdeletetracemodal.tsx_62\"\n      title={\n        <FormattedMessage\n          defaultMessage=\"{count, plural, one {Delete Trace} other {Delete Traces}}\"\n          description=\"Experiment page > traces view controls > Delete traces modal > Title\"\n          values={{ count: tracesToDelete.length }}\n        />\n      }\n      visible={visible}\n      onCancel={handleClose}\n      okText={\n        <FormattedMessage\n          defaultMessage=\"Delete {count, plural, one { # trace } other { # traces }}\"\n          description=\"Experiment page > traces view controls > Delete traces modal > Delete button\"\n          values={{ count: tracesToDelete.length }}\n        />\n      }\n      onOk={handleOk}\n      okButtonProps={{ loading: isLoading, danger: true }}\n    >\n      {errorMessage && <Typography.Paragraph color=\"error\">{errorMessage}</Typography.Paragraph>}\n      <Typography.Paragraph>\n        <Typography.Text bold>\n          <FormattedMessage\n            defaultMessage=\"{count, plural, one { # trace } other { # traces }} will be deleted.\"\n            description=\"Experiment page > traces view controls > Delete traces modal > Confirmation message title\"\n            values={{\n              count: tracesToDelete.length,\n            }}\n          />\n        </Typography.Text>\n      </Typography.Paragraph>\n      <Typography.Paragraph>\n        <FormattedMessage\n          defaultMessage=\"Deleted traces cannot be restored. Are you sure you want to proceed?\"\n          description=\"Experiment page > traces view controls > Delete traces modal > Confirmation message\"\n        />\n      </Typography.Paragraph>\n    </Modal>\n  );\n};\n", "import { Button, useDesignSystemTheme } from '@databricks/design-system';\nimport React, { useCallback, useState } from 'react';\nimport { FormattedMessage } from 'react-intl';\nimport { TracesViewDeleteTraceModal } from './TracesViewDeleteTraceModal';\n\nexport const TracesViewControlsActions = ({\n  experimentIds,\n  rowSelection,\n  setRowSelection,\n  refreshTraces,\n  baseComponentId,\n}: {\n  experimentIds: string[];\n  rowSelection: { [id: string]: boolean };\n  setRowSelection: (rowSelection: { [id: string]: boolean }) => void;\n  refreshTraces: () => void;\n  baseComponentId: string;\n}) => {\n  const [isModalOpen, setIsModalOpen] = useState(false);\n  const { theme } = useDesignSystemTheme();\n\n  const openModal = useCallback(() => {\n    setIsModalOpen(true);\n  }, [setIsModalOpen]);\n\n  const closeModal = useCallback(() => {\n    setIsModalOpen(false);\n  }, [setIsModalOpen]);\n\n  return (\n    <div\n      css={{\n        display: 'flex',\n        flexDirection: 'row',\n        alignItems: 'center',\n        gap: theme.spacing.sm,\n      }}\n    >\n      <Button componentId={`${baseComponentId}.traces_table.delete_traces`} onClick={openModal} danger>\n        <FormattedMessage\n          defaultMessage=\"Delete\"\n          description=\"Experiment page > traces view controls > Delete button\"\n        />\n      </Button>\n      <TracesViewDeleteTraceModal\n        experimentIds={experimentIds}\n        visible={isModalOpen}\n        rowSelection={rowSelection}\n        handleClose={closeModal}\n        refreshTraces={refreshTraces}\n        setRowSelection={setRowSelection}\n      />\n    </div>\n  );\n};\n", "import {\n  Button,\n  InfoIcon,\n  Input,\n  Popover,\n  SearchIcon,\n  TableFilterLayout,\n  Typography,\n  useDesignSystemTheme,\n} from '@databricks/design-system';\nimport { useState } from 'react';\nimport { FormattedMessage, useIntl } from 'react-intl';\nimport { TracesViewControlsActions } from './TracesViewControlsActions';\nimport { ModelTraceInfoWithRunName } from './hooks/useExperimentTraces';\n\nconst InputTooltip = ({ baseComponentId }: { baseComponentId: string }) => {\n  const { theme } = useDesignSystemTheme();\n\n  return (\n    <Popover.Root\n      componentId=\"codegen_mlflow_app_src_experiment-tracking_components_traces_tracesviewcontrols.tsx_28\"\n      modal={false}\n    >\n      <Popover.Trigger asChild>\n        <Button\n          size=\"small\"\n          type=\"link\"\n          icon={\n            <InfoIcon\n              css={{\n                svg: { width: 16, height: 16, color: theme.colors.textSecondary },\n              }}\n            />\n          }\n          componentId={`${baseComponentId}.traces_table.filter_tooltip`}\n        />\n      </Popover.Trigger>\n      <Popover.Content>\n        <Popover.Arrow />\n        <Typography.Paragraph>\n          <FormattedMessage\n            defaultMessage=\"Search traces using a simplified version of the SQL {whereBold} clause.\"\n            description=\"Tooltip string to explain how to search runs from the experiments table\"\n            values={{ whereBold: <b>WHERE</b> }}\n          />\n        </Typography.Paragraph>\n        <FormattedMessage defaultMessage=\"Examples:\" description=\"Text header for examples of mlflow search syntax\" />\n        <ul>\n          <li>\n            <code>tags.some_tag = \"abc\"</code>\n          </li>\n        </ul>\n      </Popover.Content>\n    </Popover.Root>\n  );\n};\n\nexport const TracesViewControls = ({\n  experimentIds,\n  filter,\n  onChangeFilter,\n  rowSelection,\n  setRowSelection,\n  refreshTraces,\n  baseComponentId,\n  runUuid,\n  traces,\n}: {\n  experimentIds: string[];\n  filter: string;\n  onChangeFilter: (newFilter: string) => void;\n  rowSelection: { [id: string]: boolean };\n  setRowSelection: (newSelection: { [id: string]: boolean }) => void;\n  refreshTraces: () => void;\n  baseComponentId: string;\n  runUuid?: string;\n  traces: ModelTraceInfoWithRunName[];\n}) => {\n  const intl = useIntl();\n  const { theme } = useDesignSystemTheme();\n\n  // Internal filter value state, used to control the input value\n  const [filterValue, setFilterValue] = useState<string | undefined>(filter || undefined);\n  const [isEvaluateTracesModalOpen, setEvaluateTracesModalOpen] = useState(false);\n\n  const displayedFilterValue = filterValue ?? filter;\n\n  const selectedRequestIds = Object.entries(rowSelection)\n    .filter(([, isSelected]) => isSelected)\n    .map(([id]) => id);\n  const showActionButtons = selectedRequestIds.length > 0;\n\n  const searchOrDeleteControls = showActionButtons ? (\n    <TracesViewControlsActions\n      experimentIds={experimentIds}\n      rowSelection={rowSelection}\n      setRowSelection={setRowSelection}\n      refreshTraces={refreshTraces}\n      baseComponentId={baseComponentId}\n    />\n  ) : (\n    <TableFilterLayout css={{ marginBottom: 0 }}>\n      <Input\n        componentId={`${baseComponentId}.traces_table.search_filter`}\n        placeholder={intl.formatMessage({\n          defaultMessage: 'Search traces',\n          description: 'Experiment page > traces view filters > filter string input placeholder',\n        })}\n        value={displayedFilterValue}\n        // Matches runs filter input width\n        css={{ width: 430 }}\n        onChange={(e) => setFilterValue(e.target.value)}\n        prefix={<SearchIcon />}\n        suffix={<InputTooltip baseComponentId={baseComponentId} />}\n        allowClear\n        onClear={() => {\n          onChangeFilter('');\n          setFilterValue(undefined);\n        }}\n        onKeyDown={(e) => {\n          if (e.key === 'Enter') {\n            onChangeFilter(displayedFilterValue);\n            setFilterValue(undefined);\n          }\n        }}\n      />\n    </TableFilterLayout>\n  );\n\n  return (\n    <div css={{ display: 'flex', gap: theme.spacing.xs }}>\n      {/* Search and delete controls */}\n      {searchOrDeleteControls}\n    </div>\n  );\n};\n", "import { useCallback, useEffect, useMemo, useState } from 'react';\nimport LocalStorageUtils from '../../../../common/utils/LocalStorageUtils';\nimport { isObject, sortBy } from 'lodash';\nimport { ExperimentViewTracesTableColumns } from '../TracesView.utils';\n\ntype LocalStorageStore = ReturnType<typeof LocalStorageUtils.getStoreForComponent>;\n\nexport interface ExperimentViewTracesUIState {\n  hiddenColumns?: string[];\n}\n\nconst defaultExperimentViewTracesUIState: ExperimentViewTracesUIState = {\n  hiddenColumns: [ExperimentViewTracesTableColumns.traceName, ExperimentViewTracesTableColumns.source],\n};\n\nconst loadExperimentViewTracesUIState = (localStore: LocalStorageStore): ExperimentViewTracesUIState => {\n  try {\n    const uiStateRaw = localStore.getItem('uiState');\n    const uiState = JSON.parse(uiStateRaw);\n    if (!isObject(uiState)) {\n      return defaultExperimentViewTracesUIState;\n    }\n    return uiState;\n  } catch (e) {\n    return defaultExperimentViewTracesUIState;\n  }\n};\n\nexport const useExperimentViewTracesUIState = (experimentIds: string[]) => {\n  const localStore = useMemo(() => {\n    const persistenceIdentifier = JSON.stringify(experimentIds.slice().sort());\n    return LocalStorageUtils.getStoreForComponent('ExperimentViewTraces', persistenceIdentifier);\n  }, [experimentIds]);\n\n  const [uiState, setUIState] = useState<ExperimentViewTracesUIState>(() =>\n    loadExperimentViewTracesUIState(localStore),\n  );\n\n  const toggleHiddenColumn = useCallback((columnId: string) => {\n    setUIState((prevUIState) => {\n      const hiddenColumns = prevUIState.hiddenColumns || [];\n      return {\n        hiddenColumns: hiddenColumns.includes(columnId)\n          ? hiddenColumns.filter((id) => id !== columnId)\n          : [...hiddenColumns, columnId],\n      };\n    });\n  }, []);\n\n  useEffect(() => {\n    localStore.setItem('uiState', JSON.stringify(uiState));\n  }, [localStore, uiState]);\n\n  return { uiState, toggleHiddenColumn };\n};\n", "import { useCallback } from 'react';\nimport { useSearchParams } from '../../../../common/utils/RoutingUtils';\n\nconst QUERY_PARAM_KEY = 'selectedTraceId';\n\n/**\n * Query param-powered hook that returns the currently selected trace ID and a function to set the selected trace ID.\n * To be used in traces page components.\n */\nexport const useActiveExperimentTrace = () => {\n  // TODO(ML-40722): Create separate UI route for traces page and use route params instead of search params\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  const selectedTraceId = searchParams.get(QUERY_PARAM_KEY) ?? undefined;\n\n  const setSelectedTraceId = useCallback(\n    (selectedTraceId: string | undefined) => {\n      setSearchParams((params) => {\n        if (selectedTraceId === undefined) {\n          params.delete(QUERY_PARAM_KEY);\n          return params;\n        }\n        params.set(QUERY_PARAM_KEY, selectedTraceId);\n        return params;\n      });\n    },\n    [setSearchParams],\n  );\n\n  return [selectedTraceId, setSelectedTraceId] as const;\n};\n", "import { useCallback } from 'react';\nimport { useSearchParams } from '../../../../common/utils/RoutingUtils';\n\nconst QUERY_PARAM_KEY = 'selectedSpanId';\n\n/**\n * Query param-powered hook that returns the currently selected span ID and a function to set the selected span ID.\n * To be used in traces page components.\n */\nexport const useActiveExperimentSpan = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  const selectedSpanId = searchParams.get(QUERY_PARAM_KEY) ?? undefined;\n\n  const setSelectedSpanId = useCallback(\n    (selectedSpanId: string | undefined) => {\n      setSearchParams(\n        (params) => {\n          if (selectedSpanId === undefined) {\n            params.delete(QUERY_PARAM_KEY);\n            return params;\n          }\n          params.set(QUERY_PARAM_KEY, selectedSpanId);\n          return params;\n        },\n        { replace: true },\n      );\n    },\n    [setSearchParams],\n  );\n\n  return [selectedSpanId, setSelectedSpanId] as const;\n};\n", "import { useDesignSystemTheme } from '@databricks/design-system';\n\nimport { useExperimentTraces } from './hooks/useExperimentTraces';\nimport { TracesViewTable } from './TracesViewTable';\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { TraceDataDrawer } from './TraceDataDrawer';\nimport { useEditExperimentTraceTags } from './hooks/useEditExperimentTraceTags';\nimport { TracesViewControls } from './TracesViewControls';\nimport { SortingState } from '@tanstack/react-table';\nimport { compact, isFunction, isNil, uniq } from 'lodash';\nimport { useExperimentViewTracesUIState } from './hooks/useExperimentViewTracesUIState';\nimport { ExperimentViewTracesTableColumns, getTraceInfoTotalTokens } from './TracesView.utils';\nimport { useActiveExperimentTrace } from './hooks/useActiveExperimentTrace';\nimport { useActiveExperimentSpan } from './hooks/useActiveExperimentSpan';\nimport { ModelTraceInfo } from '@databricks/web-shared/model-trace-explorer';\n\nexport const TRACE_AUTO_REFRESH_INTERVAL = 30000;\n\nconst defaultSorting: SortingState = [{ id: ExperimentViewTracesTableColumns.timestampMs, desc: true }];\n\nexport const TracesView = ({\n  experimentIds,\n  runUuid,\n  loggedModelId,\n  disabledColumns,\n  baseComponentId = runUuid ? 'mlflow.run.traces' : 'mlflow.experiment_page.traces',\n}: {\n  experimentIds: string[];\n  /**\n   * If `runUuid` is provided, the traces will be filtered to only show traces from that run.\n   */\n  runUuid?: string;\n  /**\n   * If `loggedModelId` is provided, the traces will be filtered to only show traces from that logged model.\n   */\n  loggedModelId?: string;\n  /**\n   * Columns that should be disabled in the table.\n   * Disabled columns are hidden and are not available to be toggled at all.\n   */\n  disabledColumns?: ExperimentViewTracesTableColumns[];\n  /**\n   * The base component ID for the traces view. If not provided, will be inferred from the other props.\n   */\n  baseComponentId?: string;\n}) => {\n  const timeoutRef = useRef<number | undefined>(undefined);\n  const [filter, setFilter] = useState<string>('');\n  const [sorting, setSorting] = useState<SortingState>(defaultSorting);\n  const [rowSelection, setRowSelection] = useState<{ [id: string]: boolean }>({});\n\n  const [selectedTraceId, setSelectedTraceId] = useActiveExperimentTrace();\n  const [selectedSpanId, setSelectedSpanId] = useActiveExperimentSpan();\n\n  const { traces, loading, error, hasNextPage, hasPreviousPage, fetchNextPage, fetchPrevPage, refreshCurrentPage } =\n    useExperimentTraces({\n      experimentIds,\n      sorting,\n      filter,\n      runUuid,\n      loggedModelId,\n    });\n\n  const onTraceClicked = useCallback(\n    ({ request_id }: ModelTraceInfo) => setSelectedTraceId(request_id),\n    [setSelectedTraceId],\n  );\n\n  // clear row selections when the page changes.\n  // the backend specifies a max of 100 deletions,\n  // plus it's confusing to have selections on a\n  // page that the user can't see\n  const onNextPage = useCallback(() => {\n    fetchNextPage();\n    setRowSelection({});\n  }, [fetchNextPage]);\n\n  const onPreviousPage = useCallback(() => {\n    fetchPrevPage();\n    setRowSelection({});\n  }, [fetchPrevPage]);\n\n  // auto-refresh traces\n  useEffect(() => {\n    // if the hook reruns, clear the current timeout, since we'll be scheduling another\n    window.clearTimeout(timeoutRef.current);\n\n    const scheduleRefresh = async () => {\n      // only refresh if the user is on the first page\n      // otherwise it might mess with browsing old traces\n      if (loading || hasPreviousPage) return;\n\n      await refreshCurrentPage(true);\n\n      window.clearTimeout(timeoutRef.current);\n      timeoutRef.current = window.setTimeout(scheduleRefresh, TRACE_AUTO_REFRESH_INTERVAL);\n    };\n\n    timeoutRef.current = window.setTimeout(scheduleRefresh, TRACE_AUTO_REFRESH_INTERVAL);\n    return () => window.clearTimeout(timeoutRef.current);\n  }, [refreshCurrentPage, loading, hasPreviousPage]);\n\n  const { theme } = useDesignSystemTheme();\n\n  // Try to find the trace info for the currently selected trace id\n  const selectedTraceInfo = useMemo(() => {\n    if (!selectedTraceId) return undefined;\n    return traces.find((trace) => trace.request_id === selectedTraceId);\n  }, [selectedTraceId, traces]);\n\n  const {\n    // hiddenColumns is a list of columns that are hidden by the user.\n    uiState,\n    toggleHiddenColumn,\n  } = useExperimentViewTracesUIState(experimentIds);\n\n  const existingTagKeys = useMemo(\n    () => uniq(compact(traces.flatMap((trace) => trace.tags?.map((tag) => tag.key)))),\n    [traces],\n  );\n\n  const { showEditTagsModalForTrace, EditTagsModal } = useEditExperimentTraceTags({\n    onSuccess: () => refreshCurrentPage(true),\n    existingTagKeys,\n  });\n\n  const usingFilters = filter !== '';\n\n  const anyTraceContainsTokenCount = traces.some((trace) => !isNil(getTraceInfoTotalTokens(trace)));\n\n  // Automatically disabled columns: hide the token count column if there's no trace that contains token count information.\n  const autoDisabledColumns = useMemo(\n    () => (!anyTraceContainsTokenCount ? [ExperimentViewTracesTableColumns.totalTokens] : []),\n    [anyTraceContainsTokenCount],\n  );\n\n  // Combine columns that are disabled by parent component and columns that are disabled automatically.\n  const allDisabledColumns = useMemo(\n    () => [...(disabledColumns ?? []), ...autoDisabledColumns],\n    [disabledColumns, autoDisabledColumns],\n  );\n\n  const allHiddenColumns = useMemo(\n    () => [...(uiState.hiddenColumns ?? []), ...allDisabledColumns],\n    [uiState, allDisabledColumns],\n  );\n\n  return (\n    <div\n      css={{\n        display: 'flex',\n        flexDirection: 'column',\n        gap: theme.spacing.sm,\n        height: '100%',\n        overflow: 'hidden',\n      }}\n    >\n      <TracesViewControls\n        experimentIds={experimentIds}\n        filter={filter}\n        onChangeFilter={setFilter}\n        rowSelection={rowSelection}\n        setRowSelection={setRowSelection}\n        refreshTraces={refreshCurrentPage}\n        baseComponentId={baseComponentId}\n        runUuid={runUuid}\n        traces={traces}\n      />\n      <TracesViewTable\n        experimentIds={experimentIds}\n        runUuid={runUuid}\n        traces={traces}\n        loading={loading}\n        error={error}\n        onTraceClicked={onTraceClicked}\n        onTraceTagsEdit={showEditTagsModalForTrace}\n        hasNextPage={hasNextPage}\n        hasPreviousPage={hasPreviousPage}\n        onPreviousPage={onPreviousPage}\n        onNextPage={onNextPage}\n        onTagsUpdated={refreshCurrentPage}\n        usingFilters={usingFilters}\n        onResetFilters={() => setFilter('')}\n        hiddenColumns={allHiddenColumns}\n        disableTokenColumn={!anyTraceContainsTokenCount}\n        disabledColumns={allDisabledColumns}\n        setSorting={(sortingSetter) => {\n          // If header is clicked enough times, tanstack table switches to \"no sort\" mode.\n          // In that case, we should just reverse the direction of the current sort instead.\n          if (isFunction(sortingSetter)) {\n            return setSorting((currentState) => {\n              const newState = sortingSetter(currentState);\n              const currentSortBy = currentState[0];\n              if ((!newState || newState.length === 0) && currentSortBy) {\n                return [{ id: currentSortBy.id, desc: !currentSortBy.desc }];\n              }\n              return newState;\n            });\n          }\n        }}\n        sorting={sorting}\n        rowSelection={rowSelection}\n        setRowSelection={setRowSelection}\n        baseComponentId={baseComponentId}\n        toggleHiddenColumn={toggleHiddenColumn}\n      />\n      {selectedTraceId && (\n        <TraceDataDrawer\n          traceInfo={selectedTraceInfo}\n          loadingTraceInfo={loading}\n          requestId={selectedTraceId}\n          onClose={() => setSelectedTraceId(undefined)}\n          selectedSpanId={selectedSpanId}\n          onSelectSpan={setSelectedSpanId}\n        />\n      )}\n      {EditTagsModal}\n    </div>\n  );\n};\n"], "names": ["ExperimentSourceTypeIcon", "_ref", "sourceType", "className", "SourceType", "NOTEBOOK", "_jsx", "NotebookIcon", "LOCAL", "FileCodeIcon", "PROJECT", "FolderBranchIcon", "JOB", "WorkflowsIcon", "serializeRequestBody", "payload", "undefined", "FormData", "Blob", "JSON", "stringify", "loggedModelsDataRequest", "async", "url", "method", "arguments", "length", "body", "response", "fetch", "headers", "ok", "predefinedError", "matchPredefinedError", "message", "json", "ToggleIconButton", "React", "props", "ref", "pressed", "onClick", "icon", "onBlur", "onFocus", "onMouseEnter", "onMouseLeave", "componentId", "analyticsEvents", "type", "remainingProps", "theme", "useDesignSystemTheme", "eventContext", "useDesignSystemEventComponentCallbacks", "componentType", "DesignSystemEventProviderComponentTypes", "<PERSON><PERSON>", "DesignSystemEventProviderAnalyticsEventTypes", "OnClick", "event", "css", "_css", "cursor", "width", "general", "heightSm", "height", "borderRadius", "legacyBorders", "borderRadiusMd", "lineHeight", "typography", "lineHeightBase", "padding", "border", "display", "alignItems", "justifyContent", "background", "colors", "actionDefaultBackgroundPress", "color", "actionDefaultTextPress", "textSecondary", "actionDefaultBackgroundHover", "actionDefaultTextHover", "children", "_ref2", "name", "styles", "Source<PERSON>ell<PERSON><PERSON><PERSON>", "_tags$Utils$sourceTyp", "value", "tags", "_Fragment", "Utils", "sourceTypeTag", "sourceLink", "renderSource", "_jsxs", "gap", "spacing", "xs", "datasetSummariesEqual", "summary1", "summary2", "digest", "context", "getDatasetSourceUrl", "datasetWithTags", "dataset", "DatasetSourceTypes", "HTTP", "parse", "source", "S3", "uri", "HUGGING_FACE", "path", "_path", "_g", "_path2", "_path3", "_defs", "_extends", "Object", "assign", "bind", "n", "e", "t", "r", "hasOwnProperty", "call", "apply", "SvgRegisteredModelGreyOk", "svgRef", "title", "titleId", "viewBox", "fill", "xmlns", "id", "clipPath", "style", "maskType", "maskUnits", "x", "y", "d", "mask", "fillRule", "clipRule", "ForwardRef", "LocalStorageUtils", "getStoreForComponent", "componentName", "LocalStorageStore", "join", "getSessionScopedStoreForComponent", "version", "constructor", "scope", "storageObj", "this", "window", "localStorage", "sessionStorage", "loadComponentState", "storedVal", "getItem", "reactComponentStateKey", "saveComponentState", "stateRecord", "targetValue", "toJSON", "setItem", "withScopePrefix", "key", "_ref3", "ExperimentViewDatasetWithContext", "_tags$find", "displayTextAsLink", "contextTag", "find", "MLFLOW_RUN_DATASET_CONTEXT_TAG", "flexDirection", "marginTop", "marginBottom", "TableIcon", "marginRight", "Typography", "Text", "size", "Tag", "textTransform", "marginLeft", "JsonPreview", "wrapperStyle", "overlayStyle", "codeSnippetStyle", "formattedJson", "is<PERSON>son<PERSON><PERSON>nt", "useFormattedJson", "position", "maxHeight", "overflow", "CodeSnippet", "language", "overflowX", "bottom", "right", "left", "parsed", "isJson", "isObject", "Date", "FormattedJsonDisplay", "wrapLongLines", "icons", "rotateLeft", "UndoIcon", "rotateRight", "RedoIcon", "zoomIn", "ZoomInIcon", "zoomOut", "ZoomOutIcon", "close", "CloseIcon", "ArrowLeftIcon", "ArrowRightIcon", "ImagePreviewGroup", "visible", "onVisibleChange", "getPopupContainer", "useContext", "DesignSystemContext", "RcImage", "PreviewGroup", "preview", "getContainer", "v", "TracesViewTableNoTracesQuickstartContext", "createContext", "TracesViewTableNoTracesQuickstartContextProvider", "introductionText", "displayVersionWarnings", "Provider", "useTracesViewTableNoTracesQuickstartContext", "MIN_GRID_IMAGE_SIZE", "ImagePlot", "imageUrl", "compressedImageUrl", "imageSize", "maxImageSize", "previewVisible", "setPreviewVisible", "useState", "imageLoading", "setImageLoading", "useEffect", "img", "Image", "onload", "onerror", "src", "backgroundColor", "backgroundSecondary", "aspectRatio", "Spinner", "max<PERSON><PERSON><PERSON>", "ImagePlotWithHistory", "metadataByStep", "step", "runUuid", "textAlign", "md", "ImageIcon", "FormattedMessage", "defaultMessage", "getArtifactLocationUrl", "filepath", "compressed_filepath", "_ref4", "_ref5", "_ref6", "EmptyImageGridPlot", "Title", "level", "HIGH_RESOLUTION_MEDIA_QUERY", "RunColorPill", "hidden", "onChangeColor", "colorValue", "setColorValue", "onChangeColorDebounced", "useMemo", "debounce", "flexShrink", "opacity", "visuallyHidden", "disabled", "onChange", "target", "list", "COLORS_PALETTE_DATALIST_ID", "TRACE_TAG_NAME_TRACE_NAME", "getTraceMetadataField", "traceInfo", "field", "_traceInfo$request_me", "_traceInfo$request_me2", "request_metadata", "isTraceMetadataPossiblyTruncated", "traceMetadata", "getTraceInfoRunId", "getTraceInfoTotalTokens", "getTraceInfoInputs", "inputs", "isNil", "getTraceInfoOutputs", "outputs", "getTraceTagValue", "tagName", "_traceInfo$tags2", "_traceInfo$tags", "_traceInfo$tags$find", "Array", "isArray", "getTraceDisplayName", "request_id", "EXPERIMENT_TRACES_SORTABLE_COLUMNS", "TRACE_TABLE_CHECKBOX_COLUMN_ID", "ExperimentViewTracesTableColumns", "ExperimentViewTracesTableColumnLabels", "requestId", "defineMessage", "traceName", "timestampMs", "status", "runName", "totalTokens", "latency", "ExperimentViewTracesStatusLabels", "UNSET", "IN_PROGRESS", "OK", "ERROR", "coerceToEnum", "enumObj", "fallback", "sizeMap", "matchedSize", "setMatchedSize", "current", "handleResize", "elementWidth", "offsetWidth", "<PERSON><PERSON><PERSON>", "keys", "filter", "sort", "a", "b", "resizeObserver", "ResizeObserver", "observe", "disconnect", "SIDEBAR_WIDTHS", "sm", "lg", "OverviewLayout", "isLoading", "secondarySections", "isTabLayout", "sidebarSize", "verticalStackOrder", "containerRef", "useRef", "stackVertically", "useResponsiveContainer", "small", "responsive", "breakpoints", "verticalDisplayPrimaryContentOnTop", "totalSidebarWidth", "innerSidebarWidth", "secondaryStackedStyles", "borderBottom", "paddingBottom", "flexGrow", "GenericSkeleton", "SidebarWrapper", "section", "content", "map", "index", "isTitleLoading", "SecondarySection", "SecondarySectionTitle", "whiteSpace", "titleComponent", "ParagraphSkeleton", "label", "compactStyles", "borderTop", "_ref7", "KeyValueProperty", "keyValue", "wordBreak", "lineHeightLg", "flex", "alignSelf", "NoneCell", "ExperimentViewDatasetSchemaTable", "schema", "filteredSchema", "row", "_", "<PERSON><PERSON><PERSON>er", "toLowerCase", "includes", "Table", "scrollable", "TableRow", "<PERSON><PERSON><PERSON><PERSON>", "TableHeader", "onWheel", "stopPropagation", "TableCell", "idx", "_ref8", "_ref9", "_ref0", "ExperimentViewDatasetSchema", "setFilter", "Header", "grey600", "form", "TableFilterInput", "placeholder", "onClear", "containerProps", "mlflow_colspec", "fontSize", "ExperimentViewDatasetLink", "runTags", "NewWindowIcon", "href", "Copy<PERSON><PERSON><PERSON>", "CopyIcon", "copyText", "EXTERNAL", "ExperimentViewDatasetSourceType", "typeLabel", "Hint", "values", "ExperimentViewDatasetSourceURL", "fontSizeSm", "columnGap", "Link", "openInNewTab", "ExperimentViewDatasetDigest", "ExperimentViewDatasetDrawerImpl", "_datasetWithTags$tags", "isOpen", "setIsOpen", "selectedDatasetWithRun", "setSelectedDatasetWithRun", "runData", "tag", "fullProfile", "profile", "getRunColor", "useGetExperimentRunColor", "experimentId", "Drawer", "Root", "open", "onOpenChange", "Content", "to", "Routes", "getRunPageRoute", "runLink", "footer", "Spacer", "borderRight", "paddingLeft", "datasets", "textDecoration", "datasetA", "datasetB", "actionTertiaryBackgroundPress", "paddingTop", "actionTertiaryBackgroundHover", "areDatasetsEqual", "LegacyTooltip", "ellipsis", "substring", "ExperimentViewDatasetDrawer", "textOverflow", "RUN_ID_FILTER_EXPRESSION", "LOGGED_MODEL_ID_FILTER_EXPRESSION", "createRunIdsFilterExpression", "runUuids", "runId", "useExperimentTraces", "experimentIds", "sorting", "loggedModelId", "traces", "setTraces", "loading", "setLoading", "error", "setError", "orderByString", "firstOrderByColumn", "first", "desc", "filterString", "isExperimentLoggedModelsUIEnabled", "pageTokens", "setPageTokens", "currentPage", "setCurrentPage", "currentPageToken", "fetchTraces", "useCallback", "pageToken", "silent", "MlflowService", "getExperimentTraces", "runNamesForTraces", "traceIdToRunIdMap", "reduce", "acc", "trace", "traceId", "uniq", "runIdsToRunNames", "searchRuns", "experiment_ids", "run_view_type", "ViewType", "ALL", "runs", "run", "info", "fetchRunNamesForTraces", "tracesWithRunNames", "prevPages", "next_page_token", "hasNextPage", "hasPreviousPage", "reset", "fetchNextPage", "prevPage", "fetchPrevPage", "refreshCurrentPage", "TracesViewTableTagCell", "onAddEditTags", "baseComponentId", "visibleTagList", "startsWith", "MLFLOW_INTERNAL_PREFIX", "containsTags", "flexWrap", "rowGap", "KeyValueTag", "charLimit", "enableFullViewModal", "PencilIcon", "getIcon", "ClockIcon", "textValidationWarning", "CheckCircleIcon", "textValidationSuccess", "XCircleIcon", "textValidationDanger", "TracesViewTableStatusCell", "original", "intl", "useIntl", "labelDescriptor", "formatMessage", "clampedLinesCss", "TracesViewTablePreviewCell", "previewFieldName", "isExpanded", "setIsExpanded", "fullData", "setFullData", "fetchFullData", "getExperimentTraceData", "previewValue", "requestData", "isString", "errorMessage", "ErrorWrapper", "getUserVisibleError", "logErrorAndNotifyUser", "valuePossiblyTruncated", "expand", "collapse", "ChevronDownIcon", "ChevronRightIcon", "ExpandedParamCell", "structuredJSONValue", "objectData", "fontFamily", "isDarkMode", "TracesViewTableRequestPreviewCell", "TracesViewTableResponsePreviewCell", "TracesViewTableSourceCell", "keyBy", "getHeaderSizeClassName", "getColumnSizeClassName", "TracesViewTableRow", "role", "minHeight", "buttonHeight", "paddingRight", "getAllCells", "cell", "_meta", "_meta2", "multiline", "column", "columnDef", "meta", "flexRender", "getContext", "prev", "next", "columns", "selected", "isEqual", "TracesViewTableTimestampCell", "timestamp_ms", "toLocaleString", "navigator", "timeZoneName", "placement", "timeSinceStr", "TracesViewTableHeaderCheckbox", "table", "isChecked", "getIsAllRowsSelected", "getIsSomeRowsSelected", "Checkbox", "margin", "toggleAllRowsSelected", "TracesViewTableCellCheckbox", "getCanSelect", "getIsSelected", "toggleSelected", "QUICKSTART_CONTENT", "openai", "minVersion", "get<PERSON>ontent", "code", "getCodeSource", "langchain", "llama_index", "dspy", "crewai", "autogen", "anthropic", "bedrock", "litellm", "gemini", "custom", "Paragraph", "text", "TraceTableGenericQuickstart", "flavorName", "alertContent", "installCommand", "<PERSON><PERSON>", "closable", "description", "zIndex", "top", "showLabel", "showLineNumbers", "TracesViewTableNoTracesQuickstart", "titleElementLevel", "isRun", "Tabs", "defaultValue", "List", "<PERSON><PERSON>", "RequestIdCell", "options", "onTraceClicked", "TraceNameCell", "RunNameCell", "experiment_id", "TraceTagsCell", "onTraceTagsEdit", "_ref10", "_ref12", "_ref14", "TracesViewTable", "onNextPage", "onPreviousPage", "usingFilters", "onResetFilters", "setSorting", "rowSelection", "setRowSelection", "hiddenColumns", "disableTokenColumn", "toggleHiddenColumn", "disabledColumns", "useStaticColumnsCells", "isUnstableNestedComponentsMigrated", "allColumnsList", "entries", "header", "enableResizing", "enableSorting", "min<PERSON><PERSON><PERSON>", "_ref1", "_ref11", "accessorFn", "data", "_ref13", "push", "execution_time_ms", "isFinite", "formatDuration", "_ref15", "useReactTable", "state", "getCoreRowModel", "getRowId", "toString", "getSortedRowModel", "onSortingChange", "onRowSelectionChange", "enableColumnResizing", "enableRowSelection", "columnResizeMode", "columnSizeInfo", "getState", "columnSizingInfo", "columnSizeVars", "getFlatHeaders", "colSizes", "for<PERSON>ach", "getSize", "empty", "getEmptyState", "getMessageField", "Empty", "image", "DangerIcon", "button", "chunks", "pagination", "CursorPagination", "getLeafHeaders", "sortable", "getCanSort", "sortDirection", "getIsSorted", "onToggleSort", "getToggleSortingHandler", "setColumnSizing", "isResizing", "getIsResizing", "TableRowAction", "DropdownMenu", "<PERSON><PERSON><PERSON><PERSON>", "ColumnsIcon", "align", "_ref16", "CheckboxItem", "checked", "ItemIndicator", "TableSkeletonRows", "getRowModel", "rows", "ModelTraceExplorer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "modelTrace", "useLatestVersion", "iframeRef", "setIsLoading", "handleMessage", "_iframeRef$current", "iframeWindow", "contentWindow", "ModelTraceChildToParentFrameMessage", "Ready", "addEventListener", "removeEventListener", "_iframeRef$current2", "postMessage", "ModelTraceParentToChildFrameMessage", "UpdateTrace", "traceData", "TitleSkeleton", "TableSkeleton", "lines", "Version", "TraceDataDrawer", "loadingTraceInfo", "onClose", "selectedSpanId", "onSelectSpan", "loadingTraceData", "skip", "setTraceData", "fetchTraceData", "spans", "useExperimentTraceData", "shouldFetchTraceInfo", "internalTraceInfo", "loadingInternalTracingInfo", "enabled", "setTraceInfoData", "fetchTraceInfo", "getExperimentTraceInfo", "trace_info", "useExperimentTraceInfo", "traceInfoToUse", "<PERSON><PERSON><PERSON><PERSON>", "combinedModelTrace", "containsSpans", "modal", "expandContentToFullHeight", "WarningIcon", "TracesViewDeleteTraceModal", "handleClose", "refreshTraces", "setErrorMessage", "tracesToDelete", "pickBy", "Modal", "count", "onCancel", "okText", "onOk", "handleOk", "_experimentIds$", "deleteTraces", "submitDeleteTraces", "okButtonProps", "danger", "bold", "TracesViewControlsActions", "isModalOpen", "setIsModalOpen", "openModal", "closeModal", "InputTooltip", "Popover", "InfoIcon", "svg", "Arrow", "whereBold", "TracesViewControls", "on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterValue", "setFilterValue", "isEvaluateTracesModalOpen", "setEvaluateTracesModalOpen", "displayedFilterValue", "selectedRequestIds", "isSelected", "searchOrDeleteControls", "TableFilterLayout", "Input", "prefix", "SearchIcon", "suffix", "allowClear", "onKeyDown", "defaultExperimentViewTracesUIState", "useExperimentViewTracesUIState", "localStore", "persistenceIdentifier", "slice", "uiState", "setUIState", "uiStateRaw", "loadExperimentViewTracesUIState", "columnId", "prevUIState", "QUERY_PARAM_KEY", "defaultSorting", "TracesView", "timeoutRef", "selectedTraceId", "setSelectedTraceId", "useActiveExperimentTrace", "_searchParams$get", "searchParams", "setSearchParams", "useSearchParams", "get", "params", "delete", "set", "setSelectedSpanId", "useActiveExperimentSpan", "replace", "clearTimeout", "scheduleRefresh", "setTimeout", "selectedTraceInfo", "existingTagKeys", "compact", "flatMap", "_trace$tags", "showEditTagsModalForTrace", "EditTagsModal", "onSuccess", "useV3Apis", "showEditTagsModal", "useEditKeyValueTagsModal", "saveTagsHandler", "editedEntity", "existingTags", "newTags", "traceRequestId", "addedOrModifiedTags", "newTagKey", "newTagValue", "some", "existingTagKey", "existingTagValue", "deletedTags", "updateRequests", "Promise", "all", "setExperimentTraceTagV3", "setExperimentTraceTag", "deleteExperimentTraceTagV3", "deleteExperimentTraceTag", "valueRequired", "allAvailableTags", "<PERSON><PERSON><PERSON>", "visibleTags", "useEditExperimentTraceTags", "anyTraceContainsTokenCount", "autoDisabledColumns", "allDisabledColumns", "allHiddenColumns", "_uiState$hiddenColumn", "onTagsUpdated", "sortingSetter", "isFunction", "currentState", "newState", "currentSortBy"], "sourceRoot": ""}