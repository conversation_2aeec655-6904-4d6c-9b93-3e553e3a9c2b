2025-08-04 18:51:20,581 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-08-04 18:58:24,370 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-08-04 19:01:47,078 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-08-04 19:03:10,054 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-08-04 19:03:58,778 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-08-04 19:09:05,614 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-08-04 19:10:53,210 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-08-04 19:14:24,929 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
2025-08-04 19:14:56,172 [ERROR] django.request: Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\code_dashboard\grade_be\myvenv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\code_dashboard\grade_be\myvenv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\code_dashboard\grade_be\myvenv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\code_dashboard\grade_be\myvenv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\code_dashboard\grade_be\myvenv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Desktop\code_dashboard\grade_be\myvenv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: index.html
2025-08-04 19:14:56,174 [ERROR] django.server: "GET / HTTP/1.1" 500 93221
2025-08-04 19:14:56,780 [WARNING] django.server: "GET /favicon.ico HTTP/1.1" 404 20260
2025-08-04 19:17:57,233 [INFO] django.utils.autoreload: Watching for file changes with StatReloader
