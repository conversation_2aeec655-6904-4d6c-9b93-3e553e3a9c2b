from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from .manager import CustomUserManager
from django.utils import timezone
from datetime import timedelta
import random
import string
from decimal import Decimal
from datetime import date


def generate_otp():
    return "".join(random.choices(string.digits, k=6))


class Organization(models.Model):
    """
    Organization model for managing educational institutions.
    """

    name = models.CharField(max_length=255)
    email = models.EmailField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.BooleanField(default=False)  # False until admin approves

    # Registration details
    address = models.TextField(default="address")
    phone_number = models.CharField(max_length=20, null=True)
    registration_date = models.DateField(default=date.today)
    registration_proof = models.FileField(
        null=True, upload_to="organization_docs/"
    )
    description = models.TextField(null=True)

    # Admin verification
    is_verified = models.BooleanField(default=False)
    verification_notes = models.TextField(null=True, blank=True)
    verified_by = models.ForeignKey(
        "User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="verified_organizations",
    )
    verified_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return self.name

    def verify_organization(self, admin_user, notes=None):
        """Verify the organization by an admin"""
        self.is_verified = True
        self.status = True
        self.verified_by = admin_user
        self.verified_at = timezone.now()
        if notes:
            self.verification_notes = notes
        self.save()

    def reject_organization(self, admin_user, notes):
        """Reject the organization registration"""
        self.is_verified = False
        self.status = False
        self.verified_by = admin_user
        self.verified_at = timezone.now()
        self.verification_notes = notes
        self.save()


class User(AbstractUser):
    """
    Custom User model with fields needed for authentication and profile.
    """

    ROLES = [
        ("student", "Student"),
        ("evaluator", "Evaluator"),
        ("qp_uploader", "QP Uploader"),
        ("mentor", "Mentor"),
    ]

    ORG_ROLES = [
        ("admin", "Admin"),
        ("student", "Student"),
    ]

    roles = models.JSONField(default=list)  # Stores all roles user has
    active_role = models.CharField(
        max_length=20, choices=ROLES, null=True, blank=True
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="users",
    )
    role_org = models.CharField(
        max_length=20, choices=ORG_ROLES, null=True, blank=True
    )
    email = models.EmailField(_("email address"), unique=True)
    username = models.CharField(max_length=150)
    is_email_verified = models.BooleanField(default=False)
    otp = models.CharField(max_length=6, null=True, blank=True)
    otp_created_at = models.DateTimeField(null=True, blank=True)
    password_reset_token = models.CharField(
        max_length=100, null=True, blank=True
    )
    password_reset_expires = models.DateTimeField(null=True, blank=True)

    # Security fields
    failed_login_attempts = models.IntegerField(default=0)
    account_locked_until = models.DateTimeField(null=True, blank=True)
    google_id = models.CharField(
        max_length=255, unique=True, null=True, blank=True
    )

    is_allowed = models.BooleanField(default=False)
    is_premium = models.BooleanField(default=False)
    is_profile_completed = models.BooleanField(default=False)
    is_admin = models.BooleanField(default=False)  # New field for admin status
    full_name = models.CharField(max_length=255, null=True, blank=True)
    country = models.CharField(max_length=100, null=True, blank=True)
    state = models.CharField(max_length=100, null=True, blank=True)
    city = models.CharField(max_length=100, null=True, blank=True)
    address_line1 = models.TextField(null=True, blank=True)
    address_line2 = models.TextField(null=True, blank=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["username"]

    objects = CustomUserManager()

    def __str__(self):
        return self.email

    def lock_account(self):
        self.account_locked_until = timezone.now() + timedelta(minutes=30)
        self.save()

    def unlock_account(self):
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.save()

    def increment_failed_attempts(self):
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 5:  # Lock after 5 attempts
            self.lock_account()
        self.save()

    def generate_otp(self):
        self.otp = generate_otp()
        self.otp_created_at = timezone.now()
        self.save()
        return self.otp

    def verify_otp(self, otp):
        if not self.otp or not self.otp_created_at:
            return False

        # OTP expires after 10 minutes
        if timezone.now() > self.otp_created_at + timedelta(minutes=10):
            return False

        return self.otp == otp

    def generate_password_reset_token(self):
        self.password_reset_token = "".join(
            random.choices(string.ascii_letters + string.digits, k=64)
        )
        self.password_reset_expires = timezone.now() + timedelta(hours=1)
        self.save()
        return self.password_reset_token

    def verify_password_reset_token(self, token):
        return (
            self.password_reset_token == token
            and self.password_reset_expires
            and timezone.now() < self.password_reset_expires
        )

    def save(self, *args, **kwargs):
        created = not self.pk
        super().save(*args, **kwargs)
        if created:
            # Create UserCredit with $1 free credit for new users
            UserCredit.objects.get_or_create(
                user=self,
                defaults={
                    "free_credit": Decimal("1.00"),
                    "paid_credit": Decimal("0.00"),
                },
            )

    def has_role(self, role_name):
        return role_name in self.roles


class PaymentTransaction(models.Model):
    STATUS_CHOICES = [
        ("created", "Created"),
        ("pending", "Pending"),
        ("completed", "Completed"),
        ("failed", "Failed"),
        ("refunded", "Refunded"),
    ]

    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="transactions"
    )
    amount = models.DecimalField(max_digits=10, decimal_places=7)
    currency = models.CharField(max_length=3, default="USD")
    razorpay_payment_id = models.CharField(
        max_length=100, null=True, blank=True
    )
    razorpay_order_id = models.CharField(max_length=100)
    razorpay_signature = models.CharField(
        max_length=100, null=True, blank=True
    )
    razorpay_invoice_id = models.CharField(
        max_length=100, null=True, blank=True
    )
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default="created"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["-created_at"]
        verbose_name = "Payment Transaction"
        verbose_name_plural = "Payment Transactions"

    def __str__(self):
        return f"{self.user.email} - {self.amount} {self.currency} ({self.status})"


class UserCredit(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, related_name="credit", primary_key=True
    )
    free_credit = models.DecimalField(
        max_digits=10, decimal_places=7, default=Decimal("1.00")
    )
    paid_credit = models.DecimalField(
        max_digits=10, decimal_places=7, default=Decimal("0.00")
    )
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "User Credit"
        verbose_name_plural = "User Credits"

    @property
    def total_credit(self):
        return self.free_credit + self.paid_credit

    def __str__(self):
        return f"{self.user.email} - {self.total_credit} USD"


class UsageHistory(models.Model):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="usage_history"
    )
    service_type = models.CharField(max_length=50)
    input_length = models.IntegerField()
    cost = models.DecimalField(max_digits=10, decimal_places=7)
    timestamp = models.DateTimeField(auto_now_add=True)
    # reference_id = models.CharField(max_length=100, null=True, blank=True)

    class Meta:
        verbose_name = "Usage History"
        verbose_name_plural = "Usage History"
        ordering = ["-timestamp"]
        indexes = [
            models.Index(fields=["user", "timestamp"]),
            models.Index(fields=["service_type"]),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.service_type} - {self.cost} USD"
