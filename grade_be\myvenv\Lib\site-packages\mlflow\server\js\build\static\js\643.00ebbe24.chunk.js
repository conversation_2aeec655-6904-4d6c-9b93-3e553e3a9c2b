/*! For license information please see 643.00ebbe24.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[643],{18930:function(t,e,r){t.exports=r(91231)},26737:function(t,e,r){r.d(e,{w:function(){return i}});var s=r(31014);const n=s.createContext(!1),i=()=>s.useContext(n);n.Provider},35067:function(t,e,r){r.d(e,{h:function(){return u}});var s=r(31014);function n(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}const i=s.createContext(n()),u=()=>s.useContext(i)},41698:function(t,e,r){r.d(e,{EU:function(){return i},iL:function(){return u},nE:function(){return n},tu:function(){return s}});const s=t=>{t.suspense&&"number"!==typeof t.staleTime&&(t.staleTime=1e3)},n=(t,e)=>t.isLoading&&t.isFetching&&!e,i=(t,e,r)=>(null==t?void 0:t.suspense)&&n(e,r),u=(t,e,r)=>e.fetchOptimistic(t).then((e=>{let{data:r}=e;null==t.onSuccess||t.onSuccess(r),null==t.onSettled||t.onSettled(r,null)})).catch((e=>{r.clearReset(),null==t.onError||t.onError(e),null==t.onSettled||t.onSettled(void 0,e)}))},44200:function(t,e,r){r.d(e,{t:function(){return h}});var s=r(31014),n=r(61226),i=r(95904),u=r(35067),o=r(27288),c=r(26737),a=r(52165),l=r(41698);function h(t,e){const r=(0,o.jE)({context:t.context}),h=(0,c.w)(),d=(0,u.h)(),f=r.defaultQueryOptions(t);f._optimisticResults=h?"isRestoring":"optimistic",f.onError&&(f.onError=i.j.batchCalls(f.onError)),f.onSuccess&&(f.onSuccess=i.j.batchCalls(f.onSuccess)),f.onSettled&&(f.onSettled=i.j.batchCalls(f.onSettled)),(0,l.tu)(f),(0,a.LJ)(f,d),(0,a.wZ)(d);const[p]=s.useState((()=>new e(r,f))),y=p.getOptimisticResult(f);if((0,n.r)(s.useCallback((t=>{const e=h?()=>{}:p.subscribe(i.j.batchCalls(t));return p.updateResult(),e}),[p,h]),(()=>p.getCurrentResult()),(()=>p.getCurrentResult())),s.useEffect((()=>{p.setOptions(f,{listeners:!1})}),[f,p]),(0,l.EU)(f,y,h))throw(0,l.iL)(f,p,d);if((0,a.$1)({result:y,errorResetBoundary:d,useErrorBoundary:f.useErrorBoundary,query:p.getCurrentQuery()}))throw y.error;return f.notifyOnChangeProps?y:p.trackResult(y)}},45586:function(t,e,r){r.d(e,{$:function(){return c}});var s=r(28999),n=r(95904),i=r(47125),u=r(21363),o=r(98159);class c extends u.Q{constructor(t,e){super(),this.client=t,this.options=e,this.trackedProps=new Set,this.selectError=null,this.bindMethods(),this.setOptions(e)}bindMethods(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.currentQuery.addObserver(this),a(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.currentQuery,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.currentQuery,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.clearStaleTimeout(),this.clearRefetchInterval(),this.currentQuery.removeObserver(this)}setOptions(t,e){const r=this.options,n=this.currentQuery;if(this.options=this.client.defaultQueryOptions(t),(0,s.f8)(r,this.options)||this.client.getQueryCache().notify({type:"observerOptionsUpdated",query:this.currentQuery,observer:this}),"undefined"!==typeof this.options.enabled&&"boolean"!==typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=r.queryKey),this.updateQuery();const i=this.hasListeners();i&&h(this.currentQuery,n,this.options,r)&&this.executeFetch(),this.updateResult(e),!i||this.currentQuery===n&&this.options.enabled===r.enabled&&this.options.staleTime===r.staleTime||this.updateStaleTimeout();const u=this.computeRefetchInterval();!i||this.currentQuery===n&&this.options.enabled===r.enabled&&u===this.currentRefetchInterval||this.updateRefetchInterval(u)}getOptimisticResult(t){const e=this.client.getQueryCache().build(this.client,t);return this.createResult(e,t)}getCurrentResult(){return this.currentResult}trackResult(t){const e={};return Object.keys(t).forEach((r=>{Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:()=>(this.trackedProps.add(r),t[r])})})),e}getCurrentQuery(){return this.currentQuery}remove(){this.client.getQueryCache().remove(this.currentQuery)}refetch(){let{refetchPage:t,...e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.fetch({...e,meta:{refetchPage:t}})}fetchOptimistic(t){const e=this.client.defaultQueryOptions(t),r=this.client.getQueryCache().build(this.client,e);return r.isFetchingOptimistic=!0,r.fetch().then((()=>this.createResult(r,e)))}fetch(t){var e;return this.executeFetch({...t,cancelRefetch:null==(e=t.cancelRefetch)||e}).then((()=>(this.updateResult(),this.currentResult)))}executeFetch(t){this.updateQuery();let e=this.currentQuery.fetch(this.options,t);return null!=t&&t.throwOnError||(e=e.catch(s.lQ)),e}updateStaleTimeout(){if(this.clearStaleTimeout(),s.S$||this.currentResult.isStale||!(0,s.gn)(this.options.staleTime))return;const t=(0,s.j3)(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout((()=>{this.currentResult.isStale||this.updateResult()}),t)}computeRefetchInterval(){var t;return"function"===typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(t=this.options.refetchInterval)&&t}updateRefetchInterval(t){this.clearRefetchInterval(),this.currentRefetchInterval=t,!s.S$&&!1!==this.options.enabled&&(0,s.gn)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval((()=>{(this.options.refetchIntervalInBackground||i.m.isFocused())&&this.executeFetch()}),this.currentRefetchInterval))}updateTimers(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())}clearStaleTimeout(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)}clearRefetchInterval(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)}createResult(t,e){const r=this.currentQuery,n=this.options,i=this.currentResult,u=this.currentResultState,c=this.currentResultOptions,l=t!==r,f=l?t.state:this.currentQueryInitialState,p=l?this.currentResult:this.previousQueryResult,{state:y}=t;let R,{dataUpdatedAt:v,error:m,errorUpdatedAt:S,fetchStatus:b,status:g}=y,E=!1,Q=!1;if(e._optimisticResults){const s=this.hasListeners(),i=!s&&a(t,e),u=s&&h(t,r,e,n);(i||u)&&(b=(0,o.v_)(t.options.networkMode)?"fetching":"paused",v||(g="loading")),"isRestoring"===e._optimisticResults&&(b="idle")}if(e.keepPreviousData&&!y.dataUpdatedAt&&null!=p&&p.isSuccess&&"error"!==g)R=p.data,v=p.dataUpdatedAt,g=p.status,E=!0;else if(e.select&&"undefined"!==typeof y.data)if(i&&y.data===(null==u?void 0:u.data)&&e.select===this.selectFn)R=this.selectResult;else try{this.selectFn=e.select,R=e.select(y.data),R=(0,s.pl)(null==i?void 0:i.data,R,e),this.selectResult=R,this.selectError=null}catch(w){0,this.selectError=w}else R=y.data;if("undefined"!==typeof e.placeholderData&&"undefined"===typeof R&&"loading"===g){let t;if(null!=i&&i.isPlaceholderData&&e.placeholderData===(null==c?void 0:c.placeholderData))t=i.data;else if(t="function"===typeof e.placeholderData?e.placeholderData():e.placeholderData,e.select&&"undefined"!==typeof t)try{t=e.select(t),this.selectError=null}catch(w){0,this.selectError=w}"undefined"!==typeof t&&(g="success",R=(0,s.pl)(null==i?void 0:i.data,t,e),Q=!0)}this.selectError&&(m=this.selectError,R=this.selectResult,S=Date.now(),g="error");const I="fetching"===b,C="loading"===g,O="error"===g;return{status:g,fetchStatus:b,isLoading:C,isSuccess:"success"===g,isError:O,isInitialLoading:C&&I,data:R,dataUpdatedAt:v,error:m,errorUpdatedAt:S,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>f.dataUpdateCount||y.errorUpdateCount>f.errorUpdateCount,isFetching:I,isRefetching:I&&!C,isLoadingError:O&&0===y.dataUpdatedAt,isPaused:"paused"===b,isPlaceholderData:Q,isPreviousData:E,isRefetchError:O&&0!==y.dataUpdatedAt,isStale:d(t,e),refetch:this.refetch,remove:this.remove}}updateResult(t){const e=this.currentResult,r=this.createResult(this.currentQuery,this.options);if(this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,(0,s.f8)(r,e))return;this.currentResult=r;const n={cache:!0};!1!==(null==t?void 0:t.listeners)&&(()=>{if(!e)return!0;const{notifyOnChangeProps:t}=this.options;if("all"===t||!t&&!this.trackedProps.size)return!0;const r=new Set(null!=t?t:this.trackedProps);return this.options.useErrorBoundary&&r.add("error"),Object.keys(this.currentResult).some((t=>{const s=t;return this.currentResult[s]!==e[s]&&r.has(s)}))})()&&(n.listeners=!0),this.notify({...n,...t})}updateQuery(){const t=this.client.getQueryCache().build(this.client,this.options);if(t===this.currentQuery)return;const e=this.currentQuery;this.currentQuery=t,this.currentQueryInitialState=t.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==e||e.removeObserver(this),t.addObserver(this))}onQueryUpdate(t){const e={};"success"===t.type?e.onSuccess=!t.manual:"error"!==t.type||(0,o.wm)(t.error)||(e.onError=!0),this.updateResult(e),this.hasListeners()&&this.updateTimers()}notify(t){n.j.batch((()=>{var e,r,s,n;if(t.onSuccess)null==(e=(r=this.options).onSuccess)||e.call(r,this.currentResult.data),null==(s=(n=this.options).onSettled)||s.call(n,this.currentResult.data,null);else if(t.onError){var i,u,o,c;null==(i=(u=this.options).onError)||i.call(u,this.currentResult.error),null==(o=(c=this.options).onSettled)||o.call(c,void 0,this.currentResult.error)}t.listeners&&this.listeners.forEach((t=>{let{listener:e}=t;e(this.currentResult)})),t.cache&&this.client.getQueryCache().notify({query:this.currentQuery,type:"observerResultsUpdated"})}))}}function a(t,e){return function(t,e){return!1!==e.enabled&&!t.state.dataUpdatedAt&&!("error"===t.state.status&&!1===e.retryOnMount)}(t,e)||t.state.dataUpdatedAt>0&&l(t,e,e.refetchOnMount)}function l(t,e,r){if(!1!==e.enabled){const s="function"===typeof r?r(t):r;return"always"===s||!1!==s&&d(t,e)}return!1}function h(t,e,r,s){return!1!==r.enabled&&(t!==e||!1===s.enabled)&&(!r.suspense||"error"!==t.state.status)&&d(t,r)}function d(t,e){return t.isStaleByTime(e.staleTime)}},52165:function(t,e,r){r.d(e,{$1:function(){return o},LJ:function(){return i},wZ:function(){return u}});var s=r(31014),n=r(71233);const i=(t,e)=>{(t.suspense||t.useErrorBoundary)&&(e.isReset()||(t.retryOnMount=!1))},u=t=>{s.useEffect((()=>{t.clearReset()}),[t])},o=t=>{let{result:e,errorResetBoundary:r,useErrorBoundary:s,query:i}=t;return e.isError&&!r.isReset()&&!e.isFetching&&(0,n.G)(s,[e.error,i])}},61226:function(t,e,r){r.d(e,{r:function(){return s}});const s=r(18930).useSyncExternalStore},71233:function(t,e,r){function s(t,e){return"function"===typeof t?t(...e):!!t}r.d(e,{G:function(){return s}})},91231:function(t,e,r){var s=r(31014);var n="function"===typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t===1/e)||t!==t&&e!==e},i=s.useState,u=s.useEffect,o=s.useLayoutEffect,c=s.useDebugValue;function a(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!n(t,r)}catch(s){return!0}}var l="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(t,e){return e()}:function(t,e){var r=e(),s=i({inst:{value:r,getSnapshot:e}}),n=s[0].inst,l=s[1];return o((function(){n.value=r,n.getSnapshot=e,a(n)&&l({inst:n})}),[t,r,e]),u((function(){return a(n)&&l({inst:n}),t((function(){a(n)&&l({inst:n})}))}),[t]),c(r),r};e.useSyncExternalStore=void 0!==s.useSyncExternalStore?s.useSyncExternalStore:l}}]);
//# sourceMappingURL=643.00ebbe24.chunk.js.map