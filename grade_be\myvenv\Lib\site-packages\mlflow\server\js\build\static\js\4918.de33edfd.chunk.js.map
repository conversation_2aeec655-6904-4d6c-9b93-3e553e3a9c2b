{"version": 3, "file": "static/js/4918.de33edfd.chunk.js", "mappings": "uOA2BA,MAAMA,EAAoB,SAI1B,IAAAC,EAAA,CAAAC,KAAA,UAAAC,OAAA,iCACA,MAAMC,EAAWC,IAUV,IAVW,GAChBC,EAAE,SACFC,EAAQ,UACRC,EAAS,WACTC,GAMDJ,EACC,MAAMK,GAAOC,EAAAA,EAAAA,KACb,OACEC,EAAAA,EAAAA,IAACC,EAAAA,IAAY,CACXC,YAAY,6EACZR,GAAIA,EACJS,IAAGd,EACHe,YAAaN,EAAKO,cAAc,CAAAX,GAAA,SAC9BY,eAAe,+BAGjBX,SAAUY,IAAiB,IAAhB,OAAEC,GAAQD,EACnB,MAAM,MAAEE,GAAUD,GACXE,EAASC,GAAOF,EAAMG,MAAM,KAC7BC,EAAUJ,EAAMK,WAAW1B,GACjCO,EAAS,CAAEgB,MAAKE,WAAU,EAC1BE,SAAA,EAEFC,EAAAA,EAAAA,GAACC,EAAAA,IAAuB,CAACC,MAAM,aAAYH,SACxCnB,EAAUuB,KAAKR,IACdK,EAAAA,EAAAA,GAACI,EAAAA,IAAkB,CAAWX,MAAOrB,EAAoBuB,EAAII,SAC1DJ,GADsBA,QAK7BK,EAAAA,EAAAA,GAACC,EAAAA,IAAuB,CAACC,MAAM,UAASH,SACrClB,EAAWsB,KAAKR,IACfK,EAAAA,EAAAA,GAACI,EAAAA,IAAkB,CAAWX,MAzCb,UAyCyCE,EAAII,SAC3DJ,GADsBA,SAKhB,EAEjB,IAAAU,EAAA,CAAA/B,KAAA,SAAAC,OAAA,iFAAA+B,EAAA,CAAAhC,KAAA,SAAAC,OAAA,0CAEK,MAAMgC,EAAgBC,IAAmD,IAAlD,SAAEC,EAAQ,YAAEC,EAAW,WAAEC,GAAmBH,EACxE,MAAOI,EAAOC,IAAYC,EAAAA,EAAAA,UAAe,CAAEnB,SAAKoB,EAAWlB,aAASkB,KAC7DC,EAAOC,IAAYH,EAAAA,EAAAA,UAAe,CAAEnB,SAAKoB,EAAWlB,aAASkB,IAE9DnC,EAAYsC,MAAMC,KAAK,IAAIC,IAAIT,EAAWU,OAAOlB,KAAImB,IAAA,IAAC,IAAE3B,GAAK2B,EAAA,OAAK3B,CAAG,MAAI4B,OACzE1C,EAAaqC,MAAMC,KAAK,IAAIC,IAAIV,EAAYW,OAAOlB,KAAIqB,IAAA,IAAC,IAAE7B,GAAK6B,EAAA,OAAK7B,CAAG,MAAI4B,OAE3EE,EAAiBA,KACrB,MAAMC,EAAO,CAAC,EAmBd,OAlBAjB,EAASkB,SAAQ,CAACC,EAAGC,KACnB,MAAMC,EAASnB,EAAWkB,GACpBE,EAAUrB,EAAYmB,GACtBG,GAAKpB,EAAMf,QAAUiC,EAASC,GAASE,MAAKC,IAAA,IAAC,IAAEvC,GAAKuC,EAAA,OAAKvC,IAAQiB,EAAMjB,GAAG,IAC1EwC,GAAKnB,EAAMnB,QAAUiC,EAASC,GAASE,MAAKG,IAAA,IAAC,IAAEzC,GAAKyC,EAAA,OAAKzC,IAAQqB,EAAMrB,GAAG,SACtEoB,IAANiB,QAAyBjB,IAANoB,IAInBH,EAAEvC,SAASiC,EAEbA,EAAKM,EAAEvC,OAAO4C,KAAKF,EAAE1C,OAGrBiC,EAAKM,EAAEvC,OAAS,CAAC0C,EAAE1C,OACrB,IAGK6C,OAAOC,QAAQb,GAAMvB,KAAIqC,IAAA,IAAE7C,EAAK8C,GAAOD,EAAA,MAAM,CAClDL,EAAGM,EACHC,KAAM,MACNpE,KAAMqB,EACNgD,OAAQ,GACRC,UAAW,IACXC,UAAW,MACZ,GAAE,EA+DL,OACE7D,EAAAA,EAAAA,IAAC8D,EAAAA,IAAG,CAAA/C,SAAA,EACFC,EAAAA,EAAAA,GAAC+C,EAAAA,IAAG,CAACC,KAAM,EAAEjD,UACXf,EAAAA,EAAAA,IAAA,OAAKG,IAAKZ,EAAO0E,aAAalD,SAAA,EAC5BC,EAAAA,EAAAA,GAAA,OAAAD,UACEC,EAAAA,EAAAA,GAACkD,EAAAA,IAAOC,MAAK,CAACC,QAAQ,kBAAiBrD,UACrCC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,iBAKrBU,EAAAA,EAAAA,GAACxB,EAAQ,CAACE,GAAG,kBAAkBC,SAAUkC,EAAUjC,UAAWA,EAAWC,WAAYA,KAErFmB,EAAAA,EAAAA,GAAA,OAAAD,UACEC,EAAAA,EAAAA,GAACkD,EAAAA,IAAOC,MAAK,CAACC,QAAQ,kBAAiBrD,UACrCC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,iBAKrBU,EAAAA,EAAAA,GAACxB,EAAQ,CAACE,GAAG,kBAAkBC,SAAUsC,EAAUrC,UAAWA,EAAWC,WAAYA,UAGzFmB,EAAAA,EAAAA,GAAC+C,EAAAA,IAAG,CAACC,KAAM,GAAGjD,SApFVa,EAAMjB,KAAOqB,EAAMrB,KAsBvBK,EAAAA,EAAAA,GAACsD,EAAAA,EAAQ,CACPnE,IAAGmB,EAKHoB,KAAMD,IACN8B,OAAQ,CACNC,OAAQ,CACNC,EAAG,IAELC,UAAW,UACXC,MAAO,CACLC,MAAOhD,EAAMjB,KAEfkE,MAAO,CACLD,MAAO5C,EAAMrB,MAGjBmE,OAAQ,CACNC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,uBAAwB,CACtB,kBACA,WACA,UACA,eACA,wBACA,0BAGJC,kBAAgB,KApDhBnE,EAAAA,EAAAA,GAAA,OACEb,IAAGkB,EAMDN,UAEFC,EAAAA,EAAAA,GAACoE,EAAAA,EAAWC,KAAI,CAACC,KAAK,KAAIvE,UACxBC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,+CAwEnB,EAIJf,EAAS,CACb0E,aAAesB,IAAU,CACvBC,YAAaD,EAAME,QAAQC,K,4HCpM/B,SAASC,IACP,OACE3E,EAAAA,EAAAA,GAAC4E,EAAAA,IAAK,CACJ,cAAY,WACZhB,OAAO5D,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SAACY,eAAe,UACxCuF,aACE7E,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,sDAInBwF,OAAO9E,EAAAA,EAAAA,GAAC+E,EAAAA,EAAU,KAGxB,CAEA,SAASC,EAAmBvG,GAAsF,IAArF,SAAEsB,EAAQ,wBAAEkF,GAAsExG,EAC7G,SAASyG,EAAkBC,EAAcC,GAEvCC,QAAQF,MAAM,4BAA6BA,EAAOC,EAAKE,eACzD,CAEA,OAAIL,GAEAjF,EAAAA,EAAAA,GAACuF,EAAAA,GAAa,CAACC,QAASN,EAAmBO,kBAAmBR,EAAwBlF,SACnFA,KAMLC,EAAAA,EAAAA,GAACuF,EAAAA,GAAa,CAACC,QAASN,EAAmBQ,UAAU1F,EAAAA,EAAAA,GAAC2E,EAAa,IAAI5E,SACpEA,GAGP,CAEO,SAAS4F,EACdC,EACAC,EACAC,EACAb,GAEA,OAAO,SAAoCc,GACzC,OACE/F,EAAAA,EAAAA,GAACgF,EAAmB,CAACC,wBAAyBA,EAAwBlF,UAEpEC,EAAAA,EAAAA,GAAC6F,EAAS,IAAKE,KAGrB,CACF,C,wDCvDe,MAAMC,EAInB,iBAAOC,CAAWvE,EAAW/B,GAC3B,IAAIuG,EAMJ,OALAxE,EAAKC,SAASlC,IACRA,EAAME,MAAQA,IAChBuG,EAAQzG,EACV,IAEKyG,CACT,CAKA,cAAOC,CAAQC,EAAYC,GACzB,MAAMC,EAAO,CAAC,EAad,OAZAF,EAAMzE,SAAS4E,GACbA,EAAK5E,SAAS6E,IACNA,EAAK7G,OAAO2G,IAEhBA,EAAKE,EAAK7G,MAAO,GAEf0G,GAAWI,MAAMC,WAAWF,EAAK/G,UAEnC6G,EAAKE,EAAK7G,MAAO,EACnB,MAIF2C,OAAOgE,KAAKA,GAETK,QAAQC,GAAMN,EAAKM,KACnBrF,MAEP,E,2FCtBK,MAAMsF,EAOThB,GAGAE,IASA,MAAMe,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,MACXnF,GAASoF,EAAAA,EAAAA,KAEf,OACElH,EAAAA,EAAAA,GAAC6F,EACC,CACA/D,OAAQA,EACRgF,SAAUA,EACVE,SAAUA,KACLjB,GACL,C,6FCpCD,MAAMoB,UAA6BC,EAAAA,UAA8BC,WAAAA,GAAA,SAAAC,WAAA,KACtEC,MAAQ,CAAEpC,MAAO,KAAO,CAExBqC,iBAAAA,CAAkBrC,EAAYsC,GAC5BC,KAAKC,SAAS,CAAExC,UAEhBE,QAAQF,MAAMA,EAAOsC,EACvB,CAEAG,kBAAAA,CAAmBzC,GACjB,OAAOuC,KAAK3B,MAAM8B,iBAAkB7I,EAAAA,EAAAA,IAAA,OAAAe,SAAA,CAAK,kBAAgBoF,EAAM2C,WAAiB,EAClF,CAEAC,MAAAA,GACE,MAAM,SAAEhI,GAAa2H,KAAK3B,OACpB,MAAEZ,GAAUuC,KAAKH,MACvB,OAAIpC,GAEAnF,EAAAA,EAAAA,GAAA,OAAAD,UACEf,EAAAA,EAAAA,IAAA,KAAAe,SAAA,EACEC,EAAAA,EAAAA,GAAA,KAAG,cAAY,YAAYgI,UAAU,uCAAuC7I,IAAK8I,EAAWC,WAC5FlI,EAAAA,EAAAA,GAAA,QAAAD,SAAM,+CACNC,EAAAA,EAAAA,GAAA,QAAAD,SAAM,qDAGNC,EAAAA,EAAAA,GAAA,KAAGmI,KAAMC,EAAAA,EAAMC,oBAAqB7I,OAAO,SAAQO,SAAC,SAEhD,IACF2H,KAAKE,mBAAmBzC,QAM3BpF,CACT,EAGF,MAAMkI,EAAa,CACjBC,QAAS,CACPI,YAAa,G,sOChDV,MAAMC,GACX,EAAAC,EAAAA,eAA+C,MCK3CC,EAAmC,CACvCC,UAAU,EACVvD,MAAO,MAGF,MAAMwD,UAAsBC,EAAAA,UAIjCrB,MAAA,KAAQkB,EAAR,GAEA,+BAAOI,CAAyB1D,GAC9B,MAAO,CAAEuD,UAAU,E,MAAMvD,EAC3B,CAEA2D,mBAAA,UAAAC,EAAA,YAAqB,WACnB,MAAM5D,MAAEA,GAAU4D,EAAKxB,MAEvB,GAAc,OAAVpC,EAAgB,SAAA6D,EAAA1B,UAAA2B,OAHGC,EAAA,IAAAhI,MAAA8H,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAAD,EAAAC,GAAA7B,UAAA6B,GAIrBJ,EAAKhD,MAAMqD,UAAU,C,KACnBF,EACAG,OAAQ,mBAGVN,EAAKpB,SAASc,EAChB,CACF,CAAC,EAXD,GAaAjB,iBAAAA,CAAkBrC,EAAcC,GAC9BsC,KAAK3B,MAAMP,UAAUL,EAAOC,EAC9B,CAEAkE,kBAAAA,CACEC,EACAC,GAEA,MAAMd,SAAEA,GAAahB,KAAKH,OACpBkC,UAAEA,GAAc/B,KAAK3B,MAQzB2C,GACoB,OAApBc,EAAUrE,OAqDhB,WAAuD,IAA9BuE,EAAApC,UAAA2B,OAAA,QAAAlI,IAAAuG,UAAA,GAAAA,UAAA,GAAW,GAAIqC,EAAArC,UAAA2B,OAAA,QAAAlI,IAAAuG,UAAA,GAAAA,UAAA,GAAW,GACjD,OACEoC,EAAET,SAAWU,EAAEV,QAAUS,EAAEE,MAAK,CAACpD,EAAM3E,KAAWS,OAAOuH,GAAGrD,EAAMmD,EAAE9H,KAExE,CAxDMiI,CAAgBP,EAAUE,UAAWA,KAErC/B,KAAK3B,MAAMqD,UAAU,CACnBW,KAAMN,EACNO,KAAMT,EAAUE,UAChBJ,OAAQ,SAGV3B,KAAKC,SAASc,GAElB,CAEAV,MAAAA,GACE,MAAMhI,SAAEA,EAAQkK,eAAEA,EAAcxE,kBAAEA,EAAiBC,SAAEA,GACnDgC,KAAK3B,OACD2C,SAAEA,EAAQvD,MAAEA,GAAUuC,KAAKH,MAEjC,IAAI2C,EAAgBnK,EAEpB,GAAI2I,EAAU,CACZ,MAAM3C,EAAuB,C,MAC3BZ,EACA2D,mBAAoBpB,KAAKoB,oBAG3B,IAAI,EAAAqB,EAAAA,gBAAezE,GACjBwE,EAAgBxE,OACX,GAA8B,oBAAnBuE,EAChBC,EAAgBD,EAAelE,OAC1B,KAAIN,EAGT,MAAM,IAAI2E,MACR,8FAHFF,GAAgB,EAAAG,EAAAA,eAAc5E,EAAmBM,EAG/C,CAGN,CAEA,OAAO,EAAAsE,EAAAA,eACL9B,EAAqB+B,SACrB,CACE7K,MAAO,C,SACLiJ,E,MACAvD,EACA2D,mBAAoBpB,KAAKoB,qBAG7BoB,EAEJ,EC5GK,SAASK,EACd9K,GAEA,GACW,MAATA,GAC0B,mBAAnBA,EAAMiJ,UACuB,oBAA7BjJ,EAAMqJ,mBAEb,MAAM,IAAIsB,MAAM,kCAGlB,OAAO,CACT,CCLO,SAASI,IACd,MAAMC,GAAU,EAAAC,EAAAA,YAAWnC,GAE3BgC,EAA2BE,GAE3B,MAAOlD,EAAOI,IAAY,EAAAgD,EAAAA,UAGvB,CACDxF,MAAO,KACPyF,UAAU,IAGNC,GAAW,EAAAC,EAAAA,UACf,KAAM,CACJC,cAAeA,KACbN,GAAS3B,qBACTnB,EAAS,CAAExC,MAAO,KAAMyF,UAAU,GAAQ,EAE5CI,aAAe7F,GACbwC,EAAS,C,MACPxC,EACAyF,UAAU,OAGhB,CAACH,GAAS3B,qBAGZ,GAAIvB,EAAMqD,SACR,MAAMrD,EAAMpC,MAGd,OAAO0F,CACT,C,iCCtCO,SAASI,EACdpF,EACAqF,GAEA,MAAMC,EAAiCpF,IAC9B,EAAAsE,EAAAA,eACL1B,EACAuC,GACA,EAAAb,EAAAA,eAAcxE,EAAWE,IAKvBzH,EAAOuH,EAAUuF,aAAevF,EAAUvH,MAAQ,UAGxD,OAFA6M,EAAQC,YAAc,qBAAqB9M,KAEpC6M,CACT,C,+JCjBO,MAAME,EAAe5M,IAA4C,IAA3C,UAAEuJ,GAAmCvJ,EAChE,MAAM,MAAE8F,IAAU+G,EAAAA,EAAAA,KAClB,OACEtL,EAAAA,EAAAA,GAACuL,EAAAA,IAAG,CACFrM,YAAY,oEACZ8I,UAAWA,EACX7I,KAAGqM,EAAAA,EAAAA,IAAE,CAAElD,WAAY/D,EAAME,QAAQC,IAAI,IACrC+G,MAAM,YAAW1L,UAEjBC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,kBAGb,C,uECXH,MAAMoM,EAA2B3F,IACtC/G,EAAAA,EAAAA,IAAA,OAAKG,IAAKZ,EAAO2J,QAAQnI,SAAA,EACvBC,EAAAA,EAAAA,GAAA,OAAKb,IAAKZ,EAAOoN,SAAS5L,SAAEgG,EAAM4F,YAClC3L,EAAAA,EAAAA,GAAA,OAAKb,IAAKZ,EAAOqN,YAAY7L,SAAEgG,EAAMhG,cAInCxB,EAAS,CACbqN,YAAa,CACXC,SAAU,SACVC,MAAO,OACPC,OAAQ,OACRC,UAAW,KAEb9D,QAAS,CACP+D,QAAS,OACTC,oBAAqB,0BAEvBP,SAAWpH,IAAY,CACrB4H,QAAS,KAAK5H,EAAME,QAAQC,S,4rQCPzB,SAAS0H,EAAcrG,GAC5B,MAAM,eAAEsG,KAAmBC,GAAcvG,EACzC,OAEE/G,EAAAA,EAAAA,IAACuN,EAAAA,IAAW,CAACpN,IAAKkN,EAAiB9N,EAAOiO,oBAAsBjO,EAAO2J,QAAQnI,SAAA,EAE7EC,EAAAA,EAAAA,GAACyM,EAAAA,EAAM,CAACtN,IAAKZ,EAAOmO,cACnBL,EAAiBtG,EAAMhG,UAAWC,EAAAA,EAAAA,GAAA,UAASsM,EAAWnN,IAAKZ,EAAOoO,cAGzE,CAEAP,EAAcQ,aAAe,CAC3BP,gBAAgB,GAGlB,MAAM9N,EAAS,CACbiO,oBAAqB,CACnBT,OAAQ,oBACRE,QAAS,OACTY,cAAe,SACf,eAAgB,CACdC,SAAU,IAGd5E,QAAS,CAAE6E,KAAM,GACjBL,YAAa,CAEXM,WAAY,GAEdL,UAAW,CACTb,MAAO,OACPgB,SAAU,EACVG,cAAe,I,0MCtBZ,MAAMC,UAA8BrH,EAAAA,UAQzCwB,WAAAA,CAAYtB,GACVoH,MAAMpH,GAAO,KAJflH,gBAAU,OACVD,eAAS,EAKP8I,KAAK7I,WAAamH,EAAAA,EAAeG,QAAQuB,KAAK3B,MAAMrF,aAAa,GACjEgH,KAAK9I,UAAYoH,EAAAA,EAAeG,QAAQuB,KAAK3B,MAAMpF,YAAY,GAE3D+G,KAAK9I,UAAUqK,OAASvB,KAAK7I,WAAWoK,OAAS,EACnDvB,KAAKH,MAAQ,CAAE6F,UAAU,GAEzB1F,KAAKH,MAAQ,CACX6F,UAAU,EACVpL,EACE0F,KAAK9I,UAAUqK,OAAS,EACpB,CACEtJ,IAAK+H,KAAK9I,UAAU,GACpByO,UAAU,GAEZ,CACE1N,IAAK+H,KAAK7I,WAAW,GACrBwO,UAAU,GAElBlL,EACEuF,KAAK7I,WAAWoK,OAAS,EACrB,CACEtJ,IAAK+H,KAAK7I,WAAW,GACrBwO,UAAU,GAEZ,CACE1N,IAAK+H,KAAK9I,UAAU,GACpByO,UAAU,GAIxB,CAKAC,QAAAA,CAASC,EAAM9O,GAA2B,IAAzB,IAAEkB,EAAG,SAAE0N,GAAe5O,EACrC,MAAMgB,EAAQuG,EAAAA,EAAeC,YAAYoH,EAAW3F,KAAK3B,MAAMrF,YAAcgH,KAAK3B,MAAMpF,YAAY4M,GAAI5N,GACxG,YAAiBoB,IAAVtB,EAAsBA,EAASA,EAAcA,KACtD,CAEAsI,MAAAA,GAEE,GAAIL,KAAKH,MAAM6F,SACb,OAAOpN,EAAAA,EAAAA,GAAA,UAGT,MAAMwN,EAAYN,EAAsBO,oBAElC/I,EAAU,GACVgJ,EAAU,GACVC,EAAgB,GAetB,OAbAjG,KAAK3B,MAAMtF,SAASkB,SAAQ,CAACC,EAAGC,KAE9B,MAAMG,EAAI0F,KAAK4F,SAASzL,EAAO6F,KAAKH,MAAMvF,GAEpCG,EAAIuF,KAAK4F,SAASzL,EAAO6F,KAAKH,MAAMpF,QAChCpB,IAANiB,QAAyBjB,IAANoB,IAGvBuC,EAAGrC,KAAKL,GACR0L,EAAGrL,KAAKF,GACRwL,EAAStL,KAAKqF,KAAKkG,iBAAiB/L,IAAO,KAI3C7B,EAAAA,EAAAA,GAAC0L,EAAAA,EAAuB,CACtBC,UACE3M,EAAAA,EAAAA,IAAA6O,EAAAA,GAAA,CAAA9N,SAAA,EACEf,EAAAA,EAAAA,IAAA,OAAAe,SAAA,EACEC,EAAAA,EAAAA,GAACkD,EAAAA,IAAOC,MAAK,CAACC,QAAQ,kBAAiBrD,UACrCC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,cAIlBoI,KAAKoG,aAAa,SAErB9N,EAAAA,EAAAA,GAACyM,EAAAA,EAAM,KACPzN,EAAAA,EAAAA,IAAA,OAAAe,SAAA,EACEC,EAAAA,EAAAA,GAACkD,EAAAA,IAAOC,MAAK,CAACC,QAAQ,kBAAiBrD,UACrCC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,cAIlBoI,KAAKoG,aAAa,WAGxB/N,UAEDC,EAAAA,EAAAA,GAACsD,EAAAA,EAAQ,CACP5B,KAAM,CACJ,CACEM,EAAG0C,EACHvC,EAAGuL,EACHK,KAAMJ,EACNK,UAAW,OACXtL,KAAM,YACNuL,KAAM,UACNC,OAAQ,CACN5J,KAAM,GACNmH,MAAO,6BAIblI,OAAQ,CACNC,OAAQ,CACNC,EAAG,IAELC,UAAW,UACXC,MAAO,CACLC,OAAOuK,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAe1G,KAAKH,MAAS,EAAE5H,IAAK6N,KAE1D3J,MAAO,CACLD,OAAOuK,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAe1G,KAAKH,MAAS,EAAE5H,IAAK6N,MAG5DrO,IAAKZ,EAAO8P,KACZvK,OAAQ,CACNC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,uBAAwB,CACtB,kBACA,WACA,UACA,eACA,wBACA,0BAGJC,kBAAgB,KAIxB,CAEA2J,YAAAA,CAAaQ,GACX,OACEtP,EAAAA,EAAAA,IAACC,EAAAA,IAAY,CACXC,YAAY,kFACZC,IAAKZ,EAAOgQ,OACZ7P,GAAI4P,EAAO,iBACX3P,SAAUN,IAAiB,IAAhB,OAAEmB,GAAQnB,EACnB,MAAM,MAAEoB,GAAUD,GACXgP,KAAWC,GAAYhP,EAAMG,MAAM,KACpCD,EAAM8O,EAASC,KAAK,KACpBrB,EAAsB,WAAXmB,EACjB9G,KAAKC,SAAS,CAAE,CAAC2G,GAAO,CAAEjB,WAAU1N,QAAQ,EAE9CF,OAAQiI,KAAKH,MAAM+G,GAAMjB,SAAW,UAAY,UAAY3F,KAAKH,MAAM+G,GAAM3O,IAAII,SAAA,EAEjFC,EAAAA,EAAAA,GAACC,EAAAA,IAAuB,CAACC,MAAM,YAAWH,SACvC2H,KAAK9I,UAAUuB,KAAKwO,IACnB3O,EAAAA,EAAAA,GAACI,EAAAA,IAAkB,CAAoBX,MAAO,SAAWkP,EAAE5O,SACxD4O,GADsB,SAAWA,QAKxC3O,EAAAA,EAAAA,GAACC,EAAAA,IAAuB,CAACC,MAAM,SAAQH,SACpC2H,KAAK7I,WAAWsB,KAAKyO,IACpB5O,EAAAA,EAAAA,GAACI,EAAAA,IAAkB,CAAqBX,MAAO,UAAYmP,EAAE7O,SAC1D6O,GADsB,UAAYA,SAO/C,CAEAhB,gBAAAA,CAAiB/L,GACf,MAAM2L,EAAYN,EAAsBO,oBAClCoB,EAAc3B,EAAsB4B,sBACpCC,EAAUrH,KAAK3B,MAAMiJ,gBAAgBnN,GAC3C,IAAIoN,EAAS,OAAMd,EAAAA,EAAAA,QAAOY,aAC1B,MAAMG,EAAYxH,KAAK3B,MAAMpF,WAAWkB,GACxCqN,EAAUvN,SAASgN,IACjBM,IACEd,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAeO,EAAEhP,IAAK6N,IACnC,MACAW,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAeO,EAAElP,MAAOoP,IACrC,MAAM,IAEV,MAAMM,EAAazH,KAAK3B,MAAMrF,YAAYmB,GAO1C,OANIsN,EAAWlG,OAAS,IACtBgG,GAAUC,EAAUjG,OAAS,EAAI,OAAS,GAC1CkG,EAAWxN,SAASiN,IAClBK,IAAUd,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAeQ,EAAEjP,IAAK6N,IAAc,KAAOpF,EAAAA,EAAMgH,aAAaR,EAAEnP,OAAS,MAAM,KAGnGwP,CACT,EA3MW/B,EAEJO,oBAAsB,GAFlBP,EAGJ4B,sBAAwB,GA2MjC,MAAMvQ,EAAS,CACbgQ,OAAQ,CACNzC,MAAO,QAETuC,KAAM,CACJvC,MAAO,SAiBEuD,GAAoBC,EAAAA,EAAAA,KAbTC,CAAChI,EAAYiI,KACnC,MAAM/O,EAAgB,GAChBC,EAAmB,GACnBC,EAAkB,IAClB,SAAE8O,GAAaD,EAMrB,OALAC,EAAS9N,SAAS+N,IAChBjP,EAAS4B,MAAKsN,EAAAA,EAAAA,IAAWD,EAASnI,IAClC7G,EAAY2B,KAAKC,OAAOG,QAAOmN,EAAAA,EAAAA,IAAiBF,EAASnI,KACzD5G,EAAW0B,KAAKC,OAAOG,QAAOoN,EAAAA,EAAAA,IAAUH,EAASnI,IAAQ,IAEpD,CAAE9G,WAAUC,cAAaC,aAAY,GAGb2O,CAAyBpC,E,uJC1NnD,MAAM4C,UAA4BjK,EAAAA,UAA8DwB,WAAAA,GAAA,SAAAC,WAAA,KAOrGC,MAAQ,CACNwI,cAAc,EACdC,mBAAmB,EACnB,CAEF,uBAAOC,CAAiBC,EAAeC,GACrC,OAAOD,EAASvJ,QAAQyJ,QACNrP,IAAZqP,EAAEjL,SAEFgL,GACAA,EAA2BE,SAASD,EAAE1R,KACtC0R,EAAEjL,MAAMmL,iBAAmBC,EAAAA,GAAWC,0BAK9C,CAEA,+BAAOC,CAAyBC,GAC9B,MAAMX,IAAeW,EAAUR,SAASjH,QACpCyH,EAAUR,SAASS,OAAOP,GAAWA,IAAkB,IAAbA,EAAEQ,SAG1CC,EAAgBf,EAAoBG,iBACxCS,EAAUR,SACVQ,EAAUP,4BAGZ,MAAO,CACLJ,eACAC,kBAAmBa,EAAc5H,OAAS,EAC1C4H,gBAEJ,CAEAC,kBAAAA,GACE,MAAM,SAAE/Q,EAAQ,SAAEmQ,EAAQ,cAAEa,EAAa,qBAAEC,EAAoB,mBAAEC,EAAkB,4BAAEC,GACnFxJ,KAAK3B,OAED,aAAEgK,EAAY,kBAAEC,EAAiB,cAAEa,GAAkBnJ,KAAKH,MAC1D4J,EAAyBN,EAAclK,QAAQyK,GAC5CA,EAAcjM,MAAMmL,iBAAmBC,EAAAA,GAAWc,oBAG3D,MAAwB,oBAAbtR,EACFA,GAAUgQ,EAAcC,EAAmBE,EAAUW,GACnDd,GAAgBC,GAAqBtI,KAAK3B,MAAMuL,2BACrDH,EAAuBlI,OAAS,GAAK+H,EAChCA,GAELhB,IAAsBiB,IACxBC,EAA8BA,EAA4BL,GAAiBU,EAAaV,IAGnF9Q,GAGFgR,IAAiB/Q,EAAAA,EAAAA,GAACwR,EAAAA,EAAO,GAClC,CAEAzJ,MAAAA,GACE,OAAOL,KAAKoJ,oBACd,EArEWhB,EACJlD,aAAe,CACpBsD,SAAU,GACVC,2BAA4B,GAC5BmB,4BAA4B,GAoEzB,MAAMC,EAAgBrB,IAI3B,MADA7K,QAAQF,MAAM,QAAS+K,GACjB9F,MAAM,8BAA6B8F,EAAS/K,QAAQ,EAO5D,OAAemK,EAAAA,EAAAA,KAJSC,CAAChI,EAAmBiI,KAAoD,CAC9FU,UAAUuB,EAAAA,EAAAA,IAAQjC,EAASkC,WAAYnK,MAGzC,CAAwCuI,E,4HC9GxC,MACa6B,EAAe,UAYrB,MAAMC,UAAoCxK,EAAAA,UAG/CC,WAAAA,GAAA,SAAAC,WAAA,KACAC,MAAQ,CAENsK,SAAU,IAAInK,KAAK3B,MAAMnH,aAAc8I,KAAK3B,MAAMlH,aA4DpD,KACAiT,2BAA6B,KAE3B,MAAMC,EAAgB,IAAI3Q,IAAIsG,KAAK3B,MAAMlH,YAGzC+S,EAA4BI,0BACzBrL,QAAQsL,GAAOF,EAAcG,IAAID,EAAGE,aACpCxQ,SAASsQ,IACPA,EAAWG,MAAMC,KAAO,QACxBJ,EAAWG,MAAME,WAAa,MAAM,GACrC,EACJ,KAEFC,8BAAiCC,IAC/B,MAAMC,EAA8B/K,KAAKgL,qBAAqBhL,KAAK3B,MAAMlH,YACnEkT,EAAgB,IAAI3Q,IAAIsG,KAAK3B,MAAMlH,YAIrC4T,IAHiC7Q,IAAAA,SAAW4Q,GAA4B7S,GAAQoS,EAAcG,IAAIvS,MAIpG+H,KAAKC,SAAS,CAAEkK,SAAUW,GAC5B,EACA,KAEFG,iBAAmBlU,IAAsC,IAAnCiD,OAAO,WAAEkR,KAAoBnU,EACjDiJ,KAAKoK,6BACLpK,KAAK6K,8BAA8BK,EAAWzS,KAAK0S,GAAWA,EAAE3S,QAAO,CACvE,CArFF,+BAAOuQ,CAAyB1K,EAAYwB,GAC1C,MAAMuL,EAAgB,IAAI/M,EAAMnH,aAAcmH,EAAMlH,YAC9CkU,EAAgBxL,EAAMsK,SAC5B,OAAKjQ,IAAAA,QAAUA,IAAAA,OAASkR,GAAgBlR,IAAAA,OAASmR,IAG1C,KAFE,CAAElB,SAAUiB,EAGvB,CAEAE,OAAAA,GACE,MAAM,SAAEnB,GAAanK,KAAKH,OACpB,gBAAE0L,EAAe,iBAAEC,EAAgB,WAAErU,GAAe6I,KAAK3B,MACzDoN,EAAgBzL,KAAKgL,qBAAqB7T,GAC1CuU,EAAsB1L,KAAK3B,MAAMmN,iBAAiBjR,MAAM4Q,GAAMA,EAAE3S,QAAUiT,IAOhF,MAAO,CACL,CACEzQ,KAAM,YACN2Q,KAAM,IATgBzB,EAA4B0B,iCAAiCF,IAUnFR,WARsBhB,EAA4B2B,+BACpD,IAAIN,KAAoBC,GACxBrB,IASJ,CAEA,qCAAO0B,CAA+BX,EAAiBf,GACrD,OAAOjQ,IAAAA,OAASgR,EAAY,CAAEY,GAAc3B,EAAS4B,QAAQD,EAAUtT,QACzE,CAIAwS,oBAAAA,CAAqBpM,GACnB,MAAM,SAAEuL,GAAanK,KAAKH,MACpBmM,EAAS,IAAItS,IAAIkF,GACvB,OAAO1E,IAAAA,SAAWiQ,GAAWlS,GAAQ+T,EAAOxB,IAAIvS,IAClD,CAEA,uCAAO2T,CAAiCE,GACtC,IAAKA,EAAW,OAAO,KAGvB,MAAO,CACLG,WAAW,EACXC,WAAY,MACZC,KALWjS,IAAAA,IAAM4R,EAAU/Q,QAM3BqR,KALWlS,IAAAA,IAAM4R,EAAU/Q,QAM3BgJ,MAAO+H,EAAU/Q,OAErB,CAmCAsF,MAAAA,GACE,OACE/H,EAAAA,EAAAA,GAACsD,EAAAA,EAAQ,CACPC,OAAQ,CAAEwQ,UAAU,EAAMvQ,OAAQ,CAAEC,EAAG,KACvCU,kBAAgB,EAChBhF,IAAKZ,EAAO8P,KACZ3M,KAAMgG,KAAKsL,UACXgB,SAAUtM,KAAKiL,iBACf3K,UAAU,WACVlE,OAAQ,CAAEmQ,gBAAgB,IAGhC,EA5GWrC,EA0CJI,wBAA0B,IAAM9Q,MAAMC,KAAK+S,SAASC,iBAvDtC,2DA4HhB,MAiDMC,EAAkBA,CAACzU,EAAU8P,EAAe4E,KACvD,IAAIC,EAAa,CAAC,EAClB,MAAMC,EAdiBC,EAAC7U,EAAU8P,EAAe4E,KACjD,IAAK,IAAI9G,EAAI,EAAGA,EAAIkC,EAASxG,OAAQsE,IACnC,GAAI8G,EAAe5E,EAASlC,IAAI5N,GAAM,CACpC,MAAM,MAAEF,GAAU4U,EAAe5E,EAASlC,IAAI5N,GAC9C,GAAqB,kBAAVF,GAAsBgH,MAAMgO,OAAOhV,KAAqB,QAAVA,EACvD,MAAO,QAEX,CAEF,MAAO,QAAQ,EAKE+U,CAAU7U,EAAK8P,EAAU4E,GAC1C,GAAiB,WAAbE,EACFD,EArDsDI,KAGxD,MAAMC,EAAmB/S,IAAAA,KAAO8S,GAAQnT,OAKxC,IAAIqT,GAAiB,EACrB,MAAMC,EAA2BF,EAAiBhO,QAAQzG,IACpDA,IAAUyR,IAAciD,GAAiB,GACtC1U,IAAUyR,KAEfiD,GACFC,EAAyBxS,KAAKsP,GAEhC,MAAMmD,EAAkBlT,IAAAA,OAASiT,GAC3BP,EAAa,CAAC,EAapB,OAVCA,EAAmB7R,OAASiS,EAAOvU,KAAKD,GAAeuU,OAAOK,EAAgB5U,MAG9EoU,EAAmBS,SAAWnT,IAAAA,MAAQiT,EAAyB5L,QAG/DqL,EAAmBU,SAAWH,EAAyB1U,KAAK8U,GAC1DA,EAAwBC,UAAU,EAAG,MAGjCZ,CAAU,EAuBFa,CACX1F,EAAStP,KAAKuP,GACZ2E,EAAe3E,GAAS/P,GAAO0U,EAAe3E,GAAS/P,GAAKF,MAAQkS,SAGnE,CACL,IAAIyD,EAAWX,OAAOY,iBACtB,MAAM5S,EAASgN,EAAStP,KAAKuP,IAC3B,GAAI2E,EAAe3E,GAAS/P,GAAM,CAChC,MAAM,MAAEF,GAAU4U,EAAe3E,GAAS/P,GACpC2V,EAAeb,OAAOhV,GAE5B,OADI2V,EAAWE,IAAcF,EAAWE,GACjCA,CACT,CACA,OAAO3D,CAAY,IAKpB2C,EAAmB7R,OAASA,EAAOtC,KAAKV,GACnCA,IAAUkS,EAAqByD,EAAW,IACvC3V,IAKR6U,EAAmBiB,WAAa,KACnC,CACA,MAAO,CACLrV,MAAOP,KACJ2U,EACJ,EAGG/V,EAAS,CACb8P,KAAM,CACJvC,MAAO,SAcX,OAAewD,EAAAA,EAAAA,KAVSC,CAAChI,EAAYiI,KACnC,MAAM,SAAEC,EAAQ,UAAE7Q,EAAS,WAAEC,GAAe2Q,GACtC,uBAAEgG,EAAsB,gBAAEC,GAAoBlO,EAAMmO,SAK1D,MAAO,CAAEzC,gBAJerU,EAAUuB,KAAKwV,GAAkBvB,EAAgBuB,EAAUlG,EAAUgG,KAInEvC,iBAHDrU,EAAWsB,KAAKyV,GACvCxB,EAAgBwB,EAAWnG,EAAU+F,KAEK,GAG9C,CAAwC5D,G,iCCjOjC,SAASiE,EAA+BpX,GAQpC,IARqC,UAC9CG,EAAS,WACTC,EAAU,kBACViX,EAAiB,mBACjBC,EAAkB,yBAClBC,EAAwB,0BACxBC,EAAyB,iBACzBC,GACMzX,EACN,OACEO,EAAAA,EAAAA,IAAA,OAAKG,IAAKZ,EAAO2J,QAAQnI,SAAA,EACvBC,EAAAA,EAAAA,GAAA,OAAAD,UACEC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,mBAInBU,EAAAA,EAAAA,GAACmW,EAAAA,IAAY,CACXlI,KAAK,WACL9O,IAAKZ,EAAOgQ,OACZnP,aACEY,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,6BAInBG,MAAOqW,EACPnX,SAAUqX,EAAyBjW,SAElCnB,EAAUuB,KAAKR,IACdK,EAAAA,EAAAA,GAACmW,EAAAA,IAAaC,OAAM,CAAC3W,MAAOE,EAAII,SAC7BJ,GADmCA,QAK1CK,EAAAA,EAAAA,GAAA,OAAKoS,MAAO,CAAEiE,UAAW,IAAKtW,UAC5BC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,gBAInBU,EAAAA,EAAAA,GAACmW,EAAAA,IAAY,CACXlI,KAAK,WACL9O,IAAKZ,EAAOgQ,OACZnP,aACEY,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,0BAInBG,MAAOsW,EACPpX,SAAUsX,EAA0BlW,SAEnClB,EAAWsB,KAAKR,IACfK,EAAAA,EAAAA,GAACmW,EAAAA,IAAaC,OAAM,CAAC3W,MAAOE,EAAII,SAC7BJ,GADmCA,QAK1CK,EAAAA,EAAAA,GAAA,OAAKoS,MAAO,CAAEiE,UAAW,IAAKtW,UAC5BC,EAAAA,EAAAA,GAACsW,EAAAA,EAAM,CACLpX,YAAY,+FACZ,eAAa,eACbqX,QAASL,EAAiBnW,UAE1BC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,oBAO3B,CAEA,MAAMf,EAAS,CACb2J,QAAU3D,IAAY,CACpB4H,QAAS,KAAK5H,EAAME,QAAQC,SAE9B6J,OAAQ,CAAEzC,MAAO,S,0BC7DZ,MAAM0K,UAAqCpP,EAAAA,UAGhDC,WAAAA,GAAA,SAAAC,WAAA,KACAC,MAAQ,CAGNuO,kBAAmBpO,KAAK3B,MAAM0Q,cAAclV,OAAOmV,MAAM,EAAG,GAG5DX,mBAAoBrO,KAAK3B,MAAM4Q,iBAAiBD,MAAM,EAAG,IACzD,KAEFV,yBAA4BY,IAC1BlP,KAAKC,SAAS,CAAEmO,kBAAmBc,GAAc,EACjD,KAEFX,0BAA6BY,IAC3BnP,KAAKC,SAAS,CAAEoO,mBAAoBc,GAAe,EACnD,KAEFX,iBAAmB,KACjBxO,KAAKC,SAAS,CAAEmO,kBAAmB,GAAIC,mBAAoB,IAAK,CAChE,CAEFhO,MAAAA,GACE,MAAM,SAAE0H,EAAQ,aAAEqH,EAAY,cAAEC,GAAkBrP,KAAK3B,OACjD,kBAAE+P,EAAiB,mBAAEC,GAAuBrO,KAAKH,MACvD,OACEvH,EAAAA,EAAAA,GAAC0L,EAAAA,EAAuB,CACtBC,UACE3L,EAAAA,EAAAA,GAAC6V,EAA+B,CAC9BjX,UAAWkY,EACXjY,WAAYkY,EACZjB,kBAAmBA,EACnBC,mBAAoBA,EACpBE,0BAA2BvO,KAAKuO,0BAChCD,yBAA0BtO,KAAKsO,yBAC/BE,iBAAkBxO,KAAKwO,mBAE1BnW,SAEC6B,IAAAA,QAAUkU,IAAuBlU,IAAAA,QAAUmU,IAQ3C/W,EAAAA,EAAAA,IAAA,OAAKG,IAAKZ,EAAOyY,iBAAkB,cAAY,qBAAoBjX,SAAA,EACjEC,EAAAA,EAAAA,GAACoE,EAAAA,EAAW6S,MAAK,CAACC,MAAO,EAAEnX,UACzBC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,2BAKnBU,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,2EAhBnBU,EAAAA,EAAAA,GAAC4R,EAA2B,CAC1BnC,SAAUA,EACV7Q,UAAWkX,EACXjX,WAAYkX,KAqBtB,EAGK,MA+BDxX,EAAS,CACbyY,iBAAmBzS,IAAU,CAC3B4H,QAAS5H,EAAME,QAAQ0S,GACvBC,UAAW,YAKf,OAAe9H,EAAAA,EAAAA,KA3BSC,CAAChI,EAAYiI,KACnC,MAAQC,SAAU4H,GAAgB7H,EAG5BC,GAAuB,OAAX4H,QAAW,IAAXA,EAAAA,EAAe,IAAI1Q,QAAQ2Q,IAAiB3H,EAAAA,EAAAA,IAAW2H,EAAM/P,KACzEuP,GAAeS,EAAAA,EAAAA,IAA0B9H,EAAUlI,GACnDwP,GAAgBS,EAAAA,EAAAA,IAA2B/H,EAAUlI,GACrDoP,GAAmBc,EAAAA,EAAAA,IAA8BhI,EAAUlI,IAC3D,gBAAEkO,GAAoBlO,EAAMmO,SAC5Be,EArBqBiB,EAACZ,EAAmBrH,EAAegG,KAC9D,MAAMgB,EAAqB,GAQ3B,OAPAK,EAAanV,SAASgW,IAEFlI,EAAStP,KACxBuP,GAAiB+F,EAAgB/F,GAASiI,IAAUlC,EAAgB/F,GAASiI,GAAOlY,QAExEkR,OAAM,CAAC3O,EAAQuL,EAAQqK,IAAa5V,IAAM4V,EAAI,MAAKnB,EAAcpU,KAAKsV,EAAM,IAEtFlB,CAAa,EAYEiB,CAAcZ,EAAcrH,EAAUgG,GAE5D,MAAO,CACLqB,eACAC,gBACAJ,mBACAF,gBACD,GAWH,CAAwCD,E,6FCxIxC,MAAMqB,GAAAA,EACGC,eAAiB,CACtBC,eAAgB,iBAChBC,YAAa,cACbC,cAAe,gBACfC,aAAc,gBAOX,MAAMC,EAAmCA,CAACC,EAA4BC,KAC3E,KAAMD,aAAwBE,EAAAA,GAC5B,OAEF,MAAM,OAAEC,GAAWH,EACnB,IAAIjT,EACJ,MAAMqT,EAAsB,CAAED,UAC1BH,EAAa9H,iBAAmBC,EAAAA,GAAWC,0BAC7CrL,EAAQ,IAAIsT,EAAAA,GAAcD,IAExBJ,EAAa9H,iBAAmBC,EAAAA,GAAWc,oBAC7ClM,EAAQ,IAAIuT,EAAAA,GAAgBF,IAE1BJ,EAAa9H,iBAAmBC,EAAAA,GAAWoI,iBAC7CxT,EAAQ,IAAIyT,EAAAA,GAAoBJ,IAE9BJ,EAAa9H,iBAAmBC,EAAAA,GAAWsI,0BAC7C1T,EAAQ,IAAI2T,EAAAA,GAAgBN,IAI9B,MAAMO,EAA0BX,EAAaY,kBAK7C,OAJI7T,GAAS4T,IACX5T,EAAM2C,QAAUiR,GAGX5T,CAAK,EAEd,K,8LCbO,SAAS8T,EAAYxa,GAA+B,IAA9B,KAAEya,GAAyBza,EACtD,MAAM0a,GACJnZ,EAAAA,EAAAA,GAACoZ,EAAAA,IAAI,CAAArZ,SAEFmZ,EAAK/Y,KAAI9B,IAAA,IAAC,GAAEK,EAAE,SAAE2a,EAAQ,QAAE9C,EAAO,KAAEpO,KAASmR,GAAYjb,EAAA,OAEvD2B,EAAAA,EAAAA,GAACoZ,EAAAA,IAAKG,KAAI,CAAUhD,QAASA,EAASpO,KAAMA,EAAM,eAAczJ,KAAQ4a,EAAUvZ,SAC/EsZ,GADa3a,EAEJ,MAMlB,OAAOwa,EAAKjQ,OAAS,GACnBjJ,EAAAA,EAAAA,GAACwZ,EAAAA,IAAQ,CAACC,QAASN,EAAcO,QAAS,CAAC,SAAUC,UAAU,aAAaC,OAAK,EAAA7Z,UAC/EC,EAAAA,EAAAA,GAACsW,EAAAA,EAAM,CACLpX,YAAY,kEACZ2a,MAAM7Z,EAAAA,EAAAA,GAAC8Z,EAAAA,IAAY,IACnB,eAAa,wBACb,aAAW,gCAGb,IACN,CAAC,IAAAva,EAAA,CAAAjB,KAAA,UAAAC,OAAA,iBAsBM,SAASwb,EAAWhU,GACzB,MAAM,MACJnC,EAAK,YACLoW,EAAc,GAAE,YAChBC,EAAc,GAAE,QAChBC,EAAO,SACPna,EAAQ,WACRoa,EAAU,WACVC,GAAa,EAAK,4BAClBC,GACEtU,GACE,MAAExB,IAAU+G,EAAAA,EAAAA,MACLvM,EAAAA,EAAAA,KAEb,OACEC,EAAAA,EAAAA,IAAA6O,EAAAA,GAAA,CAAA9N,SAAA,EACEC,EAAAA,EAAAA,GAACsa,EAAAA,IAAM,CACLN,YACEA,EAAY/Q,OAAS,IACnBjJ,EAAAA,EAAAA,GAACua,EAAAA,IAAU,CAACC,sBAAoB,EAAAza,SAC7Bia,EAAY7Z,KAAI,CAACwJ,EAAG4D,KACnBvN,EAAAA,EAAAA,GAACua,EAAAA,IAAWhB,KAAI,CAAAxZ,SAAU4J,GAAJ4D,OAK9BkN,QAAS1a,EACT6D,MAAOA,EAEPqW,aACEjb,EAAAA,EAAAA,IAAA6O,EAAAA,GAAA,CAAA9N,SAAA,CACGma,IAAWla,EAAAA,EAAAA,GAACqL,EAAAA,EAAY,CAAClM,IAAGI,IAC5B0a,KAGLI,4BAA6BA,KAE/Bra,EAAAA,EAAAA,GAACyM,EAAAA,EACC,CACAtN,KAAGqM,EAAAA,EAAAA,IAAE,CAEHwB,WAAY,KACRoN,EAAa,CAAEnO,QAAS,QAAW,CAAC,GACzC,IACD3H,KAAM6V,MAId,C,wGCnHA,MAAMO,EAAOtT,EAAAA,MAAW,IAAM,uCAEjB9D,EAAW7E,IAAA,IAAC,SAAEiH,KAAaK,GAAYtH,EAAA,OAClDuB,EAAAA,EAAAA,GAACmH,EAAAA,EAAoB,CAAApH,UACnBC,EAAAA,EAAAA,GAACoH,EAAAA,SAAc,CAAC1B,SAAkB,OAARA,QAAQ,IAARA,EAAAA,GAAY1F,EAAAA,EAAAA,GAAC2a,EAAAA,IAAc,CAAC/J,QAAM,IAAI7Q,UAC9DC,EAAAA,EAAAA,GAAC0a,EAAI,IAAK3U,OAES,C,0KCM2C,IAAAxG,EAAA,CAAAjB,KAAA,UAAAC,OAAA,cAW7D,MAAMqc,UAA0B/U,EAAAA,UAQrCwB,WAAAA,CAAYtB,GAMV,GALAoH,MAAMpH,GAAO,KAJflH,gBAAU,OACVD,eAAS,EAKP8I,KAAK7I,WAAamH,EAAAA,EAAeG,QAAQuB,KAAK3B,MAAMrF,aAAa,GACjEgH,KAAK9I,UAAYoH,EAAAA,EAAeG,QAAQuB,KAAK3B,MAAMpF,YAAY,GAE3D+G,KAAK9I,UAAUqK,OAASvB,KAAK7I,WAAWoK,OAAS,EACnDvB,KAAKH,MAAQ,CAAE6F,UAAU,OACpB,CACL,MAAMyN,EAAS,CAAEzN,UAAU,EAAO0N,cAAc,GACjB,IAA3BpT,KAAK7I,WAAWoK,OAClBvB,KAAKH,MAAQ,IACRsT,EACHlX,MAAO,CAAEhE,IAAK+H,KAAK9I,UAAU,GAAIyO,UAAU,GAC3CxJ,MAAO,CAAElE,IAAK+H,KAAK9I,UAAU,GAAIyO,UAAU,GAC3C0N,MAAO,CAAEpb,IAAK+H,KAAK9I,UAAU,GAAIyO,UAAU,IAEV,IAA1B3F,KAAK9I,UAAUqK,OACxBvB,KAAKH,MAAQ,IACRsT,EACHlX,MAAO,CAAEhE,IAAK+H,KAAK7I,WAAW,GAAIwO,UAAU,GAC5CxJ,MAAO,CAAElE,IAAK+H,KAAK7I,WAAW,GAAIwO,UAAU,GAC5C0N,MAAO,CAAEpb,IAAK+H,KAAK7I,WAAW,GAAIwO,UAAU,IAEX,IAA1B3F,KAAK9I,UAAUqK,OACxBvB,KAAKH,MAAQ,IACRsT,EACHlX,MAAO,CAAEhE,IAAK+H,KAAK9I,UAAU,GAAIyO,UAAU,GAC3CxJ,MAAO,CAAElE,IAAK+H,KAAK7I,WAAW,GAAIwO,UAAU,GAC5C0N,MAAO,CAAEpb,IAAK+H,KAAK7I,WAAW,GAAIwO,UAAU,IAG9C3F,KAAKH,MAAQ,IACRsT,EACHlX,MAAO,CAAEhE,IAAK+H,KAAK9I,UAAU,GAAIyO,UAAU,GAC3CxJ,MAAO,CAAElE,IAAK+H,KAAK9I,UAAU,GAAIyO,UAAU,GAC3C0N,MAAO,CAAEpb,IAAK+H,KAAK7I,WAAW,GAAIwO,UAAU,GAGlD,CACF,CAKAC,QAAAA,CAASC,EAAM9O,GAA2B,IAAzB,IAAEkB,EAAG,SAAE0N,GAAe5O,EACrC,MAAMgB,EAAQuG,EAAAA,EAAeC,YAAYoH,EAAW3F,KAAK3B,MAAMrF,YAAcgH,KAAK3B,MAAMpF,YAAY4M,GAAI5N,GACxG,YAAiBoB,IAAVtB,EAAsBA,EAASA,EAAcA,KACtD,CAEAub,aAAAA,GASE,MAAMpH,EAAa,CACjB,CAAC,EAAG,iBACJ,CAAC,IAAM,kBACP,CAAC,GAAK,mBACN,CAAC,GAAK,mBACN,CAAC,GAAK,oBACN,CAAC,EAAG,qBAIN,OAAIlM,KAAKH,MAAMuT,aACNlH,EAGAA,EAAWzT,KAAI,CAAA9B,EAAQwD,KAAK,IAAXoZ,GAAI5c,EAAA,MAAY,CAAC4c,EAAKrH,EAAWA,EAAW3K,OAAS,EAAIpH,GAAO,GAAG,GAE/F,CAEAkG,MAAAA,GAEE,GAAIL,KAAKH,MAAM6F,SACb,OACEpN,EAAAA,EAAAA,GAAA,OAAAD,UACEC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,uMAUvB,MAAMkO,EAAYoN,EAAkBnN,oBAE9B/I,EAAU,GACVgJ,EAAU,GACVwN,EAAU,GACVvN,EAAgB,GAEtBjG,KAAK3B,MAAMtF,SAASkB,SAAQ,CAACC,EAAGC,KAE9B,MAAMG,EAAI0F,KAAK4F,SAASzL,EAAO6F,KAAKH,MAAM5D,OAEpCxB,EAAIuF,KAAK4F,SAASzL,EAAO6F,KAAKH,MAAM1D,OAEpCsX,EAAIzT,KAAK4F,SAASzL,EAAO6F,KAAKH,MAAMwT,YAChCha,IAANiB,QAAyBjB,IAANoB,QAAyBpB,IAANoa,IAG1CzW,EAAGrC,KAAKqE,WAAW1E,IACnB0L,EAAGrL,KAAKqE,WAAWvE,IACnB+Y,EAAG7Y,KAAKqE,WAAWyU,IACnBxN,EAAStL,KAAKqF,KAAKkG,iBAAiB/L,IAAO,IAoF7C,OACE7B,EAAAA,EAAAA,GAAC0L,EAAAA,EAAuB,CACtBC,UACE3M,EAAAA,EAAAA,IAAA6O,EAAAA,GAAA,CAAA9N,SAAA,EACEf,EAAAA,EAAAA,IAAA,OAAAe,SAAA,EACEC,EAAAA,EAAAA,GAACkD,EAAAA,IAAOC,MAAK,CAACC,QAAQ,iBAAgBrD,UACpCC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,cAIlBoI,KAAKoG,aAAa,aAErB9N,EAAAA,EAAAA,GAACyM,EAAAA,EAAM,KACPzN,EAAAA,EAAAA,IAAA,OAAAe,SAAA,EACEC,EAAAA,EAAAA,GAACkD,EAAAA,IAAOC,MAAK,CAACC,QAAQ,iBAAgBrD,UACpCC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,cAIlBoI,KAAKoG,aAAa,aAErB9N,EAAAA,EAAAA,GAACyM,EAAAA,EAAM,KACPzN,EAAAA,EAAAA,IAAA,OAAAe,SAAA,EACEC,EAAAA,EAAAA,GAACkD,EAAAA,IAAOC,MAAK,CAACC,QAAQ,iBAAgBrD,UACpCC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,cAIlBoI,KAAKoG,aAAa,aAErB9N,EAAAA,EAAAA,GAACyM,EAAAA,EAAM,KACPzN,EAAAA,EAAAA,IAAA,OAAKgJ,UAAU,iBAAgBjI,SAAA,EAC7BC,EAAAA,EAAAA,GAACqD,EAAAA,EAAgB,CAAA3E,GAAA,SACfY,eAAe,mBAGd,KACHU,EAAAA,EAAAA,GAACob,EAAAA,IAAM,CACLlc,YAAY,kFACZ8I,UAAU,oBAEVqT,QAAS3T,KAAKH,MAAMuT,aACpBnc,SAAW0c,GAAY3T,KAAKC,SAAS,CAAEmT,aAAcO,YAI5Dtb,SAlImBub,MACtB,MAAMC,EAAc,GAOpB,GANI,IAAIna,IAAIsD,GAAIJ,KAAO,GACrBiX,EAAYlZ,KAAK,KAEf,IAAIjB,IAAIsM,GAAIpJ,KAAO,GACrBiX,EAAYlZ,KAAK,KAEfkZ,EAAYtS,OAAS,EAAG,CAC1B,MAAMuS,EACJD,EAAYtS,OAAS,EAAI,OAAOsS,EAAY7M,KAAK,sBAAwB,OAAO6M,EAAY,kBAC9F,OACEvb,EAAAA,EAAAA,GAAA,OACEb,IAAKZ,EAAOkd,cAAc1b,SAC1B,GAAGyb,gEAET,CAEA,OACExb,EAAAA,EAAAA,GAACsD,EAAAA,EAAQ,CACPnE,IAAKZ,EAAO8P,KACZ3M,KAAM,CAEJ,CACEyZ,EAAGD,EACHlZ,EAAG0C,EACHvC,EAAGuL,EACHhL,KAAM,UACNsL,UAAW,OACX4F,WAAYlM,KAAKsT,gBACjBU,aAAa,EACbC,SAAU,CACRC,SAAU,YAId,CACE5Z,EAAG0C,EACHvC,EAAGuL,EACHK,KAAMJ,EACNK,UAAW,OACXtL,KAAM,YACNuL,KAAM,UACNC,OAAQ,CACN5J,KAAM,GACNmH,MAAO,6BAIblI,OAAQ,CACNC,OAAQ,CACNC,EAAG,IAELC,UAAW,UACXC,MAAO,CACLC,OAAOuK,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAe1G,KAAKH,MAAa,MAAE5H,IAAK6N,IAC5DqO,MAAO,CAACC,KAAKC,OAAOrX,GAAKoX,KAAKE,OAAOtX,KAEvCb,MAAO,CACLD,OAAOuK,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAe1G,KAAKH,MAAa,MAAE5H,IAAK6N,IAC5DqO,MAAO,CAACC,KAAKC,OAAOrO,GAAKoO,KAAKE,OAAOtO,MAGzC5J,OAAQ,CACNC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,uBAAwB,CACtB,kBACA,WACA,UACA,eACA,wBACA,0BAGJC,kBAAgB,GAChB,EAuDDmX,IAGP,CAEAxN,YAAAA,CAAaQ,GACX,OACEtP,EAAAA,EAAAA,IAACC,EAAAA,IAAY,CACXC,YAAY,kFACZC,IAAGI,EACHb,GAAI4P,EAAO,YACX3P,SAAU6B,IAAiB,IAAhB,OAAEhB,GAAQgB,EACnB,MAAM,MAAEf,GAAUD,GACXgP,KAAWC,GAAYhP,EAAMG,MAAM,KACpCD,EAAM8O,EAASC,KAAK,KACpBrB,EAAsB,WAAXmB,EACjB9G,KAAKC,SAAS,CAAE,CAAC2G,GAAO,CAAEjB,WAAU1N,QAAQ,EAE9CF,OAAQiI,KAAKH,MAAM+G,GAAMjB,SAAW,UAAY,UAAY3F,KAAKH,MAAM+G,GAAM3O,IAAII,SAAA,EAEjFC,EAAAA,EAAAA,GAACC,EAAAA,IAAuB,CAACC,MAAM,YAAWH,SACvC2H,KAAK9I,UAAUuB,KAAKwO,IACnB3O,EAAAA,EAAAA,GAACI,EAAAA,IAAkB,CAAoBX,MAAO,SAAWkP,EAAE5O,SACxD4O,GADsB,SAAWA,QAKxC3O,EAAAA,EAAAA,GAACC,EAAAA,IAAuB,CAACC,MAAM,SAAQH,SACpC2H,KAAK7I,WAAWsB,KAAKyO,IACpB5O,EAAAA,EAAAA,GAACI,EAAAA,IAAkB,CAAqBX,MAAO,UAAYmP,EAAE7O,SAC1D6O,GADsB,UAAYA,SAO/C,CAEAhB,gBAAAA,CAAiB/L,GACf,MAAM2L,EAAYoN,EAAkBnN,oBAC9BoB,EAAc+L,EAAkB9L,sBAChCC,EAAUrH,KAAK3B,MAAMiJ,gBAAgBnN,GAC3C,IAAIoN,EAAS,OAAMd,EAAAA,EAAAA,QAAOY,aAC1B,MAAMG,EAAYxH,KAAK3B,MAAMpF,WAAWkB,GACxCqN,EAAUvN,SAASgN,IACjBM,IACEd,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAeO,EAAEhP,IAAK6N,IACnC,MACAW,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAeO,EAAElP,MAAOoP,IACrC,MAAM,IAEV,MAAMM,EAAazH,KAAK3B,MAAMrF,YAAYmB,GAO1C,OANIsN,EAAWlG,OAAS,IACtBgG,GAAUC,EAAUjG,OAAS,EAAI,OAAS,GAC1CkG,EAAWxN,SAASiN,IAClBK,IAAUd,EAAAA,EAAAA,QAAO/F,EAAAA,EAAMgG,eAAeQ,EAAEjP,IAAK6N,IAAc,KAAOpF,EAAAA,EAAMgH,aAAaR,EAAEnP,OAAS,MAAM,KAGnGwP,CACT,EA3TW2L,EAEJnN,oBAAsB,GAFlBmN,EAGJ9L,sBAAwB,GA2TjC,MAAMvQ,EAAS,CACb8P,KAAM,CACJvC,MAAO,QAET2P,cAAgBlX,IAAU,CACxB4H,QAAS5H,EAAME,QAAQwX,GACvBhQ,QAAS,OACTiQ,eAAgB,YAiBpB,KAAe5M,EAAAA,EAAAA,KAbSC,CAAChI,EAAYiI,KACnC,MAAM/O,EAAgB,GAChBC,EAAmB,GACnBC,EAAkB,IAClB,SAAE8O,GAAaD,EAMrB,OALAC,EAAS9N,SAAS+N,IAChBjP,EAAS4B,MAAKsN,EAAAA,EAAAA,IAAWD,EAASnI,IAClC7G,EAAY2B,KAAKC,OAAOG,QAAOmN,EAAAA,EAAAA,IAAiBF,EAASnI,KACzD5G,EAAW0B,KAAKC,OAAOG,QAAOoN,EAAAA,EAAAA,IAAUH,EAASnI,IAAQ,IAEpD,CAAE9G,WAAUC,cAAaC,aAAY,GAG9C,CAAwCia,E,6FClXjC,SAASpJ,EAAO/S,GAA8B,IAA7B,gBAAE0d,GAAwB1d,EAChD,OACEuB,EAAAA,EAAAA,GAAA,OAAKb,IAAMoF,GAAUhG,EAAO6d,QAAQ7X,EAAO4X,GAAiBpc,UAC1DC,EAAAA,EAAAA,GAAA,OAAKqc,IAAI,kBAAkBC,IAAKF,KAGtC,CAEA,MAAM7d,EAAS,CACb6d,QAASA,CAAC7X,EAAcgY,KAAmB,CACzCzQ,MAAO,IACPuK,UAAW,IACX/N,WAAY,OACZkU,YAAa,OAEbC,IAAK,CACHC,SAAU,WACVC,QAAS,EACTC,IAAK,MACLC,KAAM,MACN/Q,MAAkC,EAA3BvH,EAAMuY,QAAQC,WACrBhR,OAAmC,EAA3BxH,EAAMuY,QAAQC,WACtB1G,WAAY9R,EAAMuY,QAAQC,WAC1BzU,YAAa/D,EAAMuY,QAAQC,WAC3BC,UAAW,GAAGC,EAAAA,EAAS;;;;;;;;;iCAUvBC,eAAgBX,EAAY,KAAO,U", "sources": ["experiment-tracking/components/CompareRunBox.tsx", "common/utils/withErrorBoundary.tsx", "experiment-tracking/components/CompareRunUtil.ts", "common/utils/withRouterNext.tsx", "common/components/error-boundaries/SectionErrorBoundary.tsx", "../node_modules/react-error-boundary/dist/src/ErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/ErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/assertErrorBoundaryContext.ts", "../node_modules/react-error-boundary/dist/src/useErrorBoundary.ts", "../node_modules/react-error-boundary/dist/src/withErrorBoundary.ts", "shared/building_blocks/PreviewBadge.tsx", "experiment-tracking/components/CompareRunPlotContainer.tsx", "common/components/PageContainer.tsx", "experiment-tracking/components/CompareRunScatter.tsx", "common/components/RequestStateWrapper.tsx", "experiment-tracking/components/ParallelCoordinatesPlotView.tsx", "experiment-tracking/components/ParallelCoordinatesPlotControls.tsx", "experiment-tracking/components/ParallelCoordinatesPlotPanel.tsx", "common/utils/ErrorUtils.tsx", "shared/building_blocks/PageHeader.tsx", "experiment-tracking/components/LazyPlot.tsx", "experiment-tracking/components/CompareRunContour.tsx", "common/components/Spinner.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { FormattedMessage, useIntl } from 'react-intl';\n\nimport {\n  Typography,\n  Row,\n  Col,\n  SimpleSelect,\n  SimpleSelectOptionGroup,\n  SimpleSelectOption,\n  FormUI,\n} from '@databricks/design-system';\nimport { LazyPlot } from './LazyPlot';\nimport { RunInfoEntity } from '../types';\n\ntype Props = {\n  runUuids: string[];\n  runInfos: RunInfoEntity[];\n  metricLists: any[][];\n  paramLists: any[][];\n};\n\ntype Axis = {\n  key?: string;\n  isParam?: boolean;\n};\n\nconst paramOptionPrefix = 'param-';\nconst metricOptionPrefix = 'metric-';\n\n// Note: This component does not pass the value of the parent component to the child component.\n// Doing so will cause weird rendering issues with the label and updating of the value.\nconst Selector = ({\n  id,\n  onChange,\n  paramKeys,\n  metricKeys,\n}: {\n  id: string;\n  onChange: (axis: Axis) => void;\n  paramKeys: string[];\n  metricKeys: string[];\n}) => {\n  const intl = useIntl();\n  return (\n    <SimpleSelect\n      componentId=\"codegen_mlflow_app_src_experiment-tracking_components_comparerunbox.tsx_46\"\n      id={id}\n      css={{ width: '100%', marginBottom: '16px' }}\n      placeholder={intl.formatMessage({\n        defaultMessage: 'Select parameter or metric',\n        description: 'Placeholder text for parameter/metric selector in box plot comparison in MLflow',\n      })}\n      onChange={({ target }) => {\n        const { value } = target;\n        const [_prefix, key] = value.split('-');\n        const isParam = value.startsWith(paramOptionPrefix);\n        onChange({ key, isParam });\n      }}\n    >\n      <SimpleSelectOptionGroup label=\"Parameters\">\n        {paramKeys.map((key) => (\n          <SimpleSelectOption key={key} value={paramOptionPrefix + key}>\n            {key}\n          </SimpleSelectOption>\n        ))}\n      </SimpleSelectOptionGroup>\n      <SimpleSelectOptionGroup label=\"Metrics\">\n        {metricKeys.map((key) => (\n          <SimpleSelectOption key={key} value={metricOptionPrefix + key}>\n            {key}\n          </SimpleSelectOption>\n        ))}\n      </SimpleSelectOptionGroup>\n    </SimpleSelect>\n  );\n};\n\nexport const CompareRunBox = ({ runInfos, metricLists, paramLists }: Props) => {\n  const [xAxis, setXAxis] = useState<Axis>({ key: undefined, isParam: undefined });\n  const [yAxis, setYAxis] = useState<Axis>({ key: undefined, isParam: undefined });\n\n  const paramKeys = Array.from(new Set(paramLists.flat().map(({ key }) => key))).sort();\n  const metricKeys = Array.from(new Set(metricLists.flat().map(({ key }) => key))).sort();\n\n  const getBoxPlotData = () => {\n    const data = {};\n    runInfos.forEach((_, index) => {\n      const params = paramLists[index];\n      const metrics = metricLists[index];\n      const x = (xAxis.isParam ? params : metrics).find(({ key }) => key === xAxis.key);\n      const y = (yAxis.isParam ? params : metrics).find(({ key }) => key === yAxis.key);\n      if (x === undefined || y === undefined) {\n        return;\n      }\n\n      if (x.value in data) {\n        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n        data[x.value].push(y.value);\n      } else {\n        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n        data[x.value] = [y.value];\n      }\n    });\n\n    return Object.entries(data).map(([key, values]) => ({\n      y: values,\n      type: 'box',\n      name: key,\n      jitter: 0.3,\n      pointpos: -1.5,\n      boxpoints: 'all',\n    }));\n  };\n\n  const renderPlot = () => {\n    if (!(xAxis.key && yAxis.key)) {\n      return (\n        <div\n          css={{\n            display: 'flex',\n            width: '100%',\n            height: '100%',\n            justifyContent: 'center',\n            alignItems: 'center',\n          }}\n        >\n          <Typography.Text size=\"xl\">\n            <FormattedMessage\n              defaultMessage=\"Select parameters/metrics to plot.\"\n              description=\"Text to show when x or y axis is not selected on box plot\"\n            />\n          </Typography.Text>\n        </div>\n      );\n    }\n\n    return (\n      <LazyPlot\n        css={{\n          width: '100%',\n          height: '100%',\n          minHeight: '35vw',\n        }}\n        data={getBoxPlotData()}\n        layout={{\n          margin: {\n            t: 30,\n          },\n          hovermode: 'closest',\n          xaxis: {\n            title: xAxis.key,\n          },\n          yaxis: {\n            title: yAxis.key,\n          },\n        }}\n        config={{\n          responsive: true,\n          displaylogo: false,\n          scrollZoom: true,\n          modeBarButtonsToRemove: [\n            'sendDataToCloud',\n            'select2d',\n            'lasso2d',\n            'resetScale2d',\n            'hoverClosestCartesian',\n            'hoverCompareCartesian',\n          ],\n        }}\n        useResizeHandler\n      />\n    );\n  };\n\n  return (\n    <Row>\n      <Col span={6}>\n        <div css={styles.borderSpacer}>\n          <div>\n            <FormUI.Label htmlFor=\"x-axis-selector\">\n              <FormattedMessage\n                defaultMessage=\"X-axis:\"\n                description=\"Label text for X-axis in box plot comparison in MLflow\"\n              />\n            </FormUI.Label>\n          </div>\n          <Selector id=\"x-axis-selector\" onChange={setXAxis} paramKeys={paramKeys} metricKeys={metricKeys} />\n\n          <div>\n            <FormUI.Label htmlFor=\"y-axis-selector\">\n              <FormattedMessage\n                defaultMessage=\"Y-axis:\"\n                description=\"Label text for Y-axis in box plot comparison in MLflow\"\n              />\n            </FormUI.Label>\n          </div>\n          <Selector id=\"y-axis-selector\" onChange={setYAxis} paramKeys={paramKeys} metricKeys={metricKeys} />\n        </div>\n      </Col>\n      <Col span={18}>{renderPlot()}</Col>\n    </Row>\n  );\n};\n\nconst styles = {\n  borderSpacer: (theme: any) => ({\n    paddingLeft: theme.spacing.xs,\n  }),\n};\n", "import React from 'react';\nimport { ErrorBoundary, ErrorBoundaryPropsWithComponent, FallbackProps } from 'react-error-boundary';\nimport ErrorUtils from './ErrorUtils';\nimport { DangerIcon, Empty } from '@databricks/design-system';\nimport { FormattedMessage } from 'react-intl';\n\nexport type ErrorBoundaryProps = {\n  children: React.Component;\n  customFallbackComponent?: ErrorBoundaryPropsWithComponent['FallbackComponent'];\n};\n\nfunction ErrorFallback() {\n  return (\n    <Empty\n      data-testid=\"fallback\"\n      title={<FormattedMessage defaultMessage=\"Error\" description=\"Title of editor error fallback component\" />}\n      description={\n        <FormattedMessage\n          defaultMessage=\"An error occurred while rendering this component.\"\n          description=\"Description of error fallback component\"\n        />\n      }\n      image={<DangerIcon />}\n    />\n  );\n}\n\nfunction CustomErrorBoundary({ children, customFallbackComponent }: React.PropsWithChildren<ErrorBoundaryProps>) {\n  function logErrorToConsole(error: Error, info: { componentStack: string }) {\n    // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n    console.error('Caught Unexpected Error: ', error, info.componentStack);\n  }\n\n  if (customFallbackComponent) {\n    return (\n      <ErrorBoundary onError={logErrorToConsole} FallbackComponent={customFallbackComponent}>\n        {children}\n      </ErrorBoundary>\n    );\n  }\n\n  return (\n    <ErrorBoundary onError={logErrorToConsole} fallback={<ErrorFallback />}>\n      {children}\n    </ErrorBoundary>\n  );\n}\n\nexport function withErrorBoundary<P>(\n  service: string,\n  Component: React.ComponentType<P>,\n  errorMessage?: React.ReactNode,\n  customFallbackComponent?: React.ComponentType<FallbackProps>,\n): React.ComponentType<P> {\n  return function CustomErrorBoundaryWrapper(props: P) {\n    return (\n      <CustomErrorBoundary customFallbackComponent={customFallbackComponent}>\n        {/* @ts-expect-error Generics don't play well with WithConditionalCSSProp type coming @emotion/react jsx typing to validate css= prop values typing. More details here: emotion-js/emotion#2169 */}\n        <Component {...props} />\n      </CustomErrorBoundary>\n    );\n  };\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nexport default class CompareRunUtil {\n  /**\n   * Find in a list of metrics/params a certain key\n   */\n  static findInList(data: any, key: any) {\n    let found = undefined;\n    data.forEach((value: any) => {\n      if (value.key === key) {\n        found = value;\n      }\n    });\n    return found;\n  }\n\n  /**\n   * Get all keys present in the data in ParamLists or MetricLists or Schema input and outputs lists\n   */\n  static getKeys(lists: any, numeric: any) {\n    const keys = {};\n    lists.forEach((list: any) =>\n      list.forEach((item: any) => {\n        if (!(item.key in keys)) {\n          // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n          keys[item.key] = true;\n        }\n        if (numeric && isNaN(parseFloat(item.value))) {\n          // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n          keys[item.key] = false;\n        }\n      }),\n    );\n    return (\n      Object.keys(keys)\n        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message\n        .filter((k) => keys[k])\n        .sort()\n    );\n  }\n}\n", "import React from 'react';\n\nimport {\n  type Location,\n  type Params as RouterDOMParams,\n  type NavigateOptions,\n  type To,\n  useLocation,\n  useNavigate,\n  useParams,\n} from './RoutingUtils';\n\nexport interface WithRouterNextProps<Params extends RouterDOMParams = RouterDOMParams> {\n  navigate: ReturnType<typeof useNavigate>;\n  location: Location;\n  params: Params;\n}\n\n/**\n * This HoC serves as a retrofit for class components enabling them to use\n * react-router v6's location, navigate and params being injected via props.\n */\nexport const withRouterNext =\n  <\n    T,\n    Props extends JSX.IntrinsicAttributes &\n      JSX.LibraryManagedAttributes<React.ComponentType<T>, React.PropsWithChildren<T>>,\n    Params extends RouterDOMParams = RouterDOMParams,\n  >(\n    Component: React.ComponentType<T>,\n  ) =>\n  (\n    props: Omit<\n      Props,\n      | 'location'\n      | 'navigate'\n      | 'params'\n      | 'navigationType'\n      /* prettier-ignore*/\n    >,\n  ) => {\n    const location = useLocation();\n    const navigate = useNavigate();\n    const params = useParams<Params>();\n\n    return (\n      <Component\n        /* prettier-ignore */\n        params={params as Params}\n        location={location}\n        navigate={navigate}\n        {...(props as Props)}\n      />\n    );\n  };\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport Utils from '../../utils/Utils';\n\ntype Props = {\n  showServerError?: boolean;\n};\n\ntype State = any;\n\nexport class SectionErrorBoundary extends React.Component<Props, State> {\n  state = { error: null };\n\n  componentDidCatch(error: any, errorInfo: any) {\n    this.setState({ error });\n    // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n    console.error(error, errorInfo);\n  }\n\n  renderErrorMessage(error: any) {\n    return this.props.showServerError ? <div>Error message: {error.message}</div> : '';\n  }\n\n  render() {\n    const { children } = this.props;\n    const { error } = this.state;\n    if (error) {\n      return (\n        <div>\n          <p>\n            <i data-testid=\"icon-fail\" className=\"fa fa-exclamation-triangle icon-fail\" css={classNames.wrapper} />\n            <span> Something went wrong with this section. </span>\n            <span>If this error persists, please report an issue </span>\n            {/* Reported during ESLint upgrade */}\n            {/* eslint-disable-next-line react/jsx-no-target-blank */}\n            <a href={Utils.getSupportPageUrl()} target=\"_blank\">\n              here\n            </a>\n            .{this.renderErrorMessage(error)}\n          </p>\n        </div>\n      );\n    }\n\n    return children;\n  }\n}\n\nconst classNames = {\n  wrapper: {\n    marginLeft: -2, // to align the failure icon with the collapsable section caret toggle\n  },\n};\n", "import { createContext } from \"react\";\n\nexport type ErrorBoundaryContextType = {\n  didCatch: boolean;\n  error: any;\n  resetErrorBoundary: (...args: any[]) => void;\n};\n\nexport const ErrorBoundaryContext =\n  createContext<ErrorBoundaryContextType | null>(null);\n", "import {\n  Component,\n  createElement,\n  ErrorInfo,\n  isValidElement,\n  PropsWithChildren,\n  PropsWithRef,\n  ReactElement,\n} from \"react\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\nimport { ErrorBoundaryProps, FallbackProps } from \"./types\";\n\ntype ErrorBoundaryState = { didCatch: boolean; error: any };\n\nconst initialState: ErrorBoundaryState = {\n  didCatch: false,\n  error: null,\n};\n\nexport class ErrorBoundary extends Component<\n  PropsWithRef<PropsWithChildren<ErrorBoundaryProps>>,\n  ErrorBoundaryState\n> {\n  state = initialState;\n\n  static getDerivedStateFromError(error: Error) {\n    return { didCatch: true, error };\n  }\n\n  resetErrorBoundary = (...args: any[]) => {\n    const { error } = this.state;\n\n    if (error !== null) {\n      this.props.onReset?.({\n        args,\n        reason: \"imperative-api\",\n      });\n\n      this.setState(initialState);\n    }\n  };\n\n  componentDidCatch(error: Error, info: ErrorInfo) {\n    this.props.onError?.(error, info);\n  }\n\n  componentDidUpdate(\n    prevProps: ErrorBoundaryProps,\n    prevState: ErrorBoundaryState\n  ) {\n    const { didCatch } = this.state;\n    const { resetKeys } = this.props;\n\n    // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n    // we'd end up resetting the error boundary immediately.\n    // This would likely trigger a second error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n\n    if (\n      didCatch &&\n      prevState.error !== null &&\n      hasArrayChanged(prevProps.resetKeys, resetKeys)\n    ) {\n      this.props.onReset?.({\n        next: resetKeys,\n        prev: prevProps.resetKeys,\n        reason: \"keys\",\n      });\n\n      this.setState(initialState);\n    }\n  }\n\n  render() {\n    const { children, fallbackRender, FallbackComponent, fallback } =\n      this.props;\n    const { didCatch, error } = this.state;\n\n    let childToRender = children;\n\n    if (didCatch) {\n      const props: FallbackProps = {\n        error,\n        resetErrorBoundary: this.resetErrorBoundary,\n      };\n\n      if (isValidElement(fallback)) {\n        childToRender = fallback;\n      } else if (typeof fallbackRender === \"function\") {\n        childToRender = fallbackRender(props);\n      } else if (FallbackComponent) {\n        childToRender = createElement(FallbackComponent, props);\n      } else {\n        throw new Error(\n          \"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\"\n        );\n      }\n    }\n\n    return createElement(\n      ErrorBoundaryContext.Provider,\n      {\n        value: {\n          didCatch,\n          error,\n          resetErrorBoundary: this.resetErrorBoundary,\n        },\n      },\n      childToRender\n    ) as ReactElement;\n  }\n}\n\nfunction hasArrayChanged(a: any[] = [], b: any[] = []) {\n  return (\n    a.length !== b.length || a.some((item, index) => !Object.is(item, b[index]))\n  );\n}\n", "import { ErrorBoundaryContextType } from \"./ErrorBoundaryContext\";\n\nexport function assertErrorBoundaryContext(\n  value: any\n): value is ErrorBoundaryContextType {\n  if (\n    value == null ||\n    typeof value.didCatch !== \"boolean\" ||\n    typeof value.resetErrorBoundary !== \"function\"\n  ) {\n    throw new Error(\"ErrorBoundaryContext not found\");\n  }\n\n  return true;\n}\n", "import { useContext, useMemo, useState } from \"react\";\nimport { assertErrorBoundaryContext } from \"./assertErrorBoundaryContext\";\nimport { ErrorBoundaryContext } from \"./ErrorBoundaryContext\";\n\nexport type UseErrorBoundaryApi<Error> = {\n  resetBoundary: () => void;\n  showBoundary: (error: Error) => void;\n};\n\nexport function useErrorBoundary<Error = any>(): UseErrorBoundaryApi<Error> {\n  const context = useContext(ErrorBoundaryContext);\n\n  assertErrorBoundaryContext(context);\n\n  const [state, setState] = useState<{\n    error: Error | null;\n    hasError: boolean;\n  }>({\n    error: null,\n    hasError: false,\n  });\n\n  const memoized = useMemo(\n    () => ({\n      resetBoundary: () => {\n        context?.resetErrorBoundary();\n        setState({ error: null, hasError: false });\n      },\n      showBoundary: (error: Error) =>\n        setState({\n          error,\n          hasError: true,\n        }),\n    }),\n    [context?.resetErrorBoundary]\n  );\n\n  if (state.hasError) {\n    throw state.error;\n  }\n\n  return memoized;\n}\n", "import { ComponentType, createElement } from \"react\";\nimport { ErrorBoundary } from \"./ErrorBoundary\";\nimport { ErrorBoundaryProps } from \"./types\";\n\nexport function withErrorBoundary<Props extends Object>(\n  Component: ComponentType<Props>,\n  errorBoundaryProps: ErrorBoundaryProps\n): ComponentType<Props> {\n  const Wrapped: ComponentType<Props> = (props: Props) => {\n    return createElement(\n      ErrorBoundary,\n      errorBoundaryProps,\n      createElement(Component, props)\n    );\n  };\n\n  // Format for display in DevTools\n  const name = Component.displayName || Component.name || \"Unknown\";\n  Wrapped.displayName = `withErrorBoundary(${name})`;\n\n  return Wrapped;\n}\n", "import React from 'react';\nimport { FormattedMessage } from 'react-intl';\n\nimport { Tag, useDesignSystemTheme } from '@databricks/design-system';\nexport const PreviewBadge = ({ className }: { className?: string }) => {\n  const { theme } = useDesignSystemTheme();\n  return (\n    <Tag\n      componentId=\"codegen_mlflow_app_src_shared_building_blocks_previewbadge.tsx_14\"\n      className={className}\n      css={{ marginLeft: theme.spacing.xs }}\n      color=\"turquoise\"\n    >\n      <FormattedMessage\n        defaultMessage=\"Experimental\"\n        description=\"Experimental badge shown for features which are experimental\"\n      />\n    </Tag>\n  );\n};\n", "import { Theme } from '@emotion/react';\n\ninterface CompareRunPlotContainerProps {\n  controls: React.ReactNode;\n}\n\nexport const CompareRunPlotContainer = (props: React.PropsWithChildren<CompareRunPlotContainerProps>) => (\n  <div css={styles.wrapper}>\n    <div css={styles.controls}>{props.controls}</div>\n    <div css={styles.plotWrapper}>{props.children}</div>\n  </div>\n);\n\nconst styles = {\n  plotWrapper: {\n    overflow: 'hidden',\n    width: '100%',\n    height: '100%',\n    minHeight: 450,\n  },\n  wrapper: {\n    display: 'grid',\n    gridTemplateColumns: 'minmax(300px, 1fr) 3fr',\n  },\n  controls: (theme: Theme) => ({\n    padding: `0 ${theme.spacing.xs}px`,\n  }),\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { PageWrapper, Spacer } from '@databricks/design-system';\n\ntype OwnProps = {\n  usesFullHeight?: boolean;\n  children?: React.ReactNode;\n};\n\n// @ts-expect-error TS(2565): Property 'defaultProps' is used before being assig... Remove this comment to see the full error message\ntype Props = OwnProps & typeof PageContainer.defaultProps;\n\nexport function PageContainer(props: Props) {\n  const { usesFullHeight, ...restProps } = props;\n  return (\n    // @ts-expect-error TS(2322): Type '{ height: string; display: string; flexDirec... Remove this comment to see the full error message\n    <PageWrapper css={usesFullHeight ? styles.useFullHeightLayout : styles.wrapper}>\n      {/* @ts-expect-error TS(2322): Type '{ css: { flexShrink: number; }; }' is not as... Remove this comment to see the full error message */}\n      <Spacer css={styles.fixedSpacer} />\n      {usesFullHeight ? props.children : <div {...restProps} css={styles.container} />}\n    </PageWrapper>\n  );\n}\n\nPageContainer.defaultProps = {\n  usesFullHeight: false,\n};\n\nconst styles = {\n  useFullHeightLayout: {\n    height: 'calc(100% - 60px)', // 60px comes from header height\n    display: 'flex',\n    flexDirection: 'column',\n    '&:last-child': {\n      flexGrow: 1,\n    },\n  },\n  wrapper: { flex: 1 },\n  fixedSpacer: {\n    // Ensure spacer's fixed height regardless of flex\n    flexShrink: 0,\n  },\n  container: {\n    width: '100%',\n    flexGrow: 1,\n    paddingBottom: 24,\n  },\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport { escape } from 'lodash';\nimport React, { Component } from 'react';\nimport { getParams, getRunInfo } from '../reducers/Reducers';\nimport { connect } from 'react-redux';\nimport { FormUI, SimpleSelect, SimpleSelectOption, SimpleSelectOptionGroup, Spacer } from '@databricks/design-system';\nimport Utils from '../../common/utils/Utils';\nimport { getLatestMetrics } from '../reducers/MetricReducer';\nimport CompareRunUtil from './CompareRunUtil';\nimport { FormattedMessage } from 'react-intl';\nimport { LazyPlot } from './LazyPlot';\nimport { CompareRunPlotContainer } from './CompareRunPlotContainer';\n\ntype CompareRunScatterImplProps = {\n  runUuids: string[];\n  runInfos: any[]; // TODO: PropTypes.instanceOf(RunInfo)\n  metricLists: any[][];\n  paramLists: any[][];\n  runDisplayNames: string[];\n};\n\ntype CompareRunScatterImplState = any;\n\nexport class CompareRunScatterImpl extends Component<CompareRunScatterImplProps, CompareRunScatterImplState> {\n  // Size limits for displaying keys and values in our plot axes and tooltips\n  static MAX_PLOT_KEY_LENGTH = 40;\n  static MAX_PLOT_VALUE_LENGTH = 60;\n\n  metricKeys: any;\n  paramKeys: any;\n\n  constructor(props: CompareRunScatterImplProps) {\n    super(props);\n\n    this.metricKeys = CompareRunUtil.getKeys(this.props.metricLists, false);\n    this.paramKeys = CompareRunUtil.getKeys(this.props.paramLists, false);\n\n    if (this.paramKeys.length + this.metricKeys.length < 2) {\n      this.state = { disabled: true };\n    } else {\n      this.state = {\n        disabled: false,\n        x:\n          this.paramKeys.length > 0\n            ? {\n                key: this.paramKeys[0],\n                isMetric: false,\n              }\n            : {\n                key: this.metricKeys[1],\n                isMetric: true,\n              },\n        y:\n          this.metricKeys.length > 0\n            ? {\n                key: this.metricKeys[0],\n                isMetric: true,\n              }\n            : {\n                key: this.paramKeys[1],\n                isMetric: false,\n              },\n      };\n    }\n  }\n\n  /**\n   * Get the value of the metric/param described by {key, isMetric}, in run i\n   */\n  getValue(i: any, { key, isMetric }: any) {\n    const value = CompareRunUtil.findInList((isMetric ? this.props.metricLists : this.props.paramLists)[i], key);\n    return value === undefined ? value : (value as any).value;\n  }\n\n  render() {\n    // @ts-expect-error TS(4111): Property 'disabled' comes from an index signature,... Remove this comment to see the full error message\n    if (this.state.disabled) {\n      return <div />;\n    }\n\n    const keyLength = CompareRunScatterImpl.MAX_PLOT_KEY_LENGTH;\n\n    const xs: any = [];\n    const ys: any = [];\n    const tooltips: any = [];\n\n    this.props.runInfos.forEach((_, index) => {\n      // @ts-expect-error TS(4111): Property 'x' comes from an index signature, so it ... Remove this comment to see the full error message\n      const x = this.getValue(index, this.state.x);\n      // @ts-expect-error TS(4111): Property 'y' comes from an index signature, so it ... Remove this comment to see the full error message\n      const y = this.getValue(index, this.state.y);\n      if (x === undefined || y === undefined) {\n        return;\n      }\n      xs.push(x);\n      ys.push(y);\n      tooltips.push(this.getPlotlyTooltip(index));\n    });\n\n    return (\n      <CompareRunPlotContainer\n        controls={\n          <>\n            <div>\n              <FormUI.Label htmlFor=\"x-axis-selector\">\n                <FormattedMessage\n                  defaultMessage=\"X-axis:\"\n                  description=\"Label text for x-axis in scatter plot comparison in MLflow\"\n                />\n              </FormUI.Label>\n              {this.renderSelect('x')}\n            </div>\n            <Spacer />\n            <div>\n              <FormUI.Label htmlFor=\"y-axis-selector\">\n                <FormattedMessage\n                  defaultMessage=\"Y-axis:\"\n                  description=\"Label text for y-axis in scatter plot comparison in MLflow\"\n                />\n              </FormUI.Label>\n              {this.renderSelect('y')}\n            </div>\n          </>\n        }\n      >\n        <LazyPlot\n          data={[\n            {\n              x: xs,\n              y: ys,\n              text: tooltips,\n              hoverinfo: 'text',\n              type: 'scattergl',\n              mode: 'markers',\n              marker: {\n                size: 10,\n                color: 'rgba(200, 50, 100, .75)',\n              },\n            },\n          ]}\n          layout={{\n            margin: {\n              t: 30,\n            },\n            hovermode: 'closest',\n            xaxis: {\n              title: escape(Utils.truncateString(this.state['x'].key, keyLength)),\n            },\n            yaxis: {\n              title: escape(Utils.truncateString(this.state['y'].key, keyLength)),\n            },\n          }}\n          css={styles.plot}\n          config={{\n            responsive: true,\n            displaylogo: false,\n            scrollZoom: true,\n            modeBarButtonsToRemove: [\n              'sendDataToCloud',\n              'select2d',\n              'lasso2d',\n              'resetScale2d',\n              'hoverClosestCartesian',\n              'hoverCompareCartesian',\n            ],\n          }}\n          useResizeHandler\n        />\n      </CompareRunPlotContainer>\n    );\n  }\n\n  renderSelect(axis: any) {\n    return (\n      <SimpleSelect\n        componentId=\"codegen_mlflow_app_src_experiment-tracking_components_comparerunscatter.tsx_182\"\n        css={styles.select}\n        id={axis + '-axis-selector'}\n        onChange={({ target }) => {\n          const { value } = target;\n          const [prefix, ...keyParts] = value.split('-');\n          const key = keyParts.join('-');\n          const isMetric = prefix === 'metric';\n          this.setState({ [axis]: { isMetric, key } });\n        }}\n        value={(this.state[axis].isMetric ? 'metric-' : 'param-') + this.state[axis].key}\n      >\n        <SimpleSelectOptionGroup label=\"Parameter\">\n          {this.paramKeys.map((p: any) => (\n            <SimpleSelectOption key={'param-' + p} value={'param-' + p}>\n              {p}\n            </SimpleSelectOption>\n          ))}\n        </SimpleSelectOptionGroup>\n        <SimpleSelectOptionGroup label=\"Metric\">\n          {this.metricKeys.map((m: any) => (\n            <SimpleSelectOption key={'metric-' + m} value={'metric-' + m}>\n              {m}\n            </SimpleSelectOption>\n          ))}\n        </SimpleSelectOptionGroup>\n      </SimpleSelect>\n    );\n  }\n\n  getPlotlyTooltip(index: any) {\n    const keyLength = CompareRunScatterImpl.MAX_PLOT_KEY_LENGTH;\n    const valueLength = CompareRunScatterImpl.MAX_PLOT_VALUE_LENGTH;\n    const runName = this.props.runDisplayNames[index];\n    let result = `<b>${escape(runName)}</b><br>`;\n    const paramList = this.props.paramLists[index];\n    paramList.forEach((p) => {\n      result +=\n        escape(Utils.truncateString(p.key, keyLength)) +\n        ': ' +\n        escape(Utils.truncateString(p.value, valueLength)) +\n        '<br>';\n    });\n    const metricList = this.props.metricLists[index];\n    if (metricList.length > 0) {\n      result += paramList.length > 0 ? '<br>' : '';\n      metricList.forEach((m) => {\n        result += escape(Utils.truncateString(m.key, keyLength)) + ': ' + Utils.formatMetric(m.value) + '<br>';\n      });\n    }\n    return result;\n  }\n}\n\nconst styles = {\n  select: {\n    width: '100%',\n  },\n  plot: {\n    width: '100%',\n  },\n};\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const runInfos: any = [];\n  const metricLists: any = [];\n  const paramLists: any = [];\n  const { runUuids } = ownProps;\n  runUuids.forEach((runUuid: any) => {\n    runInfos.push(getRunInfo(runUuid, state));\n    metricLists.push(Object.values(getLatestMetrics(runUuid, state)));\n    paramLists.push(Object.values(getParams(runUuid, state)));\n  });\n  return { runInfos, metricLists, paramLists };\n};\n\nexport const CompareRunScatter = connect(mapStateToProps)(CompareRunScatterImpl);\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React, { Component } from 'react';\nimport './RequestStateWrapper.css';\nimport { connect } from 'react-redux';\nimport { getApis } from '../../experiment-tracking/reducers/Reducers';\nimport { Spinner } from './Spinner';\nimport { ErrorCodes } from '../constants';\nimport { ErrorWrapper } from '../utils/ErrorWrapper';\nimport { ReduxState } from '../../redux-types';\n\nexport const DEFAULT_ERROR_MESSAGE = 'A request error occurred.';\n\ntype RequestStateWrapperProps = {\n  children?: React.ReactNode;\n  customSpinner?: React.ReactNode;\n  shouldOptimisticallyRender?: boolean;\n  requests: any[];\n  requestIds?: string[];\n  requestIdsWith404sToIgnore?: string[];\n  description?: any; // TODO: PropTypes.oneOf(Object.values(LoadingDescription))\n  permissionDeniedView?: React.ReactNode;\n  suppressErrorThrow?: boolean;\n  customRequestErrorHandlerFn?: (\n    failedRequests: {\n      id: string;\n      active?: boolean;\n      error: Error | ErrorWrapper;\n    }[],\n  ) => void;\n};\n\ntype RequestStateWrapperState = any;\n\nexport class RequestStateWrapper extends Component<RequestStateWrapperProps, RequestStateWrapperState> {\n  static defaultProps = {\n    requests: [],\n    requestIdsWith404sToIgnore: [],\n    shouldOptimisticallyRender: false,\n  };\n\n  state = {\n    shouldRender: false,\n    shouldRenderError: false,\n  };\n\n  static getErrorRequests(requests: any, requestIdsWith404sToIgnore: any) {\n    return requests.filter((r: any) => {\n      if (r.error !== undefined) {\n        return !(\n          requestIdsWith404sToIgnore &&\n          requestIdsWith404sToIgnore.includes(r.id) &&\n          r.error.getErrorCode() === ErrorCodes.RESOURCE_DOES_NOT_EXIST\n        );\n      }\n      return false;\n    });\n  }\n\n  static getDerivedStateFromProps(nextProps: any) {\n    const shouldRender = nextProps.requests.length\n      ? nextProps.requests.every((r: any) => r && r.active === false)\n      : false;\n\n    const requestErrors = RequestStateWrapper.getErrorRequests(\n      nextProps.requests,\n      nextProps.requestIdsWith404sToIgnore,\n    );\n\n    return {\n      shouldRender,\n      shouldRenderError: requestErrors.length > 0,\n      requestErrors,\n    };\n  }\n\n  getRenderedContent() {\n    const { children, requests, customSpinner, permissionDeniedView, suppressErrorThrow, customRequestErrorHandlerFn } =\n      this.props;\n    // @ts-expect-error TS(2339): Property 'requestErrors' does not exist on type '{... Remove this comment to see the full error message\n    const { shouldRender, shouldRenderError, requestErrors } = this.state;\n    const permissionDeniedErrors = requestErrors.filter((failedRequest: any) => {\n      return failedRequest.error.getErrorCode() === ErrorCodes.PERMISSION_DENIED;\n    });\n\n    if (typeof children === 'function') {\n      return children(!shouldRender, shouldRenderError, requests, requestErrors);\n    } else if (shouldRender || shouldRenderError || this.props.shouldOptimisticallyRender) {\n      if (permissionDeniedErrors.length > 0 && permissionDeniedView) {\n        return permissionDeniedView;\n      }\n      if (shouldRenderError && !suppressErrorThrow) {\n        customRequestErrorHandlerFn ? customRequestErrorHandlerFn(requestErrors) : triggerError(requestErrors);\n      }\n\n      return children;\n    }\n\n    return customSpinner || <Spinner />;\n  }\n\n  render() {\n    return this.getRenderedContent();\n  }\n}\n\nexport const triggerError = (requests: any) => {\n  // This triggers the OOPS error boundary.\n  // eslint-disable-next-line no-console -- TODO(FEINF-3587)\n  console.error('ERROR', requests);\n  throw Error(`${DEFAULT_ERROR_MESSAGE}: ${requests.error}`);\n};\n\nconst mapStateToProps = (state: ReduxState, ownProps: Omit<RequestStateWrapperProps, 'requests'>) => ({\n  requests: getApis(ownProps.requestIds, state),\n});\n\nexport default connect(mapStateToProps)(RequestStateWrapper);\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { connect } from 'react-redux';\nimport _ from 'lodash';\nimport { LazyPlot } from './LazyPlot';\n\nconst AXIS_LABEL_CLS = '.pcp-plot .parcoords .y-axis .axis-heading .axis-title';\nexport const UNKNOWN_TERM = 'unknown';\n\ntype ParallelCoordinatesPlotViewProps = {\n  runUuids: string[];\n  paramKeys: string[];\n  metricKeys: string[];\n  paramDimensions: any[];\n  metricDimensions: any[];\n};\n\ntype ParallelCoordinatesPlotViewState = any;\n\nexport class ParallelCoordinatesPlotView extends React.Component<\n  ParallelCoordinatesPlotViewProps,\n  ParallelCoordinatesPlotViewState\n> {\n  state = {\n    // Current sequence of all axes, both parameters and metrics.\n    sequence: [...this.props.paramKeys, ...this.props.metricKeys],\n  };\n\n  static getDerivedStateFromProps(props: any, state: any) {\n    const keysFromProps = [...props.paramKeys, ...props.metricKeys];\n    const keysFromState = state.sequence;\n    if (!_.isEqual(_.sortBy(keysFromProps), _.sortBy(keysFromState))) {\n      return { sequence: keysFromProps };\n    }\n    return null;\n  }\n\n  getData() {\n    const { sequence } = this.state;\n    const { paramDimensions, metricDimensions, metricKeys } = this.props;\n    const lastMetricKey = this.findLastKeyFromState(metricKeys);\n    const lastMetricDimension = this.props.metricDimensions.find((d) => d.label === lastMetricKey);\n    const colorScaleConfigs = ParallelCoordinatesPlotView.getColorScaleConfigsForDimension(lastMetricDimension);\n    // This make sure axis order consistency across renders.\n    const orderedDimensions = ParallelCoordinatesPlotView.getDimensionsOrderedBySequence(\n      [...paramDimensions, ...metricDimensions],\n      sequence,\n    );\n    return [\n      {\n        type: 'parcoords',\n        line: { ...colorScaleConfigs },\n        dimensions: orderedDimensions,\n      },\n    ];\n  }\n\n  static getDimensionsOrderedBySequence(dimensions: any, sequence: any) {\n    return _.sortBy(dimensions, [(dimension) => sequence.indexOf(dimension.label)]);\n  }\n\n  static getLabelElementsFromDom = () => Array.from(document.querySelectorAll(AXIS_LABEL_CLS));\n\n  findLastKeyFromState(keys: any) {\n    const { sequence } = this.state;\n    const keySet = new Set(keys);\n    return _.findLast(sequence, (key) => keySet.has(key));\n  }\n\n  static getColorScaleConfigsForDimension(dimension: any) {\n    if (!dimension) return null;\n    const cmin = _.min(dimension.values);\n    const cmax = _.max(dimension.values);\n    return {\n      showscale: true,\n      colorscale: 'Jet',\n      cmin,\n      cmax,\n      color: dimension.values,\n    };\n  }\n\n  // Update styles(green & bold) for metric axes.\n  // Note(Zangr) 2019-6-25 this is needed because there is no per axis label setting available. This\n  // needs to be called every time chart updates. More information about currently available label\n  // setting here: https://plot.ly/javascript/reference/#parcoords-labelfont\n  updateMetricAxisLabelStyle = () => {\n    /* eslint-disable no-param-reassign */\n    const metricsKeySet = new Set(this.props.metricKeys);\n    // TODO(Zangr) 2019-06-20 This assumes name uniqueness across params & metrics. Find a way to\n    // make it more deterministic. Ex. Add add different data attributes to indicate axis kind.\n    ParallelCoordinatesPlotView.getLabelElementsFromDom()\n      .filter((el) => metricsKeySet.has(el.innerHTML))\n      .forEach((el) => {\n        (el as any).style.fill = 'green';\n        (el as any).style.fontWeight = 'bold';\n      });\n  };\n\n  maybeUpdateStateForColorScale = (currentSequenceFromPlotly: any) => {\n    const rightmostMetricKeyFromState = this.findLastKeyFromState(this.props.metricKeys);\n    const metricsKeySet = new Set(this.props.metricKeys);\n    const rightmostMetricKeyFromPlotly = _.findLast(currentSequenceFromPlotly, (key) => metricsKeySet.has(key));\n    // Currently we always render color scale based on the rightmost metric axis, so if that changes\n    // we need to setState with the new axes sequence to trigger a rerender.\n    if (rightmostMetricKeyFromState !== rightmostMetricKeyFromPlotly) {\n      this.setState({ sequence: currentSequenceFromPlotly });\n    }\n  };\n\n  handlePlotUpdate = ({ data: [{ dimensions }] }: any) => {\n    this.updateMetricAxisLabelStyle();\n    this.maybeUpdateStateForColorScale(dimensions.map((d: any) => d.label));\n  };\n\n  render() {\n    return (\n      <LazyPlot\n        layout={{ autosize: true, margin: { t: 50 } }}\n        useResizeHandler\n        css={styles.plot}\n        data={this.getData()}\n        onUpdate={this.handlePlotUpdate}\n        className=\"pcp-plot\"\n        config={{ displayModeBar: false }}\n      />\n    );\n  }\n}\n\nexport const generateAttributesForCategoricalDimension = (labels: any) => {\n  // Create a lookup from label to its own alphabetical sorted order.\n  // Ex. ['A', 'B', 'C'] => { 'A': '0', 'B': '1', 'C': '2' }\n  const sortedUniqLabels = _.uniq(labels).sort();\n\n  // We always want the UNKNOWN_TERM to be at the top\n  // of the chart which is end of the sorted label array\n  // Ex. ['A', 'UNKNOWN_TERM', 'B'] => { 'A': '0', 'B': '1', 'UNKNOWN_TERM': '2' }\n  let addUnknownTerm = false;\n  const filteredSortedUniqLabels = sortedUniqLabels.filter((label) => {\n    if (label === UNKNOWN_TERM) addUnknownTerm = true;\n    return label !== UNKNOWN_TERM;\n  });\n  if (addUnknownTerm) {\n    filteredSortedUniqLabels.push(UNKNOWN_TERM);\n  }\n  const labelToIndexStr = _.invert(filteredSortedUniqLabels);\n  const attributes = {};\n\n  // Values are assigned to their alphabetical sorted index number\n  (attributes as any).values = labels.map((label: any) => Number(labelToIndexStr[label]));\n\n  // Default to alphabetical order for categorical axis here. Ex. [0, 1, 2, 3 ...]\n  (attributes as any).tickvals = _.range(filteredSortedUniqLabels.length);\n\n  // Default to alphabetical order for categorical axis here. Ex. ['A', 'B', 'C', 'D' ...]\n  (attributes as any).ticktext = filteredSortedUniqLabels.map((sortedUniqLabel) =>\n    (sortedUniqLabel as any).substring(0, 10),\n  );\n\n  return attributes;\n};\n\n/**\n * Infer the type of data in a run. If all the values are numbers or castable to numbers, then\n * treat it as a number column.\n */\nexport const inferType = (key: any, runUuids: any, entryByRunUuid: any) => {\n  for (let i = 0; i < runUuids.length; i++) {\n    if (entryByRunUuid[runUuids[i]][key]) {\n      const { value } = entryByRunUuid[runUuids[i]][key];\n      if (typeof value === 'string' && isNaN(Number(value)) && value !== 'NaN') {\n        return 'string';\n      }\n    }\n  }\n  return 'number';\n};\n\nexport const createDimension = (key: any, runUuids: any, entryByRunUuid: any) => {\n  let attributes = {};\n  const dataType = inferType(key, runUuids, entryByRunUuid);\n  if (dataType === 'string') {\n    attributes = generateAttributesForCategoricalDimension(\n      runUuids.map((runUuid: any) =>\n        entryByRunUuid[runUuid][key] ? entryByRunUuid[runUuid][key].value : UNKNOWN_TERM,\n      ),\n    );\n  } else {\n    let maxValue = Number.MIN_SAFE_INTEGER;\n    const values = runUuids.map((runUuid: any) => {\n      if (entryByRunUuid[runUuid][key]) {\n        const { value } = entryByRunUuid[runUuid][key];\n        const numericValue = Number(value);\n        if (maxValue < numericValue) maxValue = numericValue;\n        return numericValue;\n      }\n      return UNKNOWN_TERM;\n    });\n\n    // For Numerical values, we take the max value of all the attribute\n    // values and 0.01 to it so it is always at top of the graph.\n    (attributes as any).values = values.map((value: any) => {\n      if (value === UNKNOWN_TERM) return maxValue + 0.01;\n      return value;\n    });\n\n    // For some reason, Plotly tries to plot these values with SI prefixes by default\n    // Explicitly set to 5 fixed digits float here\n    (attributes as any).tickformat = '.5f';\n  }\n  return {\n    label: key,\n    ...attributes,\n  };\n};\n\nconst styles = {\n  plot: {\n    width: '100%',\n  },\n};\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const { runUuids, paramKeys, metricKeys } = ownProps;\n  const { latestMetricsByRunUuid, paramsByRunUuid } = state.entities;\n  const paramDimensions = paramKeys.map((paramKey: any) => createDimension(paramKey, runUuids, paramsByRunUuid));\n  const metricDimensions = metricKeys.map((metricKey: any) =>\n    createDimension(metricKey, runUuids, latestMetricsByRunUuid),\n  );\n  return { paramDimensions, metricDimensions };\n};\n\nexport default connect(mapStateToProps)(ParallelCoordinatesPlotView);\n", "import { Button, LegacySelect } from '@databricks/design-system';\nimport { type Theme } from '@emotion/react';\nimport { FormattedMessage } from 'react-intl';\n\ntype Props = {\n  paramKeys: string[];\n  metricKeys: string[];\n  selectedParamKeys: string[];\n  selectedMetricKeys: string[];\n  handleParamsSelectChange: (paramValues: string[]) => void;\n  handleMetricsSelectChange: (metricValues: string[]) => void;\n  onClearAllSelect: () => void;\n};\n\nexport function ParallelCoordinatesPlotControls({\n  paramKeys,\n  metricKeys,\n  selectedParamKeys,\n  selectedMetricKeys,\n  handleParamsSelectChange,\n  handleMetricsSelectChange,\n  onClearAllSelect,\n}: Props) {\n  return (\n    <div css={styles.wrapper}>\n      <div>\n        <FormattedMessage\n          defaultMessage=\"Parameters:\"\n          description=\"Label text for parameters in parallel coordinates plot in MLflow\"\n        />\n      </div>\n      <LegacySelect\n        mode=\"multiple\"\n        css={styles.select}\n        placeholder={\n          <FormattedMessage\n            defaultMessage=\"Please select parameters\"\n            description=\"Placeholder text for parameters in parallel coordinates plot in MLflow\"\n          />\n        }\n        value={selectedParamKeys}\n        onChange={handleParamsSelectChange}\n      >\n        {paramKeys.map((key) => (\n          <LegacySelect.Option value={key} key={key}>\n            {key}\n          </LegacySelect.Option>\n        ))}\n      </LegacySelect>\n      <div style={{ marginTop: 20 }}>\n        <FormattedMessage\n          defaultMessage=\"Metrics:\"\n          description=\"Label text for metrics in parallel coordinates plot in MLflow\"\n        />\n      </div>\n      <LegacySelect\n        mode=\"multiple\"\n        css={styles.select}\n        placeholder={\n          <FormattedMessage\n            defaultMessage=\"Please select metrics\"\n            description=\"Placeholder text for metrics in parallel coordinates plot in MLflow\"\n          />\n        }\n        value={selectedMetricKeys}\n        onChange={handleMetricsSelectChange}\n      >\n        {metricKeys.map((key) => (\n          <LegacySelect.Option value={key} key={key}>\n            {key}\n          </LegacySelect.Option>\n        ))}\n      </LegacySelect>\n      <div style={{ marginTop: 20 }}>\n        <Button\n          componentId=\"codegen_mlflow_app_src_experiment-tracking_components_parallelcoordinatesplotcontrols.tsx_84\"\n          data-test-id=\"clear-button\"\n          onClick={onClearAllSelect}\n        >\n          <FormattedMessage\n            defaultMessage=\"Clear All\"\n            description=\"String for the clear button to clear any selected parameters and metrics\"\n          />\n        </Button>\n      </div>\n    </div>\n  );\n}\n\nconst styles = {\n  wrapper: (theme: Theme) => ({\n    padding: `0 ${theme.spacing.xs}px`,\n  }),\n  select: { width: '100%' },\n};\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { connect } from 'react-redux';\nimport ParallelCoordinatesPlotView from './ParallelCoordinatesPlotView';\nimport { ParallelCoordinatesPlotControls } from './ParallelCoordinatesPlotControls';\nimport {\n  getAllParamKeysByRunUuids,\n  getAllMetricKeysByRunUuids,\n  getSharedMetricKeysByRunUuids,\n  getRunInfo,\n} from '../reducers/Reducers';\nimport _ from 'lodash';\nimport { CompareRunPlotContainer } from './CompareRunPlotContainer';\nimport { FormattedMessage } from 'react-intl';\nimport { Typography } from '@databricks/design-system';\n\ntype ParallelCoordinatesPlotPanelProps = {\n  runUuids: string[];\n  allParamKeys: string[];\n  allMetricKeys: string[];\n  sharedMetricKeys: string[];\n  diffParamKeys: string[];\n};\n\ntype ParallelCoordinatesPlotPanelState = any;\n\nexport class ParallelCoordinatesPlotPanel extends React.Component<\n  ParallelCoordinatesPlotPanelProps,\n  ParallelCoordinatesPlotPanelState\n> {\n  state = {\n    // Default to select differing parameters. Sort alphabetically (to match\n    // highlighted params in param table), then cap at first 3\n    selectedParamKeys: this.props.diffParamKeys.sort().slice(0, 3),\n    // Default to select the first metric key.\n    // Note that there will be no color scaling if no metric is selected.\n    selectedMetricKeys: this.props.sharedMetricKeys.slice(0, 1),\n  };\n\n  handleParamsSelectChange = (paramValues: any) => {\n    this.setState({ selectedParamKeys: paramValues });\n  };\n\n  handleMetricsSelectChange = (metricValues: any) => {\n    this.setState({ selectedMetricKeys: metricValues });\n  };\n\n  onClearAllSelect = () => {\n    this.setState({ selectedParamKeys: [], selectedMetricKeys: [] });\n  };\n\n  render() {\n    const { runUuids, allParamKeys, allMetricKeys } = this.props;\n    const { selectedParamKeys, selectedMetricKeys } = this.state;\n    return (\n      <CompareRunPlotContainer\n        controls={\n          <ParallelCoordinatesPlotControls\n            paramKeys={allParamKeys}\n            metricKeys={allMetricKeys}\n            selectedParamKeys={selectedParamKeys}\n            selectedMetricKeys={selectedMetricKeys}\n            handleMetricsSelectChange={this.handleMetricsSelectChange}\n            handleParamsSelectChange={this.handleParamsSelectChange}\n            onClearAllSelect={this.onClearAllSelect}\n          />\n        }\n      >\n        {!_.isEmpty(selectedParamKeys) || !_.isEmpty(selectedMetricKeys) ? (\n          <ParallelCoordinatesPlotView\n            runUuids={runUuids}\n            paramKeys={selectedParamKeys}\n            metricKeys={selectedMetricKeys}\n          />\n        ) : (\n          // @ts-expect-error TS(2322): Type '(theme: any) => { padding: any; textAlign: s... Remove this comment to see the full error message\n          <div css={styles.noValuesSelected} data-testid=\"no-values-selected\">\n            <Typography.Title level={2}>\n              <FormattedMessage\n                defaultMessage=\"Nothing to compare!\"\n                // eslint-disable-next-line max-len\n                description=\"Header displayed in the metrics and params compare plot when no values are selected\"\n              />\n            </Typography.Title>\n            <FormattedMessage\n              defaultMessage=\"Please select parameters and/or metrics to display the comparison.\"\n              // eslint-disable-next-line max-len\n              description=\"Explanation displayed in the metrics and params compare plot when no values are selected\"\n            />\n          </div>\n        )}\n      </CompareRunPlotContainer>\n    );\n  }\n}\n\nexport const getDiffParams = (allParamKeys: any, runUuids: any, paramsByRunUuid: any) => {\n  const diffParamKeys: any = [];\n  allParamKeys.forEach((param: any) => {\n    // collect all values for this param\n    const paramVals = runUuids.map(\n      (runUuid: any) => paramsByRunUuid[runUuid][param] && paramsByRunUuid[runUuid][param].value,\n    );\n    if (!paramVals.every((x: any, i: any, arr: any) => x === arr[0])) diffParamKeys.push(param);\n  });\n  return diffParamKeys;\n};\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const { runUuids: allRunUuids } = ownProps;\n\n  // Filter out runUuids that do not have corresponding runInfos\n  const runUuids = (allRunUuids ?? []).filter((uuid: string) => getRunInfo(uuid, state));\n  const allParamKeys = getAllParamKeysByRunUuids(runUuids, state);\n  const allMetricKeys = getAllMetricKeysByRunUuids(runUuids, state);\n  const sharedMetricKeys = getSharedMetricKeysByRunUuids(runUuids, state);\n  const { paramsByRunUuid } = state.entities;\n  const diffParamKeys = getDiffParams(allParamKeys, runUuids, paramsByRunUuid);\n\n  return {\n    allParamKeys,\n    allMetricKeys,\n    sharedMetricKeys,\n    diffParamKeys,\n  };\n};\n\nconst styles = {\n  noValuesSelected: (theme: any) => ({\n    padding: theme.spacing.md,\n    textAlign: 'center',\n  }),\n};\n\n// @ts-expect-error TS(2345): Argument of type 'typeof ParallelCoordinatesPlotPa... Remove this comment to see the full error message\nexport default connect(mapStateToProps)(ParallelCoordinatesPlotPanel);\n", "import React from 'react';\nimport { BadRequestError, InternalServerError, NotFoundError, PermissionError } from '@databricks/web-shared/errors';\nimport { ErrorWrapper } from './ErrorWrapper';\nimport { ErrorCodes } from '../constants';\n\nclass ErrorUtils {\n  static mlflowServices = {\n    MODEL_REGISTRY: 'Model Registry',\n    EXPERIMENTS: 'Experiments',\n    MODEL_SERVING: 'Model Serving',\n    RUN_TRACKING: 'Run Tracking',\n  };\n}\n\n/**\n * Maps known types of ErrorWrapper (legacy) to platform's predefined error instances.\n */\nexport const mapErrorWrapperToPredefinedError = (errorWrapper: ErrorWrapper, requestId?: string) => {\n  if (!(errorWrapper instanceof ErrorWrapper)) {\n    return undefined;\n  }\n  const { status } = errorWrapper;\n  let error: Error | undefined = undefined;\n  const networkErrorDetails = { status };\n  if (errorWrapper.getErrorCode() === ErrorCodes.RESOURCE_DOES_NOT_EXIST) {\n    error = new NotFoundError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.PERMISSION_DENIED) {\n    error = new PermissionError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.INTERNAL_ERROR) {\n    error = new InternalServerError(networkErrorDetails);\n  }\n  if (errorWrapper.getErrorCode() === ErrorCodes.INVALID_PARAMETER_VALUE) {\n    error = new BadRequestError(networkErrorDetails);\n  }\n\n  // Attempt to extract message from error wrapper and assign it to the error instance.\n  const messageFromErrorWrapper = errorWrapper.getMessageField();\n  if (error && messageFromErrorWrapper) {\n    error.message = messageFromErrorWrapper;\n  }\n\n  return error;\n};\nexport default ErrorUtils;\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON>b,\n  Button,\n  Spacer,\n  Dropdown,\n  Menu,\n  Header,\n  OverflowIcon,\n  useDesignSystemTheme,\n  type HeaderProps,\n} from '@databricks/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { PreviewBadge } from './PreviewBadge';\n\ntype OverflowMenuProps = {\n  menu?: {\n    id: string;\n    itemName: React.ReactNode;\n    onClick?: (...args: any[]) => any;\n    href?: string;\n  }[];\n};\n\nexport function OverflowMenu({ menu }: OverflowMenuProps) {\n  const overflowMenu = (\n    <Menu>\n      {/* @ts-expect-error TS(2532): Object is possibly 'undefined'. */}\n      {menu.map(({ id, itemName, onClick, href, ...otherProps }) => (\n        // @ts-expect-error TS(2769): No overload matches this call.\n        <Menu.Item key={id} onClick={onClick} href={href} data-test-id={id} {...otherProps}>\n          {itemName}\n        </Menu.Item>\n      ))}\n    </Menu>\n  );\n\n  // @ts-expect-error TS(2532): Object is possibly 'undefined'.\n  return menu.length > 0 ? (\n    <Dropdown overlay={overflowMenu} trigger={['click']} placement=\"bottomLeft\" arrow>\n      <Button\n        componentId=\"codegen_mlflow_app_src_shared_building_blocks_pageheader.tsx_54\"\n        icon={<OverflowIcon />}\n        data-test-id=\"overflow-menu-trigger\"\n        aria-label=\"Open header dropdown menu\"\n      />\n    </Dropdown>\n  ) : null;\n}\n\ntype PageHeaderProps = Pick<HeaderProps, 'dangerouslyAppendEmotionCSS'> & {\n  title: React.ReactNode;\n  breadcrumbs?: React.ReactNode[];\n  preview?: boolean;\n  feedbackOrigin?: string;\n  infoPopover?: React.ReactNode;\n  children?: React.ReactNode;\n  spacerSize?: 'xs' | 'sm' | 'md' | 'lg';\n  hideSpacer?: boolean;\n  titleAddOns?: React.ReactNode | React.ReactNode[];\n};\n\n/**\n * A page header that includes:\n *   - title,\n *   - optional breadcrumb content,\n *   - optional preview mark,\n *   - optional feedback origin: shows the \"Send feedback\" button when not empty, and\n *   - optional info popover, safe to have link inside.\n */\nexport function PageHeader(props: PageHeaderProps) {\n  const {\n    title, // required\n    breadcrumbs = [],\n    titleAddOns = [],\n    preview,\n    children,\n    spacerSize,\n    hideSpacer = false,\n    dangerouslyAppendEmotionCSS,\n  } = props;\n  const { theme } = useDesignSystemTheme();\n  const intl = useIntl();\n\n  return (\n    <>\n      <Header\n        breadcrumbs={\n          breadcrumbs.length > 0 && (\n            <Breadcrumb includeTrailingCaret>\n              {breadcrumbs.map((b, i) => (\n                <Breadcrumb.Item key={i}>{b}</Breadcrumb.Item>\n              ))}\n            </Breadcrumb>\n          )\n        }\n        buttons={children}\n        title={title}\n        // prettier-ignore\n        titleAddOns={\n          <>\n            {preview && <PreviewBadge css={{ marginLeft: 0 }} />}\n            {titleAddOns}\n          </>\n        }\n        dangerouslyAppendEmotionCSS={dangerouslyAppendEmotionCSS}\n      />\n      <Spacer\n        // @ts-expect-error TS(2322): Type '{ css: { flexShrink: number; }; }' is not as... Remove this comment to see the full error message\n        css={{\n          // Ensure spacer's fixed height\n          flexShrink: 0,\n          ...(hideSpacer ? { display: 'none' } : {}),\n        }}\n        size={spacerSize}\n      />\n    </>\n  );\n}\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport React from 'react';\nimport { LegacySkeleton } from '@databricks/design-system';\nimport { SectionErrorBoundary } from '../../common/components/error-boundaries/SectionErrorBoundary';\n\nconst Plot = React.lazy(() => import('react-plotly.js'));\n\nexport const LazyPlot = ({ fallback, ...props }: any) => (\n  <SectionErrorBoundary>\n    <React.Suspense fallback={fallback ?? <LegacySkeleton active />}>\n      <Plot {...props} />\n    </React.Suspense>\n  </SectionErrorBoundary>\n);\n", "/**\n * NOTE: this code file was automatically migrated to TypeScript using ts-migrate and\n * may contain multiple `any` type annotations and `@ts-expect-error` directives.\n * If possible, please improve types while making changes to this file. If the type\n * annotations are already looking good, please remove this comment.\n */\n\nimport { escape } from 'lodash';\nimport React, { Component } from 'react';\nimport {\n  Switch,\n  Spacer,\n  SimpleSelect,\n  SimpleSelectOptionGroup,\n  SimpleSelectOption,\n  FormUI,\n} from '@databricks/design-system';\nimport { getParams, getRunInfo } from '../reducers/Reducers';\nimport { connect } from 'react-redux';\nimport Utils from '../../common/utils/Utils';\nimport { getLatestMetrics } from '../reducers/MetricReducer';\nimport CompareRunUtil from './CompareRunUtil';\nimport { FormattedMessage } from 'react-intl';\nimport { LazyPlot } from './LazyPlot';\nimport { CompareRunPlotContainer } from './CompareRunPlotContainer';\n\ntype CompareRunContourProps = {\n  runInfos: any[]; // TODO: PropTypes.instanceOf(RunInfo)\n  metricLists: any[][];\n  paramLists: any[][];\n  runDisplayNames: string[];\n};\n\ntype CompareRunContourState = any;\n\nexport class CompareRunContour extends Component<CompareRunContourProps, CompareRunContourState> {\n  // Size limits for displaying keys and values in our plot axes and tooltips\n  static MAX_PLOT_KEY_LENGTH = 40;\n  static MAX_PLOT_VALUE_LENGTH = 60;\n\n  metricKeys: any;\n  paramKeys: any;\n\n  constructor(props: CompareRunContourProps) {\n    super(props);\n\n    this.metricKeys = CompareRunUtil.getKeys(this.props.metricLists, true);\n    this.paramKeys = CompareRunUtil.getKeys(this.props.paramLists, true);\n\n    if (this.paramKeys.length + this.metricKeys.length < 3) {\n      this.state = { disabled: true };\n    } else {\n      const common = { disabled: false, reverseColor: false };\n      if (this.metricKeys.length === 0) {\n        this.state = {\n          ...common,\n          xaxis: { key: this.paramKeys[0], isMetric: false },\n          yaxis: { key: this.paramKeys[1], isMetric: false },\n          zaxis: { key: this.paramKeys[2], isMetric: false },\n        };\n      } else if (this.paramKeys.length === 0) {\n        this.state = {\n          ...common,\n          xaxis: { key: this.metricKeys[0], isMetric: true },\n          yaxis: { key: this.metricKeys[1], isMetric: true },\n          zaxis: { key: this.metricKeys[2], isMetric: true },\n        };\n      } else if (this.paramKeys.length === 1) {\n        this.state = {\n          ...common,\n          xaxis: { key: this.paramKeys[0], isMetric: false },\n          yaxis: { key: this.metricKeys[0], isMetric: true },\n          zaxis: { key: this.metricKeys[1], isMetric: true },\n        };\n      } else {\n        this.state = {\n          ...common,\n          xaxis: { key: this.paramKeys[0], isMetric: false },\n          yaxis: { key: this.paramKeys[1], isMetric: false },\n          zaxis: { key: this.metricKeys[0], isMetric: true },\n        };\n      }\n    }\n  }\n\n  /**\n   * Get the value of the metric/param described by {key, isMetric}, in run i\n   */\n  getValue(i: any, { key, isMetric }: any) {\n    const value = CompareRunUtil.findInList((isMetric ? this.props.metricLists : this.props.paramLists)[i], key);\n    return value === undefined ? value : (value as any).value;\n  }\n\n  getColorscale() {\n    /*\n     * contour plot has an option named \"reversescale\" which\n     * reverses the color mapping if True, but it doesn't work properly now.\n     *\n     * https://github.com/plotly/plotly.js/issues/4430\n     *\n     * This function is a temporary workaround for the issue.\n     */\n    const colorscale = [\n      [0, 'rgb(5,10,172)'],\n      [0.35, 'rgb(40,60,190)'],\n      [0.5, 'rgb(70,100,245)'],\n      [0.6, 'rgb(90,120,245)'],\n      [0.7, 'rgb(106,137,247)'],\n      [1, 'rgb(220,220,220)'],\n    ];\n\n    // @ts-expect-error TS(4111): Property 'reverseColor' comes from an index signat... Remove this comment to see the full error message\n    if (this.state.reverseColor) {\n      return colorscale;\n    } else {\n      // reverse only RGB values\n      return colorscale.map(([val], index) => [val, colorscale[colorscale.length - 1 - index][1]]);\n    }\n  }\n\n  render() {\n    // @ts-expect-error TS(4111): Property 'disabled' comes from an index signature,... Remove this comment to see the full error message\n    if (this.state.disabled) {\n      return (\n        <div>\n          <FormattedMessage\n            defaultMessage=\"Contour plots can only be rendered when comparing a group of runs\n              with three or more unique metrics or params. Log more metrics or params to your\n              runs to visualize them using the contour plot.\"\n            description=\"Text explanation when contour plot is disabled in comparison pages\n              in MLflow\"\n          />\n        </div>\n      );\n    }\n\n    const keyLength = CompareRunContour.MAX_PLOT_KEY_LENGTH;\n\n    const xs: any = [];\n    const ys: any = [];\n    const zs: any = [];\n    const tooltips: any = [];\n\n    this.props.runInfos.forEach((_, index) => {\n      // @ts-expect-error TS(4111): Property 'xaxis' comes from an index signature, so... Remove this comment to see the full error message\n      const x = this.getValue(index, this.state.xaxis);\n      // @ts-expect-error TS(4111): Property 'yaxis' comes from an index signature, so... Remove this comment to see the full error message\n      const y = this.getValue(index, this.state.yaxis);\n      // @ts-expect-error TS(4111): Property 'zaxis' comes from an index signature, so... Remove this comment to see the full error message\n      const z = this.getValue(index, this.state.zaxis);\n      if (x === undefined || y === undefined || z === undefined) {\n        return;\n      }\n      xs.push(parseFloat(x));\n      ys.push(parseFloat(y));\n      zs.push(parseFloat(z));\n      tooltips.push(this.getPlotlyTooltip(index));\n    });\n\n    const maybeRenderPlot = () => {\n      const invalidAxes = [];\n      if (new Set(xs).size < 2) {\n        invalidAxes.push('X');\n      }\n      if (new Set(ys).size < 2) {\n        invalidAxes.push('Y');\n      }\n      if (invalidAxes.length > 0) {\n        const messageHead =\n          invalidAxes.length > 1 ? `The ${invalidAxes.join(' and ')} axes don't` : `The ${invalidAxes[0]} axis doesn't`;\n        return (\n          <div\n            css={styles.noDataMessage}\n          >{`${messageHead} have enough unique data points to render the contour plot.`}</div>\n        );\n      }\n\n      return (\n        <LazyPlot\n          css={styles.plot}\n          data={[\n            // contour plot\n            {\n              z: zs,\n              x: xs,\n              y: ys,\n              type: 'contour',\n              hoverinfo: 'none',\n              colorscale: this.getColorscale(),\n              connectgaps: true,\n              contours: {\n                coloring: 'heatmap',\n              },\n            },\n            // scatter plot\n            {\n              x: xs,\n              y: ys,\n              text: tooltips,\n              hoverinfo: 'text',\n              type: 'scattergl',\n              mode: 'markers',\n              marker: {\n                size: 10,\n                color: 'rgba(200, 50, 100, .75)',\n              },\n            },\n          ]}\n          layout={{\n            margin: {\n              t: 30,\n            },\n            hovermode: 'closest',\n            xaxis: {\n              title: escape(Utils.truncateString(this.state['xaxis'].key, keyLength)),\n              range: [Math.min(...xs), Math.max(...xs)],\n            },\n            yaxis: {\n              title: escape(Utils.truncateString(this.state['yaxis'].key, keyLength)),\n              range: [Math.min(...ys), Math.max(...ys)],\n            },\n          }}\n          config={{\n            responsive: true,\n            displaylogo: false,\n            scrollZoom: true,\n            modeBarButtonsToRemove: [\n              'sendDataToCloud',\n              'select2d',\n              'lasso2d',\n              'resetScale2d',\n              'hoverClosestCartesian',\n              'hoverCompareCartesian',\n            ],\n          }}\n          useResizeHandler\n        />\n      );\n    };\n\n    return (\n      <CompareRunPlotContainer\n        controls={\n          <>\n            <div>\n              <FormUI.Label htmlFor=\"xaxis-selector\">\n                <FormattedMessage\n                  defaultMessage=\"X-axis:\"\n                  description=\"Label text for x-axis in contour plot comparison in MLflow\"\n                />\n              </FormUI.Label>\n              {this.renderSelect('xaxis')}\n            </div>\n            <Spacer />\n            <div>\n              <FormUI.Label htmlFor=\"yaxis-selector\">\n                <FormattedMessage\n                  defaultMessage=\"Y-axis:\"\n                  description=\"Label text for y-axis in contour plot comparison in MLflow\"\n                />\n              </FormUI.Label>\n              {this.renderSelect('yaxis')}\n            </div>\n            <Spacer />\n            <div>\n              <FormUI.Label htmlFor=\"zaxis-selector\">\n                <FormattedMessage\n                  defaultMessage=\"Z-axis:\"\n                  description=\"Label text for z-axis in contour plot comparison in MLflow\"\n                />\n              </FormUI.Label>\n              {this.renderSelect('zaxis')}\n            </div>\n            <Spacer />\n            <div className=\"inline-control\">\n              <FormattedMessage\n                defaultMessage=\"Reverse color:\"\n                description=\"Label text for reverse color toggle in contour plot comparison\n                      in MLflow\"\n              />{' '}\n              <Switch\n                componentId=\"codegen_mlflow_app_src_experiment-tracking_components_compareruncontour.tsx_282\"\n                className=\"show-point-toggle\"\n                // @ts-expect-error TS(4111): Property 'reverseColor' comes from an index signat... Remove this comment to see the full error message\n                checked={this.state.reverseColor}\n                onChange={(checked) => this.setState({ reverseColor: checked })}\n              />\n            </div>\n          </>\n        }\n      >\n        {maybeRenderPlot()}\n      </CompareRunPlotContainer>\n    );\n  }\n\n  renderSelect(axis: string) {\n    return (\n      <SimpleSelect\n        componentId=\"codegen_mlflow_app_src_experiment-tracking_components_compareruncontour.tsx_299\"\n        css={{ width: '100%' }}\n        id={axis + '-selector'}\n        onChange={({ target }) => {\n          const { value } = target;\n          const [prefix, ...keyParts] = value.split('-');\n          const key = keyParts.join('-');\n          const isMetric = prefix === 'metric';\n          this.setState({ [axis]: { isMetric, key } });\n        }}\n        value={(this.state[axis].isMetric ? 'metric-' : 'param-') + this.state[axis].key}\n      >\n        <SimpleSelectOptionGroup label=\"Parameter\">\n          {this.paramKeys.map((p: any) => (\n            <SimpleSelectOption key={'param-' + p} value={'param-' + p}>\n              {p}\n            </SimpleSelectOption>\n          ))}\n        </SimpleSelectOptionGroup>\n        <SimpleSelectOptionGroup label=\"Metric\">\n          {this.metricKeys.map((m: any) => (\n            <SimpleSelectOption key={'metric-' + m} value={'metric-' + m}>\n              {m}\n            </SimpleSelectOption>\n          ))}\n        </SimpleSelectOptionGroup>\n      </SimpleSelect>\n    );\n  }\n\n  getPlotlyTooltip(index: any) {\n    const keyLength = CompareRunContour.MAX_PLOT_KEY_LENGTH;\n    const valueLength = CompareRunContour.MAX_PLOT_VALUE_LENGTH;\n    const runName = this.props.runDisplayNames[index];\n    let result = `<b>${escape(runName)}</b><br>`;\n    const paramList = this.props.paramLists[index];\n    paramList.forEach((p) => {\n      result +=\n        escape(Utils.truncateString(p.key, keyLength)) +\n        ': ' +\n        escape(Utils.truncateString(p.value, valueLength)) +\n        '<br>';\n    });\n    const metricList = this.props.metricLists[index];\n    if (metricList.length > 0) {\n      result += paramList.length > 0 ? '<br>' : '';\n      metricList.forEach((m) => {\n        result += escape(Utils.truncateString(m.key, keyLength)) + ': ' + Utils.formatMetric(m.value) + '<br>';\n      });\n    }\n    return result;\n  }\n}\n\nconst styles = {\n  plot: {\n    width: '100%',\n  },\n  noDataMessage: (theme: any) => ({\n    padding: theme.spacing.sm,\n    display: 'flex',\n    justifyContent: 'center',\n  }),\n};\n\nconst mapStateToProps = (state: any, ownProps: any) => {\n  const runInfos: any = [];\n  const metricLists: any = [];\n  const paramLists: any = [];\n  const { runUuids } = ownProps;\n  runUuids.forEach((runUuid: any) => {\n    runInfos.push(getRunInfo(runUuid, state));\n    metricLists.push(Object.values(getLatestMetrics(runUuid, state)));\n    paramLists.push(Object.values(getParams(runUuid, state)));\n  });\n  return { runInfos, metricLists, paramLists };\n};\n\nexport default connect(mapStateToProps)(CompareRunContour);\n", "import spinner from '../static/mlflow-spinner.png';\nimport { Interpolation, keyframes, Theme } from '@emotion/react';\n\ntype Props = {\n  showImmediately?: boolean;\n};\n\nexport function Spinner({ showImmediately }: Props) {\n  return (\n    <div css={(theme) => styles.spinner(theme, showImmediately)}>\n      <img alt=\"Page loading...\" src={spinner} />\n    </div>\n  );\n}\n\nconst styles = {\n  spinner: (theme: Theme, immediate?: boolean): Interpolation<Theme> => ({\n    width: 100,\n    marginTop: 100,\n    marginLeft: 'auto',\n    marginRight: 'auto',\n\n    img: {\n      position: 'absolute',\n      opacity: 0,\n      top: '50%',\n      left: '50%',\n      width: theme.general.heightBase * 2,\n      height: theme.general.heightBase * 2,\n      marginTop: -theme.general.heightBase,\n      marginLeft: -theme.general.heightBase,\n      animation: `${keyframes`\n          0% {\n            opacity: 1;\n          }\n          100% {\n            opacity: 1;\n            -webkit-transform: rotate(360deg);\n                transform: rotate(360deg);\n            }\n          `} 3s linear infinite`,\n      animationDelay: immediate ? '0s' : '0.5s',\n    },\n  }),\n};\n"], "names": ["paramOptionPrefix", "_ref2", "name", "styles", "Selector", "_ref", "id", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metricKeys", "intl", "useIntl", "_jsxs", "SimpleSelect", "componentId", "css", "placeholder", "formatMessage", "defaultMessage", "_ref3", "target", "value", "_prefix", "key", "split", "isParam", "startsWith", "children", "_jsx", "SimpleSelectOptionGroup", "label", "map", "SimpleSelectOption", "_ref0", "_ref1", "CompareRunBox", "_ref4", "runInfos", "metricLists", "paramLists", "xAxis", "setXAxis", "useState", "undefined", "yAxis", "setYAxis", "Array", "from", "Set", "flat", "_ref5", "sort", "_ref6", "getBoxPlotData", "data", "for<PERSON>ach", "_", "index", "params", "metrics", "x", "find", "_ref7", "y", "_ref8", "push", "Object", "entries", "_ref9", "values", "type", "jitter", "pointpos", "boxpoints", "Row", "Col", "span", "borderSpacer", "FormUI", "Label", "htmlFor", "FormattedMessage", "LazyPlot", "layout", "margin", "t", "hovermode", "xaxis", "title", "yaxis", "config", "responsive", "displaylogo", "scrollZoom", "modeBarButtonsToRemove", "useResizeHandler", "Typography", "Text", "size", "theme", "paddingLeft", "spacing", "xs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Empty", "description", "image", "DangerIcon", "CustomErrorBoundary", "customFallbackComponent", "logErrorToConsole", "error", "info", "console", "componentStack", "Error<PERSON>ou<PERSON><PERSON>", "onError", "FallbackComponent", "fallback", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "service", "Component", "errorMessage", "props", "CompareRunUtil", "findInList", "found", "get<PERSON><PERSON><PERSON>", "lists", "numeric", "keys", "list", "item", "isNaN", "parseFloat", "filter", "k", "withRouterNext", "location", "useLocation", "navigate", "useNavigate", "useParams", "SectionErrorBoundary", "React", "constructor", "arguments", "state", "componentDidCatch", "errorInfo", "this", "setState", "renderErrorMessage", "showServerError", "message", "render", "className", "classNames", "wrapper", "href", "Utils", "getSupportPageUrl", "marginLeft", "$ebb31c7feaa4405e$export$b16d9fb1a22de840", "$hgUW1$createContext", "$44d7e150ebc754d2$var$initialState", "didCatch", "$44d7e150ebc754d2$export$e926676385687eaf", "$hgUW1$Component", "getDerivedStateFromError", "resetErrorBoundary", "_this", "_len", "length", "args", "_key", "onReset", "reason", "componentDidUpdate", "prevProps", "prevState", "resetKeys", "a", "b", "some", "is", "$44d7e150ebc754d2$var$hasArrayChanged", "next", "prev", "fallback<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$hgUW1$isValidElement", "Error", "$hgUW1$createElement", "Provider", "$75c9d331f9c1ed1a$export$f20aa86254872370", "$7c3c25b3f398a9d6$export$c052f6604b7d51fe", "context", "$hgUW1$useContext", "$hgUW1$useState", "<PERSON><PERSON><PERSON><PERSON>", "memoized", "$hgUW1$useMemo", "resetBoundary", "showBoundary", "$62ff477d53f02a5b$export$f0c7a449e0cfaec7", "errorBoundaryProps", "Wrapped", "displayName", "PreviewBadge", "useDesignSystemTheme", "Tag", "_css", "color", "CompareRunPlotContainer", "controls", "plotWrapper", "overflow", "width", "height", "minHeight", "display", "gridTemplateColumns", "padding", "<PERSON><PERSON><PERSON><PERSON>", "usesFullHeight", "restProps", "PageWrapper", "useFullHeightLayout", "Spacer", "fixedSpacer", "container", "defaultProps", "flexDirection", "flexGrow", "flex", "flexShrink", "paddingBottom", "CompareRunScatterImpl", "super", "disabled", "isMetric", "getValue", "i", "<PERSON><PERSON><PERSON><PERSON>", "MAX_PLOT_KEY_LENGTH", "ys", "tooltips", "getPlotlyTooltip", "_Fragment", "renderSelect", "text", "hoverinfo", "mode", "marker", "escape", "truncateString", "plot", "axis", "select", "prefix", "keyParts", "join", "p", "m", "valueLength", "MAX_PLOT_VALUE_LENGTH", "runName", "runDisplayNames", "result", "paramList", "metricList", "formatMetric", "CompareRunScatter", "connect", "mapStateToProps", "ownProps", "runUuids", "runUuid", "getRunInfo", "getLatestMetrics", "getParams", "RequestStateWrapper", "shouldRender", "shouldRenderError", "getErrorRequests", "requests", "requestIdsWith404sToIgnore", "r", "includes", "getErrorCode", "ErrorCodes", "RESOURCE_DOES_NOT_EXIST", "getDerivedStateFromProps", "nextProps", "every", "active", "requestErrors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customSpinner", "permissionDeniedView", "suppressErrorThrow", "customRequestErrorHandlerFn", "permissionDeniedErrors", "failedRequest", "PERMISSION_DENIED", "shouldOptimisticallyRender", "triggerError", "Spinner", "get<PERSON><PERSON>", "requestIds", "UNKNOWN_TERM", "ParallelCoordinatesPlotView", "sequence", "updateMetricAxisLabelStyle", "metricsKeySet", "getLabelElementsFromDom", "el", "has", "innerHTML", "style", "fill", "fontWeight", "maybeUpdateStateForColorScale", "currentSequenceFromPlotly", "rightmostMetricKeyFromState", "findLastKeyFromState", "handlePlotUpdate", "dimensions", "d", "keysFromProps", "keysFromState", "getData", "paramDimensions", "metricDimensions", "lastMetricKey", "lastMetricDimension", "line", "getColorScaleConfigsForDimension", "getDimensionsOrderedBySequence", "dimension", "indexOf", "keySet", "showscale", "colorscale", "cmin", "cmax", "autosize", "onUpdate", "displayModeBar", "document", "querySelectorAll", "createDimension", "entryByRunUuid", "attributes", "dataType", "inferType", "Number", "labels", "sortedUniqLabels", "addUnknownTerm", "filteredSortedUniqLabels", "labelToIndexStr", "tickvals", "ticktext", "sortedUniqLabel", "substring", "generateAttributesForCategoricalDimension", "maxValue", "MIN_SAFE_INTEGER", "numericValue", "tickformat", "latestMetricsByRunUuid", "paramsByRunUuid", "entities", "<PERSON><PERSON><PERSON><PERSON>", "metricKey", "ParallelCoordinatesPlotControls", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleParamsSelectChange", "handleMetricsSelectChange", "onClearAllSelect", "LegacySelect", "Option", "marginTop", "<PERSON><PERSON>", "onClick", "ParallelCoordinatesPlotPanel", "diff<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slice", "sharedMetricKeys", "paramV<PERSON><PERSON>", "metricValues", "allParamKeys", "allMetricKeys", "noValuesSelected", "Title", "level", "md", "textAlign", "allRunUuids", "uuid", "getAllParamKeysByRunUuids", "getAllMetricKeysByRunUuids", "getSharedMetricKeysByRunUuids", "getDiffParams", "param", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mlflowServices", "MODEL_REGISTRY", "EXPERIMENTS", "MODEL_SERVING", "RUN_TRACKING", "mapErrorWrapperToPredefinedError", "errorWrapper", "requestId", "ErrorWrapper", "status", "networkErrorDetails", "NotFoundError", "PermissionError", "INTERNAL_ERROR", "InternalServerError", "INVALID_PARAMETER_VALUE", "BadRequestError", "messageFromErrorWrapper", "getMessageField", "OverflowMenu", "menu", "overflowMenu", "<PERSON><PERSON>", "itemName", "otherProps", "<PERSON><PERSON>", "Dropdown", "overlay", "trigger", "placement", "arrow", "icon", "OverflowIcon", "<PERSON><PERSON><PERSON><PERSON>", "breadcrumbs", "titleAddOns", "preview", "spacerSize", "hideSpacer", "dangerouslyAppendEmotionCSS", "Header", "Breadcrumb", "includeTrailingCaret", "buttons", "Plot", "LegacySkeleton", "CompareRunContour", "common", "reverseColor", "zaxis", "getColorscale", "val", "zs", "z", "Switch", "checked", "maybeRenderPlot", "invalidAxes", "messageHead", "noDataMessage", "connectgaps", "contours", "coloring", "range", "Math", "min", "max", "sm", "justifyContent", "showImmediately", "spinner", "alt", "src", "immediate", "marginRight", "img", "position", "opacity", "top", "left", "general", "heightBase", "animation", "keyframes", "animationDelay"], "sourceRoot": ""}