"use strict";(self.webpackChunk_mlflow_mlflow=self.webpackChunk_mlflow_mlflow||[]).push([[6742],{20109:function(e,t,r){r.d(t,{X:function(){return d}});r(31014);var a=r(28486),o=r(48012),n=r(32599),s=r(88443),l=r(50111);function i(){return(0,l.Y)(o.SvL,{"data-testid":"fallback",title:(0,l.Y)(s.A,{id:"qAdWdK",defaultMessage:"Error"}),description:(0,l.Y)(s.A,{id:"RzZVxC",defaultMessage:"An error occurred while rendering this component."}),image:(0,l.Y)(n.j,{})})}function u(e){let{children:t,customFallbackComponent:r}=e;function o(e,t){console.error("Caught Unexpected Error: ",e,t.componentStack)}return r?(0,l.Y)(a.tH,{onError:o,FallbackComponent:r,children:t}):(0,l.Y)(a.tH,{onError:o,fallback:(0,l.Y)(i,{}),children:t})}function d(e,t,r,a){return function(e){return(0,l.Y)(u,{customFallbackComponent:a,children:(0,l.Y)(t,{...e})})}}},28486:function(e,t,r){r.d(t,{tH:function(){return l}});var a=r(31014);function o(e,t,r,a){Object.defineProperty(e,t,{get:r,set:a,enumerable:!0,configurable:!0})}o({},"ErrorBoundary",(()=>l));o({},"ErrorBoundaryContext",(()=>n));const n=(0,a.createContext)(null),s={didCatch:!1,error:null};class l extends a.Component{state=(()=>s)();static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary=(()=>{var e=this;return function(){const{error:t}=e.state;if(null!==t){for(var r=arguments.length,a=new Array(r),o=0;o<r;o++)a[o]=arguments[o];e.props.onReset?.({args:a,reason:"imperative-api"}),e.setState(s)}}})();componentDidCatch(e,t){this.props.onError?.(e,t)}componentDidUpdate(e,t){const{didCatch:r}=this.state,{resetKeys:a}=this.props;r&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some(((e,r)=>!Object.is(e,t[r])))}(e.resetKeys,a)&&(this.props.onReset?.({next:a,prev:e.resetKeys,reason:"keys"}),this.setState(s))}render(){const{children:e,fallbackRender:t,FallbackComponent:r,fallback:o}=this.props,{didCatch:s,error:l}=this.state;let i=e;if(s){const e={error:l,resetErrorBoundary:this.resetErrorBoundary};if((0,a.isValidElement)(o))i=o;else if("function"===typeof t)i=t(e);else{if(!r)throw new Error("react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop");i=(0,a.createElement)(r,e)}}return(0,a.createElement)(n.Provider,{value:{didCatch:s,error:l,resetErrorBoundary:this.resetErrorBoundary}},i)}}function i(e){if(null==e||"boolean"!==typeof e.didCatch||"function"!==typeof e.resetErrorBoundary)throw new Error("ErrorBoundaryContext not found");return!0}function u(){const e=(0,a.useContext)(n);i(e);const[t,r]=(0,a.useState)({error:null,hasError:!1}),o=(0,a.useMemo)((()=>({resetBoundary:()=>{e?.resetErrorBoundary(),r({error:null,hasError:!1})},showBoundary:e=>r({error:e,hasError:!0})})),[e?.resetErrorBoundary]);if(t.hasError)throw t.error;return o}o({},"useErrorBoundary",(()=>u));function d(e,t){const r=r=>(0,a.createElement)(l,t,(0,a.createElement)(e,r)),o=e.displayName||e.name||"Unknown";return r.displayName=`withErrorBoundary(${o})`,r}o({},"withErrorBoundary",(()=>d))},30922:function(e,t,r){r.d(t,{q:function(){return s}});var a=r(32599),o=r(88443),n=r(50111);const s=e=>{let{row:{original:t},getValue:r}=e;const s=r();return s?(0,n.Y)(a.T.Text,{children:(0,n.Y)(o.A,{id:"sM0MBJ",defaultMessage:"Version {version}",values:{version:s}})}):null}},41261:function(e,t,r){r.d(t,{I:function(){return s}});var a=r(28999),o=r(45586),n=r(44200);function s(e,t,r){const s=(0,a.vh)(e,t,r);return(0,n.t)(s,o.$)}},43683:function(e,t,r){r.d(t,{Q:function(){return C}});var a=r(89555),o=r(9133),n=r(31014),s=r(32599),l=r(15579),i=r(48012),u=r(88464),d=r(88443),c=r(13369),m=r(50111);var p={name:"4zleql",styles:"display:block"};function g(e){return t=>function(e,t){const r=(0,u.A)(),{theme:l}=(0,s.u)(),d=e.props.searchValue.toLowerCase();return(0,n.useMemo)((()=>{if(!d)return e;if((0,o.sortedIndexOf)(t,d)>=0)return e;const s=/^[^,.:/=\-\s]+$/.test(d);return n.cloneElement(e,{flattenOptions:[{data:{value:d,disabled:!s,style:{color:s?l.colors.actionTertiaryTextDefault:l.colors.actionDisabledText},children:(0,m.Y)(i.paO,{title:s?void 0:r.formatMessage({id:"fWEvZL",defaultMessage:", . : / - = and blank spaces are not allowed"}),placement:"right",children:(0,m.FD)("span",{css:p,children:[(0,m.Y)(i.c11,{css:(0,a.AH)({marginRight:l.spacing.sm},"")}),r.formatMessage({id:"IJbauF",defaultMessage:'Add tag "{tagKey}"'},{tagKey:d})]})})},key:d,groupOption:!1},...e.props.flattenOptions]})}),[t,e,d,r,l])}(t,e)}var f={name:"1d3w5wq",styles:"width:100%"};function h(e){let{allAvailableTags:t,control:r,onKeyChangeCallback:a}=e;const o=(0,u.A)(),[s,l]=(0,n.useState)(!1),d=(0,n.useRef)(null),{field:p,fieldState:h}=(0,c.as)({control:r,name:"key",rules:{required:{message:o.formatMessage({id:"RlBbjb",defaultMessage:"A tag key is required"}),value:!0}}});return(0,m.Y)(i._vn,{allowClear:!0,ref:d,dangerouslySetAntdProps:{showSearch:!0,dropdownRender:g(t)},css:f,placeholder:o.formatMessage({id:"8ALhnh",defaultMessage:"Type a key"}),value:p.value,defaultValue:p.value,open:s,onDropdownVisibleChange:e=>{l(e)},filterOption:(e,t)=>null===t||void 0===t?void 0:t.value.toLowerCase().includes(e.toLowerCase()),onSelect:e=>{p.onChange(e),null===a||void 0===a||a(e)},onClear:()=>{p.onChange(void 0),null===a||void 0===a||a(void 0)},validationState:h.error?"error":void 0,children:t.map((e=>(0,m.Y)(i._vn.Option,{value:e,children:e},e)))})}var v=r(98597),y=r(52350);function w(e){return new Map(e.map((e=>[e.key,e])))}var M={name:"82a6rk",styles:"flex:1"},b={name:"82a6rk",styles:"flex:1"};const C=e=>{let{onSuccess:t,saveTagsHandler:r,allAvailableTags:p,valueRequired:g=!1,title:f}=e;const C=(0,n.useRef)(),[_,k]=(0,n.useState)(""),{theme:x}=(0,s.u)(),[S,E]=(0,n.useState)(new Map),[P,A]=(0,n.useState)(new Map),[R,T]=(0,n.useState)(!1),F=(0,c.mN)({defaultValues:{key:void 0,value:""}}),V=()=>T(!1),D=(0,n.useCallback)((e=>{C.current=e,E(w(e.tags||[])),A(w(e.tags||[])),F.reset(),T(!0)}),[F]),I=async()=>{C.current&&(k(""),B(!0),r(C.current,Array.from(S.values()),Array.from(P.values())).then((()=>{V(),null===t||void 0===t||t(),B(!1)})).catch((e=>{var t;B(!1),k(e instanceof y.s?null===(t=e.getUserVisibleError())||void 0===t?void 0:t.message:e.message)})))},N=(0,u.A)(),O=F.watch(),[L,B]=(0,n.useState)(!1),U=(0,n.useMemo)((()=>!(0,o.isEqual)((0,o.sortBy)(Array.from(S.values()),"key"),(0,o.sortBy)(Array.from(P.values()),"key"))),[S,P]),K=O.key||O.value,j=U&&K;return{EditTagsModal:(0,m.FD)(l.d,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_135",destroyOnClose:!0,visible:R,title:null!==f&&void 0!==f?f:(0,m.Y)(d.A,{id:"TBX+Gs",defaultMessage:"Add/Edit tags"}),onCancel:V,footer:(0,m.FD)(s.y,{children:[(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_147",dangerouslyUseFocusPseudoClass:!0,onClick:V,css:(0,a.AH)({marginRight:U?0:x.spacing.sm},""),children:N.formatMessage({id:"2a/rR8",defaultMessage:"Cancel"})}),j?(0,m.Y)(Y,{formValues:O,isLoading:L,onSaveTask:I}):(0,m.Y)(i.paO,{title:U?void 0:N.formatMessage({id:"16onEc",defaultMessage:"Please add or remove one or more tags before saving"}),children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_174",dangerouslyUseFocusPseudoClass:!0,disabled:!U,loading:L,type:"primary",onClick:I,children:N.formatMessage({id:"801Xh4",defaultMessage:"Save tags"})})})]}),children:[(0,m.FD)("form",{onSubmit:F.handleSubmit((()=>{if(g&&!O.value.trim())return;const e=new Map(P);e.set(O.key,O),A(e),F.reset()})),css:(0,a.AH)({display:"flex",alignItems:"flex-end",gap:x.spacing.md},""),children:[(0,m.FD)("div",{css:(0,a.AH)({minWidth:0,display:"flex",gap:x.spacing.md,flex:1},""),children:[(0,m.FD)("div",{css:M,children:[(0,m.Y)(i.D$Q.Label,{htmlFor:"key",children:N.formatMessage({id:"crTWax",defaultMessage:"Key"})}),(0,m.Y)(h,{allAvailableTags:p||[],control:F.control,onKeyChangeCallback:e=>{var t;const r=e?P.get(e):void 0;F.setValue("value",null!==(t=null===r||void 0===r?void 0:r.value)&&void 0!==t?t:"")}})]}),(0,m.FD)("div",{css:b,children:[(0,m.Y)(i.D$Q.Label,{htmlFor:"value",children:g?N.formatMessage({id:"tHrp+A",defaultMessage:"Value"}):N.formatMessage({id:"4B8k46",defaultMessage:"Value (optional)"})}),(0,m.Y)(i.tc_.Input,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_223",name:"value",control:F.control,"aria-label":g?N.formatMessage({id:"tHrp+A",defaultMessage:"Value"}):N.formatMessage({id:"4B8k46",defaultMessage:"Value (optional)"}),placeholder:N.formatMessage({id:"FFe+Ug",defaultMessage:"Type a value"})})]})]}),(0,m.Y)(i.paO,{title:N.formatMessage({id:"tx3aAM",defaultMessage:"Add tag"}),children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_248",htmlType:"submit","aria-label":N.formatMessage({id:"tx3aAM",defaultMessage:"Add tag"}),children:(0,m.Y)(i.c11,{})})})]}),_&&(0,m.Y)(i.D$Q.Message,{type:"error",message:_}),(0,m.Y)("div",{css:(0,a.AH)({display:"flex",rowGap:x.spacing.xs,flexWrap:"wrap",marginTop:x.spacing.sm},""),children:Array.from(P.values()).map((e=>(0,m.Y)(v.t,{isClosable:!0,tag:e,onClose:()=>(e=>{let{key:t}=e;A((e=>(e.delete(t),new Map(e))))})(e)},e.key)))})]}),showEditTagsModal:D,isLoading:L}};var _={name:"1y0ex1",styles:"max-width:400px"};function Y(e){let{isLoading:t,formValues:r,onSaveTask:n}=e;const l=(0,u.A)(),{theme:i}=(0,s.u)(),d=`${`${(0,o.truncate)(r.key,{length:20})||"_"}`}${r.value?`:${(0,o.truncate)(r.value,{length:20})}`:""}`,c=l.formatMessage({id:"wcSVYI",defaultMessage:'Are you sure you want to save and close without adding "{tag}"'},{tag:d});return(0,m.FD)(s.av.Root,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_309",children:[(0,m.Y)(s.av.Trigger,{asChild:!0,children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_306",dangerouslyUseFocusPseudoClass:!0,loading:t,type:"primary",children:l.formatMessage({id:"801Xh4",defaultMessage:"Save tags"})})}),(0,m.FD)(s.av.Content,{align:"end","aria-label":c,children:[(0,m.Y)(s.T.Paragraph,{css:_,children:c}),(0,m.Y)(s.av.Close,{asChild:!0,children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_316",onClick:n,children:l.formatMessage({id:"mv6CY3",defaultMessage:"Yes, save and close"})})}),(0,m.Y)(s.av.Close,{asChild:!0,children:(0,m.Y)(s.B,{componentId:"codegen_mlflow_app_src_common_hooks_useeditkeyvaluetagsmodal.tsx_324",type:"primary",css:(0,a.AH)({marginLeft:i.spacing.sm},""),children:l.formatMessage({id:"geizp1",defaultMessage:"Cancel"})})}),(0,m.Y)(s.av.Arrow,{})]})]})}},56412:function(e,t,r){r.d(t,{i:function(){return u}});var a=r(31014),o=r(88443),n=r(48012),s=r(32599),l=r(50111);var i={name:"1739oy8",styles:"z-index:1"};const u=e=>{let{copyText:t,showLabel:r=!0,componentId:u,...d}=e;const[c,m]=(0,a.useState)(!1);return(0,l.Y)(n.paO,{title:(0,l.Y)(o.A,{id:"X+boXI",defaultMessage:"Copied"}),dangerouslySetAntdProps:{visible:c},children:(0,l.Y)(s.B,{componentId:null!==u&&void 0!==u?u:"mlflow.shared.copy_button",type:"primary",onClick:()=>{navigator.clipboard.writeText(t),m(!0),setTimeout((()=>{m(!1)}),3e3)},onMouseLeave:()=>{m(!1)},css:i,children:r?(0,l.Y)(o.A,{id:"1Iq+NW",defaultMessage:"Copy"}):void 0,...d})})}},62448:function(e,t,r){r.d(t,{h:function(){return l}});var a=r(39416),o=r(52350),n=r(47664);class s{}s.mlflowServices={MODEL_REGISTRY:"Model Registry",EXPERIMENTS:"Experiments",MODEL_SERVING:"Model Serving",RUN_TRACKING:"Run Tracking"};const l=(e,t)=>{if(!(e instanceof o.s))return;const{status:r}=e;let s;const l={status:r};e.getErrorCode()===n.tG.RESOURCE_DOES_NOT_EXIST&&(s=new a.m_(l)),e.getErrorCode()===n.tG.PERMISSION_DENIED&&(s=new a.i_(l)),e.getErrorCode()===n.tG.INTERNAL_ERROR&&(s=new a.PO(l)),e.getErrorCode()===n.tG.INVALID_PARAMETER_VALUE&&(s=new a.v7(l));const i=e.getMessageField();return s&&i&&(s.message=i),s};t.A=s},66742:function(e,t,r){r.r(t),r.d(t,{default:function(){return B}});var a=r(84069),o=r(41261),n=r(31014),s=r(82832);const l=e=>{let{queryKey:t}=e;const[,{searchFilter:r,pageToken:a}]=t;return s.M.listRegisteredPrompts(r,a)};var i=r(15579),u=r(48012),d=r(32599),c=r(88443),m=r(50111);const p=e=>{let{searchFilter:t,onSearchFilterChange:r}=e;return(0,m.Y)(u.R9P,{children:(0,m.Y)(u.z2z,{placeholder:"Search prompts by name",componentId:"mlflow.prompts.list.search",value:t,onChange:e=>r(e.target.value)})})};var g=r(89555),f=r(70618),h=r(9856),v=r(88464),y=r(98590),w=r(98597);var M={name:"zjik7",styles:"display:flex"},b={name:"tyk0n8",styles:"overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:flex"},C={name:"14cnl6e",styles:"flex-shrink:0;opacity:0;[role=row]:hover &{opacity:1;}[role=row]:focus-within &{opacity:1;}"};const _=e=>{var t;let{row:{original:r},table:{options:{meta:a}}}=e;const o=(0,v.A)(),{onEditTags:n}=a,s=(null===r||void 0===r||null===(t=r.tags)||void 0===t?void 0:t.filter((e=>(0,y.oD)(e.key))))||[],l=s.length>0;return(0,m.FD)("div",{css:M,children:[(0,m.Y)("div",{css:b,children:null===s||void 0===s?void 0:s.map((e=>(0,m.Y)(w.t,{tag:e},e.key)))}),(0,m.Y)(d.B,{componentId:"mlflow.prompts.list.tag.add",size:"small",icon:l?(0,m.Y)(u.R2l,{}):void 0,onClick:()=>null===n||void 0===n?void 0:n(r),"aria-label":o.formatMessage({id:"lA8QO2",defaultMessage:"Edit tags"}),children:l?void 0:(0,m.Y)(c.A,{id:"iDpAK8",defaultMessage:"Add tags"}),css:C,type:"tertiary"})]})};var Y=r(93215),k=r(58481);const x=e=>{let{row:{original:t},getValue:r}=e;const a=r();return t.name?(0,m.Y)(Y.N_,{to:k.h.getPromptDetailsPageRoute(encodeURIComponent(t.name)),children:a}):a};var S=r(76010),E=r(30922),P=r(9133);var A={name:"1h3rtzg",styles:"align-items:center"};const R=e=>{let{prompts:t,hasNextPage:r,hasPreviousPage:a,isLoading:o,isFiltered:s,onNextPage:l,onPreviousPage:i,onEditTags:p}=e;const{theme:y}=(0,d.u)(),w=(()=>{const e=(0,v.A)();return(0,n.useMemo)((()=>{const t=[{header:e.formatMessage({id:"tzA/LZ",defaultMessage:"Name"}),accessorKey:"name",id:"name",cell:x},{header:e.formatMessage({id:"bmd4rb",defaultMessage:"Latest version"}),cell:E.q,accessorFn:e=>{var t;let{latest_versions:r}=e;return null===(t=(0,P.first)(r))||void 0===t?void 0:t.version},id:"latestVersion"},{header:e.formatMessage({id:"FiKsFK",defaultMessage:"Last modified"}),id:"lastModified",accessorFn:t=>{let{last_updated_timestamp:r}=t;return S.A.formatTimestamp(r,e)}},{header:e.formatMessage({id:"KMVqUP",defaultMessage:"Tags"}),accessorKey:"tags",id:"tags",cell:_}];return t}),[e])})(),M=(0,f.N4)({data:null!==t&&void 0!==t?t:[],columns:w,getCoreRowModel:(0,h.HT)(),getRowId:(e,t)=>{var r;return null!==(r=e.name)&&void 0!==r?r:t.toString()},meta:{onEditTags:p}});return(0,m.FD)(u.XIK,{scrollable:!0,pagination:(0,m.Y)(u.vIA,{hasNextPage:r,hasPreviousPage:a,onNextPage:l,onPreviousPage:i,componentId:"mlflow.prompts.list.pagination"}),empty:(()=>{const e=!o&&(0,P.isEmpty)(t);return e&&s?(0,m.Y)(u.SvL,{image:(0,m.Y)(u.xfv,{}),title:(0,m.Y)(c.A,{id:"hlpNRa",defaultMessage:"No prompts found"}),description:null}):e?(0,m.Y)(u.SvL,{title:(0,m.Y)(c.A,{id:"/fwKFW",defaultMessage:"No prompts created"}),description:(0,m.Y)(c.A,{id:"WM5IeI",defaultMessage:'Use "Create prompt" button in order to create a new prompt'})}):null})(),children:[(0,m.Y)(u.Hjg,{isHeader:!0,children:M.getLeafHeaders().map((e=>(0,m.Y)(u.A0N,{componentId:"mlflow.prompts.list.table.header",children:(0,f.Kv)(e.column.columnDef.header,e.getContext())},e.id)))}),o?(0,m.Y)(u.BAM,{table:M}):M.getRowModel().rows.map((e=>(0,m.Y)(u.Hjg,{css:(0,g.AH)({height:y.general.buttonHeight},""),children:e.getAllCells().map((e=>(0,m.Y)(u.nA6,{css:A,children:(0,f.Kv)(e.column.columnDef.cell,e.getContext())},e.id)))},e.id)))]})};var T=r(83090),F=r(70724),V=r(20109),D=r(62448),I=r(72282),N=r(76137);var O={name:"1m5f2z8",styles:"overflow:hidden;display:flex;flex-direction:column"},L={name:"xt6tct",styles:"flex:1;display:flex;flex-direction:column;overflow:hidden"};var B=(0,V.X)(D.A.mlflowServices.EXPERIMENTS,(()=>{const[e,t]=(0,n.useState)(""),r=(0,Y.Zp)(),[s]=(0,N.d7)(e,500),{data:g,error:f,refetch:h,hasNextPage:v,hasPreviousPage:y,isLoading:w,onNextPage:M,onPreviousPage:b}=function(){var e,t,r,a;let{searchFilter:s}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const i=(0,n.useRef)([]),[u,d]=(0,n.useState)(void 0),c=(0,o.I)(["prompts_list",{searchFilter:s,pageToken:u}],{queryFn:l,retry:!1}),m=(0,n.useCallback)((()=>{var e;i.current.push(u),d(null===(e=c.data)||void 0===e?void 0:e.next_page_token)}),[null===(e=c.data)||void 0===e?void 0:e.next_page_token,u]),p=(0,n.useCallback)((()=>{const e=i.current.pop();d(e)}),[]);return{data:null===(t=c.data)||void 0===t?void 0:t.registered_models,error:null!==(r=c.error)&&void 0!==r?r:void 0,isLoading:c.isLoading,hasNextPage:void 0!==(null===(a=c.data)||void 0===a?void 0:a.next_page_token),hasPreviousPage:Boolean(u),onNextPage:m,onPreviousPage:p,refetch:c.refetch}}({searchFilter:s}),{EditTagsModal:C,showEditPromptTagsModal:_}=(0,T.i)({onSuccess:h}),{CreatePromptModal:x,openModal:S}=(0,F.z)({mode:F.v.CreatePrompt,onSuccess:e=>{let{promptName:t}=e;return r(k.h.getPromptDetailsPageRoute(t))}});return(0,m.FD)(a.m,{css:O,children:[(0,m.Y)(i.S,{shrinks:!1}),(0,m.Y)(u.Y9Y,{title:(0,m.Y)(c.A,{id:"aLRRam",defaultMessage:"Prompts"}),buttons:(0,m.Y)(d.B,{componentId:"mlflow.prompts.list.create",type:"primary",onClick:S,children:(0,m.Y)(c.A,{id:"7x6xu8",defaultMessage:"Create prompt"})})}),(0,m.Y)(i.S,{shrinks:!1}),(0,m.FD)("div",{css:L,children:[(0,m.Y)(p,{searchFilter:e,onSearchFilterChange:t}),(null===f||void 0===f?void 0:f.message)&&(0,m.FD)(m.FK,{children:[(0,m.Y)(u.FcD,{type:"error",message:f.message,componentId:"mlflow.prompts.list.error",closable:!1}),(0,m.Y)(i.S,{})]}),(0,m.Y)(R,{prompts:g,error:f,hasNextPage:v,hasPreviousPage:y,isLoading:w,isFiltered:Boolean(e),onNextPage:M,onPreviousPage:b,onEditTags:_})]}),C,x]})}),void 0,I.u)},70724:function(e,t,r){r.d(t,{v:function(){return p},z:function(){return g}});var a=r(15579),o=r(48012),n=r(31014),s=r(13369),l=r(88464),i=r(88443),u=r(77020),d=r(82832),c=r(84565);var m=r(50111);let p=function(e){return e.CreatePrompt="CreatePrompt",e.CreatePromptVersion="CreatePromptVersion",e}({});const g=e=>{let{mode:t=p.CreatePromptVersion,registeredPrompt:r,latestVersion:g,onSuccess:f}=e;const[h,v]=(0,n.useState)(!1),y=(0,l.A)(),w=(0,s.mN)({defaultValues:{draftName:"",draftValue:"",commitMessage:"",tags:[]}}),M=t===p.CreatePrompt,b=t===p.CreatePromptVersion,{mutate:C,error:_,reset:Y,isLoading:k}=(0,u.n)({mutationFn:async e=>{var t;let{promptName:r,createPromptEntity:a,content:o,commitMessage:n,tags:s}=e;a&&await d.M.createRegisteredPrompt(r);const l=await d.M.createRegisteredPromptVersion(r,[{key:c.Dh,value:o},...s],n),i=null===l||void 0===l||null===(t=l.model_version)||void 0===t?void 0:t.version;if(!i)throw new Error("Failed to create a new prompt version");return{version:i}}});return{CreatePromptModal:(0,m.FD)(a.d,{componentId:"mlflow.prompts.create.modal",visible:h,onCancel:()=>v(!1),title:b?(0,m.Y)(i.A,{id:"XLSmZx",defaultMessage:"Create prompt version"}):(0,m.Y)(i.A,{id:"wh+Eos",defaultMessage:"Create prompt"}),okText:(0,m.Y)(i.A,{id:"TSXmaB",defaultMessage:"Create"}),okButtonProps:{loading:k},onOk:w.handleSubmit((async e=>{const t=b&&null!==r&&void 0!==r&&r.name?null===r||void 0===r?void 0:r.name:e.draftName;C({createPromptEntity:M,content:e.draftValue,commitMessage:e.commitMessage,promptName:t,tags:e.tags},{onSuccess:e=>{const r=null===e||void 0===e?void 0:e.version;null===f||void 0===f||f({promptName:t,promptVersion:r}),v(!1)}})})),cancelText:(0,m.Y)(i.A,{id:"WP50re",defaultMessage:"Cancel"}),size:"wide",children:[(null===_||void 0===_?void 0:_.message)&&(0,m.FD)(m.FK,{children:[(0,m.Y)(o.FcD,{componentId:"mlflow.prompts.create.error",closable:!1,message:_.message,type:"error"}),(0,m.Y)(a.S,{})]}),M&&(0,m.FD)(m.FK,{children:[(0,m.Y)(o.D$Q.Label,{htmlFor:"mlflow.prompts.create.name",children:"Name:"}),(0,m.Y)(o.tc_.Input,{control:w.control,id:"mlflow.prompts.create.name",componentId:"mlflow.prompts.create.name",name:"draftName",rules:{required:{value:!0,message:y.formatMessage({id:"Rlwm5V",defaultMessage:"Name is required"})},pattern:{value:/^[a-zA-Z0-9_\-.]+$/,message:y.formatMessage({id:"HZdpLU",defaultMessage:"Only alphanumeric characters, underscores, hyphens, and dots are allowed"})}},placeholder:y.formatMessage({id:"zAuZ2j",defaultMessage:"Provide an unique prompt name"}),validationState:w.formState.errors.draftName?"error":void 0}),w.formState.errors.draftName&&(0,m.Y)(o.D$Q.Message,{type:"error",message:w.formState.errors.draftName.message}),(0,m.Y)(a.S,{})]}),(0,m.Y)(o.D$Q.Label,{htmlFor:"mlflow.prompts.create.content",children:"Prompt:"}),(0,m.Y)(o.tc_.TextArea,{control:w.control,id:"mlflow.prompts.create.content",componentId:"mlflow.prompts.create.content",name:"draftValue",autoSize:{minRows:3,maxRows:10},rules:{required:{value:!0,message:y.formatMessage({id:"wq+WyH",defaultMessage:"Prompt content is required"})}},placeholder:y.formatMessage({id:"2JfWh3",defaultMessage:"Type prompt content here. Wrap variables with double curly brace e.g. '{{' name '}}'."}),validationState:w.formState.errors.draftValue?"error":void 0}),w.formState.errors.draftValue&&(0,m.Y)(o.D$Q.Message,{type:"error",message:w.formState.errors.draftValue.message}),(0,m.Y)(a.S,{}),(0,m.Y)(o.D$Q.Label,{htmlFor:"mlflow.prompts.create.commit_message",children:"Commit message (optional):"}),(0,m.Y)(o.tc_.Input,{control:w.control,id:"mlflow.prompts.create.commit_message",componentId:"mlflow.prompts.create.commit_message",name:"commitMessage"})]}),openModal:()=>{var e;(Y(),t===p.CreatePromptVersion&&g)&&w.reset({commitMessage:"",draftName:"",draftValue:null!==(e=(0,c.dv)(g))&&void 0!==e?e:"",tags:[]});v(!0)}}}},72282:function(e,t,r){r.d(t,{u:function(){return u}});var a=r(48012),o=r(32599),n=r(88443),s=r(84069),l=r(50111);var i={name:"1vfb318",styles:"flex:1;display:flex;align-items:center;justify-content:center"};const u=e=>{var t;let{error:r}=e;return(0,l.Y)(s.m,{css:i,children:(0,l.Y)(a.SvL,{"data-testid":"fallback",title:(0,l.Y)(n.A,{id:"AOoWxS",defaultMessage:"Error"}),description:null!==(t=null===r||void 0===r?void 0:r.message)&&void 0!==t?t:(0,l.Y)(n.A,{id:"zmMR5p",defaultMessage:"An error occurred while rendering this component."}),image:(0,l.Y)(o.j,{})})})}},76137:function(e,t,r){r.d(t,{YQ:function(){return o},d7:function(){return s}});var a=r(31014);function o(e,t,r){var o=this,n=(0,a.useRef)(null),s=(0,a.useRef)(0),l=(0,a.useRef)(null),i=(0,a.useRef)([]),u=(0,a.useRef)(),d=(0,a.useRef)(),c=(0,a.useRef)(e),m=(0,a.useRef)(!0);c.current=e;var p="undefined"!=typeof window,g=!t&&0!==t&&p;if("function"!=typeof e)throw new TypeError("Expected a function");t=+t||0;var f=!!(r=r||{}).leading,h=!("trailing"in r)||!!r.trailing,v="maxWait"in r,y="debounceOnServer"in r&&!!r.debounceOnServer,w=v?Math.max(+r.maxWait||0,t):null;(0,a.useEffect)((function(){return m.current=!0,function(){m.current=!1}}),[]);var M=(0,a.useMemo)((function(){var e=function(e){var t=i.current,r=u.current;return i.current=u.current=null,s.current=e,d.current=c.current.apply(r,t)},r=function(e,t){g&&cancelAnimationFrame(l.current),l.current=g?requestAnimationFrame(e):setTimeout(e,t)},a=function(e){if(!m.current)return!1;var r=e-n.current;return!n.current||r>=t||r<0||v&&e-s.current>=w},M=function(t){return l.current=null,h&&i.current?e(t):(i.current=u.current=null,d.current)},b=function e(){var o=Date.now();if(a(o))return M(o);if(m.current){var l=t-(o-n.current),i=v?Math.min(l,w-(o-s.current)):l;r(e,i)}},C=function(){if(p||y){var c=Date.now(),g=a(c);if(i.current=[].slice.call(arguments),u.current=o,n.current=c,g){if(!l.current&&m.current)return s.current=n.current,r(b,t),f?e(n.current):d.current;if(v)return r(b,t),e(n.current)}return l.current||r(b,t),d.current}};return C.cancel=function(){l.current&&(g?cancelAnimationFrame(l.current):clearTimeout(l.current)),s.current=0,i.current=n.current=u.current=l.current=null},C.isPending=function(){return!!l.current},C.flush=function(){return l.current?M(Date.now()):d.current},C}),[f,v,t,w,h,g,p,y]);return M}function n(e,t){return e===t}function s(e,t,r){var s=r&&r.equalityFn||n,l=(0,a.useRef)(e),i=(0,a.useState)({})[1],u=o((0,a.useCallback)((function(e){l.current=e,i({})}),[i]),t,r),d=(0,a.useRef)(e);return s(d.current,e)||(u(e),d.current=e),[l.current,u]}},77020:function(e,t,r){r.d(t,{n:function(){return m}});var a=r(31014),o=r(61226),n=r(28999),s=r(84865),l=r(95904),i=r(21363);class u extends i.Q{constructor(e,t){super(),this.client=e,this.setOptions(t),this.bindMethods(),this.updateResult()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){var t;const r=this.options;this.options=this.client.defaultMutationOptions(e),(0,n.f8)(r,this.options)||this.client.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.currentMutation,observer:this}),null==(t=this.currentMutation)||t.setOptions(this.options)}onUnsubscribe(){var e;this.hasListeners()||(null==(e=this.currentMutation)||e.removeObserver(this))}onMutationUpdate(e){this.updateResult();const t={listeners:!0};"success"===e.type?t.onSuccess=!0:"error"===e.type&&(t.onError=!0),this.notify(t)}getCurrentResult(){return this.currentResult}reset(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})}mutate(e,t){return this.mutateOptions=t,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,{...this.options,variables:"undefined"!==typeof e?e:this.options.variables}),this.currentMutation.addObserver(this),this.currentMutation.execute()}updateResult(){const e=this.currentMutation?this.currentMutation.state:(0,s.$)(),t={...e,isLoading:"loading"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset};this.currentResult=t}notify(e){l.j.batch((()=>{var t,r,a,o;if(this.mutateOptions&&this.hasListeners())if(e.onSuccess)null==(t=(r=this.mutateOptions).onSuccess)||t.call(r,this.currentResult.data,this.currentResult.variables,this.currentResult.context),null==(a=(o=this.mutateOptions).onSettled)||a.call(o,this.currentResult.data,null,this.currentResult.variables,this.currentResult.context);else if(e.onError){var n,s,l,i;null==(n=(s=this.mutateOptions).onError)||n.call(s,this.currentResult.error,this.currentResult.variables,this.currentResult.context),null==(l=(i=this.mutateOptions).onSettled)||l.call(i,void 0,this.currentResult.error,this.currentResult.variables,this.currentResult.context)}e.listeners&&this.listeners.forEach((e=>{let{listener:t}=e;t(this.currentResult)}))}))}}var d=r(27288),c=r(71233);function m(e,t,r){const s=(0,n.GR)(e,t,r),i=(0,d.jE)({context:s.context}),[m]=a.useState((()=>new u(i,s)));a.useEffect((()=>{m.setOptions(s)}),[m,s]);const g=(0,o.r)(a.useCallback((e=>m.subscribe(l.j.batchCalls(e))),[m]),(()=>m.getCurrentResult()),(()=>m.getCurrentResult())),f=a.useCallback(((e,t)=>{m.mutate(e,t).catch(p)}),[m]);if(g.error&&(0,c.G)(m.options.useErrorBoundary,[g.error]))throw g.error;return{...g,mutate:f,mutateAsync:g.mutate}}function p(){}},82832:function(e,t,r){r.d(t,{M:function(){return l}});var a=r(39416),o=r(53962),n=r(84565);const s=async e=>{let{reject:t,response:r,err:o}=e;const n=(0,a.a$)(r),s=n instanceof a.Bk?o:n;if(r)try{var l;const e=null===(l=await r.json())||void 0===l?void 0:l.message;e&&(s.message=e)}catch{}t(s)},l={listRegisteredPrompts:(e,t)=>{const r=new URLSearchParams;let a=`tags.\`${n.PS}\` = '${n.pY}'`;e&&(a=`${a} AND name ILIKE '%${e}%'`),t&&r.append("page_token",t),r.append("filter",a);const l=["ajax-api/2.0/mlflow/registered-models/search",r.toString()].join("?");return(0,o.AC)({relativeUrl:l,error:s})},setRegisteredPromptTag:(e,t,r)=>(0,o.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/set-tag",method:"POST",body:JSON.stringify({key:t,value:r,name:e}),error:s}),deleteRegisteredPromptTag:(e,t)=>(0,o.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete-tag",method:"DELETE",body:JSON.stringify({key:t,name:e}),error:s}),createRegisteredPrompt:e=>(0,o.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/create",method:"POST",body:JSON.stringify({name:e,tags:[{key:n.PS,value:n.pY}]}),error:s}),createRegisteredPromptVersion:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0;return(0,o.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/create",method:"POST",body:JSON.stringify({name:e,description:r,source:"dummy-source",tags:[{key:n.PS,value:n.pY},...t]}),error:s})},setRegisteredPromptVersionTag:(e,t,r,a)=>(0,o.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/set-tag",method:"POST",body:JSON.stringify({key:r,value:a,name:e,version:t}),error:s}),deleteRegisteredPromptVersionTag:(e,t,r)=>{(0,o.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete-tag",method:"DELETE",body:JSON.stringify({key:r,name:e,version:t}),error:s})},getPromptDetails:e=>{const t=new URLSearchParams;t.append("name",e);const r=["ajax-api/2.0/mlflow/registered-models/get",t.toString()].join("?");return(0,o.AC)({relativeUrl:r,error:s})},getPromptVersions:e=>{const t=new URLSearchParams;t.append("filter",`name='${e}' AND tags.\`${n.PS}\` = '${n.pY}'`);const r=["ajax-api/2.0/mlflow/model-versions/search",t.toString()].join("?");return(0,o.AC)({relativeUrl:r,error:s})},getPromptVersionsForRun:e=>{const t=new URLSearchParams;t.append("filter",`tags.\`${n.PS}\` = '${n.pY}' AND tags.\`${n.xd}\` ILIKE "%${e}%"`);const r=["ajax-api/2.0/mlflow/model-versions/search",t.toString()].join("?");return(0,o.AC)({relativeUrl:r,error:s})},deleteRegisteredPrompt:e=>(0,o.AC)({relativeUrl:"ajax-api/2.0/mlflow/registered-models/delete",method:"DELETE",body:JSON.stringify({name:e}),error:s}),deleteRegisteredPromptVersion:(e,t)=>(0,o.AC)({relativeUrl:"ajax-api/2.0/mlflow/model-versions/delete",method:"DELETE",body:JSON.stringify({name:e,version:t}),error:s})}},83090:function(e,t,r){r.d(t,{i:function(){return i}});var a=r(77020),o=r(43683),n=r(82832),s=r(31014),l=r(98590);const i=e=>{let{onSuccess:t}=e;const r=(0,a.n)({mutationFn:async e=>{let{toAdd:t,toDelete:r,promptId:a}=e;return Promise.all([...t.map((e=>{let{key:t,value:r}=e;return n.M.setRegisteredPromptTag(a,t,r)})),...r.map((e=>{let{key:t}=e;return n.M.deleteRegisteredPromptTag(a,t)}))])}}),{EditTagsModal:i,showEditTagsModal:u,isLoading:d}=(0,o.Q)({valueRequired:!0,saveTagsHandler:(e,a,o)=>{const{addedOrModifiedTags:n,deletedTags:s}=(0,l.Rf)(a,o);return new Promise(((a,o)=>{if(!e.name)return o();r.mutate({promptId:e.name,toAdd:n,toDelete:s},{onSuccess:()=>{a(),null===t||void 0===t||t()},onError:o})}))}});return{EditTagsModal:i,showEditPromptTagsModal:(0,s.useCallback)((e=>u({name:e.name,tags:e.tags.filter((e=>(0,l.oD)(e.key)))})),[u]),isLoading:d}}},84069:function(e,t,r){r.d(t,{m:function(){return s}});var a=r(48012),o=r(50111);var n={name:"gmowil",styles:"height:calc(100% - 60px)"};const s=e=>{let{children:t,className:r}=e;return(0,o.Y)(a.ffj,{css:n,className:r,children:t})}},84565:function(e,t,r){r.d(t,{Dh:function(){return a},Dp:function(){return l},PS:function(){return n},dv:function(){return i},pY:function(){return s},xd:function(){return o}});const a="mlflow.prompt.text",o="mlflow.prompt.run_ids",n="mlflow.prompt.is_prompt",s="true";let l=function(e){return e.TABLE="table",e.PREVIEW="preview",e.COMPARE="compare",e}({});const i=e=>{var t,r;return null===e||void 0===e||null===(t=e.tags)||void 0===t||null===(r=t.find((e=>e.key===a)))||void 0===r?void 0:r.value}},98597:function(e,t,r){r.d(t,{t:function(){return v}});var a=r(89555),o=r(48012),n=r(32599),s=r(31014),l=r(88464),i=r(15579),u=r(56412),d=r(50111);const{Paragraph:c}=n.T;var m={name:"zjik7",styles:"display:flex"},p={name:"1ff36h2",styles:"flex-grow:1"};const g=s.memo((e=>{const{theme:t}=(0,n.u)();return(0,d.Y)(i.d,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetagfullviewmodal.tsx_17",title:"Tag: "+e.tagKey,visible:e.isKeyValueTagFullViewModalVisible,onCancel:()=>e.setIsKeyValueTagFullViewModalVisible(!1),children:(0,d.FD)("div",{css:m,children:[(0,d.Y)(c,{css:p,children:(0,d.Y)("pre",{css:(0,a.AH)({backgroundColor:t.colors.backgroundPrimary,marginTop:t.spacing.sm,whiteSpace:"pre-wrap",wordBreak:"break-all"},""),children:e.tagValue})}),(0,d.Y)("div",{css:(0,a.AH)({marginTop:t.spacing.sm},""),children:(0,d.Y)(u.i,{copyText:e.tagValue,showLabel:!1,icon:(0,d.Y)(o.TdU,{}),"aria-label":"Copy"})})]})})})),f=30;function h(){return!(arguments.length>0&&void 0!==arguments[0])||arguments[0]?{overflow:"hidden",textOverflow:"ellipsis",textWrap:"nowrap",whiteSpace:"nowrap"}:{whiteSpace:"nowrap"}}const v=e=>{let{isClosable:t=!1,onClose:r,tag:i,enableFullViewModal:u=!1,charLimit:c=f,maxWidth:m=300,className:p}=e;const v=(0,l.A)(),[y,w]=(0,s.useState)(!1),{shouldTruncateKey:M,shouldTruncateValue:b}=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f;const{key:r,value:a}=e,o=r.length+a.length,n=r.length>a.length,s=n?a.length:r.length;return o<=t?{shouldTruncateKey:!1,shouldTruncateValue:!1}:s>t/2?{shouldTruncateKey:!0,shouldTruncateValue:!0}:{shouldTruncateKey:n,shouldTruncateValue:!n}}(i,c),C=u&&(M||b),_=v.formatMessage({id:"ZXUtU8",defaultMessage:"Click to see more"});return(0,d.FD)("div",{children:[(0,d.Y)(o.vwO,{componentId:"codegen_mlflow_app_src_common_components_keyvaluetag.tsx_60",closable:t,onClose:r,title:i.key,className:p,children:(0,d.Y)(o.paO,{title:C?_:"",children:(0,d.FD)("span",{css:(0,a.AH)({maxWidth:m,display:"inline-flex"},""),onClick:()=>C?w(!0):void 0,children:[(0,d.Y)(n.T.Text,{bold:!0,title:i.key,css:h(M),children:i.key}),i.value&&(0,d.FD)(n.T.Text,{title:i.value,css:h(b),children:[": ",i.value]})]})})}),(0,d.Y)("div",{children:y&&(0,d.Y)(g,{tagKey:i.key,tagValue:i.value,isKeyValueTagFullViewModalVisible:y,setIsKeyValueTagFullViewModalVisible:w})})]})}}}]);
//# sourceMappingURL=6742.581e1e12.chunk.js.map